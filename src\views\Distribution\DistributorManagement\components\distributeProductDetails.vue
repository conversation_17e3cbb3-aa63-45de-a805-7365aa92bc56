<template>
    <JDrawer
      v-model:show="drawerVisible"
      title="分销商品"
      :isShowFooter="false"
      :isGetLoading="isGetDetailLoading"
      @after-leave="closeDrawer"
      to="#distribution"
      :contents-list="[
        {
          name: '选择分销商品范围',
          slotName: 'scope_of_distribution_goods'
        },
        {
          name: '可分销商品',
          slotName: 'distributable_products'
        },
        {
          name: '',
          slotName: 'all_products'
        },
      ]"
    >

        <template #scope_of_distribution_goods>
          <n-spin :show="distributeProductRangeLoad">
            <n-radio-group v-model:value="detailModel.distributeProductRange" name="radiogroup" @update:value="updateProduct">
            <n-space>
              <n-radio v-for="radiogroup in radiogroupData" :key="radiogroup.value" :value="radiogroup.value">
                {{ radiogroup.label }}
              </n-radio>
            </n-space>
          </n-radio-group>
          </n-spin>
        </template>
        <template v-if="detailModel.distributeProductRange !== 1" #distributable_products>
          <n-spin :show="distributeProductRangeLoad">
            <TreeContentLayout :treeWidth="240">
            <template #tree-content>
              <div class="classify-container inner-page-height">
                <!-- header -->
                <div class="header">
                  <n-space justify="space-between" style="margin-bottom: 12px;flex-wrap: nowrap;">
                    <n-button size="small" class="store-button" :loading="isGetLoading" @click="refresh">刷 新</n-button>
                  </n-space>
                </div>
                <!-- 商品分类 tree -->
                <div class="classification-tree-container">
                  <n-spin :show="isGetLoading" size="small" style="height: 100%;">
                    <n-scrollbar style="height: 100%;" >
                      <ClassificationTree
                        v-model:value="model.selectedValue"
                        :tree-data="treeData"
                        @update:selected-keys="handleUpdateSelectKeys"
                        :selected-keys="selectedKeys"
                        :defaultExpandKeys="defaultExpandKeys"
                      />
                    </n-scrollbar>
                  </n-spin>
                </div>
              </div>
            </template>
            <template #default>
              <div class="inner-page-height">
                <FormLayout
                  :isLoading="isLoading"
                  :tableData="tableData"
                  :tableColumns="tableColumns"
                  :pagination="paginationRef"
                  :isTableSelection="false"
                  @paginationChange="paginationChange"
                  :isDisplayIndex="false"
                >
                  <template #searchForm>
                    <n-form
                      ref="formRef"
                      label-placement="left"
                      label-width="auto"
                      :show-feedback="false"
                      require-mark-placement="right-hanging"
                      size="small"
                      :style="{ width: '100%' }"
                    >
                      <n-grid cols="4 m:12 l:18 xl:24" :x-gap="32" responsive="screen">
                        <!-- 商品名称 -->
                        <n-gi :span="6">
                          <n-form-item label="商品名称" path="productName">
                            <n-input-group>
                              <JSearchInput
                                v-model:value="detailModel.productName"
                                placeholder="请输入商品名称"
                                @search="formSearch"
                                :width="240"
                              />
                            </n-input-group>
                          </n-form-item>
                        </n-gi>
                        <!-- 商品编号 -->
<!--                        <n-gi :span="6">-->
<!--                          <n-form-item label="商品编号" path="productCode">-->
<!--                            <n-input-group>-->
<!--                              <JSearchInput-->
<!--                                v-model:value="detailModel.productCode"-->
<!--                                placeholder="请输入商品编号"-->
<!--                                @search="formSearch"-->
<!--                                :width="240"-->
<!--                              />-->
<!--                            </n-input-group>-->
<!--                          </n-form-item>-->
<!--                        </n-gi>-->
                      </n-grid>
                    </n-form>
                  </template>
                  <!-- 操作项 -->
                  <template #tableHeaderBtn>
                    <n-button @click="formSearch" class="store-button" :loading="isLoading">刷 新</n-button>
                    <n-button type="info" @click="handleAddDistribution">新增</n-button>
                  </template>
                </FormLayout>
                <addDistributionModal ref="addDistributionModalShow"/>
              </div>
            </template>
          </TreeContentLayout>
          </n-spin>
        </template>
        <template v-else #all_products>
          <n-spin :show="distributeProductRangeLoad">
            <div style="text-align: center;
                 margin-top: 25%;
                 transform: translateY(-159px);"
            >
              该分销员可分销全部商品
            </div>
          </n-spin>
        </template>
    </JDrawer>
</template>
  
<script setup lang="tsx" name="breakoutDetails">
import { ref, onMounted } from "vue";
import { useMessages } from "@/hooks";
import { deepClone } from "@/utils";
import TreeContentLayout from "@/components/TreeContentLayout/index.vue";
import useGetDistributeClassify from "../hooks/useGetDistributeClassify";
import type { TreeOption } from 'naive-ui';
import ClassificationTree from "@/views/StoreModule/GoodsManagement/components/ClassificationTree.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import FormLayout from "@/layout/FormLayout.vue";
import addDistributionModal from './addDistributionModal.vue'
import { distributeProduct, distributeProductAdd, distributeProductDelete, updateProductRange } from "@/services/api";
import { IsPublishStatusLabels } from "@/constants";
import TablePreview from "@/components/TablePreview/index.vue";
import { GoodsCategoryType } from "@/enums";
/* 提示信息 */
const message = useMessages();
/** 商品分类 hook */
const {
  model,
  searchValue,
  getGoodsClassificationData,
  // handleScroll,
  treeData,
  selectedKeys,
  defaultExpandKeys,
  refresh,
  isGetLoading
} = useGetDistributeClassify();

/** 表格方法Hook */
const {
  isLoading,
  tableData,
  pageTableData,
  paginationRef,
  paginationChange,
} = useTableDefault({
  pageDataRequest: distributeProduct,
});
const distributeProductRangeLoad = ref(false)
/* 表格列表项 */
const tableColumns = ref([
    {
      title: "图片",
      key: "img",
      width: 150,
      align: "left",
      render: row => {
        if (row?.productImages?.length > 0) {
          let paths = row?.productImages.map(item => item.path);
          return <TablePreview src={paths}></TablePreview>;
        }
        return "-";
      },
    },
    {
      title: "商品",
      key: "productName",
      width: 180,
      align: "left",
      render: (row) => {
        // 普通商品
        if (row.type == GoodsCategoryType.GENERAL) {
          let title = `${row.frontName ?? ""}`;
          return <table-tooltip row={row} nameKey="name" title={title} idKey="productId" />;
        }
        // 疗法
        if (row.type == GoodsCategoryType.THERAPY) {
          let title = `${row.frontName ?? ""}`;
          return <table-tooltip row={row} nameKey="name" title={title} idKey="productId" />;
        }
        // 药品
        if (row.type == GoodsCategoryType.DRUG) {
          let name = row.productName ?? "";
          let title = `[${row.frontName ?? ""}] ${row.productName ?? ""} ${name}`;
          return <table-tooltip row={row} nameKey="name" title={title} idKey="productId" />;
        }
      }
    },
    {
      title: "库存",
      key: "availStocks",
      width: 150,
      align: "left"
    },
    {
      title: "上架状态",
      key: "isPublish",
      width: 150,
      align: "left",
      render: (row) => {
        return IsPublishStatusLabels[row.isPublish] ? IsPublishStatusLabels[row.isPublish] : '-'
      }
    },
    {
        title: "操作",
        key: "action",
        width: 100,
        align: "left",
        render: (row) => {
            return (
                <n-space>
                    <n-button
                        text
                        type="error"
                        onClick={() => {deleteDistribution(row.distributeProductId)}}
                      >
                        移除
                      </n-button>
                </n-space>
            )
        }
    },
])

const radiogroupData = ref([
    {
        label:'全平台商品',
        value:1
    },
    {
        label:'部分商品',
        value:2
    },
])

/** 请求数据状态 */
const isGetDetailLoading = ref(false)

/* 表单参数初始化 */
const initParams = {
  distributeProductRange:1,
  searchValue:'',
  productName:'',
  productCode:'',
  distributorId:'',
  isAddDistributeProduct:1,
  cateId:'',
  refreshTable:null,
  type:null
}

const detailModel = ref(deepClone(initParams));

const { createMessageSuccess, createMessageError,createMessageWarning } = useMessages();

/** 抽屉状态 */
const drawerVisible = ref(false);


/* 接收父组件传过来的参数 */
const acceptParams = async(params) => {
  detailModel.value.distributorId = params?.distributorId ?? null;
  searchValue.value.distributeId = params?.distributorId ?? null;
  detailModel.value.distributeProductRange = params?.distributeProductRange ?? null;
  detailModel.value.refreshTable = params?.refreshTable ?? null;
  if (detailModel.value.distributeProductRange === 2){
    reloadSearch()
  }
  drawerVisible.value = true;
};

/** 关闭抽屉 */
const closeDrawer = () => {
    detailModel.value = deepClone(initParams);
    drawerVisible.value = false;
};


/** 树形节点选中项发生变化时的回调函数 */
const handleUpdateSelectKeys = (keys: Array<string | number>, option: Array<TreeOption & Partial<ApiStoreModule.GoodsClassification> | null>, meta: { node: TreeOption | null, action: 'select' | 'unselect' }) => {
  if (keys.length !== 0) {
    const firstOption = option[0];
    selectedKeys.value = keys;
    model.value.cateId = firstOption?.id ?? null;
    // model.value.type = model.value.cateId ?? firstOption?.type ?? null;
    // console.log(model.value.type);
    // 加载状态
    // isGetLoading.value = true;
    detailModel.value.cateId = firstOption?.id ?? null;
    detailModel.value.type = model.value.cateId ? null : firstOption?.type;
    formSearch()
  }
  // setTimeout(()=>{
  //   isGetLoading.value = false;
  // })
};

const updateProduct = async() => {
  distributeProductRangeLoad.value = true
  try{
    await updateProductRange({
      data:{
        id:detailModel.value.distributorId,
        distributeProductRange:detailModel.value.distributeProductRange,
      }
    })
    message.createMessageSuccess('更改分销商品范围成功')
    if (detailModel.value.distributeProductRange === 2){
      reloadSearch()
    }
    detailModel.value.refreshTable()
  }catch(err){
    message.createMessageError('更改分销商品范围失败：' + err)
  }finally{
    distributeProductRangeLoad.value = false
  }
};
/** 获取参数 */
const getParams = () => {
  const { searchValue, productName, productCode, distributorId, isAddDistributeProduct, cateId, type} = detailModel.value;
  return {
    searchValue,
    productName,
    productCode,
    distributorId,
    isAddDistributeProduct,
    cateId,
    type
  };
};

/** 搜索 */
const formSearch = () =>{
  pageTableData(getParams(), paginationRef.value);
}
const reloadSearch = () => {
  formSearch()
  refresh()
}
/** 添加分销员 */
const addDistributionModalShow = ref()
const handleAddDistribution = () =>{
  addDistributionModalShow.value.acceptParams({
    distributorId:detailModel.value.distributorId,
    refreshTable:reloadSearch
  })
}

const deleteDistribution = async(id) => {
  isLoading.value = true
  try{
    await distributeProductDelete(id)
    message.createMessageSuccess('移除分销商品成功')
    setTimeout(() => {
      isLoading.value = false
      formSearch()
      refresh()
    }, 200)
  }catch(err){
    message.createMessageError('移除分销商品成功失败：' + err)
    isLoading.value = false
  }
};

/** 组件挂载 */
onMounted(() => {
  model.value.pharmaceuticalsCommodities = true
});

defineExpose({
  acceptParams,
});
</script>
  
<style scoped lang="less">
@import "@/styles/default.less";
@import "@/styles/defaultVar.less";
.classify-container {
  width: 100%;
  .header {
    padding: 12px;
  }
  .classification-tree-container {
    width: 100%;
    height: calc(100% - 92px);
    :deep(.n-spin-content) {
			height: 100%;
    }
  }
}
::v-deep .inner-page-height{
  max-height: calc(100vh - 256px) !important;
}
</style>
  