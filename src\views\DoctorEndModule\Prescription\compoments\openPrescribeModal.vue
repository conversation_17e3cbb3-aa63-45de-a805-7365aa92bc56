<template>
  <div v-if="userStore.userInfo.hasAddPreAuth">
    <n-button size="tiny" type="info" @click="handleOpenPrescriptionModal">
      开处方
    </n-button>
  </div>
  <PrescriptionModal mode="add" v-model:show="prescriptionModalShow" params=""/>
</template>

<script setup lang="ts">
import PrescriptionModal from "@/views/DoctorEndModule/Prescription/compoments/prescriptionModal.vue";
import {useUserStore} from "@/stores/modules/user";
import {ref} from "vue";

const userStore = useUserStore();
const prescriptionModalShow = ref(false)

function handleOpenPrescriptionModal() {
  prescriptionModalShow.value = !prescriptionModalShow.value
}


</script>

<style scoped lang="less">

</style>
