<template>
  <JDrawer
    v-model:show="drawerVisible"
    title="订单详情"
    :isGetLoading="loadShow"
    :isShowFooter="orderStatus"
    @after-leave="closeDrawer"
    to="#OrderManagement"
  >
    <template #content>
      <!-- 用药人信息 -->
      <template v-if="model.attribute == 12">
        <StoreTitle title="处方信息" style="margin-bottom: 12px;" />
        <div class="prescription_info_wrapper">
          <!-- 第一列 -->
          <div class="prescription_info_wrapper_1">
            <!-- 真实姓名 -->
            <JFieldItem label="开方状态">
              <NSelect v-model:value="model.presStatus" disabled :options="presStatusOptions" size="small" />
            </JFieldItem>
            <!-- 处方图片 -->
            <JFieldItem v-if="model.presStatus !== 3 && model.presStatus !== 1" label="处方图片">
              <NImage v-if="model.presFile" width="100" height="100" :src="model.presFile" />
              {{ !model.presFile ? '-' : '' }}
            </JFieldItem>
          </div>
          <!-- 第二列 -->
          <div class="prescription_info_wrapper_2">
            <!-- 处方编号 -->
            <JFieldItem v-if="model.presSystem === 2 && model.presStatus !== 3" label="处方编号">
              <NInputGroup v-if="model.thirdHospitalCode">
                <n-input v-model:value="model.thirdHospitalCode" readonly style="width: 86%;" />
                <n-button v-copy="model.thirdHospitalCode">复制</n-button>
              </NInputGroup>
              <span v-else>-</span>
            </JFieldItem>
            <!-- 开方失败原因 -->
            <JFieldItem v-if="model.presSystem === 2 && model.presStatus !== 2" label="开方失败原因">
              {{model.reason ? model.reason : '-' }}
            </JFieldItem>
            <!-- 开方失败原因 -->
            <JFieldItem v-else-if="model.presStatus === 3" label="开方失败原因">取消开方</JFieldItem>
          </div>
          <!-- 第三列 -->
          <div class="prescription_info_wrapper_3">
            <!-- 处方有效期 -->
            <JFieldItem v-if="model.presSystem === 2 && model.presStatus !== 3" label="处方有效期">
              {{ model.thirdHospitalValidityTime ? model.thirdHospitalValidityTime : '-' }}
            </JFieldItem>
          </div>
        </div>
      </template>
      <!-- 订单信息 -->
      <StoreTitle title="订单信息" style="margin-bottom: 12px;" />
      <div class="order_info_wrapper">
        <!-- 第一列 -->
        <div class="order_info_wrapper_1">
          <!-- 订单编号 -->
          <JFieldItem label="订单编号">
            <NInputGroup>
              <NInput v-model:value="model.code" readonly style="width: 86%;" size="small" />
              <NButton v-copy="model.code" size="small">复制</NButton>
            </NInputGroup>
          </JFieldItem>
          <!-- T9订单编号 -->
          <JFieldItem v-if="model.thirdCode" label="T9订单编号">
            <NInputGroup>
              <NInput v-model:value="model.thirdCode" readonly style="width: 86%;" size="small" />
              <NButton v-copy="model.thirdCode" size="small">复制</NButton>
            </NInputGroup>
          </JFieldItem>
          <!-- 订单状态 -->
          <JFieldItem label="订单状态">
            <NSelect v-model:value="model.status" disabled :options="orderStatusOptions" size="small" />
          </JFieldItem>
          <!-- 锁单状态 -->
          <JFieldItem label="锁单状态">
            <NSelect v-model:value="model.isLocked " disabled :options="isLockedOptions" size="small" />
          </JFieldItem>
          <!-- 收货姓名 -->
          <JFieldItem label="收货姓名">
            <NInput v-model:value="model.name" readonly size="small" />
          </JFieldItem>
          <!-- 收货手机号 -->
          <JFieldItem label="收货手机号">
            <NInputGroup>
              <NInput v-model:value="model.consigneeMobile" readonly style="width: 86%;" size="small" />
              <NButton v-copy="model.consigneeMobile" size="small">复制</NButton>
            </NInputGroup>
          </JFieldItem>
          <!-- 收货地址 -->
          <JFieldItem label="收货地址">
            <NInput
              v-model:value="model.address"
              type="textarea"
              placeholder="请输入收货地址"
              readonly
              size="small"
              :autosize="{
                minRows: 3,
                maxRows: 5
              }"
            />
          </JFieldItem>
        </div>
        <!-- 第二列 -->
        <div
          v-if="
            model.fromType !== 7 && 
            (model.payType == OrderPayTypeEnum.NONE || 
            model.payType == OrderPayTypeEnum.ONLINE || 
            model.payType == OrderPayTypeEnum.LOGISTICS || 
            model.payType == OrderPayTypeEnum.DEPOSIT) && 
            (tabValue == OrderManagementSelection.accomplish || 
            tabValue == OrderManagementSelection.cancelled || 
            statusRef == OrderStatusEnum.Canceled) || 
            marketplaceType == SystemStoreType.PHARMACEUTICALMALL
          "
          class="order_info_wrapper_2"
        >
          <!-- 商品名称 -->
          <JFieldItem label="商品名称">
            <span class="title">
              {{model.productName ? model.productName : '-' }}
            </span>
            <NPopover trigger="hover" v-if="model.moreGoodsPoint.length > 0">
              <template #trigger>
                <span class="title" style="color: #FF3E3E;font-size: 12px;cursor: pointer;">等{{ model.moreGoodsPoint.length + 1 }}种商品</span>
              </template>
              <span>{{ model.moreGoodsPoint.join(',') }}</span>
            </NPopover>
          </JFieldItem>
        </div>
        <!-- 第三列 -->
        <div class="order_info_wrapper_3">
          <!-- 商品总额 -->
          <JFieldItem v-if="model.payType===6" label="商品总额">
            {{ model.totalCoupons + '张' + model.categoryName }}
          </JFieldItem>
          <JFieldItem v-else label="商品总额">
            {{ model.goodsAmount ? `${model.goodsAmount}` : '￥0.00'  }}
          </JFieldItem>
          <!-- 邮费 -->
          <JFieldItem label="邮费">
            {{ model.shippingFee ? `￥${model.shippingFee}` : '￥0.00'  }}
          </JFieldItem>
          <!-- 现金抵扣券 -->
          <JFieldItem label="现金抵扣券">
            {{ model.cashCouponAmt ? `￥${model.cashCouponAmt}` : '￥0.00'  }}
          </JFieldItem>
          <!-- 订单总金额 -->
          <JFieldItem label="订单总金额">
            {{ model.money ? `￥${model.money}` : '￥0.00'  }}
          </JFieldItem>
          <!-- 支付方式 -->
          <JFieldItem label="支付方式">
            <NSelect v-model:value="model.payType" disabled :options="payTypeOptions" size="small" />
          </JFieldItem>
          <!-- 在线支付金额 -->
          <JFieldItem
            v-if="
              model.fromType !== 7 && 
              tabValue != OrderManagementSelection.cancelled && 
              tabValue != OrderManagementSelection.waitPay && 
              model.payType != OrderPayTypeEnum.POINTS && 
              model.payType != OrderPayTypeEnum.POINTS_AND_CASH && 
              statusRef != OrderStatusEnum.Unpaid && 
              statusRef != OrderStatusEnum.Canceled
            "
            label="在线支付金额"
          >
            {{ model.onlinePayment ? `${model.onlinePayment}` : '-'  }}
          </JFieldItem>
          <!-- 支付礼品券 -->
          <JFieldItem v-if="model.payType===6 && model.totalCoupons && model.categoryName" label="支付礼品券">
            <TableTooltip
              :row="model"
              nameKey="couponCateId"
              :title="model.totalCoupons + '张' + model.categoryName"
              idKey="couponCateId"
            />
          </JFieldItem>
          <!-- 支付积分 -->
          <JFieldItem
            label="支付积分"
            v-if="model.payType == OrderPayTypeEnum.POINTS || model.payType == OrderPayTypeEnum.POINTS_AND_CASH"
          >
            {{ model.totalPoints ? `${model.totalPoints}` : '-'  }}
          </JFieldItem>
          <!-- 支付现金 -->
          <JFieldItem label="支付现金" v-if="model.payType == OrderPayTypeEnum.POINTS_AND_CASH">
            {{ model.cashPayment ? `￥${model.cashPayment}` : '￥0.00'  }}
          </JFieldItem>
          <!-- 物流代收金额 -->
          <JFieldItem
            v-if="
              tabValue != OrderManagementSelection.cancelled && 
              tabValue != OrderManagementSelection.waitPay && 
              model.payType != OrderPayTypeEnum.POINTS && 
              model.payType != OrderPayTypeEnum.POINTS_AND_CASH && 
              statusRef != OrderStatusEnum.Unpaid && 
              statusRef != OrderStatusEnum.Canceled
            "
            label="物流代收金额"
          >
            {{ model.cashOnDelivery ? `￥${model.cashOnDelivery}` : '￥0.00'  }}
          </JFieldItem>
          <!-- 支付状态 -->
          <JFieldItem label="支付状态">
            <NSelect v-model:value="model.payStatus" disabled :options="payStatusOptions" size="small" />
          </JFieldItem>
          <!-- 支付单号 -->
          <JFieldItem label="支付单号">
            <NInputGroup>
              <NInput
                v-model:value="model.wxPayNo"
                readonly
                style="width: 86%;"
                placeholder="暂无支付单号"
                size="small"
              />
              <NButton v-copy="model.wxPayNo" size="small">复制</NButton>
            </NInputGroup>
          </JFieldItem>
          <!-- 商户号 -->
          <JFieldItem v-if="model.wxPayNo" label="商户号">
            <NInput
              type="textarea"
              :autosize="{minRows: 1}"
              v-model:value="model.merchantNumber"
              readonly
              placeholder="暂无商户号"
            />
          </JFieldItem>
        </div>
        <!-- 第四列 -->
        <div class="order_info_wrapper_4">
          <!-- 门店名称 -->
          <JFieldItem label="门店名称">
            {{ model.storeName ? model.storeName : '-'  }}
          </JFieldItem>
          <!-- 门店联系人 -->
          <JFieldItem label="门店联系人">
            {{ model.contactName ? model.contactName : '-'  }}
          </JFieldItem>
          <!-- 联系电话 -->
          <JFieldItem label="联系电话">
            {{ model.contactPhone ? model.contactPhone : '-'  }}
          </JFieldItem>
          <!-- 门店地址 -->
          <JFieldItem label="门店地址">
            {{ model.addressDetail ? model.addressDetail : '-'  }}
          </JFieldItem>
          <!-- 用户昵称 -->
          <JFieldItem label="用户昵称">
            {{ model.customerNickName ? model.customerNickName : '-'  }}
          </JFieldItem>
          <!-- 用户ID -->
          <JFieldItem label="用户ID">
            {{ model.customerShortId ? model.customerShortId : '-'  }}
          </JFieldItem>
        </div>
        <!-- 第五列 -->
        <div class="order_info_wrapper_5">
          <!-- 创建时间 -->
          <JFieldItem label="创建时间">
            {{ model.createTime ? model.createTime : '-' }}
          </JFieldItem>
          <!-- 核销时间 -->
          <JFieldItem v-if="model.fromType === 7" label="核销时间">
            {{ model.verificationTime ? model.verificationTime : '-' }}
          </JFieldItem>
          <!-- 收货方式 -->
          <JFieldItem v-if="model.fromType === 7" label="收货方式">
            <NSelect v-model:value="model.pickupType " disabled :options="pickupTypeOptions" size="small" />
          </JFieldItem>
          <!-- 核销门店成员 -->
          <JFieldItem v-if="model.fromType === 7" label="核销门店成员">
            <TableTooltip
              :row="model"
              nameKey="operatorShortId"
              :title="model.operatorName ? model.operatorName : '-'"
              idKey="operatorShortId"
            />
          </JFieldItem>
          <!-- 发货时间 -->
          <JFieldItem
            v-if="
              tabValue == OrderManagementSelection.waitTakeOverGoods || 
              tabValue == OrderManagementSelection.accomplish || 
              statusRef == OrderStatusEnum.Unreceived || 
              statusRef == OrderStatusEnum.Completed
            "
            label="发货时间"
          >
            {{ model.shipTime ? model.shipTime : '-' }}
          </JFieldItem>
          <!-- 签收时间 -->
          <JFieldItem
            v-if="
              tabValue == OrderManagementSelection.accomplish || 
              statusRef == OrderStatusEnum.Completed
            "
            label="签收时间"
          >
            {{ model.completeTime ? model.completeTime : '-' }}
          </JFieldItem>
          <!-- 快递公司名称 -->
          <JFieldItem
            v-if="
              tabValue == OrderManagementSelection.waitTakeOverGoods || 
              tabValue == OrderManagementSelection.accomplish || 
              statusRef == OrderStatusEnum.Unreceived || 
              statusRef == OrderStatusEnum.Completed"
            label="快递公司名称"
          >
            {{ model.shipCompanyName ? model.shipCompanyName : '-' }}
          </JFieldItem>
          <!-- 快递单号 -->
          <JFieldItem
            v-if="
              tabValue == OrderManagementSelection.waitTakeOverGoods || 
              tabValue == OrderManagementSelection.accomplish || 
              statusRef == OrderStatusEnum.Unreceived || 
              statusRef == OrderStatusEnum.Completed
            "
            label="快递单号"
          >
            <NInputGroup v-if="model.trackingNo">
              <NInput v-model:value="model.trackingNo" readonly style="width: 86%;" size="small" />
              <NButton v-copy="model.trackingNo" size="small">复制</NButton>
            </NInputGroup>
            <span v-else>-</span>
          </JFieldItem>
          <!-- 签收类型 -->
          <JFieldItem
            v-if="
              tabValue == OrderManagementSelection.waitTakeOverGoods || 
              tabValue == OrderManagementSelection.accomplish || 
              statusRef == OrderStatusEnum.Unreceived || 
              statusRef == OrderStatusEnum.Completed
            "
            label="签收类型"
          >
            <NSelect v-model:value="model.completeType" disabled :options="completeTypeOptions" size="small" />
          </JFieldItem>
        </div>
      </div>
      <!-- 订单项 -->
      <StoreTitle title="订单项" style="margin-bottom: 12px;" />
      <div class="order_item_wrapper">
        <FormLayout
          style="width: 98%;height: 320px;"
          :tableData="tableData"
          :tableColumns="tableColumns"
          :is-table-pagination="false"
          :is-table-selection="false"
          :is-display-header="false"
        />
      </div>
      <template v-if="userOtherInformationStatus">
        <!--  其他信息 -->
        <StoreTitle title="其他信息" style="margin-bottom: 12px;" />
        <div class="other_info_wrapper">
          <!-- 第一列 -->
          <div class="other_info_wrapper_1">
            <!-- 订单来源  -->
            <JFieldItem label="订单来源 ">
              <NSelect v-model:value="model.fromType" :options="orderSourceOptions" disabled size="small" />
            </JFieldItem>
            <!-- 归属群管 -->
            <JFieldItem label="归属群管" v-if="model.thirdGroupMgrName">
              {{model.thirdGroupMgrName ? model.thirdGroupMgrName : '-' }}
            </JFieldItem>
            <!-- 归属经销商 -->
            <JFieldItem label="归属经销商" v-if=" model.thirdDealerName">
              {{model.thirdDealerName ? model.thirdDealerName : '-' }}
            </JFieldItem>
            <!-- 归属课程 -->
            <JFieldItem label="归属课程" v-if=" model.thirdCourseName">
              {{model.thirdCourseName ? model.thirdCourseName : '-' }}
            </JFieldItem>
            <!-- 公众号名称 -->
            <JFieldItem label="公众号名称" v-if=" model.thirdWxappEntityName">
              {{model.thirdWxappEntityName ? model.thirdWxappEntityName : '-' }}
            </JFieldItem>
            <!-- 归属训练营 -->
            <JFieldItem label="归属训练营" v-if=" model.thirdCampName">
              {{model.thirdCampName ? model.thirdCampName : '-' }}
            </JFieldItem>
            <!-- 归属营期 -->
            <JFieldItem label="归属营期" v-if=" model.thirdCampPeriodName">
              {{model.thirdCampPeriodName ? model.thirdCampPeriodName : '-' }}
            </JFieldItem>
            <!-- 归属分销员 -->
            <JFieldItem label="归属分销员" v-if=" model.distributorName">
              {{model.distributorName ? model.distributorName : '-' }}
            </JFieldItem>
          </div>
        </div>
      </template>
    </template>

    <template v-if="afterSaleManagementLabels[model.action] == '客户申请退款'" #footer>
      <div class="footer-wrapper">
        <NSpace>
          <NButton type="info" class="store-button" @click="handleRefund(drawerProps.row)" :loading="refundLoading">
            退款
          </NButton>
        </NSpace>
      </div>
    </template>
  </JDrawer>
  <!-- 退款 -->
  <RefundModal ref="refundModalShow" />
</template>

<script setup lang="tsx">
import { ref, reactive, computed } from "vue";
import { deepClone } from "@/utils";
import { useMessages } from "@/hooks";
import { useRouter } from "vue-router";
import {RoutesName} from '@/enums/routes';
import {
  payTypeLabels,
  payTypeOptions,
  payStatusOptions,
  orderStatusOptions,
  isLockedOptions,
  pickupTypeOptions,
  presStatusOptions,
  completeTypeOptions,
  orderSourceOptions,
  merchandiseTypeOptions,
  orderStatusLabels,
  afterSaleManagementLabels
} from "@/constants";
import { OrderManagementSelection, SystemStoreType, OrderPayTypeEnum, OrderStatusEnum } from '@/enums';
import { useSystemStoreWithoutSetup} from "@/stores/modules/system";
import { transformMinioSrc } from "@/utils";
import { orderDetail } from '@/services/api';
/** 相关组件 */
import FormLayout from "@/layout/FormLayout.vue";
import TableTooltip from "@/components/TableTooltip/index.vue";
import JFieldItem from '@/components/JFieldItem/index.vue';
import RefundModal from './RefundModal.vue';

defineOptions({
  name: "DrawerOrderDetails",
});

const systemStore = useSystemStoreWithoutSetup();
/** 系统商城模式 */
const marketplaceType = computed(() => {
  return systemStore._globalConfig['marketplaceType'];
});
const { createMessageError, createMessageWarning } = useMessages();
const router = useRouter();
const loadShow = ref(false);
/** 存储原本未取消处方药字段 */
const removedColumnState = ref(false);
/** 抽屉状态 */
const drawerVisible = ref(false);
const drawerProps = ref();
const orderCode = ref();
const userOtherInformationStatus = ref(true);
const tabValue = ref(null);
/** 订单状态 */
const statusRef = ref(null);
/** 商品名称显隐 */


/* 表单参数初始化 */
const initParams = {
  code: null,                   // 订单编号
  status: null,                 // 订单状态
  name: null,                   // 收货人姓名
  consigneeMobile: null,        // 收货人手机号
  address: null,                // 收货地址
  productName: null,            // 商品名称
  moreGoodsPoint: [],           // 商品累计提示
  isPres: null,                 // 是否为处方药
  goodsAmount: null,            // 商品总金额
  shippingFee: null,            // 运费
  money: null,                  // 订单总金额
  cashCouponAmt: null,          // 现金抵扣券
  payType: null,                // 支付方式
  onlinePayment: null,          // 在线支付金额
  cashOnDelivery: null,         // 物流代收金额
  payStatus: null,              // 支付状态
  shipTime: null,               // 发货时间
  completeTime: null,           // 签收时间
  createTime: null,             // 创建时间
  attribute: null,              // 订单的商品属性

  realName: null,               // 真实姓名
  relation: null,               // 与您关系
  age: null,                    // 年龄
  gender: null,                 // 性别
  mobile: null,                 // 手机号
  historyDisease: null,         // 病史
  currentPeriod: null,          // 当前阶段

  thirdGroupMgrName: null,      // 归属群管
  thirdCourseName: null,        // 归属课程
  fromType: null,               // 来源

  shipCompanyName: null,        // 物流公司
  trackingNo: null,             // 快递单号
  completeType: null,           // 签收状态
  totalPoints: null,            // 兑换积分
  cashPayment: null,            // 支付现金
  wxPayNo: null,                // 支付单号
  thirdCode: null,              // 第三方系统订单编号
  thirdWxappEntityName: null,   // 公众号名称
  thirdDealerName: null,        // 归属经销商
  merchantNumber: null,         // 商户号
  thirdCampName: null,          // 归属训练营
  thirdCampPeriodName: null,    // 归属营期
  distributorName: null,        // 归属分销员
  action: null,

  presStatus: null,             // 处方状态
  thirdHospitalCode: null,      // 第三方互联网医院返回的处方号
  thirdHospitalValidityTime: null, // 第三方互联网医院返回的处方有效期
  presFile: null,               // 处方图片地址
  reason: null,                 // 开方失败原因
  presSystem: null,             // 开方系统

  isLocked: null,               // 锁单状态
  storeName: null,              // 门店名称
  contactName: null,            // 门店联系人
  contactPhone: null,           // 联系电话
  addressDetail: null,          // 门店地址
  customerNickName: null,       // 用户昵称
  customerShortId: null,             // 用户ID
  verificationTime: null,       // 核销时间
  pickupType: null,             // 提货方式
  operatorName: null,           // 核销门店成员昵称
  supplierName: null,           // 供应商昵称
  totalCoupons: null,           // 数量
  categoryName: null,           // 福利卷名称
  couponCateId: null,           // 福利券id
  operatorShortId: null,             // 核销门店成员id
};
const model = ref(deepClone(initParams));

const tableData = reactive([]);
/* 表格项 */
const tableColumns = reactive([
  {
    title: "序号",
    width: 80,
    fixed: "left",
    key: "index",
    align: "center",
    render: (_, index) => `${index + 1}`,
  },
  {
    title: "商品",
    key: "productName",
    align: "left",
    fixed: "left",
    width: 200,
    ellipsis: { tooltip: true },
    render: rowData => {
      let productName;
      if(rowData.productFrontName && rowData.productFrontName != ''){
        if(rowData.type == merchandiseTypeOptions[0].value){
          productName = '[' + rowData.productFrontName +']' + rowData.productName + rowData.specName
        }
        else if(rowData.type == merchandiseTypeOptions[1].value){
          productName =  rowData.productName
        }
        else if(rowData.type == merchandiseTypeOptions[2].value){
          productName =  rowData.productName + rowData.specName
        }
        else {
          productName = rowData.productName + rowData.specName
        }
      }else{
          productName = '-'
      }
      return productName
    }
  },
  {
    title: "单价",
    key: "price",
    align: "left",
    width: 100,
    ellipsis: { tooltip: true },
    render: (rowData) => {
      if (rowData.isPoint == 1) {
        const points = rowData.exchangePoints || '0';
        const price = rowData.exchangePrice
          ? `+￥${(rowData.exchangePrice / 100).toFixed(2)}`
          : '';
        return `${points}积分${price}`;
      }
      return rowData.price ? (rowData.price / 100).toFixed(2) : '-';
    }
  },
  {
    title: "数量",
    key: "count",
    align: "left",
    fixed: "left",
    ellipsis: { tooltip: true },
    width: 100,
  },
  {
    title: "供应商昵称",
    key: "supplierName",
    align: "left",
    fixed: "left",
    ellipsis: { tooltip: true },
    width: 100,
  },
  {
    title: "采购单价",
    key: "costPrice",
    align: "left",
    width: 100,
    ellipsis: { tooltip: true },
    render: (rowData) => rowData.costPrice
      ? (rowData.costPrice / 100).toFixed(2)
      : '-'
  },
  {
    title: "操作",
    key: "action",
    width: 80,
    align: "center",
    fixed: "right",
    render: (row) => (
      <n-space align="center" justify="center">
        <n-button
          text
          size="small"
          type="primary"
          onClick={() => clickViewGoods(row)}
        >
          查看商品
        </n-button>
      </n-space>
    ),
  },
]);

/* 接收父组件传过来的参数 */
const acceptParams = async(params) => {
  drawerVisible.value = true;
  drawerProps.value = params;
  orderCode.value =  params.row.code;
  tabValue.value = params.tabValue;
  statusRef.value = params.row.status;
  getDetails();
};

/* 获取订单详情 */
async function getDetails() {
  loadShow.value = true; // 显示加载状态

  try {
    // 调用API获取订单详情数据
    const data = await orderDetail(orderCode.value);
    if (!data) return; // 如果没有数据则直接返回

    // 使用解构赋值从data中提取所有需要的字段，并设置默认值
    const {
      code,
      fromType,
      money,
      cashCouponAmt,
      payStatus,
      shipTime,
      payType,
      thirdCourseName,
      completeTime,
      status,
      createTime,
      orderItemList = [], // 订单项列表，默认空数组
      thirdGroupMgrName,
      shippingFee,
      onlinePayment,
      cashOnDelivery,
      completeType,
      shipCompanyName,
      trackingNo,
      goodsAmount,
      customerAddressDTO,
      customerShortId,
      presEntityDTO,
      totalPoints,
      wxPayNo,
      thirdCode,
      thirdWxappEntityName,
      thirdDealerName,
      merchantCompany,
      merchantId,
      presStatus,
      merchantPlatformName,
      thirdCampName,
      thirdCampPeriodName,
      distributorName,
      action,
      attribute,
      isLocked,
      totalCoupons,
      categoryName,
      pickupType,
      operatorName,
      supplierName,
      storeEntityDTO,
      orderVerificationDTO,
      customerNickname,
      couponCateId,
      operatorShortId
    } = data;

    // 初始化model对象，保留原有值并设置新值
    model.value = {
      ...model.value, // 保留原有属性
      code: code ?? null, // 订单编号
      status: status ?? null, // 订单状态
      moreGoodsPoint: [], // 多商品时的额外商品名称
      isLocked: isLocked ?? 0 // 是否锁定，默认为0
    };

    // ------------------------- 处理客户地址信息 -------------------------
    if (customerAddressDTO) {
      const { name, mobile, company, province, cityName, area, town, address } = customerAddressDTO;
      model.value.name = name ?? null; // 收货人姓名
      model.value.consigneeMobile = mobile ?? null; // 收货人手机号

      // 拼接完整地址
      model.value.address = customerAddressDTO
        ? `${company || ''}${province || ''}${cityName || ''}${area || ''}${town || ''}${address || ''}`
        : '-';
    }

    // ------------------------- 处理订单商品项 -------------------------
    if (orderItemList.length >= 1) {
      // 第一个商品作为主商品，其余商品名称存入moreGoodsPoint
      orderItemList.forEach((item, index) => {
        if (index !== 0) {
          model.value.moreGoodsPoint.push(item.productName);
        }
      });
      model.value.productName = orderItemList[0].productName ?? null; // 主商品名称
      model.value.isPres = orderItemList[0].isPres ?? null; // 是否处方药
    }

    // ------------------------- 处理门店信息 -------------------------
    if (storeEntityDTO) {
      model.value.storeName = storeEntityDTO.storeName ?? null; // 门店名称
      model.value.contactName = storeEntityDTO.contactName ?? null; // 联系人
      model.value.contactPhone = storeEntityDTO.contactPhone ?? null; // 联系电话

      // 拼接门店地址
      model.value.addressDetail = [
        storeEntityDTO.province,
        storeEntityDTO.city,
        storeEntityDTO.area,
        storeEntityDTO.addressDetail
      ].join('');
    }
    model.value.operatorShortId = operatorShortId ?? null; // 操作人ID
    // ------------------------- 处理订单核销信息 -------------------------
    if (orderVerificationDTO) {
      model.value.customerShortId = customerShortId ?? null; // 客户ID
      model.value.verificationTime = orderVerificationDTO.verificationTime ?? null; // 核销时间
    
    }

    // ------------------------- 处理支付金额显示 -------------------------
    if (payType != OrderPayTypeEnum.POINTS && payType != OrderPayTypeEnum.POINTS_AND_CASH) {
      // 非积分支付：显示现金金额
      model.value.goodsAmount = goodsAmount ? '￥' + (goodsAmount / 100).toFixed(2) : null;
      userOtherInformationStatus.value = true; // 显示额外用户信息
    } else if (payType == OrderPayTypeEnum.POINTS) {
      // 纯积分支付：显示积分
      model.value.goodsAmount = totalPoints ? (totalPoints + '积分') : null;
    } else if (payType == OrderPayTypeEnum.POINTS_AND_CASH) {
      // 积分+现金支付：显示两者
      model.value.goodsAmount = totalPoints
        ? (totalPoints + '积分' + ' + ' + '￥' + (goodsAmount ? (goodsAmount / 100).toFixed(2) : null))
        : null;
    }

    // 金额格式化函数（分转元）
    const formatMoney = (value) => value ? (value / 100).toFixed(2) : null;

    // 处理各种金额字段
    model.value.shippingFee = formatMoney(shippingFee); // 运费
    model.value.money = formatMoney(money); // 总金额
    model.value.cashCouponAmt = formatMoney(cashCouponAmt); // 总金额
    model.value.onlinePayment = formatMoney(onlinePayment); // 在线支付金额
    model.value.cashOnDelivery = formatMoney(cashOnDelivery); // 货到付款金额
    model.value.cashPayment = formatMoney(goodsAmount); // 现金支付部分

    // ------------------------- 处理简单属性赋值 -------------------------
    // 需要直接赋值的简单属性列表
    const simpleProps = {
      payType, payStatus, couponCateId, attribute, shipTime, completeTime,
      createTime, pickupType, operatorName, supplierName, totalCoupons,
      categoryName, customerNickName: customerNickname, totalPoints,
      wxPayNo, thirdCode, thirdWxappEntityName, thirdDealerName,
      thirdCampName, thirdCampPeriodName, distributorName, action, presStatus,
      thirdGroupMgrName, thirdCourseName, fromType, shipCompanyName,
      trackingNo, completeType
    };

    // 批量处理简单属性赋值
    Object.entries(simpleProps).forEach(([key, value]) => {
      model.value[key] = value ?? null; // 使用nullish coalescing处理undefined/null
    });

    // ------------------------- 处理商户信息 -------------------------
    model.value.merchantNumber = [
      merchantId || '', // 商户ID
      (merchantPlatformName || merchantCompany)
        ? ` 【${merchantPlatformName || ''},${merchantCompany || ''}】`
        : ''
    ].join('');

    // ------------------------- 处理处方信息 -------------------------
    if (presEntityDTO) {
      const { thirdHospitalCode, thirdHospitalValidityTime, presFile, reason, presSystem } = presEntityDTO;
      model.value.thirdHospitalCode = thirdHospitalCode ?? null; // 医院编码
      model.value.thirdHospitalValidityTime = thirdHospitalValidityTime ?? null; // 处方有效期
      model.value.presSystem = presSystem ?? null; // 处方系统
      model.value.reason = reason ?? null; // 原因

      // 处理处方文件URL
      if (presFile && presFile !== '') {
        model.value.presFile = presFile.startsWith("http")
          ? presFile // 已经是完整URL则直接使用
          : transformMinioSrc(presFile); // 否则转换MinIO地址
      }
    }

    // ------------------------- 处理表格数据 -------------------------
    if (orderItemList.length !== 0) {
      tableData.push(...orderItemList); // 添加所有订单项到表格数据
    }

    // ------------------------- 根据支付类型处理表格列 -------------------------
    const isPointsPayment = payType == OrderPayTypeEnum.POINTS || payType == OrderPayTypeEnum.POINTS_AND_CASH;
    const presColumnIndex = tableColumns.findIndex(column => column.key === "isPres");

    // 积分支付时移除"处方药"列
    if (isPointsPayment && presColumnIndex !== -1) {
      tableColumns.splice(presColumnIndex, 1);
      removedColumnState.value = true;
    }
    // 非积分支付且之前移除了列时，重新添加"处方药"列
    else if (!isPointsPayment && removedColumnState.value) {
      const newColumn = {
        title: "处方药",
        key: "isPres",
        align: "left",
        width: 100,
        ellipsis: { tooltip: true },
        render: rowData => rowData.isPres == 1 ? '是' : '否'
      };
      tableColumns.splice(2, 0, newColumn); // 在第二位置插入列
      removedColumnState.value = false;
    }

  } catch (err) {
    // 错误处理
    createMessageError('获取详情数据失败: ' + err);
  } finally {
    loadShow.value = false;
  }
};

/** 关闭抽屉 */
const closeDrawer = () => {
  model.value = deepClone(initParams);
  drawerVisible.value = false;
  tableData.length = 0
};

/** 查看商品 */
const clickViewGoods = (row) =>{
  if(model.value.thirdCode){
    createMessageWarning('请到第三方平台查看商品详情!');
    return
  }
  const productName = row?.productName.match(/\[(.*?)\]/)?.[1] || row?.productName;
  router.push({ name:RoutesName.GoodsManagement,
    query: {
      goodsName:productName
  }});
}

/** 当前进入的订单状态 */
const orderStatus = computed(() => {
  const validStatuses = new Set(['待发货', '待收货', '已完成']);
  const validPayTypes = new Set(['在线支付', '支付定金', '积分 + 现金']);

  const currentStatus = orderStatusLabels[statusRef.value];
  const currentPayType = payTypeLabels[drawerProps.value?.row.payType];

  // 如果状态是"待发货"或"待收货"，并且支付方式匹配
  if (validStatuses.has(currentStatus) &&
      ['待发货', '待收货'].includes(currentStatus) &&
      validPayTypes.has(currentPayType)) {
    return true;
  }

  // 如果状态是"已完成"
  if (currentStatus === '已完成') {
    if(payTypeLabels[drawerProps.value?.row.payType] != '纯积分支付'){
      return true;
    }
  }

  return false;
});
const shouldHidePrescription = computed(() => { 
    // 显式定义有效条件：旧版本处方（0）且属性为12
    const isValidPrescription = 
      model.value.presVersionType === 0 && 
      model.value.attribute === 12;
    
    // 取反作为隐藏条件
    return !isValidPrescription;
});
const shouldHideInquiryPrescription = computed(() => { 
    // 显式定义有效条件：新版本处方（1）且属性为12
    const isValidPrescription = 
      model.value.presVersionType === 1 && 
      model.value.attribute === 12;
    
    // 取反作为隐藏条件
    return !isValidPrescription;
});
/** 退款 */
const refundModalShow = ref();
const refundLoading = ref(false);
const handleRefund = (row) =>{
    const _params = {
      row: row,
      refresh: drawerProps.value.refresh,
      closeDrawer: closeDrawer,
      payType: payTypeLabels[row['payType']],
      status: orderStatusLabels[row['status']],
    };
    refundModalShow.value.acceptParams(_params)
}

defineExpose({
  acceptParams,
  closeDrawer,
  drawerVisible
});
</script>

<style scoped lang="less">
@import "@/styles/default.less";
@import "@/styles/defaultVar.less";

.prescription_info_wrapper {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  box-sizing: border-box;
  margin-bottom: 12px;
  .prescription_info_wrapper_1,
  .prescription_info_wrapper_2,
  .prescription_info_wrapper_3 {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
}

.order_info_wrapper {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  box-sizing: border-box;
  margin-bottom: 12px;
  .order_info_wrapper_1,
  .order_info_wrapper_2,
  .order_info_wrapper_3,
  .order_info_wrapper_4,
  .order_info_wrapper_5 {
    display: flex;
    flex-direction: column;
    gap: 24px;

    .title {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 20px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
}

.order_item_wrapper {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  box-sizing: border-box;
  margin-bottom: 12px;
}

.other_info_wrapper {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  box-sizing: border-box;
  margin-bottom: 12px;
  .other_info_wrapper_1,
  .other_info_wrapper_2,
  .other_info_wrapper_3 {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
}
.footer-wrapper {
  display: flex;
  align-items: center;
  justify-content: end;
}
</style>
