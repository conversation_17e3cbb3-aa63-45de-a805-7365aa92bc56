import { LexiconConfigAuth } from "@/enums/authKeys";
import { hasAuth } from "@/utils/auth/authUtils";


/** 敏感词库 */
export const hasLexiconConfigIndex = function(){
    return hasAuth(LexiconConfigAuth.LexiconConfigIndex.key);
}()
/** 添加敏感词 */
export const hasLexiconConfigIndexAdd = function(){
    return hasAuth(LexiconConfigAuth.LexiconConfigIndexAdd.key);
}()
/** 删除 */
export const hasLexiconConfigIndexDelete = function(){
    return hasAuth(LexiconConfigAuth.LexiconConfigIndexDelete.key);
}()


