import { isArray } from "./isUtils";

export function isProdEnv(): boolean {
  return import.meta.env.PROD && import.meta.env.VITE_SYS_ENV == 'PROD';
}

export function isTestEnv(): boolean {
  return import.meta.env.PROD && import.meta.env.VITE_SYS_ENV == 'TEST';
}


export function isDevEnv(): boolean {
  return import.meta.env.DEV;
}

export function isDoctorEnv(){
  // TODO 为测试环境临时修改
  if(isProdEnv() || isTestEnv()){
    const splitArray = location.host.split(".")
    if(isArray(splitArray) && splitArray[0]){
      return splitArray[0].endsWith('-doctor')
    }
    else return false
  }
  else{
    return false
  }
}
