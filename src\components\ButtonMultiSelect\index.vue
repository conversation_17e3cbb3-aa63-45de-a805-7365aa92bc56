<template>
  <div class="button-multi-select" :class="{ 'is-disabled': disabled }">
    <div class="button-group">
      <button
        v-for="(option, index) in options"
        :key="option.value"
        class="select-button"
        :class="{
          'is-selected': isSelected(option.value),
          'is-disabled': option.disabled || disabled
        }"
        @click="toggleOption(option.value)"
        :disabled="option.disabled || disabled"
      >
        <span class="button-text">{{ option.label }}</span>
      </button>
    </div>
    <div v-if="error" class="error-message">{{ error }}</div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';

export interface SelectOption {
  label: string;
  value: string | number;
  disabled?: boolean;
}

interface Props {
  options: SelectOption[];
  value: (string | number)[];
  disabled?: boolean;
  max?: number;
  error?: string;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  max: Infinity,
});

const emit = defineEmits<{
  (e: 'update:value', value: (string | number)[]): void;
  (e: 'change', value: (string | number)[]): void;
}>();

// 内部选中值
const selectedValues = ref<(string | number)[]>([...props.value]);

// 判断选项是否被选中
const isSelected = (value: string | number): boolean => {
  return selectedValues.value.includes(value);
};

// 切换选项选中状态
const toggleOption = (value: string | number): void => {
  if (isSelected(value)) {
    // 如果已选中，则取消选中
    selectedValues.value = selectedValues.value.filter(v => v !== value);
  } else {
    // 如果未选中且未超过最大选择数量，则选中
    if (selectedValues.value.length < props.max) {
      selectedValues.value = [...selectedValues.value, value];
    }
  }
  
  // 更新v-model值
  emit('update:value', selectedValues.value);
  emit('change', selectedValues.value);
};

// 监听外部v-model变化
watch(
  () => props.value,
  (newVal) => {
    selectedValues.value = [...newVal];
  },
  { deep: true }
);
</script>

<style lang="less" scoped>
.button-multi-select {
  display: flex;
  flex-direction: column;
  gap: 8px;
  
  .button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }
  
  .select-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 32px;
    padding: 0 16px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
    background-color: #f5f5f5;
    border: 1px solid #e8e8e8;
    color: #333;
    outline: none;
    
    &:hover:not(.is-disabled) {
      border-color: #1890ff;
      color: #1890ff;
    }
    
    &.is-selected {
      background-color: #e6f7ff;
      border-color: #1890ff;
      color: #1890ff;
    }
    
    &.is-disabled {
      cursor: not-allowed;
      opacity: 0.6;
      background-color: #f5f5f5;
      color: #999;
      border-color: #e8e8e8;
    }
  }
  
  .error-message {
    font-size: 12px;
    color: #ff4d4f;
    line-height: 1.5;
  }
  
  &.is-disabled {
    opacity: 0.7;
  }
}
</style>