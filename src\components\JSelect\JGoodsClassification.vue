<template>
  <JSelect
    :value="props.value"
    :loading="isLoading"
    :onFocus="handlerFocus"
    :options="goodsClassificationList"
    :onClear="handleClear"
    :multiple="isMultiple"
    :max-tag-count="maxTagCount"
    :display-quantity="props.isMultiple === false ? 0 : maxTagCount"
    @update:value="onChange"
    style="width: 140px;"
    placeholder="请选择商品分类"
    :filter="customFilter"
    @scroll="handleScroll"
    @keydown.enter="handleSearch"
    :reset-menu-on-options-change="isResetSelectStatusRef"
  />
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";
import JSelect from "@/components/JSelect/index.vue";
import { getGoodsClassificationPage } from "@/services/api";
import { useMessages } from "@/hooks";
import { isArray } from "@/utils/isUtils";
import type { GoodsType } from "@/enums";

/* Props */
const props = withDefaults(
  defineProps<{
    isImmediately?: boolean; // 是否立即加载
    value: Array<string | number> | string | number | null; // 选择的值
    isMultiple?: boolean; // 是否多选 --> 非必传，默认值为false
    forbiddenId?: Array<string> | null; // 禁用的id
    type?: GoodsType; // 分类类型
  }>(),
  {
    isImmediately: false,
    isMultiple: false,
    forbiddenId: null,
    type: 1
  },
);

const emits = defineEmits<{
  (e: "update:value", selectValue: any): void; // 更新选择值事件
  (e: "update:isSelectAll", val: boolean): void; // 更新是否全选
}>();

/* 提示 */
const message = useMessages();

/* 是否加载 */
const isLoading = ref(false);
const isResetSelectStatusRef = ref(false); // 重置选择状态
const goodsClassificationList = ref([]); // 列表
const _resultTempList = []; // 临时结果列表
const maxTagCount = ref(2);
const isSelectAll = ref(false); // 是否全选

/* 执行搜索返回的内容*/
let recordsTotal = 1; // 总记录数

const params: { data: { name: string, type: GoodsType }; pageVO: { current: number; size: number } } = {
  data: {
    name: '',
    type: props.type ?? null,
  },
  pageVO: {
    current: 1, // 当前页
    size: 100, // 每页大小
  },
};

const _tempMemberTagList = computed(() => {
  return goodsClassificationList.value.filter(item => item.value !== "all");
});

/* 筛选、转化{label: '', value: ''} */
function handleData(filterData: Array<ApiStoreModule.GoodsClassification>) {
  let dataList = [];
  dataList = filterData.map(item => {
    if (isArray(props.forbiddenId) && props.forbiddenId.includes(item.id)) {
      return { label: item.name, value: item.id, disabled: true };
    } else {
      return { label: item.name, value: item.id, disabled: false };
    }
  });
  // 是否开启多选，添加全部选项
  if (props.isMultiple) {
    dataList.unshift({ label: "全部", value: "all", disabled: false });
  }
  return dataList;
}

/* 获取商品分类列表 */
async function getMemberTagList() {
  try {
    isLoading.value = true;
    const { total, current, size, records } = await getGoodsClassificationPage(params);
    params.pageVO.current = Number(current);
    params.pageVO.size = Number(size);
    recordsTotal = Number(total);
    // 如果是第一页
    if (params.pageVO.current == 1) {
      isResetSelectStatusRef.value = true; // 重置选择状态为true
      goodsClassificationList.value = handleData(records);
    } else {
      isResetSelectStatusRef.value = false; // 重置选择状态为false
      handleData(records).forEach(item => {
        // 如果列表中不存在该项
        if (!goodsClassificationList.value.find(temp => temp.value == item.value)) {
          // 添加到列表中
          goodsClassificationList.value.push(item);
        }
      });
    }
    // 如果有搜索值
    if (params.data.name) {
      // 遍历会员标签列表
      goodsClassificationList.value.forEach(item => {
        // 如果临时结果列表中不存在该项
        if (!_resultTempList.find(temp => temp.value == item.value)) {
          // 添加到临时结果列表中
          _resultTempList.push(item);
        }
      });
    } else {
      // 遍历临时结果列表
      _resultTempList.forEach(item => {
        // 如果会员标签列表中不存在该项
        if (!goodsClassificationList.value.find(temp => temp.value == item.value)) {
          // 添加到会员标签列表中
          goodsClassificationList.value.push(item);
        }
      });
    }
  } catch (error) {
    message.createMessageError("获取商品分类失败：" + error);
  } finally {
    isLoading.value = false;
    if (isSelectAll.value) {
        handleSelectAll();
    }
  }
}

/** 自定义过滤函数 */
function customFilter(keyword, options) {
  const labelMatch = options.label.toLowerCase().includes(keyword.toLowerCase());
  const valueMatch = options.value.toLowerCase().includes(keyword.toLowerCase());
  return labelMatch || valueMatch;
}

/** 选择值改变事件处理函数 */
function onChange(value) {
  params.data.name = null;
  // 额外的全选逻辑
  if (props.isMultiple) {
    if (value?.includes("all")) {
      isSelectAll.value = true;
      let newVal = goodsClassificationList.value.filter(item => item.value !== "all").map(item => item.value);
      emits("update:value", newVal);
      emits("update:isSelectAll", isSelectAll.value);
      return;
    }
  }
  isSelectAll.value = false;
  emits("update:value", value);
  emits("update:isSelectAll", isSelectAll.value);
}

/** 处理全选 */
const handleSelectAll = () => {
    let newVal = goodsClassificationList.value.filter(item => item.value !== "all").map(item => item.value);
    emits("update:value", newVal);
};

/** 清空事件处理函数 */
const handleClear = () => {
  params.data.name = null;
  emits("update:value", null);
};

/** 滚动事件处理函数 */
function handleScroll(e) {
  const currentTarget = e.currentTarget as HTMLElement;
  if (currentTarget.scrollTop + currentTarget.offsetHeight >= currentTarget.scrollHeight) {
    // 如果当前页乘以每页大小小于总记录数
    if (params.pageVO.current * params.pageVO.size < recordsTotal) {
      params.pageVO.current++; // 当前页加1
      getMemberTagList();
    }
  }
}

/** 聚焦事件处理函数 */
function handlerFocus() {
  // 如果会员标签列表为空
  if (!goodsClassificationList.value.length) {
    getMemberTagList();
  }
}

/** 搜索事件处理函数 */
function handleSearch(event) {
  params.data.name = event.target.value || null;
  params.pageVO.current = 1;
  getMemberTagList();
}

/** 是否禁用全选 */
const setSelectAll = (value: any) => {
  if (props.isMultiple && value?.length === _tempMemberTagList.value.length) {
    goodsClassificationList.value.find(item => {
      if (item.value === "all") {
        item.disabled = true;
      }
    });
  } else {
    goodsClassificationList.value.find(item => {
      if (item.value === "all") {
        item.disabled = false;
      }
    });
  }
};

/** 监听 */
watch(
  () => props.isImmediately,
  newVal => {
    if (newVal) {
      getMemberTagList();
    }
  },
  { immediate: true },
);

watch(
  () => props.value,
  newVal => {
    props.isMultiple && setSelectAll(newVal);
  },
);

watch(() => props.type, (newVal) => {
  if (newVal) {
    params.data.type = newVal;
  }
});
</script>

<style scoped lang="less"></style>
