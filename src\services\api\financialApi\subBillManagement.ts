import { defHttp } from '@/services';

/** 分账入账方管理 */
export const enum SubBillManagementApi {
    allocationGetDetail = '/allocation/get/detail',
    allocationPageAllocation = '/allocation/page/allocation',
    allocationCancelAllocation = '/allocation/cancel/allocation',
    allocationRetryAllocation = '/allocation/retry/allocation',
    allocationTZ001Notify = '/allocation/TZ001/notify',
    allocationExport = '/allocation/export',
    /** 取消结算 */
    allocationCancelSettlement = '/allocation/cancel/settle',
    /** 批量取消结算 */
    allocationBatchCancelSettlement = '/allocation/batchCancel/settle'
}

/** 获取分账单详情 */
export function allocationGetDetail(id) {
    return defHttp.get({
        url: SubBillManagementApi.allocationGetDetail + '?id=' + id,
    });
}

/** 分页查询列表 */
export function allocationPageAllocation(params) {
    return defHttp.post({
        url: SubBillManagementApi.allocationPageAllocation,
        params,
    });
}

/** 取消分账单 */
export function allocationCancelAllocation(id) {
    return defHttp.post({
        url: SubBillManagementApi.allocationCancelAllocation + '?id=' + id,
    });
}

/** 重新分账 */
export function allocationRetryAllocation(id) {
    return defHttp.post({
        url: SubBillManagementApi.allocationRetryAllocation + '?id=' + id,
    });
}

/** 富友用户结算结果回调通知 */
export function allocationTZ001Notify(params) {
    return defHttp.post({
        url: SubBillManagementApi.allocationTZ001Notify,
        params,
    });
}

//订单导出
export function allocationExport(params) {
  return defHttp.post({
    url: SubBillManagementApi.allocationExport,
    requestConfig: {
      responeseType: 'stream'
    },
    params,
  })
}

/** 取消结算 */
export function allocationCancelSettlement(params) {
    return defHttp.post({
        url: SubBillManagementApi.allocationCancelSettlement,
        params,
        requestConfig: {
            isQueryParams:true
        }
    });
}

/** 批量取消结算 */
export function allocationBatchCancelSettlement(params) {
    return defHttp.post({
        url: SubBillManagementApi.allocationBatchCancelSettlement,
        params,
    });
}

/** 成本价APi */
export const enum CostPriceApi {
    /** 成本价导入 */
    costPriceImport = '/allocation/importCost',
    /** 成本价确认 */
    costPriceConfirm = '/allocation/confirmImportCost',
    /** 未处理分帐单导出 */
    costPriceExport = '/allocation/exportUnprocessed'
}

/** 成本价导入 */
export function costPriceImport(params:FormData) {
    return defHttp.post({
        url: CostPriceApi.costPriceImport,
        params,
        requestConfig: {
            requestContentType: "form-data",
        },
    });
}

/** 成本价确认 */
export function costPriceConfirm(params) {
    return defHttp.post({
        url: CostPriceApi.costPriceConfirm,
        params,
        requestConfig: {
            isQueryParams:true
        }
    });
}

/** 未处理分账单导出 */
export function costPriceExport(params:string) {
    return defHttp.get({
        url: `${CostPriceApi.costPriceExport}?unprocessedListKey=${params}`,
        requestConfig: {
            responeseType: 'stream'
        }
    });
}
