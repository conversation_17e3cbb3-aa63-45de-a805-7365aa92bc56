<template>
  <div
      ref="skeletonDomRef"
      class="image-container"
  >
    <NImage
        class="message-image"
        :src="customImageContent"
        :width="props.width"
        :height="props.height"
    />
  </div>
</template>

<script lang="ts" setup>
import {ref, withDefaults, computed} from 'vue';
import type {JChatMessageModel} from "@/views/DoctorEndModule/IM/types";
import EmptyImg from '@/assets/image/exception/emptyImg.png';

const emits = defineEmits(['previewImage']);
const props = withDefaults(
    defineProps<{
      content: string;
      messageItem: JChatMessageModel;
      width: string | number
    }>(),
    {
      content: '',
      messageItem: () => ({} as JChatMessageModel),
      width: 90,
      height: 90,
    },
);
const skeletonDomRef = ref();

const customImageContent = computed(() => {
  if (props.content) {
    return props.content
  }
  return EmptyImg
})
</script>

<style lang="less" scoped>

:deep(.n-image img) {
  max-width: min(calc(100vw - 180px), 300px);
  max-height: min(calc(100vw - 180px), 300px);
  width: inherit;
  height: inherit;
}

.image-container {
  overflow: hidden;
  background-color: #f4f4f4;

  .message-image {

  }
}
</style>
