<template>
  <JModal
    v-model:show="isShow"
    width="500"
    :title="titMap[model.welfareModalType]"
    @after-leave="closeModal"
    @positive-click="_submit"
    :positiveButtonProps="{
			loading:isAddLoading
		}"
  >
    <n-form
      ref="formRef"
      :rules="rules"
      :model="model"
      label-width="auto"
      label-placement="left"
      require-mark-placement="right-hanging"
      :style="{
          width: '100%',
        }"
    >
      <n-grid :cols="8" :x-gap="24">
        <template v-if="model.welfareModalType != WelfareModalTypeEnum.VALIDITY">
          <n-form-item-gi :span="24" label="福利券名称" path="name" required>
            <n-input v-model:value="model.name" placeholder="请输入名称" :maxlength="15" clearable/>
          </n-form-item-gi>
          <n-form-item-gi :span="24" label="福利券分类" path="categoryId" required>
            <JWelfareClassify v-model:value="model.categoryId" isImmediately style="width: 100%;" placeholder="请选择分类"></JWelfareClassify>
          </n-form-item-gi>
          <n-form-item-gi :span="24" label="发行数量"  path="totalQuantity" required>
            <n-space align="center">
              <n-input-number v-model:value="model.totalQuantity" :min="1" :max="200000" />
            </n-space>
          </n-form-item-gi>
          <n-form-item-gi :span="24" label="每人最高领取数"  path="maxPerUser" required>
            <n-space align="center">
              <n-input-number v-model:value="model.maxPerUser" :min="1" :max="1000"/>
            </n-space>
          </n-form-item-gi>
        </template>
        <n-form-item-gi :span="24" label="券有效期" path="validUntil" required>
          <n-date-picker
            v-model:value="model.validUntil"
            type="date"
            clearable
            :default-time="'23:59:59'"
            :is-date-disabled="dateDisabled"
            :input-readonly="true"
            style="width: 100%;"
          />
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </JModal>
</template>

<script setup lang="ts">
import { ref,computed, watch } from 'vue';
import { useMessages } from '@/hooks';
import JWelfareClassify from '@/components/JSelect/JWelfareClassify.vue';
import dayjs from 'dayjs';
import { WelfareModalTypeEnum,
  WelfarePageTypeEnum,
} from "@/views/StoreModule/SalesManagement/WelfareTicketManage/types";
import { addCouponBatch, updateCouponBatch, updateValidDate } from "@/services/api";
const message = useMessages();
const { createMessageSuccess, createMessageError } = useMessages();
const  dateDisabled = (ts: number)=>{
  return dayjs(ts).isBefore(dayjs().startOf('day'));
}

const titMap = {
  [WelfareModalTypeEnum.ADD]:"添加福利券",
  [WelfareModalTypeEnum.VALIDITY]:"修改有效期",
}
const isAddLoading = ref<boolean>(false)
const initParams = {
  id:null,
  categoryId:null,
  validUntil: null,
  name:null,
  totalQuantity:1,
  isInfinite:false,
  welfareModalType:0,
  maxPerUser:1,
  storeId:'',
  refreshTable:()=> {}
};
const model = ref({ ...initParams });
const isShow = ref(false)
/* 表单规则 */
const rules = {
  name:{
    required: model.value.welfareModalType !== WelfareModalTypeEnum.VALIDITY,
    trigger: ["blur", "change"],
    message: "请输入福利券名称",
  },
  totalQuantity:{
    type: "number",
    required: model.value.welfareModalType !== WelfareModalTypeEnum.VALIDITY && !model.value.isInfinite,
    trigger: ["blur", "change"],
    message: "请输入需要发行的数量",
  },
  categoryId:{
    required: model.value.welfareModalType !== WelfareModalTypeEnum.VALIDITY,
    trigger: ["blur", "change"],
    message: "请选择福利券分类",
  },
  validUntil:{
    required: true,
    type: "number",
    trigger: ["blur", "change"],
    message: "请选择有效期时间",
  },
  maxPerUser:{
    required: true,
    type: "number",
    trigger: ["blur", "change"],
    message: "请填写每人最高领取数",
  },
};

// 关闭按钮
const closeModal = () => {
  isShow.value = false;
  model.value = { ...initParams };
}

// 确认按钮
const formRef = ref(null);
const _submit = async () => {
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      if(model.value.welfareModalType != WelfareModalTypeEnum.VALIDITY){
        try {
          isAddLoading.value = true
          const endOfDay = dayjs(model.value.validUntil).endOf('day').format('YYYY-MM-DD HH:mm:ss');
          const params = model.value.welfareModalType == WelfareModalTypeEnum.ADD ? {
            data:{
              name: model.value.name,
              totalQuantity:model.value.isInfinite ? -1 : model.value.totalQuantity,
              validUntil: endOfDay,
              categoryId: model.value.categoryId,
              maxPerUser: model.value.maxPerUser,
              storeId:model.value.storeId
            }
          }:{
            id: model.value.id,
            validUntil: endOfDay
          }
          if(model.value.id){
            params.data['id'] = model.value.id
          }
          const api = model.value.welfareModalType == WelfareModalTypeEnum.ADD ? addCouponBatch:updateValidDate
          await api(params)
          model.value.refreshTable()
          createMessageSuccess("操作成功")
          isShow.value = false;
        } catch (error) {
          createMessageError("操作异常"+error)
        } finally {
          isAddLoading.value = false
        }
      }else{
        try {
          isAddLoading.value = true
          const endOfDay = dayjs(model.value.validUntil).endOf('day').format('YYYY-MM-DD HH:mm:ss');
          const params = {
            id: model.value.id,
            validUntil: endOfDay
          }
          await updateValidDate(params)
          createMessageSuccess("操作成功")
          model.value.refreshTable()
          isShow.value = false;
        } catch (error) {
          createMessageError("操作异常"+error)
        } finally {
          isAddLoading.value = false
        }
      }
    }
  });
}
/* 接收父组件参数 */
const acceptParams = (params) => {
  model.value.welfareModalType = params.welfareModalType;
  if (params.id){
    model.value.id = params.id
    model.value.validUntil = dayjs(params.validUntil).valueOf();
  }
  model.value.storeId = params.storeId
  model.value.refreshTable = params.refreshTable
  console.log(model.value);
  isShow.value = true;
};

defineExpose({
  acceptParams,
});

</script>