<template>
    倒计时:<span id="countdown">{{ countdownText }}</span>    
</template>
<script setup lang="tsx">
import { watch, ref } from "vue";
// 设置倒计时的目标时间（以毫秒为单位）
const targetTime = new Date().getTime() + 86400000; // 1天后

// 声明响应式变量
const countdownText = ref("");
// 更新倒计时的函数
const  updateCountdown = () =>{
   const targetDate = new Date(props.countdown);
   const currentDate = new Date();
   const countdown = targetDate.getTime() - currentDate.getTime();
   if (countdown <= 0) {
        countdownText.value = "倒计时结束";
    } else {
        // 计算剩余的天数、小时、分钟和秒数
        const days = Math.floor(countdown / (1000 * 60 * 60 * 24));
        const hours = Math.floor((countdown % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((countdown % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((countdown % (1000 * 60)) / 1000);

        // 更新显示的倒计时文本
        countdownText.value = `${days}天 ${hours}小时 ${minutes}分钟 ${seconds}秒`;
    }
}
const props = defineProps({
    show: Boolean,
    countdown:String
})

watch(props,
 (newVal)=>{
    if(newVal.show){
        setInterval(updateCountdown)
    }
},
{ immediate: true })
</script>
<style scoped lang="less">

</style>