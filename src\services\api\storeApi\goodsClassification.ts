import { defHttp } from "@/services";
import type { GoodsType } from "@/enums";

/** 商品分类管理 */
export const enum CommodityManagementApi {
  page = "/product/cate/page",
  add = "/product/cate/add",
  update = "/product/cate/update",
  delete = "/product/cate/delete",
  list = "/product/cate/list",

  // 积分商品分类
  addPoint = "/product/cate/add/point",
  updatePoint = "/product/cate/update/point",
}

export interface GoodsClassificationPageRes {
  records: ApiStoreModule.GoodsClassification[];
  total: string;
  size: string;
  current: string;
}
/** 商品分类分页 */
export function getGoodsClassificationPage(params: {
  data: {
    name: string;
    type?: GoodsType;
  };
  pageVO: {
    current: number;
    size: number;
  };
}) {
  return defHttp.post<GoodsClassificationPageRes>({
    url: CommodityManagementApi.page,
    params,
  });
}

/**
 * @description 查询商品分类列表
 */
export function fetchGoodsCategories(params: {
  data: { type?: GoodsType; };
}) {
  return defHttp.post({
    url: CommodityManagementApi.list,
    params,
  });
}

interface AddParams {
  name: string; // 名称
  type: GoodsType; // 商品类别 1：药品；2：疗法 3：普通商品 4： 积分商品
  iconPath: string; // 分类图标
  sort: number; // 排序号。大于等于0小于1000的整数；序号越大排位越前
  isShow: 0 | 1; // 是否在首页首屏展示该分类，归属商品类型选择药品时展示此配置项
  parentId?: number; // 父级分类 ID
  isShowList: 0 | 1; // 是否在列表页展示 0=否；1=是 默认1
}
/** 新建商品分类 */
export function addGoodsClassification(_params: AddParams) {
  return defHttp.post({
    url: CommodityManagementApi.add,
    params: {
      data: _params,
    },
  });
}

interface AddPointsParams {
  name: string; // 名称
  type: GoodsType; // 商品类别 4： 积分商品
  iconPath: string; // 分类图标
  sort: number; // 排序号。大于等于0小于1000的整数；序号越大排位越前
  isShow: 0 | 1; // 是否在首页首屏展示该分类，归属商品类型选择药品时展示此配置项
  parentId?: number; // 父级分类 ID
  isShowList?: 0 | 1; // 是否在列表页展示 0=否；1=是 默认1
}
/** 新建积分商品分类 */
export function addPointGoodsClassification(_params: AddPointsParams) {
  return defHttp.post({
    url: CommodityManagementApi.addPoint,
    params: {
      data: _params,
    },
  });
}

/** 修改商品分类 */
export const goodsClassificationUpdate = (_params: {
  id: string; // 分类Id
  name: string; // 名称
  type: GoodsType; // 商品类别 1：药品；2：疗法 3：普通商品 4： 积分商品
  iconPath: string; // 分类图标
  sort: number; // 排序号。大于等于0小于1000的整数；序号越大排位越前
  isShow: 0 | 1; // 是否在首页首屏展示该分类，归属商品类型选择药品时展示此配置项
}) => {
  return defHttp.put({
    url: CommodityManagementApi.update,
    params: {
      data: _params,
    },
  });
};

/** 修改积分分类 */
export const updatePointGoodsClass = (_params: {
  id: string; // 分类Id
  name: string; // 名称
  type: GoodsType; // 商品类别 1：药品；2：疗法 3：普通商品 4： 积分商品
  iconPath: string; // 分类图标
  sort: number; // 排序号。大于等于0小于1000的整数；序号越大排位越前
  isShow: 0 | 1; // 是否在首页首屏展示该分类，归属商品类型选择药品时展示此配置项
}) => {
  return defHttp.put({
    url: CommodityManagementApi.updatePoint,
    params: {
      data: _params,
    },
  });
};

/** 删除单个分类,更新商品 */
export function goodsClassificationDelete(params: { id: string; updateId: string }) {
  return defHttp.delete({
    url: CommodityManagementApi.delete,
    requestConfig: {
      isQueryParams: true,
    },
    params,
  });
}
