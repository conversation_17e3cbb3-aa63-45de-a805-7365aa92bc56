<template>
  <FormLayout
    :isLoading="isLoading"
    :tableData="tableData"
    :tableColumns="tableColumns"
    :pagination="paginationRef"
    @paginationChange="paginationChange"
    :isTableSelection="false"
  >
    <template #searchForm>
      <n-form
        ref="formRef"
        :model="model"
        :show-feedback="false"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
        size="small"
        :style="{ width: '100%' }"
      >
        <n-form-item :span="12" label="门店名称" path="">
          <j-search-input
            width="270px"
            v-model:value.trim="model.storeNameOrShortId"
            placeholder="请输入门店名或ID"
            @search="handlerSearch"
          />
        </n-form-item>
        <n-form-item label="创建时间" label-placement="left">
          <j-date-range-picker
            style="flex: 1"
            v-model:value="model.creationTime"
            type="datetimerange"
            format="yyyy-MM-dd"
            :default-time="['00:00:00', '23:59:59']"
            clearable
          />
        </n-form-item>
        <!-- 已完成和未通过-->
        <n-form-item v-if="isShowReviewTime" label="审核完成时间" label-placement="left">
          <j-date-range-picker
            style="flex: 1"
            v-model:value="model.reviewTime"
            type="datetimerange"
            format="yyyy-MM-dd"
            :default-time="['00:00:00', '23:59:59']"
            clearable
          />
        </n-form-item>
      </n-form>
    </template>
    <!-- 操作栏 -->
    <template #tableHeaderBtn="scoped">
      <n-button @click="refresh" class="store-button">刷 新</n-button>
      <n-button v-if="hasExportAuth" type="primary" :loading="exportLoading" @click="exportData" class="store-button">
        导出
      </n-button>
    </template>
  </FormLayout>
  <DrawerDetails ref="drawerDetailsRef" />
  <ViewLogistics ref="viewLogisticsRef" />
</template>
<script setup lang="tsx">
import { computed, ref, watch } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import DrawerDetails from "./DrawerDetails.vue";
import ViewLogistics from "./ViewLogistics.vue";
import useShopReturnLogistics from "../hooks/useShopReturnLogistics";
import {
  getShopReturnLogisticsPageList,
  exportShopReturnLogistics,
} from "@/services/api";
import { useLoading, useMessages } from "@/hooks";
import { deepClone } from "@/utils";
/** 权限 */
import {
  hasExportAuth,
  hasDetailsAuth,
  hasLogisticsAuth,
} from "../authList";
import moment from "moment";
import { ShopReturnLogisticsEnum } from "@/enums";

const { toYuanString, getTableListStatusLabel } = useShopReturnLogistics();

interface Props {
  tabNameRef: string; // tab标签值
}

/** props */
const props = withDefaults(defineProps<Props>(), {
  tabNameRef: ShopReturnLogisticsEnum.WaitingForReview,
});
const isShowReviewTime = computed(() => {
  return props.tabNameRef === ShopReturnLogisticsEnum.NotPassed || props.tabNameRef === ShopReturnLogisticsEnum.Completed;
});
const drawerDetailsRef = ref(null);
const viewLogisticsRef = ref(null);

/** 导出数据 */
const { loading: exportLoading, startLoading, endLoading } = useLoading();
const exportData = async () => {
    try {
      startLoading();
      await exportShopReturnLogistics({
        data:{...getParams()},
        pageVO:{
          current: paginationRef.value.current,
          size: paginationRef.value.pageSize
        }
      })
      createMessageSuccess('导出成功！');
    }
    catch (error) {
      createMessageError('导出失败')
    }
    finally {
      endLoading();
    }
};
const viewDetails = (row) => {
  const _params = {
    row,
    tabType: props.tabNameRef,
    refresh: refresh,
  };
  drawerDetailsRef.value?.acceptParams(_params);
};

const viewLogistics = (row) => {
  viewLogisticsRef.value?.acceptParams(row);
};

const { createMessageSuccess, createMessageError } = useMessages();

/* 初始化参数 */
const tabValue = ref(null);
const initParams = {
  storeNameOrShortId: "",
  creationTime: null,
  reviewTime: null,
};
const model = ref({ ...initParams });
/* 表格方法Hook */
const {
  isLoading,
  tableData,
  paginationRef,
  paginationChange,
  pageTableData,
} = useTableDefault({
  pageDataRequest: getShopReturnLogisticsPageList,
});

//刷新
const refresh = () => {
  getTableData();
};

/** 搜索 */
const handlerSearch = () => {
  getTableData();
};

/** 获取参数 */
const getParams = () => {
  const { storeNameOrShortId, creationTime, reviewTime } = model.value;
  const startTime = creationTime ? moment(creationTime[0]).format(`YYYY-MM-DD HH:mm:ss`) : null;
  const endTime = creationTime ? moment(creationTime[1]).format(`YYYY-MM-DD HH:mm:ss`) : null;
  let params = {
    storeNameOrShortId,
    startTime,
    endTime,
    tabCode: tabValue.value,
  } as Record<string, any>;
  if (props.tabNameRef === ShopReturnLogisticsEnum.NotPassed || props.tabNameRef === ShopReturnLogisticsEnum.Completed) {
    const approvalFinishedStartTime = reviewTime ? moment(reviewTime[0]).format(`YYYY-MM-DD HH:mm:ss`) : null;
    const approvalFinishedEndTime = reviewTime ? moment(reviewTime[1]).format(`YYYY-MM-DD HH:mm:ss`) : null;
    params = { ...params, approvalFinishedStartTime, approvalFinishedEndTime };
  }
  return params;
};

/** 请求表格数据 */
function getTableData() {
  pageTableData(getParams(), paginationRef.value, true);
}

/* 表格项 */
const tableColumns = ref([]);
const tableColumnsSource = [
  {
    title: "门店名称/ID",
    key: "name",
    align: "left",
    render: rowData => {
      return (
        <div>
          <div>{rowData.storeName}</div>
          {rowData.storeShortId && <div>ID:{rowData.storeShortId}</div>}
        </div>
      );
    },
  },
  {
    title: "店长姓名/电话",
    key: "contactName",
    align: "left",
    render: rowData => {
      return (
        <div>
          <div>{rowData.contactName}</div>
          <div> {rowData.contactPhone}</div>
        </div>
      );
    },
  },
  {
    title: "退货商品",
    key: "returnProductDesc",
    align: "left",
    // render: rowData => {
    //   return (
    //     <div>
    //       <div>张三</div>
    //       <div> *********</div>
    //     </div>
    //   );
    // },
  },
  {
    title: "退货件数",
    key: "totalReturnQuantity",
    align: "left",
  },
  {
    title: "物流公司",
    key: "logisticsCompany",
    align: "left",
  },
  {
    title: "物流单号",
    key: "trackingNumber",
    align: "left",
  },
  {
    title: "物流费用（元）",
    key: "logisticsFee",
    align: "left",
    render: rowData => {
      return rowData?.logisticsFee ? toYuanString(rowData?.logisticsFee) : "-";
    },
  },
  {
    title: "总申请费用（元）",
    key: "totalRequestedFee",
    align: "left",
    render: rowData => {
      return rowData?.totalRequestedFee ? toYuanString(rowData?.totalRequestedFee) : "-";
    },
  },
  {
    title: "申请状态",
    key: "requestedStatus",
    align: "left",
    render: rowData => {
      return getTableListStatusLabel(rowData?.requestedStatus);
    },
  },
  {
    title: "完成时间",
    key: "finishedTime",
    align: "left",
  },
  {
    title: "创建时间",
    key: "createTime",
    align: "left",
  },
  {
    title: "操作",
    key: "action",
    align: "left",
    fixed: "right",
    render: (row) => {
      return (
        <n-space align="center" justify="center">
          {hasDetailsAuth ?
            <n-button button text type="primary" onClick={() => viewDetails(row)}
            >
              查看详情
            </n-button> : null}
          {hasLogisticsAuth ?
            <n-button button text type="primary" onClick={() => viewLogistics(row)}
            >
              查看物流
            </n-button> : null}
        </n-space>
      );
    },
  },
];

/** 监听 */
watch(() => props.tabNameRef, (newVal) => {
    tableColumns.value = deepClone(tableColumnsSource);
    tabValue.value = newVal;
    getTableData();
    //不是已完成状态，过滤掉完成时间列
    if (tabValue.value !== ShopReturnLogisticsEnum.Completed) {
      tableColumns.value = tableColumns.value.filter(column => {
        return column.key !== "finishedTime";
      });
    }
  },
  {
    immediate: true,
  });
watch([
  () => model.value.creationTime,
  () => model.value.reviewTime,
],() => {
  getTableData();
});
</script>

<style lang="less" scoped>
@import "@/styles/default.less";

:deep .n-tag__content {
  text-overflow: ellipsis;
  overflow: hidden;
}

.action-wrapper {
  display: flex;
  align-items: center;
}
</style>
