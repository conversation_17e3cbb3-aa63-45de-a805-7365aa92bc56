import { isArray, isFunction, isNullOrUnDef } from "@/utils/isUtils";
import VERTC,{MediaType, MirrorType, StreamIndex, StreamRemoveReason, VideoRenderMode, type IRTCEngine} from '@volcengine/rtc';
import { RoomStatusEnum, type RTCBaseConfig, type RTCLiveStatus, type RTCLiveUserMediaStatus, StreamHostEventEnum } from "../types";
import { duplicateNewCopy } from "@/utils";

/** RTC */
export class RTCLive{
    private RTCInstance: IRTCEngine;
    private streamLocalAudioTrack:MediaTrackSettings;
    private streamLocalVideoTrack:MediaTrackSettings;
    private RTCLiveStatus: RTCLiveStatus;
    private eventMap:Map<StreamHostEventEnum,Function[]>
    static instance:RTCLive

    constructor() {
        if (!RTCLive.instance) {
            this.RTCInstance = null
            this.streamLocalAudioTrack = null;
            this.streamLocalVideoTrack = null;
            this.eventMap = new Map()
            this.RTCLiveStatus = {
                appId:'',
                roomId:'',
                userId:'',
                token:'',
                roomStatus:RoomStatusEnum.IDLE,
                isPublishing: false,
                cameraStatus:{
                  id:'',
                  enabled:true,
                  isPublishing:false  
                },
                micStatus:{
                  id:'',
                  enabled:true,
                  isPublishing:false  
                },
                userCount:0,
                userMediaList:[],
                mixLayout:{},
                videoElementId:"",
                encoderConfig:{
                    maxKbps:600,
                    frameRate:15,
                    width:1600,
                    height:720,
                },
                wrapperConfig:{
                    height:'100%',
                    width:'100%'
                }
            }
            RTCLive.instance = this;
        }
        return RTCLive.instance;
    }
    /** 触发事件绑定回调 */
    private emits(event:StreamHostEventEnum,args:Array<any>=[]){
        const _temp = this.eventMap.get(event)
        if(_temp && isArray(_temp) && _temp.length){
            _temp.forEach(fn=>{
                isFunction(fn) && fn(...args)
            })
        }
    }

    /** 注册用户状态变化事件*/
    private userConnectionStateChangeListener() {
        this.RTCInstance.on(VERTC.events.onError,({errorCode})=>{
            if(errorCode == 'DUPLICATE_LOGIN' || errorCode == 'RTM_DUPLICATE_LOGIN' || errorCode == 'ROOM_DISMISS'){
                this.emits(StreamHostEventEnum.kickedOffline)
            }
        })
    }

    private calcStreamPlayerStyle(){
        const _temp = duplicateNewCopy(this.RTCLiveStatus.userMediaList) as Array<RTCLiveUserMediaStatus>
        _temp.forEach((item, index) => {
            const totalMember = _temp.length
            if (index == 0) {
                if (totalMember == 1) {
                    item.width = '100%'
                    item.height = '100%'
                    item.x = '0px'
                    item.y = '0px'
                }
                else if(totalMember == 2){
                    item.width = '112px'
                    item.height = '153px'
                    item.x = '15px'
                    item.y = '15px'
                    item.zIndex = 10
                }
                else if(totalMember == 3){
                    item.width = '50%'
                    item.height = '50%'
                    item.x = '50%'
                    item.y = '50%'
                }
                
            }
            else if(index == 1){
                if(totalMember == 2){
                    item.width = '100%'
                    item.height = '100%'
                    item.x = '0px'
                    item.y = '0px'
                }
                else if(totalMember == 3){
                    item.width = '50%'
                    item.height = '50%'
                    item.x = '0%'
                    item.y = '50%'
                }
            }
            else if(index == 2){
                if(totalMember == 3){
                    item.width = '100%'
                    item.height = '50%'
                    item.x = '0%'
                    item.y = '0%'
                }
            }
        })
        return _temp
    }

    private userPublishStreamListener(){
        this.RTCInstance.on(VERTC.events.onUserPublishStream,async (event: { userId: string; mediaType: MediaType}) =>{
            if( event.userId != this.RTCLiveStatus.userId ){
                const index = this.RTCLiveStatus.userMediaList.findIndex(item=>item.userId == event.userId)
                if(index == -1){
                    const index = this.RTCLiveStatus.userMediaList.length 
                    this.RTCLiveStatus.userMediaList.push({ 
                        videoEl:`rtc-live-client${index}-wrapper-${new Date().valueOf()}`,
                        userId: event.userId,
                        index: index,
                        mediaType: event.mediaType
                    })
                }
            }
            this.RTCLiveStatus.userMediaList = this.calcStreamPlayerStyle()
            this.emits(StreamHostEventEnum.userPublishStream,[event])
        })
    }

    private userUnpublishStreamListener(){
        this.RTCInstance.on(VERTC.events.onUserUnpublishStream,async (event: { userId: string; mediaType: MediaType; reason: StreamRemoveReason})=>{
            if(event.mediaType !== MediaType.AUDIO_AND_VIDEO){
                return
            }
            if( event.userId != this.RTCLiveStatus.userId ){
                const _index = this.RTCLiveStatus.userMediaList.findIndex(item=>item.userId == event.userId)
                const _temp = this.RTCLiveStatus.userMediaList.filter(item=>item.userId !== event.userId).map(item=>{
                    return {
                        ...item,
                        index:item.index>_index? item.index- 1 : item.index
                    }
                })
                this.RTCLiveStatus.userMediaList = _temp
            }
            this.RTCLiveStatus.userMediaList = this.calcStreamPlayerStyle()
            this.emits(StreamHostEventEnum.userUnpublishStream,[event])
        })
    }

    private removeUserPublishStreamListener(){
        this.RTCInstance.off(VERTC.events.onUserPublishStream)
    }

    private removeUserUnpublishStreamListener(){
        this.RTCInstance.off(VERTC.events.onUserUnpublishStream)
    }

    playRemoteVideo({remoteUserId,domId}){
        this.RTCInstance.setRemoteVideoPlayer(
            StreamIndex.STREAM_INDEX_MAIN,
            {
                userId:remoteUserId,
                renderDom:domId,
                renderMode:VideoRenderMode.RENDER_MODE_HIDDEN
            }
        )
    }
    
    /** 设置options */
    async setOptions(params:Partial<Omit<RTCLiveStatus,'videoElementId'>>){
        if(this.RTCLiveStatus.roomStatus != RoomStatusEnum.READY){
            this.RTCLiveStatus = {
                ...this.RTCLiveStatus,
                ...params
            }
        }
        else{
            throw new Error('请先退出当前房间')
        }
    }

    /**加入房间 */
    private async joinTheRoom() {
        try{
            if(this.RTCLiveStatus.roomStatus != RoomStatusEnum.READY){
                await this.RTCInstance.joinRoom(
                    this.RTCLiveStatus.token, 
                    this.RTCLiveStatus.roomId,
                    {userId:this.RTCLiveStatus.userId},
                    {
                        isAutoPublish: false, // 采集音视频时自动发布到房间
                        isAutoSubscribeAudio: true, // 自动订阅音频
                        isAutoSubscribeVideo: true, // 自动订阅视频
                    }
                )
                this.RTCLiveStatus.roomStatus = RoomStatusEnum.READY
            }
        }
        catch(e){
            throw new Error (`加入 [${this.RTCLiveStatus.roomId}] 房间异常：${e}`)
        }
    }

    /** 初始化 */
    async createTheHost(params:RTCBaseConfig){
        try{
            this.setOptions(params)
            if(!this.RTCInstance){
                this.RTCInstance = VERTC.createEngine(params.appId);
            }
            await this.joinTheRoom()
            this.RTCLiveStatus.userMediaList=[{
                videoEl:`rtc-live-host-wrapper-${new Date().valueOf()}`,
                userId:this.RTCLiveStatus.userId,
                index:0,
                mediaType:MediaType.AUDIO_AND_VIDEO,
                width:this.RTCLiveStatus.wrapperConfig.width,
                height:this.RTCLiveStatus.wrapperConfig.height,
                x:0,
                y:0
            }]
            this.userConnectionStateChangeListener()
            this.userPublishStreamListener()
            this.userUnpublishStreamListener()
            return true
        }
        catch(e){
            throw new Error(`加入房间异常：${e}`)
        }
    }

    /**设置视频流播放element(会改变实例化时传入的videoEl值) */
    setVideoPlayWrapper(videoEl:string){
        this.RTCLiveStatus.videoElementId = videoEl
        this.RTCInstance.setLocalVideoPlayer(
            StreamIndex.STREAM_INDEX_MAIN,
            {
                renderDom:null,
                playerId:videoEl,
                renderMode:VideoRenderMode.RENDER_MODE_HIDDEN
            }
         ) 
         this.RTCInstance.setLocalVideoPlayer(
            StreamIndex.STREAM_INDEX_MAIN,
            {
                renderDom:videoEl,
                playerId:videoEl,
                renderMode:VideoRenderMode.RENDER_MODE_HIDDEN
            }
        )
    }
    playLocalVideo(){
        this.RTCInstance.setLocalVideoPlayer(
            StreamIndex.STREAM_INDEX_MAIN,
            {
                renderDom:this.RTCLiveStatus.videoElementId,
                renderMode:VideoRenderMode.RENDER_MODE_HIDDEN
            }
        )
    }

    /**设置麦克风设备 */
    async chooseMicphoneDevice(deviceId:string){
        if(deviceId == this.RTCLiveStatus.micStatus.id){
            console.warn('传入了和当前使用中的麦克风相同id');
            return
        }
        try{
            if(!this.streamLocalAudioTrack){
                this.streamLocalAudioTrack = await this.RTCInstance.startAudioCapture(deviceId);
            }
            else{
                await this.RTCInstance.setAudioCaptureDevice(deviceId);
            }
            this.RTCInstance.setCaptureVolume(StreamIndex.STREAM_INDEX_MAIN,100)
            this.RTCLiveStatus.micStatus.id = deviceId
            this.RTCLiveStatus.micStatus.enabled = true
        }
        catch(e){
            throw new Error(`设置id为[${deviceId}] 麦克风设置异常：${e}`)
        }
    }

    async setVideoConfig() {
        const track = this.RTCInstance.getLocalStreamTrack(StreamIndex.STREAM_INDEX_MAIN,'video')
        const capabilities = track.getCapabilities()
        const maxWidth = capabilities.width.max
        const maxHeight = capabilities.height.max

        const params = {
            ...this.RTCLiveStatus.encoderConfig,
            height:1280,
            width:720,
            frameRate:15,
            maxKbps:2260
        }
        await this.RTCInstance.setVideoEncoderConfig(params)
    }



    /** 设置摄像头设备 */
    async chooseCameraDevice(deviceId:string){
        if(deviceId == this.RTCLiveStatus.cameraStatus.id){
            this.playLocalVideo()
            console.warn('传入了和当前使用中的摄像头相同id');
            return
        }
        try{
            if(!this.streamLocalVideoTrack){
                this.streamLocalVideoTrack = await this.RTCInstance.startVideoCapture(deviceId);
            }
            else{
                await this.RTCInstance.setVideoCaptureDevice(deviceId)
            }
            await this.setVideoConfig()
            this.RTCInstance.setLocalVideoMirrorType(MirrorType.MIRROR_TYPE_NONE)
            this.playLocalVideo()
            this.RTCLiveStatus.cameraStatus.id = deviceId
            this.RTCLiveStatus.cameraStatus.enabled = true
        }
        catch(e){
            throw new Error(`设置id为[${deviceId}] 摄像头设置异常：${e}`)
        }
    }


    /** 启用/禁用当前选择的麦克风 */
    async setMicphonesStatus(status:boolean){
        try{
            if(status){
                await this.RTCInstance.startAudioCapture(this.RTCLiveStatus.micStatus.id)
            }
            else{
                await this.RTCInstance.stopAudioCapture()
            }
            this.RTCLiveStatus.micStatus.enabled = status
        }
        catch(e){
            throw new Error(`禁用麦克风异常：${e}`)
        }
    }

    /** 启用/禁用当前选择的摄像头 */
    async setCameraStatus(status:boolean){
        try{
            if(status){
                await this.RTCInstance.startVideoCapture(this.RTCLiveStatus.cameraStatus.id)
            }
            else{
                await this.RTCInstance.stopVideoCapture()
            }
            this.RTCLiveStatus.cameraStatus.enabled = status
        }
        catch(e){
            throw new Error(`禁用摄像头异常：${e}`)
        }
    }

    /** 开启/停止推流 */
    async setStreamingStatus(status:boolean){
        if(!this.streamLocalAudioTrack || !this.streamLocalVideoTrack){
            throw new Error(`请先设置${!this.streamLocalAudioTrack?'麦克风':'摄像头'}设备`)
        }
        try{
            if(status){
                if(this.RTCLiveStatus.roomStatus != RoomStatusEnum.READY){
                    await this.joinTheRoom()
                }
                await this.RTCInstance.publishStream(MediaType.AUDIO_AND_VIDEO);
            }
            else{
                await this.RTCInstance.unpublishStream(MediaType.AUDIO_AND_VIDEO)
                this.removeUserPublishStreamListener()
                this.removeUserUnpublishStreamListener()
            }
            this.RTCLiveStatus.isPublishing = status
        }
        catch(e){
            this.removeUserPublishStreamListener()
            this.removeUserUnpublishStreamListener()
            throw new Error(`${status?'推流':'停止推流'}异常：${e}`)
        }
    }

    async leaveRoom(){
        try{
            await this.RTCInstance.leaveRoom()
            this.RTCLiveStatus.roomStatus = RoomStatusEnum.LEAVE
        }
        catch(e){
            throw new Error(`离开[${this.RTCLiveStatus.roomId}]房间异常：${e}`)
        }
    }

    /** 获取当前设备状态 */ 
    get(){
        return this.RTCLiveStatus
    }

    /** 绑定事件绑定回调 */
    on(event:StreamHostEventEnum,callback:Function){
        const _temp = this.eventMap.get(event)
        if(isArray(_temp)){
            if(!_temp.includes(callback)){
                _temp.push(callback)
                this.eventMap.set(event,_temp)
            }
        }
        else{
            this.eventMap.set(event,[callback])
        }
    }

    /** 解除事件绑定回调 */
    removeListener(event:StreamHostEventEnum,callback?:Function){
        const _temp = this.eventMap.get(event)
        if(_temp){
            if(isNullOrUnDef(callback)){
                this.eventMap.delete(event)
            }
            else{
                if(isArray(_temp)){
                    const index = _temp.findIndex(item=>item===callback)
                    index && _temp.splice(index,1)
                    this.eventMap.set(event,_temp)
                }
                else{
                    this.eventMap.delete(event)
                }
            }
        }
    }
}