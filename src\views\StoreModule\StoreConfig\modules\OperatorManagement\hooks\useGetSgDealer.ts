import { ref } from "vue";
import type { TreeOption } from "naive-ui";
import { useLoading, useMessages } from "@/hooks";
import { SystemSetting } from "@/settings/systemSetting";
import { getSgDealerGroup, getSgDealerInfo } from "@/services/api";

export default function useGetSgDealer() {
    const messages = useMessages();

    /** 选中节点 */
    const checkedKeysRef = ref<(string | number)[]>(['0']);

    /** 经销商分组树形数据 */
    const treeData = ref([]);

    /** 经销商列表 */
    const dealerList = ref([]);

    const searchValue = ref(null);

    /** 经销商列表参数 */
    const _params = {
        data: {
            searchValue: searchValue.value,
            groupIdList: ['0'] as (string | number)[],
        },
        pageVO: {
            current: 1,
            size: SystemSetting.pagination.pageSize,
        },
    };
    /** 总记录数 */
    let recordsTotal = 1;

    const { loading, startLoading, endLoading } = useLoading();
    /** 获取经销商分组 */
    async function getDealerGroupsSelectList() {
        try {
            treeData.value = [];
            startLoading();
            // 获取经销商分组列表
            const res = await getSgDealerGroup({});
            // 如果响应为空，则返回
            if (!res.length) {
                return;
            }

            // 创建无分组选项
            let childrenList = [
                {
                    label: "未分组",
                    key: "0",
                    depth: 2,
                    isLeaf: true,
                    disabled: false,
                },
            ];

            // 处理经销商分组数据
            childrenList.push(
                ...res.map(item => {
                    const children = item.dealerGroupPOS?.map(subItem => ({
                        label: subItem.name,
                        key: subItem.id,
                        depth: 3,
                        isLeaf: true,
                        disabled: false,
                    }));

                    return {
                        label: item.name,
                        key: item.id,
                        depth: 2,
                        isLeaf: !children,
                        children: children || [],
                        disabled: !children,
                    };
                }),
            );

            // 将分组选项推送到 dealerGroupsSelectList
            treeData.value.push(...childrenList);
        } catch (error) {
            messages.createMessageError("获取经销商分组失败: " + error);
        } finally {
            endLoading();
        }
    }

    const { loading: isGetLoading, startLoading: startGetLoading, endLoading: endGetLoading } = useLoading();
    /** 获取经销商列表 */
    const getSgDealerInfoList = async () => {
        try {
            startGetLoading();
            _params.data.searchValue = searchValue.value;
            const { total, current, size, records } = await getSgDealerInfo(_params);
            _params.pageVO.current = Number(current);
            _params.pageVO.size = Number(size);
            recordsTotal = Number(total);
            if (records && _params.pageVO.current === 1) {
                dealerList.value = [...records];
            } else {
              records.forEach(item => {
                dealerList.value.push(item);
              });
            }
        } catch (error) {
            messages.createMessageError("获取经销商列表失败" + error);
        } finally {
            endGetLoading();
        }
    };

    /** 滚动加载 */
    const handleScroll = (e) => {
    	const currentTarget = e.currentTarget as HTMLElement;
    	if (
    		currentTarget.scrollTop + currentTarget.offsetHeight >=
    		currentTarget.scrollHeight
    	) {
    		if (_params.pageVO.current * _params.pageVO.size < recordsTotal) {
    			_params.pageVO.current++;
    			getSgDealerInfoList();
    		}
    	}
    };

    /** 节点选中项发生变化时的回调函数 */
    const handleCheckedKeysFn = (keys: Array<string | number>, option: Array<TreeOption | null>, meta: { node: TreeOption | null, action: 'check' | 'uncheck' }) => {
    	checkedKeysRef.value = keys;
        _params.data.groupIdList = keys;
    	getSgDealerInfoList();
    };

    return {
        loading,
        isGetLoading,
        treeData,
        dealerList,
        checkedKeysRef,
        searchValue,
        _params,
        getDealerGroupsSelectList,
        getSgDealerInfoList,
        handleCheckedKeysFn,
        handleScroll
    };
}
