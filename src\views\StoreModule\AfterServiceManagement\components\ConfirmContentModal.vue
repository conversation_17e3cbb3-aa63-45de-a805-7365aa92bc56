<template>
  <JModal
    v-model:show="modalVisible"
    :title="title"
    width="600"
    @after-leave="closeModal"
    :isScale="false"
    :auto-focus="false"
  >
    <n-form
      ref="formRef"
      :model="model"
      :rules="rules"
      label-placement="left"
      label-width="auto"
      require-mark-placement="right-hanging"
      size="small"
      :style="{ width: '100%' }"
    >
      <n-space justify="space-around" style="margin-bottom: 10px">
        <span class="title">{{ afterSaleManagementLabels[model.action] }}</span>
      </n-space>
      <!-- 同意部分 -->
      <n-space
        justify="space-around"
        v-if="model.type == '同意' && afterSaleManagementLabels[model.action] != afterSaleManagementLabels[3]"
      >
        <p v-if="model.action == 2" style="text-align: center">
          {{ promptLanguageConsent[1] }}{{ `￥${(drawerProps.row.refundAmount / 100).toFixed(2)}` }}
        </p>
        <p :style="'color:' + (model.action == 4 ? 'rgb(163, 0, 20)' : '')">
          {{ promptLanguageConsent[model.action]
          }}{{ model.action == 1 ? `￥${(drawerProps.row.refundAmount / 100).toFixed(2)}` : "" }}
        </p>
      </n-space>

      <!-- 同意部分地址匹配 -->
      <n-spin size="small" :show="addressShow">
        <n-space  v-if="model.type == '同意' && afterSaleManagementLabels[model.action ] == afterSaleManagementLabels[3] && !addressShow && drawerProps?.row?.belongStoreId === '0'">
			<p v-if="radioGroupArrayValue.length == 0" :style="'color:' + (model.action == 3 ? 'rgb(163, 0, 20);text-align: center;width: 500px; cursor: pointer;' : '')" @click="handleConfigurationAddress">{{promptLanguageConsent[4]}}</p>
			<n-form-item v-if="radioGroupArrayValue.length != 0" :label="promptLanguageConsent[model.action]" path="radioGroupValue">
			  <n-scrollbar style="max-height: 400px">
                <n-radio-group v-model:value="model.radioGroupValue" name="radiogroup1">
                  <n-space vertical>
                    <n-radio style="display: flex; margin: 5px 0;align-items: center;" :value="item.id" v-for="item in radioGroupArrayValue" :key="item.id" >
					 <p>{{`${item.province + item.cityName + item.area + item.address +' '+item.mobile +' '+ item.name}`}}</p>
					 <p v-if="item.remark"><span style="color: #f73d3d;">备注:</span> {{item.remark}}</p>
                     </n-radio>
                   </n-space>
                </n-radio-group>
			  </n-scrollbar>
            </n-form-item>
          </n-space>
        <n-space
          v-if="
            model.type == '同意' &&
            afterSaleManagementLabels[model.action] == afterSaleManagementLabels[3] &&
            !addressShow && drawerProps?.row?.belongStoreId !== '0'
          "
        >
          <n-form-item :label="promptLanguageConsent[model.action]" path="radioGroupValue">
            <div class="refund-return-product-address">
              <div class="row">
                <div class="label">门店名称</div>
                <div class="value">{{ shopInfo?.storeName ? shopInfo?.storeName : '-' }}</div>
              </div>
              <div class="row">
                <div class="label">店长</div>
                <div class="value">{{ shopInfo?.contactName ? shopInfo?.contactName : '-' }}</div>
              </div>
              <div class="row">
                <div class="label">门店号码</div>
                <div class="value">{{ shopInfo?.contactPhone ? shopInfo?.contactPhone : '-' }}</div>
              </div>
              <div class="row">
                <div class="label">门店地址</div>
                <div class="value">{{ shopInfo?.province }}{{ shopInfo?.city }}{{ shopInfo?.area }}{{ shopInfo?.addressDetail }}</div>
              </div>
            </div>
          </n-form-item>
          <!-- 提示 -->
          <n-space :span="8" style="margin-left: 40px; margin-bottom: 12px">
            <span style="color: #999">注：门店商品需用户退回所属门店</span>
          </n-space>
        </n-space>
      </n-spin>

      <!-- 拒绝申请 -->
      <n-space v-if="model.type == '拒绝'">
        <n-form-item :label="promptLanguageConsent[model.action]" path="returnAddress">
          <n-input
            style="width: 480px"
            v-model:value="model.returnAddress"
            type="textarea"
            maxlength="100"
            show-count
            placeholder="请输入拒绝原因，用户可以看到，必填"
            clearable
          />
        </n-form-item>
      </n-space>

      <!-- 收货并退款 -->
      <n-space justify="space-around" v-if="model.type == '收货并退款'">
        <div>
          <p style="text-align: center">
            {{ promptLanguageConsent[8] }}{{ `￥${(drawerProps.row.refundAmount / 100).toFixed(2)}` }}
          </p>
          <p>
            {{
              promptLanguageConsent[model.action] == promptLanguageConsent[9]
                ? promptLanguageConsent[model.action]
                : null
            }}
          </p>
        </div>
      </n-space>

      <!-- 再次发起退款 -->
      <n-space justify="space-around" v-if="model.type == '再次发起退款'">
        {{ promptLanguageConsent[model.action] }}{{ `￥${(drawerProps.row.refundAmount / 100).toFixed(2)}` }}
      </n-space>

      <!-- 已线下退款 -->
      <n-space justify="space-around" v-if="model.type == '已线下退款'">
        {{ promptLanguageConsent[model.action] }}
      </n-space>

      <!-- 录入物流信息 -->
      <n-form-item v-if="model.type == '录入物流信息'" label="快递公司" path="expressCompanyValue">
        <JExpressCompany
          v-model:value="model.expressCompanyValue"
          placeholder="请选择快递单号"
          isImmediately
          style="width: 100%"
        />
      </n-form-item>
      <n-form-item v-if="model.type == '录入物流信息'" label="快递单号" path="trackingNumberValue">
        <n-input
          v-model:value="model.trackingNumberValue"
          type="text"
          placeholder="请填写快递单号"
          style="width: 100%"
          maxlength="255"
          clearable
        />
      </n-form-item>
    </n-form>
    <template #footer>
      <n-space justify="end">
        <n-button
          v-if="afterSaleManagementLabels[model.action] != afterSaleManagementLabels[6]"
          size="small"
          @click="modalVisible = false"
        >
          取消
        </n-button>
        <n-button
          v-if="afterSaleManagementLabels[model.action] != afterSaleManagementLabels[6]"
          size="small"
          type="primary"
          :loading="isLoading"
          @click="handleSave"
        >
          {{ afterSaleManagementLabels[model.action] == afterSaleManagementLabels[23] ? "保存" : "确认" }}
        </n-button>
        <!-- <n-button
		v-if="(afterSaleManagementLabels[model.action] == afterSaleManagementLabels[23])"
		size="small"
		type="primary"
		:loading="isLoading"
		@click="handleSave"
		>保存</n-button
		> -->
        <!-- <n-button
		v-if="(afterSaleManagementLabels[model.action] == afterSaleManagementLabels[23])"
		size="small"
		type="primary"
		:loading="isLoading"
		@click="handleSave"
		>保存并退款</n-button
		> -->
        <n-button
          v-if="afterSaleManagementLabels[model.action] == afterSaleManagementLabels[6]"
          size="small"
          type="primary"
          @click="handleClear"
        >
          知道了
        </n-button>
      </n-space>
    </template>
  </JModal>
</template>

<script setup lang="ts" name="ConfirmContentModal">
import { ref, reactive, computed } from "vue";
import { useMessages } from "@/hooks";
import { afterSaleManagementLabels } from "@/constants";
import type { FormRules, FormItemRule } from "naive-ui";
import { returnAddressList, addressList, getShopInfo } from "@/services/api";
import { useRouter } from "vue-router";
import { RoutesName } from "@/enums/routes";
const { createMessageSuccess, createMessageError } = useMessages();

const router = useRouter();

/** 弹窗显隐 */
const modalVisible = ref(false);

/** 标题 */
const title = ref();

/* 表单参数初始化 */
const initParams: {
  type: string; // 模式
  action: number;
  returnAddress: string;
  radioGroupValue: string;
  expressCompanyValue: string | number;
  trackingNumberValue: string | number;
  refresh?: () => void; // 刷新表格
} = {
  type: "",
  action: 0,
  returnAddress: "",
  radioGroupValue: "",
  expressCompanyValue: null,
  trackingNumberValue: null,
};
const model = ref({ ...initParams });

const radioGroupArrayValue = ref([]);

/* 表单实例 */
const formRef = ref();

//弹框提示语的内容
const promptLanguageConsent = ref({
  1: "退款金额", //同意按钮
  2: "物流代收订单和定金支付订单全额退款均不支持在线操作，请先联系客户获得线下退款信息后再确认，以便后续安排线下退款。",
  3: "退货地址:",
  4: "未设置退货地址，请先设置再操作",
  5: "本次售后不用退款",
  6: "售后单状态已变更为待打款，财务付款后，请操作“已线下打款”功能完成本次售后处理流程。",
  7: "拒绝原因:",
  8: "已收到货，退款金额",
  9: "物流代收订单和定金支付订单全额退款均不支持在线操作，请先联系客户获得线下退款信息后再确认，以便后续安排线下退款。",
  10: "退款金额", // 再次发起退款按钮
  11: "财务已打款",
});

/* 接收父组件传过来的参数 */
const drawerProps = ref();
const acceptParams = params => {
	console.log(params.row);
	
  model.value.action = params.code;
  model.value.type = params.type;
  drawerProps.value = params;
  // 触发弹窗
  modalVisible.value = true;
  if (model.value.action == 3) {
	 params.row?.belongStoreId !== "0" ? getShopInfoFn(params.row?.belongStoreId) : getAddress()
  }
};

/* 清空表单 */
const formDataReset = () => {
  model.value = { ...initParams };
  radioGroupArrayValue.value = [];
};

/* 关闭弹窗之后 */
const closeModal = () => {
  formDataReset();
};

/* 确认--保存 */
const defaultActionNumber = ref(2); //但同意退款的action == 6 时 启用默认值
const isLoading = ref(false);
const handleSave = (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async errors => {
    if (!errors) {
      isLoading.value = true;
      if (model.value.action == 2) {
        model.value.action = 6;
      }
      const _params = {
        data: {
          recordNo: drawerProps.value.row.recordNo,
          action: model.value.action == 6 ? defaultActionNumber.value : model.value.action,
          addressId: model.value.radioGroupValue,
          shipCompanyCode:
            model.value.type == "录入物流信息"
              ? model.value.expressCompanyValue
              : drawerProps.value.row.shipCompanyCode,
          trackingNo:
            model.value.type == "录入物流信息" ? model.value.trackingNumberValue : drawerProps.value.row.trackingNo,
          rejectionReasonDescription: model.value.returnAddress,
          recordType: drawerProps.value.row.recordType,
        },
      };
      try {
        await drawerProps.value.getInfoApi(_params);
      } catch (err) {
        createMessageError("确认失败: " + err);
      } finally {
        if (model.value.action != 6) {
          modalVisible.value = false;
          drawerProps.value.refresh();
        }
        isLoading.value = false;
      }
    }
  });
};

/** 知道 */
const handleClear = () => {
  modalVisible.value = false;
  drawerProps.value.refresh();
};

/* 表单规则 */
const rules: FormRules = {
  returnAddress: {
    required: true,
    trigger: ["blur", "change"],
    message: "请输入拒绝原因",
  },
  expressCompanyValue: {
    required: true,
    trigger: ["blur", "change"],
    message: "请选择快递公司",
  },
  trackingNumberValue: {
    required: true,
    trigger: ["blur", "change"],
    message: "请填写快递单号",
  },
};

/** 获取地址 */
const addressShow = ref(false);
const getAddress = async () => {
  addressShow.value = true;
  try {
    const data = await addressList({ data: { type: 1 } });
    radioGroupArrayValue.value = data;
    model.value.radioGroupValue = radioGroupArrayValue.value[0]?.id;
  } catch (err) {
    createMessageError("获取地址配置失败: " + err);
  } finally {
    addressShow.value = false;
  }
};
const shopInfo = ref<any>({});
// 获取门店信息
const getShopInfoFn = async (storeId) => { 
	addressShow.value = true;
	try {
		const data = await getShopInfo(storeId);
		shopInfo.value = data
	} catch (err) {
		createMessageError("获取地址配置失败: " + err);
	} finally {
		addressShow.value = false;
	}
};
/** 跳转到配置地址 */
const handleConfigurationAddress = () => {
  router.push({
    name: RoutesName.StoreConfig,
    query: {
      configurationAddressStatus: 1,
    },
  });
};

defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less">
.title {
  font-size: 20px;
  font-weight: 700;
}
.refund-return-product-address {
  padding-top: 2px;
  margin-left: 5px;
  .row {
    display: flex;
    .label {
      width: 100px;
    }
  }
}
</style>
