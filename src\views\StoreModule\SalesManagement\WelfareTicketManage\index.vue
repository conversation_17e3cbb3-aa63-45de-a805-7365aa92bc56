<template>
  <WelfareTicketManagePage 
    :liveRoomId="props.liveRoomId"
    :welfareModalType="props.welfarePageType"
    v-if="welfarePageType == WelfarePageTypeEnum.PAFGE">
  </WelfareTicketManagePage>
  <JModal v-else
    v-model:show="isShow"
    width="1200"
    height="800"
    title="福利券管理"
    @after-leave="closeModal"
    :negativeText="false"
    :positiveText="false"
  >
    <WelfareTicketManagePage 
      :liveRoomId="props.liveRoomId" 
      :welfarePageType="props.welfarePageType"
      :liveRoomStatus="props.liveRoomStatus"
      >
    </WelfareTicketManagePage>
  </JModal>
</template>

<script lang="tsx" setup name="DoctorManagement">
import { computed } from "vue";
import WelfareTicketManagePage from "./components/WelfareTicketManagePage.vue"
import { WelfarePageTypeEnum } from "./types";
const props = withDefaults(defineProps<{
    show?: boolean;
    welfarePageType?:WelfarePageTypeEnum
    liveRoomId?:string
    liveRoomStatus?:number
}>(), {
    show: false,
    welfarePageType:WelfarePageTypeEnum.PAFGE,
    liveRoomId:'',
    liveRoomStatus:null
});
const emits = defineEmits<{
    (e: 'update:show', value: boolean): void;
}>();

const isShow = computed({
    get: () => props.show,
    set: (value: boolean) => {
      emits('update:show', value);
    }
});
// 关闭按钮
const closeModal = () => {
    isShow.value = false;
}
</script>

<style lang="less" scoped>
@import "@/styles/defaultVar.less";
</style>