import {defHttp} from '@/services';

export const enum MedicalInquiryPrescriptionApi {
    getMedicalInquiryPrescription = '/formulary/page',
    storePrescriptionDetail = '/formulary/get',
    hospitalPresGetDetail = '/formulary/get/hospital',
    checkingPrescription = '/formulary/reviewPres',
}

/** 获取问诊处方列表 */
export function getMedicalInquiryPrescription(params) {
    return defHttp.post({
        url: MedicalInquiryPrescriptionApi.getMedicalInquiryPrescription,
        params
    })
}
/** 获取问诊处方详情 */
export function getMedicalInquiryPrescriptionDetail(params) {
    return defHttp.get({
        url: MedicalInquiryPrescriptionApi.hospitalPresGetDetail,
        params,
        requestConfig:{
            isQueryParams:true
        }
    })
}
/** 获取问诊处方详情---商城后台-问诊-问诊处方详情 */
export function getStorePrescriptionDetail(params) {
  return defHttp.get({
    url: MedicalInquiryPrescriptionApi.storePrescriptionDetail,
    params,
    requestConfig:{
      isQueryParams:true
    }
  })
}
/** 药师端处方审核 */
export function checkingPrescription(params) {
    return defHttp.post({
        url: MedicalInquiryPrescriptionApi.checkingPrescription,
        params
    })
}
