<template>
    <div class="button-container" @click="emits('click')">
        <n-button v-bind="$attrs" text>
            <slot></slot>
        </n-button>
    </div>
</template>

<script lang="ts" setup name='JTextButton'>

/** emits */
const emits = defineEmits<{
    (e: 'click'): void;
}>();

</script>


<style lang="less" scoped>
.button-container {
    min-width: 68px;
    min-height: 28px;
    border-radius: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    position: relative;
    margin-right: 2px;
    margin-left: 2px;
    padding: 2px 4px;
    &:hover {
        background-color: #F2F3F5;
    }
}
</style>