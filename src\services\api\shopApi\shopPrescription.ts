import { defHttp } from "@/services";

/** 店员店长 */
export const enum ShopPrescriptionApi {
  page = "/csStoreManager/page",
  changeRole = "/csStoreManager/change/roleType",
  changeStoreInfo = "/csStoreManager/change/storeInfo",
  changeMembership = "/csStoreManager/change/staff/relation",
  shopList = "/storeEntity/listStoreInfo",
}

/**
 * @description 店员店长分页
 */
export function getShopPrescriptionPage(params) {
  return defHttp.post({
    url: ShopPrescriptionApi.page,
    params,
  });
}
/**
 * @description 修改角色
 */
export function changeShopRole(params) {
  return defHttp.post({
    url: ShopPrescriptionApi.changeRole,
    params,
  });
}
/**
 * @description 修改归属门店
 */
export function changeAffiliatedStore(params) {
  return defHttp.post({
    url: ShopPrescriptionApi.changeStoreInfo,
    params,
  });
}
/**
 * @description 一键转会员
 */
export function changeMembership(params) {
  return defHttp.post({
    url: ShopPrescriptionApi.changeMembership,
    params,
  });
}
/**
 * @description 获取门店列表（选择框使用）
 */
export function getListStoreInfo(params) {
  return defHttp.post({
    url: ShopPrescriptionApi.shopList,
    params,
  });
}