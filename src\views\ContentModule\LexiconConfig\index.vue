<template>
  <n-layout>
    <n-layout-content id="OrderManagement">
      <TabsLayout
        v-model:value="tabNameRef"
        :tabsData="tabsData"
        onlyTabs
        height="100%"
        paneClass="orderTotalReport-wrapper-tabs"
      >
        <keep-alive>
          <component :is="currentPage" />
        </keep-alive>
      </TabsLayout>
    </n-layout-content>
  </n-layout>
</template>

<script lang="ts" setup name="OrderTotalReport">
import { ref, computed } from "vue";
import { LexiconTypeEnum } from "@/enums";
/** 相关组件 */
import TabsLayout from "@/layout/TabsLayout.vue";
import OfficialLexicon from "./modules/OfficialLexicon/index.vue";
import CustomLexicon from "./modules/CustomLexicon/index.vue";

const tabNameRef = ref(LexiconTypeEnum.Official);

/** Tab */
const tabsData = [
  {
    label: "官方敏感词库",
    key: LexiconTypeEnum.Official,
  },
  {
    label: "自定义敏感词库",
    key: LexiconTypeEnum.Custom,
  }
];

/** 组件映射 */
const pageMap = {
  [LexiconTypeEnum.Official]: OfficialLexicon,
  [LexiconTypeEnum.Custom]: CustomLexicon
}

const currentPage = computed(() => pageMap[tabNameRef.value])
</script>

<style lang="less" scoped>
@import "@/styles/default.less";
.order-wrapper {
  width: 100%;
  height: 100%;
  .header {
    height: 86px;
    border-bottom: 1px solid @default-border-color;
  }
  .order-content {
    height: calc(100% - 86px);
  }
}
//:deep(.orderTotalReport-wrapper-tabs) {
//  height: calc(100% - 36px) !important;
//}
</style>
