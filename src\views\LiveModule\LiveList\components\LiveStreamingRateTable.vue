<template>
    <n-modal
        v-model:show="storeRateShow" 
        :auto-focus="false" 
        @after-leave="handleClose" 
    >
        <n-card
            class="product——select-card"
            style="width: 1000px"
            :bordered="false"
            size="small"
            title="门店上播率统计"
            closable
            @close="handleClose"
            >
            <FormLayout
                table-row-key="uniqueId"
                style="height:610px"
                :isTableSelection="false"
                :isDisplayIndex="false"
                :isLoading="isLoading"
                :is-need-collapse="false"
                :tableData="tableData"
                :tableColumns="tableColumns"
                :pagination="paginationRef"
                :defaultCheckedKeys="selectedKeysReactive.selectedKeys"
                @paginationChange="paginationChange"
            >
            <template #searchForm>
            <n-form
                ref="formRef"
                :model="searchFormReactive"
                :show-feedback="false"
                label-placement="left"
                label-width="auto"
                require-mark-placement="right-hanging"
                size="small"
                :style="{ width: '100%' }"
            >
                <n-form-item label="所属店铺" label-placement="left">
                <JStoreSelect width="100%" v-model:value="searchFormReactive.storeId" ></JStoreSelect>
                </n-form-item>
                <n-form-item label="归属店长ID" label-placement="left">
                <j-search-input
                    v-model:value="searchFormReactive.managerId"
                    placeholder="请输入归属店长ID"
                    style="width: 250px"
                    @search="searchFormSearch"
                />
                </n-form-item>
            </n-form>
            </template>
            <template #tableHeaderBtn>
            <n-button @click="refreshTableData">刷新</n-button>
            </template>
        </FormLayout>
        </n-card>
    </n-modal>
  </template>
<script setup lang="tsx">
import FormLayout from '@/layout/FormLayout.vue';
import {useTableDefault} from '@/hooks/useTableDefault';
import JStoreSelect from '@/components/JSelect/JStoreSelect.vue'
import {reactive, ref, watch, computed} from 'vue';
import { getLiveRatePage } from '@/services/api/liveApi/liveRate';
import { useMessages } from "@/hooks";
const { createMessageSuccess, createMessageError } = useMessages();
interface ProductSelectModalProps {
    show: boolean,
    liveActivityId:string
}
  
const props = withDefaults(defineProps<ProductSelectModalProps>(), {
    show: false,
    liveActivityId:''
});
  
const emits = defineEmits<{
    (e: 'update:show', show: boolean): void,
    
}>();
const storeRateShow = computed({
    get() {
        return props.show;
    },
    set(value) {
        emits("update:show", value);
    },
});

function handleClose() {
    storeRateShow.value = false;
}
const {
    isLoading,
    tableData,
    pageTableData,
    paginationRef,
    paginationChange,
    refreshTableData,
    } = useTableDefault({
    pageDataRequest: (params) => {
        return getLiveRatePage(params);
    },
});

const initSearchFormReactive = () => {
return {
    managerId: '',
    storeId:null,
    liveId:props.liveActivityId
};
};

let searchFormReactive = reactive(initSearchFormReactive());
const selectedKeysReactive = reactive({
    selectedKeys: [],
    selectedOptions: [],
});


let tableColumns = [
{
    title: '店铺名称',
    key: 'storeName',
    align: 'left',
    fixed: 'left',
    width: 120,
},
{
    title: '店铺ID',
    key: 'storeId',
    align: 'left',
    width: 120,
},
{
    title: '店长信息',
    key: 'managerName',
    align: 'left',
    fixed: 'left',
    width: 120,
    render: (rowData: any) => {
    let phone = rowData?.mobile ? rowData.mobile : '-'
    return (
        <>
            <span>{rowData?.managerName ? rowData.managerName + '(' + phone + ')' : '-'}</span>
        </>
    );
    },
},
{
    title: '店长ID',
    key: 'managerId',
    align: 'left',
    width: 120,
},
{
    title: '会员数',
    key: 'memberCount',
    align: 'left',
    width: 80,
},
{
    title: '观看会员数',
    key: 'watchMemberCount',
    align: 'left',
    width: 80,
},
{
    title: '上播率',
    key: 'watchLiveRate',
    align: 'left',
    width: 60,
    render: (rowData: any) => {
    return (
        <>
            <span>{rowData?.watchLiveRate + '%' || '-'}</span>
        </>
    );
    },
},
];

function pageProductData() {
paginationRef.value.current = 1;
    pageTableData(searchFormReactive, paginationRef.value);
}

function searchFormSearch() {
    pageProductData();
}
watch(() => props.show, (newVal) => {
    if (newVal) {
        pageProductData();
    }

}, {immediate: true});
watch(() => [
    searchFormReactive.storeId,
], 
() => {
    pageProductData();
}
);

</script>

