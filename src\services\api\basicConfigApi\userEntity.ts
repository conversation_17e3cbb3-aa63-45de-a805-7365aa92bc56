import { defHttp } from '@/services';
import { basicPlatformUrl } from "@/utils/http/urlUtils";

/** 基础配置之操作员管理 */
export const enum operatorApi {
  page = "/userEntity/page",
  add = "/userEntity/addUser",
  update = "/userEntity/update",
  delete = "/userEntity/delete",
  updateStatus = "/userEntity/updateStatus",
  resetPassword = "/userEntity/resetPassword",
}

/** 分页 */
export function getOperatorPage(params) {
  return defHttp.post({
    url: operatorApi.page,
    params
  })
}

/** 新增 */
export function addPlatformOperator(_params) {
  return defHttp.post({
    url: operatorApi.add,
    params: {
        data: _params
    }
  })
}

/** 更新 */
export function updatePlatformOperator(_params) {
  return defHttp.put({
    url: operatorApi.update,
    params: {
        data: _params
    }
  })
}

/** 删除 */
export function deleteOperatorById(id: string) {
  return defHttp.delete({
    url: operatorApi.delete,
    requestConfig: {
      isQueryParams: true,
    },
    params: {
      id
    },
  })
}

/** 启用/禁用 */
export function updateOperatorStatus(_params) {
  return defHttp.put({
    url: operatorApi.updateStatus,
    params: {
      data: _params
    }
  })
}

/** 重置密码 */
export function resetOperatorPassword(_params) {
  return defHttp.put({
    url: operatorApi.resetPassword,
    params: {
      data: _params
    }
  })
}