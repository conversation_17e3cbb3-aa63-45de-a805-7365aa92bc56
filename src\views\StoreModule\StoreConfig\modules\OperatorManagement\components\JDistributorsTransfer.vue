<template>
    <div class="wrapper">
        <n-spin :show="loading || isGetLoading" size="small" style="width: 46%;height: 100%;">
            <div class="left">
                <n-tree
                  block-line
                  checkable
                  :data="distributorsTree"
                  :on-load="handleLoad"
                  :allow-checking-not-loaded="true"
                  :cascade="true"
                  expand-on-click
                  :virtual-scroll="true"
                  check-strategy="parent"
                  @update:checked-keys="handleCheckedKeysChange"
                >    
                  <template #empty>
                      <JEmpty title="暂无分销员分组信息" />
                  </template>
                </n-tree>
            </div>
        </n-spin>
        <div class="right">
            <div class="header">
                <JSearchInput 
                    v-model:value="searchValue" 
                    placeholder="请输入分销员id、昵称" 
                    @search="handleSearch"
                    size="small" 
                    width="100%"
                />
            </div>
            <!-- 分销员列表 -->
            <div class="dealer-list-wrapper">
                <transition appear name="fade" mode="out-in">
                    <template v-if="distributorList.length > 0">
                        <div class="dealer-selected-wrapper">
                            <n-checkbox 
                                class="check-all" 
                                v-model:checked="model.checkedAllRef" 
                                label="全选"
                                :disabled="selectAll"
                                @update:checked="handleCheckedAllChange" 
                            />
                            <n-spin :show="isGetLoading" size="small" style="height: calc(100% - 36px);">
                                <div class="dealer-selected-list" @scroll="handleScroll">
                                    <n-checkbox-group v-model:value="checkDealerKeysRef" @update:value="handleUpdateValue">
                                        <n-space item-style="display: flex;" vertical>
                                            <n-checkbox class="dealer-list-item" v-for="item in distributorList" :value="item.id"
                                                :key="item.id"
                                                :disabled="item.disabled">
                                                <template #default>
                                                    <CustomOptions :infoItem="item" />
                                                </template>
                                            </n-checkbox>
                                        </n-space>
                                    </n-checkbox-group>
                                </div>
                            </n-spin>
                        </div>
                    </template>
                    <template v-else>
                        <JEmpty />
                    </template>
                </transition>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, toRaw, effectScope, watch, onScopeDispose, onMounted } from "vue";
import { useGteDistributors } from "../hooks";
import { SystemSetting } from "@/settings/systemSetting";
/** 相关组件 */
import CustomOptions from "./CustomOptions.vue";
import JEmpty from "./JEmpty.vue";
defineOptions({ name: 'JDealerTransfer' });

/** props */
const  props = withDefaults(defineProps<{
    distributors: Array<string | number>;
    communityDealersState:boolean //是否为分账入账方管理
    selectedId:any //分账入账方管理
}>(), {
    distributors: () => [],
    communityDealersState:false
});

/** emits */
const emits = defineEmits<{
    (e: "update:checked-keys", meta: { action: 'check' | 'uncheck'  | 'checkAll' | 'uncheckAll', currentId: string | number | Array<string | number>, currentItem: Object | Array<Object> }): void;
}>();

/** 分销员数据 */
const {
    loading,
    isGetLoading,
    distributorList,
    searchValue,
    _params,
    distributorsTree,
    getSgDealerInfoList,
    handleScroll,
    handleLoad,
    getStructureListStructures,
    handleCheckedKeysChange
} = useGteDistributors();

/** 初始化 */
const model = ref({
    checkedAllRef: false,
    treeValue:null
});

/** 选中的分销员id */
const checkDealerKeysRef = ref<(string | number)[]>([]);

/** 搜索 */
const handleSearch = () => {
    // 清空当前页码
    _params.pageVO.current = 1;
    _params.pageVO.size = SystemSetting.pagination.pageSize;
    getSgDealerInfoList();
};

/** 全选 */
function handleCheckedAllChange(checked: boolean) {
    const currentId = distributorList.value.map(item => item.id);
    if (checked) {
        // 触发事件
        emits("update:checked-keys", { action: 'checkAll', currentId, currentItem: toRaw(distributorList.value) });
    } else {
        // 触发事件
        emits("update:checked-keys", { action: 'uncheckAll', currentId, currentItem: toRaw(distributorList.value) });
    }
}

/** 选项组的值改变时的回调 */
function handleUpdateValue(value: Array<string | number>, meta: { actionType: 'check' | 'uncheck', value: string | number }) {
    const { actionType, value: selectId } = meta;

    // 查找当前选中的项
    const currentItem = toRaw(distributorList.value.find(dealer => dealer.id === selectId));
    // 触发事件
    emits("update:checked-keys", { action: actionType, currentId: selectId, currentItem });
}

/** 初始化 */
function init() {
    getSgDealerInfoList();
}
init();

/** 创建effectScope */
const scope = effectScope();

/** 全选按钮是否禁用 */
const selectAll = ref(false)

/** 作用域内运行一组副作用 */
scope.run(() => {
    // 监听
    watch(() => props.distributors, (newVal) => {
        if (newVal) {
            checkDealerKeysRef.value = newVal;
        }
    }, { immediate: true, deep: true });

    // 监听
    watch(() => [checkDealerKeysRef.value, distributorList.value], (newVal) => {
        if (distributorList.value.every(item => checkDealerKeysRef.value.includes(item.id))) {
            model.value.checkedAllRef = true;
        } else {
            model.value.checkedAllRef = false;
        }
    });
  });

onMounted(()=>{
    getStructureListStructures()
})

watch(
  () => distributorList.value,
  (newVal) => {
    if(props.communityDealersState){
       /** 已选中的分销员改为禁用 id赋值为空 */
        const updatedList = newVal.map((item) => {
          if (props.selectedId.includes(item.id)) {
              return { ...item, disabled: true,id:null,accountUserId:item.id};
          }
         return item;
        });
      /** 当前有分销员被选中 禁用全选按钮 */
      if (JSON.stringify(updatedList) !== JSON.stringify(distributorList.value)) {
        distributorList.value = updatedList;
        distributorList.value.map((item)=>{
          if(item.disabled){
              return selectAll.value = true
          }
        })
      }
    }
  },
  { deep: true }
);

/** 作用域销毁时，停止作用域 */
onScopeDispose(() => {
  scope.stop();
});
</script>

<style lang="less" scoped>
@import "@/styles/scrollbar.less";

.wrapper {
    width: 100%;
    height: 288px;
    display: flex;
    border: 1px solid #eee;
    :deep(.n-spin-content) {
        width: 100%;
        height: 100%;
    }

    .left {
        height: 100%;
        border-right: 1px solid #EEEEEE;
        overflow-y: auto;
        box-sizing: border-box;
        padding: 8px 12px;
        .scrollbar();
    }

    .right {
        width: 54%;
        height: 100%;
        box-sizing: border-box;

        .header {
            height: 28px;
            padding: 8px 12px;
        }

        .dealer-list-wrapper {
            height: calc(100% - 52px);
            padding-left: 12px;
            padding-bottom: 8px;
            box-sizing: border-box;

            .dealer-selected-wrapper {
                width: 100%;
                height: 100%;

                .check-all {
                    width: 100%;
                    height: 44px;
                    display: flex;
                    align-items: center;
                    padding: 2px 8px;
                    box-sizing: border-box;
                }

                :deep(.n-spin-content) {
                    height: 100%;
                }

                .dealer-selected-list {
                    height: 100%;
                    overflow-y: auto;
                    .scrollbar();

                    .dealer-list-item {
                        width: 100%;
                        display: flex;
                        align-items: center;
                        padding-left: 8px;
                        border-radius: 2px;

                        &:hover {
                            background-color: rgb(231, 241, 255);
                        }
                    }
                }
            }
        }
    }
}

:deep(.n-tree.n-tree--block-line .n-tree-node:not(.n-tree-node--disabled).n-tree-node--selected) {
    background-color: #fff;
}
</style>
