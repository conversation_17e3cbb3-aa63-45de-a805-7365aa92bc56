<template>
  <JModal
    v-model:show="isShow"
    width="700"
    title="新增患者资料"
    positiveText="确定"
    @after-leave="closeModal"
    @positive-click="_save"
    :positiveButtonProps="{
      loading: isLoading,
    }"
  >
    <n-form
      ref="formRef"
      :rules="rules"
      :model="model"
      label-width="auto"
      label-placement="left"
      require-mark-placement="right-hanging"
      :style="{
        width: '100%',
      }"
    >
      <n-grid :cols="8" :x-gap="24">
        <n-form-item-gi :span="24" label="姓名" path="name" required>
          <n-input v-model:value="model.name" placeholder="请输入真实姓名" maxlength="30" show-count type="text" />
        </n-form-item-gi>
        <n-form-item-gi v-if="systemStore._globalConfig['isAddIdNo']" :span="24" label="身份证号" path="idNo" required>
          <n-input
            v-model:value="model.idNo"
            placeholder="请输入正确的身份证号"
            maxlength="18"
            show-count
            type="text"
            @update:value="changeIdNo"
          />
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="性别" path="gender" required>
          <n-radio-group v-model:value="model.gender">
            <n-space>
              <n-radio value="男">男</n-radio>
              <n-radio value="女">女</n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="出生日期" path="birthday" required>
          <n-date-picker
            style="width: 100%"
            v-model:value="model.birthday"
            type="date"
            :is-date-disabled="dateDisabled"
            clearable
          />
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="手机号" path="mobile" required>
          <n-input
            v-model:value="model.mobile"
            placeholder="请输入正确的手机号"
            maxlength="11"
            show-count
            type="text"
          />
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="患者与问诊人关系" path="relation" required>
          <n-select v-model:value="model.relation" placeholder="请选择" :options="relationOptions"></n-select>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="肝功能" path="liverFunction" required>
          <n-radio-group v-model:value="model.isLiver">
            <n-space>
              <n-radio :value="0">正常</n-radio>
              <n-radio :value="1">异常</n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="肾功能" path="renalFunction" required>
          <n-radio-group v-model:value="model.isKidney">
            <n-space>
              <n-radio :value="0">正常</n-radio>
              <n-radio :value="1">异常</n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item-gi>
        <n-form-item-gi
          :span="24"
          label="过敏史"
          required
          :feedback-style="{
            display: `${model.allergy ? 'none' : 'block'}`,
          }"
        >
          <n-radio-group v-model:value="model.allergy">
            <n-space>
              <n-radio :value="0">无</n-radio>
              <n-radio :value="1">有</n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item-gi>
        <n-gi v-if="model.allergy" :span="24" style="margin-left: 120px; margin-bottom: 12px; margin-top: 12px">
          <ButtonMultiSelect v-model:value="model.selectedAllergy" :options="allergyOptions" />
        </n-gi>
        <n-form-item-gi
          :span="24"
          label="个人病史"
          path="medicalHistory"
          required
          :feedback-style="{
            display: `${model.medicalHistory ? 'none' : 'block'}`,
          }"
        >
          <n-radio-group v-model:value="model.medicalHistory">
            <n-space>
              <n-radio :value="0">无</n-radio>
              <n-radio :value="1">有</n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item-gi>
        <n-gi v-if="model.medicalHistory" :span="24" style="margin-left: 120px; margin-bottom: 12px; margin-top: 12px">
          <ButtonMultiSelect v-model:value="model.selectedMedicalHistory" :options="medicalHistoryOptions" />
        </n-gi>
        <n-form-item-gi
          :span="24"
          label="家族病史"
          path="familyMedicalHistory"
          required
          :feedback-style="{
            display: `${model.familyMedicalHistory ? 'none' : 'block'}`,
          }"
        >
          <n-radio-group v-model:value="model.familyMedicalHistory">
            <n-space>
              <n-radio :value="0">无</n-radio>
              <n-radio :value="1">有</n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item-gi>
        <n-gi
          v-if="model.familyMedicalHistory"
          :span="24"
          style="margin-left: 120px; margin-bottom: 12px; margin-top: 12px"
        >
          <ButtonMultiSelect v-model:value="model.selectedFamilyMedicalHistory" :options="medicalHistoryOptions" />
        </n-gi>
        <n-form-item-gi :span="24" label="备孕、妊娠、哺乳" path="isPreparePregnant" required>
          <n-radio-group v-model:value="model.isPreparePregnant">
            <n-space>
              <n-radio :value="0">无</n-radio>
              <n-radio :value="1">有</n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </JModal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { useMessages } from "@/hooks";
import ButtonMultiSelect from "@/components/ButtonMultiSelect/index.vue";
import { addPatients } from "@/services/api";
import moment from "moment";
import { validateIDNumber } from "@/utils/validateUtils";
import { parseIdNumber } from "@/utils/math";
import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
const systemStore = useSystemStoreWithoutSetup()
const { createMessageSuccess, createMessageError } = useMessages();

const props = withDefaults(
  defineProps<{
    show: boolean;
    customerId: string;
  }>(),
  {
    show: false,
    customerId: "",
  },
);
const emits = defineEmits<{
  (e: "update:show", value: boolean): void;
  (e: "refresh"): void;
  (e: "id", value: string): void;
}>();
const allergyOptions = [
  { label: "阿司匹林", value: "阿司匹林" },
  { label: "头孢类", value: "头孢类" },
  { label: "磺胺类", value: "磺胺类" },
  { label: "奶制品", value: "奶制品" },
  { label: "青霉素类", value: "青霉素类" },
  { label: "其它", value: "其它" },
];
const medicalHistoryOptions = [
  { label: "糖尿病", value: "糖尿病" },
  { label: "高血压", value: "高血压" },
  { label: "哮喘", value: "哮喘" },
  { label: "恶性肿瘤", value: "恶性肿瘤" },
  { label: "其它", value: "其它" },
];
const relationOptions = [
  { label: "本人", value: 1 },
  { label: "子女", value: 2 },
  { label: "父母", value: 3 },
  { label: "配偶", value: 4 },
  { label: "其它", value: 5 },
];
const isShow = computed({
  get: () => props.show,
  set: (value: boolean) => {
    emits("update:show", value);
  },
});
const initialData = {
  name: "",
  idNo: null,
  gender: null,
  birthday: null,
  mobile: null,
  relation: null,
  isLiver: 0,
  isKidney: 0,
  allergy: 0,
  medicalHistory: 0,
  familyMedicalHistory: 0,
  isPreparePregnant: 0,
  selectedAllergy: [],
  selectedMedicalHistory: [],
  selectedFamilyMedicalHistory: [],
};
const model = ref({ ...initialData });
/* 表单规则 */
const rules = {
  name: {
    required: true,
    trigger: ["blur", "change"],
    message: "请输入真实姓名",
    validator: () => {
      return model.value.name != "";
    },
  },
  idNo: {
    required: true,
    trigger: ["blur", "change"],
    message: "请输入正确的身份证号",
    validator: () => {
      return validateIDNumber(model.value.idNo);
    },
  },
  gender: {
    required: true,
    trigger: ["blur", "change"],
    message: "请选择性别",
    validator: () => {
      return model.value.gender != null;
    },
  },
  birthday: {
    required: true,
    trigger: ["blur", "change"],
    message: "请选择出生日期",
    validator: () => {
      return model.value.birthday != null;
    },
  },
  mobile: {
    required: true,
    trigger: ["blur", "change"],
    pattern: /^1[3-9]\d{9}$/,
    message: "请输入正确的手机号",
  },
  relation: {
    required: true,
    trigger: ["blur", "change"],
    message: "请选择患者与问诊人关系",
    validator: () => {
      return model.value.relation != null;
    },
  },
};

const isLoading = ref(false);
const getParams = () => {
  const {
    name,
    idNo,
    gender,
    birthday,
    mobile,
    relation,
    isLiver,
    isKidney,
    allergy,
    medicalHistory,
    familyMedicalHistory,
    isPreparePregnant,
    selectedAllergy,
    selectedMedicalHistory,
    selectedFamilyMedicalHistory,
  } = model.value;
  const birthdayStr = birthday ? moment(birthday).format("YYYY-MM-DD") : null;
  const isAllergyHi = allergy == 1 ? (selectedAllergy.length ? selectedAllergy.join(",") : null) : "无";
  const isPersonalMedicalHi =
    medicalHistory == 1 ? (selectedMedicalHistory.length ? selectedMedicalHistory.join(",") : null) : "无";
  const isFamilyMedicalHi =
    familyMedicalHistory == 1
      ? selectedFamilyMedicalHistory.length
        ? selectedFamilyMedicalHistory.join(",")
        : null
      : "无";
  return {
    name,
    idNo,
    gender,
    birthday: birthdayStr,
    mobile,
    relation,
    isLiver,
    isKidney,
    isAllergyHi,
    isPersonalMedicalHi,
    isFamilyMedicalHi,
    isPreparePregnant,
  };
};
// 关闭按钮
const closeModal = () => {
  isShow.value = false;
  model.value = {
    ...initialData,
  };
};
const dateDisabled = (ts: number) => {
  return ts > Date.now();
};
// 确认按钮
const formRef = ref(null);
const changeIdNo = (val: string) => {
  if (validateIDNumber(val)) {
    const { gender, birthday } = parseIdNumber(val);
    model.value.gender = gender;
    model.value.birthday = moment(birthday).valueOf();
  }
};
const _save = () => {
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      const { isAllergyHi, isFamilyMedicalHi, isPersonalMedicalHi } = getParams();
      if (!isAllergyHi) {
        createMessageError(`请选择过敏史`);
        return;
      }
      if (!isPersonalMedicalHi) {
        createMessageError(`请选择个人病史`);
        return;
      }
      if (!isFamilyMedicalHi) {
        createMessageError(`请选择家族病史`);
        return;
      }

      isLoading.value = true;
      try {
        const id = await addPatients({ data: { ...getParams(), customerId: props.customerId, isAddIdNo:systemStore._globalConfig['isAddIdNo']?1:0} });
        if (id) {
          emits("id", id);
        }
        createMessageSuccess("操作成功");
        emits("refresh");
        closeModal();
      } catch (error) {
        createMessageError(`${error}`);
      } finally {
        isLoading.value = false;
      }
    }
  });
};
</script>
