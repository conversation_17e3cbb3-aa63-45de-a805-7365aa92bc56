<template>
  <div class="main-bg checkWrapper">
    <n-spin size="large" />
  </div>
</template>

<script lang="ts" setup>
import { RoutesName } from "@/enums/routes";
import { routesMap } from "@/router/maps";
import { useRouter } from "vue-router";
import { useUserStore } from "@/stores/modules/user";
import { getFirstChildrenRoute, getRoutesMapByConfig } from "@/utils/routerUtils";
import { createCacheStorage } from "@/utils/cache/storageCache";
import { CacheConfig } from "@/utils/cache/config";
import { isDoctorEnv } from "@/utils/envUtils";
const userStore = useUserStore();
const router = useRouter();
const routeConfigStorage = createCacheStorage(CacheConfig.RouteConfig);
const _routeConfigCache = routeConfigStorage.get();
const routes = getRoutesMapByConfig(_routeConfigCache, routesMap);

const tenantRootRoute = routes.find(route=>route.name == RoutesName.Root && route.children && route.children.length)
if (userStore.token && userStore.userInfo) {
  router.replace(getFirstChildrenRoute(tenantRootRoute.children));
} 
else {
  if(isDoctorEnv()){
    router.replace(routesMap[RoutesName.DoctorLogin]);
  }
  else{
    router.replace(routesMap[RoutesName.Login]);
  }
}
</script>

<style scoped lang="less">
@import "@/styles/default.less";
.checkWrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: @blank-background-color;
}
</style>
