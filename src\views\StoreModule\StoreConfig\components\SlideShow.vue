<template>
  <div class="wrapper">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :isNeedCollapse="false"
      :isTableSelection="false"
      :pagination="paginationRef"
      @paginationChange="paginationChange"
      @selectedKeysChange="selectedKeysChange"
      @table-sorter-change="tableSorterChange"
    >
      <!-- 操作项 -->
      <template #tableHeaderBtn>
        <JAddButton v-if="hasOrderStoreconfigCarouselNew" @click="rechargeRecords(false)" type="primary">新增轮播图</JAddButton>
      </template>
    </FormLayout>
    <!-- 新建轮播图 -->
    <GroupSlideShow ref="groupSlideShow" />
  </div>
</template>

<script setup lang="tsx">
import { onMounted, ref } from "vue";
import dayjs from "dayjs";
import { useMessages } from "@/hooks";
import FormLayout from "@/layout/FormLayout.vue";
import TablePreview from '@/components/TablePreview/index.vue';
import { useTableDefault } from "@/hooks/useTableDefault";
import { addCarouseParams, updateCarouseParams, deleteCarouseParams, pageCarouseParams } from "@/services/api";
import GroupSlideShow from "@/views/StoreModule/StoreConfig/components/GroupSlideShow.vue";
import { mallConfiguration } from "@/views/StoreModule/StoreConfig/hooks";
const {positionList} = mallConfiguration()
import {
  hasOrderStoreconfigCarouselNew,
  hasOrderStoreconfigCarouselEdit,
  hasOrderStoreconfigCarouselDelete,
} from "@/views/StoreModule/StoreConfig/hooks/authList";
const { createMessageSuccess, createMessageError } = useMessages();
/* 表格方法Hook */
const {
  isLoading,
  tableData,
  paginationRef,
  pageTableData,
  deleteTableData,
  editTableData,
  addTableData,
  refreshTableData,
  paginationChange,
  sortTableData,
  summaryRef,
} = useTableDefault({
  pageDataRequest: pageCarouseParams,
});

/* 表格项 */
const tableColumns = [
  {
    title: "图片",
    key: "imgPath",
    width: 100,
    align: 'left',
    resizable:true,
    ellipsis: {
      tooltip: true,
    },
    render(rowData: any) {
      return <TablePreview src={rowData.img}></TablePreview>;
    },
  },
  {
    title: "所在位置",
    key: "position",
    align: "left",
    summaryTitle:"",
    width: 100,
    render: rowData => {
      return positionList.value[rowData.position]?.label || ''
    },
  },
  {
    title: "跳转目标",
    key: "redirect",
    align: "left",
    summaryTitle:"",
    width: 100,
    render: rowData => {
      return `${rowData.redirect == '0' ? '无' : '小程序商品详情页'}`
    },
  },
  {
    title: "排序号",
    key: "sortNumber",
    align: "left",
    summaryTitle:"",
    width: 160,
  },
  {
    title: "备注",
    key: "remark",
    align: "left",
    summaryTitle:"",
    width: 100,
  },
  {
    title: "启用",
    key: "isEnable",
    align: "left",
    summaryTitle:"",
    width: 100,
    render: rowData => {
      return <n-tag size='small' bordered={false} type={rowData.isEnable == 0 ? 'success' : 'error'}>
        {rowData.isEnable == 0 ? '是' : '否'}
      </n-tag>
    },
  },
  {
    title: "创建日期",
    key: "createTime",
    align: "left",
    summaryTitle:"",
    width: 100,
    render: rowData => {
      return `${dayjs(rowData.createTime).format('YYYY-MM-DD HH:mm')}`
    }
  },
  {
    title: "操作",
    key: "action",
    width: 120,
    align: "left",
    fixed: "right",
    render: rowData => {
      return (
        <n-space align="center" justify="center">
          <n-button text size="small" v-show={hasOrderStoreconfigCarouselEdit} onClick={() => rechargeRecords(rowData)}
                    type="primary">
            编辑
          </n-button>
          {hasOrderStoreconfigCarouselDelete ? <n-popconfirm
            onPositiveClick={() => {
              DelCarouselImg(rowData.id);
            }}
          >
            {{
              trigger: () => (
                <a style={{ color: "red", cursor: "pointer" }}>删除</a>
              ),
              default: () => <span style={{ width: "300px" }}>是否确定删除该数据？</span>,
            }}
          </n-popconfirm> : null}
          <span v-show={!hasOrderStoreconfigCarouselEdit && !hasOrderStoreconfigCarouselDelete}>-</span>
        </n-space>
      );
    },
  },
];


/** 排序 */
const tableSorterChange = (info: { sort: string, sortAsc: "ascend" | "descend" }) => {
  sortTableData(info.sort, info.sortAsc);
};

/** 选中行数据 */
const rowData = ref([]);
/** 获取选中行Key */
function selectedKeysChange(key, tableData) {
  rowData.value = tableData.map(({ _dummyId, ...rest }) => rest);
}

/* 初始化参数 */
const initParams = {
  condition: '',
  gender: null,
  status: null,
  searchType:'name',
  tagIds: null
};
const model = ref({ ...initParams });

/* 刷新列表 */
const tableSearch = () => {
  pageTableData({}, paginationRef.value);
};
const groupSlideShow = ref()
const rechargeRecords = (rowData:any) =>{
  let row = {
    row:rowData,
    api:rowData?updateCarouseParams:addCarouseParams,
    refreshTable: tableSearch,
  }
  groupSlideShow.value.acceptParams(row)
}

const DelCarouselImg = (id) => {
  try {
    deleteCarouseParams({ id:id })
    createMessageSuccess(`删除轮播图配置成功`);
    setTimeout(tableSearch,500)
  }catch (e) {
    createMessageError(`删除轮播图配置失败： ${e}`);
  }
}
/* 组件挂载 */
onMounted(() => {
  tableSearch()
});
</script>

<style lang="less" scoped>
@import "@/styles/default.less";
@import "@/styles/defaultVar.less";

.wrapper {
  width: 100%;
  height: 100%;
}

</style>
