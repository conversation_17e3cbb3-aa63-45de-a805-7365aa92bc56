<template>
    <div class="inner-page-height">
      <FormLayout
        :isLoading="isLoading"
        :tableData="tableData"
        :tableColumns="tableColumns"
        :pagination="paginationRef"
        :isTableSelection="false"
        @paginationChange="paginationChange"
        id="splitBillSettlement"
      >
        <!-- 表单 -->
        <template #searchForm>
            <n-form
              ref="formRef"
              label-placement="left"
              label-width="auto"
              :show-feedback="false"
              require-mark-placement="right-hanging"
              size="small"
              :style="{ width: '100%' }"
            >
              <n-grid cols="4 m:12 l:18 xl:24" :x-gap="32" responsive="screen">
                <!-- 经销商名称 -->
                <n-gi :span=5>
                 <n-form-item label="经销商名称">
                     <n-input 
                       v-model:value="formValue.dealerName"
                       placeholder="请输入经销商名称"  
                       @keyup.enter.native="formSearch" 
                       style="width:170px" 
                       clearable
                       @blur="formValue.dealerName=($event.target as HTMLInputElement)?.value.trim()"
                       maxlength="30" 
                     />
                 </n-form-item>
                </n-gi>
                <!-- 打款状态 -->
                <n-gi :span="4">
                    <n-form-item label="打款状态">
                        <n-select 
                         v-model:value="formValue.status" 
                         :options="payoutStatusOptions" 
                         placeholder='请选择打款状态'
                         style="width: 170px;" 
                         clearable
                         :render-option="renderOption"
                        />
                    </n-form-item>
                </n-gi>
                <!-- 创建时间 -->
                <n-gi :span="6">
                    <n-form-item label="创建时间">
                        <j-date-range-picker 
                        style="flex: 1;" 
                        v-model:value="formValue.splitTime"  
                        type="datetimerange" format="yyyy-MM-dd" 
                        :default-time="['00:00:00', '23:59:59']" 
                        clearable />
                    </n-form-item>
                </n-gi>
              </n-grid>
            </n-form>
        </template>
        <!-- 操作项 -->
        <template #tableHeaderBtn>
          <n-button @click="refresh" class="store-button" :loading="isLoading">刷 新</n-button>
          <n-button @click="DataExport" :loading="isExportLoading" type="info" class="store-button" v-if="hasFinanceAllocationSettlementExportAuth">导出</n-button>
        </template>
      </FormLayout>
      <SplitBillSettlementDetail ref="SplitBillSettlementDetailShow"/>
    </div>
</template>
  
<script lang="tsx" setup name="splitBillSettlement">
import { ref, onMounted, watch, h, type VNode, reactive } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { allocationSettlementPage ,activateAccountGetDetail, allocationSettlementRetryAllocation, allocationSettlementOfflinePayment, allocationSettlementOfflinePaymentComplete, allocationSettlementExport } from "@/services/api";
import { payoutStatusOptions, payoutStatusLabels, paymentMethodsLabels } from "@/constants";
import { useMessages } from "@/hooks";
import Popconfirm from '@/components/Popconfirm/index.vue'
import moment from "moment";
import { NTooltip, type SelectOption } from 'naive-ui';
import SplitBillSettlementDetail from './components/SplitBillSettlementDetail.vue'
import { hasFinanceAllocationSettlementDetailAuth, hasFinanceAllocationSettlementReSendAuth, hasFinanceAllocationSettlementLocalAuth,hasFinanceAllocationSettlementLocalCompleteAuth,hasFinanceAllocationSettlementExportAuth } from "../../../authList";
/** 定义一个函数来格式化价格 */
const formatPrice = (price) => `${(price / 100).toFixed(2)}`;

const renderOption = ({ node, option }: { node: VNode; option: SelectOption }) =>
        h(NTooltip, null, {
          trigger: () => node,
          default: () => option.label
})

const { createMessageSuccess, createMessageError,createMessageWarning,createMessageExportSuccess } = useMessages();
const formValue = ref({
    dealerName:undefined,
    status:undefined,
    splitTime:null,
})

/** 表格方法Hook */
const {
  isLoading,
  tableData,
  pageTableData,
  paginationRef,
  paginationChange,
} = useTableDefault({
  pageDataRequest: allocationSettlementPage,
});

/* 表格列表项 */
const tableColumns = ref([
    {
        title: "结算单号",
        key: "settlementNo",
        width: 150,
        align: "left"
    },
    {
        title: "经销商姓名",
        key: "dealerName",
        width: 180,
        align: "left"
    },
    {
        title: "结算金额（元）",
        key: "state",
        width: 150,
        align: "left",
        render: (row) => {
            return row.allocationTotalAmount ? formatPrice(row.allocationTotalAmount) : '0.00'
        }
    },
    {
        title: "打款状态",
        key: "status",
        width: 150,
        align: "left",
        render: (row) => {
          return payoutStatusLabels[row.status] ? payoutStatusLabels[row.status] : '-'
        }
    },
    {
        title: "打款渠道",
        key: "payPlatform",
        width: 150,
        align: "left",
        render: (row) => {
          return paymentMethodsLabels[row.payPlatform] ? paymentMethodsLabels[row.payPlatform] : '-'
        }
    },
    {
        title: "创建时间",
        key: "createTime",
        width: 150,
        align: "left"
    },
    {
        title: "操作",
        key: "action",
        width: 100,
        fixed: "right",
        align: "left",
        render: (row) => {
            return (
                <n-space>
                    { (anewAndOfflineButtonShow(row.status) && hasFinanceAllocationSettlementReSendAuth)? 
                      <Popconfirm 
                      onHandleClick={() =>clickReissueLaunch(row)} 
                      loading={isReissueLaunchLoading.value} 
                      buttonContent ={'重新发起'} 
                      type={'info'} 
                      disabled={isReissueLaunchDisabled.value}
                      promptContent={'是否确定重新发起?'} /> : null
                    }

                    {
                      (anewAndOfflineButtonShow(row.status) && hasFinanceAllocationSettlementLocalAuth)? 
                      <Popconfirm 
                      onHandleClick={() =>clickTurnOfflinePayouts(row)} 
                      loading={isTurnOfflinePayoutsLoading.value} 
                      buttonContent ={'转线下打款'} 
                      type={'info'} 
                      disabled={isTurnOfflinePayoutsDisabled.value}
                      promptContent={'是否确定转线下打款?'}/> : null
                    }

                    {
                      (alreadyPayoutsButtonShow(row.status) && hasFinanceAllocationSettlementLocalCompleteAuth)? 
                      <Popconfirm 
                      onHandleClick={() =>clickAlreadyOfflinePayouts(row)} 
                      loading={isAlreadyOfflinePayoutsLoading.value} 
                      buttonContent ={'已线下打款'} 
                      type={'info'} 
                      disabled={isAlreadyOfflinePayoutsDisabled.value}
                      promptContent={'是否确定已线下打款?'}/>  : null
                    }

                    {
                      hasFinanceAllocationSettlementDetailAuth ?
                      <n-button
                        text
                        type="primary"
                        onClick={() => clickDetail(row)}
                    >
                        详情
                    </n-button> : null}
                </n-space>
            );
        },
    },
]);

/** 显示重新发起按钮 和 转线下打款 */
const anewAndOfflineButtonShow = (value) =>{
  if( payoutStatusLabels[value] == '打款失败' ){
    return true
  }
  return false
}
/** 显示已下线打款按钮 */
const alreadyPayoutsButtonShow = (value) =>{
  if( payoutStatusLabels[value] == '待线下打款' ){
    return true
  }
  return false
}

/** 导出 */
const isExportLoading = ref(false)
const DataExport = () =>{
  const _params = {
    data: { ...getParams() },
    pageVO: paginationRef.value
  }
  isExportLoading.value = true
  allocationSettlementExport(_params).then(() => {
    createMessageExportSuccess("导出成功");
  }).catch((err) => {
    createMessageError("导出失败: " + err);
  }).finally(() => {
    isExportLoading.value = false
  });
}

/** 搜索 */
const formSearch = () =>{
    tableSearch();
}

/** 表格刷新 */
function refresh(){
  tableSearch();
}

/** 获取参数 */
const getParams = () => { 
  const { dealerName, status, splitTime} = formValue.value;
  const startTime =  splitTime ? moment(splitTime[0]).format(`YYYY-MM-DD HH:mm:ss`) : null
  const endTime = splitTime ? moment(splitTime[1]).format(`YYYY-MM-DD HH:mm:ss`) : null
  return {
    dealerName,
    status,
    startTime,
    endTime
  };
};

/* 表格搜索 */
const tableSearch = () => {
  pageTableData(getParams(), paginationRef.value);
};

/** 详情 */
const SplitBillSettlementDetailShow = ref()
const clickDetail = (row) =>{
    const params = {
      row:row
    }
    SplitBillSettlementDetailShow.value.acceptParams(params)
}

/** 重新发起 */
const isReissueLaunchLoading = ref(false)
const isReissueLaunchDisabled = ref(false)
const clickReissueLaunch = async(row) =>{
    isReissueLaunchLoading.value = true
    isReissueLaunchDisabled.value = true
  try{
    await allocationSettlementRetryAllocation(row.id)
    createMessageSuccess('重新发起成功')
    tableSearch();
  }catch(err){
    createMessageError('重新发起失败:'+ err)
  }finally{
    isReissueLaunchLoading.value = false
    isReissueLaunchDisabled.value = false
  }
}

/** 转线下打款 */
const isTurnOfflinePayoutsLoading = ref(false)
const isTurnOfflinePayoutsDisabled = ref(false)
const clickTurnOfflinePayouts = async(row) =>{
    isTurnOfflinePayoutsLoading.value = true
    isTurnOfflinePayoutsDisabled.value = true
  try{
    await allocationSettlementOfflinePayment(row.id)
    createMessageSuccess('转线下打款成功')
    tableSearch();
  }catch(err){
    createMessageError('转线下打款失败:'+ err)
  }finally{
    isTurnOfflinePayoutsLoading.value = false
    isTurnOfflinePayoutsDisabled.value = false
  }
}

/** 已线下打款 */
const isAlreadyOfflinePayoutsLoading = ref(false)
const isAlreadyOfflinePayoutsDisabled = ref(false)
const clickAlreadyOfflinePayouts = async(row) =>{
    isAlreadyOfflinePayoutsLoading.value = true
    isAlreadyOfflinePayoutsDisabled.value = true
  try{
    await allocationSettlementOfflinePaymentComplete(row.id)
    createMessageSuccess('已线下打款成功')
    tableSearch();
  }catch(err){
    createMessageError('已线下打款失败:'+ err)
  }finally{
    isAlreadyOfflinePayoutsLoading.value = false
    isAlreadyOfflinePayoutsDisabled.value = false
  }
}

/** 监听 */
watch(() => [
   formValue.value.status,
   formValue.value.splitTime
], (newVal) => {
  if (newVal) {
    tableSearch();
  }
});

/** 组件挂载 */
onMounted(() => {
  tableSearch();
});
</script>

<style scoped lang="less"></style>
  