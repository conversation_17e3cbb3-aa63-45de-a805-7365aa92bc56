// import {createLocalStorage} from "@/utils/cache/storageCache";
import { axiosConfigTransform } from "@/utils/http/Axios/transform";
import type {
  ResponseInterceptor,
  TAxiosInterceptors,
  RequestInterceptor,
  TAxiosConfig,
  ResponseResult,
} from "@/utils/http/Axios/type";
import { downloadFileFromStream } from "@/utils/fileUtils";
import { useMessages } from "@/hooks";
import type { AxiosError } from "axios";
import { isString } from "@/utils/isUtils";
import { useUserStoreWithoutSetup } from "@/stores/modules/user";


function isAxiosError(error): error is AxiosError<ResponseResult> {
  return error.isAxiosError;
}

const requestHeaderConfigHandler: RequestInterceptor = {
  onFulfilled(config) {
    let _configAfterTransform: TAxiosConfig = axiosConfigTransform(config);
    const {requestOptions:{withToken}} = config
    const userStore = useUserStoreWithoutSetup();
    const _token = userStore.token;
    if (_token && !withToken){
      config.headers = Object.assign(config.headers,{
        [import.meta.env.VITE_TOKEN_NAME]:_token,
      });
    }
    return _configAfterTransform;
  },
};

const streamDownloadHandler: ResponseInterceptor = {
  onFulfilled(response) {
    const { headers, data, config: { requestOptions }} = response;
    const AttachmentFlag = "attachment;";
    if (headers["content-disposition"] && requestOptions.responeseType === "stream") {
      if (headers["content-disposition"].indexOf(AttachmentFlag) != -1) {
        let filename: string;
        if (headers.filename) filename = headers.filename;
        else {
          const regResult = /filename=(.*)/.exec(headers["content-disposition"]);
          filename = regResult ? regResult[1] : "未命名文件";
        }
        downloadFileFromStream({
          stream: data,
          filename: decodeURIComponent(filename),
        });
        response.data = {
          code: "200",
          data: "下载成功",
          message: "处理成功"
        }
        return response;
      }
    } else return response;
  },
};

const responseCodeFilterHandler: ResponseInterceptor = {
  onFulfilled(response) {
    const realResponse = response.data;
    switch (realResponse.code) {
      case "200":
        return realResponse.data;
      default:
        return Promise.reject(response);
    }
  },
};

const timeoutHandler: ResponseInterceptor = {
  onRejected(error, options) {
    if(isAxiosError(error)){
      const { code, message } = error;
      if (code === "ECONNABORTED" && message.indexOf("timeout") != -1) {
        error.message = "请求超时，请稍后再试";
      }
    }
    return Promise.reject(error);
  },
};


let _timer = null;
//11003 账号停用
const tokenInvailResponseCode = ["1001", "1002","11003","401"];
const tokenInvailResponseStatus = [401];
const tokenInvaildHandler: ResponseInterceptor = {
  onRejected(error, options) {
    // if(isAxiosError(error) && !error.response){
    //   const { createMessageError } = useMessages();
    //     if (_timer) {
    //       clearTimeout(_timer);
    //       _timer = null;
    //     }
    //     _timer = setTimeout(() => {
    //       _timer = null;
    //       import("@/utils/accountUtils").then(({ afterLogout }) => {
    //         afterLogout();
    //       });
    //     }, 1000);
    //     const msgText = "登录已失效，即将跳转到登录界面";
    //     createMessageError(msgText);
    //     return Promise.reject(msgText);
    // }
    // else{
      const { data:serveResponse,status } = isAxiosError(error)?error.response:error;
      if ((status === 200 && serveResponse?.code && serveResponse?.code == '2002' && serveResponse?.message.indexOf('令牌过期')!=-1)||(status === 200 && serveResponse?.code && tokenInvailResponseCode.includes(serveResponse?.code)) || tokenInvailResponseStatus.includes(status) ) {
        // debugger
        const { createMessageError } = useMessages();
        if (_timer) {
          clearTimeout(_timer);
          _timer = null;
        }
        if(!location.hash.includes('login')){
          _timer = setTimeout(() => {
            _timer = null;
            import("@/utils/accountUtils").then(({ afterLogout }) => {
              afterLogout();
            });
          }, 1000);
        }
        const msgText = serveResponse?.code == '11003'?serveResponse.message:"登录已失效，即将跳转到登录界面";
        if(!location.hash.includes('login')){
          createMessageError(msgText);
        }
        return Promise.reject(msgText);
      } else {
        return Promise.reject(error);
      }
    }

  // },
};

const responseErrorStatusHandler: ResponseInterceptor = {
  onRejected(error, options) {
    if (isAxiosError(error)) {
      return Promise.reject(error.message);
    }
    else{
      const { status: httpStatusCode, data } = error;
      if (httpStatusCode == 502 || httpStatusCode == 500 || data.code == '500' || data.code == '503') {
        return Promise.reject(`服务器异常 [500]`);
      } else {

        return Promise.reject(isString(data.data)? data.data : data.message);
      }
    }
  },
};
export default {
  request: [requestHeaderConfigHandler],
  response: [
    streamDownloadHandler,
    responseCodeFilterHandler,
    timeoutHandler,
    tokenInvaildHandler,
    responseErrorStatusHandler,
  ], // 响应拦截
};
