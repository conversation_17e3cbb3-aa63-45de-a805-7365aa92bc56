import {ref} from 'vue'
import {uploadType}  from "../type"
const tableData = ref([])
const questionIdsOptionsContent = ref([])
const questionIds = ref([])
const getClassifyName = ref(null)
const videoUploading = ref(false)
const optionsData = ref([])
const batchVideoModal = ref(false)
export default function useBatchUploadVideo(){
    function triggerUploadingVideoRelatedContent(file,videoTime=[]){
        const filteredQuestion = questionIdsOptionsContent.value.filter(item => questionIds.value.includes(item.value));
        const data = {
          file:file,
          classify:getClassifyName.value,
         //  videoTime:videoTime.value,
          question:filteredQuestion,
          size:Math.ceil(file.file.size / 1024 / 1024)
        }
        const durationMap = {};
      //   data.videoTime.forEach(item => {
      //          durationMap[item.id] = item.duration;
      //   });
 
        for (const key in durationMap) {
         if (key === data.file.id) {
          data.file.duration = durationMap[key];
           break; // 找到匹配的键值对后跳出循环
         }
        }
       tableData.value.push({...data.file,classify:data.classify,question:data.question,size:data.size,productId:''})
    }
    function updateUploadProgress(ids,uploadProgress){
        // 获取进度值
        tableData.value = tableData.value.map(item => {
           if (item.id === ids) {
              return { ...item, percentage: uploadProgress,uploadStatus:uploadType.goOnUploading};
            }
           else {
              return { ...item };
            }
         });
    }
    
   return {
    triggerUploadingVideoRelatedContent,
    updateUploadProgress,
    tableData,
    questionIdsOptionsContent,
    questionIds,
    getClassifyName,
    videoUploading,
    optionsData,
    batchVideoModal
   };
}
