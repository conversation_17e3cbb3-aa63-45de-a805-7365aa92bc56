<template>
  <JModal
    v-model:show="show"
    :title="title"
    width="680"
    @after-leave="closeModal"
    @after-enter="handleAfterEnter"
    @positive-click="handleSave"
    :positiveButtonProps="{
    loading: isLoading,
    disabled: false
  }"
    :is-scale="false"
  >
    <n-form
      ref="formRef"
      :model="model"
      :rules="rules"
      label-width="80"
      label-placement="left"
      require-mark-placement="right-hanging"
      size="small"
      :style="{
    width: '100%',
  }"
    >
      <n-grid :cols="12" :x-gap="12" responsive="self">
        <!-- 标签名称 -->
        <n-form-item-gi :span="12" label="操作员昵称" path="name">
          <n-input
            v-model:value="model.name"
            @blur="model.name = ($event.target as HTMLInputElement)?.value.trim()"
            placeholder="请输入昵称"
            :maxlength="30"
          />
        </n-form-item-gi>
        <!-- 操作员账号 -->
        <n-form-item-gi :span="12" label="操作员账号" path="account">
          <n-input
            v-model:value="model.account"
            @blur="model.account = ($event.target as HTMLInputElement)?.value.trim()"
            placeholder="请输入账号"
            :maxlength="30"
          />
        </n-form-item-gi>
        <!-- 密码 -->
        <!-- <n-form-item-gi v-if="modalType === 'add'" :span="12" label="密码" path="pwd">
          <n-input
            v-model:value="model.pwd"
            @blur="model.pwd = ($event.target as HTMLInputElement)?.value.trim()"
            placeholder="请输入密码"
            :maxlength="30"
          />
        </n-form-item-gi> -->

        <n-form-item-gi v-if="modalType === 'add'" :span="12" label="密码">
            <n-space vertical style="width: 100%;">
              <n-space>
                <p style="display: flex;align-items: center;">
                  {{model.pwd}}
                  <n-button @click='copyPwd' title='复制' text style="font-size: 15px;margin-left: 4px;" type="info" size="tiny">
                    <n-icon>
                      <CopyOutline />
                    </n-icon>
                  </n-button>
                </p>
                <n-button strong secondary type="info" size="tiny" @click="reCreatePwd">重新生成</n-button>
              </n-space>
             
              <p style="border:1px solid rgba(250, 224, 181, 1);background-color: #fef7ed;color:#f0a020; padding: 0px 6px;border-radius: 3px;margin-top: 4px;">
                用户首次登录会被要求设置新密码
              </p>
            </n-space>
          </n-form-item-gi>
        <!-- 平台角色 -->
        <n-form-item-gi :span="12" label="平台角色" path="roleIds">
          <n-space vertical style="width: 100%;">
            <JRoleSelect v-model:value="model.roleIds" v-model:label="model.roleName" @update:label="handleRoleName" />
              <p v-if="['医生角色', '药师角色'].includes(model.roleName)" style="border:1px solid rgba(250, 224, 181, 1);background-color: #fef7ed;color:#f0a020; padding: 0px 6px;border-radius: 3px;margin-top: 6px;">
                医生和药师账号需在医生药师工作台登录
              </p>
            </n-space>

           
        </n-form-item-gi>
        <!-- 数据权限 1.0.8版本 -->
        <n-form-item-gi :span="12" label="数据权限" path="visitDataType">
          <n-flex style="width: 100%;">
            <n-select v-model:value="model.visitDataType" :options="visitDataOptions" style="width: 82%;" :disabled="['医生角色', '药师角色'].includes(model.roleName)" />
            <JTextButton type="primary" size="small" text @click="openPermissionSpecModel">权限说明</JTextButton>
          </n-flex>
        </n-form-item-gi>
        <!-- 选择经销商 1.0.8版本 -->
        <template v-if="model.visitDataType === VisitDataType.DealerData">
          <n-form-item-gi :span="12" label="选择经销商" path="visitDataType">
            <!-- 编辑回显 -->
            <div class="dealer-wrapper">
              <n-space :size="[6, 6]">
                <n-tag v-for="item in model.dealerList.slice(0, 6)" :key="item.id" @close="handleClose(item.id)" closable>
                  {{ item.name || item.nickname }}
                </n-tag>
                <n-popover v-if="model.dealerList.length > 6" placement="top" trigger="hover" :show-arrow="false">
                  <template #trigger>
                    <n-tag>+{{ model.dealerList.length - 6 }}</n-tag>
                  </template>
                  <div class="dealer-list-wrapper">
                    <n-space :size="[6, 6]">
                      <n-tag v-for="item in model.dealerList.slice(6)" :key="item.id" @close="handleClose(item.id)" closable>
                        {{ item.name || item.nickname }}
                      </n-tag>
                    </n-space>
                  </div>
                </n-popover>
              </n-space>
            </div>
          </n-form-item-gi>
          <!-- 经销商穿梭框 -->
          <n-gi :span="12">
            <JDealerTransfer :dealerIds="model.dealerIds" @update:checked-keys="handleDealersCheckedKeys" />
          </n-gi>
        </template>
        <!-- 选择群管 1.0.8版本 -->
        <template v-if="model.visitDataType === VisitDataType.GroupData">
          <n-form-item-gi :span="12" label="选择群管" path="visitDataType">
            <JDealerSelect v-model:value="model.dealerIds" style="width: 100%;" isMultiple clearable />
          </n-form-item-gi>
          <!-- 群管穿梭框 -->
          <n-gi :span="12">
            <JTubeTransfer v-model:gmIds="model.gmIds" :dealerIds="model.dealerIds" :checkGmOptions="model.checkGmOptions" />
          </n-gi>
        </template>

         <!-- 分销员管理 -->
         <template v-if="model.visitDataType === VisitDataType.DistributorsData">
          <n-form-item-gi :span="12" label="选择分销员" path="visitDataType">
            <!-- 编辑回显 -->
            <div class="dealer-wrapper">
              <n-space :size="[6, 6]">
                <n-tag v-for="item in model.distributorList.slice(0, 6)" :key="item.id" @close="handleClose(item.id)" closable>
                  {{ item.name || item.nickname }}
                </n-tag>
                <n-popover v-if="model.distributorList.length > 6" placement="top" trigger="hover" :show-arrow="false">
                  <template #trigger>
                    <n-tag>+{{ model.distributorList.length - 6 }}</n-tag>
                  </template>
                  <div class="dealer-list-wrapper">
                    <n-space :size="[6, 6]">
                      <n-tag v-for="item in model.distributorList.slice(6)" :key="item.id" @close="handleClose(item.id)" closable>
                        {{ item.name || item.nickname }}
                      </n-tag>
                    </n-space>
                  </div>
                </n-popover>
              </n-space>
            </div>
          </n-form-item-gi>
           <n-gi :span="12">
             <JDistributorsTransfer :distributors="model.distributors"   @update:checked-keys="handleDistributorsCheckedKeys"/>
           </n-gi>
         </template>

         <!-- 医生管理 -->
         <template v-if="model.visitDataType === VisitDataType.DoctorData">
          <n-form-item-gi :span="12" label="选择医生" path="visitDataType">
            <!-- 编辑回显 -->
            <div class="dealer-wrapper">
              <n-space :size="[6, 6]">
                <n-tag v-for="item in model.doctorList.slice(0, 6)" :key="item.id" @close="handleClose(item.id)" closable>
                  {{ item.doctorName }}
                </n-tag>
                <n-popover v-if="model.doctorList.length > 6" placement="top" trigger="hover" :show-arrow="false">
                  <template #trigger>
                    <n-tag>+{{ model.doctorList.length - 6 }}</n-tag>
                  </template>
                  <div class="dealer-list-wrapper">
                    <n-space :size="[6, 6]">
                      <n-tag v-for="item in model.doctorList.slice(6)" :key="item.id" @close="handleClose(item.id)" closable>
                        {{ item.doctorName }}
                      </n-tag>
                    </n-space>
                  </div>
                </n-popover>
              </n-space>
            </div>
          </n-form-item-gi>
           <n-gi :span="12">
             <JDoctorSelect :doctorId="model.doctorId" @update:checked-keys="handleDoctorCheckedKeys"/>
           </n-gi>
         </template>

         <!-- 供应商管理 -->
         <template v-if="model.visitDataType === VisitDataType.DoctorData">
          <n-form-item-gi :span="12" label="选择供应商" path="visitDataType">
            <!-- 编辑回显 -->
            <div class="dealer-wrapper">
              <n-space :size="[6, 6]">
                <n-tag v-for="item in model.supplierList.slice(0, 6)" :key="item.id" @close="handleClose(item.id)" closable>
                  {{ item.doctorName }}
                </n-tag>
                <n-popover v-if="model.supplierList.length > 6" placement="top" trigger="hover" :show-arrow="false">
                  <template #trigger>
                    <n-tag>+{{ model.supplierList.length - 6 }}</n-tag>
                  </template>
                  <div class="dealer-list-wrapper">
                    <n-space :size="[6, 6]">
                      <n-tag v-for="item in model.supplierList.slice(6)" :key="item.id" @close="handleClose(item.id)" closable>
                        {{ item.doctorName }}
                      </n-tag>
                    </n-space>
                  </div>
                </n-popover>
              </n-space>
            </div>
          </n-form-item-gi>
           <n-gi :span="12">
             <JSupplierSelect :supplierId="model.supplierId" @update:checked-keys="handleDoctorCheckedKeys"/>
           </n-gi>
         </template>

         <!-- 药师管理 -->
          <template v-if="model.roleName === '药师角色'">
            <n-form-item-gi :span="12" label="选择药师" path="visitDataType">
              <!-- 编辑回显 -->
              <div class="dealer-wrapper">
                <n-space :size="[6, 6]">
                  <n-tag
                    v-for="item in model.pharmacistList.slice(0, 6)"
                    :key="item.id"
                    @close="handleClose(item.id)"
                    closable
                  >
                    {{ item.pharmacistName }}
                  </n-tag>
                  <n-popover v-if="model.pharmacistList.length > 6" placement="top" trigger="hover" :show-arrow="false">
                    <template #trigger>
                      <n-tag>+{{ model.pharmacistList.length - 6 }}</n-tag>
                    </template>
                    <div class="dealer-list-wrapper">
                      <n-space :size="[6, 6]">
                        <n-tag
                          v-for="item in model.pharmacistList.slice(6)"
                          :key="item.id"
                          @close="handleClose(item.id)"
                          closable
                        >
                          {{ item.pharmacistName }}
                        </n-tag>
                      </n-space>
                    </div>
                  </n-popover>
                </n-space>
              </div>
            </n-form-item-gi>
            <n-gi :span="12">
              <JPharmacistSelect :pharmacistId="model.pharmacistId" @update:checked-keys="handlePharmacistCheckedKeys" />
            </n-gi>
          </template>
         <!-- 敏感数据 -->
         <n-form-item-gi :span="12" label="敏感数据" path="isShowSensitive">
             <n-radio-group  :disabled="systemStore._globalConfig['authRoleType'] != AuthRoleType.superManagement && modalType != 'add'" v-model:value="model.isShowSensitive">
               <n-space>
                 <n-radio label="不可查看" :value="SensitiveType.notViewable" />
                 <n-radio label="可查看" :value="SensitiveType.viewable" />
               </n-space>
            </n-radio-group>
            <n-tooltip trigger="hover">
              <template #trigger>
                  <svg style="width: 20px;height: 20px; cursor: pointer;" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M10 2a8 8 0 1 1 0 16a8 8 0 0 1 0-16zm0 11.5a.75.75 0 1 0 0 1.5a.75.75 0 0 0 0-1.5zm0-8A2.5 2.5 0 0 0 7.5 8a.5.5 0 0 0 1 0a1.5 1.5 0 1 1 2.632.984l-.106.11l-.118.1l-.247.185a3.11 3.11 0 0 0-.356.323C9.793 10.248 9.5 10.988 9.5 12a.5.5 0 0 0 1 0c0-.758.196-1.254.535-1.614l.075-.076l.08-.073l.088-.072l.219-.163l.154-.125A2.5 2.5 0 0 0 10 5.5z" fill="currentColor"></path></g></svg>
              </template>
              <div>
                <ul>
                  备注：
                  <li>1、可设置全平台手机号对该账号是否可见</li>
                  <li>2、修改此项配置请联系超级管理员操作</li>
                </ul>
              </div>
            </n-tooltip>
            
         </n-form-item-gi>
         
      </n-grid>
    </n-form>
    <!-- 权限说明 -->
    <JPermissionSpec ref="permissionSpecRef" />
  </JModal>
</template>

<script setup lang="ts" name="OperatorModel">
import { ref, computed } from "vue";
import { useMessages, useBoolean, useLoading } from '@/hooks';
import type { FormRules } from 'naive-ui';
import { addPlatformOperator, updatePlatformOperator, getSgDealerInfo, getSgGmInfo, distributorPageForUserModule, doctorEntityGet, pharmacistEntityGet } from "@/services/api";
import { isObject, deepClone, isArray } from "@/utils";
import { VisitDataType, VisitDataMap, SensitiveType } from "@/enums";
/** 相关组件 */
import JModal from "@/components/JModal/index.vue";
import JDealerTransfer from "./JDealerTransfer.vue";
import JTubeTransfer from "./JTubeTransfer.vue";
import JPermissionSpec from "./JPermissionSpec.vue";
import JDistributorsTransfer from './JDistributorsTransfer.vue'
import JDoctorSelect from './JDoctorSelect.vue'
import JSupplierSelect from './JSupplierSelect.vue'
import JPharmacistSelect from "./JPharmacistSelect.vue"
import { getRandomPwd } from "@/components/PwdInput/pwdUtils";
import { copyText } from "@/utils/clipboardUtils";
import { CopyOutline } from '@vicons/ionicons5';
import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
import { AuthRoleType } from "@/enums";

const systemStore = useSystemStoreWithoutSetup();

/** props */
const props = defineProps<{
  refresh: () => void;
}>();

type ModalType = 'add' | 'edit';
const { createMessageSuccess, createMessageError } = useMessages();

/** 数据权限 */
const visitDataOptions = computed(() => {
  return Object.entries(VisitDataMap).map(([label, value]) => ({
    label,
    value
  }));
});

/** 标题 */
const title = computed(() => {
  const titleMap: Record<ModalType, string> = {
    add: '新增平台操作员',
    edit: '编辑',
  };
  return titleMap[modalType.value];
});

/** 显隐 */
const { bool: show, setTrue, setFalse } = useBoolean();
const modalType = ref<ModalType>('add');

/* 接收父组件传过来的参数 */
const acceptParams = async (params: {
  type: ModalType,
  row
}) => {
  modalType.value = params.type;
  let row = params.row;
  // 处理行数据
  if (isObject(row) && Object.keys(row).length !== 0) {
    const { id, name, account, pwd, roleIds, visitDataType, thirdIdList, isShowSensitive, roleNames:roleName, systemUserId, type } = row;
    Object.assign(model.value, {
      id,
      name,
      account,
      pwd,
      roleIds,
      visitDataType,
      thirdIdList,
      isShowSensitive,
      roleName,
      doctorId: type === 1 ? systemUserId : null,
      pharmacistId: type === 2 ? systemUserId : null
    });
  }
  setTrue();
};

/** 弹窗出现后回调 */
async function handleAfterEnter() {
  const { visitDataType, thirdIdList, roleName, doctorId, pharmacistId } = model.value;
  // 编辑
  if (modalType.value === 'edit') {
    // 经销商回显
    if (visitDataType === VisitDataType.DealerData) await handleDealerData(thirdIdList);
    // 群管回显
    if (visitDataType === VisitDataType.GroupData) await handleGroupData(thirdIdList);
    // 分销员回显
    if (visitDataType === VisitDataType.DistributorsData) await handleDistributorsData(thirdIdList);
    // 医生回显
    if (visitDataType === VisitDataType.DoctorData) await handleDoctorData(doctorId);
     // 药师回显
    if (roleName === '药师角色') await handlePharmacistData(pharmacistId);
  }
}

/** 经销商回显 */
async function handleDealerData(thirdIdList: Array<number | string>) {
  try {
    const { records } = await getSgDealerInfo({
      data: {
        dealerIdList: thirdIdList,
      },
      pageVO: {
        current: 1,
        size: 500,
      },
    });
    if (isArray(records) && records.length > 0) {
      model.value.dealerList = records;
      model.value.dealerIds = records.map((item) => item.id);
    }
  } catch (error) {
    createMessageError('编辑回显经销商失败' + error);
  }
}

/** 群管回显 */
async function handleGroupData(thirdIdList: Array<number | string>) {
  try {
    const { records } = await getSgGmInfo({
      data: {
        groupMgrIdList: thirdIdList,
      },
      pageVO: {
        current: 1,
        size: 500,
      },
    });
    if (isArray(records) && records.length > 0) {
      model.value.checkGmOptions = records;
      model.value.gmIds = records.map((item) => item.id);
    }
  } catch (error) {
    createMessageError('编辑群管回显失败' + error);
  }
}

/** 分销原回显 */

async function handleDistributorsData(thirdIdList: Array<number | string>) {
  try {
    const { records } = await distributorPageForUserModule({
      data: {
        idList: thirdIdList,
      },
      pageVO: {
        current: 1,
        size: 500,
      },
    });
    if (isArray(records) && records.length > 0) {
      model.value.distributorList = records;
      model.value.distributors = records.map((item) => item.id);
    }
  } catch (error) {
    createMessageError('编辑分销员回显失败' + error);
  }
}
/** 医生回显 */
async function handleDoctorData(id: string) {
  try {
    const res = await doctorEntityGet(id);
    model.value.doctorList = [res];
    model.value.doctorId = res?.id;
  } catch (error) {
    createMessageError('编辑医生回显失败' + error);
  }
}
/** 药师回显 */
async function handlePharmacistData(id: string) {
  try {
    const res = await pharmacistEntityGet(id);
    model.value.pharmacistList = [res];
    model.value.pharmacistId = res?.id;
  } catch (error) {
    createMessageError("编辑药师回显失败" + error);
  }
}
/* 表单参数初始化 */
const initParams = {
  id: null,
  name: null,
  account: null,
  pwd: getRandomPwd(),
  roleIds: null,
  roleName:null,
  visitDataType: VisitDataType.AllPlatformData,
  thirdIdList: [],
  dealerList: [],
  dealerIds: [],
  doctorId: null,
  pharmacistId: null,
  gmIds: [],
  checkGmOptions: null,
  distributorList: [],
  doctorList: [],
  pharmacistList: [],
  distributors:[],
  isShowSensitive:SensitiveType.notViewable,
  supplierId: null,
  /** 供应商列表 */
  supplierList: [],

};
const model = ref(deepClone(initParams));

/* 表单实例 */
const formRef = ref();

/* 表单规则 */
const rules: FormRules = {
  visitDataType: {
    type: "number",
    required: true,
    trigger: ["blur", "input"],
    message: "请选择数据权限",
  },
  name: {
    type: "string",
    required: true,
    trigger: ["blur", "input"],
    message: "请输入昵称",
  },
  account: {
    type: "string",
    required: true,
    trigger: ["blur", "change"],
    message: "请输入账号",
  },
  pwd: {
    type: "string",
    required: true,
    trigger: ["blur", "change"],
    message: "请输入密码",
  },
  roleIds: {
    type: "string",
    required: true,
    trigger: ["blur", "change"],
    message: "请选择角色",
  },
  isShowSensitive:{
    type: "number",
    required: true,
    trigger: ["blur", "input"],
    message: "请输入敏感数据",
  }
};

/* 清空表单 */
const formDataReset = () => {
  model.value = deepClone(initParams);
};

/* 关闭弹窗之后 */
const closeModal = () => {
  formDataReset();
};

/** 经销商值更新回调 */
// function handleCheckedKeys(
//   meta: { 
//     action: 'check' | 'uncheck'  | 'checkAll' | 'uncheckAll', 
//     currentId: string | number | Array<string | number>, 
//     currentItem: Object | Array<Object>
// }) {
//   // console.log("经销商值更新回调", meta);

//   const { action, currentId, currentItem } = meta;

//   if (action === 'check') {
//     // 如果是 check，添加 currentItem 至 dealerList
//     model.value.dealerList.push(currentItem);
//     model.value.dealerIds.push(currentId);
//   } else if (action === 'uncheck') {
//     // 如果是 uncheck，移除 dealerList 中对应的 currentId 项
//     model.value.dealerList = model.value.dealerList.filter(dealer => dealer.id !== currentId);
//     model.value.dealerIds = model.value.dealerIds.filter(id => id !== currentId);
//   }

//   // 全选或全不选
//   if (isArray(currentItem)) {
//     const dealerIdsSet = new Set(model.value.dealerIds);
  
//     if (action === 'checkAll') {
//       currentItem.forEach(item => {
//         if (!dealerIdsSet.has(item.id)) {
//           model.value.dealerList.push(item);
//           model.value.dealerIds.push(item.id);
//         }
//       });
//     } else if (action === 'uncheckAll') {
//       const currentItemIds = new Set(currentItem.map(item => item.id));
//       model.value.dealerList = model.value.dealerList.filter(dealer => !currentItemIds.has(dealer.id));
//       model.value.dealerIds = model.value.dealerIds.filter(id => !currentItemIds.has(id));
//     }
//   }
// };

function handleCheckedKeys({ action, currentId, currentItem }, listKey, idsKey) {
  // 从 model 中获取对应的列表和 ID 数组
  const list = model.value[listKey];
  const ids = model.value[idsKey];

  if (action === 'check') {
    // 将 currentItem 添加到列表中，并将 currentId 添加到 ID 数组中
    list.push(currentItem);
    ids.push(currentId);
  } else if (action === 'uncheck') {
    // 从列表中移除对应 currentId 的项，并从 ID 数组中移除该 ID
    model.value[listKey] = list.filter(item => item.id !== currentId);
    model.value[idsKey] = ids.filter(id => id !== currentId);
  }

  if (Array.isArray(currentItem)) {
    const idsSet = new Set(ids);

    if (action === 'checkAll') {
      // 如果是全选操作，将所有未包含在 ID 集合中的项添加到列表和 ID 数组中
      currentItem.forEach(item => {
        if (!idsSet.has(item.id)) {
          list.push(item);
          ids.push(item.id);
        }
      });
    } else if (action === 'uncheckAll') {
      // 如果是全不选操作，从列表和 ID 数组中移除所有在 currentItem 中的项
      const currentItemIds = new Set(currentItem.map(item => item.id));
      model.value[listKey] = list.filter(item => !currentItemIds.has(item.id));
      model.value[idsKey] = ids.filter(id => !currentItemIds.has(id));
    }
  }
}

// 处理经销商选中状态变化的回调
function handleDealersCheckedKeys(meta) {
  handleCheckedKeys(meta, 'dealerList', 'dealerIds');
}

// 处理分销员选中状态变化的回调
function handleDistributorsCheckedKeys(meta) {
  handleCheckedKeys(meta, 'distributorList', 'distributors');
}
// 处理医生选中状态变化的回调
function handleDoctorCheckedKeys(meta) {
  if(meta.action === 'check') {
    model.value.doctorList[0] = meta.currentItem;
    model.value.doctorId = meta.currentId
  } else {
    model.value.doctorList = []
    model.value.doctorId = ""
  }
}

// 处理药师选中状态变化的回调
function handlePharmacistCheckedKeys(meta) {
  if (meta.action === "check") {
    model.value.pharmacistList[0] = meta.currentItem;
    model.value.pharmacistId = meta.currentId;
  } else {
    model.value.pharmacistList = [];
    model.value.pharmacistId = "";
  }
}


/** 删除经销商 */
const handleClose = (id: string) =>{
  model.value.dealerList = model.value.dealerList.filter(dealer => dealer.id !== id);
  model.value.dealerIds = model.value.dealerIds.filter(dealerId => dealerId !== id);
  model.value.distributorList = model.value.distributorList.filter(distributor => distributor.id !== id);
  model.value.distributors = model.value.distributors.filter(distributorsId => distributorsId !== id);
  model.value.doctorList = [];
  model.value.doctorId = null;
  model.value.supplierList = [];
  model.value.pharmacistList = [];
  model.value.pharmacistId = null;
}

/** 打开权限说明 */
const permissionSpecRef = ref<InstanceType<typeof JPermissionSpec> | null>(null);
const openPermissionSpecModel = () => {
  permissionSpecRef.value?.acceptParams();
};

/** 获取参数 */
const _getParams = () => {
  const { name, account, pwd, roleIds, visitDataType, dealerIds, gmIds, distributors, isShowSensitive, doctorId, roleName, pharmacistId} = model.value
  const type = roleName === '医生角色' || roleName === '药师角色' ? (roleName === '医生角色' ? 1 : 2) : undefined;
  const systemUserId = type ? (type === 1 ? doctorId : pharmacistId) : undefined;
  return {
    name,
    account,
    pwd,
    roleIds,
    visitDataType,
    thirdIdList:
      visitDataType === VisitDataType.DealerData
        ? dealerIds
        : visitDataType === VisitDataType.GroupData
        ? gmIds
        : visitDataType === VisitDataType.DistributorsData
        ? distributors
        : visitDataType === VisitDataType.DoctorData && doctorId
        ? [doctorId] : roleName === '药师角色' && pharmacistId ? [pharmacistId]
        : [],
    type,     
    systemUserId,
    isShowSensitive
  }
};

/**
 * @description 表单校验经销商、群管函数
 */
 function validateForm(formData) {
  let errors = "";
  // 校验经销商
  if (formData?.visitDataType === VisitDataType.DealerData && !formData?.thirdIdList?.length) {
    errors = "请选择经销商!";
  }
  // 校验群管
  if (formData?.visitDataType === VisitDataType.GroupData && !formData?.thirdIdList?.length) {
    errors = "请选择群管!";
  }
  // 校验群管
  if (formData?.visitDataType === VisitDataType.DistributorsData && !formData?.thirdIdList?.length) {
    errors = "请选择分销员!";
  }
  // 校验医生
  if (formData?.visitDataType === VisitDataType.DoctorData && formData.type === 1 && !formData?.systemUserId) {
    errors = "请选择医生!";
  }
  // 校验药师
  if (model.value.roleName === '药师角色' && formData.type === 2 && !formData?.systemUserId) {
    errors = "请选择药师!";
  }
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

/* 确认--保存 */
const { loading: isLoading, startLoading, endLoading } = useLoading();
const handleSave = (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      try {
        startLoading();
        let _operatorParams = _getParams();

        // 校验
        const { isValid, errors } = validateForm(_operatorParams);
        if (!isValid) {
          // console.log("表单校验失败:", errors);
          createMessageError(errors);
          return;
        }

        // 新增
        if (modalType.value === 'add') {
          await addPlatformOperator(_operatorParams);
          createMessageSuccess("新增操作员成功！");
        }

        // 更新
        if (modalType.value === 'edit') {
          let _params = {
            id: model.value.id,
            ..._operatorParams
          };
          await updatePlatformOperator(_params);
          createMessageSuccess("编辑操作员成功！");
        }

        // 刷新
        props.refresh && props.refresh();
        setFalse();
      } catch (error) {
        createMessageError(`操作失败: ${error}`);
      } finally {
        endLoading();
      }
    }
  });
};

/** 复制密码 */
const copyPwd = async() =>{
  try{
    await copyText(model.value.pwd)
    createMessageSuccess('复制密码成功')
  }
  catch(e){
    createMessageError(`复制密码失败${e}`)
  }
}

/** 重新生成密码 */
const reCreatePwd = () => {
  model.value.pwd = getRandomPwd()
}
const handleRoleName = (val) => {
  if(val === '医生角色') {
    model.value.visitDataType = VisitDataType.DoctorData
  } else if(val === '药师角色') {
    model.value.visitDataType = VisitDataType.AllPlatformData
  }
}
defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less">
@import "@/styles/scrollbar.less";
.dealer-list-wrapper {
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  .scrollbar();
}
</style>
