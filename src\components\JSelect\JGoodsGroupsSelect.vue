<template>
    <!-- 商品树形下拉选择 -->
    <n-tree-select
        v-bind="$attrs"
        :loading="isGetLoading"
        :options="treeData"
        :default-expanded-keys="[String(GoodsCategoryType.DRUG), String(GoodsCategoryType.THERAPY), String(GoodsCategoryType.GENERAL)]"
        :onFocus="handlerFocus"
        placeholder="请选择商品分类"
        filterable
        :clearable="false"
        block-line
    />
</template>

<script lang="ts" setup name='JGoodsGroupsSelect'>
import { useGetGoodsClassify } from "@/hooks/business";
import { GoodsCategoryType } from "@/enums";

const { isGetLoading, getGoodsClassificationData, treeData } = useGetGoodsClassify();

/** Focus 时的回调 */
const handlerFocus = () => {
	if (treeData.value.some(item => item.children?.length === 0)) {
        getGoodsClassificationData();
    }
};
</script>


<style lang="less" scoped>

</style>