import { defHttp } from "@/services";

/** 系统配置 */
export const enum GlobalConfigsApi {
  updateSysConfig = "/globalConfigs/updateSysConfig",
  getPointEnable = "/globalConfigs/getPointEnable",
}

/**
 * @description 查询是否启用系统参数
 */
export function getSystemPointEnable() {
  return defHttp.get({
      url: GlobalConfigsApi.getPointEnable,
  });
}

/**
 * @description 更新系统参数
 * @param params { id: string, value: string }
 */
export function updateSystemConfig(params) {
  return defHttp.post({
    url: GlobalConfigsApi.updateSysConfig,
    params
  })
}