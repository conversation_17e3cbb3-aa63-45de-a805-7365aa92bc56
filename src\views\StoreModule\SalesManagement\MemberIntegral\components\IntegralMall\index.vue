<template>
    <div class="integral-mall-wrapper" id="integral-mall">
        <n-scrollbar style="height: 100%;" :content-style="{ padding: '12px' }">
            <!-- 返回会员积分介绍页 -->
            <div :bordered="false" size="small" class="goback" @click="props.toggleActive">
                <n-icon size="20">
                    <ArrowUndoSharp />
                </n-icon>
            </div>
            <!-- 积分商城启用 -->
            <n-card v-if="hasPointsMallEnableAuth" title="积分商城启用" :bordered="false" size="small" class="config">
                <p style="margin-bottom: 12px;">功能启用后，小程序端才会展示积分相关功能，建议配置完积分任务、积分可兑换商品、积分规则后再启用。</p>
                <JCheckbox v-model:checked="model.isUse" @update:checked="handleChecked">启用</JCheckbox>
            </n-card>
            <!-- 获取积分配置 -->
            <n-card v-if="false" title="获取积分配置" :bordered="false" size="small" class="config">
                <p style="margin-bottom: 12px;">未勾选启用，小程序端将不显示该行内容，点击配置，可查看或更改部分内容。</p>
                <CogfigTable @clickCofig="handleClickCofig" />
            </n-card>
            <!-- 积分兑换商品配置 -->
            <n-card v-if="hasPointsProductConfigAuth" title="积分兑换商品配置" :bordered="false" size="small" class="config">
                <div class="tip" style="left: 156px;">
                    <n-icon size="18">
                        <AlertCircleOutline />
                    </n-icon>
                    <span style="margin-left: 4px;">积分兑换商品默认包邮，录入积分请将邮费一并考虑</span>
                </div>
                <n-button type="primary" @click="openIntegralGoodsConfig">配置商品</n-button>   
            </n-card>
            <!-- 积分规则配置 -->
            <n-card v-if="hasPointsRuleconfigAuth" title="积分规则配置" :bordered="false" size="small" class="config">
                <n-button type="primary" @click="openIntegrationRuleConfig" class="store-button">配 置</n-button>  
            </n-card>
            <!-- 会员等级配置 -->
            <n-card v-if="hasPointsLevelconfigAuth" title="会员等级配置" :bordered="false" size="small" class="config">
                <n-button type="primary" @click="openMembershipConfig" class="store-button">配 置</n-button>  
            </n-card>
            <!-- 更多配置 -->
<!--          v-if="hasMoreConfigurationsAuth"-->
            <n-card  title="更多配置" :bordered="false" size="small" class="config">
              <n-button type="primary" @click="openMoreConfigurationsRef" class="store-button">配 置</n-button>
            </n-card>
        </n-scrollbar>
        <!-- 每日来访配置 -->
        <!-- <DailyVisitConfig ref="dailyVisitConfigRef" /> -->
        <!-- 查看商品配置 -->
        <!-- <ViewProductConfig ref="viewProductConfigRef" /> -->
        <!-- 签到配置 -->
        <!-- <SignInConfig ref="signInConfigRef" /> -->
        <!-- 积分兑换商品配置 -->
        <IntegralGoodsConfig ref="integralGoodsConfigRef" />
        <!-- 积分规则配置 -->
        <IntegrationRuleConfig ref="integrationRuleConfigRef" />
        <!-- 会员等级配置 -->
        <MembershipConfig ref="membershipConfigRef" />
        <!-- 会员等级配置 -->
        <MoreConfigurations ref="MoreConfigurationsRef" />
    </div>
</template>

<script lang="ts" setup name='IntegralMall'>
import { ref } from "vue";
import { ArrowUndoSharp, AlertCircleOutline } from "@vicons/ionicons5";
import type { PointCofigType } from "../../types";
// import { PointCofigEnum } from "../../types";
/** 相关组件 */
import CogfigTable from "./components/CogfigTable.vue";
// import DailyVisitConfig from "./components/DailyVisitConfig.vue";
// import ViewProductConfig from "./components/ViewProductConfig.vue";
// import SignInConfig from "./components/SignInConfig.vue";
import MembershipConfig from "./components/MembershipConfig.vue";
import IntegrationRuleConfig from "./components/IntegrationRuleConfig.vue";
import IntegralGoodsConfig from "./components/IntegralGoodsConfig/index.vue";
import MoreConfigurations from "./components/MoreConfigurations.vue";
import { 
    hasPointsLevelconfigAuth, 
    hasPointsRuleconfigAuth, 
    hasPointsMallEnableAuth, 
    hasPointsProductConfigAuth,
    hasMoreConfigurationsAuth
} from "../../../authList";

/** props */
const props = defineProps<{
    isPointEnable: boolean; // 是否启用积分商城
    toggleActive: () => void; 
}>();

/** emits */
const emits = defineEmits<{
    (e: 'update:checked', value: boolean): void;
}>();

/* 参数初始化 */
const initParams = {
	isUse: props.isPointEnable ?? false,
};
const model = ref({ ...initParams });

/** 切换积分商城使用 */
function handleChecked(checked: boolean) {
    emits('update:checked', checked);
}

/** 点击配置 */
function handleClickCofig(type: PointCofigType) {
    console.log("type", type);
    // 每日来访配置
    // if(type === PointCofigEnum.DAILYVISIT) {
    //     openDailyVisitConfig();
    // }
    // // 查看商品配置
    // if(type === PointCofigEnum.VIEWPRODUCT) {
    //     openViewProductConfig();
    // }
    // // 签到配置
    // if(type === PointCofigEnum.SIGNIN) {
    //     openSignInConfig();
    // }
}

/** 打开每日来访配置 */
// const dailyVisitConfigRef = ref<InstanceType<typeof DailyVisitConfig> | null>(null);
// const openDailyVisitConfig = () => {
// 	dailyVisitConfigRef.value?.acceptParams();
// };

/** 打开查看商品配置 */
// const viewProductConfigRef = ref<InstanceType<typeof ViewProductConfig> | null>(null);
// const openViewProductConfig = () => {
// 	viewProductConfigRef.value?.acceptParams();
// };

/** 打开签到配置 */
// const signInConfigRef = ref<InstanceType<typeof SignInConfig> | null>(null);
// const openSignInConfig = () => {
// 	signInConfigRef.value?.acceptParams();
// };

/** 打开积分兑换商品配置 */
const integralGoodsConfigRef = ref<InstanceType<typeof IntegralGoodsConfig> | null>(null);
const openIntegralGoodsConfig = () => {
	integralGoodsConfigRef.value?.acceptParams();
};

/** 打开积分规则配置 */
const integrationRuleConfigRef = ref<InstanceType<typeof IntegrationRuleConfig> | null>(null);
const openIntegrationRuleConfig = () => {
	integrationRuleConfigRef.value?.acceptParams();
};

/** 打开会员等级配置 */
const membershipConfigRef = ref<InstanceType<typeof MembershipConfig> | null>(null);
const openMembershipConfig = () => {
	membershipConfigRef.value?.acceptParams();
};
/** 打开会员更多配置 */
const MoreConfigurationsRef = ref<InstanceType<typeof MoreConfigurations> | null>(null);
const openMoreConfigurationsRef = () => {
  MoreConfigurationsRef.value?.acceptParams();
};
</script>


<style lang="less" scoped>
.integral-mall-wrapper {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    background-color: #fff;
    .goback {
        width: 24px;
        padding: 6px 6px 6px 0;
        margin-bottom: 6px;
        cursor: pointer;
        &:hover {
            transform: scale(1.3);
        }
    }
    .config {
        position: relative;
        margin-bottom: 12px;
        box-shadow: rgba(17, 17, 26, 0.05) 0px 1px 0px, rgba(17, 17, 26, 0.1) 0px 0px 8px;
    }
    .tip {
        position: absolute;
        top: 14px;
        display: flex;
        align-items: center;
    }
}
</style>