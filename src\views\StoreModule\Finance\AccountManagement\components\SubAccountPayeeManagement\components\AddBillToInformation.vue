<template>
    <JModal
      v-model:show="show"
      width="800"
      title="入账方信息录入"
      @after-leave="closeModal"
          @positive-click="_save"
          :positiveButtonProps="{
          loading: isLoading
      }"
    >
      <n-form
        ref="formRef"
        :rules="rules"
        :model="model"
        label-width="140"
        label-placement="left"
        require-mark-placement="right-hanging"
        :style="{
            width: '100%',
            
          }"
      >
        <n-grid :cols="8" :x-gap="8" responsive="self">
          <n-form-item-gi :span="8" label="支付渠道" path="payPlatform">
             <n-select 
             v-model:value="model.payPlatform" 
             :options="paymentMethodsOptions.filter(item => item.label !== '微信支付')"
             placeholder="请选择支付渠道" :disabled="isEdit()"/>
          </n-form-item-gi>

          <n-form-item-gi :span="8" label="归属商户" path="merchantId"> 
            <JMerchantNumber v-model:value="model.merchantId" :disabled="isEdit()" :payPlatform="model.payPlatform"/>
          </n-form-item-gi>

          <n-form-item-gi :span="8" label="开户类型" path="cleanType">
             <n-select 
               v-model:value="model.cleanType" 
               :options="accountOpeningOptions" 
               placeholder="请选择开户类型"
               :disabled="isEdit()"/>
          </n-form-item-gi>

          <n-form-item-gi :span="8" label="开户名称" path="alias" required>
            <n-input 
              maxlength="128" 
              v-model:value="model.alias" 
              @blur="model.alias = ($event.target as HTMLInputElement)?.value.trim()"
              show-count clearable 
              placeholder="请输入开户名称" 
              />
          </n-form-item-gi>

          <n-form-item-gi :span="8" label="银行卡号" path="bankCardNo">
            <n-input 
              maxlength="40" 
              v-model:value="model.bankCardNo" 
              show-count 
              clearable 
              placeholder="请输入银行卡号" 
              :allow-input="onlyAllowNumber"
              required
              @blur="blurValidateBankCardNo"/>
          </n-form-item-gi>

          <n-form-item-gi :span="8" label="开户银行" path="bankId">
             <JAccountBank 
               v-model:value="model.bankId"
               :disabled="model.accountOpeningNameLoading"
              />
          </n-form-item-gi>

          <n-gi :span="12" v-if="!ZjPay()">
            <n-grid :cols="24" :x-gap="12">
              <n-form-item-gi 
                v-if="FyEnterpriseFormsShow()" 
                :span="8" label="开户行所在地" >
                <JProvinceSelect  
                 v-model:value="model.provinceId"
                 v-model:code="model.provinceCode"
                 v-model:label="model.provinceLabel" 
                 :disabled="model.accountOpeningNameLoading"
                />
               </n-form-item-gi>
               <n-form-item-gi 
                 v-if="FyEnterpriseFormsShow()" 
                 :span="8">
                <JUrbanSelect
                 v-model:value="model.cityId"
                 v-model:label="model.urbanLabel"
                  v-model:code="model.urbanCode"
                 :parentCode="model.provinceCode"
                 :disabled="model.provinceCode == null || model.accountOpeningNameLoading"
                />
               </n-form-item-gi>
            </n-grid>
          </n-gi>

          <n-form-item-gi v-if="FyEnterpriseFormsShow() || ZjPay()" :span="8" label="开户行名称">
            <JBankDeposit
            v-model:value="model.KHbankId"
            v-model:label="model.bankName"
            v-model:bankNo="model.bankNo"
            v-model:accountOpeningNameLoading="model.accountOpeningNameLoading"
            :payPlatForm="model.payPlatform"
            :parentId="model.bankId"
            :provinceCode="model.provinceCode"
            :urbanCode="model.urbanCode"
            :disabled="(
               model.bankId == null
               && model.provinceCode == null 
               && model.urbanCode == null
               )"
            />
          </n-form-item-gi>

          <n-form-item-gi v-if="FyEnterpriseFormsShow() || ZjPay()" :span="8" label="开户行行号">
            <n-input 
              v-model:value="model.bankNo" 
              @blur="model.bankNo = ($event.target as HTMLInputElement)?.value.trim()"
              readonly
              placeholder="选择开户行名称显示开户行行号" 
              />
          </n-form-item-gi>

          <n-form-item-gi v-if="FyIndividualBusinessAccountsFormsShow() && !ZjPay()" :span="8" label="银行账号类型"  :path="FyIndividualBusinessAccountsFormsShow() ? 'bankAccountType' : '' " >
            <n-select 
               v-model:value="model.bankAccountType" 
               :options="bankAccountOptions" 
               placeholder="请选择银行账号类型"
               clearable 
               />
          </n-form-item-gi>
          
          <n-form-item-gi :span="8" label="联系人手机号" path="mobile" required>
            <n-input-number 
             v-model:value="model.mobile" 
             :show-button="false"  
             @blur="blurMobilePhone" 
             style="width: 100%;" 
             placeholder="请输入联系电话"
             clearable />
          </n-form-item-gi>

          <n-form-item-gi :span="8" label="开户证件类型" path="certType">
             <n-select 
               v-model:value="model.certType" 
               :options="accountOptions" 
               placeholder="请选择开户证件类型"
               disabled/>
          </n-form-item-gi>

          <n-form-item-gi :span="8" label="开户证件号" path="certNo">
            <n-input 
              maxlength="40" 
              v-model:value="model.certNo" 
              @blur="blurAccountDocuments"
              show-count 
              clearable 
              :placeholder="accountDocumentsPrompt" 
              required/>
          </n-form-item-gi>

          <!-- 1.1.6 -->
          <n-form-item-gi v-if="ZjPay()" :span="8" label="身份证证件日期" :path="!model.identityCardProlonged ? 'identityCardDate' : 'identityProlongedDatep'" required>
            <j-date-range-picker v-if='!model.identityCardProlonged' v-model:value="model.identityCardDate" type="daterange" format="yyyy-MM-dd" :width="100" :clearable="false" />
            <n-date-picker v-if='model.identityCardProlonged' v-model:value="model.identityProlongedDatep" type="date"/>
            <n-space style="margin-left: 10px;" >
              <n-checkbox v-model:checked="model.identityCardProlonged">
                长期
              </n-checkbox>
            </n-space>
          </n-form-item-gi>

          <n-form-item-gi v-if="ZjPay()" :span="8" label="详情地址" path="customerAddress">
            <n-input v-model:value="model.customerAddress" placeholder="请填写证件上详细地址" maxlength="100" show-count/>
          </n-form-item-gi>

          <!-- 法人身份证人像面 -->
          <n-form-item-gi v-if="ZjPay() && !ZjAccountOpeningType()" :span="8" label="法人身份证人像面" path="corprateImgFront">
             <div>
              <CustomizeUpload 
              v-model:value="model.corprateImgFront"
              v-model:imgId="model.corprateImgFrontImgId"
              accept=".jpg,.jpeg,.png" 
              :fileListSize="5" 
              :max="1" 
              :isZjImgUpload="true" 
              :imageUploadType="['image/jpg','image/jpeg','image/png']" 
              :enableTypeValidation="true" 
              :merchantNumber="model.merchantId"
              />
              <span class="picturePrompts">只支持jpg,jpeg,png格式图片,不大于5M</span>
             </div>
          </n-form-item-gi>
           
          <!-- 法人身份证国徽面 -->
          <n-form-item-gi v-if="ZjPay() && !ZjAccountOpeningType()" :span="8" label="法人身份证国徽面" path="corprateImgBack" >
             <div>
              <CustomizeUpload 
              v-model:value="model.corprateImgBack" 
              v-model:imgId="model.corprateImgBackImgId"
              accept=".jpg,.jpeg,.png" 
              :fileListSize="5" 
              :max="1" 
              :isZjImgUpload="true" 
              :merchantNumber="model.merchantId"
              :imageUploadType="['image/jpg','image/jpeg','image/png']" :enableTypeValidation="true"
              />
              <span class="picturePrompts">只支持jpg,jpeg,png格式图片,不大于5M</span>
             </div>
          </n-form-item-gi>

          <!-- 营业执照图片 -->
          <n-form-item-gi v-if="ZjPay() && !ZjAccountOpeningType()" :span="8" label="营业执照图片" path="usccImg">
             <div>
              <CustomizeUpload 
              v-model:value="model.usccImg" 
              v-model:imgId="model.usccImgId" 
              accept=".jpg,.jpeg,.png" 
              :fileListSize="5" 
              :max="1" 
              :isZjImgUpload="true" 
              :imageUploadType="['image/jpg','image/jpeg','image/png']" :enableTypeValidation="true" 
              :merchantNumber="model.merchantId"
              />
              <span class="picturePrompts">只支持jpg,jpeg,png格式图片,不大于5M</span>
             </div>
          </n-form-item-gi>

          <!-- 开户许可证图片 -->
          <n-form-item-gi v-if="ZjPay() && !ZjAccountOpeningType()" :span="8" label="开户许可证图片" path="accountOpenImg" >
             <div>
              <CustomizeUpload 
              v-model:value="model.accountOpenImg" 
              v-model:imgId="model.accountOpenImgId" 
              accept=".jpg,.jpeg,.png" 
              :fileListSize="5" 
              :max="1" 
              :imageUploadType="['image/jpg','image/jpeg','image/png']" :enableTypeValidation="true" 
              :merchantNumber="model.merchantId"
              :isZjImgUpload="true"
              />
              <span class="picturePrompts">只支持jpg,jpeg,png格式图片,不大于5M</span>
             </div>
          </n-form-item-gi>

          <n-form-item-gi v-if="ZjPay() && !ZjAccountOpeningType()" :span="8" label="法人名称" path="corprateName">
            <n-input v-model:value="model.corprateName" placeholder="请填写法人姓名" maxlength="11" show-count/>
          </n-form-item-gi>

          <n-form-item-gi v-if="ZjPay() && !ZjAccountOpeningType()" :span="8" label="法人身份证号" path="corprateCertNo">
            <n-input 
              maxlength="40" 
              v-model:value="model.corprateCertNo" 
              @blur="blurCorporateIdNumber"
              clearable 
              placeholder="请填写法人身份证号码" 
              required
              show-count/>
          </n-form-item-gi>


          <n-form-item-gi v-if="ZjPay() && !ZjAccountOpeningType()" :span="8" label="法人证件日期" :path="!model.juridicalProlonged ? 'juridicalPersonDocumentsDate' : 'juridicalPersonProlongedDatep'" required>
            <j-date-range-picker v-if="!model.juridicalProlonged" v-model:value="model.juridicalPersonDocumentsDate" type="daterange" format="yyyy-MM-dd" :clearable="false"  :width="100"/>
            <n-date-picker v-else v-model:value="model.juridicalPersonProlongedDatep" type="date" :width="100"/>
            <n-space style="margin-left: 10px;">
              <n-checkbox v-model:checked="model.juridicalProlonged">
                长期
              </n-checkbox>
            </n-space>
          </n-form-item-gi>
          

          <n-form-item-gi v-if="ZjPay() && !ZjAccountOpeningType()" :span="8" label="法人手机" path="corprateMobile">
            <n-input v-model:value="model.corprateMobile" placeholder="请填写法人手机" maxlength="11" show-count clearable/>
          </n-form-item-gi>
          
          <n-form-item-gi v-if="ZjPay() && !ZjAccountOpeningType()" :span="8" label="公司邮箱" path="customerEmail">
            <n-input v-model:value="model.customerEmail" placeholder="请填写公司邮箱" maxlength="100" show-count @blur="blurValidateEmail" clearable/>
          </n-form-item-gi>


          <n-form-item-gi v-if="bankAccountLabels[model.bankAccountType] == '法人对私卡' && accountOpeningLabels[model.cleanType] == '个体工商户'" :span="8" label="法人证件类型" path="legalCertTp">
             <n-select 
               v-model:value="model.legalCertTp" 
               :options="legalPersonCertificateOptions" 
               placeholder="请选择开户证件类型"
               />
          </n-form-item-gi>

          <n-form-item-gi v-if="bankAccountLabels[model.bankAccountType] == '法人对私卡' && accountOpeningLabels[model.cleanType] == '个体工商户'" :span="8" label="法人证件号" path="legalCertNo" required>
            <n-input 
              v-model:value="model.legalCertNo"
              clearable 
              :placeholder="legalPersonDocumentsPrompt"/>
          </n-form-item-gi>

          <n-form-item-gi :span="8" label="绑定社群端经销商账号(可选项)" path="reason">
            <n-space>
                 <n-tag 
                   v-for="item in model.dealerList" 
                   :key="item.id"  
                   closable
                   @close="handleClose(item.id)" 
                   >
                  {{ item.name || item.nickname }}
                 </n-tag>
            </n-space>
            <n-button text type="info" @click="handleDealerModal">
               选择
            </n-button>
          </n-form-item-gi>

        </n-grid>
      </n-form>
    </JModal>
    <dealerModal ref="dealerModalShow"
    :callAddState="true" 
    @dealer-ids="handleDealerIds" 
    @dealer-list="handleDealerList"/>
</template>
  
<script setup lang="ts" name="AddorEditVideo">
import { computed, onMounted, ref, watch,  } from "vue";
import { useMessages } from "@/hooks";
import { accountOpeningOptions,
    accountOpeningLabels,
    bankAccountOptions,
    accountOptions,
    paymentMethodsOptions,
    bankAccountLabels,
    legalPersonCertificateOptions,
    paymentMethodsLabels } from "@/constants";
import useSubAccountPayeeManagement from '../hooks/useSubAccountPayeeManagement'
import dealerModal from './dealerModal.vue'
import { allocationAccountAdd } from "@/services/api";
import moment from "moment";
const {validateIDNumber,numberVerification,validateEmail,validateBankCardNo} = useSubAccountPayeeManagement()

const onlyAllowNumber = (value: string) => !value || /^\d+$/.test(value)

const initParams = {
  /** 入账方名称 */
  payPlatform:2,
  /** 商户号 */
  merchantId:null,
  /** 账号类型 */
  cleanType:1,
  /** 入账方名称 */
  alias:null,
  /** 银行卡号 */
  bankCardNo:null,
  /** 开户银行名称 */
  bankName:null,
  /** 银行id */
  bankId:null,
  /** 开户行id */
  KHbankId:null,
  /** 联系人手机号 */
  mobile:null,
  /** 证件类型 */
  certType:0,
  /** 证件号 */
  certNo:null,
  /** 省份 */
  provinceId:null,
  /** 城市 */
  cityId:null,
  /** 银行账号类型 */
  bankAccountType:1,
  /** 社群经销商账号列表 */
  userIds:[],
  /** 开户银行行号 */
  bankNo:null,
  /** 省份Code */
  provinceCode:null,
  /** 省份Value */
  provinceValue:null,
  /** 省份Label */
  provinceLabel:null,
  /** 市区Value */
  urbanValue:null,
  /** 市区Label */
  urbanLabel:null,
  /** 省市区Code */
  urbanCode:null,
  /** 经销商list */
  dealerList:[],
  /** 法人证件类型 */
  legalCertTp:0,
  /** 法人证件号 */
  legalCertNo:null,

  //1.1.6
  /** 表单类型 */
  type:null,
  /** 开户名称 loading*/
  accountOpeningNameLoading:false,
  /** 证件生效日期 */
  cardStartDate:undefined,
  /** 证件失效日期 */
  cardEndDate:undefined,
  /** 详情地址 */
  customerAddress:undefined,
   /** 法人身份证人像面 */
  corprateImgFront:'',
  /** 法人身份证人像面imgId */
  corprateImgFrontImgId:null,
  /** 法人身份证国徽面 */
  corprateImgBack:'',
  /** 法人身份证国徽面imgId */
  corprateImgBackImgId:null,
  /** 营业执照图片 */
  usccImg:'',
  /** 营业执照图片imgId */
  usccImgId:null,
  /** 开户许可证图片 */
  accountOpenImg:'',
   /** 开户许可证图片imgId */
   accountOpenImgId:null,
  /** 法人名称 */
  corprateName:undefined,
  /** 法人身份证号码 */
  corprateCertNo:undefined,
  /** 法人证件证件生效日期 */
  corprateCertExpiry:undefined,
  /** 法人证件证件失效日期 */
  corprateCertExpiryEnd:undefined,
  /** 法人手机 */
  corprateMobile:undefined,
  /** 公司邮箱 */
  customerEmail:undefined,
  /** 身份证证件日期 */
  identityCardDate:'',
  /** 法人证件日期 */
  juridicalPersonDocumentsDate:'',
  /** 身份证长期 */
  identityCardProlonged:false,
  /** 法人长期 */
  juridicalProlonged:false,
  /** 身份证长期开始时间 */
  identityProlongedDatep:null,
  /** 法人证件长期开始时间 */
  juridicalPersonProlongedDatep:null

};
const model = ref({ ...initParams });
export interface BillToInformationProps { 
    type?:string;
    row?:any;
    tableData?: any;
    refresh?: () => void; // 刷新表格
}

/* 提示信息 */
const message = useMessages();

/* 模态框显隐状态 */
const show = ref(false);

/* 表单规则 */
const rules = ref(
  {
  payPlatform:{
    required: true,
    trigger: ["blur", "change"],
    message: "请选择支付渠道",
    validator: ()=>{
      return model.value.payPlatform != null;
    }
  },
  merchantId:{
    required: true,
    trigger: ["blur", "change"],
    message: "请选择归属商户",
    validator: ()=>{
      return model.value.merchantId != null;
    }
  },
  cleanType:{
    required: true,
    trigger: ["blur", "change"],
    message: "请选择开户类型",
    validator: ()=>{
      return model.value.cleanType != null;
    }
  },
  alias:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入开户名称",
  },
  bankCardNo:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入银行卡号",
  },
  mobile:{
    type: "number",
    required: true,
    trigger: ["blur", "change"],
    message: "请输入联系电话",
  },
  certNo:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入开户证件号",
  },
  certType:{
    required: true,
    trigger: ["blur", "change"],
    message: "请选择开户证件类型",
    validator: ()=>{
      return model.value.certType != null;
    }
  },
  bankId:{
    required: true,
    trigger: ["blur", "change"],
    message: "请选择开户银行",
    validator: ()=>{
      return model.value.bankId != null;
    }
  },
  legalCertTp:{
    required: true,
    trigger: ["blur", "change"],
    message: "请选择法人证件类型",
    validator: ()=>{
      return model.value.legalCertTp != null;
    }
  },
  legalCertNo:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入法人证件号",
  },
  identityCardDate:{
    type: "array",
    required: true,
    trigger: ["blur", "change"],
    message: "请选择身份证证件日期",
    validator: ()=>{
      if(!model.value.identityCardProlonged){
        if(model.value.identityCardDate == '' || model.value.identityCardDate == null){
          return false
        }
      }
    }
  },
  customerAddress:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入详情地址",
  },
  corprateImgFront:{
    required: true,
    trigger: ["blur", "change"],
    message: "请选择法人身份证人像面",
  },
  corprateImgBack:{
    required: true,
    trigger: ["blur", "change"],
    message: "请选择法人身份证国徽面",
  },
  usccImg:{
    required: true,
    trigger: ["blur", "change"],
    message: "请选择营业执照图片",
  },
  accountOpenImg:{
    required: true,
    trigger: ["blur", "change"],
    message: "请选择开户许可证照片",
  },
  corprateName:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入法人名称",
  },
  corprateCertNo:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入法人身份证号码",
  },
  juridicalPersonDocumentsDate:{
    type: "array",
    required: true,
    trigger: ["blur", "change"],
    message: "请选择法人证件证件日期",
    validator: ()=>{
      if(!model.value.juridicalProlonged){
        if(model.value.juridicalPersonDocumentsDate == '' || model.value.juridicalPersonDocumentsDate == null){
          return false
        }
      }
    }
  },
  corprateMobile:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入法人手机",
  },
  customerEmail:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入公司邮箱",
  },
  identityProlongedDatep:{
    required: true,
    trigger: ["blur", "change"],
    message: "请选择身份证证件开始日期",
    validator: ()=>{
      if(model.value.identityCardProlonged){
        return model.value.identityProlongedDatep != null; 
      }
    }
  },
  juridicalPersonProlongedDatep:{
     required: true,
     trigger: ["blur", "change"],
     message: "请选择法人证件证件开始日期",
     validator: ()=>{
      if(model.value.juridicalProlonged){
        return model.value.juridicalPersonProlongedDatep != null;        
      }
    }
  }
}
)
const tableData = ref()
const acceptParams = (param) => {
  parameter.value = param
  tableData.value =  parameter.value.tableData
  show.value = true
  model.value.type = parameter.value.type
};

/* 表单实例 */
const formRef = ref(null);

/* 表单参数初始化 */
const formDataReset = () => {
  model.value = { ...initParams };
};

const parameter = ref<BillToInformationProps>({
  
});

/* 关闭弹窗 */
const closeModal = () => {
  formDataReset();
  // 弹窗取消
  show.value = false;
  parameter.value.refresh();
};

/* 确认--保存 */
const isLoading = ref(false);
const _save = (e: MouseEvent) => {
  
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors && !isLoading.value) {
      const merchantDuplicated = tableData.value.some((item)=>{
       if(item.merchantId == model.value.merchantId && item.certNo == model.value.certNo){
         message.createMessageWarning('同个商户号不允许使用同个证件号');
         return true
       }
       return false
      })
      if(!merchantDuplicated){
        isLoading.value = true;
        const {payPlatform,merchantId,cleanType,alias,bankCardNo,bankId,provinceId,cityId,bankName,bankNo,bankAccountType,mobile,certType,certNo,userIds,KHbankId,legalCertTp,legalCertNo,identityCardDate,customerAddress,corprateImgFrontImgId,corprateImgBackImgId,usccImgId,accountOpenImgId,corprateName,corprateCertNo,juridicalPersonDocumentsDate,corprateMobile,customerEmail,identityCardProlonged,juridicalProlonged,identityProlongedDatep,juridicalPersonProlongedDatep} = model.value
        const params = {
          data:{  
            payPlatform,
            merchantId,
            cleanType:ZjPay() ? cleanType : ('0'+ cleanType),
            alias,
            bankCardNo,
            bankId: KHbankId != null ? KHbankId : bankId,
            provinceId,
            cityId,
            bankName,
            bankNo,
            bankAccountType: bankAccountType != null ? ('0'+ bankAccountType) : null,
            mobile,
            certType,
            certNo,
            userIds,
            legalCertTp: bankAccountLabels[bankAccountType] == '法人对私卡' ? legalCertTp : undefined,
            legalCertNo: bankAccountLabels[bankAccountType] == '法人对私卡' ? legalCertNo : undefined,
            cardStartDate: identityCardDate ? moment(identityCardDate[0]).format(`YYYY-MM-DD`) :  moment(identityProlongedDatep).format(`YYYY-MM-DD`),
            cardEndDate: !identityCardProlonged ? (identityCardDate ? moment(identityCardDate[1]).format(`YYYY-MM-DD`) : undefined) : '2999-12-31',
            customerAddress,
            corprateImgFront:corprateImgFrontImgId,
            corprateImgBack:corprateImgBackImgId,
            usccImg:usccImgId,
            accountOpenImg:accountOpenImgId,
            corprateName,
            corprateCertNo,
            corprateCertExpiry: juridicalPersonDocumentsDate ? moment(juridicalPersonDocumentsDate[0]).format(`YYYY-MM-DD`) : moment(juridicalPersonProlongedDatep).format(`YYYY-MM-DD`),
            corprateCertExpiryEnd: !juridicalProlonged ? (juridicalPersonDocumentsDate ? moment(juridicalPersonDocumentsDate[1]).format(`YYYY-MM-DD`) : undefined) : '2999-12-31',
            corprateMobile,
            customerEmail,
          }
        }
        try {
          // 刷新表格数据
          await allocationAccountAdd(params)
          closeModal()
          message.createMessageSuccess('入账方信息录入成功');
        } catch (err) {
          message.createMessageError(`入账方信息录入失败： ${err}`);
        }finally{
          isLoading.value = false;
        }
      }
    }
  });
};


/** 判断输入的手机号码是否正确 */
const blurMobilePhone = () =>{
    if(!numberVerification(model.value.mobile) && model.value.mobile != null){
        model.value.mobile = null
    }
}

/** 开户证件号提示转态 */
const accountDocumentsPrompt = computed(() => {
    return model.value.certType == accountOptions[0].value ? '请填写身份证号' : '请填写企业社会信用代码'
})

/** 法人证件号提示转态 */
const legalPersonDocumentsPrompt = computed(() => {
    if(model.value.legalCertTp == legalPersonCertificateOptions[0].value){
       return  '请填写身份证号'
    }else if(model.value.legalCertTp == legalPersonCertificateOptions[1].value){
       return  '请填写外国护照'
    }else if(model.value.legalCertTp == legalPersonCertificateOptions[2].value){
       return  '请填写港澳通行证'
    }
})

/** 开户账号blur事件 */
const blurAccountDocuments = (event) =>{
    // 去除输入框值的首尾空格
    const idNumber = event.target?.value.trim();
    if(idNumber != ''){
      model.value.certNo = idNumber;
      // 校验身份证号是否符合规则
      if (!validateIDNumber(idNumber) && accountDocumentsPrompt.value == '请填写身份证号') {
        message.createMessageWarning("请输入有效的身份证号");
        model.value.certNo = ""; // 清空无效的身份证号
      }
    }
}

/** 法人身份证号blur事件 */
const blurCorporateIdNumber = (event) =>{
  // 去除输入框值的首尾空格
  const idNumber = event.target?.value.trim();
    if(idNumber != ''){
      model.value.certNo = idNumber;
      // 校验身份证号是否符合规则
      if (!validateIDNumber(idNumber)) {
        message.createMessageWarning("请输入有效的法人身份证号");
        model.value.certNo = ""; // 清空无效的身份证号
      }
    }
}

/** 经销商Id */
const handleDealerIds = (value) =>{
  model.value.userIds = value
}

/** 经销商List */
const handleDealerList = (value) =>{
  model.value.dealerList = value
}

/** 经销商模态框 */
const dealerModalShow = ref()
const handleDealerModal = () =>{
    const params = {
      ids:model.value.userIds,
      list:model.value.dealerList
    }
    dealerModalShow.value.acceptParams(params)
}

/** 删除标签事件 */
const handleClose = (id) => {
  const index = model.value.userIds.indexOf(id); // 找到目标 id 的索引
  if (index !== -1) {
    model.value.userIds.splice(index, 1); // 删除目标 id
  }
  model.value.dealerList = model.value.dealerList.filter(dealer => dealer.id !== id);
}

/** 中金支付 */
const ZjPay = () =>{
  return paymentMethodsLabels[model.value.payPlatform] == '中金支付'
}

/** 富友支付 */
const FyPay = () =>{
  return paymentMethodsLabels[model.value.payPlatform] == '富友支付'
}

/** 中金开户类型 */
const ZjAccountOpeningType = () =>{
   return accountOpeningLabels[model.value.cleanType] == '个人'
}

/** 表单类型判断 */
const isEdit = () =>{
  return model.value.type == 'edit'
}

/** 邮箱校验 */
const blurValidateEmail = () =>{
  if(!validateEmail(model.value.customerEmail)){
    model.value.customerEmail = null
  }
}

/** 银行卡号校验 */
const blurValidateBankCardNo = () =>{
  if(!validateBankCardNo(model.value.bankCardNo)){
    model.value.bankCardNo = null
  }
}

/** 富有企业表单显示 */
const enterpriseFormStatus = ref(false); // 企业表单状态
const FyEnterpriseFormsShow = () =>{
   const label = accountOpeningLabels[model.value.cleanType];
   enterpriseFormStatus.value = label !== "个人";
   if(enterpriseFormStatus.value){
        model.value.certType = accountOptions[1].value
    }else{
        model.value.certType = accountOptions[0].value
    }
   return enterpriseFormStatus.value
}

/** 富有个体工商户表单显示 */
const soleProprietorshipFormstatus = ref(false); // 个体工商户表单状态
const FyIndividualBusinessAccountsFormsShow = () =>{
   const label = accountOpeningLabels[model.value.cleanType];
   soleProprietorshipFormstatus.value = label === "个体工商户";
   return soleProprietorshipFormstatus.value
}

/** 开户类型监听 */
watch(
  () => model.value.cleanType,
  (newVal) => {

    model.value = {
        ...model.value,
        provinceId: null,
        provinceCode: null,
        provinceLabel: null,
        cityId: null,
        urbanLabel: null,
        urbanCode: null,
        KHbankId: ZjPay() ? model.value.KHbankId : null,
        bankName: null,
        bankNo: ZjPay() ? model.value.bankNo : null,
        bankAccountType: null
    }
    
    //中金切换开户类型赋值重置判断
    if(ZjPay() && !FyEnterpriseFormsShow()){
      model.value = {
        ...model.value,
        corprateImgFront:'',
        corprateImgBack:'',
        usccImg:'',
        accountOpenImg:'',
        corprateName:undefined,
        corprateCertNo:undefined,
        corprateCertExpiry:undefined,
        corprateCertExpiryEnd:undefined,
        corprateMobile:undefined,
        customerEmail:undefined,
        identityCardDate:'',
        juridicalPersonDocumentsDate:'',
        juridicalProlonged:false
      }
    }
  },
);

/** 开户银行、开户行所在地监听 */
watch(
  () => [
    model.value.bankId,
    model.value.provinceCode,
    model.value.urbanCode,
  ],
  newVal => {
    Object.assign(model.value, {
      bankName: null,
      bankNo: null,
      KHbankId: null,
    });
  },
);

/** 银行账号类型监听 */
watch(
  () => model.value.bankAccountType,
  newVal => {
    model.value = {
      ...model.value,
      legalCertTp: 0,
      legalCertNo: null,
    }
  },
);

/** 银行账号类型监听 */
watch(
  () => model.value.payPlatform,
  newVal => {
    model.value = {
      ...model.value,
      KHbankId: null,
      bankNo: null,
      bankId: null,
      provinceId: null,
      cityId: null,
      provinceCode: null,
      urbanCode: null,
      cardStartDate: undefined,
      cardEndDate: undefined,
      customerAddress: undefined,
      corprateImgFront: "",
      corprateImgBack: "",
      usccImg: "",
      accountOpenImg: "",
      corprateName: undefined,
      corprateCertNo: undefined,
      corprateCertExpiry: undefined,
      corprateCertExpiryEnd: undefined,
      corprateMobile: undefined,
      customerEmail: undefined,
      identityCardDate: "",
      juridicalPersonDocumentsDate: "",
      identityCardProlonged: false,
      juridicalProlonged: false,
      bankAccountType:null
    };
  }
);


/** 身份证长期单选框 监听 */
watch(
  () => model.value.identityCardProlonged,
  newVal => {
    if(newVal){
      model.value.identityProlongedDatep = model.value.identityCardDate[0]
      model.value.identityCardDate = ''
    }else{
      model.value.identityProlongedDatep = null
    }
    
  },
);

/** 法人长期单选框 监听 */
watch(
  () => model.value.juridicalProlonged,
  newVal => {
   
    if(newVal){
      model.value.juridicalPersonProlongedDatep =  model.value.juridicalPersonDocumentsDate[0]
      model.value.juridicalPersonDocumentsDate = ''
    }else{
      model.value.juridicalPersonProlongedDatep = null
    }
  },
);

const fieldsToWatch = [
  { key: 'corprateImgFront', idKey: 'corprateImgFrontImgId' },
  { key: 'corprateImgBack', idKey: 'corprateImgBackImgId' },
  { key: 'usccImg', idKey: 'usccImgId' },
  { key: 'accountOpenImg', idKey: 'accountOpenImgId' }
];
/** 监听图片上传 */
fieldsToWatch.forEach(({ key, idKey }) => {
  watch(
    () => model.value[key],
    (newVal) => {
      if (newVal === '') {
        model.value[idKey] = null;
      }
    }
  );
});


defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less">
.picturePrompts{
  font-size: 14px;
  color: #c2c2c2;
}
</style>
  