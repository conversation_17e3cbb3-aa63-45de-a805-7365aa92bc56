import { createApp } from "vue";
import App from "./App.vue";
import { setupRouter } from "./router";
import './assets/transition.css'
import './assets/normalize.css'
import '@/styles/default.less'
import 'virtual:svg-icons-register';
import { setupStores } from './stores'
import { setupDirectives } from './directives'
const app = createApp(App)
setupStores(app)
setupRouter(app)
setupDirectives(app)
app.mount('#app')

