<script setup lang="ts">
import { reactive, ref, watch, computed, toRef } from "vue";
import { NModal, NSpace, NTag, NDivider, NButton, NScrollbar } from "naive-ui";
import Draggable from "vuedraggable";
import { createCacheStorage } from "@/utils/cache/storageCache";
import { Cache_Key, StorageType } from "@/enums/cache";
import { useMessages } from "@/hooks";
// import {
//   sortTableAdd,
//   sortTableUpdate,
//   sortTableDelete,
//   sortTableGetByTbName,
// } from "@/services/api";
import { deepmerge } from "deepmerge-ts";
import SvgIcon from "@/components/SvgIcon/index.vue";
import { isArray } from "@/utils/isUtils";
// props
const props = defineProps({
  show: Boolean,
  columns: Object,
  tableKey: String,
  saveLocalAuth: Boolean,
  saveGlobalAuth: <PERSON>olean,
});

const emits = defineEmits<{
  (e: "update:columns", columns: any): void;
  (e: "update:show", show: boolean): void;
}>();

// modal框显隐
// const j_show = props.show
const j_show = toRef(props, "show");

let _tempCols = deepmerge([], props.columns);

// modal框样式
const segmented = { content: "soft" };
// content-style 样式
const contentStyel = {
  padding: "10px 10px 10px 20px",
  border: "1px solid rgb(239, 239, 245)",
  height: "400px",
};
const saveGlobalMethod = {
  update: "update",
  add: "add",
};
const storageConfig = {
  key: "sortTable-" + props.tableKey,
  type: StorageType.LOCAL,
  hasEncrypt: false,
};
const sortStorage = createCacheStorage(storageConfig);
const { createMessageSuccess, createMessageError } = useMessages();

//全局缓存
let howSaveGlobal = null;
//需要排序的数据
const columnsArr = [];

const isSavaGlobalDisable = ref(false);

const isAllSelectRef = ref(false);
const isAddLoadingRef = ref(false);
const isDataLoadingRef = ref(false);

const colsCategoryConfigAndDefault = computed(() => {
  if (props.colsCategoryConfig)
    return [{ label: "默认属性", key: "default" }, ...props.colsCategoryConfig];
  else return undefined;
});

//分类数据
const colsCategoryConfig = colsCategoryConfigAndDefault.value || [
  { label: "默认属性", key: "default" },
];
//字段分类结构
const headerTypes = computed(() => {
  let data = colsCategoryConfig;
  data.forEach((item) => {
    item.value = [];
    if (item.key === "default") {
      columnsArr.forEach((columnsItem) => {
        if (!columnsItem.colsCategory) item.value.push(columnsItem);
        if (columnsItem.isRequired) {
          if (
            columnHeaders.value.findIndex((element) => {
              return element === columnsItem.key;
            }) === -1
          )
            columnHeaders.value.push(columnsItem.key);
        }
      });
    } else {
      columnsArr.forEach((columnsItem) => {
        if (columnsItem.colsCategory === item.key) {
          item.value.push(columnsItem);
        }
        if (columnsItem.isRequired) {
          if (
            columnHeaders.value.findIndex((element) => {
              return element === columnsItem.key;
            }) === -1
          )
            columnHeaders.value.push(columnsItem.key);
        }
      });
    }
  });
  return data;
});
//分类全选结构
const checkboxReactive = ref({});
//构建分类全选结构
const typesAllSelectKey = () => {
  let object = {};
  headerTypes.value.forEach((item) => {
    let allCheckKeys = [];
    let isRequired = [];
    item.value.forEach((valItem) => {
      allCheckKeys.push(valItem.key);
      if (valItem.isRequired) isRequired.push(valItem.key);
    });
    object[item.key] = {
      allCheckKeys,
      isRequired,
      isAllSelectRef: false,
      indeterminate: isRequired.length > 0 ? true : false,
    };
  });
  checkboxReactive.value = object;
};
// {
//     key: {
//         allCheckKeys:[],
//         checkedKeys:[],
//         isRequired: [],
//     },
// }
//可选字段
const checkLength = computed(() => {
  let length = columnsArr.length;
  columnsArr.forEach((item) => {
    if (item.isRequired) length = length - 1;
  });
  return length;
});
//默认展开项（全部）
const defaultExpandedNames = computed(() => {
  return colsCategoryConfig.map((item, index) => {
    return index;
  });
});
//选中的数据(排序)
const columnHeaders = ref([]);
//最大可选数
const checkedMax = ref(20);

//选中回调(checkOK)
const checkboxGroupChange = (meta, type) => {
  if (meta.actionType === "check") {
    const index = columnsArr.findIndex((item) => {
      return item.key === meta.value;
    });
    if (index != -1) {
      sort.value.push(columnsArr[index]);
    }
  } else if (meta.actionType === "uncheck") {
    const index = sort.value.findIndex((item) => {
      return item.key === meta.value;
    });
    if (index != -1) sort.value.splice(index, 1);
  }
  compactHeadersUtils.isIndeterminate(type);
};

//
const selectionAndIndex = {
  fixedLeft: [],
  fixedRight: [],
};

//排序数据
const sort = ref([]);

// 过滤columns数据源
const labelArrFiltration = () => {
  _tempCols.forEach((item) => {
    if (item.type === "selection" || item.key === "index") {
      selectionAndIndex.fixedLeft.push({ key: item.key });
    } else
      columnsArr.push({
        isRequired: item.isRequired,
        title: item.colName,
        key: item.key,
        colsCategory: item.colsCategory,
      });
    if (item.isRequired) {
      if (item.fixed === "left")
        selectionAndIndex.fixedLeft.push({ key: item.key });
      else if (item.fixed === "right")
        selectionAndIndex.fixedRight.push({ key: item.key });
    }
  });
};

//删除标签
const close = (key) => {
  const sortIndex = sort.value.findIndex((sortItem) => {
    return sortItem.key === key;
  });
  const columnHeadersIndex = columnHeaders.value.findIndex((closItem) => {
    return closItem === key;
  });
  if (sortIndex != -1) sort.value.splice(sortIndex, 1);
  if (columnHeadersIndex != -1)
    columnHeaders.value.splice(columnHeadersIndex, 1);
};
// 清空
const clearAll = () => {
  sort.value = [];
  columnHeaders.value = [];
  if (columnsArr && columnsArr.length > 0) {
    columnsArr.forEach((item) => {
      if (item.isRequired) {
        // sort.value.push(item)
        columnHeaders.value.push(item.key);
      }
    });
  }
  colsCategoryConfig.forEach((item) => {
    compactHeadersUtils.isIndeterminate(item.key);
  });
};
//utils
const compactHeadersUtils = {
  getAfterSortKey: () => {
    const _totalCols = [].concat(
      selectionAndIndex.fixedLeft,
      sort.value,
      selectionAndIndex.fixedRight
    );
    const colsAfterSortKeys = [];
    _totalCols
      .map((item) => item.key)
      .forEach((item) => {
        if (item !== "selection" && item !== "index") {
          const index = colsAfterSortKeys.findIndex((col) => col === item);
          if (index === -1) {
            colsAfterSortKeys.push(item);
          }
        }
      });
    return colsAfterSortKeys;
  },
  // columns数据重组
  columnsReorganization: () => {
    const _totalCols = [].concat(
      selectionAndIndex.fixedLeft,
      sort.value,
      selectionAndIndex.fixedRight
    );
    const colsAfterSort = [];
    _totalCols.forEach((item) => {
      const index = colsAfterSort.findIndex((col) => col.key === item.key);
      if (index === -1) {
        colsAfterSort.push(_tempCols.find((col) => col.key === item.key));
      }
    });
    emits("update:columns", colsAfterSort);
  },
  // get 数据获取
  _getStoreSortData: async () => {
    //缓存
    try {
      let data = [];
      sort.value = [];
      columnHeaders.value = [];
      let _cache = sortStorage.get();
      if (_cache && isArray(_cache)) {
        _cache.forEach((key) => {
          const index = columnsArr.findIndex((colsItem) => {
            return colsItem.key === key;
          });
          if (index != -1) data.push(columnsArr[index]);
        });
      } else {
        //全局
        try {
          isDataLoadingRef.value = true;
          const res = await sortTableGetByTbName(storageConfig.key);
          const obj = res;
          isSavaGlobalDisable.value = false;
          if (JSON.stringify(obj) != "{}") {
            howSaveGlobal = saveGlobalMethod.update;
            if (res.colArr && res.colArr.length) {
              sortStorage.set(res.colArr);
              res.colArr.forEach((key) => {
                const index = columnsArr.findIndex((colsItem) => {
                  return colsItem.key === key;
                });
                if (index != -1) data.push(columnsArr[index]);
              });
            }
          } else {
            howSaveGlobal = saveGlobalMethod.add;
          }
        } catch (e) {
          createMessageError("网络异常，此次修改将无法保存至服务器");
          isSavaGlobalDisable.value = true;
        } finally {
          isDataLoadingRef.value = false;
        }
      }
      isDataLoadingRef.value = false;
      let newData = [];
      let colsData = [];

      if (data && data.length > 0) {
        data.forEach((item) => {
          if (
            sort.value.findIndex((sortItem) => {
              return item.key === sortItem.key;
            }) === -1
          )
            newData.push(item);
          if (
            columnHeaders.value.findIndex((cols) => {
              return item.key === cols;
            }) === -1
          )
            colsData.push(item.key);
        });
      } else {
        columnsArr.forEach((item) => {
          if (
            sort.value.findIndex((sortItem) => {
              return item.key === sortItem.key;
            }) === -1
          )
            newData.push(item);
          if (
            columnHeaders.value.findIndex((cols) => {
              return item.key === cols;
            }) === -1
          )
            colsData.push(item.key);
        });
      }
      sort.value = sort.value.concat(newData);
      sort.value = sort.value.filter((item) => {
        return (
          selectionAndIndex.fixedLeft.findIndex(
            (col) => col.key === item.key
          ) == -1 &&
          selectionAndIndex.fixedRight.findIndex(
            (col) => col.key === item.key
          ) == -1
        );
      });

      columnHeaders.value = columnHeaders.value.concat(colsData);
      colsCategoryConfig.forEach((item) => {
        compactHeadersUtils.isIndeterminate(item.key);
      });
      compactHeadersUtils.columnsReorganization();
    } catch (e) {
      createMessageError(e);
    }
  },
  // 部分选中的判断
  isIndeterminate: (type) => {
    let currentTypeChecked = [];
    checkboxReactive.value[type].allCheckKeys.forEach((key) => {
      if (
        columnHeaders.value.filter((colsItem) => {
          return colsItem === key;
        })[0]
      )
        currentTypeChecked.push(
          columnHeaders.value.filter((colsItem) => {
            return colsItem === key;
          })[0]
        );
    });
    if (
      checkboxReactive.value[type].allCheckKeys.length ===
      currentTypeChecked.length
    ) {
      checkboxReactive.value[type].indeterminate = false;
      checkboxReactive.value[type].isAllSelectRef = true;
    } else {
      for (
        let i = 0;
        i < checkboxReactive.value[type].allCheckKeys.length;
        i++
      ) {
        if (
          columnHeaders.value.findIndex((colsItem) => {
            return checkboxReactive.value[type].allCheckKeys[i] === colsItem;
          }) != -1
        ) {
          checkboxReactive.value[type].indeterminate = true;
          if (checkboxReactive.value[type].isAllSelectRef)
            checkboxReactive.value[type].isAllSelectRef = false;
          return;
        } else {
          checkboxReactive.value[type].indeterminate = false;
        }
      }
    }
    // checkboxReactive.value[type].allCheckKeys.forEach((item)=>{
    //     if(columnHeaders.value.findIndex(colsItem => {return item === colsItem}) === -1) checkboxReactive.value[type].indeterminate =false
    //     else checkboxReactive.value[type].indeterminate =true
    // })
  },
};
const init = () => {
  labelArrFiltration();
  typesAllSelectKey();
  compactHeadersUtils._getStoreSortData();
};
// 保存
const saveBtn = () => {
  // const _cache = sort.value.map((item) => { return item.key })
  const _cache = compactHeadersUtils.getAfterSortKey();
  sortStorage.set(_cache);
  compactHeadersUtils.columnsReorganization();
  emits("update:show", false);
  // j_show.value = false
};
// 保存全局
const saveGlobal = async () => {
  // const _cache = sort.value.map((item) => { return item.key })
  const _cache = compactHeadersUtils.getAfterSortKey();
  // 请求
  try {
    isAddLoadingRef.value = true;
    if (howSaveGlobal === saveGlobalMethod.update) {
      await sortTableUpdate({ tbname: storageConfig.key, colArr: _cache });
    } else if (howSaveGlobal === saveGlobalMethod.add) {
      await sortTableAdd({ tbname: storageConfig.key, colArr: _cache });
    } else {
      await sortTableGetByTbName(storageConfig.key).then((res) => {
        const obj = res;
        if (JSON.stringify(obj) != "{}")
          howSaveGlobal = saveGlobalMethod.update;
        else howSaveGlobal = saveGlobalMethod.add;
      });
      if (howSaveGlobal === saveGlobalMethod.update)
        await sortTableUpdate({ tbname: storageConfig.key, colArr: _cache });
      else if (howSaveGlobal === saveGlobalMethod.add)
        await sortTableAdd({ tbname: storageConfig.key, colArr: _cache });
    }
    compactHeadersUtils.columnsReorganization();
    sortStorage.set(_cache);
    emits("update:show", false);
    // j_show.value = false
  } catch (e) {
    createMessageError("保存异常，请稍后再试");
  } finally {
    isAddLoadingRef.value = false;
  }
};

function allSelectChange(value, type) {
  checkboxReactive.value[type].indeterminate = false;
  checkboxReactive.value[type].isAllSelectRef = value;
  if (value) {
    let newData = [];
    let colsData = [];
    columnsArr.forEach((item) => {
      if (
        sort.value.findIndex((sortItem) => {
          return item.key === sortItem.key;
        }) === -1 &&
        checkboxReactive.value[type].allCheckKeys.findIndex((allCheckKey) => {
          return item.key === allCheckKey;
        }) != -1
      )
        newData.push(item);
      if (
        columnHeaders.value.findIndex((cols) => {
          return item.key === cols;
        }) === -1 &&
        checkboxReactive.value[type].allCheckKeys.findIndex((allCheckKey) => {
          return item.key === allCheckKey;
        }) != -1
      )
        colsData.push(item.key);
    });
    sort.value = sort.value.concat(newData);
    sort.value = sort.value.filter((item) => {
      return (
        selectionAndIndex.fixedLeft.findIndex((col) => col.key === item.key) ==
          -1 &&
        selectionAndIndex.fixedRight.findIndex((col) => col.key === item.key) ==
          -1
      );
    });
    columnHeaders.value = columnHeaders.value.concat(colsData);
  } else {
    checkboxReactive.value[type].allCheckKeys.forEach((item) => {
      const sortIndex = sort.value.findIndex((sortItem) => {
        return item === sortItem.key;
      });
      const colsIndex = columnHeaders.value.findIndex((cols) => {
        return item === cols;
      });
      if (
        sortIndex != -1 &&
        checkboxReactive.value[type].isRequired.findIndex((isRequiredKey) => {
          return item === isRequiredKey;
        }) === -1
      )
        sort.value.splice(sortIndex, 1);
      if (
        colsIndex != -1 &&
        checkboxReactive.value[type].isRequired.findIndex((isRequiredKey) => {
          return item === isRequiredKey;
        }) === -1
      )
        columnHeaders.value.splice(colsIndex, 1);
    });
    compactHeadersUtils.isIndeterminate(type);
    // clearAll()
  }
}

watch(
  j_show,
  (newVal, oldVal) => {
    if (newVal) compactHeadersUtils._getStoreSortData();
  },
  { immediate: true }
);
//调用
init();
// compactHeadersUtils._getStoreSortData()
</script>

<template>
  <n-modal
    v-model:show="j_show"
    preset="card"
    :style="{ width: '900px' }"
    :title="`自定义列表`"
    size="small"
    :bordered="false"
    :auto-focus="false"
    @update:show="emits('update:show', false)"
  >
    <div
      v-if="isDataLoadingRef"
      style="
        width: 100%;
        height: 460px;
        display: flex;
        justify-content: center;
        align-items: center;
      "
    >
      <n-spin size="small" />
    </div>
    <n-space vertical size="large" v-else>
      <n-layout has-sider :native-scrollbar="false">
        <n-layout :native-scrollbar="false">
          <n-layout-content :content-style="contentStyel" :native-scrollbar="true">
            <p class="optionalField">
              可选字段
              <span style="color: var(--n-color-target)">
                {{
                checkLength
                }}
              </span>
            </p>

            <n-collapse :default-expanded-names="defaultExpandedNames">
              <n-collapse-item
                v-for="(headerType, index) in headerTypes"
                :key="headerType.key"
                :title="headerType.label"
                :name="index"
              >
                <n-scrollbar style="max-height: 300px">
                  <n-grid x-gap="12" :y-gap="8" :cols="4">
                    <n-gi :span="4">
                      <n-checkbox
                        label="全选"
                        :checked="
                          checkboxReactive[headerType.key].isAllSelectRef
                        "
                        :indeterminate="
                          checkboxReactive[headerType.key].indeterminate
                        "
                        :disabled="false"
                        @update:checked="
                          allSelectChange($event, headerType.key)
                        "
                      />
                    </n-gi>
                    <n-gi :span="4">
                      <n-checkbox-group
                        v-model:value="columnHeaders"
                        :max="checkedMax"
                        @update:value="
                          (value, meta) =>
                            checkboxGroupChange(meta, headerType.key)
                        "
                      >
                        <n-grid x-gap="12" :y-gap="8" :cols="4">
                          <n-gi v-for="colTitle in headerType.value" :key="colTitle.key">
                            <n-checkbox
                              :key="colTitle.key"
                              :value="colTitle.key"
                              :disabled="
                                colTitle.isRequired
                                  ? colTitle.isRequired
                                  : false
                              "
                            >
                              <n-ellipsis style="max-width: 120px">
                                {{ colTitle.title }}
                              </n-ellipsis>
                            </n-checkbox>
                          </n-gi>
                        </n-grid>
                      </n-checkbox-group>
                    </n-gi>
                  </n-grid>
                </n-scrollbar>
              </n-collapse-item>
            </n-collapse>
          </n-layout-content>
        </n-layout>
        <n-layout-sider :content-style="contentStyel">
          <div class="checkedField">
            <p class="optionalField">
              已选字段
              <span style="color: var(--n-color-target)">
                {{
                sort.length
                }}
              </span>
            </p>
            <n-button text type="primary" @click="clearAll">清空</n-button>
          </div>
          <n-scrollbar style="max-height: 330px">
            <Draggable v-model="sort" :animation="100" :sort="true" itemKey="key">
              <template #item="{ element }">
                <div class="draggable-item">
                  <div
                    style="
                      cursor: move;
                      background-color: #eeeeee;
                      padding: 3px 8px;
                    "
                  >
                    <n-space justify="space-between">
                      <div>
                        <n-space justify="space-between">
                          <n-icon style="padding: 0px">
                            <SvgIcon localIcon="move"></SvgIcon>
                          </n-icon>
                          <n-ellipsis style="max-width: 200px">
                            {{ element.title }}
                          </n-ellipsis>
                        </n-space>
                      </div>
                      <n-button v-if="!element.isRequired" text @click="close(element.key)">
                        <n-icon style="padding: 0px">
                          <SvgIcon localIcon="close"></SvgIcon>
                        </n-icon>
                      </n-button>
                      <n-button v-else text></n-button>
                    </n-space>
                  </div>
                </div>
              </template>
            </Draggable>
          </n-scrollbar>
        </n-layout-sider>
      </n-layout>
    </n-space>
    <template #action>
      <n-space justify="end" v-if="!isDataLoadingRef">
        <n-button @click="emits('update:show', false)">取消</n-button>
        <n-button v-if="!isSavaGlobalDisable" @click="saveGlobal" :loading="isAddLoadingRef">保存到服务器</n-button>
        <n-button v-show="true" type="primary" @click="saveBtn">保存到本地</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<style lang="less" scoped>
.checkedField {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
}

.draggable-item {
  padding-bottom: 5px;
}

:deep(
    .n-collapse
      .n-collapse-item
      .n-collapse-item__content-wrapper
      .n-collapse-item__content-inner
  ) {
  padding-top: 5px;
}

:deep(
    .n-collapse
      .n-collapse-item
      .n-collapse-item__header
      .n-collapse-item__header-main
  ) {
  font-weight: bold;
}

.optionalField {
  font-size: 16px;
  font-weight: bold;
  padding: 5px 0px;
}
</style>
