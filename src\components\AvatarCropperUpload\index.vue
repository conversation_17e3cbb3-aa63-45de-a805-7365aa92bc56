<template>
  <n-modal
    :show="showRef"
    :auto-focus="false"
    preset="card"
    :style="{
      width: '650px',
    }"
    title="修改头像"
    size="small"
    @update:show="handleCacnel"
  >
    <n-upload :custom-request="customRequest" @before-upload="beforeUpload">
      <n-button>选择本地文件</n-button>
      <p class="upload-notice">仅支持JPG、静态GIF和PNG图片文件，且小于3M。</p>
    </n-upload>
    <div class="avatar-edit-content">
      <div class="cropper-wrapper">
        <div v-if="!cropperSrcRef" class="cropper-notice">
          <p>1.从本地选择一张你要上传的照片。</p>
          <p>2.编辑裁切调整合适角度和尺寸。</p>
        </div>
        <vue-cropper
          v-else
          ref="cropperRef"
          :img="cropperSrcRef"
          :fixed="true"
          :auto-crop="true"
          :center-box="true"
          @real-time="realTime"
          mode="cover"
        ></vue-cropper>
      </div>
      <div class="preview-wrapper">
        <p>您上传的图片将会自动生成三种尺寸头像和高清头像，请注意中小尺寸的头像是否清晰。</p>
        <div class="img-wrapper">
          <div class="preview lg">
            <div v-if="previews.url" :style="previewStyleReactive.large">
              <img :src="previews.url" :style="previews.img" />
            </div>
            <img v-else class="lg" :src="avatarSrc" alt="" />
            <span class="notice">大尺寸</span>
          </div>
          <div class="preview md">
            <div v-if="previews.url" :style="previewStyleReactive.md">
              <img :src="previews.url" :style="previews.img" />
            </div>
            <img v-else class="md" :src="avatarSrc" alt="" />
            <span class="notice">中尺寸</span>
          </div>
          <div class="preview sm">
            <div v-if="previews.url" :style="previewStyleReactive.sm">
              <img :src="previews.url" :style="previews.img" />
            </div>
            <img v-else class="sm" :src="avatarSrc" alt="" />
            <span class="notice">小尺寸</span>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <n-space justify="end">
        <n-button size="small" @click="handleCacnel">取消</n-button>
        <n-button size="small" type="primary" @click="handleAvatarSave" :loading="isUploadLoadingRef">保存</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script lang="ts" setup>
import "vue-cropper/dist/index.css";
import { VueCropper } from "vue-cropper";
import { reactive, ref, toRef, toRefs, watch } from "vue";
import type { UploadCustomRequestOptions, UploadFileInfo } from "naive-ui";
import {
  asyncFileToBase64,
  blobToFile,
  transformMinioSrc,
} from "@/utils/fileUtils";
import { fileUpload } from "@/services/api";
import avatarSrc from "@/assets/image/system/avatar.png";
import { useMessages } from "@/hooks";
const props = defineProps<{
  value: string;
  show: boolean;
}>();
const valueRef = toRef(props, "value");
const showRef = toRef(props, "show");
const { createMessageError } = useMessages();
const isUploadLoadingRef = ref(false);
const emits = defineEmits<{
  (e: "update:value", src: string): void;
  (e: "update:show", show: boolean): void;
}>();

const cropperSrcRef = ref();
const previews = ref("");
const cropperRef = ref(null);
const previewStyleReactive = reactive({
  large: {},
  md: {},
  sm: {},
});
watch(
  valueRef,
  (newVal) => {
    if (!newVal) {
      cropperSrcRef.value = transformMinioSrc(newVal);
    }
  },
  { immediate: true }
);

function handleAvatarSave() {
  cropperRef.value.getCropBlob(async (data) => {
    const _file = blobToFile(data);
    const _formData = new FormData();
    _formData.append("file", _file);
    try {
      isUploadLoadingRef.value = true;
      const { miniopath } = await fileUpload(_formData);
      emits("update:value", miniopath);
      emits("update:show", false);
    } catch (e) {
      createMessageError(`上传头像失败:${e}`);
    } finally {
      isUploadLoadingRef.value = false;
    }
  });
}

function getPreviewStyleByHeight(data, height: number) {
  return {
    width: data.w + "px",
    height: data.h + "px",
    overflow: "hidden",
    margin: "0",
    zoom: height / data.h,
  };
}

function realTime(data) {
  previews.value = data;
  previewStyleReactive.large = getPreviewStyleByHeight(data, 150);
  previewStyleReactive.md = getPreviewStyleByHeight(data, 100);
  previewStyleReactive.sm = getPreviewStyleByHeight(data, 50);
}
function beforeUpload(data: {
  file: UploadFileInfo;
  fileList: UploadFileInfo[];
}) {
  const {
    file: { size },
  } = data.file;
  if (size > 3 * 1024 * 1024) {
    createMessageError(`该文件超出大小，大小限制为3MB`);
    return false;
  }
  return true;
}

async function customRequest({
  file,
  onFinish,
  onError,
}: UploadCustomRequestOptions) {
  try {
    const blob = await asyncFileToBase64(file.file);
    cropperSrcRef.value = blob;
    onFinish();
  } catch (e) {
    createMessageError(`读取图片失败，请稍后再试`);
    onError();
  }
}

function handleCacnel() {
  emits("update:show", false);
}
</script>

<style lang="less" scoped>
.upload-notice {
  color: #cacaca;
  padding: 10px 0px;
}
.avatar-edit-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.cropper-wrapper {
  width: 200px;
  height: 200px;
  border: 1px solid #b1b1b1;
  position: relative;
  background: #f2f3f5;
  .cropper-notice {
    position: absolute;
    top: 50%;
    padding-left: 10px;
    transform: translateY(-50%);
    font-size: 12px;
  }
}
.preview-wrapper {
  display: flex;
  flex-wrap: wrap;
  width: 350px;
  .img-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    flex: 1;
    padding: 10px 0px;
  }
  p {
    font-size: 12px;
  }
  .notice {
    font-size: 10px;
  }
  .preview {
    text-align: center;
    &.lg,
    .lg {
      width: 150px;
    }
    &.md,
    .md {
      width: 100px;
    }
    &.sm,
    .sm {
      width: 50px;
    }
  }
}

:deep(.n-upload-file-list) {
  display: none;
}
</style>
