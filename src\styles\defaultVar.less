@primary-color: #1677FF;

@success-color: #00b42a;

@primary-color-hover: #4592FF;

@main-header-height: 60px;
@main-footer-height: 0px;
@main-content-height:calc (100vh - @main-header-height - @main-footer-height);
@main-content-width: 100vw;

@blank-background-color: #f2f3f5;

@default-border-radius: 5px;

@default-padding-md: 10px;
@default-padding-lg: 16px 12px;

@blank-page-padding: 4px;

@secondary-text-color: #666666;

@default-border-color: #eeeeee;

@inner-bg-height: calc(100vh - @main-header-height - @main-footer-height - @blank-page-padding*2);

@tab-pane-inner-bg-height: calc(100vh - 42px - @main-header-height - @main-footer-height - @blank-page-padding*2);

@font-size-sm: 12px;
@font-size-base: 14px;
@font-size-lg: 16px;

@previe-padding:0px 0px 4px;
@preview-height:80px;
