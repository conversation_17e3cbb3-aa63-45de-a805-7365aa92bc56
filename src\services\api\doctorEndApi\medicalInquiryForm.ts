import { defHttp } from "@/services";

export const enum MedicalInquiryFormApi {
    getMedicalInquiryForm = "/pres/page/hospital",
    getMedicalInquiryFormDetail = "/pres/get/hospital",
    medicalInquiryFormExport = "/pres/export",
    medicalInquiryFormRefund = "/afterSaleRecord/refundConsultation",
}

/** 获取问诊单列表 */
export function getDoctorMedicalInquiryForm(params) {
    return defHttp.post({
        url: MedicalInquiryFormApi.getMedicalInquiryForm,
        params,
    });
}
/** 获取问诊单详情 */
export function getDoctorMedicalInquiryFormDetail(params) {
    return defHttp.get({
        url: MedicalInquiryFormApi.getMedicalInquiryFormDetail,
        params,
    });
}
/** 获取问诊单导出 */
export function medicalInquiryFormExport(params) {
    return defHttp.post({
        url: MedicalInquiryFormApi.medicalInquiryFormExport,
        requestConfig: {
            responeseType: "stream",
        },
        params,
    });
}
/** 问诊单退款 */
export function medicalInquiryFormRefund(params) {
    return defHttp.post({
        url: MedicalInquiryFormApi.medicalInquiryFormRefund,
        params,
    });
}
