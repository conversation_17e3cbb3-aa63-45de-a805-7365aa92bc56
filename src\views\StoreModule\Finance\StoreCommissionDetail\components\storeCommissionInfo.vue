<template>
  <n-modal
    v-model:show="showModal"
    :auto-focus="false"
    :style="{width:detailData.commissionType == 1 ? '500px' : '800px' }"
    title="详情"
    :bordered="false"
    size="small"
    :closable="true"
    @after-leave="closeModal"
    preset="card"
  >
    <n-space vertical :size="10">
      <n-space vertical :size="10">
        <n-space>
          <div>订单编号：</div>
          <div>{{detailData.orderNo}}</div>
        </n-space>
        <n-space v-if="detailData.commissionType == 1">
          <div>商品总额(实付款)：</div>
          <div>￥{{(detailData?.allocationItemVOList[0]?.totalAmount / 100).toFixed(2)}}</div>
        </n-space>
        <n-space>
          <div>分佣对象：</div>
          <div>{{ detailData.commissionType == 1 ? "店长店员" : "门店归属经销商" }}</div>
        </n-space>
        <n-space>
          <div>分佣规则：</div>
          <div>
            {{ detailData.commissionType == 1 ? "按订单商品总额实付款计算佣金" : "按商品维度设置的分佣比例分佣" }}
          </div>
        </n-space>
        <n-space vertical :size="10" v-if="detailData.commissionType == 1" >
          <n-space>
            <div>订单归属用户分佣比例：</div>
            <div>{{ detailData.clerkRate }}%</div>
          </n-space>
          <n-space>
            <div>订单归属用户分佣金额：</div>
            <div>{{ detailData.clerkCommission ? (detailData.clerkCommission / 100).toFixed(2) : '0.00' }}</div>
          </n-space>
          <n-space>
            <div>店长分佣比例：</div>
            <div>{{ detailData.managerRate }}%</div>
          </n-space>
          <n-space>
            <div>店长分佣金额：</div>
            <div>{{ detailData.managerCommission ? (detailData.managerCommission / 100).toFixed(2) : '0.00' }}</div>
          </n-space>
        </n-space>
      </n-space>

      <div style="height: 200px;" v-if="detailData.commissionType == 2">
        <FormLayout
          :tableData="detailData.allocationItemVOList"
          :tableColumns="tableColumns"
          :isNeedCollapse="false"
          :isBatchDelete="false"
          :isTableSelection="false"
          :isTablePagination="false"
          :isDisplayHeader="false"
          :isDisplayIndex="false"
        ></FormLayout>
      </div>
    </n-space>
  </n-modal>
</template>

<script setup lang="tsx">
import { ref, reactive, toRefs, onMounted, watch, computed } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { type StoreCommissionDetailDTO } from "@/services/api";

const props = withDefaults(defineProps<{
  show: boolean;
  detailData: StoreCommissionDetailDTO;
}>(), {
  show: false,
  detailData: () => ({}),
})

const emits = defineEmits<{(e: 'update:show', value: boolean): void}>()

const goodsInfo = ref([])
const tableColumns = [
    {
        title: "商品名称/ID",
        key: "productId",
        width: 130,
        render: (row) => {
            return <table-tooltip row={row} nameKey="productName" title={row['productName']} idKey="id" />;
        }
    },
    {
        title: "商品规格",
        key: "productSpecName",
        width: 100,
    },
    {
        title: "商品总额(实付款)",
        key: "totalAmount",
        width: 100,
        render: (row) => {
            return <span>{row.totalAmount ? (row.totalAmount / 100).toFixed(2) : '0.00'}</span>;
        }
    },
    {
        title: "经销商分佣比例",
        key: "dealerAllocationRatio",
        width: 100,
    },{
        title: "经销商分佣金额",
        key: "dealerAllocationAmount",
        width: 100,
        render: (row) => {
            return <span>{row.dealerAllocationAmount ? (row.dealerAllocationAmount / 100).toFixed(2) : '0.00'}</span>;
        }
    }]

const showModal = computed({
    get() {
        return props.show;
    },
    set(value) {
        emits("update:show", value);
    },
})

const closeModal = () => {
    showModal.value = false;
}
</script>

<style scoped lang="less"></style>
