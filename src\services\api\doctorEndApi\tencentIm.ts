import {defHttp} from '@/services';
import {Doctor<PERSON><PERSON>} from "@/services/api";

/** tencent Im */
export const enum TencentImApi {
    register = 'im/registerImUser',
    getImUserSig = 'im/getImUserSig',
    getImSysConfig = 'im/getImSysConfig',
    onlineStatus = '/doctorEntity/updateOnlineStatus',
    conversationPage = '/imContact/pageConversation', // 会话列表页
    endConversation = '/pres/completePres', // 提前结束会话
    imMessagePage = '/imMessage/page', // IM历史消息数据
}

/** 注册 */
export function registerImAccount(params) {
    return defHttp.post({
        url: TencentImApi.register,
        params
    })
}


/** 获取Im的UserSig签名 */
export function getImUserSig(params) {
    return defHttp.get({
        url: TencentImApi.getImUserSig,
        params
    })
}


/** 获取Im的系统配置 */
export function getImSysConfig(params) {
    return defHttp.get({
        url: TencentImApi.getImSysConfig,
        params
    })
}

// 更新医师在线状态   
export function updateOnlineStatus(params) {
    return defHttp.get({
        url: TencentImApi.onlineStatus,
        params
    })
}

export function getConversationPage(params) {
    return defHttp.post({
        url: TencentImApi.conversationPage,
        params
    })
}

/**
 * 结束会话
 * @param params
 */
export function endConversation(params) {
    return defHttp.delete({
        url: TencentImApi.endConversation,
        params,
        requestConfig: {
            isQueryParams: true
        }
    })
}

/**
 * 分页获取会话消息
 * @param params
 */
export function getConversationIMMessagePage(params) {
    return defHttp.post({
        url: TencentImApi.imMessagePage,
        params
    })
}
