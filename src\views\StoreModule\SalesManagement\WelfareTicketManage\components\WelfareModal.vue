<template>
    <JModal
    v-model:show="isShow"
    width="600"
    :title="titMap[props.welfareModalType]"
    @after-leave="closeModal"
		@positive-click="_submit"
		:positiveButtonProps="{
			loading: props.isAddLoading
		}"
  >
  <n-form
      ref="formRef"
      :rules="rules"
      :model="model"
      label-width="auto"
      label-placement="left"
      require-mark-placement="right-hanging"
      :style="{
          width: '100%',
        }"
    >
      <n-grid :cols="8" :x-gap="24">
        <template v-if="props.welfareModalType != WelfareModalTypeEnum.VALIDITY">
          <n-form-item-gi :span="24" label="福利券名称" path="name" required>
            <n-input v-model:value="model.name" placeholder="请输入名称" :maxlength="props.welfarePageType == WelfarePageTypeEnum.LIVEROOM ? 100:30" clearable/>
          </n-form-item-gi>
          <n-form-item-gi :span="24" label="发放数量"  path="totalQuantity" required>
            <n-space align="center">
              <n-input-number v-model:value="model.totalQuantity" :min="1" :disabled="model.isInfinite || props.welfareModalType == WelfareModalTypeEnum.EDIT" />
              <n-checkbox v-model:checked="model.isInfinite" :disabled="props.welfareModalType == WelfareModalTypeEnum.EDIT || model.receiveConditionType == 2">无限制</n-checkbox>
            </n-space>
          </n-form-item-gi>
        </template>
          <n-form-item-gi :span="24" label="券有效期" path="validUntil" required>
          <n-date-picker 
              v-model:value="model.validUntil" 
              type="date" 
              clearable
              :default-time="'23:59:59'"
              :is-date-disabled="dateDisabled"
              :input-readonly="true"
              style="width: 100%;"
            />
          </n-form-item-gi>
          <template v-if="props.welfareModalType != WelfareModalTypeEnum.VALIDITY">
          <n-form-item-gi :span="24" label="福利券分类" path="categoryId"  required>
            <JWelfareClassify v-model:value="model.categoryId" isImmediately style="width: 100%;" placeholder="请选择分类"></JWelfareClassify>
          </n-form-item-gi>
          <n-form-item-gi :span="24" label="领取条件" path="minute" required>
            <n-space align="center">
              <n-radio-group v-model:value="model.receiveConditionType" style="margin-left: 10px;" @update:value="handleRadioChange" :disabled="watchMinuteDis">
            <n-space>
              <n-radio :value="1">无条件</n-radio>
              <n-radio :value="2">观看时长</n-radio>
            </n-space>
          </n-radio-group>
          <div style="display: flex;align-items: center;" v-if="showMinute">满&nbsp;<n-input-number v-model:value="model.minute" :disabled="watchMinuteDis" :precision="0" :min="1" :style="{ width: '23%', }" :show-button="false"/>分钟可领取</div>
            </n-space>
          </n-form-item-gi>
        </template>
      </n-grid>
    </n-form>
  </JModal>
</template>

<script setup lang="ts">
import { ref,computed, watch } from 'vue';
import { useMessages } from '@/hooks';
import { WelfareModalTypeEnum,WelfarePageTypeEnum } from '../types';
import JWelfareClassify from '@/components/JSelect/JWelfareClassify.vue';
import dayjs from 'dayjs';
const message = useMessages();

const props = withDefaults(defineProps<{
    row?: any;
    show: boolean;
    welfareModalType: WelfareModalTypeEnum;
    welfarePageType: WelfarePageTypeEnum;
    isAddLoading: boolean;
}>(), {
    row: null,
    show: false,
    welfareModalType: WelfareModalTypeEnum.ADD,
    welfarePageType: WelfarePageTypeEnum.PAFGE,
    isAddLoading: false,
  });
const emits = defineEmits<{
    (e: 'update:show', value: boolean): void;
    (e: 'refresh'): void;
    (e: 'onSubmit',value:any): void;
}>();
const showMinute = ref(false)
const isShow = computed({
    get: () => props.show,
    set: (value: boolean) => {
      emits('update:show', value);
    }
});

const  dateDisabled = (ts: number)=>{
  return dayjs(ts).isBefore(dayjs().startOf('day'));
}

const titMap = {
  [WelfareModalTypeEnum.ADD]:"添加福利券",
  [WelfareModalTypeEnum.EDIT]:"编辑福利券",
  [WelfareModalTypeEnum.VALIDITY]:"修改有效期",
}
const initParams = {
  id:null,
  categoryId:null,
  validUntil: null,
  name:null,
  totalQuantity:1,
  isInfinite:false,
  receiveConditionType:1,
  minute:null,
};
const model = ref({ ...initParams });

/* 表单规则 */
const rules = {
  name:{
    required: props.welfareModalType !== WelfareModalTypeEnum.VALIDITY,
    trigger: ["blur", "change"],
    message: "请输入福利券名称",
  },
  totalQuantity:{
    type: "number",
    required: props.welfareModalType !== WelfareModalTypeEnum.VALIDITY && !model.value.isInfinite,
    trigger: ["blur", "change"],
    message: "请输入需要发行的数量",
  },
  categoryId:{
    required: props.welfareModalType !== WelfareModalTypeEnum.VALIDITY,
    trigger: ["blur", "change"],
    message: "请选择福利券分类",
  },
  validUntil:{
    required: true,
    type: "number",
    trigger: ["blur", "change"],
    message: "请选择有效期时间",
  },
  minute:{
    required: true,
    type: "number",
    trigger: ["blur", "change"],
    message: "领取时间不能为空且大于0",
    validator:()=>{
      if(model.value.receiveConditionType == 1){
        return true
      }else{
        return model.value.minute !=null && model.value.minute > 0
      }
    }
  }

};
const watchMinuteDis = ref(false)
// 关闭按钮
const closeModal = () => {
  isShow.value = false;
  showMinute.value = false
  model.value = { ...initParams };
}

// 确认按钮
const formRef = ref(null); 
const _submit = async () => {
    formRef.value?.validate(async (errors: any) => {
        if (!errors) {
          emits("onSubmit",{type:props.welfareModalType,data:model.value})
        }
    });
}
const handleRadioChange =(value)=>{
  if(value == 2){
    showMinute.value = true
    model.value.isInfinite = true

  }else{
    showMinute.value = false
  }


}
watch(()=>props.show, (newVal, oldVal) => {
  if(newVal && (props.welfareModalType === WelfareModalTypeEnum.VALIDITY || props.welfareModalType === WelfareModalTypeEnum.EDIT )){
    const time = dayjs(props.row.validUntil).valueOf();
    const rowData = {
      id: props.row.id,
      validUntil: time,
      name:props.row.name,
      totalQuantity:props.row.totalQuantity,
      categoryId:props.row.categoryId,
      isInfinite:props.row.totalQuantity == -1 ? true : false,
      receiveConditionType:props.row?.receiveConditionType || 1,
      minute:props.row.watchDuration,
    }
    Object.assign(model.value, rowData);
    showMinute.value = props.row.receiveConditionType == 2 ? true : false
      watchMinuteDis.value = true
  }else{
    watchMinuteDis.value = false
  }
});

// watch(()=>model.value.totalQuantity, (newVal, oldVal) => {
//   if(model.value.totalQuantity == -1){
//       model.value.isInfinite = model.value.totalQuantity == -1 ? true : false
//   }
// });

</script>