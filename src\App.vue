<template>
  <n-config-provider :theme-overrides="themeOverrides" :locale="zhCN" :date-locale="dateZhCN">
    <n-message-provider>
      <n-notification-provider :max="2" placement="bottom-right" >
        <n-dialog-provider>
          <BlankLayout />
        </n-dialog-provider>
      </n-notification-provider>
    </n-message-provider>
  </n-config-provider>
</template>

<script setup lang="ts">
import { RouterView, useRouter } from "vue-router";
import { themeOverrides } from "@/styles/themeOverride";
import { zhCN, dateZhCN, useMessage } from "naive-ui";
import { bootstrap } from "./bootstrap";
import { stores } from "@/stores/index";
import BlankLayout from "./layout/BlankLayout.vue";
import { onBeforeUnmount, onMounted, watch } from "vue";
import { useDocumentVisibility } from "@/hooks/useDocumentVisibility";
import { useUserStore } from "./stores/modules/user";
const { isDocumentVisibilityRef } = useDocumentVisibility();
const userStore = useUserStore();
bootstrap({
  router: useRouter(),
  store: stores,
});

watch(isDocumentVisibilityRef, (newVal) => {
  // if (newVal && userStore.token && userStore.userInfo) refreshUserToken();
});

onMounted(() => {
  window.addEventListener('resize', calcContentRect);
  calcContentRect();
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', calcContentRect);
});

/** 适配手机 */
function calcContentRect() {
  const bodyDom = document.getElementsByTagName('html')[0];
  if (navigator.userAgent.match(/Mobi/i) || navigator.userAgent.match(/Android/i) || navigator.userAgent.match(/iPhone/i)) {
    // 当前设备是移动设备
    document.documentElement.style.setProperty('--content-client-width', `1500px`);
    document.documentElement.style.setProperty('--content-client-height', `950px`);
  } else {
    document.documentElement.style.setProperty('--content-client-width', `${bodyDom.clientWidth}px`);
    document.documentElement.style.setProperty('--content-client-height', `${bodyDom.clientHeight}px`);
  }
};
</script>

<style lang="less">
@import "@/styles/default.less";
:root {
  --content-client-width: 100vw;
  --content-client-height: 100vh;
}

html,
body,
#app {
  min-width: var(--content-client-width);
  min-height: var(--content-client-height);
  font-family: "SourceHanSansCN", "Microsoft YaHei", "微软雅黑" !important;
}
</style>
