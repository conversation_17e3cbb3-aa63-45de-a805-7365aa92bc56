import type {IConversationModel} from "@tencentcloud/chat-uikit-engine";

export interface IImageMessageContent {
    showName?: string;
    url?: string;
    width?: number;
    height?: number;
}

export const CHAT_MSG_CUSTOM_TYPE = {
    TEXT: 'TEXT', // 自定义消息-文本消息
    IMG: 'IMG', // 自定义消息-图片消息
    PRES_CARD: 'PRES_CARD', // 自定义消息-问诊卡片
    FORMULARY_CARD: 'FORMULARY_CARD', // 自定义消息-处方卡片
    SYS: 'SYS' // 自定义消息-系统消息
};


export interface ICustomMessagePayload {
    businessID?: string;
    // Evaluation-related custom message fields
    score?: number;
    comment?: string;
    // Order & Hyperlink Class Custom Message Common Fields
    link?: string;
    // Order-related custom message fields
    imageUrl?: string;
    title?: string;
    description?: string;
    price?: string;
    // Hyperlink custom message related fields
    text?: string;
}

export type ToolbarDisplayType = 'emojiPicker' | 'tools' | 'none';

export interface ITipTapEditorContent {
    type: 'text' | 'image' | 'video' | 'file' | 'custom';
    payload: {
        text?: string;
        file?: File;
        atUserList?: string[];
    };
}

export interface IOfflinePushInfoCreateParams {
    conversation: IConversationModel;
    payload?: any;
    messageType: string;
}

export type InputDisplayType = 'editor' | 'audio';

export type JSONContent = {
    type?: string
    attrs?: Record<string, any>
    content?: JSONContent[]
    marks?: {
        type: string
        attrs?: Record<string, any>
        [key: string]: any
    }[]
    text?: string
    [key: string]: any
}

export interface IImageMessageContent {
    showName?: string;
    url?: string;
    width?: number;
    height?: number;
}

// 定义状态类型联合
export type ServiceSysStatus =
    | "THE_SERVICE_HAS_BEGUN"
    | "THE_SERVICE_HAS_ENDED"
    | "PRESCRIPTION_AUDIT_PASSED"
    | "PRESCRIPTION_AUDIT_NOT_PASSED"
    | "PRESCRIPTION_OPEN";

// 创建紧凑映射对象（类型自动推断）
export const SysStatusMap = {
    THE_SERVICE_HAS_BEGUN: "服务已开始",
    THE_SERVICE_HAS_ENDED: "本次服务已结束",
    PRESCRIPTION_AUDIT_PASSED: "处方审核通过，已自动发送给患者",
    PRESCRIPTION_AUDIT_NOT_PASSED: "处方审核不通过，请修改后重新提交",
    PRESCRIPTION_OPEN: "处方已发送给药师，药师审核通过后自动发送给患者"
} as const;

export interface JConversationModel {
    /** 对话唯一标识 */
    id: string;
    /** 创建时间 (YYYY-MM-DD HH:mm:ss 格式) */
    createTime: string;
    /** 最后更新时间 (YYYY-MM-DD HH:mm:ss 格式) */
    updateTime: string;
    /** 用户 ID */
    userId: string;
    /** 用户类型 */
    userType: number;
    /** 联系人用户 ID */
    contactUserId: string;
    /** 联系人用户类型 */
    contactUserType: number;
    /** 联系人即时通讯用户 ID */
    contactImUserId: string;
    /** 会话 ID */
    conversationId: string;
    /** 最后一条消息 ID */
    lastMsgId: string;
    /** 最后一条消息内容 */
    lastContent: string;
    /** 最后一条消息类型 (如 TEXT) */
    lastMsgType: string;
    /** 最后消息时间 (YYYY-MM-DD HH:mm:ss 格式) */
    lastContentTime: string;
    /** 未读消息数量 */
    unreadCount: number;
    /** 联系人昵称 */
    nickname: string;
    /** 联系人头像 URL */
    img: string;
    /** 问诊开始时间 (YYYY-MM-DD HH:mm:ss 格式) */
    receiveStartTime?: string;
    /** 问诊单id */
    inquiryId: string;
    /** 会话持续时间，到时间会自动结束问诊 */
    duration: number;
}

export interface JChatMessageModel {
    content: string;
    contentType?: ServiceSysStatus
    conversationId: string;
    fromImUserId: string;
    fromUserId: string;
    fromUserType: number;
    id: string;
    msgId: string;
    msgKey: string;
    msgRandom: string;
    msgSeq: string;
    msgTime: number;       // 时间戳（秒级）
    origin?: string // 图片的相对路径
    originCdn?: string
    sendMsgResult: number; // 发送结果状态码
    toImUserId: string;
    toUserId: string;
    toUserType: number;
    type: JMessageType;          // 消息类型字面量
    img: string, // 消息对应的头像
}

type JMessageType = "TEXT" | "IMG" | 'PRES_CARD' | 'FORMULARY_CARD' | 'SYS';
