import BlankLayout from '@/layout/BlankLayout.vue'
import { RoutesName } from "@/enums/routes";
import type { RouteLocation } from "vue-router";
export const Default = {
  [RoutesName.StoreModule]: {
      path: "/store",
      component: BlankLayout,
      meta: {
        title: "商城",
      },
  },
  [RoutesName.Home]: {
    path: "home",
    component: () => import("@/views/StoreModule/Home/index.vue"),
    meta: {
      title: "首页",
      icon: 'home'
    }
  },
  [RoutesName.GoodsManagement]: {
    path: "goodsManagement",
    component: () => import("@/views/StoreModule/GoodsManagement/index.vue"),
    meta: {
      title: "商品管理",
      icon: 'commodity'
    },
    props:(route:RouteLocation) => ({ 
      goodsName: route.query.goodsName || '', // 商品名
      isPublish: route.query.isPublish || '', // 是否上架 0: 否  1：是
      isSellout: route.query.isSellout || '', // 是否售罄 0: 未售罄 1: 已售罄
    })
  },
  [RoutesName.SupplierProductManagement]: {
    path: "supplierProduct",
    component: () => import("@/views/StoreModule/SupplierProductManagement/index.vue"),
    meta: {
      title: "供应商商品",
      icon: 'commodity'
    },
    props:(route:RouteLocation) => ({ 
      goodsName: route.query.goodsName || '', // 商品名
      isPublish: route.query.isPublish || '', // 是否上架 0: 否  1：是
      isSellout: route.query.isSellout || '', // 是否售罄 0: 未售罄 1: 已售罄
    })
  },
  [RoutesName.CustomerManagement]: {
    path: "customerManagement",
    component: () => import("@/views/StoreModule/CustomerManagement/index.vue"),
    meta: {
      title: "会员管理",
      icon: 'user-menu'
    },
  },
  [RoutesName.OrderManagement]: {
    path: "orderManagement",
    component: () => import("@/views/StoreModule/OrderManagement/index.vue"),
    meta: {
      title: "订单管理",
      icon: 'order'
    },
    props:(route:RouteLocation) => ({ 
      presId: route.query.presId || '', // 处方id
      type:route.query.type || '', // 处方类型
      pathValue:route.query.pathValue || '', // 路径值
      tabValue:route.query.tabValue || '', // tab值
    })
  },
  [RoutesName.SalesManagement]: {
    path: "salesManagement",
    component: () => import("@/views/StoreModule/SalesManagement/index.vue"),
    meta: {
      title: "营销管理",
      icon: 'marketing'
    },
  },
  [RoutesName.AfterServiceManagement]: {
    path: "afterserviceManagement",
    component: () => import("@/views/StoreModule/AfterServiceManagement/index.vue"),
    meta: {
      title: "售后管理",
      icon: 'after-sale'
    },
  },
  [RoutesName.DataReports]: {
    path: "dataReports",
    component: () => import("@/views/StoreModule/DataReports/index.vue"),
    meta: {
      title: "数据报表",
      icon: 'data-report'
    },
  },
  [RoutesName.StoreConfig]:{
    path: "storeConfig",
    component: () => import("@/views/StoreModule/StoreConfig/index.vue"),
    meta: {
      title: "商城配置",
      icon: 'store-config'
    },
    props:(route:RouteLocation) => ({ 
      configurationAddressStatus: route.query.configurationAddressStatus || 0, // 处方id
    })
  },
  [RoutesName.SupplierManagement]: {
    path: "supplierManagement",
    component: () => import("@/views/StoreModule/SupplierManagement/index.vue"),
    meta: {
      title: "供应商管理",
      icon: 'supplier-icon'
    },
  },
};
