import { defHttp } from "@/services";

/** 商品统计 */
export const enum GoodsReportsApi {
  produceStatData = "/report/produceStatData",
  produceStatDataExport = "/report/produceStatDataExport",
  // 商品销售榜
  produceSaleStatData = "/report/produceSaleStatData"
}

/** 商品销售榜 */
export function getProduceSaleStatData(params) {
  return defHttp.post({
    url: GoodsReportsApi.produceSaleStatData,
    params,
  });
}

/** 获取商品统计 */
export function getProduceStatData(params) {
  return defHttp.post({
    url: GoodsReportsApi.produceStatData,
    params,
  });
}

/** 商品统计导出 */
export function produceStatDataExport(params) {
  return defHttp.post({
    url: GoodsReportsApi.produceStatDataExport,
    requestConfig: {
      responeseType: "stream",
    },
    params,
  });
}
