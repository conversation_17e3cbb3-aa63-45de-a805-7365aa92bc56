import { ref } from "vue";
import type { TreeOption } from "naive-ui";
import { useLoading, useMessages } from "@/hooks";
import { SystemSetting } from "@/settings/systemSetting";
import { distributorPageForUserModule, structureListForUserModule } from "@/services/api";

export default function useGetSgDealer() {
    const messages = useMessages();

    /** 分销员列表 */
    const distributorList = ref([]);

    /** 分销员树 */
    const distributorsTree = ref([])

    const searchValue = ref(null);

    const structPathList = ref(null)

    /** 分销员列表参数 */
    const _params = {
        data: {
            idList: null,
            structPathList: null,
            searchValue:null
        },
        pageVO: {
            current: 1,
            size: SystemSetting.pagination.pageSize,
        },
    };
    /** 总记录数 */
    let recordsTotal = 1;

    const { loading } = useLoading();
    const { loading: isGetLoading, startLoading: startGetLoading, endLoading: endGetLoading } = useLoading();
    /** 获取分销员列表 */
    const getSgDealerInfoList = async () => {
        try {
            startGetLoading();
            _params.data.searchValue = searchValue.value;
            _params.data.structPathList = structPathList.value;
            const { total, current, size, records } = await distributorPageForUserModule(_params);
            _params.pageVO.current = Number(current);
            _params.pageVO.size = Number(size);
            recordsTotal = Number(total);
            if (records && _params.pageVO.current === 1) {
                distributorList.value = [...records];
            } else {
              records.forEach(item => {
                distributorList.value.push(item);
              });
            }
        } catch (error) {
            messages.createMessageError("获取分销员列表失败" + error);
        } finally {
            endGetLoading();
        }
    };

    /** 滚动加载 */
    const handleScroll = (e) => {
    	const currentTarget = e.currentTarget as HTMLElement;
    	if (
    		currentTarget.scrollTop + currentTarget.offsetHeight >=
    		currentTarget.scrollHeight
    	) {
    		if (_params.pageVO.current * _params.pageVO.size < recordsTotal) {
    			_params.pageVO.current++;
    			getSgDealerInfoList();
    		}
    	}
    };

    /** 加载distributorsTree */
    const handleLoad = (node) => {
        return new Promise<void>(async(resolve) => {
            node.children = await getChild(node)
            resolve()
        })
      }

    //请求父级
    const getStructureListStructures = async() =>{
        const notGrouped = [{ label: '未分组', key: 'noStruct',path:'noStruct', }];
        loading.value = true
        const params = {
            data:{
               parentCode:0,
            }
        }
        try{
          const res = await structureListForUserModule(params)
          distributorsTree.value = processData(res);
          distributorsTree.value = [...notGrouped, ...distributorsTree.value]
        }catch(err){
            messages.createMessageError('获取组织架构列表失败:' + err)
        }finally{
            loading.value = false
        }
    }
    
    //请求子级
    const getChild = async (option) => {
      const params = {
        data: {
          parentCode: option.code,
        },
      };
      try {
        const res = await structureListForUserModule(params); 
        const children = []
        res.forEach(item => {
            children.push ( {
            label: item.name, // 替换 name 为 label
            key: item.id, // 替换 parentCode 为 key
            isLeaf: !item.hasChildren,
            depth: option.depth + 1,
            code:item.code,
            path:item.path
           })
        }); 
        return children
      } catch (err) {
        messages.createMessageError("获取组织架构列表失败: " + err);
        return []; 
      }
    };

    // 数据处理函数
    const processData = (data) => {
        return data.map(item => {
          const result = {
            label: item.name, // 替换 name 为 label
            key: item.id, // 替换 parentCode 为 key
            isLeaf: !item.hasChildren, //是否有子级请求
            code:item.code,
            path:item.path,
            depth: 1,
          };
          return result;
        });
    };

    /** 分销员选中tree */
    const handleCheckedKeysChange = (value,label) =>{
        structPathList.value = label.map(item => item.path);
        getSgDealerInfoList();
    }

    return {
        loading,
        isGetLoading,
        distributorList,
        searchValue,
        _params,
        structPathList,
        distributorsTree,
        getSgDealerInfoList,
        handleScroll,
        handleLoad,
        getStructureListStructures,
        handleCheckedKeysChange
    };
}
