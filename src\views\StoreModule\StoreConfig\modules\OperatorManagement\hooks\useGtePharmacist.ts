import { ref } from "vue";
import { useLoading, useMessages } from "@/hooks";
import { SystemSetting } from "@/settings/systemSetting";
import { pharmacistEntityPageUnauthorized } from "@/services/api";

export default function useGteDoctor() {
  const messages = useMessages();

  /** 药师列表 */
  const pharmacistList = ref([]);

  const searchValue = ref(null);

  const structPathList = ref(null);

  /** 药师列表参数 */
  const _params = {
    data: {
      pharmacistName: "",
    },
    pageVO: {
      current: 1,
      size: 10,
    },
  };
  /** 总记录数 */
  let recordsTotal = 1;

  const { loading } = useLoading();
  const { loading: isGetLoading, startLoading: startGetLoading, endLoading: endGetLoading } = useLoading();
  /** 获取药师列表 */
  const getPharmacistList = async () => {
    try {
      startGetLoading();
      _params.data.pharmacistName = searchValue.value;
      const { total, current, size, records } = await pharmacistEntityPageUnauthorized(_params);
      _params.pageVO.current = Number(current);
      _params.pageVO.size = Number(size);
      recordsTotal = Number(total);
      if (records && _params.pageVO.current === 1) {
        pharmacistList.value = [...records];
      } else {
        records.forEach(item => {
          pharmacistList.value.push(item);
        });
      }
    } catch (error) {
      messages.createMessageError("获取药师列表失败" + error);
    } finally {
      endGetLoading();
    }
  };

  /** 滚动加载 */
  const handleScroll = e => {
    const currentTarget = e.currentTarget as HTMLElement;
    if (currentTarget.scrollTop + currentTarget.offsetHeight >= currentTarget.scrollHeight) {
      if (_params.pageVO.current * _params.pageVO.size < recordsTotal) {
        _params.pageVO.current++;
        getPharmacistList();
      }
    }
  };

  return {
    loading,
    isGetLoading,
    pharmacistList,
    searchValue,
    _params,
    structPathList,
    getPharmacistList,
    handleScroll,
  };
}
