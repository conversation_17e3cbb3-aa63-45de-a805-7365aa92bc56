import { defHttp } from "@/services";

/** 财务管理之轨迹查询余额 */
export const enum UserRechargeApi {
  // 获取充值记录列表-分页查询
  rechargeRecordGetPage = "/rechargeRecord/getPage",
  // 获取用户支出记录列表-分页查询
  expenseRecordGetPage = "/expenseRecord/getPage",
  // 获取用户账户余额列表-分页查询
  userAccountGetPage = "/userAccount/getPage",
  // 开通用户充值账户
  createAccount = "/userAccount/createAccount",
  // 线下充值
  offlineRecharge = "/userAccount/offlineRecharge",
  // 线下退款（扣减用户账户余额）
  offlineRefund = "/userAccount/offlineRefund",
}

export function rechargeRecordGetPage(params = {}) {
  return defHttp.post({
    url: UserRechargeApi.rechargeRecordGetPage,
    params,
  });
}

export function expenseRecordGetPage(params = {}) {
  return defHttp.post({
    url: UserRechargeApi.expenseRecordGetPage,
    params,
  });
}

export function userAccountGetPage(params = {}) {
  return defHttp.post({
    url: UserRechargeApi.userAccountGetPage,
    params,
  });
}

export function createAccount(id:string) {
  return defHttp.post({
    url: `${UserRechargeApi.createAccount}?accountUserId=${id}`,
  });
}

export function offlineRecharge(params = {}) {
  return defHttp.post({
    url: UserRechargeApi.offlineRecharge,
    params,
  });
}

export function offlineRefund(params = {}) {
  return defHttp.post({
    url: UserRechargeApi.offlineRefund,
    params,
  });
}