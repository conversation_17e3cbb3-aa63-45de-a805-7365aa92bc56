<template>
    <JDrawer
      v-model:show="drawerVisible"
      title="入账方基本信息"
      :isShowFooter="false"
      :isGetLoading="isGetLoading"
      @after-leave="closeDrawer"
      to="#subAccountPayeeManagement"
      :contents-list="[
        {
          name: '',
          slotName: 'basic_information'
        },
        {
          name: '银行卡',
          slotName: 'bank_cards'
        },
        {
          name: '绑定社群端经销商账号',
          slotName: 'reseller_account_number'  
        }
      ]"
    >
     <template #basic_information>
        <n-form
          :model="model"
          label-width="100"
          label-placement="left"
          require-mark-placement="right-hanging"
          size="small"
          :style="{
            width: '100%',
          }"
          id="orderInfo"
        >
          <n-grid cols="6 m:18 l:18 xl:24" :x-gap="24" responsive="screen">
            <!-- 第一列 -->
            <n-gi :span="12">
              <!-- 入账方ID -->
              <n-form-item-gi :span="12" label="入账方ID">
                <n-input-group>
                  <n-input 
                   v-model:value="model.accountCode"
                   readonly 
                   placeholder='无'/>
                </n-input-group>
              </n-form-item-gi>

              <!-- 入账方ID -->
              <n-form-item-gi :span="6" label="开户类型">
                <n-select 
                  v-model:value="model.cleanType" 
                  :options="accountOpeningOptions" 
                  placeholder='无'
                  style="width: 100%;"
                  clearable
                  disabled
                />
              </n-form-item-gi>

              <!-- 入账方ID -->
              <n-form-item-gi :span="12" label="开户名称">
                <n-input-group>
                  <n-input 
                   v-model:value="model.alias"
                   readonly 
                   placeholder='无'/>
                </n-input-group>
              </n-form-item-gi>

              <!-- 入账方ID -->
              <n-form-item-gi :span="12" label="联系人手机号">
                <n-input-group>
                  <n-input 
                   v-model:value="model.mobile"
                   readonly 
                   placeholder='无'/>
                </n-input-group>
              </n-form-item-gi>

              <!-- 状态 -->
              <n-form-item-gi :span="12" label="状态">
                <n-select 
                 v-model:value="model.state" 
                 :options="SubAccountPayeeManagementStateOptions" 
                 placeholder='无'
                 style="width: 100%;"
                 clearable
                 disabled
                />
              </n-form-item-gi>
            </n-gi>
          </n-grid>
        </n-form>
     </template>
     <template #bank_cards>
        <FormLayout
          style="width: 98%;height: 300px;"
          :tableData="tableBankCardsData" 
          :tableColumns="tableBankCardsColumns"
          :is-table-pagination="false"
          :is-table-selection="false"
          :is-display-header="false"
        >
        </FormLayout>
     </template>
     <template #reseller_account_number>
        <div style="display: flex;
             justify-content: end;
             padding: 0px 3%;
             margin-bottom: 10px;">
            <n-button text type="info" @click="handleAddDealerData">
               新增
             </n-button>
        </div>
        <FormLayout
          style="width: 98%;height: 300px;"
          :tableData="model.dealerList" 
          :tableColumns="tableDealerColumns"
          :is-table-pagination="false"
          :is-table-selection="false"
          :is-display-header="false"
        >
        </FormLayout>
     </template>
    </JDrawer>
    <dealerModal ref="dealerModalShow" 
    :accountId="model.id"
    :accountCode="model.accountCode"
    :callAddState="true"
    :detailsAddState="true"
    @dealer-ids="handleDealerIds" 
    @dealer-list="handleDealerList"/>
</template>
  
<script setup lang="tsx" name="breakoutDetails">
import { ref, reactive } from "vue";
import { useMessages } from "@/hooks";
import { deepClone } from "@/utils";
import FormLayout from "@/layout/FormLayout.vue";
import dealerModal from './dealerModal.vue'
import { allocationAccountGetDetail,allocationAccountCardUpdateIsDefault,allocationAccountUserDeleteById} from "@/services/api";
import { accountOpeningOptions ,SubAccountPayeeManagementStateOptions,bankCardBindingLabels } from "@/constants";
import Popconfirm from '@/components/Popconfirm/index.vue'

/** 请求数据状态 */
const isGetLoading = ref(false)

/* 表单参数初始化 */
const initParams = {
    id:null,
    accountCode:null,
    cleanType:null,//开户类型
    alias:null,//开户名称
    mobile:null,//联系人手机号
    state:null,//状态
    dealerIds:[],//经销商ids
    dealerList:[],//经销商list
}

const model = ref(deepClone(initParams));

const { createMessageSuccess, createMessageError,createMessageWarning } = useMessages();

/* 绑定社群端经销商账号 -- 表格项 */
const tableDealerColumns = reactive([
    {
      title: "经销商姓名",
      key: "dealerName",
      align: "left",
      fixed: "left",
      width: 300,
      render: (row) => {
        return row.dealerName || "-"; 
      }
    },
    {
      title: "经销商id",
      key: "accountUserId",
      align: "left",
      width: 300,
      render: (row) => {
        return row.accountUserId || "-"; 
      }
    },
    {
      title: "操作",
      key: "action",
      align: "left",
      fixed: "right",
      width: 100,
      render: (row) => {
        return (
                <n-space>
                    <Popconfirm 
                      onHandleClick={() =>clickDelete(row.accountUserId)} 
                      loading={isDeleteLoading.value} 
                      disabled={isDeleteDisabled.value}
                      promptContent={'是否确定删除该数据?'}/>
                </n-space>
            );
      }
    }
]) 

const tableBankCardsData = ref([])

/* 银行卡 -- 表格项 */
const tableBankCardsColumns = reactive([
    {
      title: "账户名称",
      key: "outAccountName",
      align: "left",
      fixed: "left",
      width: 300
    },
    {
      title: "账号",
      key: "outAccountNo",
      align: "left",
      width: 300,
    },
    {
      title: "开户行",
      key: "bankName",
      align: "left",
      width: 300,
    },
    {
      title: "绑卡状态",
      key: "state",
      align: "left",
      width: 300,
      render:(row) => {
        return bankCardBindingLabels[row.state] ? bankCardBindingLabels[row.state] : '-'
      }
    },
    {
      title: "操作",
      key: "action",
      align: "left",
      fixed: "right",
      width: 100,
      render: (row,index) => {
        return (
                <n-space>
                    {
                      <n-switch value={row.isDefault == 1} 
                       onUpdate:value={(value: boolean) => handleUpdate(row,value,index)}
                       disabled={switchDisabled.value || tableBankCardsData.value.length == 1}
                       loading={switchLoading.value}
                       />}
                </n-space>
            );
      }
    }
]) 

/** 抽屉状态 */
const drawerVisible = ref(false);

/** params */
const drawerParams = ref(null)

/* 接收父组件传过来的参数 */
const acceptParams = async(params) => {
  drawerVisible.value = true;
  drawerParams.value = params
  allocationAccountGetDetailApi(drawerParams.value.row?.id)
};

/** 关闭抽屉 */
const closeDrawer = () => {
    model.value = deepClone(initParams);
    drawerVisible.value = false;
};

/** 绑定社群端经销商账号 -- 删除 */
const isDeleteLoading = ref(false)
const isDeleteDisabled = ref(false)
const clickDelete = async(id) =>{
  isDeleteLoading.value = true
  isDeleteDisabled.value = true
  try{
    await allocationAccountUserDeleteById(id)
    const index = model.value.dealerIds.indexOf(id); // 找到目标 id 的索引
    if (index !== -1) {
      model.value.dealerIds.splice(index, 1); // 删除目标 id
    }
    model.value.dealerList = model.value.dealerList.filter(dealer => dealer.accountUserId !== id);
    createMessageSuccess("删除经销商账号成功")
  }catch(err){
    createMessageError("删除经销商账号失败:" + err)
  }finally{
    isDeleteLoading.value = false
    isDeleteDisabled.value = false
  }
  
}

/** 银行卡操作开关 */
const switchDisabled = ref(false)
const switchLoading = ref(false)
const handleUpdate = async(row,value,index) =>{
  switchDisabled.value = true
  switchLoading.value = true
  const param = {
    data:{
      id:row.id,
      accountId:model.value.id,
      isDefault:value ? 1 : 0
    }
  }
  try{
    await allocationAccountCardUpdateIsDefault(param)
    tableBankCardsData.value[index].isDeleted = value ? 1 : 0;
    tableBankCardsData.value[index].state = value ? 1 : 0;
    if(value){

    }
    createMessageSuccess("启动银行卡成功")
  }catch(err){
    createMessageError("启动银行卡失败:" + err)
  }finally{
    switchDisabled.value = false
    switchLoading.value = false
  }
}

/** handleDealerIds */
const handleDealerIds = (value) =>{
  model.value.dealerIds = value
}

/** handleDealerList */
const handleDealerList = (value) =>{
  model.value.dealerList = value 
}

/** 经销商模态框 */
const dealerModalShow = ref()
const handleAddDealerData = () =>{
    const params = {
      ids:model.value.dealerIds,
      list:model.value.dealerList
    }
    dealerModalShow.value.acceptParams(params)
}

/** 获取详情数据 */
const allocationAccountGetDetailApi = async(id) =>{
  isGetLoading.value = true
  try{
   const res = await allocationAccountGetDetail(id)
   const {accountCode, cleanType, alias, state, allocationAccountCardInfos,allocationAccountUserInfos} = res
   // 直接赋值的字段
   Object.assign(model.value, {
    accountCode,
    cleanType:cleanType != undefined ?  Number(cleanType.replace(/^0+/, '')) : '',
    alias,
    state,
    });
    tableBankCardsData.value = allocationAccountCardInfos
    model.value.id = res.id
    if(allocationAccountCardInfos.length > 0){
      model.value.mobile = allocationAccountCardInfos[0].mobile
    }
    if(allocationAccountUserInfos?.length != 0 && allocationAccountUserInfos){
      allocationAccountUserInfos.forEach((item)=>{
        model.value.dealerIds.push(item.accountUserId) 
      })
      allocationAccountUserInfos.forEach((item)=>{
        model.value.dealerList.push(item) 
      })
    }
   
  }catch(err){
    createMessageError('获取详情数据失败:' + err)
  }finally{
    isGetLoading.value = false
  }
}

defineExpose({
  acceptParams,
  closeDrawer,
  drawerVisible
});
</script>
  
<style scoped lang="less">
@import "@/styles/default.less";
@import "@/styles/defaultVar.less";
.wrapper {
  width: 100%;
  height: 100%;
  background-color: #f2f3f5;

  .publics-form {
    width: 100%;
    height: calc(100% - 52px);
    box-sizing: border-box;
    background-color: #fff;
    .order-info,
    .user-info,
    .order-info,
    .order-item {
      padding: 12px 24px;
    }
    .title-wrapper {
      height: 30px;
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: 700;
      margin-bottom: 12px;
      .title-line {
      	width: 4px;
      	height: 60%;
      	background-color: @primary-color;
      	margin-right: 5px;
      }
    }
  }
}

:deep(.n-form-item-label){
    width: 150px !important;
}
</style>
  