import { defHttp } from "@/services";

/** 直播列表 */
export const enum LiveListApi {
  page = "/liveActivity/page",
  addLive = "/liveActivity/add",
  getIframeEmbeddingUrl = "/liveActivity/getIframeEmbeddingUrl",
  getStreams = "/liveActivity/getStreams",
  getShare = "/liveActivity/getShare",
  updateLive = "/liveActivity/update",
}

/**
 * @description 直播列表分页查询
 */
export function getLivePage(params) {
  return defHttp.post({
    url: LiveListApi.page,
    params,
  });
}
/**
 * @description 创建直播间
 */
export function addLiveRoom(_params) {
  return defHttp.post({
    url: LiveListApi.addLive,
    params: {
      data: _params,
    },
  });
}
/**
 * @description 获取Iframe 嵌入链接
 */
export function getIframeEmbeddingUrl(params) {
  return defHttp.get({
    url: LiveListApi.getIframeEmbeddingUrl,
    params,
  });
}
/**
 * @description 获取推流直播和拉流直播信息
 */
export function getStreams(params) {
  return defHttp.get({
    url: LiveListApi.getStreams,
    params,
  });
}
/**
 * @description 编辑直播
 */
export function updateLive(_params) {
  return defHttp.put({
    url: LiveListApi.updateLive,
    params: {
      data: _params,
    },
  });
}
/**
 * @description 获取分享
 */
export function getShare(params) {
  return defHttp.get({
    url: LiveListApi.getShare,
    params,
  });
}
