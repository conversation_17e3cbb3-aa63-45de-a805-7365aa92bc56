<template>
  <div class="wrapper inner-page-height">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :pagination="paginationRef"
      @paginationChange="paginationChange"
      :isNeedCollapse="false"
      :isDisplayIndex="false"
    >
      <!-- 表单 -->
      <template #searchForm>
        <n-form
          ref="formRef"
          :model="formValue"
          label-placement="left"
          label-width="auto"
          :show-feedback="false"
          require-mark-placement="right-hanging"
          size="small"
          :style="{ width: '100%' }"
        >
          <n-form-item label="">
            <n-input-group>
              <n-select
                v-model:value="formValue.searchType"
                placeholder="请选择"
                :options="searchTypeOptions"
                style="width: 120px"
              />
              <j-search-input
                v-model:value="formValue.searchValue"
                placeholder="请输入"
                @search="handlerSearch"
                :width="240"
              />
            </n-input-group>
          </n-form-item>
          <n-form-item label="提现状态">
            <n-select
              v-model:value="formValue.status"
              placeholder="请选择"
              :options="settlementStatusOptions"
              @update:value="tableSearch"
              style="width: 120px"
              clearable
            />
          </n-form-item>

          <n-form-item label="创建时间">
            <j-date-range-picker
              v-model:value="formValue.creationTime"
              type="datetimerange"
              format="yyyy-MM-dd"
              :default-time="['00:00:00', '23:59:59']"
              clearable
            />
          </n-form-item>
        </n-form>
      </template>

      <template #tableHeaderBtn>
        <n-button @click="refresh" :loading="isLoading" class="store-button">刷 新</n-button>
      </template>
      <!-- 多选按钮 -->
      <template #tableFooterBtn="scope">
        <!-- 批量审核通过 -->
        <Popconfirm
          @handleClick="batchApprove(scope.selectedListIds, scope.selectedList)"
          :loading="isBatchLoading"
          buttonContent ="批量审核通过"
          type="primary"
          :text="false"
          ghost
          size="small"
          promptContent='此操作将对所有的选中执行审核通过操作，是否继续？'
        />
        
        <!-- 批量打款 -->
        <Popconfirm
          @handleClick="batchPayout(scope.selectedListIds, scope.selectedList)"
          :loading="isBatchLoading"
          buttonContent ="批量打款"
          type="success"
          :text="false"
          ghost
          size="small"
          promptContent='此操作将对所有的选中执行打款操作，是否继续？'
        />
      </template>
    </FormLayout>
    <rejectDialog v-model:show="showRejectDialog" :rejectId="rejectId" @success="refresh"></rejectDialog>
    <revenueDetail v-model:show="showRevenueDetail" :detailData="detailData"></revenueDetail>
  </div>
</template>

<script lang="tsx" setup name="DoctorManagement">
import { onMounted, ref, watch } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { useMessages } from "@/hooks";
import moment from "moment";
import { payoutAuditPage, paymentAuditGet,paymentAuditPass, paymentAuditPayout, type PaymentAuditDTO } from "@/services/api";
import TablePreview from "@/components/TablePreview/index.vue";
import rejectDialog from "./components/rejectDialog.vue";
import revenueDetail from "./components/revenueDetail.vue";
const { createMessageSuccess, createMessageError } = useMessages();
import { hasFinancePaymentReviewDetailAuth, hasFinancePaymentReviewReviewAuth, hasFinancePaymentReviewRejectAuth, hasFinancePaymentReviewPayoutAuth } from "../authList";
import Popconfirm from '@/components/Popconfirm/index.vue'
/** 表格hook */
const { isLoading, tableData, paginationRef, pageTableData, paginationChange } = useTableDefault({
  pageDataRequest: payoutAuditPage,
});

const detailData = ref<PaymentAuditDTO>({});

/** 搜索类型 */
const searchTypeOptions = [
  {
    label: "用户ID",
    value: "userId",
  },
  {
    label: "申请单号",
    value: "applicationNo",
  }
];

/** 结算状态选项 */
const settlementStatusOptions = [
  {
    label: "待审核",
    value: 1,
  },
  {
    label: "待打款",
    value: 2,
  },
  {
    label: "打款中",
    value: 3,
  },{
    label: "打款失败",
    value: 4,
  },{
    label: "已打款",
    value: 5,
  },{
    label: "已驳回",
    value: 6,
  }
];
const orderStatus = ["待审核", "待打款", "打款中", "打款失败", "已打款", "已驳回"];
/** 参数 */
const formValue = ref({
  searchValue: "",
  searchType: "userId",
  orderCode:"",
  creationTime: null,
  status: null,
  type: null
});
/* 表格列表项 */
const tableColumns = ref([
  {
    title: "申请单号",
    key: "applicationNo",
    width: 200,
    align: "left",
  },
  {
    title: "申请来源",
    key: "applicationSource",
    width: 180,
    align: "left",
    render: row => {
      return <div>{row.applicationSource == 1 ? "用户提现" : "-"}</div>;
    },
  },
  {
    title: "用户ID",
    key: "userShortId",
    align: "left",
  },
  {
    title: "用户昵称",
    key: "userNickname",
    align: "left",
  },
  {
    title: "打款金额",
    key: "amount",
    align: "left",
    render: row => {
      return <div>{row.amount ? (row.amount/100).toFixed(2) : '0.00'}</div>;
    },
  },
  {
    title: "打款状态",
    key: "status",
    align: "left",
    render: row => {
      return<n-tag 
            bordered={false} 
            size="small" 
            type={row.status == 1 ? "warning" : row.status == 6 || row.status == 4 ? "error" : "success"}
          >
          {orderStatus[Number(row.status) - 1]}
          </n-tag>
      
    },
  },
  {
    title: "创建时间",
    key: "createTime",
    align: "left",
  },
  {
    title: "操作",
    key: "action",
    width: 180,
    fixed: "right",
    align: "left",
    render: row => {
      return (
        <n-space style="padding: 5px 0;">
          {hasFinancePaymentReviewDetailAuth ? (
            <n-button text type="primary" onClick={() => handlerDetailDrawerClick(row)}>
              详情
            </n-button>
          ) : null}
          {hasFinancePaymentReviewReviewAuth && row.status == 1 ? (
            <Popconfirm
              onHandleClick={() => batchApprove([row.id],[row])}
              loading={isBatchLoading.value}
              type={'primary'}
              promptContent={"是否确定审核通过?"}
              buttonContent={"审核通过"}
            />
          ) : null}
          {hasFinancePaymentReviewPayoutAuth && (row.status == 2 || row.status == 4) ? (
            <Popconfirm
              onHandleClick={() => batchPayout([row.id],[row])}
              loading={isBatchLoading.value}
              type={'primary'}
              promptContent={"是否确定打款?"}
              buttonContent={"打款"}
            />
          ) : null}
          {hasFinancePaymentReviewRejectAuth && (row.status == 1 || row.status == 2 || row.status == 4) ? (
            <n-button text type="primary" onClick={() => handlerRefund(row)}>
              驳回
            </n-button>
          ) : null}
        </n-space>
      );
    },
  },
]);
watch([() => formValue.value.creationTime], () => {
  tableSearch();
});
/** 获取参数 */
const getParams = () => {
  const { searchType, searchValue, creationTime, status, type } = formValue.value;
  const startTime = creationTime ? moment(creationTime[0]).format(`YYYY-MM-DD HH:mm:ss`) : null;
  const endTime = creationTime ? moment(creationTime[1]).format(`YYYY-MM-DD HH:mm:ss`) : null;
  return {
    // searchType,
    // searchValue,
    applicationNo:searchType == "applicationNo" ? searchValue : null,
    userId:searchType == "userId" ? searchValue : null,
    startTime,
    endTime,
    status,
    type
  };
};

/** 搜索 */
const handlerSearch = () => {
  tableSearch();
};

/** 表格刷新 */
function refresh() {
  tableSearch();
}

/* 表格搜索 */
const tableSearch = () => {
  pageTableData(getParams(), paginationRef.value);
};

/** 批量审核通过 */
const isBatchLoading = ref(false);
const batchApprove = (selectedListIds, selectedList) =>{
  const selectIds = selectedList.filter(item => item['status'] == 1).map(item => item['id']);
  if (selectIds.length == 0) {
    createMessageError('请选择待审核状态的记录');
    return;
  }
  isBatchLoading.value = true;
  paymentAuditPass({data:selectIds}).then(() => {
    createMessageSuccess('操作成功');
    refresh();
  }).catch(() => {
    createMessageError('操作失败');
  }).finally(() => {
    isBatchLoading.value = false;
  })
}

/** 批量打款 */
const batchPayout = (selectedListIds, selectedList) =>{
  const selectIds = selectedList.filter(item => item['status'] == 2 || item['status'] == 4).map(item => item['id']);
  if (selectIds.length == 0) {
    createMessageError('请选择待打款状态的记录');
    return;
  }
  isBatchLoading.value = true;
  paymentAuditPayout({data:selectedListIds}).then(() => {
    createMessageSuccess('操作成功');
    refresh();
  }).catch((err) => {
    createMessageError(`操作失败:${err}`);
  }).finally(() => {
    isBatchLoading.value = false;
  })
}

/** 驳回id */
const rejectId = ref(null);
const handlerRefund = (row) => {
  rejectId.value = row.id;
  showRejectDialog.value = true;
};

const showRejectDialog = ref(false);
const showRevenueDetail = ref(false);

/** 打开详情 */
const handlerDetailDrawerClick = row => {
  isLoading.value = true;
  paymentAuditGet({ id: row.id }).then(res => {
    detailData.value = res;
    showRevenueDetail.value = true;
  }).catch(err => {
    createMessageError(`获取详情失败:${err}`);
  }).finally(() => {
    isLoading.value = false;
  });
};


/** 组件挂载 */
onMounted(() => {
  tableSearch();
});
</script>

<style lang="less" scoped>
@import "@/styles/defaultVar.less";
</style>
