import { fileUpload } from "@/services/api"
import { useMessages } from "@/hooks";
const { createMessageSuccess, createMessageError } = useMessages();

export function userGetCoverImg() {

    const getCoverImgFile = (url: string) => {
        return new Promise(function (resolve, reject)  {
            try {
                let dataURL = "";
                let video = document.createElement("video");
                video.setAttribute("crossOrigin", "anonymous"); //处理跨域
                video.setAttribute("src", url);
                // video.setAttribute("width", '400');
                // video.setAttribute("height", '240');
                video.setAttribute("preload", "auto");
                // 捕获加载视频时的错误事件
            video.addEventListener("error", function () {
                reject("视频加载错误");
            });
                video.addEventListener("loadeddata", function () {
                    let canvas = document.createElement("canvas");
                    const width = video.videoWidth;  // 视频的真实宽度
                    const height = video.videoHeight; // 视频的真实高度
                    canvas.width = width;
                    canvas.height = height;
                    canvas.getContext("2d").drawImage(video, 0, 0, width, height); //绘制canvas
                    dataURL = canvas.toDataURL("image/jpeg"); //转换为base64
                    resolve(base64ToFile(dataURL))
                });
            } catch (error) {
                reject(error)
            }
        })
    }

    function base64ToFile(base64: string, filename: string = "coverImg.jpeg"): File {
        // 将 Base64 头部移除，如果有的话
        const arr = base64.split(',');
        const mime = arr[0].match(/:(.*?);/)?.[1] || '';
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);

        // 将 Base64 字符串转换为二进制数据
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n);
        }

        // 创建一个 File 对象
        return new File([u8arr], filename, { type: mime });
    }

    const getCoverImg = (videoUrl: string) :Promise<string> => {
        return new Promise(async (resolve, reject) => {
            try {
                // 调用 getCoverImgFile，等待其结果
                const imgFile = await getCoverImgFile(videoUrl);
                // 如果成功，处理文件上传
                const formData = new FormData();
                formData.append('files', imgFile as File);
                // 上传文件并处理结果
                fileUpload(false, formData).then(res => {
                    resolve(res[0]);
                }).catch(err => {
                    console.log(err);
                    createMessageError("上传视频封面失败: " + err);
                    reject(err);
                });
    
            } catch (err) {
                // 如果 getCoverImgFile 失败，直接 reject
                console.log(err);
                createMessageError("获取视频封面失败: " + err);
                reject(err);
            }
        });
    };
    

    return {
        getCoverImg
    }
}