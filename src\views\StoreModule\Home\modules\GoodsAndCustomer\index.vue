<template>
  <NGrid cols="s:1 m:2 l:3" responsive="screen" :x-gap="28" :y-gap="16">
    <NGridItem span="s:1 m:2 l:2">
      <NCard :bordered="false" style="border-radius: 16px;">
        <NSpin :show="loading" size="small">
          <NSpace vertical :size="20">
            <NFlex align="center" :size="2">
              <span class="title">商品管理</span>
            </NFlex>
            <NGrid cols="s:1 m:2 l:2 xl:3" responsive="screen" :x-gap="28" :y-gap="16">
              <NGi v-for="item in dataPanel" :key="item.title">
                <NCard
                  :bordered="false"
                  :style="{ height: '182px', backgroundImage: `url(${cardBg})`, backgroundSize: '100% 100%' }"
                >
                  <div class="container">
                    <img class="data-icon" :src="item.iconImg" alt="" />
                    <div class="data-content">
                      <span class="data-title" @click="jumpTo(item.isAuth , item.path, item.query )">
                        {{ item.title }}
                      </span>
                      <div class="data-num" :style="{ color: item.numColor }">
                        <NNumberAnimation :from="0" :to="Number(item.num)" />
                      </div>
                    </div>
                  </div>
                </NCard>
              </NGi>
            </NGrid>
          </NSpace>
        </NSpin>
      </NCard>
    </NGridItem>
    <NGridItem span="s:1 m:2 l:1">
      <NCard :bordered="false" style="border-radius: 16px; height: 100%;">
        <NSpin :show="customerLoading" size="small">
          <NSpace vertical :size="12">
            <NFlex align="center" :size="2">
              <span class="title">客户管理</span>
            </NFlex>
            <!-- 数据 -->
            <NGrid cols="m:2 l:1 xl:2" responsive="screen" :x-gap="2" :y-gap="2">
              <NGi>
                <div ref="pieRef" style="height: 190px;"></div>
              </NGi>
              <NGi>
                <div class="customer-num">
                  <div v-for="item in customerDataPanel" :key="item.title" class="customerNumItem">
                    <div class="numItemleft">
                      <div class="numItemIcon" :class="`${item.className}`"></div>
                      <span class="numItem-title">{{ item.title }}</span>
                    </div>
                    <div class="customerItem-num">
                      <NNumberAnimation :from="0" :to="Number(item.num)" />
                    </div>
                  </div>
                </div>
              </NGi>
            </NGrid>
          </NSpace>
        </NSpin>
      </NCard>
    </NGridItem>
  </NGrid>
</template>

<script lang="ts" setup>
import { reactive, computed } from "vue";
import { RoutesName } from '@/enums/routes';
import { getProductCount, getCustomerCount } from "@/services/api";
import { useLoading, useMessages, useEcharts } from "@/hooks";
import useJumpTo from "../../hooks/useJumpTo";
import cardBg from "@/assets/image/system/home/<USER>/cardBg.png";
import onSale from "@/assets/image/system/home/<USER>/onSale.png";
import soldOut from "@/assets/image/system/home/<USER>/soldOut.png";
import waitSale from "@/assets/image/system/home/<USER>/waitSale.png";

const { jumpTo } = useJumpTo();
const message = useMessages();
import { hasGoodsManagementAuth } from "../../authList"
/** 图表配置 */
const { domRef: pieRef, updateOptions } = useEcharts(() => ({
    title: {
        text: '0',
        subtext: '客户总数',
        top: '35%',
        left: 'center',
        textStyle: {
            fontSize: 25,
            fontWeight: 'bold',
            color: '#4DA4FF',
        },
        subtextStyle: {
            fontSize: 12,
            color: '#666666',
        },
    },
    series: [
        {
            type: 'pie',
            radius: ['50%', '90%'],
            startAngle: 260,
            label: { show: false },
            emphasis: {
                focus: 'none',
            },
            data: [
                {
                    name: '客户总数',
                    value: null,
                    itemStyle: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [
                                { offset: 0, color: '#4ED5FF' },
                                { offset: 1, color: '#4E96FF' },
                            ],
                        },
                        borderRadius: 5,
                    },
                },
                {
                    name: '本月新增',
                    value: null,
                    itemStyle: { color: 'rgba(0,0,0,0)' },
                },
            ],
        },
        {
            type: 'pie',
            radius: ['55%', '85%'],
            avoidLabelOverlap: false,
            startAngle: 260,
            endAngle: 'auto',
            label: {
                show: false,
                position: 'center'
            },
            emphasis: {
                focus: 'none',
                itemStyle: null
            },
            data: [
                {
                    name: '客户总数',
                    value: null,
                    itemStyle: { color: 'rgba(0,0,0,0)' },
                },
                {
                    name: '本月新增',
                    value: null,
                    itemStyle: { color: '#ECF5FF' },
                },
            ],
        },
    ],
}), { onRender() { } });

/** 数据源 */
const productCount = reactive({
    saleProductCount: 0,
    waitSaleProductCount: 0,
    soldOutProductCount: 0
});

/** 数据面板 */
const dataPanel = computed(() => {
    let originData = [
        {
            title: '销售中',
            num: productCount.saleProductCount,
            path: RoutesName.GoodsManagement,
            iconImg: onSale,
            numColor: 'black',
            isAuth:hasGoodsManagementAuth,
            query: {
                isPublish: 1
            }
        },
        {
            title: '待上架',
            num: productCount.waitSaleProductCount,
            path: RoutesName.GoodsManagement,
            iconImg: waitSale,
            numColor: 'black',
            isAuth:hasGoodsManagementAuth,
            query: {
                isPublish: 0
            }
        },
        {
            title: '已售罄',
            num: productCount.soldOutProductCount,
            path: RoutesName.GoodsManagement,
            iconImg: soldOut,
            numColor: 'black',
            isAuth:hasGoodsManagementAuth,
            query: {
                isSellout: 1
            }
        }
    ];
    return originData;
});

/** 客户管理 */
const customerCount = reactive({
    totalCustomer: 0,
    monthAddCustomer: 0
});
const customerDataPanel = computed(() => {
    let originData = [
        {
            title: '客户总数',
            num: customerCount.totalCustomer,
            className: 'blueIcon',
        },
        {
            title: '本月新增',
            num: customerCount.monthAddCustomer,
            className: 'gridIcon',
        },
    ];
    return originData;
});


/** 获取商品管理数据 */
const { loading, startLoading, endLoading } = useLoading();
function getGoodsManagement() {
    startLoading();
    getProductCount().then(res => {
        Object.assign(productCount, res);
    }).catch(err => {
        message.createMessageError("获取商品管理信息失败：" + err);
    }).finally(() => {
        endLoading();
    });
};

/** 获取客户管理数据 */
const { loading: customerLoading, startLoading: startCustomerLoading, endLoading: endCustomerLoading } = useLoading();
function getCustomerManagement() {
    startCustomerLoading();
    getCustomerCount().then(res => {
        Object.assign(customerCount, res);
        // 更新饼图
        updateOptions(opts => {
            // 更新 series 数据
            opts.series.forEach(series => {
                series.data.forEach(dataPoint => {
                    if (dataPoint.name === '客户总数') {
                        dataPoint.value = customerCount.totalCustomer;
                    } else if (dataPoint.name === '本月新增') {
                        dataPoint.value = customerCount.monthAddCustomer;
                    }
                });
            });

            // 更新 title 的 text
            opts.title.text = customerCount.totalCustomer.toString();

            return opts;
        });
    }).catch(err => {
        message.createMessageError("获取商品管理信息失败：" + err);
    }).finally(() => {
        endCustomerLoading();
    })
}

function init() {
    getGoodsManagement();
    getCustomerManagement();
}
init();
</script>

<style lang="less" scoped>
@import "@/styles/default.less";

.title {
    font-size: 20px;
    font-weight: bold;
    color: #333333;

    &::before {
        content: '';
        display: inline-block;
        width: 4px;
        height: 16px;
        background: #4DA4FF;
        border-radius: 12px;
        margin-right: 4px;
    }
}

.subhead {
    font-size: 16px;
    color: #999999;
}

.container {
    display: flex;
    align-items: center;

    .data-icon {
        width: 97px;
        height: 97px;
    }

    .data-content {
        display: flex;
        flex-direction: column;

        .data-title {
            font-size: 16px;
            color: #666666;
            display: flex;
            align-items: center;
            margin-top: 12px;
            cursor: pointer;

            &::after {
                content: '';
                display: inline-block;
                width: 16px;
                height: 16px;
                background: url(@/assets/image/system/home/<USER>/ChevronRight.png) no-repeat;
                background-size: 100% 100%;
            }
        }

        .data-num {
            font-size: 56px;
            font-family: Bebas Neue, Bebas Neue;
            // 字间距
            letter-spacing: -3px;
        }
    }
}


.customer-num {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .customerNumItem {
        display: flex;
        justify-content: space-between;

        .numItemleft {
            display: flex;
            align-items: center;

            .numItemIcon {
                margin: 0 5px;
                width: 12px;
                height: 12px;
                border-radius: 27px;
            }

            .numItem-title {
                font-size: 15px;
                color: #666666;
            }
        }

        .customerItem-num {
            color: #333333;
            font-size: 32px;
            font-weight: 500;
            font-family: Bebas Neue, Bebas Neue;
        }
    }

}

.gridIcon {
    background: #ECF5FF;
}

.blueIcon {
    background: linear-gradient(314deg, #4E96FF 29%, #4ED5FF 100%);
}
</style>
