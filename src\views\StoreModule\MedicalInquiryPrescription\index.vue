<script setup lang="ts">
import TabsLayout from "@/layout/TabsLayout.vue";
import { ref } from "vue";
import  MedicalInquiryTab  from './compoments/medicalInquiryTab.vue';
import { MedicalInquiryPrescriptionSelection } from '@/enums';

const tabNameRef = ref<string>(MedicalInquiryPrescriptionSelection.pendingReview);

const tableDataUpdate = ref()

const tabsData = ref([
  {
    label: "待审核",
    key: MedicalInquiryPrescriptionSelection.pendingReview,
  },
  {
    label: "可使用",
    key: MedicalInquiryPrescriptionSelection.available,
  },
  {
    label: "已使用",
    key: MedicalInquiryPrescriptionSelection.haveBeenUsed,
  },
  {
    label: "审核不通过",
    key: MedicalInquiryPrescriptionSelection.notApproved,
  },
  {
    label: "已失效",
    key: MedicalInquiryPrescriptionSelection.lostEfficacy,
  },
  {
    label: "全部",
    key: null,
  },
]);

</script>

<template >
   <n-layout>
    <n-layout-content id="prescription-management">
      <TabsLayout  v-model:value="tabNameRef" :tabsData="(tabsData as any)"  :onlyTabs="true" class="tabsLayout">
        <MedicalInquiryTab ref="tableDataUpdate" :tabNameRef="tabNameRef" />
      </TabsLayout>
    </n-layout-content>
  </n-layout>
</template>

<style lang="less" scoped>
// @import "@/styles/default.less";
</style>
