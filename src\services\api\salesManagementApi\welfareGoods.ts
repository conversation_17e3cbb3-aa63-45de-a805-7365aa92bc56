import { defHttp } from "@/services";

/** 福利品分类 */
export const enum welfareGoodsClassApi {
  addCouponProductCate = "/couponProductCate/add",
  updatecouponProductCate = "/couponProductCate/update",
  couponProductCateId = "/couponProductCate/get",
  deletecouponProductCate ="/couponProductCate/delete",
  couponProductCatePage ="/couponProductCate/page",
}

/**
 * @description 新建福利品分类
 */
export function addCouponProductCate(params) {
    return defHttp.post({
        url: welfareGoodsClassApi.addCouponProductCate,
        params,
    });
}

/**
 * @description 修改福利品分类
 */
export function updatecouponProductCate(params) {
    return defHttp.put({
        url: welfareGoodsClassApi.updatecouponProductCate,
        params,
    });
}

/**
 * @description 根据Id查询福利品分类
 */
export function couponProductCateId(id: string) {
    return defHttp.get({
        url: welfareGoodsClassApi.couponProductCateId + `?id=${id}`
    });
}

/**
 * @description 删除福利品分类
 */
export function deletecouponProductCate(params) {
    return defHttp.delete({
        url: welfareGoodsClassApi.deletecouponProductCate,
        params,
        requestConfig: {
            isQueryParams: true,
        },
    });
}

/**
 * @description 分页福利品分类
 */
export function couponProductCatePage(params) {
    return defHttp.post({
        url: welfareGoodsClassApi.couponProductCatePage,
        params,
    });
}
  