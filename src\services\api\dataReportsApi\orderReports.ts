import { defHttp } from '@/services';

/** 订单统计 */
export const enum OrderReportsEnum{
    page="/report/orderSum",
    export = "/report/orderSumExport",
    chart="/report/orderSumChart"
}

export function getOrderReportsPage(params) {
    return defHttp.post({
        url: OrderReportsEnum.page,
        params,
    });
}
export function exportOrderReportsPage(params) {
    return defHttp.post({
        url: OrderReportsEnum.export,
        requestConfig:{
            responeseType:'stream'
          },
        params,
    });
}
export function getOrderReportsChart(params) {
    return defHttp.post({
        url: OrderReportsEnum.chart,
        params,
    });
}