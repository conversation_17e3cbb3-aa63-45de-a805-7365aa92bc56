import {defHttp} from '@/services';

//标签
export const enum customerLabelApi {
 tagEntityAddTag = '/tagEntity/add/tag',
 tagEntityUpdateTag = '/tagEntity/update/tag',
 tagEntityGet = '/tagEntity/get',
 tagEntityDeleteTag = '/tagEntity/delete/tag',
 getCustomerTag = '/tagEntity/page/tag'
}

//客户标签
export const enum customerTagLabelApi {
  customerTagGetBindTag = '/customerTag/get/bindTag',
  gmMemberRelateList = '/gmMemberRelate/list'
}


export function tagEntityAddTag(params) {
    return defHttp.post({
      url: customerLabelApi.tagEntityAddTag,
      params
    });
}

export const tagEntityUpdateTag = (params) => {
    return defHttp.put({
      url: customerLabelApi.tagEntityUpdateTag,
      params,
    });
};

export function tagEntityGet(id: string) {
    return defHttp.get({
      url: customerLabelApi.tagEntityGet,
      params: {
        id
      },
    })
}

export function tagEntityDeleteTag(params) {
  return defHttp.delete({
    url: customerLabelApi.tagEntityDeleteTag,
    requestConfig: {
      isQueryParams: true,
    },
    params,
  })
}

export interface CustomerTagPageRes {
  records: ApiCustomerManagement.CustomerTag[],
  total: string;
  size: string;
  current: string;
}
/**
 * @description 获取客户标签（分页数据）
 * @returns 
 */
export function getCustomerTag(params: {
  data: {
    name: string,
  },
  pageVO: {
    current: number,
    size: number
  }
}) {
  return defHttp.post<CustomerTagPageRes>({
    url: customerLabelApi.getCustomerTag,
    params
  });
}

export function customerTagGetBindTag(params) {
  return defHttp.get({
    url: customerTagLabelApi.customerTagGetBindTag,
    params
  })
}

export function gmMemberRelateList(params) {
  return defHttp.post({
    url: customerTagLabelApi.gmMemberRelateList+'?csId=' + params.csId,
  })
}
