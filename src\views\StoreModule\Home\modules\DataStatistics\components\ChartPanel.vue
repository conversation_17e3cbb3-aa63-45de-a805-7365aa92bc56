<template>
    <div class="chart-box">
        <div class="label" :style="{ left: isNumber(props.tipLeft) ? props.tipLeft + 'px' : props.tipLeft }">{{
            props.tip
        }}</div>
        <div ref="chartRef" style="width: 100%; height: 100%"></div>
    </div>
</template>

<script setup lang="ts" name="DistributionChart">
import { onMounted, watch } from "vue";
import { isNumber } from "@/utils/isUtils";
import useEcharts from "@/hooks/useEcharts";
interface DataInfo {
    list: (number | string)[],
    times: string[]
}
const props = withDefaults(defineProps<{
    title?: string,
    tip?: string,
    desc?: string,
    tipLeft?: number | string,
    data: DataInfo
}>(), {
    title: '',
    tip: '',
    desc: '',
    tipLeft: -21,
    data: () => ({ list: [], times: [] })
})
const { domRef: chartRef, updateOptions } = useEcharts(() => {
    return {
        xAxis: {
            type: 'category',
            data: []
        },
        title: {
            left: 'center',
            text: props.title
        },
        legend: {
            orient: 'horizontal',
            bottom: '0%',
            data: [props.desc]
        },
        yAxis: {
            type: 'value'
        },
        series: [
            {
                name: props.desc,
                data: [],
                type: 'line',
                smooth: true,
                emphasis: {
                    itemStyle: {
                        color: 'red'
                    },
                    label: {
                        show: true,
                        color: 'black', 
                        fontSize: 16,
                        fontWeight: 'bold',
                    },
                    shadowBlur: 10,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            },
        ]
    };
})
watch(() => props.data, (newVal) => {
    updateOptions(opts => {
        // console.log('更新')
        // 更新 series 数据
        opts.series.forEach(series => {
            series.data = newVal.list;
        });
        opts.xAxis.data = newVal.times;
        return opts;
    });
}, {
    deep: true,
    immediate: true
})
</script>

<style lang="less" scoped>
.chart-box {
    width: 100%;
    height: 100%;
    position: relative;

    .label {
        position: absolute;
        bottom: 50%;
        left: -21px;
        color: #666;
        font-size: 14px;
        transform: rotate(-90deg);
    }
}
</style>