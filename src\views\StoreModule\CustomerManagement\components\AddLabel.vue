<template>
  <JModal
    v-model:show="modalVisible"
    :title="title"
    width="500"
    @after-leave="closeModal"
    :isScale="false"
    @positive-click="handleSave"
    :positiveButtonProps="{
		loading: isLoading
	}"
  >
    <n-form
      ref="formRef"
      :model="model"
      :rules="rules"
      label-width="auto"
      label-placement="left"
      require-mark-placement="right-hanging"
      :style="{
		width: '100%',
	}"
    >
      <n-grid :cols="24" :x-gap="24" responsive="self">
        <!-- 标签名称 -->
        <n-form-item-gi :span="20" :label="model.type === 'edit' ? '修改标签名称' : '标签名称'" path="name">
          <n-input
            v-model:value="model.name"
            @blur="model.name = ($event.target as HTMLInputElement)?.value.trim()"
            placeholder="请输入标签名称"
            :maxlength="50"
          />
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </JModal>
</template>

<script setup lang="ts" name="addBootcamp">
import { ref, reactive, computed } from "vue";
import { useMessages } from "@/hooks";
import { tagEntityAddTag, tagEntityUpdateTag } from '@/services/api';

const { createMessageSuccess, createMessageError } = useMessages();

interface ModalProps {
	type: 'add' | 'edit';
	refresh?: () => void; // 刷新表格
	editId?: string;
	editName?: string;
};

/** emits */
const emits = defineEmits<{
	(e: 'afterSave', data: any): void
}>();

/** 弹窗显隐 */
const modalVisible = ref(false);

/** 标题 */
const title = computed(() => {
	const titleMap: Record<'add' | 'edit', string> = {
		add: '新建标签',
		edit: '修改标签',
	};
	return titleMap[model.value.type];
});

/* 表单参数初始化 */
const initParams:
	{
		type: 'add' | 'edit'; // 模式
		name: string;
		editId: string; // 编辑时, 标签ID
		refresh?: () => void; // 刷新表格
	} = {
	type: 'add',
	name: "",
	editId: undefined
};
const model = ref({ ...initParams });

/* 接收父组件传过来的参数 */
const acceptParams = (params: ModalProps) => {
	model.value.type = params.type;
	model.value.editId = params.editId;
	rules['name'].message = params.type === 'edit' ? '请输入修改标签名称' : '请输入标签名称';
	if (params?.editName) model.value.name = params?.editName;
	model.value.refresh = params.refresh;
	// 触发弹窗
	modalVisible.value = true;
};

/* 表单实例 */
const formRef = ref();

/* 表单规则 */
const rules = reactive({
	name: {
		required: true,
		trigger: ['blur', 'change'],
		message: '',
	},
});

/* 清空表单 */
const formDataReset = () => {
	model.value = { ...initParams };
};

/* 关闭弹窗之后 */
const closeModal = () => {
	formDataReset();
};

/* 确认--保存 */
const isLoading = ref(false);
const handleSave = (e: MouseEvent) => {
	e.preventDefault();
	formRef.value?.validate(async (errors: any) => {
		if (!errors) {
			const params = {
				data: {
					id: model.value.editId,
					name: model.value.name || null,
				},
				pageVO: {
					current: 1,
					size: 10
				}
			}
			try {
				isLoading.value = true;
				let data;
				// 编辑
				if (model.value.type === 'edit') {
					data = await tagEntityUpdateTag(params);
				} else {// 新增
					data = await tagEntityAddTag(params);
				}
				isLoading.value = false;
				createMessageSuccess(model.value.type === 'edit' ? '修改标签名称成功' : '新建标签成功');
				// 刷新
				modalVisible.value = false;
				model.value?.refresh();
				emits("afterSave", data);
			} catch (error) {
				isLoading.value = false;
				createMessageError(`${model.value.type === 'edit' ? '修改标签名称失败' : '新建标签失败'}: ${error}`);
			}
		}
	});
};

defineExpose({
	acceptParams,
});
</script>

<style scoped lang="less"></style>
