import {defHttp} from '@/services';

export const enum customerManagementApi {
 customerEntityAdd = '/customerEntity/add',
 customerEntityUpdate = '/customerEntity/update',
 customerEntityGet = '/customerEntity/get',
 customerEntityPage = '/customerEntity/page',
 pointRecordPagePointsRecord = '/pointRecord/pagePointsRecord',
 pointChannelListCsChannel = "/pointChannel/listCsChannel",
 customerEntityAddVirAccount = '/customerEntity/addVirAccount',
 customerEntityUpdateCommentState = '/customerEntity/updateCommentState',
 pointRecordAddOrDecPoints = '/pointRecord/addOrDecPoints' , // 增加/减少积分接口
 customerEntityBlackList  = '/customerEntity/blacklist',
 customerEntityExportCustomers  = '/customerEntity/exportCustomers',
 customerEntityChangeStaffRelation = '/customerEntity/changeStaffRelation',
 customerEntityPageCount = '/customerEntity/countCustomer'
}


export function customerEntityAdd(params) {
    return defHttp.post({
      url: customerManagementApi.customerEntityAdd,
      params
    });
}

export function customerEntityAddVirAccount(params) {
  return defHttp.post({
    url: customerManagementApi.customerEntityAddVirAccount,
    params
  });
}

export function customerEntityUpdateCommentState(params) {
  return defHttp.post({
    url: customerManagementApi.customerEntityUpdateCommentState,
    params
  });
}

export const customerEntityUpdate = (params) => {
    return defHttp.put({
      url: customerManagementApi.customerEntityUpdate,
      params: {
        data: params
      },
    });
};

export function customerEntityGet(params) {
    return defHttp.get({
      url: customerManagementApi.customerEntityGet,
      params,
    })
}

export function customerEntityPage(params) {
  return defHttp.post({
    url: customerManagementApi.customerEntityPage,
    params,
  });
}

/** 获取客户列表分页总数 */
export function customerEntityPageCount(params) {
  return defHttp.post({
    url: customerManagementApi.customerEntityPageCount,
    params,
  });
}


export function pointRecordPagePointsRecord(params) {
  return defHttp.post({
    url: customerManagementApi.pointRecordPagePointsRecord,
    params
  });
}

export function pointChannelListCsChannel(params) {
  return defHttp.get({
    url: customerManagementApi.pointChannelListCsChannel,
    params
  });
}

export function pointRecordAddOrDecPoints(params) {
  return defHttp.post({
    url: customerManagementApi.pointRecordAddOrDecPoints,
    params
  });
}

/** 取消/拉黑 */
export function customerEntityBlackList(params) {
  return defHttp.put({
    url: customerManagementApi.customerEntityBlackList,
    params
  });
}

/** 导出会员 */
export function customerEntityExportCustomers(params) {
  return defHttp.post({
    url: customerManagementApi.customerEntityExportCustomers,
    requestConfig: {
      responeseType: 'stream'
    },
    params
  });
}

/** 修改归属店员 */
export function customerEntityChangeStaffRelation(params) {
  return defHttp.put({
    url: customerManagementApi.customerEntityChangeStaffRelation,
    params
  })
}

