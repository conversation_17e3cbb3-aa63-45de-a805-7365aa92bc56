<template>
  <JModal
    v-model:show="show"
    width="680"
    :title="Add_active?'新增轮播图':'编辑轮播图'"
    @after-leave="closeModal"
		@positive-click="_save"
		:positiveButtonProps="{
			loading: isLoading
		}"
  >
    <n-form
      ref="formRef"
      :rules="rules"
      :model="model"
      label-width="auto"
      label-placement="left"
      require-mark-placement="right-hanging"
      :style="{
          width: '100%',
        }"
    >
      <n-grid :cols="8" :x-gap="24">
        <n-form-item-gi :span="24" label="所在位置" path="position" required>
          <n-select v-model:value="model.position" :options="positionList"/>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="图片" path="imgPath" required>
          <div style="width: 100%;">
            <CustomizeUpload v-model:value="model.imgPath" :imageUploadType="['image/jpg','image/jpeg','image/gif','image/png']" accept="image/jpg,image/jpeg,image/gif,image/png" enableTypeValidation :fileListSize="1" :max="1"/>
            <div style="width: 98%; margin-top: 12px;">图片需小于1M，支持png、jpg、JPEG、GIF格式，建议尺寸：{{ sizeList[model.position] }}(px)</div>
          </div>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="跳转目标" path="redirect" required>
          <n-select v-model:value="model.redirect" :options="redirectList"/>
        </n-form-item-gi>
        <n-form-item-gi v-if="model.redirect == 1" :span="24" label="商品ID" path="productId" required>
          <n-input v-model:value="model.productId" style="width: 100%" :show-button="false" maxlength="19" placeholder="商品ID可在商品管理页面复制" clearable/>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="排序号" path="sortNumber">
          <n-input-number v-model:value="model.sortNumber" style="width: 100%" :show-button="false" :min="0" :max="999" placeholder="序号越大排序越前,最大三位数" clearable/>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="启用" path="isEnable" required>
          <n-radio-group v-model:value="model.isEnable" name="active">
            <n-space>
              <n-radio v-for="song in radio" :key="song.value" :value="song.value">
                {{ song.label }}
              </n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item-gi>
        <n-form-item-gi :span="8" label="备注" path="remark">
          <n-input v-model:value="model.remark" placeholder="最多30个字" maxlength="30"/>
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </JModal>
</template>

<script setup lang="ts" name="AddorEditVideo">
import { computed, ref, watch } from "vue";
import { useMessages } from "@/hooks";
import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
import { SystemStoreType } from "@/enums";
export interface AddCompanyModalProps {
  api?: (params: any) => Promise<any>;// 新增保存Api
  refreshTable?: () => void; // 刷新表格数据
  row?: Partial<ApiStoreModule.CarouselImg>;
  // id?:string
}
const Add_active = ref<boolean>(true)
const systemStore = useSystemStoreWithoutSetup();
const initParams = {
  id:null,
  position: 1,
  imgPath: '',
  redirect: 0,
  productId: null,
  sortNumber:0,
  isEnable:0,
  remark:''
};
const model = ref({ ...initParams });

/* 提示信息 */
const message = useMessages();
/* 模态框显隐状态 */
const show = ref(false);
/* 父组件传过来的参数 */
const parameter = ref<AddCompanyModalProps>({
});
/* 接收父组件参数 */
const acceptParams = (params: AddCompanyModalProps) => {
  parameter.value = params;
  if (params.row.id){
    Add_active.value = false
    model.value.id = params.row.id
    model.value.position = Number(params.row.position) ? Number(params.row.position):0
    model.value.imgPath = params.row.imgPath ? params.row.imgPath : ''
    model.value.redirect = Number(params.row.redirect) ? Number(params.row.redirect) : 0
    model.value.productId = params.row.productId ? params.row.productId : null
    model.value.sortNumber = Number(params.row.sortNumber) ? Number(params.row.sortNumber):0
    model.value.isEnable = params.row.isEnable ? params.row.isEnable:0
    model.value.remark = params.row.remark ? params.row.remark : ''
  }
  show.value = true;
};
const radio = [
  {
    value:0,
    label:"是"
  },
  {
    value:1,
    label:"否"
  }
]
//  尺寸列表
const sizeList = [
  '351*120',
  '351*120'
]
const positionList = computed(() => {
  if(systemStore._globalConfig['marketplaceType'] === SystemStoreType.PHARMACEUTICALMALL) {
      return [
        {
          value:1,
          label:'商城首页',
        },
        {
          value:2,
          label:'积分商城首页',
        },
        {
          value:3,
          label:'问诊首页',
        }
      ]
  } else {
      return [
        {
          value:1,
          label:'商城首页',
        },
        {
          value:2,
          label:'积分商城首页',
        }
    ]
  }
 
})
const redirectList= ref([
  {
    value:0,
    label:'无',
  },
  {
    value:1,
    label:'小程序商品详情页',
  },
])
const phoneReg = /\d{1,19}$/
/* 表单规则 */
const rules = {
  sortNumber: {
    required: true,
    trigger: ["blur", "change"],
    message: "请输入大于等于0小于1000的整数",
    validator: ()=>{
      return model.value.sortNumber == 0 || model.value.sortNumber != null;
    }
  },
  productId:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入商品ID(限19位整数)",
    validator: ()=>{
      return model.value.redirect != 1 || (model.value.productId != null && phoneReg.test(model.value.productId));
    }
  },
  imgPath:{
    required: true,
    trigger: ["blur", "change"],
    message: "请先上传图片",
    validator: ()=>{
      return model.value.imgPath != '';
    }
  },
};
/* 表单实例 */
const formRef = ref(null);
/* 表单参数初始化 */
/* 清空表单 */
const formDataReset = () => {
  model.value = { ...initParams };
  Add_active.value = true
  parameter.value = {}
};
/* 关闭弹窗 */
const closeModal = () => {
  formDataReset();
};
/* 确认--保存 */
const isLoading = ref(false);
const _save = (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      try {
        isLoading.value = true;
        await parameter.value.api({
            "data": {
              "id":model.value.id?model.value.id:null,
              "position": model.value.position,
              "imgPath": Array.isArray(model.value.imgPath)?model.value.imgPath[0]:model.value.imgPath,
              "redirect": model.value.redirect,
              "productId": model.value.redirect == 1 ? model.value.productId:null,
              "sortNumber": model.value.sortNumber,
              "isEnable": model.value.isEnable,
              "remark": model.value.remark
            }
          }).then(e=>{
          if (Add_active.value) {
            message.createMessageSuccess(`添加轮播图配置成功`);
          }else {
            message.createMessageSuccess(`修改轮播图配置成功`);
          }
        });
        isLoading.value = false;
      } catch (e) {
        if (Add_active.value) {
          message.createMessageError(`新增轮播图配置失败： ${e}`);
        }else {
          message.createMessageError(`修改轮播图配置失败： ${e}`);
        }
        isLoading.value = false;
      } finally {
        // 弹窗取消
        show.value = false;
        // 刷新表格数据
        parameter.value.refreshTable();
      }
    }
  });
};
watch(()=>model.value.imgPath,(newVal)=>{
  if(newVal[0] == '') model.value.imgPath = ''
})
defineExpose({
  acceptParams,
});

</script>

<style scoped lang="less">
::v-deep .n-form-item-label{
  width: 70px !important;
}
</style>
