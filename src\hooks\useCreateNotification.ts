import { _debounce } from "@/utils";
import { useNotification, NButton, type NotificationReactive } from "naive-ui";
import { h, onUnmounted, ref, type Ref } from "vue";
import toBeopened from "@/assets/audio/toBeopened.mp3";
import abnormalShip from "@/assets/audio/abnormalShip.mp3";
import { useRouter } from "vue-router";
import { RoutesName } from "@/enums/routes";
import { getRemind } from "@/services/api";
import { isEmpty, isNullOrUnDef } from "@/utils";
import useAbnormalShipment from "@/hooks/useAbnormalShipment";
/** 处方待开方通知 */
let prescriptionRef: NotificationReactive | null = null;
/** 发货异常通知 */
let abnormalShipmentRef: NotificationReactive | null = null;
/** 通知实例 */
let notificationInstance = null;
/** 定时器 */
let timer = null;

const { isShowAbnormalShipmentRef } = useAbnormalShipment();

interface NotificationConfig {
  id: string | null;
  idRef: Ref<string | null>;
  notificationRef: NotificationReactive | null;
  showNotification: () => void;
}

export default function useCreateNotification() {
  const router = useRouter();
  /** 最新待开方处方id */
  const prescriptionCode = ref(null);
  /** 最新发货异常订单id */
  const abnormalShipmentId = ref(null);
  if (!notificationInstance) {
    notificationInstance = useNotification();
  }
  const audioRef = ref(null);

  /** 展示处方待开方通知 */
  function showPrescriptionNotification() {
    cancelButtonClick(prescriptionRef);
    /** 处方播报优先级低 所以判断当前播报是否结束 ，不可打断当前播报 */
    if (!audioRef.value || audioRef.value.ended) {
      audioRef.value = new Audio(toBeopened);
      audioRef.value.play();
    }
    prescriptionRef = notificationInstance.create({
      title: "待办事项提醒",
      // description: 'From the Beach Boys',
      content: () =>
        h("div", { class: "notification-content" }, [
          h("div", "有待开方需求，请及时处理。"),
          h("div", { class: "btnGroup" }, [
            h(
              NButton,
              {
                type: "default",
                onClick: () => cancelButtonClick(prescriptionRef),
              },
              {
                default: () => "取消",
              },
            ),
            h(
              NButton,
              {
                type: "primary",
                onClick: handleButtonClick,
              },
              {
                default: () => "去处理",
              },
            ),
          ]),
        ]),
      // meta: '2019-5-27 15:11',
      onClose: () => {
        prescriptionRef = null;
        closeAudio();
      },
    });
  }

  /** 展示发货异常通知 */
  function showAbnormalShipmentNotification() {
    /** 发货异常播报优先级高 所以关闭当前播报 */
    closeAudio();
    cancelButtonClick(abnormalShipmentRef);
    // 播放提示音
    audioRef.value = new Audio(abnormalShip);
    audioRef.value.play();
    abnormalShipmentRef = notificationInstance.create({
      title: "待办事项提醒",
      // description: 'From the Beach Boys',
      content: () =>
        h("div", { class: "notification-content" }, [
          h("div", "你有发货异常的订单，请及时处理。"),
          h("div", { class: "btnGroup" }, [
            h(
              NButton,
              {
                type: "default",
                onClick: () => cancelButtonClick(abnormalShipmentRef),
              },
              {
                default: () => "取消",
              },
            ),
            h(
              NButton,
              {
                type: "primary",
                onClick: () => {
                  isShowAbnormalShipmentRef.value = true;
                  cancelButtonClick(abnormalShipmentRef);
                },
              },
              {
                default: () => "去处理",
              },
            ),
          ]),
        ]),
      // meta: '2019-5-27 15:11',
      onClose: () => {
        abnormalShipmentRef = null;
        closeAudio();
      },
    });
  }

  function handleButtonClick() {
    router.push({ name: RoutesName.PrescriptionManagement });
    cancelButtonClick(prescriptionRef);
  }
  /** 关闭处方通知 */
  function cancelButtonClick(notificationRef: NotificationReactive) {
    if (notificationRef) {
      notificationRef.destroy();
      notificationRef = null;
    }
  }
  function closeAudio() {
    if (audioRef.value) {
      audioRef.value.pause();
      audioRef.value.currentTime = 0;
    }
  }

  /** 处理通知逻辑 */
  function handleNotification({ id, idRef, notificationRef, showNotification }: NotificationConfig) {
    if (!isNullOrUnDef(id)) {
      const isNewNotification = isNullOrUnDef(idRef.value) || id > idRef.value;
      if (isNewNotification) {
        idRef.value = id;
        showNotification();
      }
    } else if (notificationRef) {
      notificationRef.destroy();
      notificationRef = null;
    }
  }

  /** 开始轮询 通过后端自增id判断 大于上次保存id 则给出弹窗，如果为空且弹窗存在则关闭弹窗 */
  function openNotification(isPharmacy: boolean, hasManagementShip: boolean) {
    getRemind()
      .then(({ orderCode, presId }) => {
        // 处理异常发货通知
        if (hasManagementShip) {
          handleNotification({
            id: orderCode,
            idRef: abnormalShipmentId,
            notificationRef: abnormalShipmentRef,
            showNotification: showAbnormalShipmentNotification,
          });
        }

        // 处理处方通知
        if (isPharmacy) {
          handleNotification({
            id: presId,
            idRef: prescriptionCode,
            notificationRef: prescriptionRef,
            showNotification: showPrescriptionNotification,
          });
        }
      })
      .catch(err => {
        console.error("获取提醒失败:", err);
      })
      .finally(() => {
        if (timer) {
          clearTimeout(timer);
        }
        timer = setTimeout(openNotification, 10000);
      });
  }
  function destroyNotification() {
    notificationInstance.destroyAll();
    clearTimeout(timer);
  }

  return {
    openNotification,
    destroyNotification
  };
}
