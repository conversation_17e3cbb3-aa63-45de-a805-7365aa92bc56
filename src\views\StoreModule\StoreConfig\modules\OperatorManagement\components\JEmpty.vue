<template>
  <div class="empty-wrapper">
    <img :src="EmptySrc" alt="" />
    <div class="notice">{{ props.title }}</div>
  </div>
</template>

<script lang="ts" setup name="">
import EmptySrc from "@/assets/image/exception/emptyData.png";

/** props */
const props = withDefaults(
  defineProps<{
    title?: string;
  }>(),
  {
    title: "暂无数据",
  }
);

</script>

<style lang="less" scoped>
.empty-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    img {
        height: 160px;
    }
}
</style>
