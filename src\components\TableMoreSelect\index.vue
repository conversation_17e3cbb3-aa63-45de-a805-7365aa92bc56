<template>
  <n-popover 
    v-if="optionsVaildListComp.length"
    trigger="click" 
    v-model:show="isShowRef"
    style="padding: 5px;"
    placement="bottom-end"
  >
    <template #trigger>
      <n-button text @click="showPopoverHandler" type="primary">
        {{ props.text }}
        <n-icon><Component :is="isShowRef?ChevronUpOutline:ChevronDownOutline"/></n-icon>
      </n-button>
    </template>
    <n-button-group vertical size="small">
      <n-button 
        v-for="option in optionsVaildListComp" 
        :style="{padding:'5px',color:option.startcolor ? 'red' : ''}"
        text 
        type="primary" 
        :key="option.key" 
        @click="()=>btnClickHandler(option.key)"
        
      >
        {{ option.label }}
      </n-button>
    </n-button-group>
  </n-popover>
</template>
<script setup lang="ts">
import { computed, ref } from 'vue';
import {ChevronDownOutline,ChevronUpOutline } from '@vicons/ionicons5'
import { isNullOrUnDef } from '@/utils/isUtils';
type TableMoreSelectOption={
  label:string,
  key: string | number,
  show?:boolean,
  startcolor?:boolean
}
interface TableMoreSelectProps{
  text?:string,
  options?:Array<TableMoreSelectOption>,
}
interface TableMoreSelectEmits{
  (e:"onClick",key:string | number):void
}
const isShowRef = ref(false)
const props = withDefaults(defineProps<TableMoreSelectProps>(),{
  text:'更多',
  options:()=>[]
})

const emits = defineEmits<TableMoreSelectEmits>()
const optionsVaildListComp = computed(()=> props.options.filter(item=>isNullOrUnDef(item.show)?true:item.show))
function btnClickHandler(key:string | number){
  emits("onClick",key)
  isShowRef.value = false
}
function showPopoverHandler(){
  if(optionsVaildListComp.value.length){
    isShowRef.value = !isShowRef.value
  }
  else {
    isShowRef.value = false
  }
}
</script>