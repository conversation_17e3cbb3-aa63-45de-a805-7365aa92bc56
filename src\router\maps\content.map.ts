import BlankLayout from '@/layout/BlankLayout.vue'
import { RoutesName } from "@/enums/routes";
import type { RouteLocation } from "vue-router";

export const Default = {
  [RoutesName.Content]: {
    path: "content",
    component: BlankLayout,
    meta: {
      title: "内容",
    },
  },
  [RoutesName.VideoManagement]: {
    path: "VideoManagement",
    component: () => import("@/views/ContentModule/VideoManagement/index.vue"),
    meta: {
      title: "视频管理",
      icon: 'video-management'
    }
  },
  [RoutesName.CommentManagement]: {
    path: "CommentManagement",
    component: () => import("@/views/ContentModule/CommentManagement/index.vue"),
    meta: {
      title: "评论管理",
      icon: 'comment-management'
    }
  },
  [RoutesName.LexiconConfig]: {
    path: "lexiconConfig",
    component: () => import("@/views/ContentModule/LexiconConfig/index.vue"),
    meta: {
      title: "敏感词库",
      icon: 'lexicon-config'
    }
  },
  [RoutesName.ContentConfig]: {
    path: "contentConfig",
    component:() => import("@/views/ContentModule/ContentConfig/index.vue"),
    meta: {
      title: "内容配置",
      icon: 'content-config'
    }
  },
};
