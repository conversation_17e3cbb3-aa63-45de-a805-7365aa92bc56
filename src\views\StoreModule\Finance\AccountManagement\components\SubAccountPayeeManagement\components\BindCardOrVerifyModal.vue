<template>
    <JModal
      v-model:show="show"
      width="700"
      :title="parameter.type"
      :positiveText = "parameter.type == '绑卡确认' ? '确定' : '保存'"
      @after-leave="closeModal"
          @positive-click="_save"
          :positiveButtonProps="{
              loading: isLoading
          }"
    >
    <n-spin :show="model.modalLoading">
    <n-form
        ref="formRef"
        :rules="rules"
        :model="model"
        label-width="110"
        label-placement="left"
        
        require-mark-placement="right-hanging"
        :style="{
            width: '100%',
            
          }"
      >
        <n-grid :cols="8" :x-gap="8" >
          <n-form-item-gi v-if="bindCard()" :span="8" label="开户名称">
            <n-input v-model:value="model.alias" placeholder="请输入开户名称" maxlength="10" disabled/>
          </n-form-item-gi>
          <n-form-item-gi v-if="bindCard()" :span="8" label="银行卡号" path="bankCardNo">
            <n-input 
              maxlength="40" 
              v-model:value="model.bankCardNo" 
              show-count 
              clearable 
              placeholder="请输入银行卡号" 
              :allow-input="onlyAllowNumber"
              required
              @blur="blurValidateBankCardNo"
              :disabled="accountOpeningLabels[model.cleanType] != '个人'"/>
          </n-form-item-gi>
          <n-form-item-gi v-if="bindCard()" :span="8" label="开户银行" path="bankId">
             <JAccountBank 
               v-model:value="model.bankId"
               :disabled="model.accountOpeningNameLoading || accountOpeningLabels[model.cleanType] != '个人'"
               isImmediately
               />
          </n-form-item-gi>

          <n-form-item-gi v-if="bindCard()"  :span="8" label="开户行名称">
            <JBankDeposit
            v-model:value="model.KHbankId"
            v-model:bankNo="model.bankNo"
            v-model:accountOpeningNameLoading="model.accountOpeningNameLoading"
            :payPlatForm="3"
            :parentId="model.bankId"
            :disabled="(
               model.bankId == null
            )"
            isImmediately
            :isBindCardcard="true"
            />
          </n-form-item-gi>

          <n-form-item-gi v-if="bindCard()" :span="8" label="开户行行号" path="bankNo">
            <n-input v-model:value="model.bankNo" placeholder="请输入开户行行号" maxlength="10" readonly/>
          </n-form-item-gi>
          <n-form-item-gi v-if="bindCard()" :span="8" label="联系人手机号" path="mobile">
            <n-input-number 
             v-model:value="model.mobile" 
             :show-button="false"  
             @blur="blurMobilePhone" 
             style="width: 100%;" 
             placeholder="请输入联系电话"
             clearable 
             :disabled="accountOpeningLabels[model.cleanType] != '个人'"
             />
          </n-form-item-gi>

          <n-form-item-gi v-if="verify() && SubAccountPayeeManagementStateLabels[model.state] == '待打款验证'" :span="8" label="打款金额(元)" path="payoutsAmount">
            <n-input v-model:value="model.payoutsAmount" placeholder="请输入银行账户收到的打款金额" maxlength="10"/>
          </n-form-item-gi>
          <n-form-item-gi v-if="verify() && SubAccountPayeeManagementStateLabels[model.state] == '待短信验证'" :span="8" label="短信验证码" path="shortMessage">
            <n-input v-model:value="model.shortMessage" placeholder="请输入联系人手机短信验证码" maxlength="10"/>
          </n-form-item-gi>
        </n-grid>
      </n-form>
    </n-spin>
    </JModal>
  </template>
  
<script setup lang="ts" name="AddorEditVideo">
import { ref,watch } from "vue";
import { useMessages } from "@/hooks";
import {allocationAccountUpdateBinkApi,allocationAccountBinkAuth,allocationAccountGetBinkInfo} from "@/services/api"
import useSubAccountPayeeManagement from '../hooks/useSubAccountPayeeManagement'
import { SubAccountPayeeManagementStateLabels,accountOpeningLabels } from "@/constants";
const {numberVerification, validateBankCardNo} = useSubAccountPayeeManagement()
const { createMessageSuccess, createMessageError,createMessageWarning } = useMessages();

const initParams = {
    /** 开户名称 */
    alias:undefined,
    /** 银行卡号 */
    bankCardNo:undefined,
    /** 银行id */
    bankId:undefined,
    /** 开户银行行号 */
    bankNo:undefined,
    /** 手机号 */
    mobile:undefined,
    /** 开户行id */
    KHbankId:undefined,
    /** 开户名称 loading*/
    accountOpeningNameLoading:false,
    /** 商户号 */
    merchantId:undefined,
    /** 入账方ID */
    accountCode:undefined,
    /** 状态 */
    state:undefined,
    /** 打款金额 */
    payoutsAmount:undefined,
    /** 短信验证 */
    shortMessage:undefined,
    /** 开户类型 */
    cleanType:undefined,
    /** 应该id是否已经赋值完产生变化 */
    bankIdHasChanged:false,
    /** 模态框加载状态 */
    modalLoading:false,
};

const onlyAllowNumber = (value: string) => !value || /^\d+$/.test(value)

/** 银行卡号校验 */
const blurValidateBankCardNo = () =>{
  if(!validateBankCardNo(model.value.bankCardNo)){
    model.value.bankCardNo = null
  }
}

const model = ref({ ...initParams });
export interface BindCardOrVerifyModalProps {
  row?:any
  type?:string
  refresh?: () => void; // 刷新表格数据
}

/** 模态框显隐状态 */
const show = ref(false);

/** 表单规则 */
const rules = {
  bankCardNo:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入银行卡号",
  },
  bankId:{
    required: true,
    trigger: ["blur", "change"],
    message: "请选择开户银行",
  },
  bankNo:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入开户银行行号",
  },
  mobile:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入联系电话",
  },
  payoutsAmount:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入打款金额",
  },
  shortMessage:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入短信验证",
  }
};

const acceptParams = async(params) => {
  parameter.value = params
  show.value = true
  const {alias,merchantId,accountCode,state,cleanType,id} = parameter.value.row
  if(SubAccountPayeeManagementStateLabels[state] != '待打款验证' && SubAccountPayeeManagementStateLabels[state] != '待短信验证'){
    model.value.alias = alias
    model.value.cleanType = cleanType
  }
  model.value.merchantId =merchantId
  model.value.accountCode = accountCode
  model.value.state = state
  
  if(bindCard()){
    await dataValue(id)
    model.value.bankIdHasChanged = true
  }
};

/** 获取数据表格赋值内容 */
const dataValue = async(id) =>{
  model.value.modalLoading = true
  try{
    const { allocationAccountCardInfos } = await allocationAccountGetBinkInfo(id)
    const {bankParentId, mobile, outAccountNo, interBankNo} = allocationAccountCardInfos[0]
    model.value.bankId = bankParentId
    model.value.mobile = mobile
    model.value.bankCardNo = outAccountNo
    model.value.bankNo = interBankNo
  }catch(err){
    createMessageError('获取表格绑定数据内容失败:' + err)
  }finally{
    model.value.modalLoading = false
  }
}

/** 表单实例 */
const formRef = ref(null);

/** 清空表单 */
const formDataReset = () => {
  model.value = { ...initParams };
};

const parameter = ref<BindCardOrVerifyModalProps>({});

/** 关闭弹窗 */
const closeModal = () => {
  //表单重置
  formDataReset();
  // 弹窗取消
  show.value = false;
  //刷新表格
  parameter.value.refresh()
};

/** 确认--保存 */
const isLoading = ref(false);
const _save = (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      const {bankCardNo,accountCode,merchantId,bankNo,mobile,bankId,shortMessage,payoutsAmount,state,cleanType} = model.value
      const params = { 
         data:{
          bankCardNo,
          accountCode,
          merchantId,
          bankNo,
          mobile,
          bankId,
          amtOrSms: SubAccountPayeeManagementStateLabels[state] == '待打款验证' ? payoutsAmount : shortMessage,
          cleanType
         }
       };
      isLoading.value = true
      try{
        bindCard() ? await allocationAccountUpdateBinkApi(params) : await allocationAccountBinkAuth(params)
        createMessageSuccess ((bindCard() ? '绑卡确认成功' : '绑卡验证成功'))
        closeModal()
      }catch(err){
        createMessageError((bindCard() ? '绑卡确认失败:' : '绑卡验证失败:') + err)
      }finally{
        isLoading.value = false;
      }
    }
  });
};

/** 绑卡 */
const bindCard = () =>{
  return parameter.value.type == '绑卡确认'
}

/** 验证 */
const verify = () =>{
  return parameter.value.type == '绑卡验证'
}

/** 判断输入的手机号码是否正确 */
const blurMobilePhone = () =>{
    if(!numberVerification(model.value.mobile) && model.value.mobile != null){
        model.value.mobile = null
    }
}

/** 开户银行、开户行所在地监听 */
watch(
  () => model.value.bankId,
  () => {
    if(model.value.bankIdHasChanged){
      Object.assign(model.value, {
        bankNo: null,
        KHbankId: null,
      });
    }
  },
  { immediate: false } // 避免首次执行
);

defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less">
</style>
  