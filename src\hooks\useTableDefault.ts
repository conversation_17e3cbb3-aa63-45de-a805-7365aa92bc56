import { ref } from "vue";
import type { PaginationProps } from "@/components/LTable/type";
import { SystemSetting } from "@/settings/systemSetting";
import { useMessages } from "./useMessage";
import { isArray, isNullOrUnDef, isObject, isString } from "@/utils/isUtils";
import { createDummyId, duplicateNewCopy } from "@/utils";
type PageVOType = {
  current: number;
  size: number;
};
function argumentsHandler(args) {
  let _tempArgs;
  if (args.length == 3) {
    if (isObject(args[args.length - 1])) _tempArgs = [...args, false];
    else _tempArgs = [null, ...args];
  } else if (args.length === 2) {
    _tempArgs = [null, ...args, false];
  } else if (args.length === 4) {
    _tempArgs = args;
  }
  return _tempArgs;
}

// interface useTableDefaultReturn{
//     isLoading:Ref<boolean>,
//     isAddLoading:Ref<boolean>,
//     isEditLoading:Ref<boolean>,
//     isDeleteLoading:Ref<boolean>,
//     tableData:Ref<[]>,
//     pageTableData:<T>()=>Promise<T>,
//     deleteTableData:<T>()=>Promise<T>,
//     editTableData:<T>()=>Promise<T>,
//     addTableData:<T>()=>Promise<T>
// }

export function useTableDefault({
  getTableTotalRequest,
  pageDataRequest,
  getDataRequest,
  addDataRequest,
  editDataRequest,
  deleteDataRequest,
  defaultPageSize = SystemSetting.pagination.pageSize,
  isPagination = true,
  isIncludeSummary = false,
  isIncludeStatistic = false,
  rateCalcRules,
  compareKeys,
  useDummyId=false
}: {
  getTableTotalRequest?: any
  pageDataRequest?: any;
  addDataRequest?: any;
  getDataRequest?:any;
  editDataRequest?: any;
  deleteDataRequest?: any;
  defaultPageSize?: number;
  isPagination?:boolean;
  isIncludeSummary?:boolean; // 是否包含总计数据，默认false
  isIncludeStatistic?:boolean; // 是否包含统计数据，默认false
  rateCalcRules?:Record<string,Function>,
  compareKeys?:Array<string>,
  useDummyId?:boolean
}) {
  const { createMessageSuccess, createMessageError, createMessageWarning } = useMessages();
  function checkLoadingHandler(flag: boolean): boolean {
    if (flag) {
      createMessageWarning("正在请求中，请勿重复点击");
    }
    return flag;
  }
  const paginationRef = ref<PaginationProps>({
    current: 1,
    total: 1,
    pageSize: defaultPageSize,
  });
  const dataPageParams: {
    data: object;
    pageVO: PageVOType;
  } = {
    data: {},
    pageVO: {
      current: paginationRef.value.current,
      size: paginationRef.value.pageSize,
    },
  };
  const tableData = ref<Array<any>>([]);
  let rawTableData = []
  let lastSortInfo = {
    key:'',
    sortAsc:''
  }
  const isLoading = ref<boolean>(false);
  const isAddLoading = ref<boolean>(false);
  const isEditLoading = ref<boolean>(false);
  const isDeleteLoading = ref<boolean>(false);
  // 总计数据
  const summaryRef = ref();
  // 统计数据
  const statisticRef = ref();
  function calcCompare(){
    if(isArray(compareKeys) && compareKeys.length){
      tableData.value.forEach((item,index)=>{
        compareKeys.forEach(key=>{
          if(!isNullOrUnDef(item[key])){
            if(!isObject(item[key])){
              const _temp = item[key]
              item[key] = {
                val:_temp
              }
              if(index != tableData.value.length-1){
                item[key].prevVal = tableData.value[index+1][key]
              }
            }
          }
        })
      })
    }
  }
  function pageTableData(
    ...args:
      | [{}, PaginationProps, ignoreLoadingCheck?: boolean | undefined]
      | [Event, {}, PaginationProps, ignoreLoadingCheck?: boolean | undefined]
  ) {
    // if(checkLoadingHandler(isLoading.value)) return
    const [event = null, searchParams = {}, pagination = {}, ignoreLoadingCheck = false] = argumentsHandler(args);
    if (!ignoreLoadingCheck && checkLoadingHandler(isLoading.value)) return;
    return new Promise(async (resolve, reject) => {
      if (event) event.preventDefault();
      for(let key in searchParams){
        if(isString(searchParams[key])) searchParams[key] = searchParams[key].trim().replaceAll('\t','')
      }
      let isInitCurrentFlag = false;
      if(Object.keys(searchParams).length !== Object.keys(dataPageParams.data).length){
        isInitCurrentFlag = true;
      }
      else{
        for(let key in searchParams){
          if(searchParams[key] !== dataPageParams.data[key]){
            isInitCurrentFlag = true;
            break;
          }
        }
      }
      isLoading.value = true;
      dataPageParams.data = searchParams;
      dataPageParams.pageVO = {
        current: isInitCurrentFlag?1:pagination.current,
        size: pagination.pageSize,
      };
      try {
        const resp = await pageDataRequest(dataPageParams);
        const {current, records, size, total} = isIncludeSummary?(resp.pageDTO ? resp.pageDTO : resp.statData):resp;
        if(isIncludeSummary) summaryRef.value = resp.sumData;
        if(isIncludeStatistic) statisticRef.value = resp.statistic;
        records.forEach(item=>{
          if(useDummyId){
            item['_dummyId'] = createDummyId()
          }
          if(!isNullOrUnDef(rateCalcRules)){
            for(let key in rateCalcRules){
              const _val = rateCalcRules[key](item)
              item[key] = isNaN(_val)?0:_val
            }
          }
        })
        rawTableData = duplicateNewCopy(records) as []
        tableData.value = records;
        if(lastSortInfo.key){
          sortTableData(lastSortInfo.key,lastSortInfo.sortAsc)
        }
        calcCompare()
        //部分table列表分页总数需要另外请求接口获取，列表接口不返回总页数的情况
        if(getTableTotalRequest && isInitCurrentFlag){
          const data=await getTableTotalRequest({data:dataPageParams.data});
          paginationRef.value.total=Number(data) ?? 0
        }
        paginationRef.value = {
          current: Number(current),
          pageSize: Number(size),
          ...(getTableTotalRequest ? {total: paginationRef.value.total} : { total: Number(total) })
        };
        resolve("");
      } catch (e) {
        createMessageError(`请求失败: ${e}`);
        reject(e);
      }
      isLoading.value = false;
    });
  }
  function getTableData(searchParams={}, ignoreLoadingCheck?: boolean | undefined) {
    if (!ignoreLoadingCheck && checkLoadingHandler(isLoading.value)) return;
    return new Promise(async (resolve, reject) => {
      for(let key in searchParams){
        if(isString(searchParams[key])) searchParams[key] = searchParams[key].trim().replaceAll('\t','')
      }
      isLoading.value = true;
      dataPageParams.data = searchParams;
      try {
        const resp = await getDataRequest(dataPageParams);
        resp.forEach(item=>{
          if(useDummyId){
            item['_dummyId'] = createDummyId()
          }
          if(!isNullOrUnDef(rateCalcRules)){
            for(let key in rateCalcRules){
              const _val = rateCalcRules[key](item)
              item[key] = isNaN(_val)?0:_val
            }
          }
        })
        rawTableData = duplicateNewCopy(resp) as []
        tableData.value = resp
        if(lastSortInfo.key){
          sortTableData(lastSortInfo.key,lastSortInfo.sortAsc)
        }
        calcCompare()
        
        resolve("");
      } catch (e) {
        createMessageError(`请求失败: ${e}`);
        reject(e);
      }
      isLoading.value = false;
    });
  }
  function sortTableData(key:string,sortAsc:"ascend"|"descend"){
    lastSortInfo.key = key;
    lastSortInfo.sortAsc = sortAsc
    if(key){
      tableData.value = (duplicateNewCopy(rawTableData) as []).sort((a,b)=>{
        const valA = Number(`${a[key]}`.replace('%',''))
        const valB = Number(`${b[key]}`.replace('%',''))
        if(!isNaN(valA) && !isNaN(valB)){
          if(sortAsc == 'ascend'){
            return valA - valB
          }
          else {
            return valB - valA
          }
        }
        else return 0
      })
      
    }
    else{
      tableData.value = duplicateNewCopy(rawTableData) as []
    }
    calcCompare()
  }
  function refreshTableData() {
    if(isPagination){
      if (!pageDataRequest) return;
      paginationRef.value.current = 1;
      return pageTableData(dataPageParams.data, paginationRef.value);
    }else{
      if (!getDataRequest) return;
      return getTableData(dataPageParams.data);
    }
  }

  function deleteTableData(...args: [string, boolean] | [Event, string, boolean]) {
    if (checkLoadingHandler(isDeleteLoading.value)) return;
    const [event = null, ids = "", isRefreshTableData = false] = argumentsHandler(args);
    return new Promise(async (resolve, reject) => {
      if (event) event.preventDefault();
      isDeleteLoading.value = true;
      try {
        const resp = await deleteDataRequest(ids);
        createMessageSuccess("删除成功");
        if (isRefreshTableData) refreshTableData();
        resolve(resp);
      } catch (e) {
        createMessageError(`删除失败: ${e}`);
        reject(e);
      }
      isDeleteLoading.value = false;
    });
  }

  function editTableData(...args: [{}, boolean] | [Event, {}, boolean]) {
    if (checkLoadingHandler(isEditLoading.value)) return;
    const [event = null, params = {}, isRefreshTableData = false] = argumentsHandler(args);
    return new Promise(async (resolve, reject) => {
      if (event) event.preventDefault();
      isEditLoading.value = true;
      try {
        const resp = await editDataRequest(params);
        createMessageSuccess("编辑成功");
        if (isRefreshTableData) refreshTableData();
        resolve(resp);
      } catch (e) {
        createMessageError(`编辑失败: ${e}`);
        reject(e);
      }
      isEditLoading.value = false;
    });
  }

  function addTableData(...args: [{}, boolean] | [Event, {}, boolean]) {
    if (checkLoadingHandler(isAddLoading.value)) return;
    const [event = null, params = {}, isRefreshTableData = false] = argumentsHandler(args);
    return new Promise(async (resolve, reject) => {
      if (event) event.preventDefault();
      isAddLoading.value = true;
      try {
        const resp = await addDataRequest(params);
        resolve(resp);
        createMessageSuccess("添加成功");
        if (isRefreshTableData) refreshTableData();
      } catch (e) {
        createMessageError(`添加失败: ${e}`);
        reject(e);
      }
      isAddLoading.value = false;
    });
  }

  function paginationChange(pagination: PaginationProps) {
    Object.assign(paginationRef.value, pagination);
    return pageTableData(dataPageParams.data, paginationRef.value);
  }

  return {
    isLoading,
    isAddLoading,
    isEditLoading,
    isDeleteLoading,
    tableData,
    paginationRef,
    pageTableData,
    getTableData,
    deleteTableData,
    editTableData,
    addTableData,
    paginationChange,
    refreshTableData,
    summaryRef,
    statisticRef,
    sortTableData
  };
}
