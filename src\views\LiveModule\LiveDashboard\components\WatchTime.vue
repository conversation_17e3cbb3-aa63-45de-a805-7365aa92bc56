<template>
    <div class="watch-time-wrapper">
        <span class="time-digit mr-8">{{ hours[0] }}</span>
        <span class="time-digit">{{ hours[1] }}</span>
        <span class="time-separator">时</span>
        <span class="time-digit minutes-color mr-8">{{ minutes[0] }}</span>
        <span class="time-digit minutes-color">{{ minutes[1] }}</span>
        <span class="time-separator">分</span>
        <span class="time-digit mr-8">{{ seconds[0] }}</span>
        <span class="time-digit">{{ seconds[1] }}</span>
        <span class="time-separator">秒</span>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import useLiveDashboard from "../hook/useLiveDashboard";

const { WatchDurationPerPeopleRef } = useLiveDashboard()

const hours = computed(() => {
    const h = Math.floor(WatchDurationPerPeopleRef.value / 3600);
    return String(h).padStart(2, '0').split('');
});

const minutes = computed(() => {
    const m = Math.floor((WatchDurationPerPeopleRef.value % 3600) / 60);
    return String(m).padStart(2, '0').split('');
});

const seconds = computed(() => {
    const s = WatchDurationPerPeopleRef.value % 60;
    return String(s).padStart(2, '0').split('');
});
</script>

<style lang="less" scoped>
.watch-time-wrapper {
    margin-top: 16px;
    font-size: 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .time-digit {
        background: #757070;
        padding: 2px 4px;
        border-radius: 4px;
        height: 60px;
        width: 40px;
        text-align: center;
        line-height: 60px;
    }
    .minutes-color{
        color: #df504f;
    }
    .time-separator {
        margin: 0 2px;
        font-size: 14px;
        color: #fff;
    }
    .mr-8{
        margin-right: 8px;
    }
}
</style>