import { ref } from "vue";
import { useMessages } from "@/hooks";
import { getGlobalConfigs } from "@/services/api";
import { isObject } from "@/utils";
import { type GlobalConfig } from "@/enums";

/**
 * @description 获取全局类型
 */
export default function useGlobalConfig() {
  const globalConfig = ref<GlobalConfig>({});
  const { createMessageError } = useMessages();

  /** 获取 */
  async function getGlobalConfiguration() {
    try {
      const data = await getGlobalConfigs();
      if (isObject(data)) {
        globalConfig.value = data;
      }
    } catch (error) {
      createMessageError("获取系统全局配置失败：" + error);
    }
  }

  return {
    getGlobalConfiguration,
    globalConfig,
  };
}
