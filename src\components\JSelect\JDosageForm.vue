<template>
	<JSelect
		:value="props.value"
		:loading="isLoading"
		:onFocus="handlerFocus"
		:options="dosageList"
		:onClear="handleClear"
		@update:value="onChange"
		placeholder="请选择剂型"
		:filter="customFilter"
	/>
</template>

<script setup lang="ts" name="JDosageForm">
import { ref, watch } from "vue";
import JSelect from "@/components/JSelect/index.vue";
import { isObject } from "@/utils";
import { useMessages } from "@/hooks";
import { getDosageForm } from "@/services/api";

/* Props */
const props = withDefaults(
	defineProps<{
		value?: Array<string | number> | string | number | null;
	}>(),{},
);

/** emits */
const emits = defineEmits<{
	(e: "update:value", selectValue: any): void;
}>();

/* 提示 */
const message = useMessages();

/* 是否加载 */
const isLoading = ref(false);

/** 列表 */
const dosageList = ref([]);

/* 筛选、转化{label: '', value: ''} */
const handleData = (filterData: { [key: string]: string }) => {
	const medicationForms = Object.keys(filterData).map(key => {
        const value = filterData[key];
        return { label: value, value: Number(key) };
    });
	return medicationForms;
};

/** 获取焦点 */
function handlerFocus() {
	if (!dosageList.value.length) {
		getDosageList();
	}
};

/* 获取剂型列表 */
const getDosageList = async () => {
	try {
		isLoading.value = true;
		const result = await getDosageForm({});
		if(isObject(result) && Object.keys(result).length !== 0) {
			dosageList.value = handleData(result);
		}
	} catch (error) {
		message.createMessageError("获取剂型列表失败: " + error);
	} finally {
		isLoading.value = false;
	}
};

/** 前端过滤 */
const customFilter = (keyword, options) => {
	const labelMatch = options.label
		.toLowerCase()
		.includes(keyword.toLowerCase());
	return labelMatch;
};

/** 选中值回调 */
function onChange(value) {
  emits("update:value", value);
}

/** 清空 */
const handleClear = () => {
	emits("update:value", null);
};

/** 监听 */
watch(
	() => props.value,
	(newVal) => {
		if (newVal && dosageList.value.length === 0) {
			getDosageList();
		}
	},
	{ immediate: true },
);
</script>

<style scoped lang="less"></style>
