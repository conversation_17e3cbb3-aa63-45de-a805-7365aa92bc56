<template>
  <n-empty v-bind="$attrs">
    <template #icon>
      <div class="empty-wrapper">
        <img :src="EmptySrc" alt="" />
      </div>
    </template>
  </n-empty>
</template>
<script lang="ts" setup>
import EmptySrc from "@/assets/image/exception/emptyData.png";
import { toRef } from "vue";
const props = withDefaults(
  defineProps<{
    size: "small" | "large";
  }>(),
  {
    size: "small",
  }
);
const sizeRef = toRef(props, "size");
</script>
<style scoped lang="less">
.empty-wrapper {
  width: 100%;
  height: auto;
  img {
    width: 100%;
  }
}
:deep(.n-empty__icon) {
  width: 350px;
  height: auto;
}
</style>
