<template>
    <n-space :wrap="false" id="tree-layout">
      <div class='tree-select-wrapper'>
        <n-input size="small" v-model:value="treeSearchInputRef" placeholder="请输入分类名称" clearable style="margin-bottom: 10px;"/>
        <n-tree
          v-bind="$attrs"
          :show-irrelevant-nodes="false"
          :pattern="treeSearchInputRef"
          block-line
          virtual-scroll
          style="height:calc(100% - 38px);"
        />
      </div>
      <slot name="content"></slot>
    </n-space>
</template>
<script setup lang="ts">
    import { ref } from 'vue';
    const treeSearchInputRef = ref('')
</script>
<style lang="less" scoped>
@import "@/styles/default.less";

:deep(.n-tree .n-tree-node.n-tree-node--highlight .n-tree-node-content .n-tree-node-content__text) {
  border-bottom-color: transparent !important;
}
#tree-layout{
  height: 100%;
}
.tree-select-wrapper{
  box-sizing: border-box;
  width: 250px;
  border: 1px solid #eeeeee;
  height: 100%;
  padding: 14px 10px;
  border-radius: 5px;
}
</style>
