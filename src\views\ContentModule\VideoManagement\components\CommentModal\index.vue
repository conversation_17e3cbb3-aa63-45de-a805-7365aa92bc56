<template>
    <n-modal v-bind="$attrs" preset="card" style="width: 375px; height: 667px" :auto-focus="false" :content-style="{
        padding: '5px 10px',
        borderRadius: '10px',
    }" :header-style="{
        padding: '5px 10px',
        fontSize: '15px',
    }" :on-after-enter="showModal" 
        :on-after-leave="closeModal"
    >
        
        <n-spin :show="loadShow">
            <n-scrollbar style="height: 630px" :on-scroll="handleScroll" >
                <!-- {{ commentData }} -->
                <div v-for="(item,index) in commentList" :key="item.id" class="commentItem" >
                    <template v-if="item.comment " >
                        <!-- <img  :src="transformMinioSrc(item.img) || EmptyImg" /> -->
                    <div class="avatar" >
                        <n-avatar
                        round
                        size="large"
                        :src="item.img || EmptyImg"
                        />
                    </div>
                    <div class="commentInfo" >
                        <div class="commentUser" >
                            <div class="userNickname" >{{item.nickname || "暂无用户名"}}</div>
                            <div v-if="item.userId == videoDetail.publisherId" class="tag" >作者</div>
                        </div>
                        <div class="commentContainer" >{{item.comment}}</div>
                        <div class="handlerContainer" v-if="!item.isDescription" >
                            <div class="left" >
                                <span class="time" >{{commentTimeTransition(item.createTime)}}</span>
                                <span class="delete" > <n-button text type="error" size="tiny" @click="handleDelete(item.id)" >
                                    删除
                                </n-button></span>
                            </div>
                            <div class="likes" > <img :src="likesCount" > {{item.likesCount}}</div>
                        </div>
                    </div>
                    </template>
                </div>
            </n-scrollbar>
    </n-spin>
    </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, toRefs, onMounted, watch, computed } from 'vue';
import { commentByVideo , deleteComment } from "@/services/api";
import EmptyImg from '@/assets/image/system/avatar.png';
import dayjs from "dayjs"
import { transformMinioSrc } from "@/utils/fileUtils";
import { useMessages } from "@/hooks";
const { createMessageSuccess, createMessageError } = useMessages();
import likesCount from "@/assets/image/system/compare/likesIcon.png"
import { _throttle } from "@/utils";

const props = defineProps<{
    videoDetail: any
}>();
const pageVO = reactive({
    "current": 1,
    "size": 10
});
const recordsTotal = ref(0)
const loadShow = ref(false)
const commentList = computed(()=>{
    return [
        {
            isDescription :true,
            img: isHttp(props.videoDetail.customerImg) ? props.videoDetail.customerImg : transformMinioSrc(props.videoDetail.customerImg) || EmptyImg,
            comment:props.videoDetail.description,
            nickname:props.videoDetail.customerName,
            id:'description',
            userId:props.videoDetail.publisherId
        },
        ...commentData.value
    ]
})

function isHttp(url) {
    // 判断字符串是否以 'http' 开头
    return url?.startsWith("http");
}

const commentData = ref([])

const showModal = () => {
    commentData.value = [];
    pageVO.current =1;
    getComment()
}
const closeModal = ()=>{

}


const nextPage = ()=>{
    pageVO.current ++ ;
    getComment()
}
const getComment = ()=>{
    loadShow.value = true;
    commentByVideo({
        data: { videoId: props.videoDetail.id },
        pageVO
    }).then(res => {
        commentData.value.push(...res.records);
        recordsTotal.value = res.total
    }).catch(err => {
        console.log(err, 'err');
        createMessageError(`获取评论失败：${err}`)
    }).finally(()=>{
        loadShow.value = false;
    })
}
const throttleNextPage = _throttle(nextPage,500)


const handleScroll = (e: Event)=>{
    const currentTarget = e.currentTarget as HTMLElement
    if (
      currentTarget.scrollTop + currentTarget.offsetHeight >=
      currentTarget.scrollHeight - 10
    ) {
        
        if(pageVO.current * pageVO.size < recordsTotal.value){
            throttleNextPage()
        }
    }
}


const handleDelete = (selectedListIds:string) => {
    loadShow.value = true
    deleteComment([selectedListIds]).then(res=>{
        createMessageSuccess('删除成功')
        showModal();
    }).catch(err=>{
        console.log(err,'err');
    }).finally(()=>{
        loadShow.value = false
    })


}

/**
 * 格式化时间展示
 * @param {string} timeStr 时间字符串，格式为"YYYY-MM-DD HH:mm:ss"
 * @returns {string} 格式化后的时间展示
 */
 function commentTimeTransition(timeStr) {
  const now = dayjs(); // 当前时间
  const targetTime = dayjs(timeStr, 'YYYY-MM-DD HH:mm:ss');
  const diffInMinutes = now.diff(targetTime, 'minute'); 

  if (diffInMinutes < 5) {
    return '刚刚';
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes}分钟前`;
  } else {
    const diffInHours = now.diff(targetTime, 'hour');
    if (diffInHours < 24) {
      return `${diffInHours}小时前`;
    } else {
      return targetTime.format('YYYY/MM/DD');
    }
  }
}

</script>
<style scoped lang="less">
.commentItem{
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    .avatar{
        flex-shrink: 0;
    }
    .commentInfo{
        .commentUser{
            display: flex;
            align-items: center;
            .userNickname{
                color: #999999;
            }
            .tag{
                margin-left: 5px;
                width: 24px;
                height: 14px;
                background: #FF4D4D;
                border-radius: 2px 2px 2px 2px;
                font-size: 10px;
                color: white;
                text-align: center;
                line-height: 14px;
            }
        }
        
        .handlerContainer{
            display: flex;
            color: #999999;
            justify-content: space-between;
            width: 300px;
            .time{
                font-size: 12px;
                margin-right: 10px;
            }
            .likes{
                img{
                    width: 13px;
                    height: 13px;
                }
            }
        }
    }
}
</style>