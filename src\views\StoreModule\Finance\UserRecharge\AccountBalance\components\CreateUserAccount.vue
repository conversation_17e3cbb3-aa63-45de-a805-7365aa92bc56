<template>
  <JModal
    v-model:show="show"
    width="580"
    :title="title"
    @after-leave="closeModal"
    @positive-click="_save"
    :positiveButtonProps="{
      loading: isLoading,
    }"
  >
    <n-form
      ref="formRef"
      :rules="rules"
      :model="model"
      label-width="150"
      label-placement="left"
      require-mark-placement="right-hanging"
      :style="{
          width: '100%',
        }"
    >
      <n-grid :cols="8" :x-gap="24">
        <n-form-item-gi :span="24" label="用户昵称" path="accountUserId">
          <UserSelect v-model:value="model.accountUserId" />
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </JModal>
</template>

<script setup lang="ts" name="CreateUserAccount">
import { ref } from "vue";
import { useMessages } from "@/hooks";
import { createAccount } from "@/services/api/financialApi/userRecharge";
import UserSelect from "./UserSelect.vue";

const initParams = {
  accountUserId: "",
};
const title = ref("开通用户充值账户");
const model = ref({ ...initParams });
const props = ref<any>({});
/* 提示信息 */
const { createMessageSuccess, createMessageError } = useMessages();
/* 模态框显隐状态 */
const show = ref(false);
/* 表单规则 */
const rules = {
  accountUserId: {
    required: true,
    trigger: ["blur", "change"],
    message: "请输入客户ID",
  },
};
const acceptParams = ({ refreshTable }) => {
  show.value = true;
  props.value.refresh = refreshTable;
};
/* 表单实例 */
const formRef = ref(null);
/* 表单参数初始化 */
/* 清空表单 */
const formDataReset = () => {
  model.value = { ...initParams };
};
/* 关闭弹窗 */
const closeModal = () => {
  formDataReset();
  isLoading.value = false;
  // 弹窗取消
  show.value = false;
};
/* 确认--保存 */
const isLoading = ref(false);
const _save = (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors && !isLoading.value) {
      // if (diffMinutes < 30) {
      //   createMessageError("直播计划结束时间与开始时间间隔不得少于30分钟");
      //   return;
      // }
      try {
        isLoading.value = true;
        await createAccount(model.value.accountUserId);
        createMessageSuccess(`开通用户充值账户成功`);
        // 刷新表格数据
        props.value.refresh();
        closeModal();
      } catch (e) {
        createMessageError(e);
        isLoading.value = false;
      }
    }
  });
};
defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less"></style>
