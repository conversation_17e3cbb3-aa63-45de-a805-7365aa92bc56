<template>
  <NLayout has-sider style="border-radius: 4px;position: relative;">
    <NLayoutSider 
      collapse-mode="width" 
      :collapsed-width="0" 
      :collapsed="collapsed" 
      :width="props.treeWidth" 
      bordered
    >
      <div v-show="!collapsed" class="tree-content-wrapper">
        <slot name="tree-content"></slot>
      </div>
    </NLayoutSider>
    <NLayoutContent>
      <div class="inner-page-height">
        <slot></slot>
      </div>
    </NLayoutContent>
    <!-- <FoldIcon :tree-width="props.treeWidth" :collapsed="collapsed" @click="toggle" /> -->
  </NLayout>
</template>

<script lang="ts" setup>
import {  } from "vue";
import { NLayout, NLayoutSider, NLayoutContent, NTooltip } from "naive-ui";
import { useBoolean } from "@/hooks";
/** 相关组件 */
import FoldIcon from "./components/FoldIcon.vue";

defineOptions({
  name: 'TreeContentLayout'
});

/** props */
const props = withDefaults(defineProps<{
  treeWidth?: number; // 左侧宽度
}>(), {
  treeWidth: 200,
})

/** 是否展开 */
const { bool: collapsed, toggle } = useBoolean(false);
</script>

<style lang="less" scoped>
.tree-content-wrapper {
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}
</style>
