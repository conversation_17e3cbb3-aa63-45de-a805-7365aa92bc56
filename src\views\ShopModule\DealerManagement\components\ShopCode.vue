<template>
  <n-modal v-model:show="modalShow" :auto-focus="false" @after-leave="handleClose">
    <n-card
      class="regional-manager-registration-code-card"
      style="width: 420px"
      :bordered="false"
      size="small"
      title="门店注册码"
      closable
      @close="handleClose"
    >
      <n-spin :show="loading" description="加载中...">
        <div class="poster-container" id="poster">
          <img class="poster-img" :src="ShopCodeBg" />
          <n-qr-code v-if="linkCode" class="link-code-img" :value="linkCode" error-correction-level="Q" size="200" />
        </div>
      </n-spin>
    </n-card>
  </n-modal>
</template>
<script setup lang="ts">
import { computed, reactive, ref, onMounted } from "vue";
import { useMessages } from "@/hooks/useMessage";
import { downloadFile } from "@/utils/fileUtils";
import ShopCodeBg from "@/assets/image/system/shop_code_bg.png";
import { getSubStructQrCode } from "@/services/api";
const { createMessageError, createMessageSuccess } = useMessages();
const linkCode = ref()
const loading = ref(false);
const emit = defineEmits(["update:show"]);  
const modalShow = ref(false)
const handleClose = () => {
  linkCode.value = undefined
  modalShow.value = false;
}

const acceptParams = async (params) => {
  modalShow.value = true;
  await getSubStructQrCodeFn(params?.id)
};
const getSubStructQrCodeFn = async (structureId) => {
  loading.value = true;
  try {
    linkCode.value = await getSubStructQrCode(structureId);
  } catch (error) {
    console.log(error);
  }finally {
    loading.value = false
  }
}
defineExpose({
  acceptParams,
});
</script>

<style lang="less">
.regional-manager-registration-code-card {
  .n-card__content {
    display: flex;
    flex-direction: column;
    align-items: center;
    .poster-container {
      position: relative;
      img {
        display: block;
      }
      .poster-img {
        width: 400px;
        border-radius: 16px;
      }
      .link-code-img {
        position: absolute;
        left: 50%;
        top: 39%;
        transform: translateX(-50%);
      }
    }

    .n-button {
      margin-top: 8px;
      padding: 4px 25px;
    }
  }
}
</style>
