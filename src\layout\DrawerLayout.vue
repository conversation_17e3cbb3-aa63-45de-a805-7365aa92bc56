<template>
  <div class="inner-page-height layout-warp">
    <!-- 标题 -->
    <n-card size="small" :bordered="false" :content-style="{ padding: '10px 12px' }">
      <div style="height: 28px; display: flex; align-items: center;">
        <n-button text class="mr-12" @click="emits('clickIcon')">
          <template #icon>
            <ArrowBackOutline size="18" />
          </template>
        </n-button>
        <!-- 可支持props与插槽 -->
        <slot name="title">
          <n-breadcrumb style="font-weight: 600; line-height: 28px; font-size: 16px;">
            {{ titleRef }}
          </n-breadcrumb>
        </slot>
      </div>
    </n-card>
    <!-- 内容 -->
    <n-card size="small" :bordered="false" :content-style="{ padding: '8px 12px' }" class="information"
      v-if="isShowInformationRef"
      :style="{ height: isNumber(informationDomHeightRef) ? `${informationDomHeightRef}px` : `${informationDomHeightRef}` }">
      <slot name="information"></slot>
    </n-card>
    <!-- tabs -->
    <div class="default-bg">
      <n-tabs size="small" style="flex: 1;" ref="tabRef" v-bind="$attrs"  type="line" animated tab-style="padding:8px 0px;" :tabs-padding="12"
        :pane-class="`tab-pane-default ${paneClassRef}`" :pane-style="{height:paneHeight?`${paneHeight}px`:null}">
        <template v-if="onlyTabsRef">
          <n-tab v-for="tab in tabsDataRef" :key="tab.key" :name="tab.key">
            {{ tab.label }}
          </n-tab>
        </template>
        <n-tab-pane v-else v-for="tab in tabsDataRef" :name="tab.key" :key="tab.key" :tab="tab.label"
          :display-directive="tab.directive ? tab.directive : 'show'">
          <slot :name="tab.key"></slot>
        </n-tab-pane>
      </n-tabs>
      <div v-if="onlyTabsRef" :class="`tab-pane-default ${paneClassRef}`">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="drawerLayout">
import { toRefs,ref,computed } from 'vue';
import { isNumber } from "@/utils/isUtils";
import { ArrowBackOutline } from "@vicons/ionicons5";

interface DrawerLayoutProps {
  title?: string; // 标题
  informationDomHeight?: string | number; // 信息区域高度 ---> 非必传(默认值120)
  isShowInformation?: boolean; // 是否展示信息区域 ---> 非必传(默认值true)
  tabsData: Array<{
    label: string;
    key: string | number;
    directive?: "if" | "show";
  }>;
  height?: string;
  paneClass?: string;
  onlyTabs?: boolean;
}

const tabRef = ref(null)
const paneHeight = computed(()=>{
 return isNumber(tabRef.value?.$el?.offsetHeight)?tabRef.value.$el.offsetHeight - 36 :null
})
const props = withDefaults(defineProps<DrawerLayoutProps>(), {
  informationDomHeight: '120px',
  isShowInformation: true,
});

const {
  title: titleRef,
  isShowInformation: isShowInformationRef,
  informationDomHeight: informationDomHeightRef,
  onlyTabs: onlyTabsRef,
  paneClass: paneClassRef,
  tabsData: tabsDataRef,
} = toRefs(props);

const emits = defineEmits<{
  (e: "clickIcon"): void;
}>();
</script>

<style scoped lang="less">
@import "@/styles/default.less";
@import "@/styles/defaultVar.less";

.layout-warp {
  display: flex;
  flex-direction: column;
  width: 100%;

  .information {
    background-color: #fff;
    margin-top: @blank-page-padding;
    width: 100%;
  }

  .default-bg {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    margin-top: @blank-page-padding;
    width: 100%;
  }

  .tab-pane-default {
    padding-top: 0px;
    height: calc(@inner-bg-height - 48px - 120px - 44px - @blank-page-padding * 2);
  }
}
</style>
