<template>
	<!-- 分页组件 -->
	<n-pagination
		:page-size="pageable.pageSize"
		:item-count="pageable.total"
		:page-sizes="pageSizes"
		@update:page="handleCurrentChange"
		@update:page-size="handleSizeChange"
		:display-order="['pages', 'size-picker', 'quick-jumper']"
		show-quick-jumper
		show-size-picker
	>
		<template #prefix> 共 {{ pageable.total }} 项 </template>
	</n-pagination>
</template>

<script setup lang="ts" name="pagination">
interface Pageable {
	pageNum: number;
	pageSize: number;
	total: number;
}

interface PaginationProps {
	pageable: Pageable;
	handleSizeChange: (size: number) => void;
	handleCurrentChange: (currentPage: number) => void;
}

defineProps<PaginationProps>();

const pageSizes = [
	{
		label: "10 \/ 页",
		value: 10,
	},
	{
		label: "50 \/ 页",
		value: 50,
	},
	{
		label: "100 \/ 页",
		value: 100,
	},
	{
		label: "200 \/ 页",
		value: 200,
	},
	{
		label: "300 \/ 页",
		value: 300,
	},
];
</script>
