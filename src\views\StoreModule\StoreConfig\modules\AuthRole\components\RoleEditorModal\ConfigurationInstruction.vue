<template>
  <JModal
    v-model:show="show"
    title="经销商或群管角色配置说明"
    width="800"
    @after-leave="closeModal"
    :is-scale="false"
    :positive-text="null"
    :negative-text="null"
  >
    通过角色配置经销商或群管能看到的页面及页面上的功能权限，可以实现经销商或群管登录后台查看各自会员、订单、数据报表等信息的需求，以及进行客户信息编辑、订单发货、报表导出等操作。
    <div class="wrapper">
      <n-scrollbar style="height: 500px;">
        <n-card title="配置说明:" :bordered="false" size="small">
          <div class="default-rule-wrapper">
            <n-data-table
              :columns="COLUMNS"
              :data="POINTSRULE"
              :row-key="row => row.id"
              :style="{ minHeight: `${430}px` }"
              default-expand-all
              flex-height
              :single-line="false"
              size="small"
            />
          </div>
        </n-card>
      </n-scrollbar>
    </div>
    注意：如果经销商或群管只能查看数据，不能对数据进行操作，请不要勾选【功能权限】列内容。
  </JModal>
</template>

<script setup lang="ts">
import { ref } from "vue";
/** 相关组件 */
import JModal from "@/components/JModal/index.vue";
import {
  COLUMNS,
  POINTSRULE,
} from "./setting";

/** 弹窗显隐 */
const show = ref(false);
/** 表格数据 */
const tableDataRef = ref([]);
/* 接收父组件传过来的参数 */
const acceptParams = () => {
  show.value = true;
};

/* 关闭弹窗之后 */
const closeModal = () => {

};

defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less">
.wrapper {
  width: 100%;
  height: 100%;
  .title {
    font-size: 16px;
    font-weight: 600;
  }
}
:deep(.n-scrollbar > .n-scrollbar-container > .n-scrollbar-content) {
  padding-right: 18px;
}
</style>
