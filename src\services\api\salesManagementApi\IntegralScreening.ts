import { defHttp } from "@/services";

/** 积分筛选 */
export const enum IntegralScreeningApi {
    add = "/pointSift/add",
    update = "/pointSift/update",
    get = "/pointSift/get",
    delete = "/pointSift/delete",
    list = "/pointSift/list",
}

/**
 * @description 新增积分筛选
 */
export function addPointSift(_params) {
    return defHttp.post({
        url: IntegralScreeningApi.add,
        params: {
            data: _params,
        },
    });
}

/**
 * @description 修改积分筛选
 */
export function updatePointSift(_params) {
    return defHttp.put({
        url: IntegralScreeningApi.update,
        params: {
            data: _params,
        },
    });
}

/**
 * @description 根据积分筛选ID查询
 */
export function getPointSift(id: string) {
    return defHttp.get({
        url: IntegralScreeningApi.get + "?id=" + id,
    });
}

/**
 * @description 根据ID删除积分筛选
 */
export function deletepointSiftById(id: string) {
    return defHttp.delete({
        url: IntegralScreeningApi.delete,
        requestConfig: {
            isQueryParams: true,
        },
        params: {
            id
        },
    });
}

/**
 * @description 查询所有积分筛选
 */
export function getAllPointSift(_params) {
    return defHttp.post({
        url: IntegralScreeningApi.list,
        params: _params,
    });
}