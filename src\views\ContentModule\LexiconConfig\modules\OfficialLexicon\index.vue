<template>
  <div class="wrapper">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :isNeedCollapse="false"
      :isTableSelection="false"
      :pagination="paginationRef"
      @paginationChange="paginationChange"
      @selectedKeysChange="selectedKeysChange"
    >
      <template #searchForm>
        <n-form
          :model="model"
          :show-feedback="false"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          size="small"
          :style="{ width: '100%' }"
        >
          <n-form-item label="敏感词">
            <n-input @clear="handleClear" clearable @keydown.enter="handleSearch" size="small" v-model:value="searchTagVal" type="text" placeholder="请输入需要查找的标签" >
              <template #suffix>
                <n-icon style="cursor: pointer;" @click="handleSearch" :component="SearchOutline" />
              </template>
            </n-input>
          </n-form-item>
        </n-form>
      </template>
    </FormLayout>
  </div>
</template>

<script setup lang="tsx" name="OfficiaLexicon">
import { onMounted, ref } from "vue";
import { useMessages } from "@/hooks";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import {
  sensitiveWordPage,
} from "@/services/api/contentConfigApi";
import { SearchOutline } from "@vicons/ionicons5";
const { createMessageSuccess, createMessageError } = useMessages();
/* 表格方法Hook */
const {
  isLoading,
  tableData,
  paginationRef,
  paginationChange,
  pageTableData,
  refreshTableData,
  sortTableData,
} = useTableDefault({
  pageDataRequest: sensitiveWordPage,
});
const searchTagVal = ref(null)
/* 表格项 */
const tableColumns = [
  {
    title: "序号",
    width: 20,
    fixed: "left",
    key: "index",
    align: "center",
    render: (renderData: object, index: number) => {
      return `${index + 1}`;
    },
  },
  {
    title: "敏感词",
    key: "word",
    align: "center",
    summaryTitle:"",
    width: 160,
  },
];
/** 选中行数据 */
const rowData = ref([]);
/** 获取选中行Key */
function selectedKeysChange(key, tableData) {
  rowData.value = tableData.map(({ _dummyId, ...rest }) => rest);
}
const handleSearch = ()=>{
  tableSearch()
}
const handleClear = ()=>{
  searchTagVal.value = null
  tableSearch()
}
/* 刷新列表 */
const tableSearch = () => {
  pageTableData({
    type:0,
    searchKey:searchTagVal.value
  }, paginationRef.value);
};

/* 组件挂载 */
onMounted(() => {
  tableSearch()
});
</script>

<style lang="less" scoped>
@import "@/styles/default.less";
@import "@/styles/defaultVar.less";

.wrapper {
  width: 100%;
  height: 100%;
}

</style>
