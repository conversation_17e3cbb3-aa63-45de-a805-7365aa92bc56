import {defHttp} from '@/services';

export const enum Reception {
    // presPage = '/pres/pageForDoctor',
    // 1.2.1版本 待接诊-分页查询
    presPage = '/pres/page/doctor/toBeConsulted',
    presCountToBeConsulted = '/pres/countToBeConsulted',
    presBatchConsult = '/pres/batchConsult',
    presConsult = '/pres/consult',
    presCancelConsult = '/pres/cancelConsult',
    getPresReceiveEndTime = '/pres/getReceiveEndTime', // 获取问诊单结束时间
    presUploadVideo = '/pres/uploadVideo',
    
    addForVideo = '/formulary/addForVideo', // 开处方-视频问诊
    presCompletePres = '/pres/completePres', // 预约问诊-结束问诊
    getPresCardInfo = '/pres/getPresCardInfo', // '获取问诊卡信息
    getPresAuth = '/doctorEntity/getPresAuth', // 获取医生是否有开方权限
    getReceptionRemind = '/remind/get/consult' // 待接诊提醒
}

//待接诊-分页查询
export function receptionPresPage(params) {
    return defHttp.post({
        url: Reception.presPage,
        params
    });
}

//待接诊-总数
export function receptionPresCountToBeConsulted(params) {
    return defHttp.get({
        url: Reception.presCountToBeConsulted,
    })
}

//待接诊-单独接诊
export function receptionPresConsult(params) {
    return defHttp.post({
        url: Reception.presConsult,
        params
    })
}

//待接诊-批量接诊
export function receptionPresBatchConsult(params) {
    return defHttp.post({
        url: Reception.presBatchConsult,
        params
    })
}


//待接诊-退诊
export function receptionPresCancelConsult(params) {
    return defHttp.delete({
        url: Reception.presCancelConsult,
        params,
        requestConfig: {
            isQueryParams: true,
        },
    });
}

// 根据获取问诊数据、问诊结束时间
export function getPresReceiveEndTime(params) {
    return defHttp.get({
        url: Reception.getPresReceiveEndTime,
        params,
        requestConfig: {
            isQueryParams: true
        }
    })
}

//待接诊-上传视频
export function receptionPresUploadVideo(id,params,onUploadProgress?: (percent) => void) {
    return defHttp.post({
        url: Reception.presUploadVideo + "?id=" + id,
        params,
        options: {
            timeout: 0,
            onUploadProgress: onUploadProgress ? onUploadProgress : function () {},
        },
        requestConfig: {
            requestContentType: "form-data",
        },
    })
}

/**
 * 添加处方-视频问诊开方
 * @param params
 */
export function doctorEndAddForVideo(params) {
    return defHttp.post({
        url: Reception.addForVideo,
        params
    });
}

/**
 * 预约问诊-结束问诊
 * @param params
 */
export function doctorPresCompletePres(params) {
    return defHttp.delete({
        url: Reception.presCompletePres,
        params,
        requestConfig: {
            isQueryParams: true
        }
    })
}

/**
 * 获取医生是否有开方权限
 * @param params
 */
export function doctorGetPreAuth(params) {
    return defHttp.get({
        url: Reception.getPresAuth,
        params,
        requestConfig: {
            isQueryParams: true
        }
    })
}

/** 待接诊提醒 */
export function getReceptionRemind(){
    return defHttp.post({
        url: Reception.getReceptionRemind,
    });
}

/**
 * 获取问诊单详情
 * @param params
 */
export function doctorGetPresCardInfo(params) {
    return defHttp.get({
        url: Reception.getPresCardInfo,
        params,
        requestConfig: {
            isQueryParams: true
        }
    })
}
