import { hasAuth } from "@/utils/auth/authUtils";
import { OrganizationManagementAuth } from "@/enums/authKeys";

/** 新增 */
export const hasOrganizationManagementAddAuth = function(){
    return hasAuth(OrganizationManagementAuth.OrganizationManagementAdd.key);
}()

/** 编辑 */
export const hasOrganizationManagementEditAuth = function(){
    return hasAuth(OrganizationManagementAuth.OrganizationManagementEdit.key);
}()

/** 删除 */
export const hasOrganizationManagementDeleteAuth = function(){
    return hasAuth(OrganizationManagementAuth.OrganizationManagementDelete.key);
}()
