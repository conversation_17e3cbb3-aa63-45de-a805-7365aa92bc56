<template>
  <JModal v-model:show="show" width="580" title="进入时间 - 离开时间" @after-leave="closeModal">
    <div v-if="timeList.length" class="view-data-details-box">
      <div class="view-data-details-item" v-for="(item, index) in timeList" :key="index">
        {{ item.watchStart }} - {{ item.watchEnd }}
      </div>
    </div>
    <div v-else class="no-view-data">暂无数据</div>
    <template #footer>
      <n-flex justify="end">
        <n-button
          size="small"
          :style="{
            minWidth: '72px',
            minHeight: '32px',
            '--n-padding': '4px 11px',
            '--n-border-radius': '5px',
          }"
          @click="closeModal"
        >
          关闭
        </n-button>
      </n-flex>
    </template>
  </JModal>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { useMessages } from "@/hooks";
import moment from "moment";
import { getViewDataDetails } from "@/services/api";
const _params = {
  data: {
    id: "",
  },
  pageVO: {
    current: 1,
    size: 10,
  },
};
const timeList = ref([]);
/* 提示信息 */
const { createMessageSuccess, createMessageError } = useMessages();
/* 模态框显隐状态 */
const show = ref(false);
const acceptParams = async id => {
  show.value = true;
  try {
    timeList.value = await getViewDataDetails({ id });
  } catch (error) {
    createMessageError(error.message);
  }
};
/* 关闭弹窗 */
const closeModal = () => {
  timeList.value = [];
  // 弹窗取消
  show.value = false;
};

defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less">
@import "@/styles/scrollbar.less";
.view-data-details-box {
  height: 400px;
  font-size: 16px;
  overflow-y: auto;
  .scrollbar();
}
.no-view-data {
  height: 400px;
  font-size: 16px;
  line-height: 400px;
  text-align: center;
}
</style>
