<template>
  <div
      v-show="isShowReadStatus"
      :class="{
      'message-label': true,
      'unread': isUseUnreadStyle,
      'finger-point': isHoverFingerPointer,
    }"
      @click="openReadUserPanel"
  >
    <span>{{ readStatusText }}</span>
  </div>
</template>

<script setup lang="ts">
import {computed, ref, onMounted, onUnmounted} from 'vue';
import type {Message} from "@tencentcloud/chat";
import TencentCloudChat from "@tencentcloud/chat";

interface IProps {
  message: Message;
}

interface IEmits {
  (e: 'openReadUserPanel'): void;
}

const emits = defineEmits<IEmits>();
const props = withDefaults(defineProps<IProps>(), {
  message: () => ({}) as Message,
});

// const ReadStatus = TencentCloudChat.getFeatureConfig('ReadStatus');

enum ReadState {
  Read,
  Unread,
  AllRead,
  NotShow,
  PartiallyRead,
}

const TYPES = TencentCloudChat.TYPES;
// User-level read receipt toggle has the highest priority.
const isDisplayMessageReadReceipt = ref<boolean>();

onMounted(() => {

});

onUnmounted(() => {

});

const isShowReadStatus = computed<boolean>(() => {
  return false;
});

const readState = computed<ReadState>(() => {
  const {conversationType, needReadReceipt = false, isPeerRead = false} = props.message;
  const {readCount = 0, unreadCount = 0, isPeerRead: isReceiptPeerRead = false} = props.message.readReceiptInfo;
  if (conversationType === 'C2C') {
    if (needReadReceipt) {
      return isReceiptPeerRead ? ReadState.Read : ReadState.Unread;
    } else {
      return isPeerRead ? ReadState.Read : ReadState.Unread;
    }
  } else if (conversationType === 'GROUP') {
    if (needReadReceipt) {
      if (readCount === 0) {
        return ReadState.Unread;
      } else if (unreadCount === 0) {
        return ReadState.AllRead;
      } else {
        return ReadState.PartiallyRead;
      }
    } else {
      return ReadState.NotShow;
    }
  }
  return ReadState.Unread;
});

const readStatusText = computed(() => {
  const {readCount = 0} = props.message.readReceiptInfo;
  switch (readState.value) {
    case ReadState.Read:
      return '已读';
    case ReadState.Unread:
      return '未读';
    case ReadState.AllRead:
      return '全部已读';
    case ReadState.PartiallyRead:
      return `${readCount}人已读}`;
    default:
      return '';
  }
});

const isUseUnreadStyle = computed(() => {
  const {conversationType} = props.message;
  if (conversationType === 'C2C') {
    return readState.value !== ReadState.Read;
  } else if (conversationType === 'GROUP') {
    return readState.value !== ReadState.AllRead;
  }
  return false;
});

const isHoverFingerPointer = computed<boolean>(() => {
  return (
      props.message.needReadReceipt
      && props.message.conversationType === 'GROUP'
      && (readState.value === ReadState.PartiallyRead || readState.value === ReadState.Unread)
  );
});

function openReadUserPanel() {
  if (isHoverFingerPointer.value) {
    emits('openReadUserPanel');
  }
}

function onDisplayMessageReadReceiptUpdate(isDisplay: boolean) {
  isDisplayMessageReadReceipt.value = isDisplay;
}
</script>

<style scoped lang="less">
.message-label {
  align-self: flex-end;
  font-size: 12px;
  color: #b6b8ba;
  word-break: keep-all;
  flex: 0 0 auto;

  &.unread {
    color: #679ce1 !important;
  }
}

.finger-point {
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
}
</style>
