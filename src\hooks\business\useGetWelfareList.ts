import { ref } from "vue";
import { isArray } from "@/utils";
import { useMessages } from "@/hooks";
import { createCacheStorage } from "@/utils/cache/storageCache";
import { CacheConfig } from "@/utils/cache/config";
import { getCouponTypeList } from "@/services/api";

export default function useGetWelfareList() {
  /* 提示 */
  const message = useMessages();

  /** 列表 */
  const selectListData = ref([]);

  /* 是否加载 */
  const isLoading = ref(false);

  /* 筛选、转化 */
  const handleData = filterData => {
    return filterData.map(item => ({ label: item.name, value: item.id }));
  };

  /* 获取下拉列表 */
  const getSelectList = async () => {
    try {
      isLoading.value = true;
      const couponTypeList = createCacheStorage(CacheConfig.CouponTypeList);
      const oldCouponTypeListData = couponTypeList.get();
      if (oldCouponTypeListData) {
        selectListData.value = handleData(oldCouponTypeListData);
      } else {
        const result = await getCouponTypeList({});
        if (isArray(result)) {
          selectListData.value = handleData(result);
          couponTypeList.set(result);
        }
      }
    } catch (error) {
      message.createMessageError("获取福利券分类失败: " + error);
    } finally {
      isLoading.value = false;
    }
  };

  /** 清空缓存 */
  function clearCache() {
    const couponTypeList = createCacheStorage(CacheConfig.CouponTypeList);
    couponTypeList.remove();
  }

  return {
    selectListData,
    isLoading,
    getSelectList,
    clearCache
  };
}
