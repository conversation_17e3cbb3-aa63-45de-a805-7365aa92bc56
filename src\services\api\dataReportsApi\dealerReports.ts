import { defHttp } from "@/services";

/** 经销商统计 */
export const enum DealerReportsApi {
  dealerStatData = "/report/dealerStatData",
  dealerStatDataExport = "/report/dealerStatDataExport",
}

/** 获取经销商统计 */
export function getDealerStatData(params) {
  return defHttp.post({
    url: DealerReportsApi.dealerStatData,
    params,
  });
}

/** 经销商统计导出 */
export function dealerStatDataExport(params) {
  return defHttp.post({
    url: DealerReportsApi.dealerStatDataExport,
    requestConfig: {
      responeseType: "stream",
    },
    params,
  });
}
