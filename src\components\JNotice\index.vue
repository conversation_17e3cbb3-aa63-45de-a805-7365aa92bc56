<template>
    <div class="store-notice-info">
        <span class="store-notice-icon">
            <n-icon size="16">
                <AlertCircleOutline />
            </n-icon>
        </span>
        <div class="store-notice-content">
            <slot></slot>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { AlertCircleOutline } from "@vicons/ionicons5";

defineOptions({ name: 'JNotice' });

</script>


<style lang="less" scoped>
.store-notice-info {
    background: #e1eaff;
    padding: 6px 0 6px 12px;
    font-size: 14px;
    font-weight: 600;
    border-radius: 6px;
    display: flex;
    align-items: center;
    text-align: left;
    .store-notice-icon {
        align-self: baseline;
        margin-top: 4px;
        margin-right: 8px;
        color: #3370ff;
    }
    .store-notice-content {
        padding-right: 24px;
    }
}
</style>