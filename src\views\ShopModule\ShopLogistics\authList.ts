import { StoreLogisticsAuth } from "@/enums/authKeys";
import { hasAuth } from "@/utils/auth/authUtils";

/** 创建物流单 */
export const hasCreatAuth = (function () {
  return hasAuth(StoreLogisticsAuth.creat.key);
})
/** 编辑 */
export const hasUpdateAuth = (function () {
  return hasAuth(StoreLogisticsAuth.logistics.key);
})
/** 查看物流 */
export const hasLogisticsAuth = (function () {
    return hasAuth(StoreLogisticsAuth.logistics.key);
})
/** 删除 */
export const hasDeleteAuth = (function () {
  return hasAuth(StoreLogisticsAuth.delete.key);
})
/** 批量删除 */
export const hasBatchDeleteAuth = (function () {
    return hasAuth(StoreLogisticsAuth.batchDelete.key);
})