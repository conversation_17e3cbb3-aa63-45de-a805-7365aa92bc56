<template>
  <n-card 
    size="small" 
    style="height: 100%;"
    :content-style="{ padding: 0 }"
    :footer-style="{
      padding: '12px',
      boxShadow: 'rgba(17, 17, 26, 0.1) 0px 4px 16px, rgba(17, 17, 26, 0.05) 0px 8px 32px'
    }"
  >
    <div class="wrapper">
      <n-spin :show="loadShow" class="h-full">
				<n-scrollbar style="height: 100%">
          <n-form
            ref="formRef"
            :model="model"
            :rules="rules"
            label-width="auto"
            label-placement="left"
            require-mark-placement="right-hanging"
            size="small"
            :style="{
              width: '100%',
            }"
          >
            <n-grid :cols="24" :x-gap="24" responsive="self">
              <n-gi :span="24">
                <div class="title-wrapper">
                  <div class="title-line"></div>
                  <span>小程序健康相关模块展示设置</span>
                </div>
                <div style="margin-bottom: 16px;">
                  根据版本号设置小程序端是否展示健康类内容，未设置的版本号将默认展示。
                </div>
                <n-form-item-gi :span="24" label="版本号" path="sto_version_number">
                  <n-input v-model:value="model.sto_version_number.value" type="text" :maxlength="10" style="width: 170px;" />
                </n-form-item-gi>
                <n-form-item-gi :span="24" label="展示内容">
                  <n-radio-group v-model:value="model.sto_display_content.value" name="sto_display_content">
                    <n-space>
                      <n-radio v-for="song in radio" :key="song.value" :value="song.value">
                        {{ song.label }}
                      </n-radio>
                    </n-space>
                  </n-radio-group>
                </n-form-item-gi>
              </n-gi>
            </n-grid>
          </n-form>
        </n-scrollbar>
      </n-spin>
    </div>
    <template #footer>
      <n-space justify="end">
        <n-button type="info" @click="save">
          保存
        </n-button>
      </n-space>
    </template>
  </n-card>
</template>

<script setup lang="tsx" name="campPeriodDetails">
import { ref, onMounted } from "vue";
import { useMessages } from "@/hooks";
import { deepClone } from "@/utils";
const { createMessageSuccess, createMessageError,  } = useMessages();
import { getParamsByGroupName, batchUpdateSysConfig } from "@/services/api";
/* 表单参数初始化 */
const initParams = {
  sto_version_number: {
    value:'1.0.0',
    id:'',
    key:''
  },
  sto_display_content:{
    value:'true',
    id:'',
    key:''
  },
};
/* 表单实例 */
const formRef = ref(null);
const loadShow = ref(true)
const model = ref(deepClone(initParams));
const radio = [
  {
    value:'true',
    label:"是"
  },{
    value:'false',
    label:"否"
  }
]
/* 表单规则 */
const rules = {
  sto_version_number: {
    type: "string",
    required: true,
    trigger: ["blur", "change"],
    message: "请输入小程序版本号",
    validator: () => {
      return !!model.value.sto_version_number.value;
    },
  },
};

function fromInit() {
  loadShow.value = true
  try{
    getParamsByGroupName({
      groupName:'devConfig'
    }).then(e=>{
      e.forEach((item: {
        id: string; configName: string, value: any,key:string
      })=>{
        model.value[item.configName] = {
          value:item.value,
          id:item.id,
          key:item.key
        }
      })
    })
  }catch(err){
    createMessageError(`获取开发配置失败：${err}`);
  }finally {
    loadShow.value = false
  }
}

function save() {
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      loadShow.value = true
      try{
        let obj_list = []
        for (const objListKey in model.value) {
          obj_list.push({
            groupName: "devConfig",
            id: model.value[objListKey].id + '',
            key: model.value[objListKey].key,
            configName: objListKey,
            value: model.value[objListKey].value,
          })
        }
        batchUpdateSysConfig({ data:obj_list }).then(e=>{
          createMessageSuccess(`操作成功!`);
          setTimeout(fromInit,300)
        })
      }catch(err){
        createMessageError(`操作失败：${err}`);
        loadShow.value = false
      }
    }
  });
}
/* 组件挂载 */
onMounted(() => {
  fromInit()
});
</script>

<style scoped lang="less">
@import "@/styles/default.less";
@import "@/styles/defaultVar.less";

.wrapper {
  width: 100%;
  height: calc(@inner-bg-height - 60px);
  box-sizing: border-box;
  .h-full {
    height: 100%;
  }
}

:deep(.n-input__input-el){
  text-overflow: ellipsis;
}
:deep(.n-scrollbar > .n-scrollbar-container > .n-scrollbar-content) {
	padding: 12px 16px;
}
:deep(.n-spin-content) {
	height: 100%;
}

.title-wrapper {
  height: 30px;
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 6px;

  .title-line {
    width: 4px;
    height: 60%;
    background-color: @primary-color;
    margin-right: 5px;
  }
}
</style>
