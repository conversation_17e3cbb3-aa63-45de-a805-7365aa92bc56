<template>
  <div class="drug-information-wrapper">
    <!--    <n-data-table-->
    <!--        :columns="columns"-->
    <!--        :data="tableDataRef"-->
    <!--        :row-key="row => row.id"-->
    <!--        default-expand-all-->
    <!--        :style="{ height: `${400}px` }"-->
    <!--        flex-height-->
    <!--        :single-line="false"-->
    <!--        size="small"-->
    <!--        @update:checked-row-keys="handleCheck"-->
    <!--    >-->
    <!--    </n-data-table>-->
    <!-- 第一项药品信息 -->
    <div class="dashed-item" v-for="(row,index) in tableDataRef" :key="row.id">
      <n-form-item style="margin-left: 10px" :show-feedback="index !== tableDataRef.length - 1">
        <n-grid x-gap="12" :y-gap="12" :cols="1">
          <n-gi>
            <div style="display: flex; width: 100%; justify-content: space-between">
              <n-text style="flex: 1">{{ getFrontName(row) }}</n-text>
              <n-flex style="width: 170px" justify="space-between">
                <n-flex>
                  <n-button text type="default" @click="subDrugNum(row)">
                    <n-image width="22" :src="SubBtn" preview-disabled/>
                  </n-button>
                  <n-input-number
                      style="width: 60px"
                      min={1}
                      :disabled="props.mode == 'detail'"
                      v-model:value="row.count"
                      :show-button="false"
                  />
                  <n-button text type="default" @click="()=>row.count+=1">
                    <n-image width="22" :src="AddBtn" preview-disabled/>
                  </n-button>
                </n-flex>
                <n-button text type="default" @click="handleDeleteItem(row)">
                  <p style="color:rgba(153, 153, 153, 1)">删除</p>
                </n-button>
              </n-flex>
            </div>
          </n-gi>
          <n-gi>
            <n-flex>
              <div style="width:250px">
                <template v-if="row.routeOfAdministration == 0">
                  <n-input-group>
                    <n-select
                        style="width:33%"
                        v-model:value="row.routeOfAdministration"
                        :options="routeOfAdministrationOptions"
                        :disabled="props.mode == 'detail'"
                        placeholder="请选择给药方式"
                    />
                    <n-input
                        v-model:value="row.routeOfAdministrationOther"
                        style='width:67%'
                        :maxlength="10"
                        show-count
                        :disabled="props.mode == 'detail'"
                        placeholder="请输入其他给药方式"
                    />
                  </n-input-group>
                </template>
                <template v-else>
                  <n-select
                      v-model:value="row.routeOfAdministration"
                      :options="routeOfAdministrationOptions"
                      :disabled="props.mode == 'detail'"
                      placeholder="请选择给药方式"
                  />
                </template>
              </div>
              <div style="width:250px">
                <template v-if="row.frequencyOfAdministration == 0">
                  <n-input-group>
                    <n-select
                        style='width:33%'
                        v-model:value="row.frequencyOfAdministration"
                        :options="frequencyOfAdministrationOptions"
                        :disabled="props.mode == 'detail'"
                        placeholder="请选择用药频次"
                    />
                    <n-input
                        v-model:value="row.frequencyOfAdministrationOther"
                        :disabled="props.mode== 'detail'"
                        :maxlength="10"
                        show-count
                        style="width:67%"
                        placeholder="请输入其他用药频次"
                    />
                  </n-input-group>
                </template>
                <template v-else>
                  <n-select
                      v-model:value="row.frequencyOfAdministration"
                      :options="frequencyOfAdministrationOptions"
                      :disabled="props.mode == 'detail'"
                      placeholder="请选择用药频次"
                  />
                </template>
              </div>
              <div style="width:250px">
                <n-input-group>
                  <n-button type="default" ghost>每次</n-button>
                  <n-input
                      placeholder="请输入剂量"
                      show-button={false}
                      :disabled="props.mode == 'detail'"
                      v-model:value="row.dosage"
                  />
                  <n-select
                      style="width: 150px"
                      placeholder="单位"
                      :disabled="props.mode== 'detail'"
                      v-model:value="row.dosageUnits"
                      :options="dosageUnitsOptions"/>
                </n-input-group>
              </div>
            </n-flex>
          </n-gi>
        </n-grid>
      </n-form-item>
    </div>
  </div>
</template>

<script setup lang="tsx">
import {ref, watch} from "vue";
import type {DataTableRowKey} from "naive-ui";
import {useBoolean, useMessages} from '@/hooks';
import {
  dosageUnitsMap,
  frequencyOfAdministrationMap,
  routeOfAdministrationMap
} from "@/views/DoctorEndModule/Prescription/types";
import AddBtn from "@/assets/image/opt/addBtn.png"
import SubBtn from "@/assets/image/opt/subBtn.png"

/** 相关组件 */

export interface DrugInformation {
  id: string,
  productId: string, // 商品ID
  specId: string, // 规格ID
  routeOfAdministration: number, // 给药方式
  routeOfAdministrationOther: string // 自定义给药方式
  frequencyOfAdministration: number // 用药频次
  frequencyOfAdministrationOther: string // 自定义用药频次
  dosage: number, // 剂量
  dosageUnits: number, // 剂量单位 1=粒；2=片
  count: number // 购买数量
}

type DrugInformationTable = {
  value: Array<DrugInformation>,
  mode: 'add' | 'edit' | 'detail';
}

const {createMessageError} = useMessages();

defineOptions({
  name: 'DrugInformationTable'
})

/** Props */
const props = withDefaults(defineProps<DrugInformationTable>(), {
  value: () => [],
  mode: 'add'
});

/** emits */
const emits = defineEmits<{
  (e: 'update:value', value: Array<any>): void;
  (e: 'productSpecVOList', value: Array<any>): void;
}>();

/** 付款方式展示 */
const show = ref(false);
/** 售价 */
const price = ref(null);
/** 当前选中行 */
const checkedRowKeysRef = ref<DataTableRowKey[]>([]);

const {bool: showPopover, setFalse, setTrue} = useBoolean();
const currentFocusId = ref(null);

/* 表单规则 */
const rules = {
      clinicalDiagnosis: {
        required: true,
        trigger: ['blur', 'change'],
        validator(rule, value: Array<string>) {
          if (!value || value.length == 0) {
            return new Error('请输入临床诊断')
          }
          if (value.length > 5) {
            return new Error('临床诊断数已超出上限')
          }
          return true
        },
      },
    }
;

/** 表单参数 */
const model = ref<{
  id: string;
  isDownPayment: 0 | 1; // 是否支持定金支付。0=否；1=是
  isCashOnDelivery: 0 | 1; // 是否支持物流代收。0=否；1=是
  downPayment: number; // 定金单价，单位分
}>({
  id: null,
  isDownPayment: 0,
  isCashOnDelivery: 0,
  downPayment: null,
});

/** 表格数据 */
const tableDataRef = ref<Array<Partial<DrugInformation>>>([]);

// 通用转换器（支持TS类型推断）
const mapToOptions = <T extends string | number>(map: Map<number, T>) =>
    Array.from(map, ([value, label]) => ({label, value}));

// 给药方式
const routeOfAdministrationOptions = mapToOptions(routeOfAdministrationMap);

// 用药频次选项
const frequencyOfAdministrationOptions = mapToOptions(frequencyOfAdministrationMap);

// 剂量选项
const dosageUnitsOptions = mapToOptions(dosageUnitsMap);


/** 选中行回调 */
function handleCheck(rowKeys: DataTableRowKey[]) {
  checkedRowKeysRef.value = rowKeys;
}

/** 表单项 */
let columns = [
  // {
  //   type: "selection",
  //   key: "selection",
  //   fixed: "left",
  //   width: 50,
  //   minWidth: 50,
  //   align: "center",
  // },
  {
    title: '药品名',
    key: 'frontName',
    resizable: true,
    render: (row) => {
      return <span>{(row?.name && row?.productSpecDTOList.length > 0) ?
          `${row?.fontName || ''}${row.name} ${row.productSpecDTOList[0].name}`
          : row.productJoinName}</span>
    }
  },
  {
    title: '给药方式',
    key: 'routeOfAdministration',
    resizable: true,
    render: (row) => {
      if (row.routeOfAdministration == 0) {
        return <n-input-group>
          <n-select style={'width:33%'}
                    v-model:value={row.routeOfAdministration}
                    options={routeOfAdministrationOptions}
                    disabled={props.mode == 'detail'}
          />
          <n-input
              v-model:value={row.routeOfAdministrationOther}
              style={'width:67%'}
              maxlength={10}
              show-count
              disabled={props.mode == 'detail'}/>
        </n-input-group>
      }
      return <n-select
          v-model:value={row.routeOfAdministration}
          options={routeOfAdministrationOptions}
          disabled={props.mode == 'detail'}
      />
    }
  },
  {
    title: '用药频次',
    key: 'frequencyOfAdministration',
    resizable: true,
    render: (row) => {
      if (row.frequencyOfAdministration == 0) {
        return <n-input-group>
          <n-select
              style={'width:33%'}
              disabled={props.mode == 'detail'}
              v-model:value={row.frequencyOfAdministration}
              options={frequencyOfAdministrationOptions}
          />
          <n-input
              v-model:value={row.frequencyOfAdministrationOther}
              disabled={props.mode == 'detail'}
              maxlength={10}
              show-count
              style={'width:67%'}/>
        </n-input-group>
      }
      return <n-select
          disabled={props.mode == 'detail'}
          v-model:value={row.frequencyOfAdministration}
          options={frequencyOfAdministrationOptions}/>
    }
  },
  {
    title: '剂量',
    key: 'dosage',
    resizable: true,
    render: (row) => {
      return <>
        <n-input-group>
          <n-button type="primary">每次</n-button>
          <n-input
              placeholder="请输入剂量"
              show-button={false}
              disabled={props.mode == 'detail'}
              v-model:value={row.dosage}
              style={'width:67%'}/>
          <n-select
              placeholder="单位"
              disabled={props.mode == 'detail'}
              style={'width:33%'} v-model:value={row.dosageUnits}
              options={dosageUnitsOptions}/>
        </n-input-group>
      </>
    }
  },
  {
    title: '购买数量',
    key: 'count',
    width: 120,
    render: (row) => {
      return <>
        <n-input-number
            min={1}
            disabled={props.mode == 'detail'}
            v-model:value={row.count} button-placement="both"/>
      </>
    }
  },
  {
    title: '操作',
    key: 'operation',
    width: 120,
    fixed: "right",
    render: (row, index) => {
      return (
          <n-button text type="primary" onClick={() => handleDeleteItem(row)}>删除</n-button>
      )
    },
  }
];

/* 减少药品数量 */
const subDrugNum = (row) => {
  if (row.count <= 1) {
    return
  }
  row.count -= 1
}

// 删除
function handleDeleteItem(row) {
  tableDataRef.value = tableDataRef.value.filter(item => item.id != row.id)
  emits('update:value', tableDataRef.value);
}

const getFrontName = (row: any) => {
  let frontName = ''
  if (row.frontName) {
    frontName = `[${row.frontName}]`
  }
  return (row?.name && row?.productSpecDTOList.length > 0) ?
      `${frontName}${row.name} ${row.productSpecDTOList[0].name}`
      : row.productJoinName
}


/** 监听 */
watch(() => props.value, (newVal) => {
  tableDataRef.value = newVal;
  console.log('tableDataRef====>', tableDataRef.value)
  if (props.mode == 'detail') {
    columns = columns.filter(item => item.title != '操作')
  }
}, {immediate: true});
</script>

<style lang="less" scoped>
.drug-information-wrapper {
  position: relative;
  width: 100%;
}

:deep(.n-checkbox .n-checkbox-box) {
  width: 18px;
  height: 18px;
}

/* 定义带点和虚线的项样式 */
.dashed-item {
  position: relative;
  padding-left: 16px; /* 给虚线和点留出空间 */
}

.dashed-item::before {
  content: '';
  position: absolute;
  left: 4px;
  top: 11px;
  transform: translateY(-50%);
  width: 10px; /* 内层圆大小 */
  height: 10px;
  background-color: #409eff; /* 内层颜色为白色 */
  border-radius: 50%;
  box-shadow: 0 0 0 6px #f1f7ff; /* 外层圆环，颜色为蓝色，宽度 3px */
}

.dashed-item::after {
  content: '';
  position: absolute;
  left: 9px;
  top: 20px; /* 调整虚线起始位置 */
  bottom: 0;
  width: 1px;
  border-left: 1px dashed #c0c4cc; /* 虚线样式和颜色，可按需修改 */
}

.dashed-item:last-of-type::after {
  content: none;
}
</style>
