<template>
  <JModal
    v-model:show="show"
    width="580"
    :title="title"
    @after-leave="closeModal"
    @positive-click="_save"
    positiveText="确定"
    :positiveButtonProps="{
      loading: isLoading,
    }"
  >
    <n-form
      ref="formRef"
      :rules="rules"
      :model="model"
      label-width="120"
      label-placement="left"
      require-mark-placement="right-hanging"
    >
      <n-grid :cols="8" :x-gap="24">
        <template v-if="props.type === 'EditRole'">
          <n-form-item-gi :span="8" label="新的角色" path="targetRoleType" required>
            <n-radio-group v-model:value="model.targetRoleType" name="radiogroup">
              <n-space>
                <n-radio :value="1">店长</n-radio>
                <n-radio :value="2">店员</n-radio>
                <n-radio :value="3">普通会员</n-radio>
              </n-space>
            </n-radio-group>
          </n-form-item-gi>
          <n-form-item-gi v-if="model.targetRoleType === 3" :span="8" label="名下会员转移给" path="targetMemberId" required>
            <JShopUserSelect
              v-model:value="model.targetMemberId"
              placeholder="请输入店员/店长昵称或ID查询"
            ></JShopUserSelect>
          </n-form-item-gi>
          <!-- 提示 -->
          <n-gi v-if="model.targetRoleType === 3" :span="8" style="margin-left: 120px; margin-bottom: 12px">
            <span style="color: #999">同时绑定该店员</span>
          </n-gi>
        </template>
        <template v-else-if="props.type === 'EditShop'">
          <n-form-item-gi :span="8" label="新的门店" path="targetStoreId" required>
            <JShopSelect v-model:value="model.targetStoreId" placeholder="请输入门店名称或ID查询"></JShopSelect>
          </n-form-item-gi>
          <!-- 提示 -->
          <n-gi :span="8" style="margin-left: 120px; margin-bottom: 12px">
            <span style="color: #999">提示：此操作会把店员和店员下的所有会员都迁移到新店铺</span>
          </n-gi>
        </template>
        <template v-else>
          <n-form-item-gi :span="8" label="转给店员/店长" path="targetMemberId" required>
            <JShopUserSelect
              v-model:value="model.targetMemberId"
              placeholder="请输入店员/店长昵称或ID查询"
            ></JShopUserSelect>
          </n-form-item-gi>
        </template>
      </n-grid>
    </n-form>
  </JModal>
</template>

<script setup lang="ts" name="BulkWelfareVouchers">
import { h, ref, watch } from "vue";
import { useMessages } from "@/hooks";
import { changeMembership, changeAffiliatedStore, changeShopRole } from "@/services/api";
import JShopUserSelect from "@/components/JSelect/JShopUserSelect.vue";
import JShopSelect from "@/components/JSelect/JShopSelect.vue";
const initParams = {
  targetStoreId: null,
  targetRoleType: 1,
  sourceMemberId: null,
  targetMemberId: null,
};
const model = ref({ ...initParams });
const props = ref<any>({});
const title = ref("");
/* 提示信息 */
const { createMessageSuccess, createMessageError } = useMessages();
/* 模态框显隐状态 */
const show = ref(false);
/* 表单规则 */
const rules = {
  targetMemberId: {
    trigger: ["blur", "change"],
    message: "请选择店员/店长",
    validator: () => {
      return model.value.targetMemberId != null;
    },
  },
  targetStoreId: {
    trigger: ["blur", "change"],
    message: "请选择门店",
    validator: () => {
      return model.value.targetStoreId != null;
    },
  },
};
const acceptParams = params => {
  show.value = true;
  props.value = params;
  switch (params.type) {
    case "EditRole":
      title.value = `修改角色【店员/店长昵称：海阔天空】`;
      model.value.targetRoleType = params.data.roleType;
      break;
    case "EditShop":
      title.value = `修改归属门店【店员/店长昵称：海阔天空】`;
      break;
    case "ChangeMembership":
      title.value = `一键转会员【店员/店长昵称：海阔天空】`;
      break;
  }
};

/* 表单实例 */
const formRef = ref(null);
/* 表单参数初始化 */
/* 清空表单 */
const formDataReset = () => {
  model.value = { ...initParams };
};
/* 关闭弹窗 */
const closeModal = () => {
  formDataReset();

  isLoading.value = false;
  // 弹窗取消
  show.value = false;
};
/* 确认--保存 */
const isLoading = ref(false);
const _save = (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors && !isLoading.value) {
      try {
        isLoading.value = true;
        switch (props.value.type) {
          case "EditRole":
            await changeShopRole({
              data: {
                sourceMemberId: props.value.data.id,
                targetRoleType: model.value.targetRoleType,
                targetMemberId: model.value.targetRoleType === 3 ? model.value.targetMemberId : null,
              },
            });
            break;
          case "EditShop":
            await changeAffiliatedStore({
              data: {
                sourceMemberId: props.value.data.id,
                targetStoreId: model.value.targetStoreId,
              },
            });
            break;
          case "ChangeMembership":
            await changeMembership({
              data: {
                sourceMemberId: props.value.data.id,
                targetMemberId: model.value.targetMemberId,
              },
            });
            break;
        }
        createMessageSuccess(`操作成功`);
        // 刷新表格数据
        props.value.refresh();
        closeModal();
      } catch (e) {
        createMessageError(e);
        isLoading.value = false;
      }
    }
  });
};

defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less"></style>
