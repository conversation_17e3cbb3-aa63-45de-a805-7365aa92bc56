import { defHttp } from "@/services";
import type { GoodsType } from "@/enums";

/** 商品管理 */
export const enum GoodsApi {
    page = "/product/manage/page",
    soldOutProduct = "/product/manage/page/soldOutProduct", // 查询已售盘商品接口
    // 药品
    addDrugGoods = '/product/manage/add/pharmaceuticals',
    editDrugGoods = '/product/manage/edit/pharmaceuticals',
    updateDrugGoods = '/product/manage/update/pharmaceuticals',
    copyDrugGoods = "/product/manage/copyProduct/pharmaceuticals",
    // 疗法
    addTherapyDrug = "/product/manage/add/therapyDrug",
    editTherapyDrugGoods="/product/manage/edit/therapyDrug",
    updateTherapyDrugGoods="/product/manage/update/therapyDrug",
    copyTherapyDrugGoods = "/product/manage/copyProduct/therapyDrug",
    // 普通商品
    addBaseItem = "/product/manage/add/baseItem",
    editBaseItem = "/product/manage/edit/baseProductItem",
    updateBaseItem = "/product/manage/update/baseItem",
    copyBaseItem = "/product/manage/copyBaseProduct",

    delete = '/product/manage/delete', // 未使用
    checkProductCode = '/product/manage/checkProductCode',

    batch = "/product/manage/batch/publish",

    saveConfig ="/pointConfig/saveConfig", // 保存积分配置
    /** 新增商品规格id */
    addProductSpec = "/product/manage/get/id",
    /** 根据商品id查属性 */
    getProductAttribute = "/productAttributeValue/get/productId",
}

/**
 * @description 新建、编辑 -- 保存积分领取配置
 */
export function addPointConfig(_params) {
    return defHttp.post({
        url: GoodsApi.saveConfig,
        params: {
            data: _params
        },
    });
}

export interface GoodsPageRes {
    records: ApiStoreModule.Goods[],
    total: string;
    size: string;
    current: string;
}
/** 商品分页 */
export function getGoodsPage(params: {
    data: {
        name?: string,
        cateId?: string
        isPres?: 0 | 1;
        isPublish?: 0 | 1;
        type?: GoodsType;
        code?: string;
    },
    pageVO: {
        current: number,
        size: number
    }
}) {
    return defHttp.post<GoodsPageRes>({
        url: GoodsApi.page,
        params,
    });
}

/** 商品(已售盘)分页 */
export function getSoldOutProductPage(params) {
    return defHttp.post<GoodsPageRes>({
        url: GoodsApi.soldOutProduct,
        params,
    });
}

/** 新增药品商品  */
export function addDrugGoods(_params) {
    return defHttp.post({
        url: GoodsApi.addDrugGoods,
        params: {
            data: _params
        },
    });
}

/** 根据商品id查询药品商品信息 */
export function getDrugGoodsById(id: string) {
    return defHttp.post<ApiStoreModule.DrugGoodsInfo>({
        url: GoodsApi.editDrugGoods + `?id=${id}`,
    });
}

/** 更新药品商品信息 */
export function updateDrugGoods(_params) {
    return defHttp.put({
        url: GoodsApi.updateDrugGoods,
        params: {
            data: _params
        },
    });
}

/** 复制药品商品 */
export function copyDrugGoodsById(id: string) {
    return defHttp.post({
        url: GoodsApi.copyDrugGoods + `?id=${id}`,
    });
}

/** 新增疗法商品  */
export function addTherapyDrug(_params) {
    return defHttp.post({
        url: GoodsApi.addTherapyDrug,
        params: {
            data: _params
        },
    });
}

/** 根据商品id查询疗法商品信息 */
export function getTherapyDrugGoodsById(id: string) {
    return defHttp.post<ApiStoreModule.TherapyDrugGoodsInfo>({
        url: GoodsApi.editTherapyDrugGoods + `?id=${id}`,
    });
}

/** 更新疗法商品信息 */
export function updateTherapyDrugGoods(_params) {
    return defHttp.put({
        url: GoodsApi.updateTherapyDrugGoods,
        params: {
            data: _params
        },
    });
}

/** 复制疗法商品 */
export function copyTherapyDrugGoodsById(id: string) {
    return defHttp.post({
        url: GoodsApi.copyTherapyDrugGoods + `?id=${id}`,
    });
}

/** 新增普通商品  */
export function addGeneralGoods(_params) {
    return defHttp.post({
        url: GoodsApi.addBaseItem,
        params: {
            data: _params
        },
    });
}

/** 根据商品id查询药品商品信息 */
export function getGeneralGoodsById(id: string) {
    return defHttp.post({
        url: GoodsApi.editBaseItem + `?id=${id}`,
    });
}

/** 更新药品商品信息 */
export function updateGeneralGoods(_params) {
    return defHttp.put({
        url: GoodsApi.updateBaseItem,
        params: {
            data: _params
        },
    });
}

/** 复制药品商品 */
export function copyGeneralGoodsById(id: string) {
    return defHttp.post({
        url: GoodsApi.copyBaseItem + `?id=${id}`,
    });
}

/** 检查编号是否唯一 */
export function checkProductCode(_params: { id?: string; code: string | number }) {
    return defHttp.post({
        url: GoodsApi.checkProductCode,
        params: {
            data: _params
        },
    });
}

/** 批量上下架 */
export function batchUnmountGoods(_params:{ productIds: string, isPublish: '0' | '1', cateIds: string }) {
  return defHttp.put({
    url: GoodsApi.batch + `?productIds=${_params.productIds}&isPublish=${_params.isPublish}&cateIds=${_params.cateIds}`,
  })
};

/** 新增规格id */
export function addProductSpecId() {
    return defHttp.get({
        url: GoodsApi.addProductSpec,
    });
}

/** 根据商品id查属性 */
export function getProductAttribute(id: string) {
    return defHttp.get({
        url: GoodsApi.getProductAttribute + `?productId=${id}`,
    });
}

/** 供应商商品管理 */
export const enum SupplierProductApi {
    page = "/product/supplier/page", // 供应商商品分页
    add = "/product/supplier/add", // 新增供应商商品
    detail = "/product/supplier/get", // 商品详情
    update = "/product/supplier/update", // 更新供应商商品
    apply = "/product/supplier/doSubmitAnApplication", // 提交申请
    getAuditRecord = "/productAuditOperation/list",// 获取商品审核记录
}

/** 供应商商品分页 */
export function getSupplierProductPage(_params) {
    return defHttp.post({
        url: SupplierProductApi.page,
        params: _params
    });
}

/** 供应商商品新增 */
export function addSupplierProduct(_params) {
    return defHttp.post({
        url: SupplierProductApi.add,
        params: {
            data: _params
        },
    });
}

/** 供应商商品详情 */
export function getSupplierProductDetail(id: string) {
    return defHttp.post({
        url: SupplierProductApi.detail + `?id=${id}`,
    });
}

/** 供应商商品更新 */
export function updateSupplierProduct(_params) {
    return defHttp.put({
        url: SupplierProductApi.update,
        params: {
            data: _params
        },
    });
}

/** 供应商商品提交申请 */
export function applySupplierProduct(supplierProductId) {
    return defHttp.get({
        url: `${SupplierProductApi.apply}?supplierProductId=${supplierProductId}`,
    });
}

/** 供应商商品审核记录 */
export function getAuditRecord(_params) {
    return defHttp.post({
        url: SupplierProductApi.getAuditRecord, // 供应商商品审核记录
        params: {
            data:_params
        }
    });
}
