/**
 * @description 排序类型
 * 1=总订单数排序 
 * 2=已支付单数排序 
 * 3=未支付单数排序 
 * 4=已支付总金额 
 * 5=线上实收款
 */
export const enum SortType {
    TOTALORDER = 1,
    PAIDORDER = 2,
    UNPAIDORDER = 3,
    TOTALAMOUNT = 4,
    ONLINEPAYMENTAMOUNT = 5,
}

/** 统计排序 */
export const SortTypeValue: Record<string, number> = {
    'totalOrder': SortType.TOTALORDER,
    'paidOrder': SortType.PAIDORDER,
    'unpaidOrder': SortType.UNPAIDORDER,
    'totalAmount': SortType.TOTALAMOUNT,
    'onlinePaymentAmount': SortType.ONLINEPAYMENTAMOUNT,
  };

/**
 * @description 报表类型
 * 1=订单统计
 * 2=会员消费榜统计
 * 3=群管统计
 * 4=经销商统计
 * 5=商品销售榜
 * 6=课程统计
 */

export const enum ReportType {
    ORDERREPORT = 1,
    CUSTOMERREPORT = 2,
    GROUPMGRREPORT = 3,
    DEALERREPORT = 4,
    GOODSREPORT = 5,
    COURSEREPORT = 6,
}

/**
 * @description 商品统计商品搜索类型
 */
export const enum GoodsSearchType {
    NAME = 1,
    ID = 2,
}

/**
 * @description 商品销售排序类型
 */
export const enum ProductSaleSortTypeEnum {
    /** 总订单数排序 */
    TotalOrder = 1,
    /** 已支付单数排序 */
    PaidOrder = 2,
    /** 未支付单数排序 */
    UnpaidOrder = 3,
    /** 已支付总金额 */
    PaidAmount = 4,
    /** 线上实收款 */
    OnlinePayment = 5,
    /** 实际支付订单数 */
    ActualPaidOrder = 6,
    /** 实际消费金额 */
    ActualConsumption = 7,
    /** 销量 */
    SalesVolume = 8,
    /** 销售额 */
    SalesAmount = 9
}

/**
 * @description 商品统计排序 
 */
export const ProductSaleSortValue: Record<string, number> = {
    "saleNum": ProductSaleSortTypeEnum.TotalOrder,
    "saleAmount": ProductSaleSortTypeEnum.SalesAmount,
    "paidOrder": ProductSaleSortTypeEnum.PaidOrder,
    "actualConsumeAmount": ProductSaleSortTypeEnum.ActualConsumption,
}