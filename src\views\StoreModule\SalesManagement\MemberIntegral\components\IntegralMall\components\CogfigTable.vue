<template>
    <n-data-table 
        :columns="columns" 
        :data="tableDataRef" 
        :row-key="row => row.id" 
        :style="{ minHeight: `${140}px` }" 
        default-expand-all
        flex-height
        :single-line="false"
        size="small" 
    >
    </n-data-table>
</template>

<script setup lang="tsx" name="CogfigTable">
import { ref } from "vue";
import { uuid } from "@/utils";
import type { PointCofigType } from "../../../types";
import { PointCofigEnum } from "../../../types";

/** emits */
const emits = defineEmits<{
    (e: 'clickCofig', type: PointCofigType): void;
}>();

/** 表格数据 */
const tableDataRef = ref([
    {
        id: uuid(),
        type: PointCofigEnum.DAILYVISIT,
        name: '每日任务-每日来访',
        content: '用户登录小程序，完成该任务获得配置积分，只能获得一次。',
    },
    {
        id: uuid(),
        type: PointCofigEnum.VIEWPRODUCT,
        name: '每日任务-查看商品',
        content: '用户前台点击去完成，随机跳转后台配置好的商品中，查看该商品到配置时间，完成该任务获得配置积分，只能获得一次。',
    },
    {
        id: uuid(),
        type: PointCofigEnum.SIGNIN,
        name: '签到',
        content: '点取签到，即可获取奖励配置中积分',
    },
]);

/** 表单项 */
const columns = [
    {
        title: '名称',
        key: 'name',
        width: 140,
        resizable: true,
    },
    {
        title: '内容',
        key: 'content',
        resizable: true,
    },
    {
        title: '操作',
        key: 'operation',
        width: 160,
        fixed: "right",
        render: (row, index) => {
            return (
                <n-button text type="primary" onClick={() => { emits('clickCofig', row?.type) }}>配置</n-button>
            )
        },
    }
];
</script>


<style lang="less" scoped></style>