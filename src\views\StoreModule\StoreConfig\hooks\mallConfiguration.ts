import { reactive, ref } from "vue";
import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
import { SystemStoreType } from "@/enums";
import { systemConfigOptionGetOptions } from "@/services/api";
import { useMessages } from "@/hooks";
const message = useMessages();
const isLoading = ref(false)
export default function mallConfiguration() {
    const systemStore = useSystemStoreWithoutSetup();
    const positionList= ref([
        {
            value:0,
            label:'小程序首页首屏',
        },
        {
            value:1,
            label:'积分商城首页',
        },
        {
            value:2,
            label:'门店商城首页',
        }
    ])
    const selectList = reactive({
        sto_points_statistical_method:[
            {
              value: 1,
              label: "全渠道统计",
            },
            {
                value:2,
                label: "分渠道统计",
            }
        ],
        sto_pay_mode:[
            {
                value: 0,
                label: "无",
            },
            {
                value: 1,
                label: "微信支付",
            },
            {
                value:2,
                label: "富友支付",
            }
        ],
        sto_fy_pay_mode:[
            {
                value: 1,
                label: "在本小程序支付",
            },
            {
                value:2,
                label: "跳转富友小程序支付",
            }
        ],
        sto_marketplace_type:[
            {
                value: 1,
                label: "普通商城",
            },
            {
                value:2,
                label: "医药商城",
            }
        ],
        sto_applet_state:[
            {
                value: 0,
                label: "开发版",
            },
            {
                value: 1,
                label: "体验版",
            },
            {
                value:2,
                label: "正式版",
            }
        ],
        sto_merchant_info:[]
    })
        
    
 

    const pointPageList = systemStore._globalConfig['marketplaceType'] === SystemStoreType.PHARMACEUTICALMALL ?[
        {
            value:0,
            label:'商城首页',
        },
        {
            value:1,
            label:'积分首页',
        },
        {
            value:2,
            label:'购物车',
        },
        {
            value:3,
            label:'个人中心',
        },
        {
            value:4,
            label:'药品分类列表页',
        },
        {
            value:5,
            label:'疗法分类列表页',
        },
        {
            value:6,
            label:'普通商品分类列表页',
        },
        {
            value:7,
            label:'视频',
        },
        {
            value:9,
            label:'问诊首页',
        },
        {
            value:10,
            label:'会话消息列表页',
        },
        {
            value:11,
            label:'直播',
        }

    ]:[
      {
          value:0,
          label:'商城首页',
      },
        {
            value:1,
            label:'积分首页',
        },
        {
            value:2,
            label:'购物车',
        },
        {
            value:3,
            label:'个人中心',
        },
        {
            value:6,
            label:'普通商品分类列表页',
        },
        {
            value:7,
            label:'视频',
        },
        {
            value:11,
            label:'直播',
        },
      ]

    const messageList = {
        'sto_free_shipping_amount' : '请填写邮费，最低0元，最高10000元.',
        'sto_receiving_days' : '请填写用户确认收货天数，最短1天，最长30天.',
        'sto_shipping_fee' : '请填写邮费，最低0元，最高10000元.',
        'sto_cancellation_time' : '请填写系统取消订单时长，最小5分钟，最长30天.',
        'sto_refund_apply_deadline' : '请填写退款申请时限(天)，最短1天，最长30天.',
        'sto_returns_deadline' : '请填写退货时限(天)，最短1天，最长30天.',
        'sto_handling_fee_': '请输入大于等于0小于100的数值，支持两位小数.',
        'sto_point_validity_period' : '请填写积分有效时限(月)，最短0月，最大12月.',
        'sto_points_expiration_tips' : '请填写积分到期提示最大30天，最小2天.',
        'sto_user_agreement' : '请选择PDF文件上传.',
        'sto_privacy_policy' : '请选择PDF文件上传.',
        'sto_informed_consent' : '请选择PDF文件上传.',
        'sto_allocation_account_action_time' : '请填写小于或等于360天、大于或等于1天的天数',
    }
    const radioList = {
        'sto_display_content': [
            {
                value:'true',
                label:"是"
            },
            {
                value:'false',
                label:"否"
            }
        ],
        'sto_allocation_account_rule':[
            {
                value:'1',
                label:"不分账"
            },
            {
                value:'2',
                label:"按订单分账[订单分账金额 = 订单中每个商品分账总额，每个商品分账金额 = （商品总额 - 总成本价）* 商品分账比例]"
            }
        ]
    }
    function validatorTest(key,val) {
        // 正整数
        let numReg = /^\d+$/
        // 两位小数
        let smallNumReg = /^[0-9]+(.[0-9]{1,2})?$/
        switch (key) {
            case 'sto_free_shipping_amount':
                return numReg.test(val) && val <= 1000000
            case 'sto_receiving_days':
                return numReg.test(val) && val <= 30 && val >= 1
            case 'sto_shipping_fee':
                return numReg.test(val) && val <= 1000000
            case 'sto_cancellation_time':
                return numReg.test(val) && val <= 43200 && val >= 5
            case 'sto_refund_apply_deadline':
                return numReg.test(val) && val <= 30 && val >= 1
            case 'sto_returns_deadline':
                return numReg.test(val) && val <= 30 && val >= 1
            case 'sto_handling_fee_':
                return smallNumReg.test(val) && val <= 100 && val >= 0
            case 'sto_point_validity_period':
                return numReg.test(val) && val <= 12 && val >= 0
            case 'sto_points_expiration_tips':
                return numReg.test(val) && val <= 30 && val >= 2
            case 'sto_allocation_account_action_time':
                return numReg.test(val) && val <= 360 && val >= 1
            default:
                return numReg.test(val)
        }
    }
    function validatorStrTest(key,val) {
        // 正整数
        let numReg = /^\d+$/
        // 两位小数
        let smallNumReg = /^[0-9]+(.[0-9]{1,2})?$/
        switch (key) {
            case 'sto_handling_fee_':
                return smallNumReg.test(val) && val <= 100 && val >= 0
        }
    }
    const  systemConfigOptionGetOptionsApi = async(configId) =>{
        isLoading.value = true
        try{
            const res = await systemConfigOptionGetOptions(configId)
            if(res && res.lenght != 0){
                selectList.sto_merchant_info = res.map(item => ({
                    value: item.value,
                    label: item.value,
                    desc: item.desc,
                    disabled:item.isActivated ? false : true
                }));
            }
        }catch(err){
            message.createMessageError('获取查询参数选项失败' + err)
        }finally{
            isLoading.value = false
        }
    }


    return {
        selectList,
        pointPageList,
        radioList,
        messageList,
        isLoading,
        validatorTest,
        validatorStrTest,
        systemConfigOptionGetOptionsApi,
        positionList
    }
}