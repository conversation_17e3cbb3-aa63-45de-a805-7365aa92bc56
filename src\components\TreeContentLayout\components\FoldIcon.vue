<template>
  <div 
    class="fold-icon-wrapper" 
    :class="{ 'collapse': props.collapsed }"
    :style="{ left: !props.collapsed ? `${props.treeWidth - 21}px` : 0}"
  >
    <span class="universe-icon fold-icon">
      <SvgIcon :icon="ChevronBack" />
    </span>
  </div>
</template>

<script lang="ts" setup>
import { ChevronBackOutline as ChevronBack } from "@vicons/ionicons5";
/** 相关组件 */
import SvgIcon from "@/components/SvgIcon/index.vue";

defineOptions({
    name: 'FoldIcon'
});

/** props */
const props = defineProps<{
    treeWidth: number; // 左侧宽度
    collapsed: boolean; // 是否展开
}>();
</script>

<style lang="less" scoped>
@import "@/styles/default.less";
.fold-icon-wrapper {
    position: absolute;
    top: 22px;
    width: 19px;
    height: 32px;
    line-height: 28px;
    z-index: 999;
    text-align: center;
    border: 1px solid @default-border-color;
    border-radius: 4px;
    background-color: #fff;
    border-right: none;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    cursor: pointer;
    .universe-icon {
        font-size: 12px;
        color: #646a73;
    }
}

.collapse {
    transform: rotate(180deg);
}
</style>
