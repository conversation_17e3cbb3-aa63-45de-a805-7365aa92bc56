import { defineStore } from "pinia";
import { StoreName } from "@/enums/stores";
import { stores } from "@/stores";
import { createCacheStorage } from "@/utils/cache/storageCache";
import { CacheConfig } from "@/utils/cache/config";

export const useUserStore = defineStore(StoreName.User, {
  state: () => {
    return {
      _userInfo: null,
      _token: null,
      _routeConfig: null,
      _optAuthList: null,
      _imConfig: null,
    };
  },
  getters: {
    optAuthList: state => {
      if (!state._optAuthList) {
        try {
          const optListStorage = createCacheStorage(CacheConfig.OptList);
          const _optListCache = optListStorage.get();
          state._optAuthList = _optListCache;
        } catch (e) {
          console.error(e);
        }
      }
      return state._optAuthList;
    },
    userInfo: state => {
      if (!state._userInfo) {
        try {
          const userConfigStorage = createCacheStorage(CacheConfig.UserInfo);
          const _userInfoCache = userConfigStorage.get();
          state._userInfo = _userInfoCache;
        } catch (e) {
          console.error(e);
        }
      }
      return state._userInfo;
    },
    imConfig:state=> {
      const ImConfigStorage = createCacheStorage(CacheConfig.ImConfig);
      const _imConfigCache = ImConfigStorage.get();
      state._imConfig = _imConfigCache;
      return state._imConfig
    },
    token: state => {
      if (!state._token) {
        try {
          const authStorage = createCacheStorage(CacheConfig.Token);
          const _tokenCache = authStorage.get();
          state._token = _tokenCache;
        } catch (e) {
          console.error(e);
        }
      }
      return state._token;
    },
    routeConfig: state => {
      if (!state._routeConfig) {
        try {
          const routeConfigStorage = createCacheStorage(CacheConfig.RouteConfig);
          const _routeConfigCache = routeConfigStorage.get();
          state._routeConfig = _routeConfigCache;
        } catch (e) {
          console.error(e);
        }
      }
      return state._routeConfig;
    },
  },
  actions: {
    setOptAuthList(optAuthList: Array<string>) {
      const optListStorage = createCacheStorage(CacheConfig.OptList);
      optListStorage.set(optAuthList);
      this._optAuthList = optAuthList;
    },
    setUserInfo(userInfo: object) {
      const userConfigStorage = createCacheStorage(CacheConfig.UserInfo);
      userConfigStorage.set(userInfo);
      this._userInfo = userInfo;
    },
    setToken(token: string) {
      this._token = token;
      const authStorage = createCacheStorage(CacheConfig.Token);
      authStorage.set(token);
    },
    setRouteConfig(routeConfig: unknown[]) {
      this._routeConfig = routeConfig;
      const routeConfigStorage = createCacheStorage(CacheConfig.RouteConfig);
      routeConfigStorage.set(routeConfig);
    },
    setImConfig(imConfig: object) {
      const ImConfigStorage = createCacheStorage(CacheConfig.ImConfig);
      ImConfigStorage.set(imConfig);
      this._imConfig = imConfig;
    },
  },
});

export function useUserStoreWithoutSetup() {
  return useUserStore(stores);
}
