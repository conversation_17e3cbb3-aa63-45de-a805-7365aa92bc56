<template>
    <FormLayout :isLoading="isLoading" :tableData="tableData" :tableColumns="tableColumns" :pagination="paginationRef"
        @paginationChange="paginationChange" :isTableSelection="true" @selectedKeysChange="selectedKeysChange"
        id="member">
        <template #searchForm>
            <n-form ref="formRef" :model="formValue" :show-feedback="false" label-placement="left" label-width="auto"
                require-mark-placement="right-hanging" size="small" :style="{ width: '100%' }">

                <n-form-item label="用户ID">
                    <n-input v-model:value="formValue.customerId" type="text" placeholder="请输入用户ID"
                        @keyup.enter.native="formSearch" style="width:170px" clearable :allow-input="onlyAllowNumber" :maxlength="19" />
                </n-form-item>
                <n-form-item label="用户昵称">
                    <n-input v-model:value="formValue.nickName" type="text" placeholder="请输入用户昵称"
                        @keyup.enter.native="formSearch" style="width:170px" clearable />
                </n-form-item>
                <n-form-item label="视频ID">
                    <n-input v-model:value="formValue.videoId" type="text" placeholder="请输入视频ID"
                        @keyup.enter.native="formSearch" style="width:170px" clearable :allow-input="onlyAllowNumber" :maxlength="19" />
                </n-form-item>
            </n-form>
        </template>
        <template #tableHeaderBtn>
            <n-button @click="refresh" class="store-button" :loading="isLoading" >刷 新</n-button>
            <JAddButton @click="addVideo" v-if="hasManagementPublish" type="primary">发布视频</JAddButton>
        </template>
        <template #tableFooterBtn="scope">
            <!-- 批量上架 -->
            <n-popconfirm @positive-click="handlePublishType(scope.selectedListIds, scope.selectedList, '1')"
                :positive-button-props="{
                    loading: isBatchLoading
                }">
                <template #trigger>
                    <n-button ghost type="primary" size="small">批量发布</n-button>
                </template>
                此操作将发布所有选中的视频，是否继续？
            </n-popconfirm>
            <!-- 批量下架 -->
            <n-popconfirm @positive-click="handlePublishType(scope.selectedListIds, scope.selectedList, '2')"
                :positive-button-props="{
                    loading: isBatchLoading
                }">
                <template #trigger>
                    <n-button ghost type="error" size="small">批量取消发布</n-button>
                </template>
                此操作将取消发布所有选中的视频，是否继续？
            </n-popconfirm>
        </template>
    </FormLayout>
    <drawerAddVideo v-model:show="showAddDrawer" @closeVideo="closeDrawer(closeDrawerEnum.confirm)" ></drawerAddVideo>
    <drawerVideolDetail v-model:show="showVidelDetail" :video-id="videoId" @closeVideo="closeDrawer" ></drawerVideolDetail>
</template>
<script setup lang="tsx">
import { ref, reactive, watch, h, type VNode } from 'vue';
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { videoPage, deleteVideo, updateType } from '@/services/api';
import { useMessages } from "@/hooks";
import { VideoTypeEnum } from '@/enums';
import moment from "moment";
import { NTooltip, type SelectOption } from 'naive-ui';
import { hasManagementDelete, hasManagementDetails , hasManagementPublish , hasManagementStatus } from "../authList";
import drawerAddVideo from "./drawerAddVideo.vue";
import drawerVideolDetail from "./drawerVideoDetail.vue";
import JImage from "@/components/JImage/index.vue";
import TablePreview from "@/components/TablePreview/index.vue";
import { getOssFileUrlPrefix } from "@/utils/http/urlUtils";
import { transformMinioSrc } from "@/utils/fileUtils"

import { _debounce } from "@/utils";
import { videoTypeLabel } from "@/constants";
import { closeDrawerEnum } from "../type"



const renderOption = ({ node, option }: { node: VNode; option: SelectOption }) =>
    h(NTooltip, null, {
        trigger: () => node,
        default: () => option.label
    })
interface prescriptionProps {
    tabNameRef: VideoTypeEnum //tab标签值
}

const props = withDefaults(defineProps<prescriptionProps>(), {
    tabNameRef: VideoTypeEnum.AuditResolve
});

const tabValue = ref(null)
const { createMessageSuccess, createMessageError } = useMessages();
/* 初始化参数 */
const initParams = {
    videoId: '',     // 视频ID
    customerId: '',      // 用户ID
    nickName: '',    // 用户昵称
};
const formValue = reactive({ ...initParams });

/* 表格方法Hook */
const {
    isLoading,
    tableData,
    paginationRef,
    paginationChange,
    pageTableData,
} = useTableDefault({
    pageDataRequest: videoPage,
});

//刷新
const refresh = () => {
    getTableData()
}
const showAddDrawer = ref<boolean>(false)
/** 新增视频 */
const addVideo = () => {
    showAddDrawer.value = true
}



const closeDrawer = (type:closeDrawerEnum ,cancelId:string='' )=>{
    if (type == closeDrawerEnum.cancel) {
        showVidelDetail.value = false
    }else{
        showAddDrawer.value = false;
        createMessageSuccess('发布成功');
        refresh();
    }
}

const showVidelDetail = ref<boolean>(false)
const isBatchLoading = ref<boolean>(false)
/** 批量发布或取消发布  */
const handlePublishType = (selectedListIds:string[], selectedList, type) => {
    changeVideoType(selectedListIds.join(),type)
}
/** 搜索 */
const formSearch = () => {
    getTableData()
}

function getTableData() {
    const _params = {
        ...formValue
    }

    pageTableData(_params, paginationRef.value, true);
}

const onlyAllowNumber = (value: string) => !value || /^\d+$/.test(value);



/* 表格项 */
const tableColumns = reactive([
{
      title: "头像",
      key: "customerImg",
      align: "left",
      fixed: "left",
      width: 100,
      render: rowData => {
          return <JImage  imgPath={rowData.customerImg} />;
      },
    },
    {
        title: "用户昵称",
        key: "nickName",
        align: "left",
        width: 100,
        render: (row) => {
            return <table-tooltip row={row} nameKey="customerId" title={row['nickName']} idKey="customerId"/>;
        }
    },
    {
    title: "视频文件",
    key: "img",
    align: "left",
    width: 170,
    render: row => {
        return <n-space align="center" justify="center">
                    <TablePreview src={transformMinioSrc(row.path)} type='video' ></TablePreview>
                    <table-tooltip row={row} nameKey="" idKey="id"  />
                </n-space>
            
    },
  },
    {
        title: "作品描述",
        key: "description",
        align: "left",
        width: 100,
    },
    {
        title: "评论数",
        key: "commentsCount",
        align: "left",
        width: 100,
    },
    {
        title: "点赞数",
        key: "likesCount",
        align: "left",
        width: 100,
    },
    {
        title: "收藏数",
        key: "favoritesCount",
        align: "left",
        width: 100,
    },
    {
        title: "转发数",
        key: "sharesCount",
        align: "left",
        width: 100,
    },
    {
        title: "视频创建时间",
        key: "createTime",
        align: "left",
        width: 100
    },
    {
    title: "发布状态",
    key: "isPublish",
    width: 100,
    align: "left",
    render: row => {
      return (
        <n-tag bordered={false} size="small" type={row.state === 1 ? "success" : "error"}>
          {videoTypeLabel[row.state]}
        </n-tag>
      );
    },
  },
    {
        title: "审核人",
        key: "account",
        align: "left",
        width: 100,
    },
    {
        title: "审核时间",
        key: "auditTime",
        align: "left",
        width: 100,
    },
    {
        title: "归属群管",
        key: "lastThirdGroupMgrName",
        align: "left",
        width: 100,
    },
    {
        title: "归属经销商",
        key: "lastThirdDealerName",
        align: "left",
        width: 100,
    },
    
    {
        title: "操作",
        key: "action",
        width: 150,
        align: "left",
        fixed: "right",
        render: (row) => {
            return (
                <n-space align="center" justify="center">

                    {
                        hasManagementDetails ? <n-button text size="small" type="primary" class="mt-4 mr-4 edit-icon" onClick={() => clickDetails(row, tabValue.value)}>查看详情
                        </n-button> : null
                    }
                    {hasManagementStatus ? <n-button text size="small" type={row['state'] == 1 ? 'error' : 'primary'} class="mt-4 mr-4 edit-icon" onClick={() => clickVideoType(row)}>{row['state'] == 1 ? '取消发布' : '发布'}
                    </n-button> : null}

                    {hasManagementDelete ? <n-popconfirm
                        onPositiveClick={() => { clickDelete(row.id) }}
                    >
                        {{
                            trigger: () => (
                                <n-button text size="small" type="error" >删除</n-button>
                            ),
                            default: () => <span style={{ width: '300px' }}>是否确定删除该数据？</span>
                        }}
                    </n-popconfirm> : null}

                </n-space>
            );
        },
    },
])



//勾选
const codeListData = ref([])
const selectedKeysChange = (value, row) => {
    const codeList = []
    row.forEach(item => {
        codeList.push(item.code)
    })
    codeListData.value = codeList
}

/** 发布 或 取消发布 */
const clickVideoType = (row) => {
    isLoading.value = true;
    const state = row.state == 1 ? 2 : 1;
    changeVideoType(row.id,state);
}
const changeVideoType = (videoIds:string,state:1|2=1)=>{
    videoId.value = '';
    updateType(videoIds,state).then(res=>{
        createMessageSuccess(state != 1 ? '取消发布成功':'发布成功')
        refresh()
    }).catch(err=>{
        createMessageError(`${state != 1 ? '取消发布失败':'发布失败'}:${err}`)
    }).finally(()=>{
        isLoading.value = false;
    })
}


/** 删除 */
const clickDelete = (code) => {
    isLoading.value = true;
    deleteVideo(code).then(res=>{
        createMessageSuccess('删除成功')
        refresh()
    }).catch(err=>{
        createMessageError('删除失败:' + err)
    }).finally(()=>{
        isLoading.value = false;
    })
}

const videoId = ref<string>('')
/** 详情 */
const clickDetails = (row, tabValue) => {
    videoId.value = row.id;
    showVidelDetail.value = true
}
const debounceGetTableData = _debounce(getTableData,500)

// watch([
//     () => formValue.customerId,
//     () => formValue.nickName,
//     () => formValue.videoId
// ], () => {
//     debounceGetTableData();
// });

watch(
    () => props.tabNameRef,
    (newVal) => {
        getTableData()
        tabValue.value = newVal
    },
    {
        immediate: true
    }
)

watch(()=>showVidelDetail.value,(newV)=>{
    if (!newV) {
        refresh();
        videoId.value = '';
    }
})
</script>
<style lang="less" scoped>
@import "@/styles/default.less";
</style>