<template>
  <n-drawer
    :show="show"
    width="100%"
    height="100%"
    :auto-focus="false"
    @after-leave="handleAfterLeave"
    @after-enter="handleAfterEnter"
    @close="show = false"
    @mask-click="show = false"
    @esc="show = false"
    :to="props.to"
  >
    <n-drawer-content
      :body-content-style="{ padding: '0' }"
      :footer-style="{
          padding: '12px',
          boxShadow: 'rgba(17, 17, 26, 0.1) 0px 4px 16px, rgba(17, 17, 26, 0.05) 0px 8px 32px'
        }"
    >
      <div class="wrapper">
        <!-- 标题 -->
        <DrawerTitle :title="title" @goBack="show = false" />
        <div class="publics-form">
          <n-scrollbar>
            <n-spin :show="isGetLoading" size="small">
              <n-form
                ref="formRef"
                :model="model"
                :rules="rules"
                label-placement="left"
                label-width="auto"
                require-mark-placement="right-hanging"
                :style="{ width: '100%' }"
                size="small"
              >
                <n-grid :cols="12" :x-gap="12">
                  <!-- 基本信息 -->
                  <n-gi :span="10">
                    <div class="title-wrapper">
                      <div class="title-line"></div>
                      <span>基本信息</span>
                    </div>
                  </n-gi>
                  <!-- 管理名称 -->
                  <n-form-item-gi :span="10" label="管理名称" path="name">
                    <n-input v-model:value="model.name" placeholder="请输入管理名称" :maxlength="60" clearable />
                  </n-form-item-gi>
                  <!-- 前端名称 -->
                  <n-form-item-gi :span="10" label="前端名称" path="frontName">
                    <div style="width: 100%;">
                      <n-input v-model:value="model.frontName" placeholder="请输入前端名称" :maxlength="60" clearable />
                      <span style="font-size: 14px;">注：前端商城小程序显示使用</span>
                    </div>
                  </n-form-item-gi>
                  <!-- 商品编码 -->
                  <n-form-item-gi :span="10" label="商品编码" :path="props.isT9Sync ? 'sku' : ''">
                    <n-input 
                      v-model:value="model.sku" 
                      placeholder="用于商家内部管理所使用的自定义编码" 
                      :maxlength="60" 
                      clearable
                     />
                  </n-form-item-gi>
                  <!-- 所属分类 -->
                  <n-form-item-gi :span="10" label="所属分类" path="cateId">
                    <JProductTreeSelector
                      style="width: 100%;"
                      v-model:value="model.cateId"
                      :type="GoodsCategoryType.THERAPY"
                      isImmediately
                    />
                  </n-form-item-gi>
                  <!-- 商品图 -->
                  <n-gi :span="12">
                    <n-grid :cols="24" :x-gap="12">
                      <n-form-item-gi :span="2" label="商品图"></n-form-item-gi>
                      <n-form-item-gi
                        :span="6"
                        label="首图"
                        :path="model.productFirstImg.length || model.productMoreImg.length ? '' : 'productFirstImg'"
                      >
                        <UploadProductImg v-model:value="model.productFirstImg" accept="image/*" :fileListSize="1" />
                      </n-form-item-gi>
                      <n-form-item-gi
                        :span="16"
                        label="更多图片"
                        :path="model.productFirstImg.length || model.productMoreImg.length ? '' : 'productMoreImg'"
                      >
                        <UploadProductImg
                          v-model:value="model.productMoreImg"
                          accept="image/*"
                          :fileListSize="8"
                          is-multiple
                        />
                      </n-form-item-gi>
                    </n-grid>
                  </n-gi>
                  <!-- 商品视频 *******版本新增 -->
                  <n-form-item-gi :span="10" label="商品视频">
                    <n-flex vertical :size="4">
                      <UploadProductVideo v-model:value="model.productVideoPath" />
                      <span style="font-size: 14px;">注：只支持mp4格式，视频时长不超过60秒，视频大小不超过200M，视频将默认展示在商品轮播图之前</span>
                    </n-flex>
                  </n-form-item-gi>
                  <!-- 产品标签 -->
                  <n-form-item-gi :span="6" label="产品标签">
                    <div class="productLabel-wrapper">
                      <div v-for="(item,index) in model.productLabelList" :key="index" class="product-label">
                        <n-input
                          v-model:value="item.label"
                          type="text"
                          width="100%"
                          size="small"
                          placeholder="请输入产品标签"
                          clearable
                          style="margin-right: 12px;"
                        />
                        <JTextButton
                          v-if="(index === model.productLabelList.length - 1) && model.productLabelList.length < 3"
                          size="small"
                          text
                          type="primary"
                          @click="addItem"
                        >
                          新 增
                        </JTextButton>
                        <JTextButton
                          v-if="model.productLabelList.length !== 1"
                          size="small"
                          text
                          type="error"
                          @click="removeItem(index)"
                        >
                          删 除
                        </JTextButton>
                      </div>
                    </div>
                  </n-form-item-gi>
                  <!-- 每人购买上限 1.0.8版本增加 -->
                  <n-form-item-gi :span="10" label="每人购买上限" path="upperLimit">
                    <n-input-number 
                      v-model:value="model.upperLimit" 
                      placeholder="请输入购买上限" 
                      :min="0" 
                      :max="99999" 
                      :precision="0"
                      :show-button="false"
                    />
                    <span style="margin-left: 12px;font-size: 14px;">注：0表示不设限</span>
                  </n-form-item-gi>
                  <!-- 售价与库存 -->
                  <n-gi :span="8">
                    <div class="title-wrapper">
                      <div class="title-line"></div>
                      <span>售价与库存</span>
                      <span style="color: #FF4D4F;">*</span>
                    </div>
                  </n-gi>
                  <n-form-item-gi :span="12" :show-label="false" path="productSpecVOList">
                    <TherapySellingPriceInventory 
                      v-model:value="model.productSpecVOList"
                      style="width: 98%;"
                    />
                  </n-form-item-gi>
                  <!-- 疗法详情 -->
                  <n-gi :span="8">
                    <div class="title-wrapper">
                      <div class="title-line"></div>
                      <span>疗法详情</span>
                    </div>
                  </n-gi>
                  <!-- 产品清单 -->
                  <n-form-item-gi
                    :span="12"
                    label="产品清单"
                    label-placement="top"
                    label-style="font-size: 16px;font-weight: 700;"
                    path="therapyDrugItemRelateVOList"
                  >
                    <TherapyProductList 
                      v-model:value="model.therapyDrugItemRelateVOList" 
                      :type="pattern"
                      style="width: 98%;"
                     />
                  </n-form-item-gi>
                  <!-- 产品包备注 -->
                  <n-form-item-gi
                    :span="10"
                    label="产品包备注"
                    label-placement="top"
                    label-style="font-size: 16px;font-weight: 700;"
                  >
                    <n-input
                      v-model:value="model.therapyDrugDetailVO.remark"
                      placeholder="请输入产品包备注"
                      :maxlength="100"
                      clearable
                    />
                  </n-form-item-gi>
                  <!-- 功能主治 -->
                  <n-form-item-gi
                    :span="10"
                    label="功能主治"
                    label-placement="top"
                    label-style="font-size: 16px;font-weight: 700;"
                  >
                    <n-input
                      v-model:value="model.therapyDrugDetailVO.uses"
                      type="textarea"
                      maxlength="500"
                      show-count
                      placeholder="请输入功能主治"
                      clearable
                    />
                  </n-form-item-gi>
                  <!-- 服用、使用方式 -->
                  <n-form-item-gi
                    :span="10"
                    label="服用/使用方式"
                    label-placement="top"
                    label-style="font-size: 16px;font-weight: 700;"
                  >
                    <n-input
                      v-model:value="model.therapyDrugDetailVO.dosage"
                      type="textarea"
                      maxlength="500"
                      show-count
                      placeholder="请输入服用/使用方式"
                      clearable
                    />
                  </n-form-item-gi>
                  <!-- 适宜人群 -->
                  <n-form-item-gi
                    :span="10"
                    label="适宜人群"
                    label-placement="top"
                    label-style="font-size: 16px;font-weight: 700;"
                  >
                    <n-input
                      v-model:value="model.therapyDrugDetailVO.appropriateCrowd"
                      type="textarea"
                      maxlength="100"
                      show-count
                      placeholder="请输入适宜人群"
                      clearable
                    />
                  </n-form-item-gi>
                  <!-- 禁忌人群 -->
                  <n-form-item-gi
                    :span="10"
                    label="禁忌人群"
                    label-placement="top"
                    label-style="font-size: 16px;font-weight: 700;"
                  >
                    <n-input
                      v-model:value="model.therapyDrugDetailVO.contraindications"
                      type="textarea"
                      maxlength="100"
                      show-count
                      placeholder="请输入禁忌人群"
                      clearable
                    />
                  </n-form-item-gi>
                  <!-- 方解、搭配优势 -->
                  <n-form-item-gi
                    :span="10"
                    label="方解/搭配优势"
                    label-placement="top"
                    label-style="font-size: 16px;font-weight: 700;"
                  >
                    <n-input
                      v-model:value="model.therapyDrugDetailVO.combination"
                      type="textarea"
                      maxlength="500"
                      show-count
                      placeholder="请输入方解/搭配优势"
                      clearable
                    />
                  </n-form-item-gi>
                  <!-- 图文描述 -->
                  <n-form-item-gi
                    :span="12"
                    label="图文描述"
                    label-placement="top"
                    label-style="font-size: 16px;font-weight: 700;"
                  >
                    <div style="height:520px; width: 98%; margin-bottom: 32px;">
                      <div id="desc-richtext-container" ref="descRichTextDomRef" style="height:100%;width:100%;"></div>
                    </div>
                  </n-form-item-gi>
                  <!-- 更多设置 -->
                  <n-gi :span="12">
                    <div class="title-wrapper">
                      <div class="title-line"></div>
                      <span>更多设置</span>
                    </div>
                  </n-gi>
                  <!-- 积分设置 -->
                  <n-form-item-gi :span="12">
                    <JCheckbox v-model:checked="model.isEnabled" style="margin-left: 24px;" size="medium">
                      <span style="font-size: 16px; display: flex; align-items: center;">
                        订单完成后可获得积分，用户获得
                        <n-input-number
                          v-model:value="model.points"
                          :min="MINPOINT"
                          :max="MAXPOINT"
                          :precision="0"
                          @click.stop=""
                          :show-button="false"
                          placeholder="请输入积分"
                          style="width: 120px;"
                          size="small"
                        />
                        积分（只能录入大于0小于10万的数值，不支持小数，录入后勾选用户购买该商品则获得积分）
                      </span>
                    </JCheckbox>
                  </n-form-item-gi>
                  <!-- 经销商分账 -->
                  <n-form-item-gi :span="12">
                    <JCheckbox
                      v-model:checked="model.isAllocation"
                      @update:checked="handleCheckboxChange"
                      style="margin-left:24px;"
                      size="medium"
                    >
                      <n-form-item path="dealerAllocationRatio" style="height: 32px;" >
                      <span style="font-size: 16px; display: flex; align-items: center;">
                          给社群端经销商分账，分账比例
                          <n-input-number
                            v-model:value="model.dealerAllocationRatio"
                            :min="0"
                            :max="80"
                            @click.stop=""
                            :precision="0"
                            :show-button="false"
                            placeholder="可输入0-80"
                            style="width: 120px;"
                            :disabled="!model.isAllocation"
                            size="small"
                          />
                          %
                      </span>
                      </n-form-item>
                    </JCheckbox>
                  </n-form-item-gi>
                  <!-- 备注 -->
                  <n-form-item-gi :span="12">
                    <div style="font-size: 12px;margin-left: 30px;">
                      <p>备注:</p>
                      <p>开展分账业务需在“财务管理”》“分账规则设置”界面设置分账规则</p>
                    </div>
                  </n-form-item-gi>
                  <!-- 分销员分佣 -->
                  <n-form-item-gi :span="12">
                    <JCheckbox
                      v-model:checked="model.isDistribution"
                      style="margin-left:24px;"
                      size="medium"
                    >
                      <n-form-item path="distributionRatio" style="height: 32px;" >
                      <span style="font-size: 16px; display: flex; align-items: center;">
                          给分销员分佣，佣金比例
                          <n-input-number
                            v-model:value="model.distributionRatio"
                            :min="0"
                            :max="80"
                            @click.stop=""
                            :precision="0"
                            :show-button="false"
                            placeholder="可输入0-80"
                            style="width: 120px;"
                            :disabled="!model.isDistribution"
                            size="small"
                          />
                          %
                        </span>
                      </n-form-item>
                    </JCheckbox>
                  </n-form-item-gi>
                  <!-- 备注 -->
                  <n-form-item-gi :span="12">
                    <div style="font-size: 12px;margin-left: 30px;">
                      <p>备注:</p>
                      <p>1、开展分销业务需在【分销】》【分销设置】界面开启分销功能</p>
                      <p>2、按商品销售额计算佣金，佣金金额 = 商品销售总额 * 佣金比例</p>
                      <p>3、如果社群端经销商也参与分账，分账分佣总额占订单总额的比例不能大于80%</p>
                    </div>
                  </n-form-item-gi>
                  <!-- 提示 -->
                  <n-form-item-gi v-if="!props.isPointEnable" :span="12">
                    <JPointsTip />
                  </n-form-item-gi>
                </n-grid>
              </n-form>
            </n-spin>
          </n-scrollbar>
        </div>
      </div>
      <template #footer v-if="pattern !== 'view'">
        <div class="footer-wrapper">
          <!-- 商品是否上架 -->
          <JCheckbox v-model:checked="model.isPublish" style="margin-left: 24px;">
            <span style="font-size: 16px;">上架</span>
          </JCheckbox>
          <n-space>
            <n-button @click="show = false" class="store-button">取 消</n-button>
            <n-button type="primary" :loading="isLoading" @click="_save" class="store-button">保 存</n-button>
          </n-space>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>

<script lang="ts" setup name="NewTherapyGoods">
import { ref, computed, nextTick, watch } from "vue";
import { createDummyId, deepClone, isArray } from "@/utils";
import type { FormRules, FormItemRule } from "naive-ui";
import { isObject, _debounce, isNullOrUnDef } from "@/utils";
import { addTherapyDrug, getTherapyDrugGoodsById, updateTherapyDrugGoods, uploadRichTextResouce } from "@/services/api";
import { useMessages } from '@/hooks';
import Quill from 'quill';
import "@/assets/quill/quill.snow.css";
// import quillDropdownIcon from 'quill/assets/icons/dropdown.svg';
import DrawerTitle from "@/components/DrawerTitle/index.vue";
import UploadProductImg from "@/components/UploadProductImg/index.vue";
import { getOssUrlPrefix } from "@/utils/http/urlUtils";
import { createVideoElement } from "@/utils/quill/createVideoElement";
import {type GoodsType, GoodsCategoryType, ProductSalesSceneEnum } from "@/enums";
import { THERAPYUPPERDEFAULTVALUE } from "../types";
import { MAXPOINT, MINPOINT } from "@/views/StoreModule/SalesManagement/MemberIntegral/types";
/** 相关组件 */
import TherapySellingPriceInventory from "./TherapySellingPriceInventory.vue";
import TherapyProductList from "./TherapyProductList.vue";
import JPointsTip from "./JPointsTip.vue";

const { createMessageSuccess, createMessageError } = useMessages();
const descRichTextDomRef = ref(null);

/** props */
const props = withDefaults(defineProps<{
  to?: string; // 弹窗位置
  isPointEnable?: boolean; // 是否启用积分功能
  isT9Sync?: boolean; // 是否同步到T9
  refreshTable?: () => void; // 刷新表格数据
}>(), {
  to: '.table-wrapper',
  isPointEnable: false
});

/** 显隐 */
const show = ref(false);

/* 表单实例 */
const formRef = ref();

/* 表单参数初始化 */
const initParams = {
  id: null, // 编辑时，商品id
  type: GoodsCategoryType.THERAPY, // 商品类别 2 = 疗法
  isDistribution:false,//分销员选框值
  distributionRatio:null,//佣金比例
  name: null, // 管理名称
  frontName: null, // 前端名称
  sku: null, // 编号
  cateId: null, // 所属分类
  upperLimit: 0, // 每订单上限(1.0.8版本)
  sellingScenario: ProductSalesSceneEnum.Store, // 销售场景(*******版本)

  productFirstImg: [], // 首图
  productMoreImg: [], // 更多图片
  productLabelList: [
    {
      label: null
    }
  ], // 产品标签(最多能添加3个)

  productSpecVOList: [{
    id: createDummyId(), // 标识
    price: null, // 售价（元）
    costPrice: null, // 成本价(元)
    dailyCost: null, // 日均费用（元）
    cycle: null, // 用药周期（天）
    availStocks: null, // 可用库存
    lockedStocks: 0, // 冻结库存
    initSaled: 0, // 初始已售（前端展示）
    upper: THERAPYUPPERDEFAULTVALUE, // 每次购买数量上限
    soldQty: 0, // 已售数量，已售数量=实际已售数量+初始已售数量；默认为实际已售数量
    isDeleted: 0, // 是否删除 0: 删除 1: 不删除
    isDownPayment: 0 as 0 | 1, // 是否支持定金支付。0=否；1=是
    isCashOnDelivery: 0 as 0 | 1, // 是否支持物流代收。0=否；1=是
    downPayment: null, // 定金单价，单位分
    sku: null, // 商品编码
  }], // 售价与库存(商品规格)

  therapyDrugItemRelateVOList: [], // 产品清单

  therapyDrugItemRelateDTOSList: [], // 疗法绑定产品清单（编辑时，进行对比）

  // 疗法详情
  therapyDrugDetailVO: {
    remark: null, // 产品包备注
    uses: null, // 功能主治
    dosage: null, // 服用、使用方式
    appropriateCrowd: null, // 适宜人群
    contraindications: null, // 禁忌人群
    combination: null, // 方解、搭配优势
  },

  isPublish: false, // 商品是否上架
  desc: null, // 图文描述

  // 疗法更多设置
  pointConfigId: null, // 编辑时,积分配置Id
  sourceId: null, // // 编辑时,来源关联ID
  isEnabled: false, // 是否获取积分
  points: null, // 积分
  isAllocation: false, // 是否分账
  dealerAllocationRatio: null, // 分账比例
  pointIsDeleted: 0, // 是否删除配置：0=否、1=是

  productVideoPath: null, // *******版本 商品视频
};
const model = ref(deepClone(initParams));

/** 富文本 */
let quill: Quill = null;

/* 接收父组件传过来的参数 */
const pattern = ref<'add' | 'edit' | 'view'>('add');
const acceptParams = (params: {
  cateId?: string | null; // 商品分类Id
  row: Partial<ApiStoreModule.Goods>;
  type: 'add' | 'edit' | 'view';
  productTypes: GoodsType;
}) => {
  pattern.value = params.type;
  // 当前新增商品分类id
  if (params.productTypes === GoodsCategoryType.THERAPY) {
    model.value.cateId = params.cateId ?? null;
  }
  if (isObject(params.row) && Object.keys(params.row).length !== 0) {
    if (params.type === 'edit' || params.type === 'view') {
      model.value.id = params.row?.id;
    }
  }
  show.value = true;
};

/** 标题 */
const title = computed(() => {
  const titleMap: Record<'add' | 'edit' | 'view', string> = {
    add: '新建疗法商品',
    edit: '编辑疗法商品',
    view: '疗法商品详情',
  };
  return titleMap[pattern.value];
});

/** 添加产品标签选项 */
const addItem = () => {
  if (model.value.productLabelList.length < 3) {
    model.value.productLabelList.push({ label: null });
  }
};

/** 移除产品标签选项 */
const removeItem = (index: number) => {
  model.value.productLabelList.splice(index, 1);
};

/* 表单规则 */
const rules: FormRules = {
  sku: {
    type: "string",
    required: true,
    trigger: ["blur", "input"],
    message: "请输入商品编码",
  },
  upperLimit: {
    type: "number",
    required: true,
    trigger: ["blur", "input"],
    message: "请输入每人购买上限",
  },
  name: {
    type: "string",
    required: true,
    trigger: ["blur", "input"],
    message: "请输入管理名称",
  },
  frontName: {
    type: "string",
    required: true,
    trigger: ["blur", "input"],
    message: "请输入商品名",
  },
  cateId: {
    type: "string",
    required: true,
    trigger: ["blur", "input"],
    message: "请选择所属分类",
  },
  productFirstImg: {
    type: "array",
    required: true,
    trigger: ["blur", "input"],
    message: "请选择商品图片",
  },
  productMoreImg: {
    type: "array",
    required: true,
    trigger: ["blur", "input"],
    message: "请选择商品图片",
  },
  productSpecVOList: {
      type: "array",
      required: true,
      validator: (rule: FormItemRule, value: Array<{
        id: string,
        name: string,
        price: number,
        dailyCost: number,
        cycle: number,
        availStocks: number,
        initSaled: number,
        costPrice:number,
      }>) => {
        if (value?.length > 0) {
          let check = value?.every(product => {
              return (
                (product.costPrice !== null || !model.value.isAllocation) &&
                product.price !== null &&
                product.dailyCost !== null &&
                product.cycle !== null &&
                product.availStocks !== null &&
                product.initSaled !== null
              );
          });
          return check;
        }
      },
      trigger: ['blur', 'change'],
      message: "请输入售价（元）、日均费用（元）、用药周期（天）、库存、初始已售",
    },
    therapyDrugItemRelateVOList: {
      type: "array",
      required: true,
      message: "请添加产品",
    },
    points: {
      type: "number",
      required: true,
      trigger: ["blur", "change"],
      message: "请输入正确的积分",
      validator:(rule,value)=>{
        if (!model.value.isEnabled) return true
        if ( value < 1 || value > 100000 ) {
          return false
        }
      }
    },
    dealerAllocationRatio: {
      type: "number",
      required: true,
      trigger: ["blur", "change"],
      message: "请输入正确的分账比例",
      validator:(rule,value)=>{
        if (!model.value.isAllocation) return true
        if ( value < 1 || value > 1000000 ) {
          return false
        }
      }
    },
    distributionRatio: {
      type: "number",
      required: true,
      trigger: ["blur", "change"],
      message: "请输入正确的分销比例",
      validator:(rule,value)=>{
        if (!model.value.isDistribution) return true
        if ( value == null || value > 80 ) {
          return false
        }
      }
    },
};
/**  */
const isDistributorChecked  = computed (() =>{
  return model.value.isDistribution && model.value.isAllocation
})
const handleCheckboxChange = (checked) => {
  model.value.isAllocation = checked;
  // 手动触发整个表单的验证
  formRef.value?.validate(['productSpecVOList']);
};
/** 关闭抽屉回调 */
const handleAfterLeave = () => {
  // 初始化参数
  model.value = deepClone(initParams);
};

/** 抽屉出现后的回调(编辑请求) */
const isGetLoading = ref(false);
const handleAfterEnter = async () => {
  try {
    if (show.value && (pattern.value === 'edit' || pattern.value === 'view')) {
      isGetLoading.value = true;
      const resp = await getTherapyDrugGoodsById(model.value.id);
      // 解构
      const { 
        id, 
        name, 
        frontName, 
        cateId, 
        isPublish, 
        upperLimit,
        tag, 
        desc, 
        productImgDTOSList, 
        productSpecDTOSList, 
        therapyDrugDetailDTO, 
        therapyDrugItemDTOSList, 
        therapyDrugItemRelateDTOSList,
        pointConfigDTOSList,
        distributionRatio,
        isDistribution,
        isAllocation,
        dealerAllocationRatio,
      } = resp;
      // 商品积分配置
      const { points, isEnabled, id: pointConfigId, sourceId } = pointConfigDTOSList[0] ?? {
        points: null,
        isEnabled: 0,
        pointConfigId: null,
        sourceId: null
      }; // 疗法单个规格

      // 过滤图片与视频
      const productFirstImg = productImgDTOSList.filter(img => img.isFirst === 1 && img.type === 0);
      const productMoreImg = productImgDTOSList.filter(img => img.isFirst !== 1 && img.type === 0);
      const productVideoPath = productImgDTOSList.filter(img => img.type === 1) || null;

      Object.assign(model.value, {
        id,
        name,
        frontName,
        cateId,
        upperLimit,
        isPublish: isPublish ? true : false,
        // 产品标签
        productLabelList: tag ? tag.split(',').map(item => ({ label: item })) : [{ label: null }],
        // 疗法图片
        productFirstImg,
        productMoreImg,
        isAllocation: isAllocation == 1,
        dealerAllocationRatio,
        distributionRatio,
        isDistribution:isDistribution == 1,
        // 疗法视频
        productVideoPath: productVideoPath.length > 0 ? productVideoPath : null,
        // 疗法售价与库存
        productSpecVOList: productSpecDTOSList?.length > 0 ? productSpecDTOSList.map(({ price, costPrice, dailyCost, ...rest}) => ({...rest, price: price / 100, dailyCost: dailyCost / 100 ,costPrice: (costPrice !==undefined ? (costPrice  / 100 ) : null)})) : [
          {
            id: createDummyId(),
            price: null,
            costPrice: null,
            dailyCost: null,
            cycle: null,
            availStocks: null,
            lockedStocks: 0,
            initSaled: 0,
            upper: THERAPYUPPERDEFAULTVALUE,
            soldQty: 0,
            isDeleted: 0,
            isDownPayment: 0,
            isCashOnDelivery: 0,
            downPayment: null,
          }
        ],
        // 疗法产品清单
        therapyDrugItemRelateVOList: therapyDrugItemDTOSList?.length > 0 ? therapyDrugItemDTOSList: [],
        // 疗法详情
        therapyDrugDetailVO: { ...therapyDrugDetailDTO },
        therapyDrugItemRelateDTOSList: therapyDrugItemRelateDTOSList,
        // 疗法积分配置
        pointConfigId, // 编辑时, 积分配置Id
        sourceId,
        isEnabled: isEnabled ? true : false, // 是否获取积分
        points, // 积分
        sku: productSpecDTOSList?.length > 0 ? productSpecDTOSList[0].sku : null,
      });
      quill.root.innerHTML = desc;
    }
  } catch (error) {

  } finally {
    isGetLoading.value = false;
  }
};

/** 获取新增参数 */
const getAddTherapyGoodsParams = () => {
  const {
    name,
    type,
    cateId,
    sku,
    upperLimit,
    sellingScenario,
    isPublish,
    frontName,
    productLabelList,
    productMoreImg,
    productFirstImg,
    productVideoPath,
    productSpecVOList,
    therapyDrugItemRelateVOList,
    isDistribution,
    therapyDrugDetailVO,
    distributionRatio,
    isAllocation,
    dealerAllocationRatio
  } = model.value;

  // 疗法图片处理
  const imgList = deepClone(productMoreImg).map(item => ({ ...item, type: 0 }));
  if (productFirstImg.length > 0) {
    imgList.unshift({ ...productFirstImg[0], type: 0 });
  }
  // 商品视频
  if (productVideoPath !== null) {
    imgList.push(...productVideoPath);
  }

  // 疗法产品标签处理
  let labels = productLabelList.map(obj => obj.label);
  let tagsString = labels.join(',');

  return {
    name,
    type,
    cateId,
    upperLimit,
    sellingScenario,
    isHidden:0,
    isPres: 1, // 是否处方药 0否 1是 疗法类商品默认就是处方药
    isPublish: isPublish ? 1 : 0,
    frontName,
    desc: quill.root.innerHTML,
    tag: tagsString,
    productImgVOList: imgList,
    productSpecVOList: productSpecVOList.map(({ id, price, dailyCost,costPrice, ...rest }) => ({
      ...rest,
      price: parseFloat((price * 100).toFixed(2)),
      costPrice: costPrice !== null ? parseFloat((costPrice * 100).toFixed(2)) : null,
      dailyCost: dailyCost * 100,
      lockedStocks: 0,
      upper: THERAPYUPPERDEFAULTVALUE,
      soldQty: 0,
      isDeleted: 0,
      sku,
      ..._getPointsAddParams(),
    })),
    isDistribution:isDistribution ? 1 :0,
    isAllocation:isAllocation ? 1 :0,
    distributionRatio: isDistribution ? distributionRatio : null,
    dealerAllocationRatio: isAllocation? dealerAllocationRatio : null,
    // 产品清单
    therapyDrugItemRelateVOList: therapyDrugItemRelateVOList.map(obj => ({ itemId: obj.id })),
    therapyDrugDetailVO,
  }
};

/** 获取编辑参数 */
const getEditTherapyGoodsParams = () => {
  const {
    id, // 商品id
    name,
    type,
    cateId,
    sku,
    upperLimit,
    sellingScenario,
    isPublish,
    frontName,
    productLabelList,
    productMoreImg,
    productFirstImg,
    productVideoPath,
    productSpecVOList,
    therapyDrugItemRelateVOList,
    therapyDrugDetailVO,
    therapyDrugItemRelateDTOSList,
    isDistribution,
    distributionRatio,
    isAllocation,
    dealerAllocationRatio
  } = model.value;
  // 疗法图片
  const imgList = productMoreImg.map(item => ({ ...item, productId: model.value.id, isFirst: 0, type: 0 }));
  if (productFirstImg.length > 0) {
    imgList.unshift({ ...productFirstImg[0], productId: model.value.id, isFirst: 1, type: 0 });
  } else if (imgList.length > 0) {
    // 首图为空，设置更多图片第一张为首图
    imgList[0].isFirst = 1;
  }
  // 商品视频
  if (productVideoPath !== null && isArray(productVideoPath)) {
    const videoList = productVideoPath.map(item => ({ ...item, productId: model.value.id, isFirst: 0, type: 1 }));
    imgList.push(...videoList);
  }

  // 疗法产品标签处理
  let labels = productLabelList.map(obj => obj.label).join(',');

  let therapyDrugItemMap = new Map(therapyDrugItemRelateDTOSList.map(item => [item.itemId, item]));
  // 当前列表的产品清单Id
  let currentTherapyDrugItemId = therapyDrugItemRelateVOList.map(obj => obj.id);
  let therapyDrugItemParams = currentTherapyDrugItemId.map(item => {
    let therapy = therapyDrugItemMap.get(item);
    if (therapy) {
      return {
        id: therapy.id,
        itemId: therapy.itemId,
        productId: therapy.productId,
      };
    } else {
      return {
        itemId: item,
        productId: id,
      };
    }
  });

  return {
    id,
    name,
    type,
    cateId,
    isPres: 1, // 是否处方药 0否 1是 疗法类商品默认就是处方药
    isPublish: isPublish ? 1 : 0,
    frontName,
    desc: quill.root.innerHTML,
    tag: labels,
    upperLimit,
    sellingScenario,
    isHidden:0,
    productImgVOList: imgList,
    isDistribution: isDistribution ? 1 : 0,
    isAllocation: isAllocation ? 1 : 0,
    dealerAllocationRatio: isAllocation ? dealerAllocationRatio : null,
    distributionRatio: isDistribution ? distributionRatio : null,
    productSpecVOList: productSpecVOList.map(({ price, dailyCost,costPrice, ...rest }) => ({
      ...rest,
      price: parseFloat((price * 100).toFixed(2)),
      costPrice: costPrice !== null ? parseFloat((costPrice * 100).toFixed(2)) : null,
      dailyCost: dailyCost * 100,
      lockedStocks: 0,
      upper: THERAPYUPPERDEFAULTVALUE,
      soldQty: 0,
      isDeleted: 0,
      isUpdate: 1,
      sku,
      ..._getPointsEditParams(),
    })),
    // 产品清单
    therapyDrugItemRelateVOList: therapyDrugItemParams,
    therapyDrugDetailVO,
  }
};

/** 获取商城积分配置 */
function _getPointsAddParams() {
  const { points, isEnabled, pointIsDeleted } = model.value;
  return {
    points,
    isEnabled: isEnabled ? 1 : 0,
    pointIsDeleted
  }
}

/** 获取商城编辑积分配置 */
function _getPointsEditParams() {
  const { points, isEnabled, pointConfigId, sourceId } = model.value;
  return {
    // 药品编辑积分配置
    pointConfigId,
    sourceId,
    isEnabled: isEnabled ? 1 : 0,
    points,
    pointIsDeleted: !isEnabled && !points ? 1 : 0,
  }
}

/** 校验函数 */
function handleVerifyParams(_params): boolean {
  const { isEnabled, points } = _params;
  if (isEnabled) {
    if (isNullOrUnDef(points)) {
      createMessageError("请输入用户可获取积分！");
      return false;
    }
  }
  return true;
}

/* 确认--保存 */
const isLoading = ref(false);
const _save = (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      try {
        isLoading.value = true;
        /** 新增 */
        if (pattern.value === 'add') {
          // console.log("疗法新增产品参数", getAddTherapyGoodsParams());
          let checked = handleVerifyParams(_getPointsAddParams());
          if (!checked) {
            return;
          }
          await addTherapyDrug(getAddTherapyGoodsParams());
        }
        /** 编辑 */
        if (pattern.value === 'edit') {
          console.log("疗法编辑产品参数", getEditTherapyGoodsParams());
          let checked = handleVerifyParams(_getPointsEditParams());
          if (!checked) {
            return;
          }
          await updateTherapyDrugGoods(getEditTherapyGoodsParams());
        }
        createMessageSuccess(pattern.value === 'add' ? '新增商品成功' : '编辑商品成功');
        // 关闭并刷新
        show.value = false;
        props?.refreshTable();
      } catch (error) {
        createMessageError(pattern.value === 'add' ? `新增商品失败: ${error}` : `编辑商品失败: ${error}`);
      } finally {
        isLoading.value = false;
      }
    }
  });
};
const prices = ref(0) //全部售价
const costPrices = ref(0) //全部成本加
const total = ref<string | number>(0)//订单总额比率
/** 设置经销商分账和分销员佣金总额 */
const handleTotal = () =>{
  prices.value = model.value.productSpecVOList.reduce((total, item) => total + (item.price || 0), 0);
  costPrices.value = model.value.productSpecVOList.reduce((total, item) => total + (item.costPrice || 0), 0);
  const splitAccountValue =  model.value.dealerAllocationRatio ? (model.value.dealerAllocationRatio / 100) : 0
  const distributionRatioValue =  model.value.distributionRatio ? (model.value.distributionRatio / 100) : 0
  const distributorCommission = isNaN((prices.value - costPrices.value) * splitAccountValue / prices.value) ?  0 : ((prices.value - costPrices.value) * splitAccountValue / prices.value);
  const proportionTotal = (distributorCommission + distributionRatioValue) * 100
  total.value = !isFinite(proportionTotal) ? 0 : (proportionTotal >= 0 ? proportionTotal.toFixed() : 0)
}

/** 监听分账比例 */
watch(() => model.value.dealerAllocationRatio,(newVal) =>{
  handleTotal()
})

/** 佣金比例比例 */
watch(() => model.value.distributionRatio,(newVal) =>{
  handleTotal()
})

/** 监听 */
watch(show, (newVal) => {
  if (newVal) {
    nextTick(() => {
      createVideoElement()
      quill = new Quill(descRichTextDomRef.value, {
        theme: 'snow', // 使用 snow 主题
        modules: {
          toolbar: {
            container: [
              // Include image button in the toolbar
              [{ 'size': ['small', false, 'large', 'huge'] }],  // 自定义字体大小
              ['bold', 'italic', 'underline', 'strike'],        // 加粗、斜体、下划线和删除线
              [{ 'color': [] }, { 'background': [] }],          // 字体颜色和背景颜色
              [{ 'header': '1' }, { 'header': '2' }, 'blockquote',],  // 标题、引用和代码块
              [{ 'list': 'ordered' }, { 'list': 'bullet' }, { 'indent': '-1' }, { 'indent': '+1' }], // 列表和缩进
              [{ 'direction': 'rtl' }, { 'align': [] }],        // 文本方向和对齐方式
              ['image', 'video'],            // 链接、图片、视频和公式
            ],
            // Handle image uploads
            handlers: {
              image: function () {
                const input = document.createElement('input');
                input.setAttribute('type', 'file');
                input.setAttribute('accept', 'image/*');
                input.setAttribute('multiple', 'true');
                input.click();

                input.onchange = () => {
                  const file = input.files[0];
                  if (file) {
                    const _formData = new FormData()
                    for (let key in input.files) {
                      if (input.files.hasOwnProperty(key)) {
                        console.log(key, input.files[key]);
                        _formData.append('files', input.files[key])
                      }
                    }
                    uploadRichTextResouce(_formData).then(res => {
                      res.forEach(e=>{
                        const range = quill.getSelection();
                        quill.insertEmbed(range.index, 'image', `${getOssUrlPrefix()}/${e}`);
                      })
                    }).catch((error) => {
                      console.error('Image upload failed:', error);
                    });
                  }
                };
              },
              video: function () {
                const input = document.createElement('input');
                input.setAttribute('type', 'file');
                input.setAttribute('accept', 'video/mp4');
                input.click();

                input.onchange = () => {
                  const file = input.files[0];
                  if (file) {
                    console.log(file);
                    const _formData = new FormData()
                    _formData.append('files', file)
                    uploadRichTextResouce(_formData).then(res => {
                      const videoUrl = res[0]
                      const range = quill.getSelection();
                      quill.insertEmbed(range.index, 'video', `${getOssUrlPrefix()}/${videoUrl}`);
                    }).catch((error) => {
                      console.error('Video upload failed:', error);
                    });
                  }
                };
              }
            }
          }
        }
      });
    })
  }
})

/** 关闭 */
const close = () => {
  show.value = false;
}

defineExpose({
  acceptParams,
  close
});
</script>

<style lang="less" scoped>
@import "@/styles/defaultVar.less";
:deep(.n-scrollbar-rail) {
  bottom: 8px !important;
}

.wrapper {
  width: 100%;
  height: 100%;
  .publics-form {
    width: 100%;
    height: calc(100% - 52px);
    padding: 12px 6px 12px 24px;
    box-sizing: border-box;
    background-color: #fff;
    .productLabel-wrapper {
      width: 100%;
      display: flex;
      flex-direction: column;
      .product-label {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 12px;
      }
    }
    .title-wrapper {
      height: 30px;
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: 700;
      margin-bottom: 12px;
      .title-line {
        width: 4px;
        height: 60%;
        background-color: @primary-color;
        margin-right: 5px;
      }
      .unpack {
        margin-left: 12px;
      }
      :deep(.n-button__icon) {
        margin-left: 2px;
      }
    }
  }
}
.footer-wrapper {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
