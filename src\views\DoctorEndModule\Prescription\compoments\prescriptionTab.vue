<template>
  <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :pagination="paginationRef"
      @paginationChange="paginationChange"
      :isTableSelection="true"
      class="pharmacist-prescription-tabs-layout"
  >
    <template #searchForm>
      <n-form
          ref="formRef"
          :model="formValue"
          :show-feedback="false"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          size="small"
          :style="{ width: '100%' }"
      >
        <!-- 搜索关键字 -->
        <n-form-item>
          <n-input-group>
            <n-select
                v-model:value="formValue.searchType"
                placeholder="请选择"
                :options="searchTypeRecordOptions"
                style="width: 120px;"
            />
            <JSearchInput
                v-model:value="formValue.searchValue"
                placeholder="请输入内容"
                @search="getTableData"/>
          </n-input-group>
        </n-form-item>
        <n-form-item label="创建时间" label-placement="left">
          <j-date-range-picker
              style="flex: 1;"
              v-model:value="formValue.creationTime"
              type="datetimerange"
              format="yyyy-MM-dd"
              :default-time="['00:00:00', '23:59:59']"
              clearable
          />
        </n-form-item>
      </n-form>
    </template>
    <!-- 操作栏 -->
    <template #tableHeaderBtn="scoped">
      <n-button @click="refresh" class="store-button">刷 新</n-button>
    </template>
  </FormLayout>
  <!--  <DrawerPrescriptionDetail ref="drawerPrescriptionShow"/>-->
  <DetailsDrawer ref="detailDrawerRef"/>
</template>
<script setup lang="tsx">
import {ref, watch, onMounted} from 'vue';
import FormLayout from "@/layout/FormLayout.vue";
import {useTableDefault} from "@/hooks/useTableDefault";
import {useMessages} from "@/hooks";
import {
  PrescriptionSelection,
  PrescriptionType,
  DoctorEndPrescriptionSelection,
  MedicalInquiryPrescriptionSelection
} from '@/enums';
import {deepClone, downloadLocalFile, isNullStringOrNullOrUnDef, transformMinioSrc} from "@/utils";
import {
  DoctorEndModulePrescriptionSearchTypeRecord, DoctorEndModulePrescriptionStatusRecord
} from "@/constants";
import {transformObjectToOption} from "@/constants/_shared";
import {NButton} from "naive-ui";
import moment from "moment/moment";
import {DoctorEndPresPage} from "@/services/api/doctorEndApi";
import {hasManagementDetails, hasManagementForm} from "@/views/PharmacistPrescription/authList";
import DetailsDrawer from "@/views/PharmacistPrescription/compoments/detailsDrawer.vue";
import {useRoute} from "vue-router";

interface prescriptionProps {
  tabNameRef: string; // tab标签值
}

/** props */
const props = withDefaults(defineProps<prescriptionProps>(), {
  tabNameRef: DoctorEndPrescriptionSelection.waitCheck
});

const {createMessageSuccess, createMessageError} = useMessages();

const route = useRoute();
/* 初始化参数 */
const tabValue = ref(null);
const initParams = {
  searchValue: '', // 搜索关键字

  code: null, // 处方编号
  nickName: null, // 用户昵称
  pharmacistName: null, // 药师姓名
  doctorName: null, // 医生姓名
  patientsName: null, // 患者姓名
  consultationCode: null, // 问诊单单号

  searchType: 'patientsName',
  creationTime: null, // 创建时间
};
const formValue = ref({...initParams});
const searchTypeRecordOptions = transformObjectToOption(DoctorEndModulePrescriptionSearchTypeRecord);

/* 表格方法Hook */
const {
  isLoading,
  tableData,
  paginationRef,
  paginationChange,
  summaryRef,
  sortTableData,
  pageTableData,
} = useTableDefault({
  pageDataRequest: DoctorEndPresPage,
});

//刷新
const refresh = () => {
  getTableData()
}

/** 搜索 */
const formSearch = () => {
  getTableData()
}

/** 获取参数 */
const getParams = () => {
  const {searchType, searchValue, creationTime} = formValue.value;
  return {
    status: tabValue.value !== DoctorEndPrescriptionSelection.all ? tabValue.value : undefined,
    [searchType]: !isNullStringOrNullOrUnDef(searchValue) ? searchValue : undefined,
    startTime: creationTime ? moment(creationTime[0]).format(`YYYY-MM-DD HH:mm:ss`) : null,
    endTime: creationTime ? moment(creationTime[1]).format(`YYYY-MM-DD HH:mm:ss`) : null,
  }
};

/** 请求表格数据 */
function getTableData() {
  pageTableData(getParams(), paginationRef.value, true);
}

/** 弹窗类型 */
const getDrawerType = (type) => {
  switch (type) {
    case PrescriptionSelection.unopened:
      return PrescriptionType.beginPrescription;
    case PrescriptionSelection.alreadyOpen:
      return PrescriptionType.editPrescription;
    default:
      return PrescriptionType.detailsPrescription;
  }
};

/* 表格项 */
const tableColumns = ref([]);
const tableColumnsSource = [
  {
    title: "处方编号",
    key: "id",
    align: "left",
    fixed: "left",
    width: 100,
  },
  {
    title: "医生姓名",
    key: "doctorName",
    align: "left",
    width: 100,
  },
  {
    title: "科室",
    key: "departmentName",
    align: "left",
    width: 100,
  },
  {
    title: "机构",
    key: "institutionName",
    align: "left",
    width: 120,
  },
  {
    title: "患者姓名",
    key: "patientsName",
    align: "left",
    width: 120,
  },
  {
    title: "用户昵称",
    key: "nickName",
    align: "left",
    width: 120,
  },
  {
    title: "处方状态",
    key: "status",
    align: "left",
    width: 100,
    render: (row) => {
      return `${DoctorEndModulePrescriptionStatusRecord[row.status]}`
    }
  },
  {
    title: "关联问诊单号",
    key: "consultationCode",
    align: "left",
    width: 120,
  },
  {
    title: "审核药师",
    key: "pharmacistName",
    align: "left",
    width: 100,
  },
  {
    title: "创建时间",
    key: "createTime",
    align: "left",
  },
  {
    title: "操作",
    key: "action",
    width: 80,
    align: "left",
    fixed: "right",
    render: row => {
      return (
          <n-space style="padding: 5px 0;">
            {hasManagementDetails ? (
                <n-button text type="primary" onClick={() => handlerDetailDrawerClick(row)}>
                  详情
                </n-button>
            ) : null}
            {hasManagementForm &&
            (row.status == MedicalInquiryPrescriptionSelection.available ||
                row.status == MedicalInquiryPrescriptionSelection.haveBeenUsed) ? (
                <n-button
                    text
                    type="primary"
                    disabled={isDownloading.value}
                    onClick={() => handlerMedicalInquiryFormClick(row)}>
                  处方笺
                </n-button>
            ) : null}
          </n-space>
      );
    },
  },
];

/** 开处方 与 编辑 */
// const drawerPrescriptionShow = ref<InstanceType<typeof DrawerPrescriptionDetail> | null>(null);

const detailDrawerRef = ref(null)
const isDownloading = ref(false);
const handlerMedicalInquiryFormClick = async row => {
  let pdfMinioUrl = ""
  if (row.pdfMinioUrl && row.pdfMinioUrl !== "") {
    if (!row.pdfMinioUrl.startsWith("http")) {
      pdfMinioUrl = transformMinioSrc(row.pdfMinioUrl);
    } else {
      pdfMinioUrl = row.pdfMinioUrl;

    }
  }
  isDownloading.value = true;
  downloadLocalFile(pdfMinioUrl, "处方笺");
  isDownloading.value = false;
};
const handlerDetailDrawerClick = row => {
  const _params = {
    row,
    refresh: refresh,
    mode: 'detail',
  };
  detailDrawerRef.value?.acceptParams(_params);
};

onMounted(() => {
  if (route.query?.id) {
    setTimeout(() => {
      handlerDetailDrawerClick({id: route.query.id})
    }, 300)
  }
})

/** 监听 */
watch(() => [
      formValue.value.creationTime
    ],
    () => {
      getTableData();
    }
);

watch(() => [formValue.value.searchType], (newValue) => {
  formValue.value.code = null
  formValue.value.nickName = null
  formValue.value.pharmacistName = null
  formValue.value.doctorName = null
  formValue.value.patientsName = null
  formValue.value.consultationCode = null
})

/** 监听 */
watch(() => props.tabNameRef, (newVal) => {
      tableColumns.value = deepClone(tableColumnsSource);
      tabValue.value = newVal;
      getTableData();
    },
    {
      immediate: true
    });
</script>

<style lang="less" scoped>
@import "@/styles/default.less";

:deep .n-tag__content {
  text-overflow: ellipsis;
  overflow: hidden;
}

.action-wrapper {
  display: flex;
  align-items: center;
}
</style>
