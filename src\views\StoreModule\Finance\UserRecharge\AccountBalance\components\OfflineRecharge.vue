<template>
  <JModal
    v-model:show="show"
    width="580"
    :title="title"
    @after-leave="closeModal"
    @positive-click="_save"
    :positiveButtonProps="{
      loading: isLoading,
    }"
  >
    <n-form
      ref="formRef"
      :rules="rules"
      :model="model"
      label-width="150"
      label-placement="left"
      require-mark-placement="right-hanging"
      :style="{
          width: '100%',
        }"
    >
      <n-grid :cols="8" :x-gap="24">
        <n-form-item-gi :span="24" label="充值金额（元）" path="rechargeAmount">
          <n-input-number
            style="width: 100%"
            v-model:value="model.rechargeAmount"
            placeholder="请输入充值金额"
            :min="0"
            :max="10000000"
            :show-button="false"
          />
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="手续费（元）" path="handlingFee">
          <n-input-number
            style="width: 100%"
            v-model:value="model.handlingFee"
            placeholder="请输入手续费"
            :min="0"
            :max="10000000"
            :show-button="false"
          />
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="实充金额（元）">
          <n-input-number
            style="width: 100%"
            :value="actualRechargeAmount"
            placeholder="请输入实充金额"
            :show-button="false"
            :disabled="true"
          />
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </JModal>
</template>

<script setup lang="ts" name="OfflineRecharge">
import { ref, computed } from "vue";
import { useMessages } from "@/hooks";
import { offlineRecharge } from "@/services/api/financialApi/userRecharge";
import { FinanceBusinessType } from "@/enums";
import { useUnitConversion } from "../../hooks/unitConversion";

const { toFen, verifyDecimalPoint } = useUnitConversion();

const initParams = {
  accountUserId: null,
  rechargeAmount: null,
  handlingFee: null,
  businessType: FinanceBusinessType.OfflineRecharge,
};
const title = ref("线下充值");
const model = ref({ ...initParams });

// 计算属性：实际到账金额
const actualRechargeAmount = computed(() => {
  const rechargeAmount = model.value.rechargeAmount || 0;
  const handlingFee = model.value.handlingFee || 0;
  // 防止为负数
  const total = Math.max(rechargeAmount - handlingFee, 0);
  return parseFloat(total.toFixed(2));
});
const props = ref<any>({});
/* 提示信息 */
const { createMessageSuccess, createMessageError } = useMessages();
/* 模态框显隐状态 */
const show = ref(false);
/* 表单规则 */
const rules = {
  rechargeAmount: {
    required: true,
    trigger: ["blur", "change"],
    validator(rule, value) {
      if (!value) {
        return new Error("请输入充值金额");
      } else if (!verifyDecimalPoint(value)) {
        return new Error("只支持小数点后两位");
      }
      return true;
    },
  },
  handlingFee: {
    required: true,
    trigger: ["blur", "change"],
    validator(rule, value) {
      if (!value && value != 0) {
        return new Error("请输入手续费");
      }
      if (model.value?.rechargeAmount && model.value?.handlingFee && model.value?.handlingFee >= model.value?.rechargeAmount) {
        return new Error("手续费不能大于充值金额");
      } else if (!verifyDecimalPoint(value)) {
        return new Error("只支持小数点后两位");
      }
      return true;
    },
  },
  actualRechargeAmount: {
    trigger: ["blur", "change"],
    message: "请输入实充金额",
  },
};
const acceptParams = ({ row, refreshTable }) => {
  show.value = true;
  props.value.refresh = refreshTable;
  model.value.accountUserId = row?.accountUserId ?? "";
};
/* 表单实例 */
const formRef = ref(null);
/* 表单参数初始化 */
/* 清空表单 */
const formDataReset = () => {
  model.value = { ...initParams };
};
/* 关闭弹窗 */
const closeModal = () => {
  formDataReset();
  isLoading.value = false;
  // 弹窗取消
  show.value = false;
};
/* 确认--保存 */
const isLoading = ref(false);
const _save = (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors && !isLoading.value) {
      // if (diffMinutes < 30) {
      //   createMessageError("直播计划结束时间与开始时间间隔不得少于30分钟");
      //   return;
      // }
      try {
        isLoading.value = true;
        const params = {
          data: {
            ...model.value,
            rechargeAmount: toFen(model.value?.rechargeAmount),
            handlingFee: toFen(model.value?.handlingFee),
            actualRechargeAmount: toFen(actualRechargeAmount.value),
          },
        };
        await offlineRecharge(params);
        createMessageSuccess(`充值成功`);
        // 刷新表格数据
        props.value.refresh();
        closeModal();
      } catch (e) {
        createMessageError(`充值失败:${e}`);
        isLoading.value = false;
      }
    }
  });
};
defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less"></style>
