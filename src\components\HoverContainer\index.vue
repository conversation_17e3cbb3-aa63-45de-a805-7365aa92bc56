<template>
    <div v-if="showTooltip">
      <n-tooltip :placement="placement" trigger="hover" :content-style="{ padding: '4px 6px' }">
        <template #trigger>
          <div class="trigger-container" :class="contentClassName">
            <slot></slot>
          </div>
        </template>
        {{ tooltipContent }}
      </n-tooltip>
    </div>
    <div v-else class="withoutTip-container" :class="contentClassName">
      <slot></slot>
    </div>
  </template>
  
  <script lang="ts" setup>
  import { computed } from 'vue';
  import type { PopoverPlacement } from 'naive-ui';
  
  defineOptions({ name: 'HoverContainer' });
  
  interface Props {
    /** tooltip显示文本 */
    tooltipContent?: string;
    /** tooltip的位置 */
    placement?: PopoverPlacement;
    /** class类 */
    contentClass?: string;
  }
  
  const props = withDefaults(defineProps<Props>(), {
    tooltipContent: '',
    placement: 'bottom',
    contentClass: '',
  });
  
  const showTooltip = computed(() => Boolean(props.tooltipContent));
  
  const contentClassName = computed(
    () => `${props.contentClass}`
  );
  </script>
  
<style scoped lang="less">
.trigger-container {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    &:hover {
        background-color: #f6f6f6;
    }
}
.withoutTip-container {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    &:hover {
        background-color: #f6f6f6;
    }
}
</style>
  