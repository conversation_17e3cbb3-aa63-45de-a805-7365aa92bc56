<template>
    <div class="selling-wrapper">
        <n-data-table 
            :columns="columns" 
            :data="tableDataRef" 
            :row-key="row => row.id" 
            default-expand-all
            :style="{ height: `${120}px` }" 
            flex-height
            :single-line="false"
            size="small" 
        >
        </n-data-table>
        <!-- 付款方式设置 -->
        <PaymentMethod 
            v-model:show="show" 
            v-model:value="model"
            :price="price" 
            @save-successfully="handleSave"
        />
    </div>
</template>

<script setup lang="tsx" name="TherapySellingPriceInventory">
import { ref, watch } from "vue";
import { useMessages } from '@/hooks';
/** 相关组件 */
import PaymentMethod from "./PaymentMethod.vue";
import InventoryDetail from "./InventoryDetail.vue";

type GoodsSpecificationTable = {
    value: Array<{
        id: string;
        price: number; // 售价（元）
        costPrice:number; // 成本价（元）
        dailyCost: number; // 日均费用（元）
        cycle: number; // 用药周期（天）
        availStocks: number; // 可用库存
        lockedStocks: number, // 冻结库存
        initSaled: number; // 初始已售（前端展示）
        isDownPayment: 0 | 1, // 是否支持定金支付。0=否；1=是
        isCashOnDelivery: 0 | 1, // 是否支持物流代收。0=否；1=是
        downPayment: number, // 定金单价，单位分
    }>,
}

/** Props */
const props = withDefaults(defineProps<GoodsSpecificationTable>(), {});

/** emits */
const emits = defineEmits<{
    (e: 'update:value', value: Array<{
        id: string;
        price: number; // 售价（元）
        dailyCost: number; // 日均费用（元）
        cycle: number; // 用药周期（天）
        availStocks: number; // 可用库存
        initSaled: number; // 初始已售（前端展示）
        isDownPayment: 0 | 1, // 是否支持定金支付。0=否；1=是
        isCashOnDelivery: 0 | 1, // 是否支持物流代收。0=否；1=是
        downPayment: number, // 定金单价，单位分
    }>,
    ): void;
}>();

const { createMessageError } = useMessages();

/** 付款方式展示 */
const show = ref(false);
/** 售价 */
const price = ref(null);
/** 成本价 */
const costPrice = ref(null);
/** 表单参数 */
const model = ref<{
    id: string;
    isDownPayment: 0 | 1; // 是否支持定金支付。0=否；1=是
    isCashOnDelivery: 0 | 1; // 是否支持物流代收。0=否；1=是
    downPayment: number; // 定金单价，单位分
}>({
    id: null,
    isDownPayment: 0,
    isCashOnDelivery: 0,
    downPayment: null,
});

/** 表格数据 */
const tableDataRef = ref([]);

/** 表单项 */
const columns = [
    {
        title: '售价（元）',
        key: 'price',
        resizable: true,
        render: (row) => {
            return <n-input-number
                precision={2}
                value={row?.price}
                onUpdateValue={(value) => handleUpdateValue(row.id, 'price', value)}
                placeholder="请输入售价（元）"
                min={0}
                max={99999}
                show-button={false}
            />
        }
    },
    {
        title: '日均费用（元）',
        key: 'dailyCost',
        resizable: true,
        render: (row) => {
            return <n-input-number
                precision={2}
                value={row?.dailyCost}
                onUpdateValue={(value) => handleUpdateValue(row.id, 'dailyCost', value)}
                placeholder="请输入日均费用（元）"
                min={0.01}
                max={10000}
                show-button={false}
            />
        }
    },
    {
        title: '用药周期（天）',
        key: 'cycle',
        resizable: true,
        render: (row) => {
            return <n-input-number
                value={row?.cycle}
                onUpdateValue={(value) => handleUpdateValue(row.id, 'cycle', value)}
                placeholder="请输入用药周期（天）"
                min={1}
                max={1000}
                show-button={false}
                precision={0}
            />
        }
    },
    {
        title: () => {
            return <span>库存
                <n-popover trigger="hover" raw>
                    {{
                        default: () => <InventoryDetail quantityInStock={{lockedStocks: tableDataRef.value[0]?.lockedStocks ?? 0, availStocks: tableDataRef.value[0]?.availStocks ?? 0 }} />,
                        trigger: () => <span style="cursor: pointer;">【详细】</span>
                    }}
                </n-popover>
            </span>
        },
        key: 'availStocks',
        resizable: true,
        render: (row) => {
            return <n-input-number
                value={row?.availStocks}
                onUpdateValue={(value) => handleUpdateValue(row.id, 'availStocks', value)}
                placeholder="请输入库存"
                min={0}
                max={99999}
                show-button={false}
                precision={0}
            />
        }
    },
    {
      title: '成本价（元）',
      key: 'costPrice',
      resizable: true,
      render: (row) => {
        return <n-input-number
          precision={2}
          value={row?.costPrice}
          onUpdateValue={(value) => handleUpdateValue(row.id, 'costPrice', value)}
          placeholder="请输入成本价（元）"
          show-button={false}
          min={0}
          max={1000000}
        />
      }
    },
    {
        title: '初始已售（前端显示）',
        key: 'initSaled',
        resizable: true,
        render: (row) => {
            return <n-input-number
                value={row?.initSaled}
                onUpdateValue={(value) => handleUpdateValue(row.id, 'initSaled', value)}
                placeholder="请输入初始已售"
                show-button={false}
                precision={0}
                max={10000}
            />
        }
    },
    {
        title: '操作',
        key: 'operation',
        width: 120,
        fixed: "right",
        render: (row, index) => {
            return (
                <n-button text type="primary" onClick={() => handleSetPaymentMethod(row)}>付款方式设置</n-button>
            )
        },
    }
];

/** 设置付款方式 */
const handleSetPaymentMethod = (row: Partial<{
    id: string;
    price: number; // 售价
    isDownPayment: 0 | 1; // 是否支持定金支付。0=否；1=是
    isCashOnDelivery: 0 | 1; // 是否支持物流代收。0=否；1=是
    downPayment: number; // 定金单价，单位分
}>) => {
    if (!row?.price) {
        createMessageError("请先输入售价！");
        return;
    }
    price.value = Number(row.price);
    model.value.id = row.id;
    model.value.isCashOnDelivery = row.isCashOnDelivery;
    model.value.isDownPayment = row.isDownPayment;
    model.value.downPayment = row.downPayment;
    show.value = true;
};

/** 更新值 */
const handleUpdateValue = (id: string, key: string, value: string | number) => {
    tableDataRef.value.forEach((item) => {
      if (item.id == id) {
        item[key] = value;
      }
    });
    emits('update:value', tableDataRef.value);
};

/** 付款方式设置成功回调 */
const handleSave = (formData: {
    id: string;
    isDownPayment: 0 | 1; // 是否支持定金支付。0=否；1=是
    isCashOnDelivery: 0 | 1; // 是否支持物流代收。0=否；1=是
    downPayment: number; // 定金单价，单位分
}) => {
    const item = tableDataRef.value.find(item => item.id == formData.id);
    if (item) {
        item['isCashOnDelivery'] = formData.isCashOnDelivery;
        item['isDownPayment'] = formData.isDownPayment;
        item['downPayment'] = formData.downPayment;
        emits('update:value', tableDataRef.value);
    }
};

/** 监听 */
watch(() => props.value, (newVal) => {
    tableDataRef.value = newVal;
}, { immediate: true });
</script>


<style lang="less" scoped></style>