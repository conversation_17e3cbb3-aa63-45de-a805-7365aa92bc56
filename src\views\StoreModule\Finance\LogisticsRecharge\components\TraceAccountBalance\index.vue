<template>
  <div class="inner-page-height">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :pagination="paginationRef"
      :isTableSelection="false"
      @paginationChange="paginationChange"
    >
      <!-- 操作项 -->
      <template #tableHeaderBtn>
        <n-button @click="refresh" class="store-button">刷 新</n-button>
      </template>
    </FormLayout>
	<!-- 充值历史 -->
	<RechargeHistory ref="rchargeHistoryRef" />
  </div>
</template>

<script lang="tsx" setup name="TraceAccountBalance">
import { ref, onMounted } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { getTrackBalance, type TrackBalance } from "@/services/api";
import { useRouter } from "vue-router";
import { hasRechargerecordAuth } from "../../../authList";
import RechargeHistory from "./components/RechargeHistory.vue";

const router = useRouter();

/** 表格方法Hook */
const {
  isLoading,
  tableData,
  pageTableData,
  paginationRef,
  paginationChange,
  refreshTableData,
} = useTableDefault({
  pageDataRequest: getTrackBalance,
});

/* 表格列表项 */
const tableColumns = ref([
	{
		title: "平台方",
		key: "platformName",
		width: 150,
		align: "left",
		render: (row) => {
			return row.platformName ?? "-";
		},
	},
	{
		title: "剩余单数",
		key: "remainTotalTimes",
		width: 180,
		align: "left",
		render: (row) => {
			return row.remainTotalTimes ?? "-";
		},
	},
	{
		title: "总充值",
		key: "totalMoney",
		width: 150,
		align: "left",
		render: (row) => {
			return <amount-conver value={row.totalMoney} />;
		},
	},
	{
		title: "操作",
		key: "action",
		width: 100,
		fixed: "right",
		align: "left",
		render: (row: TrackBalance) => {
			return (
				<n-space>
					{/* 充值记录 */}
					{hasRechargerecordAuth ? <n-button
						text
						type="primary"
						onclick={() => openRechargeHistoryModal(row)}
					>
						充值记录
					</n-button> : null}
				</n-space>
			);
		},
	},
]);

/** 打开充值记录 */
const rchargeHistoryRef = ref<InstanceType<typeof RechargeHistory> | null>(null);
const openRechargeHistoryModal = (row: Partial<TrackBalance> = {}) => {
	const params = {
		businessId: row.businessId,
	};
	rchargeHistoryRef.value?.acceptParams(params);
};

/** 表格刷新 */
function refresh(){
  tableSearch();
}

/* 表格搜索 */
const tableSearch = () => {
  pageTableData({}, paginationRef.value);
};

/** 组件挂载 */
onMounted(() => {
  tableSearch();
});
</script>

<style scoped lang="less"></style>
