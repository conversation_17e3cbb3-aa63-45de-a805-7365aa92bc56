<template>
  <div class="wrapper inner-page-height">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :pagination="paginationRef"
      :isTableSelection="false"
      @paginationChange="paginationChange"
      @table-sorter-change="tableSorterChange"
      :isNeedCollapse="false"
      :isDisplayIndex="false"
    >
      <!-- 表单 -->
      <template #searchForm>
        <n-form
          ref="formRef"
          label-placement="left"
          label-width="auto"
          :show-feedback="false"
          require-mark-placement="right-hanging"
          size="small"
          :style="{ width: '100%' }"
        >
          <n-form-item :span="12" label="用户昵称" path="">
            <j-search-input v-model:value.trim="model.nickname" placeholder="请输入用户昵称" @search="handlerSearch" />
          </n-form-item>
        </n-form>
      </template>
      <template #tableHeaderBtn>
        <n-button @click="refresh" :loading="isLoading" class="store-button">刷 新</n-button>
      </template>
    </FormLayout>
  </div>
</template>

<script lang="tsx" setup name="ExpenditureRecord">
import { onMounted, ref } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { expenseRecordGetPage } from "@/services/api/financialApi/userRecharge";
import { useMessages } from "@/hooks";
import TableTooltip from "@/components/TableTooltip/index.vue";
import { useUnitConversion } from "../hooks/unitConversion";

const { toYuanString } = useUnitConversion();

const { createMessageSuccess, createMessageError } = useMessages();

function getExpenseType(type: number): string {
  switch (type) {
    case 1:
      return "退款";
    case 2:
      return "经销商会员兑换积分商品成本扣除";
    default:
      return "";
  }
}

/** 表格hook */
const {
  isLoading,
  tableData,
  paginationRef,
  pageTableData,
  paginationChange,
  sortTableData,
} = useTableDefault({
  pageDataRequest: expenseRecordGetPage,
});

/* 表格列表项 */
const tableColumns = ref([
  {
    title: "用户昵称/ID",
    key: "nickname",
    width: 150,
    align: "left",
    ellipsis: {
      tooltip: true,
    },
    render(rowData: any) {
      return <TableTooltip row={rowData} nameKey="nickname" title={rowData.nickname} idKey="userId" />;
    },
  },
  {
    title: "支出金额（元）",
    key: "paymentAmount",
    width: 100,
    align: "left",
    render: rowData => {
      return toYuanString(rowData?.paymentAmount);
    },
  },
  {
    title: "支出类型",
    key: "expenseType",
    width: 100,
    align: "left",
    render: (row) => {
      return getExpenseType(row?.expenseType);
    },
  },
  {
    title: "备注",
    key: "remarks",
    width: 180,
    align: "left",
  },
  {
    title: "充值时间",
    key: "createTime",
    width: 150,
    align: "left",
  },
  // {
  //   title: "操作",
  //   key: "action",
  //   width: 120,
  //   fixed: "right",
  //   align: "left",
  // },
]);

/** 参数 */
const model = ref({
  nickname: "",
});

/** 搜索 */
const handlerSearch = () => {
  tableSearch();
};

/** 表格刷新 */
function refresh() {
  tableSearch();
}

const getParams = () => {
  return model.value;
};

/** 排序 */
const tableSorterChange = (info: { sort: string, sortAsc: "ascend" | "descend" }) => {
  sortTableData(info.sort, info.sortAsc);
};


/* 表格搜索 */
const tableSearch = () => {
  pageTableData(getParams(), paginationRef.value);
};

/** 组件挂载 */
onMounted(() => {
  tableSearch();
});
</script>

<style lang="less" scoped>
@import "@/styles/defaultVar.less";
</style>
