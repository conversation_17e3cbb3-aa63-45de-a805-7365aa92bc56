<template>
  <n-modal v-model:show="modalShow" :auto-focus="false" @after-leave="handleClose">
    <n-card
      style="width: 600px"
      :bordered="false"
      size="small"
      :title="props.title + '- 推流地址'"
      closable
      @close="handleClose"
    >
      <n-form
        ref="formRef"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
        size="small"
        :style="{ width: '100%' }"
      >
        <n-grid :cols="24" :x-gap="24" responsive="self">
          <!-- <n-form-item-gi :span="24" label="rtmp推流地址" path="">
            <n-space>
              <n-input :readonly="true" :value="formValue.rtmp" placeholder="" style="width: 400px" />
              <n-button @click="createCenterLink(formValue.rtmp)">复制</n-button>
            </n-space>
          </n-form-item-gi> -->
          <n-form-item-gi :span="24" label="OBS服务器" path="">
            <n-space>
              <n-input :readonly="true" :value="pushPath" placeholder="" style="width: 400px" />
              <n-button @click="createCenterLink(pushPath)">复制</n-button>
            </n-space>
          </n-form-item-gi>
          <n-form-item-gi :span="24" label="OBS推流码" path="">
            <n-space>
              <n-input :readonly="true" :value="streamingCode" placeholder="" style="width: 400px" />
              <n-button @click="createCenterLink(streamingCode)">复制</n-button>
            </n-space>
          </n-form-item-gi>
        </n-grid>
      </n-form>
      <div class="steps">
        <p>OBS直播教程</p>
        <n-space vertical>
          <n-steps vertical size="small" :current="currentRef">
            <n-step title=" 步骤1：获取外部推流地址" />
            <n-step title=" 步骤2：在外部推流器，填写推流的服务器地址和推流码，开始推流" />
            <n-step title=" 步骤3：在观看页查看直播效果" />
          </n-steps>
        </n-space>
      </div>
    </n-card>
  </n-modal>
</template>
<script setup lang="ts">
import { computed, reactive, ref, onMounted } from "vue";
import { copyText } from "@/utils/clipboardUtils";
import { useMessages } from "@/hooks/useMessage";
const message = useMessages();

const props = defineProps({
  show: {
    // 是否显示
    type: Boolean,
    default: false,
  },
  pushPath: {
    type: String,
    default: "",
  },
  streamingCode: {
    type: String,
    default: "",
  },
  title: {
    type: String,
    default: "",
  },
});
const currentRef = ref<number | null>(0);
const emit = defineEmits(["update:show"]);

const modalShow = computed({
  get() {
    return props.show;
  },
  set(value) {
    emit("update:show", value);
  },
});
function handleClose() {
  modalShow.value = false;
}
const createCenterLink = val => {
  copyText(val);
  message.createMessageExportSuccess("复制成功");
};
</script>

<style scoped lang="less">
.steps {
  p {
    margin-bottom: 10px;
    font-size: 16px;
    color: black;
  }
}
:deep(.n-step-content-header) {
  color: #6b6b6b !important;
}
</style>
