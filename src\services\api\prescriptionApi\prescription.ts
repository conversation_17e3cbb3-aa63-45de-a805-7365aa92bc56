import {defHttp} from '@/services';

export const enum PrescriptionApi {
  presPage = '/pres/page', 
  presGetDetail = '/pres/get',
  presPrescribing = '/pres/prescribing',
  presBatchUpdateStatus = '/pres/batchUpdateStatus',
  presUpdate = '/pres/update',
  presBatchDelete = '/pres/batchDelete'
}

//处方-分页查询
export function presPage(params) {
    return defHttp.post({
      url: PrescriptionApi.presPage,
      params
    });
}

//处方-详情
export function getPrescriptionDetail(params) {
    return defHttp.get({
      url: PrescriptionApi.presGetDetail+'?id=' + params,
    })
}

//处方-开处方
export function presPrescribing(params) {
    return defHttp.put({
      url: PrescriptionApi.presPrescribing,
      params: {
        data: params
      },
    })
}

//处方-批量取消/恢复处方
export function presBatchUpdateStatus(params) {
    return defHttp.put({
      url: PrescriptionApi.presBatchUpdateStatus,
      params: {
        data: params
      },
    })
}


//处方-编辑处方
export function presUpdate(params) {
    return defHttp.put({
      url: PrescriptionApi.presUpdate,
      params: {
        data: params
      },
    })
}

//处方-批量删除
export function presBatchDelete(params) {
    return defHttp.post({
      url: PrescriptionApi.presBatchDelete,
      params
    });
}