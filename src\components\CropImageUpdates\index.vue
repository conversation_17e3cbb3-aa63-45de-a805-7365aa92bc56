<template>
    <n-modal
      :show="showRef"
      :auto-focus="false"
      preset="card"
      :style="{
        width: '650px',
      }"
      :title="title"
      size="small"
      @update:show="handleCacnel"
    >
        <div class="cropper-wrapper" >
          <vue-cropper
            ref="cropperRef"
            :img="cropperSrcRef"
            :auto-crop="true"
            :center-box="true"
            :background="false"
            :output-type="'png'" 
            mode="cover"
            :fixed="true"
            :fixedNumber="props.fixedNumber"
            :fixedBox="props.fixedBox"
            :original="props.original"
            :infoTrue="props.infoTrue"
          ></vue-cropper>
        </div>
      
      <template #footer>
        <n-space justify="end">
          <n-button size="small" @click="handleCacnel">取消</n-button>
          <n-button size="small" type="primary" @click="handleAvatarSave" :loading="isUploadLoadingRef">保存</n-button>
        </n-space>
      </template>
    </n-modal>
  </template>
  
<script lang="ts" setup>
import "vue-cropper/dist/index.css";
import { VueCropper } from "vue-cropper";
import { ref, toRef, watch } from "vue";
import {
  blobToFile,
} from "@/utils/fileUtils";
import { fileUpload } from "@/services/api";
import { useMessages } from "@/hooks";

const props = withDefaults(
  defineProps<{
    value: string;
    show: boolean;
    /** 是否生成缩略图 */
    isThumb?: boolean,
    title?:string,
    /** 长宽比例 */
    aspectRatio?:number,
    /** 截图框的宽高比例 */
    fixedNumber?:[],
    /** 固定截图框大小，不允许改变 */
    fixedBox?:boolean,
    /** 上传图片按照原始比例渲染 */
    original?:boolean,
    /** true为展示真实输出图片宽高，false展示看到的截图框宽高 */
    infoTrue?:boolean,
  }>(),
  {
    isThumb: false,
    title:'修改头像',
    aspectRatio:1,
    fixedBox:true,
    original:true,
    infoTrue:true
  }
);
const valueRef = toRef(props, "value");
const showRef = toRef(props, "show");
const { createMessageError } = useMessages();
const isUploadLoadingRef = ref(false);
const emits = defineEmits<{
  (e: "update:value", src: string): void;
  (e: "update:show", show: boolean): void;
  (e: "updateCropImage", updateCropImage);
}>();
  
const cropperSrcRef = ref();
const cropperRef = ref(null);

function handleAvatarSave() {
  cropperRef.value.getCropBlob(async (data) => {
    const _file = blobToFile(data);
    
    const _formData = new FormData();
    _formData.append("files", _file);
    try {
      isUploadLoadingRef.value = true;
      const resp = await fileUpload(props.isThumb,_formData);
      emits("updateCropImage", resp);
      emits("update:show", false);
    } catch (e) {
      createMessageError(`上传头像失败:${e}`);
    } finally {
      isUploadLoadingRef.value = false;
    }
  });
}
  
function handleCacnel() {
  emits("update:show", false);
  emits("updateCropImage", []);
}

watch(
  valueRef,
  (newVal) => {
      cropperSrcRef.value = newVal
  },
  { immediate: true }
);
</script>
  
<style lang="less" scoped>
 .cropper-wrapper {
   width: 100%;
   height: 400px;
   border: 1px solid #b1b1b1;
   position: relative;
   background: #f2f3f5;
 }
</style>
  