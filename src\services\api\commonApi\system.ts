import { defHttp } from "@/services";
// import { basicPlatformUrl } from "@/utils/http/urlUtils";
import { type GlobalConfig } from "@/enums";

/** 下拉选项Api */
export const enum SystemApi{
    // getParamByKey = "/systemConfig/getParamByKey",
    getGlobalConfigs = "/globalConfigs/getGlobalConfigs"
}

/**
 * @description 查询系统参数(接口调整)
 */
// export function getSystemConfigByKey(key: string) {
//     return defHttp.get({
//         url: basicPlatformUrl(SystemApi.getParamByKey) + "?key=" + key,
//     });
// }

/**
 * @description 获取全局配置
 */
export function getGlobalConfigs() {
    return defHttp.get<GlobalConfig>({
        url: SystemApi.getGlobalConfigs,
    });
}