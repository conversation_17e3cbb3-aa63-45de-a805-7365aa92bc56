<template>
  <JModal
    v-model:show="show"
    width="680"
    title="新增敏感词"
    @after-leave="closeModal"
    @positive-click="_save"
    :positiveButtonProps="{
			loading: isLoading
		}"
  >
    <span>分行录入可录入不同数量，一次可录入250个，一行最多8字符</span>
    <n-form
      style="padding-top: 10px;"
      ref="formRef"
      :rules="rules"
      :model="model"
      label-width="auto"
      label-placement="left"
      require-mark-placement="right-hanging"
      :style="{
          width: '100%',
        }"
    >
      <n-grid :cols="8" :x-gap="24">
        <n-form-item-gi :span="24" label="" path="lexicon" required>
          <n-input
            v-model:value="model.lexicon"
            type="textarea"
            style="width: 100%;height:480px"
            @input="lexiconInput"
            :show-button="false"
            clearable
          />
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </JModal>
</template>

<script setup lang="ts" name="GroupConfigurationShow">
import { ref } from "vue";
import { useMessages } from "@/hooks";
import {
  sensitiveWordBatchAdd
} from "@/services/api/contentConfigApi";

export interface AddLexiconModelProps {
  refreshTable?: () => void; // 刷新表格数据
}
const Add_active = ref<boolean>(true);

/** 初始化参数 */
const initParams = {
  lexicon:''
};
const model = ref({ ...initParams });

/* 提示信息 */
const message = useMessages();
/* 模态框显隐状态 */
const show = ref(false);
/* 父组件传过来的参数 */
const parameter = ref<AddLexiconModelProps>({});

/* 表单规则 */
const rules = {
  lexicon:{
    required: true,
    trigger: ["blur", "change"],
    message: "请先输入敏感词",
  },
};

/* 表单实例 */
const formRef = ref(null);

/* 清空表单 */
const formDataReset = () => {
  model.value = { ...initParams };
  Add_active.value = true
  parameter.value = {}
};

/* 关闭弹窗 */
const closeModal = () => {
  formDataReset();
};

/** 获取参数 */
function getParams() {
  const { lexicon } = model.value;
  return {
    lexicon
  }
}

const acceptParams = async (params: AddLexiconModelProps) => {
  parameter.value = params;
  show.value = true;
};

const lexiconInput = (e) => {
  console.log(e);
  var lines = e.split('\n');
  for (var i = 0; i < lines.length; i++) {
    if (lines[i].length > 8) {
      lines[i] = lines[i].slice(0, 8);
    }
  }
  model.value.lexicon = lines.slice(0,250).join('\n');
}

/* 确认--保存 */
const isLoading = ref(false);
const _save = (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      try {
        isLoading.value = true;
        await sensitiveWordBatchAdd({
          "data": model.value.lexicon.split('\n'),
        })
        message.createMessageSuccess(`添加自定义敏感词成功`);
        // 弹窗取消
        show.value = false;
      } catch (e) {
        message.createMessageError(`新增自定义敏感词失败： ${e}`);
      } finally {
        isLoading.value = false;
        // 刷新表格数据
        parameter.value.refreshTable();
      }
    }
  });
};

defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less"></style>
