<template>
  <n-tooltip placement="bottom" :trigger="isShowToolTipRef ? 'hover' : 'click'" :disabled="!isShowToolTipRef">
    <template #trigger>
      <n-button
        v-if="props.size != 'text'"
        v-bind="$attrs"
        :ghost="isGhostRef"
        :type="typeRef"
        :size="sizeRef"
        @click="copyUrl"
        ref="copyBtnRef"
        :data-clipboard-text="valueRef"
      >
        {{ labelRef }}
      </n-button>
      <n-button
        v-else
        text
        v-bind="$attrs"
        :type="typeRef"
        :size="sizeRef"
        @click="copyUrl"
        ref="copyBtnRef"
        :data-clipboard-text="valueRef"
      >
        <p class="text-copt-btn">{{ labelRef }}<img :src="copyIconSrc" v-if="!props.displayId"/></p>
        
      </n-button>
    </template>
    <p style="max-width: 200px">{{ valueRef }}</p>
  </n-tooltip>
  <input v-if="!isCanUseClipboard" type="text" :value="valueRef" ref="tempInputRef" class="tempInput" />
</template>

<script setup lang="ts">
import { useMessages } from "@/hooks";
import { nextTick, onBeforeUnmount, onMounted, ref, toRefs } from "vue";
import Clipboard from "clipboard";
import copyIconSrc from "@/assets/image/opt/copyIcon.png"
const props = withDefaults(
  defineProps<{
    value: string;
    type?:
      | "default"
      | "tertiary"
      | "primary"
      | "success"
      | "info"
      | "warning"
      | "error";
    size?: "tiny" | "small" | "medium" | "large" | 'text';
    isGhost?: boolean;
    label?: string;
    isShowToolTip?: boolean;
    displayId?:boolean;
  }>(),
  {
    value: "",
    type: "info",
    isGhost: true,
    isShowToolTip: false,
    size: "text",
    label: "复制",
    displayId:false,
  }
);
const {
  value: valueRef,
  label: labelRef,
  type: typeRef,
  isGhost: isGhostRef,
  size: sizeRef,
  isShowToolTip: isShowToolTipRef,
} = toRefs(props);
const { createMessageError, createMessageSuccess } = useMessages();
const copyBtnRef = ref(null);
const tempInputRef = ref(null);
const isCanUseClipboard = navigator.clipboard ? true : false;
// let clipboard = null
// onMounted(async ()=>{
//     await nextTick()
//     clipboard = new Clipboard(copyBtnRef.value.selfElRef)
// })
// onBeforeUnmount(()=>{
//     clipboard&&clipboard.destroy();
// })
async function copyUrl() {
  if (isCanUseClipboard) {
    try {
      await navigator.clipboard.writeText(valueRef.value);
      createMessageSuccess("复制成功");
    } catch (e) {
      createMessageError("复制失败");
    }
  } else {
    tempInputRef.value.focus();
    tempInputRef.value.select();
    document.execCommand("Copy");
    createMessageSuccess("复制成功");
  }
  // clipboard.on('success', () => {
  //     createMessageSuccess('复制成功')
  // })
  // clipboard.on('error', ()=>{
  //     createMessageError('复制失败');
  // });
}
</script>
<style scoped lang="less">
.tempInput {
  position: absolute;
  z-index: 1;
  right: 10000px;
  bottom: 10000px;
}
.text-copt-btn{
  display: flex;
  align-items: center;
  line-height: 16px;
  font-size: 14px;
  img{
    margin-left: 2px;
    height:16px;
    width: 16px;
  }

}
</style>
