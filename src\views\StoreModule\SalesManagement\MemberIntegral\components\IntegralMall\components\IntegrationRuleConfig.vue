<template>
  <JDrawer
    v-model:show="show"
    title="积分规则配置"
    :isGetLoading="isGetLoading"
    @after-leave="handleAfterLeave"
    @after-enter="handleAfterEnter"
    :to="props.to"
  >
    <!-- 基本信息 -->
    <template #content>
      <n-flex align="center" justify="space-between" style="width: 97%;">
        <span class="explain">
          说明：可更改自定义规则，非必填。录入要修改内容，保存配置，即可更改前台积分规则内容。可预览查看效果。
        </span>
        <n-button type="primary" @click="openJRulePreview" class="store-button">预 览</n-button>
      </n-flex>
      <!-- 积分相关规则 -->
      <n-card title="积分相关规则" :bordered="false" size="small">
        <div class="default-rule-wrapper">
          <n-data-table
            :columns="COLUMNS"
            :data="POINTSRULE"
            :row-key="row => row.id"
            :style="{ minHeight: `${170}px` }"
            default-expand-all
            flex-height
            :single-line="false"
            size="small"
          />
        </div>
      </n-card>
      <!-- 自定义规则 -->
      <n-card title="自定义规则" :bordered="false" size="small">
        <div class="custom-rule-wrapper">
          <n-data-table
            :columns="columns"
            :data="tableDataRef"
            :row-key="row => row.id"
            :style="{ minHeight: `${400}px` }"
            default-expand-all
            flex-height
            :single-line="false"
            size="small"
          >
            <template #empty>
              <JEmpty />
            </template>
          </n-data-table>
          <!-- 新增 -->
          <JTextButton 
            type="primary" 
            size="large" 
            text 
            class="add" 
            @click="addIntegralRule"
          >
            新 增
          </JTextButton>
        </div>  
      </n-card>
    </template>
    <!-- Footer -->
    <template #footer>
      <div class="footer-wrapper">
        <n-space>
          <n-button @click="show = false" class="store-button">取消</n-button>
          <n-button type="primary" :loading="isLoading" @click="_save" class="store-button">保存</n-button>
        </n-space>
      </div>
    </template>
    <!-- 打开预览 -->
    <JRulePreview ref="jRulePreviewRef" :previewContent="previewContent"  />
  </JDrawer>
</template>

<script lang="tsx" setup name="IntegrationRuleConfig">
import { ref, computed } from "vue";
import { getPointRule, addPointRule } from "@/services/api";
import { useMessages, useLoading } from '@/hooks';
import { isArray, uuid } from "@/utils";
import { POINTSRULE, COLUMNS } from "../settings";
/** 相关组件 */
import JDrawer from "@/components/JDrawer/index.vue";
import JTextButton from "@/components/JTextButton/index.vue";
import JEmpty from "./JEmpty.vue";
import JRulePreview from "./JRulePreview.vue";

const { createMessageSuccess, createMessageError } = useMessages();

/** props */
const props = withDefaults(defineProps<{
  to?: string; // 弹窗位置
  refreshTable?: () => void; // 刷新表格数据
}>(), {
  to: '#integral-mall'
});

/** 显隐 */
const show = ref(false);
/** 接收父组件参数 */
const acceptParams = () => {
  show.value = true;
};

/** 预览数据 */
const previewContent = computed(() => {
  const defaultIntegrationData = POINTSRULE.map(item => ({
    id: item?.id,
    title: item?.name,
    content: item?.content
  }));

  const customIntegrationData = tableDataRef.value.map(item => ({
    id: item?.id,
    title: item?.content,
  }));

  return [...defaultIntegrationData, ...customIntegrationData];
});

/** 表格数据 */
const tableDataRef = ref([]);

/** edit 源自定义规则Id */
const editSourceRuleId = ref([]);

/** 自定义删除规则Id */
const delRuleId = ref([]);

/** 表单项 */
const columns = [
  {
    title: "序号",
    width: 80,
    fixed: "left",
    key: "index",
    align: "center",
    render: (renderData: object, index: number) => {
      return `${index + 1}`;
    },
  },
  {
    title: '规则内容',
    key: 'content',
    resizable: true,
    render: (row) => {
      return (
        <n-input
          value={row?.content}
          type="textarea"
          placeholder="请输入自定义规则内容"
          maxlength="300" 
          show-count
          autosize={{
            minRows: 3
          }}
          onUpdateValue={(value) => handleUpdateValue(row?.id ?? row?.addId, 'content', value)}
        />
      );
    }
  },
  {
    title: '操作',
    key: 'operation',
    width: 160,
    fixed: "right",
    render: (row, index) => {
      return (
        <n-flex>
          <n-popconfirm onPositiveClick={() => handleDelete(row?.id ?? row?.addId)}>
            {{
              default: () => {
                return (
                  <span>{`确认删除该条自定义规则吗？`}</span>
                );
              },
              trigger: () => {
                return (
                  <n-button text type="error">
                    删除
                  </n-button>
                );
              },
            }}
          </n-popconfirm>
        </n-flex>
      )
    },
  }
];

/** 更新 */
function handleUpdateValue(id: string, key: string, value: string | number) {
  const item = tableDataRef.value.find(item => item?.id === id || item?.addId === id);
  if (item && key in item) {
    item[key] = value;
  }
}

/** 校验数据录入 */
function checkItemsForNullContent(data: Array<any>) {
  for (let i = 0; i < data.length; i++) {
    if (data[i].content === null || data[i].content?.length === 0) {
      createMessageError(`第${i + 1}项规则为空，请录入数据`)
      return false;
    }
  }
  return true;
}

/** 删除自定义规则 */
function handleDelete(id: string) {
  tableDataRef.value = tableDataRef.value.filter(item => {
    return (item?.id && item.id !== id) || (item?.addId && item.addId !== id);
  });
  if (editSourceRuleId.value.includes(id)) {
    delRuleId.value.push(id);
  }
}

/** 关闭抽屉回调 */
const handleAfterLeave = () => {
  editSourceRuleId.value = [];
  delRuleId.value = [];
};

/** 抽屉出现后的回调 */
const { loading: isGetLoading, startLoading, endLoading } = useLoading(false);
const handleAfterEnter = async () => {
  try {
    startLoading();
    const data = await getPointRule({});
    if (isArray(data)) {
      tableDataRef.value = data;
      editSourceRuleId.value = data.map(item => item.id);
    }
  } catch (error) {
    createMessageError("获取自定义积分规则失败：" + error);
  } finally {
    endLoading();
  }
};

/** 新增等级 */
function addIntegralRule() {
  tableDataRef.value.push({
    addId: uuid(),
    content: null,
  });
}

/** 打开规则预览 */
const jRulePreviewRef = ref<InstanceType<typeof JRulePreview> | null>(null);
const openJRulePreview = () => {
  jRulePreviewRef.value?.acceptParams();
};

/** 获取参数 */
function _getParams() {
  let updateVOS = [];
  let addVOS = [];

  for (let i = 0; i < tableDataRef.value.length; i++) {
    if (tableDataRef.value[i]?.id) {
      updateVOS.push({
        id: tableDataRef.value[i]?.id,
        content: tableDataRef.value[i]?.content,
      });
    }
    if (tableDataRef.value[i]?.addId) {
      addVOS.push({
        content: tableDataRef.value[i]?.content,
      });
    }
  }

  return {
    updateVOS: [...updateVOS],
    addVOS: [...addVOS],
    deleteIds: [...delRuleId.value],
  }
}

/* 确认--保存 */
const { loading: isLoading, startLoading: startSaveLoading, endLoading: endSaveLoading } = useLoading(false);
const _save = async (e: MouseEvent) => {
  e.preventDefault();
  try {
    startSaveLoading();
    let checked = checkItemsForNullContent(tableDataRef.value);
    if (!checked) {
      return;
    }
    await addPointRule(_getParams());
    createMessageSuccess("自定义规则保存成功！");
    // 关闭
    show.value = false;
  } catch (error) {
    createMessageError("自定义规则保存失败：" + error);
  } finally {
    endSaveLoading();
  }
};

defineExpose({
  acceptParams,
});
</script>

<style lang="less" scoped>
@import "../styles/index.less";

:deep(.n-scrollbar-rail) {
  bottom: 8px !important;
}

.footer-wrapper {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: end;
}

.default-rule-wrapper {
  width: 98%;
}

.custom-rule-wrapper {
  width: 98%;
  position: relative;
  .add {
    position: absolute;
    top: -38px;
    right: 0;
    font-size: 16px;
  }
}
</style>
