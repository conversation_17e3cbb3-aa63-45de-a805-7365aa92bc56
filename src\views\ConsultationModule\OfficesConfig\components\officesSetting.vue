<template>
    <JModal
      v-model:show="isShow"
      width="680"
      :title="type===OfficesConfigEnum.Add?'新增科室': type===OfficesConfigEnum.Edit?'编辑科室':'创建二级科室'"
      @after-leave="closeModal"
      @positive-click="_save"
      :positiveButtonProps="{
        loading: isLoading
      }"
    >
    <n-form
      ref="formRef"
      :rules="rules"
      :model="model"
      label-width="auto"
      label-placement="left"
      require-mark-placement="right-hanging"
      :style="{
          width: '100%',
        }"
    >
      <n-grid :cols="8" :x-gap="24">
        <n-form-item-gi :span="24" label="科室名称" path="name" required>
          <n-input v-model:value="model.name" placeholder="请输入科室名称" :maxlength="12" clearable/>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="图标">
          <UploadProductImg
            ref="uploadProductImgRef"
            v-model:value="model.uploadPictures"
            :maxFileSize="1"
            accept="image/*"
          />
        </n-form-item-gi>
        <!-- 提示 -->
        <n-gi :span="12" style="margin-left: 65px; margin-bottom: 12px">
          <span>注：支持png、jpg、JPEG、GIF格式，不能大于1M，建议尺寸：20 x 20</span>
        </n-gi>
        <n-form-item-gi :span="24" label="排序号" path="order">
          <n-input v-model:value="model.order" placeholder="序号" :maxlength="3" clearable />
        </n-form-item-gi>
        <n-gi :span="12" style="margin-left: 65px; margin-bottom: 12px">
          <span>序号越大排名越前，最多3位数</span>
        </n-gi>
        <n-form-item-gi :span="12" label=" ">
          <n-checkbox v-model:checked="model.isRecommend">推荐科室</n-checkbox>
          <HelpPopover helpEntry="推荐科室" />
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </JModal>
</template>

<script setup lang="ts">
import { ref,computed, watch, nextTick } from 'vue';
import { OfficesConfigEnum } from '../type';
import { addDepartment, editDepartment, addSecondDepartment } from '@/services/api';
import { useMessages } from "@/hooks";
import UploadProductImg from "@/components/UploadProductImg/index.vue";
const message = useMessages();

const props = withDefaults(defineProps<{
    row?: any;
    show: boolean;
    type: OfficesConfigEnum;
}>(), {
    row: null,
    show: false,
    type: OfficesConfigEnum.Add,
});
const emits = defineEmits<{
    (e: 'update:show', value: boolean): void;
    (e: 'refresh'): void;
}>();

const isShow = computed({
    get: () => props.show,
    set: (value: boolean) => {
        emits('update:show', value);
    }
});

const initParams = {
  id:null,
  name: '',
  order: null,
  uploadPictures: [],
  isRecommend:false
};
const model = ref({ ...initParams });

/* 表单规则 */
const rules = {
  name:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入科室名称",
    validator: ()=>{
      return model.value.name != '';
    }
  },
  order: {
    trigger: ["blur", "change"],
    pattern: /^\d+$/,
    message: '请输入有效的数字',
  }
};

watch(()=>props.show, ()=>{
  if(props.show){
    if(props.type == OfficesConfigEnum.Edit){
      props.row?.img ? model.value.uploadPictures.push({path:props.row.img}) : null
      model.value.order = props.row.sort ?? null;
      model.value.isRecommend = Boolean(props.row.isRecommend) ;
      model.value.name = props.row.name ?? props.row.name;
    }
  }
})

const isLoading = ref(false);

// 关闭按钮
const closeModal = () => {
    isShow.value = false;
    console.log('关闭');
    initParams.uploadPictures = []
    model.value = { ...initParams };
;
}
const uploadProductImgRef = ref<InstanceType<typeof UploadProductImg> | null>(null);
// 确认按钮
const formRef = ref(null);
const _save = () => {
    formRef.value?.validate((errors: any) => {
        if (!errors) {
          console.log('errors', errors);
          if (uploadProductImgRef.value?.isUploadLoading) {
            message.createMessageError('文件正在上传中，请稍等！');
            return;
          }
          // TODO:调用接口
          const params = {
            data:{
              name: model.value.name,
              ...(props.type == OfficesConfigEnum.AddSub ? { parentId: props.row.id } : {}),
              ...(props.type == OfficesConfigEnum.Edit ? { id: props.row.id } : {}),
              img:model.value.uploadPictures.length ? model.value.uploadPictures[0].path : '',
              sort: model.value.order,
              isRecommend: Number(model.value.isRecommend)
            }
          }
          const api = props.type == OfficesConfigEnum.AddSub ? addSecondDepartment : props.type == OfficesConfigEnum.Edit ? editDepartment : addDepartment;
          isLoading.value = true;
          api(params).then((res: any) => {
            message.createMessageSuccess('操作成功');
            emits('refresh');
            closeModal();
          }).catch(err=>{
            message.createMessageError(`${err}`);
          }).finally(()=>{
            isLoading.value = false;
          })
        }
    });
}

</script>