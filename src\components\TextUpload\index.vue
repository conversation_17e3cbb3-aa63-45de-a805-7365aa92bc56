<script setup lang="ts">
import { fileUpload } from "@/services/api";
import type { UploadCustomRequestOptions, UploadFileInfo } from "naive-ui";
import { computed, onMounted, ref, toRef, toRefs, watch } from "vue";
import { useMessages } from "@/hooks";
import { getMediaDuration, revertToMinioSrc, transformMinioSrc } from "@/utils/fileUtils";
import { isArray, isString } from "@/utils/isUtils";
import type {valueType} from './hook'
import { routesMap } from "@/router/maps";
import { useRouter } from "vue-router";
import { getApiUrlPrefix, getOssFileUrlPrefix, getOssUrlPrefix } from "@/utils/http/urlUtils";
const { createMessageWarning, createMessageError } = useMessages();
const props = withDefaults(
  defineProps<{
    value: Array<valueType>;
    listType?: "text" | "image" | "image-card";
    valueKey?: string;
    maxFileSize?: number; // 文件大小
    fileListSize?: number;
    title?: string; // 上传标题
    accept?: string;
    disabled?:boolean,
    isThumb?: boolean, // 是否生成缩略图
  }>(),
  {
    listType: "image-card",
    valueKey: "data",
    maxFileSize: 0,
    fileListSize: 1,
    isThumb: null,
    title: "上传文件"
  }
);

const valueRef = toRef(props, "value");
const acceptRef = toRef(props, "accept");
const fileName = toRef(props, "value");
const textUpload = ref()
const btn = ref()
const finishStr = ref(true)
const router = useRouter();
const tempValue = ref<any>([
  {
    ...valueRef.value
  }
]);
const {
  listType: listTypeRef,
  valueKey: keyRef,
  maxFileSize: maxFileSizeRef,
  fileListSize: fileListSizeRef,
} = toRefs(props);

const fileList = ref<Array<UploadFileInfo>>([]);
watch(
  valueRef,
  (newVal) => {
    const fileListTemp: Array<UploadFileInfo> = [];
    if (!isArray(newVal)){
      newVal = [newVal]
    }
    newVal.forEach((item, index) => {
      if (item) {
        const _id = `${Math.random() * 1000}-${index}`;
        fileListTemp.push({
          id: _id,
          name: `file-${_id}`,
          status: "finished",
          url: transformMinioSrc(item.url),
        });
      }
    });
    if (fileListTemp.length>1){
      fileList.value = [fileListTemp[fileListTemp.length-1]];
    }else {
      fileList.value = [...fileListTemp];
    }
    finishStr.value = true
  },
  { immediate: true }
);
const emits = defineEmits<{
  (e: "update:value", urlList: Array<valueType>): void;
  (e: 'update:duration',duration:Array<string>| string): void;
}>();

async function customRequest(options: UploadCustomRequestOptions) {
  const { file, onFinish, onError, onProgress } = options;
  const _formData = new FormData();
  _formData.append("files", file.file);
  finishStr.value = false
  try {
    const resp = await fileUpload(true, _formData, ({ progress }) => {
      onProgress({ percent: Number((progress * 100).toFixed(2)) });
    });
    onFinish();
    if (tempValue.value.length >= fileListSizeRef.value) {
      tempValue.value = tempValue.value.splice(0, 1);
    }
    tempValue.value.push({
      name: file.file.name,
      url:resp[0]
    });
    emits("update:value", tempValue.value);
  } catch (e) {
    finishStr.value = true
    createMessageError(`上传失败：${e}`);
    onError();
  }
}
function fileRemoveHandler(options: {
  file: UploadFileInfo;
  fileList: Array<UploadFileInfo>;
}) {
  const { url, status } = options.file;
  if (status === "uploading") {
    createMessageWarning("正在上传中，请勿删除");
    return false;
  }
  return false;
}

function AddClick() {
  btn.value.handleClick()
}


function Download() {
  window.open(getOssFileUrlPrefix() + '/' + fileStr(fileName.value,'url'), '_blank');
}

function fileStr(file,str='name') {
  if (isArray(file)){
    if (file.length>1){
      return file[1][str]
    }else if (file.length == 1){
      return file[0][str]
    }
  }else {
    return file[str]
  }
}

function beforeUpload(data: {
  file: UploadFileInfo;
  fileList: UploadFileInfo[];
}) {
  if (maxFileSizeRef.value) {
    const {
      file: { size },
    } = data.file;
    if (size > maxFileSizeRef.value * 1024 * 1024) {
      createMessageError(
        `该文件超出大小，大小限制为 ${maxFileSizeRef.value} MB`
      );
      return false;
    }
  }
  return true;
}
</script>

<template>
  <n-upload
    ref="textUpload"
    v-bind="$attrs"
    :custom-request="customRequest"
    abstract
    :file-list-style="{marginTop:0}"
    :list-type="listTypeRef"
    :accept="acceptRef"
    :disabled="props.disabled"
    v-model:file-list="fileList"
    @remove="fileRemoveHandler"
    @before-upload="beforeUpload"
  >
    <n-button-group>
      <n-upload-trigger #="{ handleClick }" abstract>
        <n-button ref="btn" v-show="fileList.length<1" text size="medium" type="primary"  @click="handleClick">
          {{ props.title }}
        </n-button>
      </n-upload-trigger>
    </n-button-group>
  </n-upload>
  <n-button v-if="fileList.length>=1" text size="medium" @click="Download" tag="span" type="primary">
    {{ finishStr?fileStr(fileName):'上传中...' }}
  </n-button>
  <n-button v-if="fileList.length>=1 && finishStr" style="margin-left: 10px" text size="medium" type="error"  @click="AddClick">
    更改
  </n-button>
</template>
