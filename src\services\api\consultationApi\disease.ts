import {defHttp} from '@/services';

/** 病种管理 */
export const enum DiseaseApi {
    /** 病种列表 */
    diseaseList = '/diseaseManagements/page',
    /** 新增病种 */
    addDisease = '/diseaseManagements/add',
    /** 编辑病种 */
    editDisease = '/diseaseManagements/update',
    /** 删除病种 */
    deleteDisease = '/diseaseManagements/delete',
}

/** 病种列表 */
export function diseaseList(params) {
    return defHttp.post({
        url: DiseaseApi.diseaseList,
        params
    })
}

/** 新增病种 */
export function addDisease(params) {
    return defHttp.post({
        url: DiseaseApi.addDisease,
        params
    })
}

/** 编辑病种 */
export function editDisease(params) {
    return defHttp.put({
        url: DiseaseApi.editDisease,
        params
    })
}

/** 删除病种 */
export function deleteDisease(params) {
    return defHttp.delete({
        url: DiseaseApi.deleteDisease,
        requestConfig:{
            isQueryParams:true
        },
        params
    })
}

