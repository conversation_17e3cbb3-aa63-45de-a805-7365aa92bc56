<template>
  <JDrawer
    v-model:show="drawerVisible"
    title="详情"
    :isGetLoading="isGetLoading"
    :enableFooterWrapper="false"
    @after-leave="closeDrawer"
    to="#ShopReturnLogisticsTab"
    :contents-list="[
            {
                name: '退货单详情',
                slotName: 'returnOrder'
            }, {
                name: '退货清单',
                slotName: 'returnList'
            },
            {
                name: '平台审核',
                slotName: 'review'
            },
        ]"
  >
    <template #returnOrder>
      <div class="return-order-table">
        <n-table :bordered="true" :single-line="false">
          <tbody>
            <tr>
              <td class="label">门店名称/ID</td>
              <td class="value">
                {{ detailsInfo.storeName }}
                <span class="space" v-if="detailsInfo.storeID">ID{{ detailsInfo.storeID }}</span>
              </td>
              <td class="label">店长姓名/电话</td>
              <td class="value">
                {{ detailsInfo.contactName }}
                <span class="space" v-if="detailsInfo.contactPhone">{{ detailsInfo.contactPhone }}</span>
              </td>
            </tr>
            <tr>
              <td class="label">物流公司</td>
              <td class="value">{{ detailsInfo.logisticsCompany }}</td>
              <td class="label">物流单号</td>
              <td class="value">{{ detailsInfo.trackingNumber }}</td>
            </tr>
            <tr>
              <td class="label">物流费用（元）</td>
              <td class="value">{{ toYuanString(detailsInfo?.logisticsFee) }}</td>
              <td class="label">其它费用类型</td>
              <td class="value">{{ detailsInfo.otherFeeType }}</td>
            </tr>
            <tr>
              <td class="label">其它费用（元）</td>
              <td class="value">{{ toYuanString(detailsInfo?.otherFee) }}</td>
              <td class="label">总申请费用（元）</td>
              <td class="value">{{ toYuanString(detailsInfo?.totalRequestedFee) }}</td>
            </tr>
            <tr>
              <td class="label">备注</td>
              <td class="value">{{ detailsInfo.remark }}</td>
              <td class="label">凭证</td>
              <td class="value">
                <n-flex>
                  <template v-for="item in detailsInfo?.imgDTOList" :key="item.id">
                    <JImage width="20" height="20" :imgPath="item.path" />
                  </template>
                </n-flex>
              </td>
            </tr>
          </tbody>
        </n-table>
      </div>
    </template>
    <template #returnList>
      <div class="return-list-table">
        <n-data-table
          :columns="columns"
          :data="detailsInfo.returnLogisticsOrderItemDTOList"
          :row-key="row => row.id"
          :show-header="false"
          :single-line="false"
          size="small"
          style="background-color: #fff;"
        ></n-data-table>
      </div>
    </template>
    <template #review>
      <n-flex vertical style="margin-bottom: 20px">
        <template v-for="item in detailsInfo.stepInstDTOList" :key="item.id">
          <n-flex>
            <span class="title">{{ item.stepName }}</span>
            <!--审核不通过/审核通过-->
            <template v-if="item.status==StepInstStatusEnum.NotPassed || item.status==StepInstStatusEnum.Completed">
              <span class="staff">{{ item.auditorName }}</span>
              <span class="status">
                {{ getStatusLabel(item.status) }}
                <span v-if="item.opinion">(原因：{{ item.opinion }})</span>
              </span>
              <span class="time">{{ item.operateTime }}</span>
            </template>
            <template v-else-if="item?.candidateList?.length>0">
              <span class="staff" v-for="candidateItem in item.candidateList" :key="candidateItem.id">
                {{ candidateItem.auditorName }}
              </span>
              <!--              是否显示待审状态-->
              <span
                class="status"
                v-if="isShowPendingLabel(item)"
                :class="item.status===StepInstStatusEnum.WaitingForReview?'statusWaitingForReview':''"
              >
                {{ getStatusLabel(item.status) }}
              </span>
            </template>
          </n-flex>
        </template>
      </n-flex>
      <div class="review-main" v-if="isShowReviewInput && currentUserHasApprovePermission">
        <n-input v-model:value="model.reason" placeholder="请输入审核不通过的原因，必填" style="width: 100%" />
        <n-flex justify="end" style="margin-top: 20px;">
          <n-button type="warning" @click="_confirm(StepInstStatusEnum.NotPassed)" :disabled="isReviewLoading">
            审核不通过
          </n-button>
          <n-button type="info" @click="_confirm(StepInstStatusEnum.Completed)" :disabled="isReviewLoading">
            审核通过
          </n-button>
        </n-flex>
      </div>
    </template>
  </JDrawer>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from "vue";
import { useMessages } from "@/hooks";
import { getShopReturnLogisticsDetail, approveShopReturnLogistics } from "@/services/api";
import useShopReturnLogistics from "../hooks/useShopReturnLogistics";

const { toYuanString } = useShopReturnLogistics();
const { createMessageSuccess, createMessageError } = useMessages();
import { deepClone } from "@/utils";
import { ShopReturnLogisticsEnum } from "@/enums";

const isReviewLoading = ref<boolean>(false);
const initParams = {
  reason: null,
  id: null,
};
const model = ref(deepClone(initParams));
type ImgDTOItem = {
  id: string
  path: string
}
type ReturnLogisticsOrderItem = {
  id: string,
  productFrontName: string
  count: number
  spuCode: string
}
type CandidateItem = {
  id: string
  auditorId: string
  auditorName: string
}
type StepInstDTOItem = {
  id: string
  stepName: string
  instId: string
  status: string
  auditorId: string
  auditorName: string
  opinion: string
  operateTime: string
  candidateList: CandidateItem[]
}
type DetailsInfoType = {
  id: string
  processInstId: string  //流程实例ID
  hasApprovePermission: boolean
  storeName: string
  storeID: string
  contactName: string
  contactPhone: string
  logisticsCompany: string
  trackingNumber: string
  logisticsFee: number
  otherFee: number
  otherFeeType: string
  totalRequestedFee: number
  remark: string
  imgDTOList: ImgDTOItem[]
  returnLogisticsOrderItemDTOList: ReturnLogisticsOrderItem[]
  stepInstDTOList: StepInstDTOItem[]
}

// 审核环节的状态
const enum StepInstStatusEnum {
  //待审核
  WaitingForReview = "pending",
  //审核通过
  Completed = "approved",
  //审核不通过
  NotPassed = "rejected"
}

const detailsInfo = ref({
  hasApprovePermission: false,
  imgDTOList: [],
  returnLogisticsOrderItemDTOList: [],
  stepInstDTOList: [],
} as DetailsInfoType);

const getStatusLabel = (status: StepInstStatusEnum) => {
  switch (status) {
    case StepInstStatusEnum.Completed:
      return "审核通过";
    case StepInstStatusEnum.NotPassed:
      return "审核不通过";
    default:
      return "待审核";
  }
};

/** 查找状态为待审核的，第一个元素 */
const getFirstPendingParams = () => {
  const stepInstDTOList = detailsInfo.value.stepInstDTOList ?? [];
  return stepInstDTOList.find(item => item.status === StepInstStatusEnum.WaitingForReview);
};

/** 是否存在审核不通过 */
const hasNotPassed = () => {
  const stepInstDTOList = detailsInfo.value.stepInstDTOList ?? [];
  return stepInstDTOList.some(item => item.status === StepInstStatusEnum.NotPassed);
}


/** 判断是否显示待审核标识
 * 多个待审核，只显示一个
 * 存在审核不通过的也不显示待审标识
 * */
const isShowPendingLabel = (item: StepInstDTOItem) => {
  const currentNeedReviewItem = getFirstPendingParams();

  if (!currentNeedReviewItem || hasNotPassed()) {
    return null;
  }
  return item.id === currentNeedReviewItem.id;
};

/** 抽屉状态 */
const drawerVisible = ref(false);
const drawerProps = reactive(
  {
    row: {},
    tabType: null,
    refresh: null,
  },
);

//待审核才需要显示审核框
const isShowReviewInput = computed(() => {
  return drawerProps.tabType === ShopReturnLogisticsEnum.WaitingForReview || drawerProps.tabType === ShopReturnLogisticsEnum.InProgress;
});

//判断当前登录账号是否有权限审核
const currentUserHasApprovePermission = computed(() => {
  return detailsInfo.value.hasApprovePermission;
});

const acceptParams = async (param) => {
  drawerVisible.value = true;
  drawerProps.row = param.row;
  drawerProps.refresh = param.refresh;
  drawerProps.tabType = param.tabType;
  getShopReturnLogisticsDetailData(param?.row?.id);
};

const getShopReturnLogisticsDetailData = (id) => {
  // detailsInfo.value = {
  //   "id": 0,
  //   "createTime": "",
  //   "updateTime": "",
  //   "processInstId": 0,
  //   "storeName": "",
  //   "storeId": 0,
  //   "storeShortId": 0,
  //   "contactName": "",
  //   "contactPhone": "",
  //   "logisticsCompany": "",
  //   "trackingNumber": "",
  //   "logisticsFee": 0,
  //   "otherFeeType": "",
  //   "otherFee": 0,
  //   "totalRequestedFee": 0,
  //   "requestedStatus": "",
  //   "remark": "我是备注",
  //   "hasApprovePermission": true,
  //   "imgDTOList": [
  //     {
  //       "id": 1,
  //       "createTime": "",
  //       "updateTime": "",
  //       "recordNo": 0,
  //       "img": "",
  //       "path": "https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg",
  //     },
  //     {
  //       "id": 2,
  //       "createTime": "",
  //       "updateTime": "",
  //       "recordNo": 0,
  //       "img": "",
  //       "path": "https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg",
  //     },
  //   ],
  //   "returnLogisticsOrderItemDTOList": [
  //     {
  //       "id": 1,
  //       "createTime": "",
  //       "updateTime": "",
  //       "productFrontName": "抽风机",
  //       "count": 20,
  //       "spuCode": "abddesee",
  //     },
  //   ],
  //   "stepInstDTOList": [
  //     {
  //       "id": 1,
  //       "createTime": "",
  //       "updateTime": "",
  //       "instId": 1,
  //       "stepDefId": 0,
  //       "stepName":"一审",
  //       "sort": 0,
  //       "auditorId": 0,
  //       "auditorName": "我是不给过的人",
  //       "status": "approved",
  //       "opinion": "就是不给过",
  //       "operateTime": "2026-03-12 12:03:02",
  //       "candidateList": [
  //         {
  //           "id": 1,
  //           "createTime": "",
  //           "updateTime": "",
  //           "auditorId": 0,
  //           "auditorName": "张三",
  //         },
  //         {
  //           "id": 2,
  //           "createTime": "",
  //           "updateTime": "",
  //           "auditorId": 0,
  //           "auditorName": "李四",
  //         },
  //       ],
  //     },
  //     {
  //       "id": 2,
  //       "createTime": "",
  //       "updateTime": "",
  //       "instId": 2,
  //       "stepDefId": 0,
  //       "sort": 0,
  //       "stepName":"二审",
  //       "auditorId": 0,
  //       "auditorName": "222",
  //       "status": "pending",
  //       "opinion": "就是不给过",
  //       "operateTime": "2026-03-12 12:03:02",
  //       "candidateList": [
  //         {
  //           "id": 1,
  //           "createTime": "",
  //           "updateTime": "",
  //           "auditorId": 0,
  //           "auditorName": "张三22",
  //         },
  //         {
  //           "id": 2,
  //           "createTime": "",
  //           "updateTime": "",
  //           "auditorId": 0,
  //           "auditorName": "李四222",
  //         },
  //       ],
  //     },
  //     {
  //       "id": 3,
  //       "createTime": "",
  //       "updateTime": "",
  //       "instId": 3,
  //       "stepDefId": 0,
  //       "sort": 0,
  //       "stepName":"三审",
  //       "auditorId": 0,
  //       "auditorName": "222",
  //       "status": "pending",
  //       "opinion": "就是不给过",
  //       "operateTime": "2026-03-12 12:03:02",
  //       "candidateList": [
  //         {
  //           "id": 1,
  //           "createTime": "",
  //           "updateTime": "",
  //           "auditorId": 0,
  //           "auditorName": "张三3",
  //         },
  //         {
  //           "id": 2,
  //           "createTime": "",
  //           "updateTime": "",
  //           "auditorId": 0,
  //           "auditorName": "李四3",
  //         },
  //       ],
  //     },
  //   ],
  // };
  getShopReturnLogisticsDetail(id).then(res => {
    detailsInfo.value = res ?? {};
  }).catch(err => {
    createMessageError(`获取退货物流信息失败：${err}`);
  });
};

/** 审核 */
const _confirm = (status) => {
  let pendingParams = getFirstPendingParams();
  if (!pendingParams) {
    createMessageError("流程实例环节参数");
  }
  if (status === ShopReturnLogisticsEnum.NotPassed && !model.value.reason) {
    return createMessageError(`审核不通过的原因为必填`);
  }
  const params = { opinion: model.value.reason, status, id: pendingParams.id, instId: pendingParams.instId };
  isReviewLoading.value = true;
  approveShopReturnLogistics(params).then(res => {
    createMessageSuccess("审核状修改成功");
    //刷新表格
    drawerProps.refresh();
    closeDrawer();
  }).catch(err => {
    createMessageError(`审核状态改变失败：${err}`);
  }).finally(() => {
    isReviewLoading.value = false;
  });
};
/** 关闭抽屉 */
const closeDrawer = () => {
  model.value = deepClone(initParams);
  drawerVisible.value = false;
};
/** 表格数据 */
const tableData = ref([]);
/** 表单项 */
const columns = [
  {
    title: "序号",
    key: "",
    align: "center",
    render: (renderData: object, index: number) => {
      return `${index + 1}`;
    },
  },
  {
    title: "商品名",
    key: "",
    align: "left",
    render: () => {
      return "商品名";
    },
  },
  {
    title: "商品值",
    key: "productFrontName",
    align: "left",
  },
  {
    title: "退货件数名",
    key: "",
    align: "left",
    render: () => {
      return "退货件数";
    },
  },
  {
    title: "退货件数",
    key: "count",
    align: "left",
  },
  {
    title: "商品自定义SPU ID",
    key: "",
    align: "left",
    render: () => {
      return "商品自定义SPU ID";
    },
  },
  {
    title: "spuID",
    key: "spuCode",
    align: "left",
  },
];
defineExpose({
  acceptParams,
});
</script>
<style lang="less" scoped>
@import "@/styles/default.less";

.statusWaitingForReview {
  color: red;
}

.space {
  padding-left: 10px;
}

.return-order-table {
  padding-bottom: 20px;

  .label {
    font-weight: bolder;
    width: 25%;
  }

  .value {
    width: 25%;
  }
}

.return-list-table {
  padding-bottom: 20px;
}

.return-list-table :deep(.n-data-table-thead) {
  display: none !important;
}
</style>
