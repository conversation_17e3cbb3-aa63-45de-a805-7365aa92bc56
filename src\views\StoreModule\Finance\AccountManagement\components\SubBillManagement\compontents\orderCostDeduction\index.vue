<template>
  <JDrawer
    v-model:show="drawerVisible"
    title="订单成本扣除"
    :isShowFooter="false"
    to="#subBillManagement"
    @after-leave="afterLeave"
    :contents-list="[
            {
                name: '订单成本扣除',
                slotName: 'title'
            },
            {
                name: '',
                slotName: 'content'
            }
        ]"
  >
    <template #title>
      <h1>
        使用本功能，可以将订单线下核算的屡单成本（譬如包邮单实际产生的快递运费）先扣除再计算分账金额。此部分成本需在分账单结算的前一天上传，结算当天及后续上传无效。
      </h1>
    </template>

    <template #content>
      <n-space justify="center">
        <div style="text-align: center;padding-top: 40px;height: 100%;">
          <p>{{ costDeductionText }}</p>
          <uploadAndDownload
            v-if="importCostPriceStatus === ImportCostPriceStatus.NOT_IMPORTED"
            @importedData="importedData"
            :isLoadingShow="isLoadingShow"
          />
          <n-space
            justify="center"
            style="margin-top: 10px;"
            v-if="importCostPriceStatus == ImportCostPriceStatus.UPLOAD_SUCCESS || importCostPriceStatus == ImportCostPriceStatus.IMPORTED"
          >
            <n-button type="primary" ghost @click="leftButtonClick" :disabled="confirmLoading">
              {{ importCostPriceStatus === ImportCostPriceStatus.UPLOAD_SUCCESS ? '取 消' : '完成' }}
            </n-button>
            <n-button type="primary" secondary @click="rightButtonClick" :loading="confirmLoading">
              {{ importCostPriceStatus === ImportCostPriceStatus.UPLOAD_SUCCESS ? '确定' : '继续上传' }}
            </n-button>
          </n-space>
          <TabsLayout
            height="calc(100vh - 400px)"
            v-if="importCostPriceStatus == ImportCostPriceStatus.IMPORTED"
            paneClass="deliveryExceptionTab"
            v-model:value="tabNameRef"
            :tabsData="(tabsData as any)"
            :onlyTabs="true"
            class="tabsLayout"
          >
            <costDeductionTab ref="tableDataUpdate" :tabNameRef="tabNameRef" :tabularData="tableData" :fileKey="unprocessedListKey" />
          </TabsLayout>
        </div>
      </n-space>
    </template>
  </JDrawer>
</template>

<script setup lang="tsx">
import { computed, reactive, ref, watch } from 'vue';
import { ImportCostPriceStatus, OrderCostDeductionStatus } from './type';
import uploadAndDownload from './components/uploadAndDownload.vue';
import TabsLayout from "@/layout/TabsLayout.vue";
import costDeductionTab from './components/costDeductionTab.vue';
import { costPriceImport, costPriceConfirm, costPriceExport } from '@/services/api';
import { useMessages } from "@/hooks";
const { createMessageSuccess, createMessageError } = useMessages();
const props = withDefaults(defineProps<{
    show: boolean
}>(), {
    show: false
});

/** 导入成本价格状态 */
const importCostPriceStatus = ref<ImportCostPriceStatus>(ImportCostPriceStatus.NOT_IMPORTED);
/** 导入文件地址（minio地址） */
const importFileUrl = ref('');
/** 导入数量数据 */
const importCostCountReactive = reactive({
    /** 导入数量 */
    importCount: 0,
    /** 成功数量 */
    successCount: 0,
    /** 失败数量 */
    failedCount: 0,
    /** 总数量 */
    successAllocationCount: 0,
    /** 未处理数量 */
    unprocessedCount: 0
});
/** 成本扣除文本 */
const costDeductionText = computed(() => {
    switch (importCostPriceStatus.value) {
        case ImportCostPriceStatus.NOT_IMPORTED:
            return '单次最多可传5000张订单'
        case ImportCostPriceStatus.IMPORTING:
            return '数据导入中，请不要刷新当前页面 ....'
        case ImportCostPriceStatus.UPLOAD_SUCCESS:
            return `已导入${importCostCountReactive.importCount}张订单，确定上传后不可撤销`
        case ImportCostPriceStatus.IMPORTED:
            return `处理成功${importCostCountReactive.successCount}个订单，处理失败${importCostCountReactive.failedCount}个订单。共更新${importCostCountReactive.successAllocationCount}个分账单分账金额，${importCostCountReactive.unprocessedCount}个分账单未处理`
        default:
            return ''
    }
});

/** 导入数据 */
const isLoadingShow = ref(false);
/** 导入数据 */
const importedData = (data: FormData) => {
    isLoadingShow.value = true;
    importCostPriceStatus.value = ImportCostPriceStatus.IMPORTING;
    costPriceImport(data).then((res) => {
        importCostCountReactive.importCount = res.totalCount;
        importFileUrl.value = res.fileUrl;
        importCostPriceStatus.value = ImportCostPriceStatus.UPLOAD_SUCCESS;
    }).catch((err) => {
        createMessageError(`导入失败:${err}`);
        importCostPriceStatus.value = ImportCostPriceStatus.NOT_IMPORTED;
    }).finally(() => {
        isLoadingShow.value = false;
    })
}

/** 取消按钮点击 */
const leftButtonClick = () => {
    if(importCostPriceStatus.value == ImportCostPriceStatus.UPLOAD_SUCCESS) {
        importCostPriceStatus.value = ImportCostPriceStatus.NOT_IMPORTED;
    } else {
        drawerVisible.value = false;
    }
}

/** 处理失败订单 */
const failedList = ref([]);
/** 未处理分账单 */
const unprocessedAllocationList = ref([]);
/** 确定loading */
const confirmLoading = ref(false);
/** 未处理分账单key */
const unprocessedListKey = ref(''); 
/** 确定导入 */
const rightButtonClick = () => {
    if(importCostPriceStatus.value == ImportCostPriceStatus.UPLOAD_SUCCESS) {
        confirmLoading.value = true;
        costPriceConfirm({
            fileUrl: importFileUrl.value,
        }).then((res) => {
            console.log(res,'确认成功')
            Object.assign(importCostCountReactive, res);
            failedList.value = res.failedList;
            unprocessedAllocationList.value = res.unprocessedAllocationList;
            importCostPriceStatus.value = ImportCostPriceStatus.IMPORTED;
            unprocessedListKey.value = res.unprocessedListKey;
        }).catch((err) => {
            createMessageError(`确认失败:${err}`);
        }).finally(() => {
            confirmLoading.value = false;
        })
    } else {
        failedList.value = [];
        unprocessedAllocationList.value = [];
        importCostPriceStatus.value = ImportCostPriceStatus.NOT_IMPORTED;
    }
}

const emits = defineEmits(['update:show','closeDrawer']);

const tabNameRef = ref<OrderCostDeductionStatus>(OrderCostDeductionStatus.FAILED);

const tableData = computed(() => {
    return tabNameRef.value == OrderCostDeductionStatus.FAILED ? failedList.value : unprocessedAllocationList.value;
});

/** 抽屉关闭回调 */
const afterLeave = () => {
    // 还原所有状态
    failedList.value = [];
    unprocessedAllocationList.value = [];
    const initParams = {
        importCount: 0,
        successCount: 0,
        failedCount: 0,
        successAllocationCount: 0,
        unprocessedCount: 0
    }
    Object.assign(importCostCountReactive, initParams);
    importFileUrl.value = '';
    tabNameRef.value = OrderCostDeductionStatus.FAILED;
    importCostPriceStatus.value = ImportCostPriceStatus.NOT_IMPORTED;
    emits('closeDrawer');
}

const tabsData = ref([
  {
    label: "处理失败订单",
    key: OrderCostDeductionStatus.FAILED,
  },
  {
    label: "未处理分账单",
    key: OrderCostDeductionStatus.NOT_PROCESSED,
  },
]);


const drawerVisible = computed({
    get() {
        return props.show
    },
    set(value) {
        emits('update:show', value)
    }
})
</script>

<style scoped lang="less">
:deep(.deliveryExceptionTab){
    height: 100%;
}
</style>
