import type { AxiosInstance, AxiosResponse, AxiosError } from "axios";
import axios from "axios";
import { AxiosCanceler } from "./axiosCancel.js";
import { defaultInterceptors } from "./defaultInterceptors.js";
import { stringify } from "qs";

import type { RequestProps, AxiosOptions, RequestOptions, TAxiosConfig, TAxiosInterceptors } from "./type";
import { HttpRequestTypeEnum } from "@/enums/http.js";
import { axiosConfigTransform } from "./transform";
import { deepmerge } from "deepmerge-ts";

export class TAxios {
  private axiosInstance: AxiosInstance;
  private isLoadInterceptors: boolean;
  private options: object;
  //Axios构造函数
  constructor(options: TAxiosConfig) {
    this.isLoadInterceptors = false;
    if (!TAxios.instance) {
      this.options = options;
      this.axiosInstance = axios.create(options);
      //单例模式
      TAxios.instance = this;
      //初始化axios canceler
      this.axiosCanceler = new AxiosCanceler();
    }
    return TAxios.instance;
  }
  //设置baseUrl
  setBaseURL(url) {
    if (!url) throw new Error("请传入非空参数");
    this.axiosInstance.defaults.baseURL = url;
  }

  //注册axios canceler拦截器
  initAxiosCanceler() {
    const reqCanceler = {
      onFulfilled: config => {
        const { ignoreCancelToken } = config;
        !ignoreCancelToken && this.axiosCanceler.addPending(config);
        return config;
      },
    };
    const resCanceler = {
      onFulfilled: response => {
        response && this.axiosCanceler.clearPending(response.config);
        return response;
      },
      onRejected: error => {
        !axios.isCancel(error) && this.axiosCanceler.clearPending(error.config);
        return Promise.reject(error);
      },
    };
    return { reqCanceler, resCanceler };
  }

  //注册axios拦截器
  loadInterceptors(interceptors: TAxiosInterceptors, options) {
    if (this.isLoadInterceptors) {
      return;
    }
    const { request = [], response = [] } = interceptors;
    const { request: defaultRequest = [], response: defaultResponse = [] } = defaultInterceptors;
    const { reqCanceler, resCanceler } = this.initAxiosCanceler();
    // 加载请求拦截器
    // request.push(reqCanceler);
    const requestInterceptorsList = [...defaultRequest, ...request, reqCanceler];
    requestInterceptorsList?.forEach(item => {
      let { onFulfilled, onRejected } = item;
      if (!onFulfilled || typeof onFulfilled !== "function") {
        onFulfilled = config => config;
      }
      if (!onRejected || typeof onRejected !== "function") {
        onRejected = error => Promise.reject(error);
      }
      this.axiosInstance.interceptors.request.use(
        config => onFulfilled(config, options, this.axiosInstance),
        error => onRejected(error, options, this.axiosInstance),
      );
    });

    // 加载响应拦截器
    const responseInterceptorsList = [resCanceler, ...defaultResponse, ...response];
    // response.unshift(resCanceler);
    responseInterceptorsList.forEach(item => {
      let { onFulfilled, onRejected } = item;
      if (!onFulfilled || typeof onFulfilled !== "function") {
        onFulfilled = resp => resp;
      }
      if (!onRejected || typeof onRejected !== "function") {
        onRejected = error => Promise.reject(error);
      }
      this.axiosInstance.interceptors.response.use(
        resp => onFulfilled(resp, options, this.axiosInstance),
        error => onRejected(error, options, this.axiosInstance),
      );
    });
    this.isLoadInterceptors = true;
  }

  //Axios请求事件
  private request<T = any>(axiosOptions: AxiosOptions, requestOptions: RequestOptions): Promise<T> {
    let config: TAxiosConfig = deepmerge({}, axiosOptions);
    config.requestOptions = requestOptions;
    return new Promise((resolve, reject) => {
      this.axiosInstance
        .request(config)
        .then(res => {
          resolve(res as unknown as Promise<T>);
        })
        .catch(err => {
          if (!axios.isCancel(err)) {
            reject(err);
          }
        });
    });
  }

  clearAllRequest() {
    this.axiosCanceler.removeAllPending();
  }

  get<T = any>({ url, params, options, requestConfig }: RequestProps) {
    return this.request<T>(
      {
        url: url,
        method: HttpRequestTypeEnum.GET,
        params: params,
        ...options,
      },
      requestConfig,
    );
  }

  post<T = any>({ url, params, options, requestConfig }: RequestProps) {
    const paramsObj = {
      url: url,
      method: HttpRequestTypeEnum.POST,
      ...options,
    };
    if (requestConfig?.isQueryParams) {
      paramsObj["params"] = params;
    } else {
      paramsObj["data"] = params;
    }
    return this.request<T>(paramsObj, requestConfig);
  }

  put<T = any>({ url, params, options, requestConfig }: RequestProps) {
    return this.request<T>(
      {
        url: url,
        method: HttpRequestTypeEnum.PUT,
        data: params,
        ...options,
      },
      requestConfig,
    );
  }

  delete<T = any>({ url, params, options, requestConfig }: RequestProps) {
    const paramsObj = {
      url: url,
      method: HttpRequestTypeEnum.DELETE,
      ...options,
    };
    if (requestConfig.isQueryParams) {
      paramsObj["params"] = params;
    } else {
      paramsObj["data"] = params;
    }
    return this.request<T>(paramsObj, requestConfig);
  }
}
