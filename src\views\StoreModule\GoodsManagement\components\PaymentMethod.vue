<template>
    <JModal
      :show="props.show"
      title="付款方式设置"
      width="620"
      @after-leave="closeModal"
      @close="emits('update:show', false)"
		  @positive-click="_save"
      @negative-click="emits('update:show', false)"
      :isScale="false"
      isConfirm
    >
      <n-card
        content-style="padding: 12px"
        size="small"
        :bordered="false"
        aria-modal="true"
      >
        <n-form
            ref="formRef"
            :model="model"
            :rules="rules"
            label-placement="left"
            label-width="auto"
            require-mark-placement="right-hanging"
            :style="{ width: '100%' }"
        >
          <!-- 支持物流代收 -->
          <n-form-item :show-label="false" :show-feedback="false">
            <div style="display: flex;flex-direction: column;">
              <JCheckbox  v-model:checked="model.isCashOnDelivery">支持物流代收</JCheckbox>
              <span style="margin-bottom: 12px;">勾选后，用户购买此商品，在付款界面可以选择物流代收，待物流送货上门后再支付订单费用（包含邮费）。</span>
            </div>
          </n-form-item>
          <!-- 支持定金支付 -->
          <n-form-item :show-label="false" :show-feedback="false">
            <div style="display: flex;flex-direction: column;">
              <JCheckbox  v-model:checked="model.isDownPayment">支持定金支付</JCheckbox>
              <span style="margin-bottom: 12px;">勾选后，用户购买此商品，可以预付定金和邮费，余额部分待物流送货上门后再支付。</span>
            </div>
          </n-form-item>
          <!-- 定金金额 -->
          <n-form-item label="定金金额(元)：" :path="model.isDownPayment ? 'downPayment' : ''">
            <n-input-number
                size="small"
                :precision="2"
                :min="0.01"
                :max="props.price > 100000 ? 100000 : props.price"
                v-model:value="model.downPayment"
                placeholder="请输入定金金额（元）"
                :show-button="false"
            />
          </n-form-item>
        </n-form>
      </n-card>
    </JModal>
  </template>
  
  <script lang="ts" setup name="PaymentMethod">
  import { ref, watch } from "vue";
  import type { FormRules } from 'naive-ui';
  /** 相关组件 */
  import JModal from "@/components/JModal/index.vue";
  
  interface ModalProps {
    show: boolean;
    price: number; // 售价
    value: {
      id: string; 
      isDownPayment: 0 | 1; // 是否支持定金支付。0=否；1=是
      isCashOnDelivery: 0 | 1; // 是否支持物流代收。0=否；1=是
      downPayment: number; // 定金单价，单位分
    }
  }
  
  /** props */
  const props = defineProps<ModalProps>();
  
  /** emits */
  const emits = defineEmits<{
    (e: "update:show", value: boolean): void;
    (e: "update:value",value: {
      id: string; 
      isDownPayment: 0 | 1;
      isCashOnDelivery: 0 | 1;
      downPayment: number;
    }): void;
    (e: "saveSuccessfully",value: {
      id: string; 
      isDownPayment: 0 | 1;
      isCashOnDelivery: 0 | 1;
      downPayment: number;
    }): void;
  }>();
  
  /* 表单参数初始化 */
  const initParams: {
    id: string;
    isDownPayment: boolean; // 是否支持定金支付
    isCashOnDelivery: boolean; // 是否支持物流代收
    downPayment: number; // 定金单价，单位分
  } = {
    id: null,
    isDownPayment: false,
    isCashOnDelivery: false,
    downPayment: null,
  };
  
  const model = ref({ ...initParams });
  
  /* 清空表单 */
  const formDataReset = () => {
    model.value = { ...initParams };
  };
  
  /* 关闭弹窗之后 */
  const closeModal = () => {
    formDataReset();
  };
  
  /** 表单实例 */
  const formRef = ref();
  
  /* 表单规则 */
  const rules: FormRules = {
    downPayment: {
      type: 'number',
      required: true,
      trigger: ['blur', 'change'],
      message: '请输入定金金额（元）',
    },
  };
  
  /** 保存 */
  const _save = async e => {
    e.preventDefault();
    formRef.value?.validate(async (errors: any) => {
      if (!errors) {
        try {
         emits('update:value', {
          id: model.value.id,
          isDownPayment: model.value.isDownPayment ? 1 : 0,
          isCashOnDelivery: model.value.isCashOnDelivery ? 1 : 0,
          downPayment: model.value.downPayment * 100, // 转分
         });
         emits('saveSuccessfully', {
          id: model.value.id,
          isDownPayment: model.value.isDownPayment ? 1 : 0,
          isCashOnDelivery: model.value.isCashOnDelivery ? 1 : 0,
          downPayment: model.value.downPayment * 100, // 转分
         });
         emits('update:show', false);
        } catch (error) {
          console.log("保存付款方式失败：" + error);
        } 
      }
    });
  };

  /** 监听 */
  watch(() => props.show, (newVal) => {
    if (newVal) {
      model.value.id = props.value.id ?? null;
      model.value.downPayment = props.value.downPayment ? (props.value.downPayment / 100) : null;
      model.value.isDownPayment = props.value.isDownPayment == 1 ? true : false;
      model.value.isCashOnDelivery = props.value.isCashOnDelivery == 1 ? true : false;
    }
  }, { immediate: true });
  </script>
  
  <style lang="less" scoped></style>
  