/** 通用类型 */
declare namespace Common {
	/**
	 * 策略模式
	 * [状态, 为true时执行的回调函数]
	 */
	type StrategyAction = [boolean, () => void];

	/** 选项数据 */
	type OptionWithKey<K> = { value: K; label: string };
}

export function transformObjectToOption<T extends object>(obj: T, isTransformNumber: boolean = false){
  return Object.entries(obj).map(([value, label]) => ({
    value: isTransformNumber ? Number(value) : value,
    label
  })) as Common.OptionWithKey<keyof T>[];
}
