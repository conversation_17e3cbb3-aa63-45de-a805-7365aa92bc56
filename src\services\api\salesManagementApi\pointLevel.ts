import { defHttp } from "@/services";

/** 会员等级配置 */
export const enum pointLevelApi {
  add = "/pointLevel/batchSave",
  get = "/pointLevel/list",
  delete = "/pointLevel/delete",
  saveLevel ="/pointLevel/saveLevel"
}

/**
 * @description 会员等级列表
 */
export function getPointLevel(params) {
    return defHttp.post({
        url: pointLevelApi.get,
        params,
    });
}

/**
 * @description 新增会员等级规则
 */
export function addPointLevel(_params) {
    return defHttp.post({
        url: pointLevelApi.saveLevel,
        params: {
            data: _params,
        },
    });
}

/**
 * @description 删除会员等级
 */
export function deletePointLevelById(id: string) {
    return defHttp.post({
        url: pointLevelApi.delete + `?id=${id}`
    });
}
