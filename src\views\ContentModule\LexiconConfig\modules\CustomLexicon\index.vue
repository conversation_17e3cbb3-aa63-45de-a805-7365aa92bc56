<template>
  <div class="wrapper">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :isNeedCollapse="false"
      :pagination="paginationRef"
      @paginationChange="paginationChange"
      @selectedKeysChange="selectedKeysChange"
    >
      <template #searchForm>
        <n-form
          :model="model"
          :show-feedback="false"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          size="small"
          :style="{ width: '100%' }"
        >
          <n-form-item label="敏感词">
            <n-input @clear="handleClear" clearable @keydown.enter="handleSearch" size="small" v-model:value="searchTagVal" type="text" placeholder="请输入需要查找的标签" >
              <template #suffix>
                <n-icon style="cursor: pointer;" @click="handleSearch" :component="SearchOutline" />
              </template>
            </n-input>
          </n-form-item>
        </n-form>
      </template>
      <!-- 操作项 -->
      <template #tableHeaderBtn>
        <JAddButton v-if="hasLexiconConfigIndexAdd" @click="addSensitiveTag" type="primary">添加敏感词</JAddButton>
        <span style="min-width: 60px;" type="info">{{ paginationRef.total }}/500</span>
      </template>
      <!-- 表格底部按钮 -->
      <template #tableFooterBtn="scope">
        <!-- 批量删除 -->
        <n-popconfirm
          v-if="hasLexiconConfigIndexDelete"
          @positive-click="handleDelete(scope.selectedList)"
          :positive-button-props="{
          loading: isDeleteLoading
        }"
        >
          <template #trigger>
            <n-button ghost type="error" size="small">批量删除</n-button>
          </template>
          此操作将删除选中的商品，是否继续？
        </n-popconfirm>
      </template>
    </FormLayout>
    <!-- 新建轮播图 -->
    <AddLexiconModel ref="addLexiconModel" />
  </div>
</template>

<script setup lang="tsx" name="CoustomLexicon">
import { onMounted, ref } from "vue";
import { useLoading, useMessages } from "@/hooks";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import {
  sensitiveWordPage,
  sensitiveWordBatchDelete
} from "@/services/api/contentConfigApi";
import { SearchOutline } from "@vicons/ionicons5";
import AddLexiconModel from "@/views/ContentModule/LexiconConfig/components/AddLexiconModel.vue";
import { hasLexiconConfigIndexAdd, hasLexiconConfigIndexDelete } from "@/views/ContentModule/LexiconConfig/authList";
const { createMessageSuccess, createMessageError } = useMessages();
/* 表格方法Hook */
const {
  isLoading,
  tableData,
  paginationRef,
  paginationChange,
  pageTableData,
  refreshTableData,
  sortTableData,
} = useTableDefault({
  pageDataRequest: sensitiveWordPage,
});
const searchTagVal = ref(null)
const addLexiconModel = ref(null)
const { loading: isDeleteLoading, startLoading, endLoading } = useLoading();
const addSensitiveTag = () => {
  addLexiconModel.value.acceptParams({refreshTable:tableSearch})
}

/* 表格项 */
const tableColumns = [
  {
    title: "",
    width: 20,
    fixed: "left",
    type: "selection",
    align: "center",
  },
  {
    title: "序号",
    width: 20,
    fixed: "left",
    key: "index",
    align: "center",
    render: (renderData: object, index: number) => {
      return `${index + 1}`;
    },
  },
  {
    title: "敏感词",
    key: "word",
    align: "center",
    summaryTitle:"",
    width: 160,
  },
];
/** 选中行数据 */
const rowData = ref([]);
/** 获取选中行Key */
function selectedKeysChange(key, tableData) {
  rowData.value = tableData.map(({ _dummyId, ...rest }) => rest);
}
const handleSearch = ()=>{
  tableSearch()
}

async function handleDelete(rowList: Array<string>) {
  try {
    startLoading();
    console.log(rowList);
    let _params = { data:[] };
    rowList.forEach(e=>{
      _params.data.push({
        id:e.id,
        word:e.word
      })
    })
    await sensitiveWordBatchDelete(_params);
    createMessageSuccess('删除自定义敏感词成功！');
    tableSearch();
  } catch (error) {
    createMessageError('删除自定义敏感词失败：' + error);
  } finally {
    endLoading();
  }
}

const handleClear = ()=>{
  searchTagVal.value = null
  tableSearch()
}
/* 刷新列表 */
const tableSearch = () => {
  pageTableData({
    type:1,
    searchKey:searchTagVal.value
  }, paginationRef.value);
};

/* 组件挂载 */
onMounted(() => {
  tableSearch()
});
</script>

<style lang="less" scoped>
@import "@/styles/default.less";
@import "@/styles/defaultVar.less";

.wrapper {
  width: 100%;
  height: 100%;
}

</style>
