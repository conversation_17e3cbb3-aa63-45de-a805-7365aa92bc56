<template>
  <JDrawer
    v-model:show="show"
    title="积分兑换积分商品配置"
    :isGetIntegralLoading="isGetIntegralLoading"
    @after-leave="handleAfterLeave"
    @after-enter="handleAfterEnter"
    :to="props.to"
    :is-show-footer="false"
    :is-show-scroll="false"
    :main-style="{ backgroundColor: '#f2f3f5', padding: 0 }"
  >
    <!-- 基本信息 -->
    <template #noScrollContent>
      <div class="wrapper">
        <div class="left">
          <!-- 头部 -->
          <div class="header">
            <n-space justify="space-between" style="margin-bottom: 12px;">
              <n-button size="small" @click="refresh" class="store-button">刷 新</n-button>
              <!-- 新增积分商品 -->
              <n-popover trigger="hover" placement="bottom" :show-arrow="false" style="padding: 0;">
                <template #trigger>
                  <n-button type="primary" size="small" class="store-button">新 建</n-button>
                </template>
                <n-button-group vertical style="width: 80px;">
                  <n-button quaternary size="small" @click="openAddIntegralGoodsClass('add')">分类</n-button>
                  <n-button quaternary size="small" @click="openAddIntegralGoodsLabel('add')">标签</n-button>
                </n-button-group>
              </n-popover>
            </n-space>
            <JSearchInput 
              v-model:value="searchValue" 
              @search="getIntegralGoodsClassData" 
              width="100%" 
              size="small"
              placeholder="请输入分类名称"
              clearable
            />
          </div>
          <!-- 积分积分商品分类 tree -->
          <div class="tree-container">
            <n-spin :show="isGetIntegralLoading" size="small" style="height: 100%;">
              <n-scrollbar style="height: 100%" @scroll="handleScroll">
                <IntegralGoodsClassTree
                  v-model:value="model.selectedValue"
                  :tree-data="treeData"
                  :class-menu-options="classOptions"
                  :label-menu-options="labelOptions"
                  @menu-select="handleMenuSelect"
                  @update:selected-keys="handleUpdateSelectKeys"
                  :selected-keys="selectedKeys"
                />
              </n-scrollbar>
            </n-spin>
          </div>
        </div>
        <div class="right">
          <IntegralGoodsTable
            :type="model.type"
            :cateId="model.cateId"
            :min-points="model.minPoints"
            :max-points="model.maxPoints"
            @successful="isGetIntegralLoading = false"
          />
        </div>
        <!-- 新建积分积分商品分类 -->
        <AddIntegralGoodsClass 
          ref="addIntegralGoodsClassRef" 
          @afterSuccess="handleAfterAddSuccess"
        />
        <!-- 新建积分积分商品标签 -->
        <AddIntegralGoodsLabel 
          ref="addIntegralGoodsLabelRef" 
          @afterSuccess="handleAfterAddSuccess"
        />
        <!-- 删除积分积分商品分类 -->
        <DelIntegralGoodsClass 
          ref="delIntegralGoodsClassRef" 
          @after-success="handleAfterSuccess"
        />
      </div>
    </template>
  </JDrawer>
</template>

<script lang="tsx" setup name="IntegralGoodsConfig">
import { ref, computed } from "vue";
import { useDialog } from 'naive-ui';
import type { TreeOption } from 'naive-ui';
import { deletepointSiftById } from "@/services/api";
import { useMessages } from '@/hooks';
import { useGetIntegralGoodsClass } from "./hooks";
import { GoodsCategoryType } from "@/enums";
import type { GoodsType } from "@/enums";
/** 相关组件 */
import JDrawer from "@/components/JDrawer/index.vue";
import IntegralGoodsTable from "./components/IntegralGoodsTable.vue";
import IntegralGoodsClassTree from "./components/IntegralGoodsClassTree.vue";
import AddIntegralGoodsClass from "./components/AddIntegralGoodsClass.vue";
import AddIntegralGoodsLabel from "./components/AddIntegralGoodsLabel.vue";
import DelIntegralGoodsClass from "./components/DelIntegralGoodsClass.vue";

const { createMessageSuccess, createMessageError } = useMessages();

/** props */
const props = withDefaults(defineProps<{
  to?: string; // 弹窗位置
  refreshTable?: () => void; // 刷新表格数据
}>(), {
  to: '#integral-mall'
});

/** 积分积分商品分类 */
const {
  searchValue,
  selectedKeys,
  isGetIntegralLoading,
  getIntegralGoodsClassData,
  handleScroll,
  refresh,
  treeData
} = useGetIntegralGoodsClass();

/** 最大创建分类 */
const MAXPOINTSCLASS = 10;

/** 现存积分分类数量 */
const pointsGoodsCount = computed(() => {
  let pointsClass = treeData.value.find(item => item?.type === GoodsCategoryType.INTEGRAL);
  return pointsClass?.children.length;
})

/** 确认框 */
const dialog = useDialog();

/** 显隐 */
const show = ref(false);
/** 接收父组件参数 */
const acceptParams = () => {
  show.value = true;
};

/* 表单实例 */
const formRef = ref();

/* 表单参数初始化 */
const initParams = {
  selectedValue: '',
  cateId: null, // 当前选中的积分商品分类Id
  type: null, // 积分商品分类类型
  minPoints: null, // 积分商品最小积分
  maxPoints: null, // 积分商品最大积分
};
const model = ref({ ...initParams });

/** 右键选项 */
const classOptions = [
  {
    label: '删除分类',
    key: 'delete_class'
  },
  {
    label: '修改分类',
    key: 'edit_class'
  },
];
const labelOptions = [
  {
    label: '删除标签',
    key: 'delete_label'
  },
  {
    label: '修改标签',
    key: 'edit_label'
  },
];

/** 删除积分商品分类成功回调 */
function handleAfterSuccess() {
  selectedKeys.value = [String(GoodsCategoryType.ALL)];
  model.value.cateId = null;
  model.value.type = null;
  refresh();
}

/** 新增积分商品分类成功回调 */
const handleAfterAddSuccess = (val: string, type: GoodsType) => {
  selectedKeys.value = [val];
  model.value.cateId = val;
  model.value.type = type;
  refresh();
};

/** 右键操作 */
const handleMenuSelect = (key: string | number, option: TreeOption) => {
  console.log("右键操作", key, option);
  // 分类编辑操作
  if (key === 'edit_class') {
    openAddIntegralGoodsClass('edit', option);
  }
  // 分类删除操作
  if (key === 'delete_class') {
    openDelIntegralGoodsClass(option);
  }
  // 标签编辑
  if (key === 'edit_label') {
    openAddIntegralGoodsLabel('edit', option);
  }
  // 标签删除
  if (key === 'delete_label') {
    const d = dialog.warning({
      title: '确定删除《' + option?.label + '》标签',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          d.loading = true;
          await deletepointSiftById(option?.id as string);
          createMessageSuccess("删除成功!");
          refresh();
        } catch (error) {
          createMessageError("删除失败：" + error);
        } finally {
          d.loading = false;
        }
      },
    });
  }
};

/** 树形节点选中项发生变化时的回调函数 */
const handleUpdateSelectKeys = (
  keys: Array<string | number>, 
  option: Array<TreeOption & Partial<ApiSalesManagement.PointSift & ApiStoreModule.GoodsClassification> | null>, 
  meta: { node: TreeOption | null, action: 'select' | 'unselect' }
) => {
  const resetModel = () => {
    model.value.cateId = null;
    model.value.type = null;
    model.value.minPoints = undefined;
    model.value.maxPoints = undefined;
  };

  if (keys.length !== 0) {
    selectedKeys.value = keys;
  } else {
    selectedKeys.value = [String(GoodsCategoryType.ALL)];
    resetModel();
  }

  if (option.length > 0) {
    const firstOption = option[0];

    if (firstOption) {
      const { id, type, minPoints, maxPoints } = firstOption;
      const isIntegral = type === GoodsCategoryType.INTEGRAL;

      if (model.value.cateId !== id) {
        isGetIntegralLoading.value = true;
      }

      model.value.cateId = isIntegral ? id ?? null : undefined;
      model.value.minPoints = isIntegral ? undefined : minPoints ?? null;
      model.value.maxPoints = isIntegral ? undefined : maxPoints ?? null;
      model.value.type = isIntegral ? type ?? null : undefined;
    }
  }
};

/** 关闭抽屉回调 */
const handleAfterLeave = () => {
  // 初始化参数
  model.value = { ...initParams };
};

/** 抽屉出现后的回调 */
const handleAfterEnter = async () => {
  getIntegralGoodsClassData();
};

/** 打开新建积分积分商品分类 */
const addIntegralGoodsClassRef = ref<InstanceType<typeof AddIntegralGoodsClass> | null>(null);
  const openAddIntegralGoodsClass = (
  type: 'add' | 'edit',
  option: TreeOption & { isMenu?: boolean } & Partial<ApiStoreModule.GoodsClassification> = {}
) => {
  const handleAddType = () => {
    if (pointsGoodsCount.value < MAXPOINTSCLASS) {
      addIntegralGoodsClassRef.value?.acceptParams({
        type,
        row: option,
      });
    } else {
      createMessageError(`分类最多创建${MAXPOINTSCLASS}个，请删除分类后新建`);
    }
  };

  const handleEditType = () => {
    addIntegralGoodsClassRef.value?.acceptParams({
      type,
      row: option,
    });
  };

  if (type === 'add') {
    handleAddType();
  } else {
    handleEditType();
  }
};


/** 打开新建积分积分商品标签 */
const addIntegralGoodsLabelRef = ref<InstanceType<typeof AddIntegralGoodsLabel> | null>(null);
const openAddIntegralGoodsLabel = (type: 'add' | 'edit', option: TreeOption & { isMenu?: boolean } & Partial<ApiSalesManagement.IntegralGoodsClass> = {}) => {
  addIntegralGoodsLabelRef.value?.acceptParams({
    type,
    row: option
  });
};

/** 打开删除积分积分商品分类 */
const delIntegralGoodsClassRef = ref<InstanceType<typeof DelIntegralGoodsClass> | null>(null);
const openDelIntegralGoodsClass = (option: TreeOption & { isMenu?: boolean } & Partial<ApiSalesManagement.IntegralGoodsClass> = {}) => {
  delIntegralGoodsClassRef.value?.acceptParams({
    row: option
  });
};

defineExpose({
  acceptParams,
});
</script>

<style lang="less" scoped>
@import "@/styles/defaultVar.less";

:deep(.n-scrollbar-rail) {
  bottom: 8px !important;
}

.wrapper {
  width: 100%;
  height: 100%;
  display: flex;

  .left {
    width: 200px;
    height: 100%;
    background-color: #fff;
    border-right: 1px solid @default-border-color;

    .header {
      padding: 12px 12px 0 12px;
    }

    .tree-container {
      height: calc(100% - 92px);

      :deep(.n-spin-content) {
        height: 100%;
      }
    }
  }

  .right {
    width: calc(100% - 200px);
    overflow: hidden;
  }
}
</style>
