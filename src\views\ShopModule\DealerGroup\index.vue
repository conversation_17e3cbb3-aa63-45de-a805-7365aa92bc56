<template>
  <TabsLayout v-model:value="tabNameRef" :tabsData="tabsData" :onlyTabs="true">
    <component :is="currentPage"></component>
  </TabsLayout>
</template>

<script setup lang="ts">
import { ref, watch, toRef, computed, onMounted } from "vue";
import GroupSetting from "./components/GroupSetting/index.vue";
import Relationship from "./components/Relationship/index.vue";
import TabsLayout from "@/layout/TabsLayout.vue";
const tabsData = [
  {
    label: "分类设置",
    key: "GroupSetting",
  },
  {
    label: "分组关联经销商",
    key: "Relationship",
  },
];
const pageMap = {
  GroupSetting,
  Relationship,
};
const tabNameRef = ref("GroupSetting");
const currentPage = computed(() => pageMap[tabNameRef.value]);
</script>

<style scoped lang="less"></style>
