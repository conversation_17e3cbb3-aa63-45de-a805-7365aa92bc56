import { h } from 'vue';
import type { Component } from 'vue';

/**
 * SVG 图标渲染钩子
 *
 * @param SvgIcon SVG 图标组件
 */
export default function useSvgIconRender(SvgIcon: Component) {
  /**
   * 图标配置接口
   */
  interface IconConfig {
    /** Iconify 图标名称 */
    icon?: Component;
    /** 本地图标名称 */
    localIcon?: string;
    /** 图标颜色 */
    color?: string;
    /** 图标大小 */
    fontSize?: number;
  }

  /**
   * 图标样式类型
   */
  type IconStyle = Partial<Pick<CSSStyleDeclaration, 'color' | 'fontSize'>>;

  /**
   * SVG 图标虚拟节点
   *
   * @param config 图标配置
   */
  const SvgIconVNode = (config: IconConfig) => {
    const { color, fontSize, icon, localIcon } = config;

    const style: IconStyle = {};

    if (color) {
      style.color = color;
    }
    if (fontSize) {
      style.fontSize = `${fontSize}px`;
    }

    // 如果没有传递 icon 或 localIcon，则返回 undefined
    if (!icon && !localIcon) {
      return undefined;
    }

    // 返回渲染 SVG 图标的函数
    return () => h(SvgIcon, { icon, localIcon, style });
  };

  return {
    SvgIconVNode
  };
}
