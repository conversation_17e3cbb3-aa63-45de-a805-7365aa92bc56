<template>
    <div class="rtc-live-wrapper">
        <div class='chat-wrapper'>
            <div 
                class="chat-item"
                v-for='media in streamRTCListRef'
                :key="media.videoEl" 
                :id="media.videoEl" 
                :style="{
                    position:'absolute',
                    top:media.y,
                    left:media.x,
                    width:media.width,
                    height:media.height,
                    zIndex:media.zIndex
                }"
            />
            </div>
        <div class='btn-wrapper'>
            <p style="width:100%; text-align: center; color:#FFFFFF">{{ formatTime(durationTimeRef) }}</p>
            <div class="btn-item">
                <div class="img-wrapper" @click="changeMicStatus" :title="isMicEnabledRef?'静音':'打开麦克风'">
                    <img :src="!isMicEnabledRef?MuteSrc:MicSrc" alt="">
                </div>
                <n-popselect
                    :value="nowMicIdSelected"
                    :options="micListRef.map(item=>({label:item.label,value:item.deviceId}))"
                    @update:value="handleMicSelect"
                    trigger="click"
                    placement="top-end"
                >
                    <p style="cursor: pointer;">切换麦克风</p>
                </n-popselect>
            </div>
            <div class="btn-item">
                <div class="img-wrapper" @click="handleHangup">
                    <img :src="isPublishing?HangupSrc:ConnectSrc" alt="">
                </div>
                <p>{{isPublishing?'挂断':'重连'}}</p>
            </div>
             <div class="btn-item">
                <div class="img-wrapper"  @click="changeCameraStatus" :title="isCameraEnabledRef?'关闭摄像头':'打开摄像头'">
                    <img :src="!isCameraEnabledRef?MutedCamSrc:CamSrc" alt="">
                </div>
                <n-popselect
                    :value="nowCameraIdSelected"
                    :options="cameraListRef.map(item=>({label:item.label,value:item.deviceId}))"
                    @update:value="handleCameraSelect"
                    trigger="click"
                    placement="top-end"
                >
                    <p style="cursor: pointer;">切换摄像头</p>
                </n-popselect>
            </div>
        </div>
    </div>
</template>
<script setup lang='ts'>
import { nextTick, ref, watch } from 'vue';
import { useRTCLive } from './hooks/useRTCStatus';
import{ type RTCLiveUserMediaStatus,  type RTCBaseConfig, StreamHostEventEnum } from './types';
import { formatTime, isObject } from '@/utils';
import { getCameraList, getMicrophoneList } from './utils/liveUtils';
import CamSrc from '@/assets/image/rtc/cam.png'
import HangupSrc from '@/assets/image/rtc/hangup.png'
import MicSrc from '@/assets/image/rtc/mic.png'
import MuteSrc from '@/assets/image/rtc/mute.png'
import ConnectSrc from '@/assets/image/rtc/connect.png'
import MutedCamSrc from '@/assets/image/rtc/muteCam.png'
import { useDialog } from 'naive-ui';
import { useMessages } from '@/hooks';
    interface RTCLiveModalProps{
        RTCRoomInfo:RTCBaseConfig
    }
    interface RTCLiveModalEmits{
        (e:'RTCStatusChange'):void
    }
    const dialog = useDialog()
    const durationTimeRef = ref(0)
    let durationTimer = null
    const streamRTCListRef = ref<Array<RTCLiveUserMediaStatus>>([])
    const cameraListRef = ref<Array<MediaDeviceInfo>>([])
    const micListRef = ref<Array<MediaDeviceInfo>>([])
    const nowCameraIdSelected = ref('')
    const nowMicIdSelected = ref('')
    const props = withDefaults(defineProps<RTCLiveModalProps>(),{
        RTCRoomInfo:null
    })
    const emits = defineEmits<RTCLiveModalEmits>()
    const {RTCLiveInstance} = useRTCLive()
    const isMicEnabledRef = ref<boolean>(true)
    const isCameraEnabledRef = ref<boolean>(true)
    const isPublishing = ref<boolean>(true)
    const {createMessageError} = useMessages()
    bindRTCEvent()

    function stopDurationTimer(){
        if(durationTimer){
            clearInterval(durationTimer)
        }
        durationTimer = null
        durationTimeRef.value = 0
    }

    function startDurationTimer(){
        stopDurationTimer()
        durationTimer = setInterval(()=>{
            durationTimeRef.value = durationTimeRef.value + 1
        },1000)   
    }

    function bindRTCEvent(){
        RTCLiveInstance.on(StreamHostEventEnum.userPublishStream,async({userId:remoteUserId})=>{
            streamRTCListRef.value = RTCLiveInstance.get().userMediaList
            await nextTick()
            RTCLiveInstance.playRemoteVideo({remoteUserId,domId:streamRTCListRef.value[streamRTCListRef.value.length - 1].videoEl})
       
        })
         RTCLiveInstance.on(StreamHostEventEnum.userUnpublishStream,async({userId:remoteUserId})=>{
            streamRTCListRef.value = RTCLiveInstance.get().userMediaList
            // await nextTick()
            // RTCLiveInstance.playRemoteVideo({remoteUserId,domId:streamRTCListRef.value[streamRTCListRef.value.length - 1].videoEl})
        })
    }

    async function initRTC(config:RTCBaseConfig) {
        try{
            cameraListRef.value = await getCameraList()
            micListRef.value = await getMicrophoneList()
            nowCameraIdSelected.value = cameraListRef.value[0].deviceId
            nowMicIdSelected.value = micListRef.value[0].deviceId
            await RTCLiveInstance.createTheHost(config)
            await RTCLiveInstance.chooseCameraDevice(nowCameraIdSelected.value)
            await RTCLiveInstance.chooseMicphoneDevice(nowMicIdSelected.value)
            const rtcStatus = RTCLiveInstance.get()
            streamRTCListRef.value = rtcStatus.userMediaList   
            isMicEnabledRef.value =  rtcStatus.micStatus.enabled 
            isCameraEnabledRef.value = rtcStatus.cameraStatus.enabled
            await nextTick()
            RTCLiveInstance.setVideoPlayWrapper(streamRTCListRef.value[0].videoEl)
            await RTCLiveInstance.setStreamingStatus(true)
            isPublishing.value = true
            startDurationTimer()
        }
        catch(e){
            createMessageError(`链接到RTC失败: ${e}`)
            isPublishing.value = false
            console.warn(e)
        }
    }
    async function handleCameraSelect(value:string){
        try{
            await RTCLiveInstance.chooseCameraDevice(value)
            nowCameraIdSelected.value = value
        }
        catch(e){
            createMessageError(`切换摄像头失败: ${e}`)
        }
    }
      async function handleMicSelect(value:string){
        try{
            await RTCLiveInstance.chooseMicphoneDevice(value)
            nowMicIdSelected.value = value
        }
        catch(e){
            createMessageError(`切换麦克风失败: ${e}`)
        }
    }
    async function changeCameraStatus() {
        try{
            await RTCLiveInstance.setCameraStatus(!isCameraEnabledRef.value)
            isCameraEnabledRef.value = RTCLiveInstance.get().cameraStatus.enabled 
        }
        catch(e){
            createMessageError(`修改摄像头状态失败: ${e}`)
        }
    }

    async function changeMicStatus(){
        try{
            await RTCLiveInstance.setMicphonesStatus(!isMicEnabledRef.value)
            isMicEnabledRef.value = RTCLiveInstance.get().micStatus.enabled 
        }
        catch(e){
            createMessageError(`修改麦克风状态失败: ${e}`)
        }
    }
    async function handleHangup() {
        try{
            if(isPublishing.value){
                const _d = dialog.warning({
                    title: '挂断当前视频问诊',
                    content: '挂断后可以点击【重连】重新链接',
                    positiveText: '挂断',
                    negativeText: '取消',
                    onPositiveClick: () => {
                        return new Promise((resolve)=>{
                            _d.loading = true
                            RTCLiveInstance.setStreamingStatus(false)
                            .then(()=>{
                                isPublishing.value = false 
                                stopDurationTimer()
                                resolve(true)
                            })
                            .catch(e=>{
                                createMessageError(`修改推流状态失败: ${e}`)
                            })
                            .finally(()=>{
                                _d.loading = false
                            })

                        })
                    },
                    onNegativeClick: () => {
                    
                    }
                })
            }
            else{
                const _d = dialog.info({
                    title: '重连到当前视频问诊',
                    content: '房间内的成员会看到当前摄像头画面',
                    positiveText: '重连',
                    negativeText: '取消',
                    onPositiveClick: () => {
                        return new Promise((resolve)=>{
                            _d.loading = true
                            RTCLiveInstance.setStreamingStatus(true)
                            .then(()=>{
                                isPublishing.value = true 
                                startDurationTimer()
                                resolve(true)
                            })
                            .catch(e=>{
                                createMessageError(`修改推流状态失败: ${e}`)
                            })
                            .finally(()=>{
                                _d.loading = false
                            })
                        })
                    },
                    onNegativeClick: () => {
                    
                    }
                })
            }
        
        }
        catch(e){

        }
    }

    watch(()=>props.RTCRoomInfo,(newVal)=>{
        if(isObject(newVal)&&newVal.token){
           initRTC(newVal)
        }
    })

</script>
<style scoped lang="less">
    .rtc-live-wrapper{
        background:#272323;
        width: 375px;
        height: 100%;
        .chat-wrapper{
            height: 75%;
            position: relative;
            .chat-item{
                transition: all 0.2s ease-in-out;
            }
        }
        .btn-wrapper{
            height: 25%;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-around;
            align-items: center;
            color:#FFFFFF;
            font-size: 12px;
            line-height: 20px;
            .btn-item{
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                .img-wrapper{
                    width: 64px;
                    height: 64px;
                    border-radius: 50%; 
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    cursor: pointer;
                    margin-bottom: 10px;
                    img{
                        width: 100%;
                        height: 100%;
                    }
                }
            }
        }
    }
</style>