<template>
    <NCard :bordered="false" style="border-radius: 16px;">
        <NSpin :show="loading" size="small">
            <NSpace vertical :size="20">
                <NFlex align="center" :size="2">
                    <span class="title">待处理</span>
                    <!-- <span class="subhead">(不含积分兑换订单)</span> -->
                </NFlex>
                <NGrid cols="s:1 m:2 l:4" responsive="screen" :x-gap="28" :y-gap="16">
                    <NGi v-for="item in dataPanel" :key="item.title">
                        <NCard 
                            :bordered="false"
                            :style="{ height: '165px', backgroundImage: `url(${item.bgImg})`, backgroundSize: '100% 100%' }"
                        >
                            <div class="data-content">
                                <span class="data-title" @click="jumpTo( item.isAuth,item.path, item.query)">{{ item.title }}</span>
                                <div class="data-num" :style="{ color: item.numColor }">
                                    <n-number-animation :from="0" :to="Number(item.num)" />
                                </div>
                            </div>
                        </NCard>
                    </NGi>
                </NGrid>
            </NSpace>
        </NSpin>
    </NCard>
</template>

<script lang="ts" setup>
import { reactive, computed } from "vue";
import { RoutesName } from '@/enums/routes';
import { OrderManagementSelection, PrescriptionSelection , SystemStoreType } from '@/enums';
import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
import { getPendingCount } from "@/services/api";
import { useLoading, useMessages } from "@/hooks";
import useJumpTo from "../../hooks/useJumpTo";
import cardBg from "@/assets/image/system/home/<USER>/cardBg.png";
import cardBgYellow from "@/assets/image/system/home/<USER>/cardBgYellow.png";
import cardBgPink from "@/assets/image/system/home/<USER>/cardBgPink.png";
import cardBgPurple from "@/assets/image/system/home/<USER>/cardBgPurple.png";

const systemStore = useSystemStoreWithoutSetup();
const { jumpTo } = useJumpTo();
const message = useMessages();
import { hasAfterServiceManagementAuth , hasOrderManagementAuth , hasPrescriptionAuth } from "../../authList"

/** 数据源 */
const pendingCount = reactive({
    pendingOrderCount: 0,
    pendingPaymentCount: 0,
    toBeShippedCount: 0,
    pendingAfterSaleCount: 0
});
/** 数据面板 */
const dataPanel = computed(() => {
    let originData = [
    {
            title: '待医生开方',
            num: pendingCount.pendingOrderCount,
            path: RoutesName.PrescriptionManagement,
            bgImg: cardBg,
            numColor: '#4DA4FF',
            isAuth:hasPrescriptionAuth,
            query: {
                tabValue: PrescriptionSelection.unopened
            }
        },
        {
            title: '待付款',
            num: pendingCount.pendingPaymentCount,
            path: RoutesName.OrderManagement,
            bgImg: cardBgYellow,
            numColor: '#FF9636',
            isAuth:hasOrderManagementAuth,
            query: {
                tabValue: OrderManagementSelection.waitPay
            }
        },
        {
            title: '待发货',
            num: pendingCount.toBeShippedCount,
            path: RoutesName.OrderManagement,
            bgImg: cardBgPink,
            numColor: '#FF6864',
            isAuth:hasOrderManagementAuth,
            query: {
                tabValue: OrderManagementSelection.waitFeliverGoods
            }
        },
        {
            title: '售后',
            num: pendingCount.pendingAfterSaleCount,
            path: RoutesName.AfterServiceManagement,
            bgImg: cardBgPurple,
            numColor: '#817FFA',
            isAuth:hasAfterServiceManagementAuth,
        }
    ];
    if (systemStore._globalConfig['marketplaceType'] !== SystemStoreType.PHARMACEUTICALMALL) {
        originData.shift()
    }
    return originData;
});
/** 获取商品管理数据 */
const { loading, startLoading, endLoading } = useLoading();
function getPendingMangement() {
    startLoading();
    getPendingCount().then(res => {
        Object.assign(pendingCount, res);
    }).catch(err => {
        message.createMessageError("获取待处理信息失败：" + err);
    }).finally(() => {
        endLoading();
    });
};

function init() {
    getPendingMangement();
}
init();
</script>

<style lang="less" scoped>
@import "@/styles/default.less";

.title {
    font-size: 20px;
    font-weight: bold;
    color: #333333;

    &::before {
        content: '';
        display: inline-block;
        width: 4px;
        height: 16px;
        background: #4DA4FF;
        border-radius: 12px;
        margin-right: 4px;
    }
}

.subhead {
    font-size: 16px;
    color: #999999;
}

.data-content {
    display: flex;
    flex-direction: column;

    .data-title {
        font-size: 16px;
        color: #666666;
        display: flex;
        align-items: center;
        margin-top: 12px;
        cursor: pointer;

        &::after {
            content: '';
            display: inline-block;
            width: 16px;
            height: 16px;
            background: url(@/assets/image/system/home/<USER>/ChevronRight.png) no-repeat;
            background-size: 100% 100%;
        }
    }

    .data-num {
        font-size: 56px;
        font-family: Bebas Neue, Bebas Neue;
        // 字间距
        letter-spacing: -3px;
    }
}
</style>
