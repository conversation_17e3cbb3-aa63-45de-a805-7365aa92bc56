<template>
  <div class="shop-management-page inner-page-height">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :pagination="paginationRef"
      @paginationChange="paginationChange"
      :isNeedCollapse="false"
      :isTableSelection="false"
      :isDisplayIndex="false"
    >
      <!-- 表单 -->
      <template #searchForm>
        <n-form
          ref="formRef"
          label-placement="left"
          label-width="auto"
          :show-feedback="false"
          require-mark-placement="right-hanging"
          size="small"
          :style="{ width: '100%' }"
        >
          <n-form-item :span="12" label="门店名称" path="">
            <j-search-input
              width="270px"
              v-model:value.trim="model.nameOrId"
              placeholder="请输入门店名或ID"
              @search="handlerSearch"
            />
          </n-form-item>
          <n-form-item :span="12" label="经销商姓名" path="">
            <j-search-input
              width="270px"
              v-model:value.trim="model.dealerNameOrId"
              placeholder="请输入经销商姓名或ID"
              @search="handlerSearch"
            />
          </n-form-item>
          <n-form-item label="创建时间" label-placement="left">
            <j-date-range-picker
              style="flex: 1"
              v-model:value="model.creationTime"
              type="datetimerange"
              format="yyyy-MM-dd"
              :default-time="['00:00:00', '23:59:59']"
              clearable
            />
          </n-form-item>
        </n-form>
      </template>

      <template #tableHeaderBtn>
        <n-button v-if="hasBatchWelfareVoucherAuth" type="primary" @click="handleBulkWelfareVouchers">
          批量发放福利券
        </n-button>
      </template>
    </FormLayout>
    <BulkWelfareVouchers ref="bulkWelfareVouchersRef" />
    <DistributeWelfareVoucherModal ref="distributeWelfareVoucherModalRef"></DistributeWelfareVoucherModal>
    <EditOrganizeModal ref="editOrganizeModalRef" />
  </div>
</template>

<script lang="tsx" setup>
import { computed, onMounted, ref, watch } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { getShopInfoPage } from "@/services/api";
import { useMessages } from "@/hooks";
import BulkWelfareVouchers from "./components/BulkWelfareVouchers.vue";
import EditOrganizeModal from "./components/EditOrganizeModal.vue";
import DistributeWelfareVoucherModal from "@/views/Distribution/OrganizationalStructure/components/distributeWelfareVoucherModal.vue";
import { hasWelfareVoucherAuth, hasEditAuth, hasCheckAuth, hasBatchWelfareVoucherAuth } from "./authList";
import moment from "moment";
import { useRoute, useRouter } from "vue-router";
import { RoutesName } from "@/enums/routes";

const { createMessageSuccess, createMessageError } = useMessages();
/** 表格hook */
const { isLoading, tableData, paginationRef, pageTableData, paginationChange } = useTableDefault({
  pageDataRequest: getShopInfoPage,
});
const router = useRouter();
/* 表格列表项 */
const tableColumns = ref([
  {
    title: "门店名称/ID",
    key: "name",
    align: "left",
    render: rowData => {
      return (
        <div>
          <div>{rowData.storeName ? rowData.storeName : "-"} </div>
          <div> {rowData.shortId ? rowData.shortId : "-"}</div>
        </div>
      );
    },
  },
  {
    title: "门店地址",
    key: "addressDetail",
    align: "left",
    width: 300,
  },
  {
    title: "联系人姓名/电话",
    key: "contactName",
    align: "left",
    render: rowData => {
      return (
        <div>
          <div>{rowData.contactName ? rowData.contactName : "-"}</div>
          <div> {rowData.contactPhone ? rowData.contactPhone : "-"} </div>
        </div>
      );
    },
  },
  {
    title: "店长昵称/ID",
    key: "nickName",
    align: "left",
    render: rowData => {
      return (
        <div>
          <div>{rowData.managerNickname ? rowData.managerNickname : "-"} </div>
          <div> {rowData.managerShortId ? rowData.managerShortId : "-"} </div>
        </div>
      );
    },
  },
  {
    title: "店员数",
    key: "staffNum",
    align: "left",
    render: rowData => {
      return (
        <n-button button text type="primary" onClick={() => handleToPersonnelManagement(rowData)}>
          {rowData.assistantCount}
        </n-button>
      );
    },
  },
  {
    title: "会员数",
    key: "memberNum",
    align: "left",
    render: rowData => {
      return (
        <n-button button text type="primary" onClick={() => handleToCustomerManagement(rowData)}>
          {rowData.memberCount}
        </n-button>
      );
    },
  },
  {
    title: "归属经销商姓名/ID",
    key: "dealerName",
    align: "left",
    render: rowData => {
      return (
        <div>
          <div>{rowData.dealerName ? rowData.dealerName : "-"} </div>
          <div>用户ID：{rowData.dealerShortCsId  ? rowData.dealerShortCsId  : "-"} </div>
        </div>
      );
    },
  },
  {
    title: "门店状态",
    key: "status",
    align: "left",
    render: rowData => {
      const status = ["停用", "启用", "删除"];
      return status[rowData.storeStatus];
    },
  },
  {
    title: "创建时间",
    key: "createTime",
    align: "left",
  },
  {
    title: "操作",
    key: "action",
    fixed: "right",
    align: "left",
    render: rowData => {
      return (
        <n-space align="center" justify="center">
          {hasEditAuth ? (
            <n-button button text type="primary" onClick={() => handleEditOrganizeModal(rowData)}>
              编辑
            </n-button>
          ) : null}
          {hasWelfareVoucherAuth ? (
            <n-button text type="primary" onClick={() => distributeWelfareVoucher(rowData.id)}>
              发放福利券
            </n-button>
          ) : null}
          {hasCheckAuth ? (
            <n-button text type="primary" onClick={() => handleCheckOrder(rowData)}>
              查看订单
            </n-button>
          ) : null}
        </n-space>
      );
    },
  },
]);
const route = useRoute();
const initParams = {
  nameOrId: "",
  creationTime: null,
  dealerNameOrId: route?.query?.dealerShortId ?? ""
};
/** 参数 */
const model = ref({
  ...initParams,
});
/** 获取参数 */
const getParams = () => {
  const { nameOrId, creationTime, dealerNameOrId } = model.value;
  const startTime = creationTime ? moment(creationTime[0]).format(`YYYY-MM-DD HH:mm:ss`) : null;
  const endTime = creationTime ? moment(creationTime[1]).format(`YYYY-MM-DD HH:mm:ss`) : null;
  return {
    nameOrId,
    dealerNameOrId,
    startTime,
    endTime,
  };
};
const distributeWelfareVoucherModalRef = ref();
const distributeWelfareVoucher = id => {
  distributeWelfareVoucherModalRef.value.acceptParams(id);
};
const editOrganizeModalRef = ref();

const bulkWelfareVouchersRef = ref();
const handleBulkWelfareVouchers = () => {
  const _params = {
    refresh: refresh,
  };
  bulkWelfareVouchersRef.value?.acceptParams(_params);
};
const handleEditOrganizeModal = rowData => {
  editOrganizeModalRef.value.acceptParams({});
};
const handleCheckOrder = rowData => {
  router.push({
    name: RoutesName.OrderManagement,
    query: {
      storeShortId: rowData?.shortId,
    },
  });
};

const handleToPersonnelManagement = rowData => {
  router.push({
    name: RoutesName.PersonnelManagement,
    query: {
      shortId: rowData?.shortId,
    },
  });
};
const handleToCustomerManagement = rowData => {
  router.push({
    name: RoutesName.CustomerManagement,
    query: {
      storeShortId: rowData?.shortId,
    },
  });
};
/** 搜索 */
const handlerSearch = () => {
  tableSearch();
};

/** 表格刷新 */
function refresh() {
  tableSearch();
}

/* 表格搜索 */
const tableSearch = () => {
  pageTableData(getParams(), paginationRef.value);
};

/** 监听 */
watch(
  () => [model.value.creationTime],
  () => {
    tableSearch();
  },
);
/** 组件挂载 */
onMounted(() => {
  tableSearch();
});
</script>

<style lang="less" scoped>
@import "@/styles/defaultVar.less";
</style>
