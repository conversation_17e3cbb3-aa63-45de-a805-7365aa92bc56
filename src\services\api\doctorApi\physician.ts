import { defHttp } from '@/services';

/** 医生管理 */
export const enum DoctorApi {
    add = 'doctorEntity/add',
    update = 'doctorEntity/update',
    get = 'doctorEntity/get',
    page = 'doctorEntity/page',
    updateDoctorStatus = 'doctorEntity/updateDoctorStatus',
    list = '/doctorEntity/list',
    addressEntityListByCode = '/addressEntity/listByCode'
}

/** 1.1.5医生管理 */
export const enum SecondEditionDoctorApi{
    doctorEntityPage = '/doctorEntity/page',
    doctorEntityAdd = '/doctorEntity/add',
    doctorEntityUpdate = '/doctorEntity/update',
    doctorEntityGet = '/doctorEntity/get',
    doctorEntityPageForPres = '/doctorEntity/pageForPres',
    doctorEntityPageCommon = '/doctorEntity/page/common',
    departmentConfigListDepartmentsForDoctor = '/departmentConfig/listDepartmentsForDoctor',
    institutionManagementsPageForDoctor = '/institutionManagements/pageForDoctor'
}

interface GetDoctorEntityPageRes {
    records: ApiStoreModule.DoctorEntity[],
    total: string;
    size: string;
    current: string;
}
/** 医生管理分页 */
export function getDoctorEntityPage(params: {
    data: {
        doctorName: string
    }, pageVO: { current: number, size: number }
}) {
    return defHttp.post<GetDoctorEntityPageRes>({
        url: DoctorApi.page,
        params,
    });
}

/** 切换医生状态 */
export function switchDoctorStatus(_params: {
    id: string,
    isActived: 0 | 1, // 0: 启用， 1： 禁用
}) {
    return defHttp.post({
        url: DoctorApi.updateDoctorStatus,
        params: {
            data: _params
        },
    });
}

/** 新增医生 */
export function addDoctor(_params: {
    img?: string, // 头像（minio地址）
    doctorName: string, // 医生姓名
    mobile?: string, // 手机号码
    doctorCode?: string, // 资格证书编码
    platform?: string, // 开方医院或平台
}) {
    return defHttp.post<GetDoctorEntityPageRes>({
        url: DoctorApi.add,
        params: {
            data: _params
        },
    });
}

/** 医生编辑 */
export const doctorUpdate = (_params: {
    id: string,
    img?: string, // 头像（minio地址）
    doctorName: string, // 医生姓名
    mobile?: string, // 手机号码
    doctorCode?: string, // 资格证书编码
    platform?: string, // 开方医院或平台
}) => {
    return defHttp.put({
        url: DoctorApi.update,
        params: {
            data: _params
        },
    });
};

interface DoctorEntityres {
    createTime: string;
    doctorCode: string;
    doctorName: string;
    id: string;
    img: string;
    isActived: 0 | 1;
    mobile: string;
    platform: string;
    updateTime: string;
}
/** 查询某个医生 */
export function getDoctorEntityById(id: string) {
    return defHttp.get<DoctorEntityres>({
        url: DoctorApi.get + '?id=' + id,
    })
}

/** 医生列表 */
export function getDoctorEntityList() {
    return defHttp.post({
        url: DoctorApi.list,
    })
}

/** 地址 */
export function addressEntityListByCode(params){
    return defHttp.get({
        url: DoctorApi.addressEntityListByCode+'?parentCode='+ params.data.parentCode + '&cateType=' + params.data.cateType,
    })
}

/** 调整-医生分页查询 */
export function doctorEntityPage(params) {
    return defHttp.post({
        url: SecondEditionDoctorApi.doctorEntityPage,
        params
    })
}

/** 调整-新增医生 */
export function doctorEntityAdd(params) {
    return defHttp.post({
        url: SecondEditionDoctorApi.doctorEntityAdd,
        params
    })
}

/** 调整-编辑医生 */
export function doctorEntityUpdate(params) {
    return defHttp.put({
        url: SecondEditionDoctorApi.doctorEntityUpdate,
        params
    })
}

/** 调整-单个查询 */
export function doctorEntityGet(id) {
    return defHttp.get({
        url: SecondEditionDoctorApi.doctorEntityGet + '?id=' + id,
    })
}

/** 医生-分页查询（仅供PC后台 处方详情 使用） */
export function doctorEntityPageForPres(params) {
    return defHttp.post({
        url: SecondEditionDoctorApi.doctorEntityPageForPres,
        params
    })
}
/** 医生-分页查询（新） */
export function doctorEntityPageCommon(params) {
    return defHttp.post({
        url: SecondEditionDoctorApi.doctorEntityPageCommon,
        params
    })
}
/** 科室列表 */
export function departmentConfigListDepartmentsForDoctor() {
  return defHttp.post({
    url: SecondEditionDoctorApi.departmentConfigListDepartmentsForDoctor,
  })
}

/** 机构列表 */
export function institutionManagementsPageForDoctor(params) {
    return defHttp.post({
      url: SecondEditionDoctorApi.institutionManagementsPageForDoctor,
      params
    })
  }

