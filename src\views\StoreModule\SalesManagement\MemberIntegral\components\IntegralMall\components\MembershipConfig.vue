<template>
  <JDrawer
    v-model:show="show"
    title="会员等级配置"
    @after-leave="handleAfterLeave"
    @after-enter="handleAfterEnter"
    :to="props.to"
    :is-show-scroll="false"
  >
    <!-- 基本信息 -->
    <template #noScrollContent>
      <div class="wrapper">
        <div class="header">
          <span class="explain">
            说明：通过新增可增设会员等级，最少1级，最高10级，需录入等级名称及最低积分，下一级最低积分需大于上一级，只能录入大于0的整数。
          </span>
          <!-- 新增 -->
          <JTextButton 
            v-if="tableDataRef.length < 10" 
            text
            type="primary"
            size="large"
            @click="addMemberGrade" 
            class="add"
          >
            新 增
          </JTextButton>
        </div>
        <!-- 会员等级表格 -->
        <div class="content">
          <n-data-table
            :columns="columns"
            :data="tableDataRef"
            :row-key="row => row.id"
            :style="{ height: `100%` }"
            default-expand-all
            flex-height
            :single-line="false"
            size="small"
            :loading="isGetLoading"
          >
            <template #empty>
              <JEmpty />
            </template>
          </n-data-table>
        </div>
      </div>
    </template>
    <!-- Footer -->
    <template #footer>
      <div class="footer-wrapper">
        <!-- 启用会员等级 -->
        <JCheckbox v-model:checked="model.isEnable" style="margin-left: 24px;">
          <span style="font-size: 16px;">启用会员等级</span>
        </JCheckbox>
        <n-space>
          <n-button @click="show = false" class="store-button">取 消</n-button>
          <n-button type="primary" :loading="isLoading" @click="_save" class="store-button">保 存</n-button>
        </n-space>
      </div>
    </template>
  </JDrawer>
</template>

<script lang="tsx" setup name="MembershipConfig">
import { ref } from "vue";
import { getPointLevel, addPointLevel } from "@/services/api";
import { useMessages, useLoading } from '@/hooks';
import { uuid, isArray, deepClone } from "@/utils";
import { MEMBERMAXPOINT } from "../../../types";
/** 相关组件 */
import JDrawer from "@/components/JDrawer/index.vue";
import JTextButton from "@/components/JTextButton/index.vue";
import JEmpty from "./JEmpty.vue";

const { createMessageSuccess, createMessageError } = useMessages();

/** props */
const props = withDefaults(defineProps<{
  to?: string; // 弹窗位置
  refreshTable?: () => void; // 刷新表格数据
}>(), {
  to: '#integral-mall'
});

/** 显隐 */
const show = ref(false);

/* 表单参数初始化 */
const initParams = {
  isEnable: false, // 是否启用配置
  delPointsLevelList: [], // 编辑时，删除的会员等级
};
const model = ref(deepClone(initParams));

/** 表格数据 */
const tableDataRef = ref([]);

/** 表单项 */
const columns = [
  {
    title: "序号",
    width: 80,
    fixed: "left",
    key: "sort",
    align: "center",
    render: (renderData: object, index: number) => {
      return `${index + 1}`;
    },
  },
  {
    title: '等级名称',
    key: 'title',
    resizable: true,
    render: (row) => {
      return (
        <n-input
          value={row?.title}
          placeholder="请输入等级名称"
          maxlength="5"
          show-count
          style="width: 100%;"
          onUpdateValue={(value) => handleUpdateValue(row?.id ?? row?.addId, 'title', value)}
        />
      );
    }
  },
  {
    title: '最低积分',
    key: 'minPoints',
    resizable: true,
    render: (row, index) => {
      if (!index) { // 第一项
        return (
          <n-input-number
            value={row?.minPoints}
            placeholder="请输入积分"
            style="width: 100%;"
            show-button={false}
            precision={0}
            min={0}
            max={0}
            onUpdateValue={(value) => handleUpdateValue(row?.id ?? row?.addId, 'minPoints', value)}
          />
        );
      }
      return (
        <n-input-number
          value={row?.minPoints}
          placeholder="请输入积分"
          style="width: 100%;"
          show-button={false}
          precision={0}
          min={0}
          max={MEMBERMAXPOINT}
          onUpdateValue={(value) => handleUpdateValue(row?.id ?? row?.addId, 'minPoints', value)}
        />
      );
    }
  },
  {
    title: '操作',
    key: 'operation',
    width: 160,
    fixed: "right",
    render: (row, index) => {
      return (
        <n-flex>
          <n-button text type="error" onClick={() => handleDelete(row)}>
            删除
          </n-button>
        </n-flex>
      )
    },
  }
];

/** 接收父组件参数 */
const acceptParams = () => {
  show.value = true;
};

/** 校验数据录入 */
function validateTableData(tableData: { id: string, title: string, minPoints: number }[], isEnable: boolean): boolean {
  if (tableData.length === 0) {
    createMessageError(`请录入数据！`);
    return false;
  }
  for (let i = 0; i < tableData.length; i++) {
    if (!tableData[i].title || tableData[i].title.trim() === '') {
      createMessageError(`序号：${i + 1}，请录入数据！`);
      return false;
    }
    if (i === 0 && tableData[i].minPoints !== 0 && isEnable) {
      createMessageError(`序号为${i + 1}时，最低积分限制必须为0！`);
      return false;
    }
    if ((tableData[i].minPoints < 0 || !Number.isInteger(tableData[i].minPoints)) && isEnable) {
      createMessageError(`会员级别《${tableData[i].title}》，不是大于等于0的整数！`);
      return false;
    }
    if ((i > 0 && tableData[i].minPoints <= tableData[i - 1].minPoints) && isEnable) {
      createMessageError(`当前会员级别《${tableData[i].title}》的积分要大于前一个级别《${tableData[i - 1].title}》的积分！`);
      return false;
    }
  }
  return true;
}

/** 更新 */
function handleUpdateValue(id: string, key: string, value: string | number) {
  const item = tableDataRef.value.find(item => (item.id === id || item.addId === id));
  if (item && key in item) {
    item[key] = value;
  }
}

/** 删除会员等级 */
function handleDelete(row) {
  try {
    // 远程删除
    if (row?.id) {
      const pointsLevelObj = tableDataRef.value.find(item => item?.id === row?.id);
      if (pointsLevelObj) {
        model.value.delPointsLevelList.push({
          ...pointsLevelObj,
          isDeleted: 1,
        });
      }
    } 

    tableDataRef.value = tableDataRef.value.filter(item => item?.id !== row?.id || item?.addId !== row?.addId);

    // 如果存在成员且第一个成员的序号为1，将最低积分限制为0，不可更改
    if (tableDataRef.value.length > 0) {
      const firstItem = { ...tableDataRef.value[0], minPoints: 0 };
      Object.defineProperty(firstItem, 'minPoints', { writable: false });
      tableDataRef.value[0] = firstItem;
    }
  } catch (error) {
    createMessageError('会员等级删除失败：' + error);
  }
}

/** 关闭抽屉回调 */
const handleAfterLeave = () => {
  // 初始化参数
  model.value = deepClone(initParams);
  tableDataRef.value = [];
};

/** 抽屉出现后的回调 */
const { loading: isGetLoading, startLoading, endLoading } = useLoading(false);
const handleAfterEnter = async () => {
  try {
    startLoading();
    const data = await getPointLevel({});
    if (isArray(data)) {
      tableDataRef.value = data;
      model.value.isEnable = data[0]['isEnable'] ?? false;
    } else {
      tableDataRef.value = [];
      model.value.isEnable = false;
    }
  } catch (error) {
    createMessageError("获取会员等级失败：" + error);
  } finally {
    endLoading();
  }
};

/** 新增等级 */
function addMemberGrade() {
  const newMember = {
    addId: uuid(),
    title: null,
    minPoints: null
  };

  if (tableDataRef.value.length === 0) {
    newMember.minPoints = 0;
    Object.defineProperty(newMember, 'minPoints', {
      writable: false
    });
  }

  tableDataRef.value.push(newMember);
}

/** 获取参数 */
function _getParams() {
  let _params = [];
  const { isEnable } = model.value;
  const tableData = tableDataRef.value;

  // 遍历 tableData 并根据 id 和 addId 分别处理
  tableData.forEach((item, index) => {
    const baseObj = {
      sort: index + 1,
      isEnable,
      minPoints: item?.minPoints,
      title: item?.title,
    };

    if (item?.id) {
      _params.push({ ...baseObj, id: item.id });
    } else if (item?.addId) {
      _params.push(baseObj);
    }
  });

  // 将删除的项目添加到 _params 中
  _params.push(...model.value.delPointsLevelList);

  return {
    pointLevelList: _params,
  };
}


/* 确认--保存 */
const { loading: isLoading, startLoading: startSaveLoading, endLoading: endSaveLoading } = useLoading(false);
const _save = async (e: MouseEvent) => {
  e.preventDefault();
  try {
    startSaveLoading();
    const { isEnable } = model.value;
    // 启用时，校验数据
    let checked = validateTableData(tableDataRef.value, isEnable);
    if (!checked) {
      return;
    }
    await addPointLevel(_getParams());
    createMessageSuccess("保存成功！");
    // 关闭
    show.value = false;
  } catch (error) {
    createMessageError("保存失败：" + error);
  } finally {
    endSaveLoading();
  }
};

defineExpose({
  acceptParams,
});
</script>

<style lang="less" scoped>
@import "../styles/index.less";

:deep(.n-scrollbar-rail) {
  bottom: 8px !important;
}

.wrapper {
  width: 100%;
  height: 100%;
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .add {
      margin-right: 36px;
    }
  }

  .content {
    width: 98%;
    height: calc(100% - 50px);
    margin-top: 12px;
  }

  .config-wrapper {
    width: 100%;
    display: flex;
    flex-wrap: wrap;

    .integral-item {
      display: flex;
      align-items: center;
      margin-left: 32px;
      margin-bottom: 24px;
    }
  }
}
</style>
