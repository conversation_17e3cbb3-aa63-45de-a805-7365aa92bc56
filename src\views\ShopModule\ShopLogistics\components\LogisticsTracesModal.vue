<template>
    <n-modal v-model:show="modalShow" :auto-focus="false" @after-leave="handleClose">
        <n-card
            style="width: 600px;max-height: 650px;"
            :bordered="false"
            size="small"
            title="物流详情"
            closable
            @close="handleClose"
        >
        <n-spin :show="loading" description="加载中...">
            <n-tabs
                v-model:value="nameRef"
                type="card"
                tab-style="min-width: 80px;"
                @update:value="hanelChangeTab"
            >
                <n-tab-pane
                    v-for="panel in logisticsCode"
                    :key="panel.trackingNo"
                    :tab="panel.trackingNo"
                    :name="panel.trackingNo"
                    >
                    <div v-if="!isNoLog">
                        <div class="logistics">
                            <div class="infoClass">
                                <p class="companyName">物流公司:{{ shipCompanyName || '-'}}</p>
                                <p>快递单号：{{ panel.trackingNo || '-' }}</p>
                                <p>最后更新：{{ lastUpadtaTime || '-'}}</p>
                            </div>
                        </div>
                        <div class="steps">
                            <n-space vertical>
                                <n-timeline>
                                    <n-timeline-item
                                        v-for="(item,index) in logisticsInfo"
                                        :type="index == 0 ? 'success' : ''"
                                        :title="ExpressStatusText[item.state]+ '&nbsp;&nbsp;'+ item.acceptTime"
                                        :content="item.acceptStation"
                                        >
                                        <template #icon>
                                        <n-icon>
                                        <EllipseSharp />
                                        </n-icon>
                                    </template>
                                </n-timeline-item>
                                </n-timeline>
                            </n-space>
                        </div>
                    </div>
                    <div v-else v-for="item in noLogList" class="noLogListStyle">
                        <div>快递单号：{{ item.trackingNo }}</div>
                        <div>快递公司：{{ item.shipCompanyName }}</div>
                    </div>
                </n-tab-pane>
            </n-tabs>
        </n-spin>
        <template #footer>
            <n-space  justify="end">
                <n-button size="small" @click="emit('update:show',false)">关闭</n-button>
            </n-space>
        </template>
        </n-card>
    </n-modal>
</template>
<script setup lang="ts">
import { computed, reactive, ref, onMounted } from "vue";
import { copyText } from "@/utils/clipboardUtils";
import { useMessages } from "@/hooks/useMessage";
const message = useMessages();
import { getLogisticTraces } from "@/services/api";
const { createMessageSuccess, createMessageError } = useMessages();
import { EllipseSharp } from '@vicons/ionicons5';
import { ExpressStatusText } from '../type'
const props = defineProps({
    show: {
        // 是否显示
        type: Boolean,
        default: false,
    },
    rowData: {
        type: Object,
        default: {},
    }

});
const loading = ref(false)
const currentRef = ref<number | null>(1);
const emit = defineEmits(["update:show"]);
const nameRef = ref('')
const logisticsInfo = ref([])
const logisticsCode = ref([])
const isNoLog = ref(false)
const noLogList = ref([])
const shipCompanyName = ref('')
const lastUpadtaTime = ref('')
const modalShow = computed({
get() {
    return props.show;
},
set(value) {
    emit("update:show", value);
},
});
onMounted(()=>{
    getLogisticData()
})
const getLogisticData = async()=>{
    loading.value = true
    let params ={
        id:props.rowData.id,
    }
    try {
        let res = await getLogisticTraces(params)
            if(res.isMoreNumbers){
                logisticsCode.value = res.logisticsNumbers.map(trackingNo => ({ trackingNo }));
            }else{
                logisticsCode.value = res.logisticsNumbers.map(trackingNo => ({ trackingNo }));
                nameRef.value =  logisticsCode.value[0].trackingNo
                if(res?.queryState){
                    logisticsInfo.value = res.traces
                    shipCompanyName.value = res.shipCompanyName
                    lastUpadtaTime.value = res.traces[0].acceptTime
                }else{
                    isNoLog.value = true
                    noLogList.value = res.traces
                }
            }
    } catch (error) {
        createMessageError(error)
        loading.value = false
    }finally{
        loading.value = false
    }
}
function handleClose() {
modalShow.value = false;
}
const hanelChangeTab = async(val) =>{
    logisticsInfo.value = []
    noLogList.value = []
    isNoLog .value = true
    let params ={
        id:props.rowData.id,
        logisticsNumber:val
    }
    loading.value = true
    try {
        let res = await getLogisticTraces(params)
        if(res?.queryState && res.queryState){      
            isNoLog .value = false
            logisticsInfo.value = res.traces
            shipCompanyName.value = res.shipCompanyName
            lastUpadtaTime.value = res.traces[0].acceptTime
        }else{
            isNoLog .value = true
            noLogList.value = res.traces
        }
    } catch (error) {
        createMessageError(error)
        loading.value = false
    }finally{
        loading.value = false
    }

}
</script>
  
<style scoped lang="less">
.steps {
    margin-top: 10px;
    height: 410px;
    overflow-y: scroll;
p {
    margin-bottom: 10px;
    font-size: 16px;
    color: black;
}
}
:deep(.n-step-content-header) {
    color: #6b6b6b !important;
}
.infoClass{
    padding: 10px;
    box-shadow: 0px 1px 25px 15px rgba(246, 246, 246, 0.5);
}
.companyName{
    font-weight: 600 !important;
}
.noLogListStyle{
    width: 100%;
    height: 100%;
    margin-top: 20px;

}
:deep(.n-spin-container){
    height: 100% !important;
}
</style>
