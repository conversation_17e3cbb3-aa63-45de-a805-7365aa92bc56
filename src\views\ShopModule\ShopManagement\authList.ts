import { ShopManagementAuth,OrderManagementAuth } from "@/enums/authKeys";
import { hasAuth } from "@/utils/auth/authUtils";

/** 发放福利券 */
export const hasWelfareVoucherAuth = (function () {
  return hasAuth(ShopManagementAuth.WelfareVoucher.key);
})();

/** 批量发放福利券 */
export const hasBatchWelfareVoucherAuth = (function () {
  return hasAuth(ShopManagementAuth.BatchWelfareVoucher.key);
})();

/** 编辑 */
export const hasEditAuth = (function () {
  return hasAuth(ShopManagementAuth.Edit.key);
})();

/** 查看订单 */
export const hasCheckAuth = (function () {
  return hasAuth(OrderManagementAuth.orderManagementIndex.key);
})();
