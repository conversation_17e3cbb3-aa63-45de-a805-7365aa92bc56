<template>
    <JSelect
      :value="props.value"
      :loading="isLoading"
      :onFocus="handlerFocus"
      :onBlur="handlerBlur"
      :options="welfareClassifyList"
      :onClear="handleClear"
      :multiple="isMultiple"
      :max-tag-count="maxTagCount"
      :display-quantity="props.isMultiple === false ? 0 : maxTagCount"
      @update:value="onChange"
      :style="{width: props.width+'px'}"
      placeholder="请选择福利券分类"
      :filter="customFilter"
      @scroll="handleScroll"
      @keydown.enter="handleSearch"
      :reset-menu-on-options-change="isResetSelectStatusRef"
      
    />
  </template>
  <script setup lang="ts">
  import { ref, watch, } from "vue";
  import JSelect from "@/components/JSelect/index.vue";
  import { pageCouponCate } from "@/services/api";
  import { useMessages } from "@/hooks";
  /* Props */
  const props = withDefaults(
    defineProps<{
      isImmediately?: boolean; // 是否立即加载
      value: Array<string | number> | string | number | null; // 选择的值
      isMultiple?: boolean; // 是否多选 --> 非必传，默认值为false
      forbiddenId?: Array<string> | null; // 禁用的id
      width?: string | number ; // 宽度
      isSearch?:boolean //是否用于搜索
    }>(),
    {
      isImmediately: false,
      isMultiple: false,
      forbiddenId: null,
      width:140,
      isSearch:false
    },
  );
  
  const emits = defineEmits<{
    (e: "update:value", selectValue: any): void; // 更新选择值事件
  }>();
  
  /* 提示 */
  const message = useMessages();
  
  /* 是否加载 */
  const isLoading = ref(false);
  const isResetSelectStatusRef = ref(false); // 重置选择状态
  const welfareClassifyList = ref([]); // 列表
  const maxTagCount = ref(2);
  const selectedValue = ref([]) //已经被选中的selectedValue
  
  /* 执行搜索返回的内容*/
  let recordsTotal = 1; // 总记录数
  
  const params: { data:{name:string|number},pageVO: { current: number; size: number } } = {
    data:{
      name:''
    },
    pageVO: {
      current: 1, // 当前页
      size: 100, // 每页大小
    },
  };
  
  /* 筛选、转化{label: '', value: ''} */
  function handleData(filterData: Array<{id:string,name:string}>) {
    return filterData.map((item) => ({ label: item.name, value: item.id }));
  }
  
  /* 获取福利券分类列表 */
  async function getWelfareClassifyList() {
    try {
      isLoading.value = true;
      const { total, current, size, records } = await pageCouponCate(params);
      params.pageVO.current = Number(current);
      params.pageVO.size = Number(size);
      recordsTotal = Number(total);
      // 如果是第一页
      if (params.pageVO.current == 1) {
        isResetSelectStatusRef.value = true; // 重置选择状态为true
        welfareClassifyList.value = handleData(records);
      } else {
        isResetSelectStatusRef.value = false; // 重置选择状态为false
        handleData(records).forEach(item => {
          // 如果列表中不存在该项
          if (!welfareClassifyList.value.find(temp => temp.value == item.value)) {
            // 添加到列表中
            welfareClassifyList.value.push(item);
          }
        });
      }
  
      // 判断是否有已选中内容
      if(selectedValue.value.length && props.value){
        selectedValue.value.forEach(item => {
          if (!welfareClassifyList.value.some(existingItem => existingItem.value === item.value)) {
            welfareClassifyList.value.push(item);
            selectedValue.value = []
          }
        });
      }
    } catch (error) {
      console.log("福利券分类异常：" + error);    
      
      message.createMessageError("福利券分类异常：" + error);
    } finally {
      isLoading.value = false;
    }
  }
  
  /** 自定义过滤函数 */
  function customFilter(keyword, options) {
    const labelMatch = options.label.toLowerCase().includes(keyword.toLowerCase());
    const valueMatch = options.value.toLowerCase().includes(keyword.toLowerCase());
    return labelMatch || valueMatch;
  }
  
  /** 选择值改变事件处理函数 */
  function onChange(value,label) {
    // 额外的全选逻辑
    if (props.isMultiple) {
      if (value?.includes("all")) {
        let newVal = welfareClassifyList.value.filter(item => item.value !== "all").map(item => item.value);
        emits("update:value", newVal);
        return;
      }
    }
    selectedValue.value = [label]
    emits("update:value", value);
    handleClearName()
  }
  
  /** 清空事件处理函数 */
  const handleClear = () => {
    emits("update:value", null);
    handleClearName()
  };
  
  /** 滚动事件处理函数 */
  function handleScroll(e) {
    const currentTarget = e.currentTarget as HTMLElement;
    if (currentTarget.scrollTop + currentTarget.offsetHeight >= currentTarget.scrollHeight) {
      // 如果当前页乘以每页大小小于总记录数
      if (params.pageVO.current * params.pageVO.size < recordsTotal) {
        params.pageVO.current++; // 当前页加1
        getWelfareClassifyList();
      }
    }
  }
  
  /** 聚焦事件处理函数 */
  function handlerFocus() {
    // 如果列表为空
    if (!welfareClassifyList.value.length) {
      getWelfareClassifyList();
    }
  }
  
  /** 失焦事件处理函数 */
  function handlerBlur(){
    handleClearName()
  }
  
  /** 搜索事件处理函数 */
  function handleSearch(event) {
    params.pageVO.current = 1;
    params.data.name = event.target.value;
    getWelfareClassifyList();
  }
  
  /** 清除搜索名称函数 */
  const handleClearName = () =>{
    if(params.data.name != ''){
      params.data.name = ''
      getWelfareClassifyList();
    }
  }
  
  /** 监听 */
  watch(
    () => props.isImmediately,
    newVal => {
      if (newVal) {
        getWelfareClassifyList();
      }
    },
    { immediate: true },
  );
  
  </script>
  
  <style scoped lang="less"></style>
  