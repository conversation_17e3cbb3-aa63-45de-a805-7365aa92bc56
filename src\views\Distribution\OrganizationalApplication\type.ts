import type { DepartmentType, DepartmentStatus, MemberType } from "@/enums";

export interface organizeForm {
  // 表单类型
  title: string,
  // 主键
  id: string,
  // 创建时间
  createTime: string,
  // 修改时间
  updateTime: string,
  // 编号
  code: string,
  // 名称
  name: string,
  // 父节点编号
  parentCode: string,
  // 层级。由上到下范围为 1~10
  level: number,
  // 路径。由编号和“-”组成
  path: string,
  // 是否删除。0=否；1=是
  isDeleted: number,
  // 创建者
  createBy: string,
  // 更新者
  updateBy: string,
  // 部门类型(0=经销商 1=门店，2=公司 3=直播间 4=市场 5=运营 6=大区 7=区域 8=财务)
  departmentType: DepartmentType,
  // 使用状态 0=停用，1=启用
  useStatus: DepartmentStatus,
  // 是否有子节点
  hasChildren: boolean,
  // 门店信息
  storeEntityVO?:organizeStoreEntityDTO,
  // 门店信息
  storeEntityDTO?:organizeStoreEntityDTO,
}

// 门店信息
interface organizeStoreEntityDTO{
  // 主键id
  id: string,
  // 创建时间
  createTime: string,
  // 修改时间
  updateTime: string,
  // 门店名称
  storeName: string,
  // 门店状态
  storeStatus: DepartmentStatus,
  // 门店头像
  storeAvatar: string,
  // 门店联系人
  contactName: string,
  // 联系电话
  contactPhone: string,
  // 营业时间
  businessHours: string,
  // 省
  province: string,
  // 省ID
  provinceId: string,
  // 市
  city: string,
  // 市ID
  cityId: string,
  // 区
  area: string,
  // 区ID
  districtId: string,
  // 详细地址
  addressDetail: string,
  // 店长ID
  managerId: string,
  // 地址对象
  addressOptions?: {
    provinceId: string,
    province: string,
    cityId: string,
    cityName: string,
    areaId: string,
    area: string,
    remark:string,
  }
  // 用户列表
  storeStaffRelationList?: storeStaffRelationListDTO[]
}
interface storeStaffRelationListDTO{
  // 主键ID
  id?: string,
  // 创建时间
  createTime?: string,
  // 更新时间
  updateTime?: string,
  // 门店ID
  storeId?: string,
  // 商城用户ID
  customerId: string,
  // 人员类型
  memberType: MemberType,
  // 归属ID
  memberId: string,
  // 经销商ID
  thirdDealerId?: string,
  // 公众号ID
  wxappId?: string,
  // 用户名
  nickName?:string
}

export const ApplicationTypeOption = [
  {
    label: '区域',
    value: 1
  },
  {
    label: '经销商',
    value: 2
  },
  {
    label: '门店',
    value: 3
  }
]
export enum ApplicationType {
  AREA = 1,
  DEALER = 2,
  STORE = 3
}

export const AuditStatusOption = [
  {
    label: '待审核',
    value: 0
  },
  {
    label: '审核通过',
    value: 1
  },
  {
    label: '审核不通过',
    value: 2
  }
]
export enum AuditStatus {
  WAIT = 0,
  PASS = 1,
  NOT_PASS = 2
}