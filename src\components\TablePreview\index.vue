<script setup lang="ts">
import { ref, toRef, watch } from "vue";
// import errorImgSrc from "@/assets/image/exception/picLoadError.png";
import { isArray, isString } from "@/utils/isUtils";
// import { transformMinioSrc } from "@/utils/fileUtils";
const props = withDefaults(
  defineProps<{
    src: Array<string> | string;
    type?: "image" | "video";
    name?: string;
  }>(),
  {
    type: "image",
  }
);
const imgSrcRef = toRef(props, "src");
const typeRef = toRef(props, "type");
const srcList = ref<Array<string>>([]);

const show = ref(false);

const handleClickoutside = () => {
  const element = document.querySelector('.n-image-preview-container');
  if (!element) {
    show.value = false;
  }
};

watch(
  imgSrcRef,
  (newVal) => {
    if (isArray(newVal)) srcList.value = newVal;
    else if (isString(newVal)) srcList.value = [newVal];
  },
  { immediate: true }
);
</script>

<template>
  <n-popover :show="show" placement="bottom" trigger="click" display-directive="if" @clickoutside="handleClickoutside">
    <template #trigger>
      <n-tooltip trigger="hover" v-if="name">
        <template #trigger>
          <n-tag :bordered="false" type="info" size="small" @click="show = true">
            <n-ellipsis style="max-width: 120px" :tooltip="false">
              {{ name || '-' }}
            </n-ellipsis>
          </n-tag>
        </template>
        {{ name || '-' }}
      </n-tooltip>
      <n-tag v-else :bordered="false" type="info" size="small" @click="show = true">查看{{ srcList.length }}个文件</n-tag>
    </template>
    <n-image-group v-if="typeRef === 'image'" show-toolbar-tooltip>
      <n-space>
        <n-image v-for="src in srcList" :key='src' height="80" width="80" :src="src" />
      </n-space>
    </n-image-group>
    <template v-if="typeRef === 'video'">
      <video v-for="src in srcList" :key='src' :src="src.indexOf('https://') != -1 ? src : src"
        controls width="250"></video>
    </template>
  </n-popover>
</template>