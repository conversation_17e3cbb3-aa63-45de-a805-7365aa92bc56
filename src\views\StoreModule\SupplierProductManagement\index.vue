<template>
  <TreeContentLayout :treeWidth="240">
    <template #tree-content>
      <div class="classify-container inner-page-height">
        <!-- header -->
        <div class="header">
          <n-space justify="space-between" style="margin-bottom: 12px;flex-wrap: nowrap;">
            <n-button size="small" :loading="isGetLoading" @click="refresh" class="store-button">刷 新</n-button>
          </n-space>
          <JSearchInput
            v-model:value="searchValue"
            @search="handleSearch"
            width="100%"
            size="small"
            placeholder="请输入分类名称"
            clearable
          />
        </div>
        <!-- 商品分类 tree -->
        <div class="classification-tree-container">
          <n-spin :show="isGetLoading" size="small" style="height: 100%;">
            <n-scrollbar style="height: 100%;" @scroll="handleScroll">
              <ClassificationTree
                v-model:value="model.selectedValue"
                :tree-data="treeData"
                @update:selected-keys="handleUpdateSelectKeys"
                :selected-keys="selectedKeys"
                :defaultExpandKeys="defaultExpandKeys"
              />
            </n-scrollbar>
          </n-spin>
        </div>
      </div>
    </template>
    <template #default>
      <div class="goods-container">
        <ProductList
          :cate-id="model.cateId"
          :type="3"
          @successful="isGetLoading = false"
          :name="props.goodsName"
          :is-publish="props.isPublish"
          :is-sellout="props.isSellout"
          :storeType="storeType"
        />
      </div>
    </template>
  </TreeContentLayout>
</template>

<script lang="ts" setup name="SupplierProductManagement">
import { ref, onMounted } from "vue";
import { useGetGoodsClassify } from "@/hooks/business";
import type { TreeOption } from 'naive-ui';
import { GoodsCategoryType } from "@/enums";
import type { GoodsType } from "@/enums";
/** 相关组件 */
import TreeContentLayout from "@/components/TreeContentLayout/index.vue";
import ClassificationTree from "./components/ClassificationTree.vue";
import ProductList from "./components/ProductList.vue";

const props = defineProps<{
  goodsName?: string, // 商品名
  isPublish?: '0' | '1', // 商品上架状态
  isSellout?: '0' | '1', // 是否售罄 0: 未售罄 1: 已售罄
}>();

/** 商品分类 hook */
const {
  model,
  searchValue, 
  selectedKeys, 
  isGetLoading, 
  getGoodsClassificationData, 
  handleScroll,
  refresh,
  treeData,
  storeType,
  defaultExpandKeys
} = useGetGoodsClassify(3,true);



/** 树形节点选中项发生变化时的回调函数 */
const handleUpdateSelectKeys = (keys: Array<string | number>, option: Array<TreeOption & Partial<ApiStoreModule.GoodsClassification> | null>, meta: { node: TreeOption | null, action: 'select' | 'unselect' }) => {
  // console.log("---------------树形节点点击 start--------------");
  // console.log("keys", keys);
  // console.log("option", option);
  // console.log("meta", meta);
  // console.log("---------------树形节点点击 end--------------");
  // 赋值
  if (keys.length !== 0) {
    const firstOption = option[0];
    selectedKeys.value = keys;
    model.value.cateId = firstOption?.id ?? null;
    model.value.type = firstOption?.type ?? null;
    // 加载状态
    isGetLoading.value = true;
  }
};

/** 搜索分类 */
const handleSearch = () => {
  getGoodsClassificationData(()=>{},undefined,3);
};

/** 组件挂载 */
onMounted(() => {
  getGoodsClassificationData(()=>{},undefined,3);
});
</script>

<style lang="less" scoped>
.classify-container {
  width: 100%;
  .header {
    padding: 12px;
  }
  .classification-tree-container {
    width: 100%;
    height: calc(100% - 104px);
    :deep(.n-spin-content) {
			height: 100%;
    }
  }
}

.goods-container {
  width: 100%;
  height: 100%;
}
</style>
