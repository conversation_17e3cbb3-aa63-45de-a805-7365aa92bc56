import BlankLayout from "@/layout/BlankLayout.vue";
import { RoutesName } from "@/enums/routes";
import type { RouteLocation } from "vue-router";

export const Default = {
  [RoutesName.Live]: {
    path: "live",
    component: BlankLayout,
    meta: {
      title: "直播",
    },
  },
  [RoutesName.LiveList]: {
    path: "liveList",
    component: () => import("@/views/LiveModule/LiveList/index.vue"),
    meta: {
      title: "直播列表",
      icon: "liveList-icon",
    },
  },
  [RoutesName.ViewDataStatistics]: {
    path: "viewDataStatistics",
    component: () => import("@/views/LiveModule/ViewDataStatistics/index.vue"),
    meta: {
      title: "观看数据统计",
      icon: "view-data-statistics",
    },
  },
  [RoutesName.ViewDuration]: {
    path: "viewDuration",
    component: () => import("@/views/LiveModule/ViewDuration/index.vue"),
    meta: {
      title: "观看时长汇总",
      icon: "view-duration",
    },
  },
  [RoutesName.LiveSensitiveWords]: {
    path: "liveSensitiveWords",
    component: () => import("@/views/LiveModule/LiveSensitiveWords/index.vue"),
    meta: {
      title: "直播敏感词",
      icon: "organization-management",
    },
  },
  [RoutesName.LiveDashboard]:{
    path:'/liveDashboard',
    component:() => import("@/views/LiveModule/LiveDashboard/index.vue"),
    meta:{
      title:'直播数据大屏',
      isMenu: false,
      isShow: false,
    },
    props:(route:RouteLocation) => ({
      id:route.query.id || '',
    })
  },
};
