<template>
  <JModal
    v-model:show="show"
    width="680"
    :title="Add_active?'新增导航':'编辑导航'"
    @after-leave="closeModal"
		@positive-click="_save"
		:positiveButtonProps="{
			loading: isLoading
		}"
  >
    <n-form
      ref="formRef"
      :rules="rules"
      :model="model"
      label-width="auto"
      label-placement="left"
      require-mark-placement="right-hanging"
      :style="{
          width: '100%',
        }"
    >
      <n-grid :cols="8" :x-gap="24">
        <n-form-item-gi :span="24" label="导航名称" path="name" required>
          <n-input v-model:value="model.name" style="width: 100%" :show-button="false" maxlength="19" placeholder="导航名称(最多4字)" clearable/>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="指向页面" path="pointPage" required>
          <n-select v-model:value="model.pointPage" :options="pointPageList"/>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="icon" path="imgPath" required>
          <div style="display: flex">
            <div style="white-space: nowrap;margin-left: 6px">未选中</div>
            <div  style="margin-left: 12px">
              <CustomizeUpload v-model:value="model.iconUncheckedPath" accept="image/*" :fileListSize="1" :max="1"/>
            </div>
            <div style="white-space: nowrap;margin-left: 6px">已选中</div>
            <div  style="margin-left: 12px">
              <CustomizeUpload v-model:value="model.iconCheckedPath" accept="image/*" :fileListSize="1" :max="1"/>
            </div>
            <div style="white-space: nowrap;margin-left: 4px;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;">建议尺寸：340*120(px)</div>
          </div>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="排序位置" path="sort" required>
          <n-input-number v-model:value="model.sort" style="width: 100%" :show-button="false" :min="1" :max="5" placeholder="可输入1-5" clearable/>
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </JModal>
</template>

<script setup lang="ts" name="AddorEditVideo">
import { ref, watch } from "vue";
import { useMessages } from "@/hooks";
import { mallConfiguration } from "../hooks"
import {
  getNavigationConfig
} from "@/services/api";
const {pointPageList} = mallConfiguration()
export interface AddCompanyModalProps {
  api?: (params: any) => Promise<any>;// 新增保存Api
  refreshTable?: () => void; // 刷新表格数据
  // row?: Partial<ApiStoreModule.NavigationConfig>;
  id?:string
}
const Add_active = ref<boolean>(true)
const initParams = {
  id:null,
  name: '',
  pointPage: 0,
  iconUncheckedPath: '',
  iconCheckedPath: '',
  sort: 1,
};
const model = ref({ ...initParams });

/* 提示信息 */
const message = useMessages();
/* 模态框显隐状态 */
const show = ref(false);
/* 父组件传过来的参数 */
const parameter = ref<AddCompanyModalProps>({
});
/* 接收父组件参数 */
const acceptParams = (params: AddCompanyModalProps) => {
  parameter.value = params;
  if (params.id){
    Add_active.value = false
    getNavigationConfig(params.id).then(e=>{
      model.value = e
    }).catch(err=> {
        message.createMessageError(`获取导航配置失败： ${err}`);
        show.value = false;
    })
  }
  show.value = true;
};
/* 表单规则 */
const rules = {
  sort: {
    required: true,
    trigger: ["blur", "change"],
    message: "请填写排序位置(1-5的数字)",
    validator: ()=>{
      return model.value.sort != null;
    }
  },
  name:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入导航名称(最多4个字)",
    validator: ()=>{
      return model.value.name.length >= 1 && model.value.name.length <= 4;
    }
  },
  imgPath:{
    required: true,
    trigger: ["blur", "change"],
    message: "请先请上传icon",
    validator: ()=>{
      console.log(model.value.iconUncheckedPath);
      console.log(model.value.iconCheckedPath);
      return model.value.iconUncheckedPath != '' && model.value.iconCheckedPath != '';
    }
  },
};
/* 表单实例 */
const formRef = ref(null);
/* 表单参数初始化 */
/* 清空表单 */
const formDataReset = () => {
  model.value = { ...initParams };
  Add_active.value = true
  parameter.value = {}
};
/* 关闭弹窗 */
const closeModal = () => {
  formDataReset();
};
/* 确认--保存 */
const isLoading = ref(false);
const _save = (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      try {
        isLoading.value = true;
        await parameter.value.api({
          "data": {
            "id":model.value.id?model.value.id:null,
            "pointPage": model.value.pointPage,
            "iconUncheckedPath": Array.isArray(model.value.iconUncheckedPath)?model.value.iconUncheckedPath[0]:model.value.iconUncheckedPath,
            "iconCheckedPath": Array.isArray(model.value.iconCheckedPath)?model.value.iconCheckedPath[0]:model.value.iconCheckedPath,
            "sort": model.value.sort,
            "name": model.value.name,
          }
        }).then(e=>{
          if (Add_active.value) {
            message.createMessageSuccess(`添加导航配置成功`);
          }else {
            message.createMessageSuccess(`修改导航配置成功`);
          }
        });

      } catch (e) {
        if (Add_active.value) {
          message.createMessageError(`新增导航配置失败： ${e}`);
        }else {
          message.createMessageError(`修改导航配置失败： ${e}`);
        }

      } finally {
        isLoading.value = false;
        // 弹窗取消
        show.value = false;
        // 刷新表格数据
        parameter.value.refreshTable();
      }
    }
  });
};
watch(()=>model.value.iconUncheckedPath,(newVal)=>{
  if(newVal[0] == '') model.value.iconUncheckedPath = ''
})
watch(()=>model.value.iconCheckedPath,(newVal)=>{
  if(newVal[0] == '') model.value.iconCheckedPath = ''
})
defineExpose({
  acceptParams,
});

</script>

<style scoped lang="less">
</style>
