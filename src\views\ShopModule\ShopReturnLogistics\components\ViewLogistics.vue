<template>
  <JModal
    v-model:show="show"
    width="680"
    title="物流详情"
    @after-leave="closeModal"
    negativeText="关闭"
    positiveText=""
  >
    <div class="logistics">
      <div class="logistics-top">
        <div class="item">物流公司：{{ model.shipCompany }}</div>
        <div class="item">物流单号：{{ model.trackingNo }}</div>
      </div>
      <div class="loginstics-main">
        <n-timeline size="large" icon-size="10">
          <n-timeline-item v-for="(item,index) in model.trackList" :key="item.id">
            <template #icon>
              <span class="timeline-icon" :class="index===0?'timeline-icon-action':''"></span>
            </template>
            <template #header>
              <div class="timeline-title">
                <span>{{ logisticsStateMap[item.state] }}</span>
                <span class="time">{{ item.acceptTime }}</span>
              </div>
            </template>
            <template #footer>
              <div>{{ item.acceptStation }}</div>
            </template>
          </n-timeline-item>
<!--          <n-timeline-item>-->
<!--            <template #icon>-->
<!--              <span class="timeline-icon"></span>-->
<!--            </template>-->
<!--            <template #header>-->
<!--              <div class="timeline-title">-->
<!--                <span>派件</span>-->
<!--                <span class="time">2025-05-05 10:10:10</span>-->
<!--              </div>-->
<!--            </template>-->
<!--            <template #footer>-->
<!--              <div>正在揽件</div>-->
<!--            </template>-->
<!--          </n-timeline-item>-->
        </n-timeline>
      </div>
    </div>
  </JModal>
</template>

<script setup lang="ts" name="ViewLogistics">
import { ref } from "vue";
import { useMessages } from "@/hooks";
import { getShopReturnLogisticsInfo } from "@/services/api";

enum BziType {
  /** 订单业务 */
  Order = 1,
  /** 门店退货订单 */
  ReturnOrder = 2,
}

enum LogisticsStateType {
  // 暂无轨迹信息
  NoTrackInfo = "0",
  // 已揽收
  Collected = "1",
  // 在途中
  InTransit = "2",
  // 签收
  Signed = "3",
  // 问题件
  Problem = "4",
  // 转寄
  Transferred = "5",
  // 清关
  Clearance = "6",
}

const logisticsStateMap = {
  [LogisticsStateType.NoTrackInfo]: "暂无轨迹信息",
  [LogisticsStateType.Collected]: "已揽收",
  [LogisticsStateType.InTransit]: "在途中",
  [LogisticsStateType.Signed]: "签收",
  [LogisticsStateType.Problem]: "问题件",
  [LogisticsStateType.Transferred]: "转寄",
  [LogisticsStateType.Clearance]: "清关",
};

type TrackItem = {
  id: string;
  acceptTime: string;
  acceptStation: string;
  state: string;
};
type InitLogisticsInfoType = {
  orderCode: string;
  shipCompany: string;
  shipCompanyCode: string;
  trackingNo: string;
  trackList: TrackItem[];
}
const initLogisticsInfo = {
  orderCode: "",
  shipCompany: "",
  shipCompanyCode: "",
  trackingNo: "",
  trackList: [],
};
const model = ref<InitLogisticsInfoType>({ ...initLogisticsInfo });
/* 提示信息 */
const { createMessageError } = useMessages();
/* 模态框显隐状态 */
const show = ref(false);
const acceptParams = params => {
  show.value = true;
  model.value.orderCode = params?.id ?? "";
  model.value.shipCompany = params?.logisticsCompany ?? "";
  model.value.shipCompanyCode = params?.logisticsCode ?? "";
  model.value.trackingNo = params?.trackingNumber ?? "";
  getLogisticsInfo();
};
const logisticsDataReset = () => {
  model.value = { ...initLogisticsInfo };
};
/* 关闭弹窗 */
const closeModal = () => {
  logisticsDataReset();
  // 弹窗取消
  show.value = false;
};
/* 确认--保存 */
const isLoading = ref(false);

/** 获取物流信息*/
const getLogisticsInfo = () => {
  isLoading.value = true;
  const params = {
    orderCode: model.value?.orderCode,
    trackingNo: model.value?.trackingNo,
    shipCompanyCode: model.value?.shipCompanyCode,
    bizType: BziType.ReturnOrder,
  };
  getShopReturnLogisticsInfo(params).then(res => {
    model.value.trackList = res ?? [];
  }).catch(err => {
    createMessageError(`获取物流信息失败:${err}`);
  }).finally(() => {
    isLoading.value = false;
  });
};

defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less">
.timeline-icon {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #999999;
}

.timeline-icon-action {
  background-color: #00b42a;
}

.logistics {
  .loginstics-main {
    margin-top: 20px;
    max-height: 500px;
    overflow: auto;

    .timeline-title {
      font-size: 16px;

      .time {
        color: #999999;
        padding-left: 10px;
      }
    }
  }
}
</style>
