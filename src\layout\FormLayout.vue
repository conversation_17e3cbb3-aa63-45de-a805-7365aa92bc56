<script setup lang="ts">
import {
  ref,
  toRefs,
  onMounted,
  toRef,
  watch,
  nextTick,
  onUnmounted,
  useSlots,
} from "vue";
import LTable from "@/components/LTable/index.vue";
import type { HeaderData, PaginationProps } from "@/components/LTable/type";
import type {
  DataTableBaseColumn,
  DataTableColumn,
  DataTableCreateSummary,
  DataTableFilterState,
} from "naive-ui";
import { isVue3SlotExist } from "@/utils/isUtils";

interface LayoutTableProps {
  tableHeaderData?: HeaderData[];
  isLoading?: boolean;
  tableColumns: Array<any>;
  tableData: Array<object | null>;
  pagination?: PaginationProps;
  isNeedCollapse?: boolean;
  useTabs?: boolean;
  defaultCheckedKeys?: Array<string | number>;
  isBatchDelete?: boolean;
  tableSummary?: object;
  tableKey?: string;
  customCols?: boolean;
  isTableSelection?: boolean;
  isUseBreadcrumb?: boolean;
  isTablePagination?:boolean;
  isDisplayIndex?:boolean;
  isDisplayHeader?:boolean;
  defaultExpandAll?:boolean;
  tableRowKey?:string;
  isUseOverview?:boolean;
  rowProps?:Function;
  isRowClick?:boolean;
  /** 是否使用 合计渲染*/
  totalRendering?:boolean
}

const props = withDefaults(defineProps<LayoutTableProps>(), {
  isLoading: false,
  isNeedCollapse: true,
  isBatchDelete: false,
  customCols: false,
  isTableSelection: true,
  isUseBreadcrumb: false,
  isTablePagination:true,
  isDisplayIndex:true,
  isDisplayHeader:true,
  defaultExpandAll:false,
  tableRowKey:'id',
  isUseOverview:false,
  isRowClick:false,
  totalRendering:true
});
const emits = defineEmits<{
  (e: "paginationChange", pagination: Object): void;
  (
    e: "selectedKeysChange",
    selectedKeys: Array<string | number>,
    tableData: Array<object>
  ): void;
  (e: "update:collapse", collapse: boolean): void;
  (
    e: "filtersChange",
    {}: {
      filters: DataTableFilterState;
      initiatorColumn: DataTableBaseColumn;
      searchParams: Object;
    }
  ): void;
  (e: "batchDelete", selectedKeys: Array<string | number>): void;
  (e: "tableSorterChange", value: object): void;
  (e: "selectedKeysChangeInfo",
    keys: Array<string | number>,
    rows: object[],
    meta: {
      row: object | undefined,
      action: 'check' | 'uncheck' | 'checkAll' | 'uncheckAll'
    }
  ): void; // checked-row-keys 值改变时触发的回调函数
}>();
const {
  tableColumns,
  tableData,
  isLoading,
  isNeedCollapse,
  pagination,
  defaultCheckedKeys,
  customCols,
  tableKey,
  tableSummary,
  isBatchDelete,
  isUseBreadcrumb,
  isTablePagination,
  isDisplayIndex,
  isDisplayHeader,
  defaultExpandAll,
  rowProps,
  isRowClick,
} = toRefs(props);
watch(pagination, (newVAl) => {}, { immediate: true });
const tableHeaderData = toRef(props, "tableHeaderData");
const collapseFormRef = ref(null);
const LTableHeight = ref(null);

/** 多选数据 */
const isSelected = ref<boolean>(false);
const selectedList = ref<{ [key: string]: any }[]>([]);
const selectedListIds = ref<string[]>([]);

const calcTableWrapperHeight = () => {
  // const {domRef:collapseDom} = collapseFormRef.value
  // const collapseDomMarginBottom = 10
  // LTableHeight.value = `calc(100% - ${collapseDom.offsetHeight}px - ${collapseDomMarginBottom}px)`
  LTableHeight.value = `100%`;
};
const onCollapseChange = (isCollapse) => {
  calcTableWrapperHeight();
  emits("update:collapse", isCollapse);
};
const paginationChange = (pageInfo) => {
  emits("paginationChange", pageInfo);
};
const selectedKeysChange = (keys, tableData) => {
  emits("selectedKeysChange", keys, tableData);
  keys.length ? (isSelected.value = true) : (isSelected.value = false);
	selectedListIds.value = keys;
	selectedList.value = tableData;
};
const selectedKeysChangeInfo = (keys, rows, meta) => {
  emits("selectedKeysChangeInfo", keys, rows, meta);
}
const filtersChange = (params: {
  filters: DataTableFilterState;
  initiatorColumn: DataTableBaseColumn;
  searchParams: Object;
}) => {
  emits("filtersChange", params);
};
const batchDelete = (selectedKeys) => {
  emits("batchDelete", selectedKeys);
};
const createFormLayoutResizeObserver = () => {
  return new ResizeObserver(() => {
    calcTableWrapperHeight();
  });
};
function handleTableSorterChange(sorterInfo) {
  emits("tableSorterChange", sorterInfo);
}
const formLayoutRef = ref(null);
const formLayoutResizeObserver = createFormLayoutResizeObserver();
/**  DOM 更新循环结束之后执行延迟回调 */
nextTick()
  .then(() => {
    setTimeout(() => {
      try {
        calcTableWrapperHeight();
        // 观察表格是否调整大小
        if (formLayoutRef.value) {
          formLayoutResizeObserver.observe(formLayoutRef.value as HTMLElement);
        }
      } catch (error) {
        // 处理或者抛出错误
        console.error(error);
      }
    }, 0);
  })
  .catch((error) => {
    // 处理或者抛出 nextTick 的错误
    console.error(error);
});
onUnmounted(() => {
  formLayoutResizeObserver.disconnect();
});

/** 监听表格数据 */
watch(() => props.tableData, () => {
	isSelected.value = false;
	selectedListIds.value = [];
	selectedList.value = [];
});

defineExpose({ calcTableWrapperHeight });
</script>

<template>
  <div class="default-bg" ref="formLayoutRef">
    <!-- <CollapseSearchForm class="search-form-wrapper" :isNeedCollapse="isNeedCollapse" @update:collapse="onCollapseChange" ref="collapseFormRef">
      <template #default>
        <slot name="searchForm"></slot>
      </template>
      <template #hidden>
        <slot name="searchDetailForm"></slot>
      </template>
      <template #button>
        <slot name="searchBtn"></slot>
      </template>
    </CollapseSearchForm> -->
    <template v-if="props.useTabs">
      <slot name="tabs" :height="LTableHeight" />
    </template>
    <template v-else>
      <LTable
        :tableKey="tableKey"
        :height="LTableHeight"
        :headerData="tableHeaderData"
        :isLoading="isLoading"
        :tableColumns="tableColumns"
        :tableData="tableData"
        :pagination="pagination"
        :isBatchDelete="isBatchDelete"
        :defaultCheckedKeys="defaultCheckedKeys"
        @paginationChange="paginationChange"
        @selectedKeysChange="selectedKeysChange"
        @selected-keys-change-info="selectedKeysChangeInfo"
        @filtersChange="filtersChange"
        @batchDelete="batchDelete"
        :summary="tableSummary"
        :customCols="customCols"
        :isSelection="isTableSelection"
        :isUseBreadcrumb="isUseBreadcrumb"
        @sorterChange="handleTableSorterChange"
        :is-pagination="isTablePagination"
        :isDisplayIndex="isDisplayIndex"
        :isDisplayHeader="isDisplayHeader"
        :defaultExpandAll="defaultExpandAll"
        :row-key="props.tableRowKey"
        :row-props="rowProps"
        :isRowClick="isRowClick"
        :isUseOverview="props.isUseOverview"
        :totalRendering="props.totalRendering"
      >
        <template #headerLeftBtn>
          <!-- <slot name="tableHeaderLeftBtn"></slot> -->
          <slot name="searchForm"></slot>
        </template>
        <template #btn>
          <slot 
            name="tableHeaderBtn"
            :selected-list-ids="selectedListIds"
						:selected-list="selectedList"
						:is-selected="isSelected"
          ></slot>
        </template>
        <template #footer-btn>
          <slot 
            name="tableFooterBtn"
            :selected-list-ids="selectedListIds"
						:selected-list="selectedList"
						:is-selected="isSelected"
          ></slot>
        </template>
        <template #breadcrumb>
          <!-- <slot name="tableHeaderLeftBtn"></slot> -->
          <slot name="tableBreadcrumb"></slot>
        </template>
        <template #overview>
          <slot name="tableOverview"></slot>
        </template>
      </LTable>
    </template>
  </div>
</template>

<style lang="less" scoped>
@import "@/styles/default.less";

.default-bg {
  height: 100%;
  max-height: @inner-bg-height;
  background-color: @blank-background-color;
  width: 100%;
}

.search-form-wrapper {
  margin-bottom: @default-padding-md;
}

.n-checkbox-box__border{
  width: 18px;
  height: 18px;
}
</style>
