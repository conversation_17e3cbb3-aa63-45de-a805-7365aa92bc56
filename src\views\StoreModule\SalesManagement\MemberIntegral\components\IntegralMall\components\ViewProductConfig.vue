<template>
  <JDrawer
    v-model:show="show"
    title="查看商品配置"
    :isGetLoading="isGetLoading"
    @after-leave="handleAfterLeave"
    @after-enter="handleAfterEnter"
    :to="props.to"
  >
    <!-- 基本信息 -->
    <template #content>
      <n-form
        ref="formRef"
        :model="model"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
        size="small"
        :style="{ width: '100%' }"
      >
        <n-grid :cols="12" :x-gap="12">
          <!-- 基本配置 -->
          <n-gi :span="12">
            <div class="title-wrapper">
              <div class="title-line"></div>
              <span>基本配置</span>
            </div>
          </n-gi>
          <n-form-item-gi :span="12" label="任务内容">
            <span class="explain">查看指定商品</span>
          </n-form-item-gi>
          <n-form-item-gi :span="12" label="规则说明">
            <span class="explain">查看指定商品到规定时间可获得积分奖励</span>
          </n-form-item-gi>
          <n-form-item-gi :span="12" label="配置获取积分说明">
            <span class="explain">
              可更换数据，输入数据点击保存配置，前台相应数据发生改变。录入的数据必须大于0，且为整数。
            </span>
          </n-form-item-gi>
          <n-form-item-gi :span="3" label="配置获取积分">
            <n-input-number v-model:value="model.points" :min="MINPOINT" :precision="0" :show-button="false" />
          </n-form-item-gi>
          <n-form-item-gi :span="3" label="配置查看时间">
            <n-input-number v-model:value="model.viewDuration" :min="MINVIEWTIME" :precision="0" :show-button="false">
              <template #suffix>s</template>
            </n-input-number>
          </n-form-item-gi>
          <!-- 配置商品 -->
          <n-gi :span="12">
            <div class="title-wrapper">
              <div class="title-line"></div>
              <span>配置商品（用户随机跳转已启用一款商品的详情页,未启用不被跳转）</span>
            </div>
          </n-gi>
          <n-gi :span="12">
            <!-- 商品配置 -->
            <ViewProductTable ref="viewProductTableRef" />
          </n-gi>
        </n-grid>
      </n-form>
    </template>
    <!-- Footer -->
    <template #footer>
      <div class="footer-wrapper">
        <!-- 商品是否上架 -->
        <JCheckbox v-model:checked="model.isEnabled" style="margin-left: 24px;">
          <span style="font-size: 16px;">启用查看商品配置</span>
        </JCheckbox>
        <n-space>
          <n-button @click="setFalse">取消</n-button>
          <n-button type="primary" :loading="isSaveLoading" @click="_save">保存</n-button>
        </n-space>
      </div>
    </template>
  </JDrawer>
</template>

<script lang="ts" setup name="ViewProductConfig">
import { ref } from "vue";
import { getIntegralConfig, addIntegralConfig, updateIntegralConfig } from "@/services/api";
import { useMessages, useBoolean, useLoading } from '@/hooks';
import { IntegralConfigType } from "@/enums";
import { MINVIEWTIME, MINPOINT } from "../../../types";
/** 相关组件 */
import JDrawer from "@/components/JDrawer/index.vue";
import ViewProductTable from "./ViewProductTable.vue";

const { createMessageSuccess, createMessageError } = useMessages();

/** props */
const props = withDefaults(defineProps<{
  to?: string; // 弹窗位置
  refreshTable?: () => void; // 刷新表格数据
}>(), {
  to: '#integral-mall'
});

/** 显隐 */
const { bool: show, setTrue, setFalse } = useBoolean(false);

/* 表单实例 */
const formRef = ref();

/* 表单参数初始化 */
const initParams = {
  id: undefined, // 积分配置ID
  points: null, // 积分
  viewDuration: null, // 查看时间
  isEnabled: false, // 是否启用配置
};
const model = ref({ ...initParams });

const acceptParams = () => {
  setTrue();
};

/** 关闭抽屉回调 */
const handleAfterLeave = () => {
  // 初始化参数
  model.value = { ...initParams };
};

/** 抽屉出现后的回调 */
const { loading: isGetLoading, startLoading, endLoading } = useLoading(false);
const handleAfterEnter = async () => {
  try {
    startLoading();
    const { id, points, isEnabled, viewDuration } = await getIntegralConfig(IntegralConfigType.VIEWPRODUCT);
    if (!id) return;
    Object.assign(model.value, {
      id: id ?? undefined,
      points: points ?? null,
      viewDuration: viewDuration ?? null,
      isEnabled: isEnabled ? true : false,
    });
  } catch (error) {
    createMessageError('获取每日任务-查看商品积分配置失败：' + error);
  } finally {
    endLoading();
  }
};

/** 配置商品实例 */
const viewProductTableRef = ref<InstanceType<typeof ViewProductTable> | null>(null);

/** 校验商品配置是否启用 */
function checkIsPublish(products) {
  for (let product of products) {
    if (product.isPublish !== 0) {
      return false;
    }
  }
  return true;
};

/* 确认--保存 */
const { loading: isSaveLoading, startLoading: startSaveLoading, endLoading: endSaveLoading } = useLoading(false);
const _save = async (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      try {
        startSaveLoading();
        const { id, isEnabled, points, viewDuration } = model.value;
        // 启用时，校验数据
        if (isEnabled) {
          if (!points || !viewDuration) {
            createMessageError('请录入数据！');
            return;
          };
          const tableData = viewProductTableRef.value?.tableData;
          if (!tableData.length || checkIsPublish(tableData)) {
            createMessageError('配置商品为空或配置的商品全部未启用，请配置商品！');
            return;
          }
        }
        //保存配置
        if (!id) {
          // 新增
          await addIntegralConfig({
            id,
            source: IntegralConfigType.VIEWPRODUCT,
            points,
            viewDuration,
            isEnabled: isEnabled ? 1 : 0,
          });
          createMessageSuccess('新建每日任务-查看商品积分配置成功');
        } else {
          // 编辑
          await updateIntegralConfig({
            source: IntegralConfigType.VIEWPRODUCT,
            points,
            viewDuration,
            isEnabled: isEnabled ? 1 : 0,
          });
          createMessageSuccess('编辑每日任务-查看商品积分配置成功');
        }
      } catch (error) {
        createMessageError('保存每日任务-查看商品积分配置失败：' + error);
      } finally {
        endSaveLoading();
      }
    }
  });
};

defineExpose({
  acceptParams,
});
</script>

<style lang="less" scoped>
@import "../styles/index.less";

:deep(.n-scrollbar-rail) {
  bottom: 8px !important;
}
</style>
