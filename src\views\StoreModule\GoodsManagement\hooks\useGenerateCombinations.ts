import type { SpecList } from "@/views/StoreModule/GoodsManagement/components/PriceAndStock/types";

export default function useGenerateCombinations() {
    // 生成笛卡尔积的函数
const generateCombinations = (specList: SpecList[]) => {
  if (!specList.length) return []

  const result: Array<{attributeValue: string, id: string}[]> = [[]]

  for (const spec of specList) {
    const newResult: Array<{attributeValue: string, id: string}[]> = []
    for (const combination of result) {
      for (const value of spec.specValue) {
        // 修改：移除空值过滤，保留所有规格值
        newResult.push([...combination, { 
          attributeValue: value.attributeValue || '', // 空值用空字符串
          id: value.id 
        }])
      }
    }
    result.splice(0, result.length, ...newResult)
  }

  return result
}

return {
    generateCombinations
}
}
