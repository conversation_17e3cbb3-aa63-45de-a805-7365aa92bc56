<template>
  <MenuLayout v-model:activeKey="activeTypeRef" :menuOptions="menuList">
    <component :is="currentPage" />
  </MenuLayout>
</template>

<script setup lang="tsx">
import { ref, watch, computed } from "vue";
import { ContentTypeEnum } from "@/enums/systemEnum";
import VideoConfig from "./modules/VideoConfig/index.vue"
import MenuLayout from "@/components/MenuLayout/index.vue";
import { hasContentConfigVideoConfig } from "@/views/ContentModule/ContentConfig/authList";
/** props */
const props = defineProps<{
  configurationAddressStatus?: number, // 配置地址跳转状态
}>();

const activeTypeRef = ref(ContentTypeEnum.VideConfig);

/** 获取到的标签数据 */
const menuList = ref([
  {
    label: '视频配置',
    key: ContentTypeEnum.VideConfig,
    show:hasContentConfigVideoConfig
  }
]);

/** 相关组件 */
const pageMap = {
  [ContentTypeEnum.VideConfig]: VideoConfig,
}

/** 当前页 */
const currentPage = computed(() => pageMap[activeTypeRef.value])

/** 监听 */
watch(()=>{
  return props.configurationAddressStatus
},(newVal)=>{
},{immediate:true})
</script>

<style lang="less" scoped></style>
