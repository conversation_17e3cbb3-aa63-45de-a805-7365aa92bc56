<template>
  <FormLayout
    :isLoading="isLoading"
    :tableData="tableData"
    :tableColumns="tableColumns"
    :isNeedCollapse="false"
    :isDisplayIndex="false"
    :pagination="paginationRef"
    @paginationChange="paginationChange"
    @filtersChange="filtersChange"
  >
    <template #searchForm>
      <NForm
        ref="formRef"
        label-placement="left"
        label-width="auto"
        :show-feedback="false"
        require-mark-placement="right-hanging"
        size="small"
        :style="{ width: '100%' }"
      >
        <!-- 福利品名称输入框 -->
        <NFormItem label="福利品名称">
          <JSearchInput
            v-model:value="model.couponName"
            placeholder="请输入福利品名称"
            @search="handlerSearch"
          />
        </NFormItem>
        <!-- 上架状态选择器 -->
        <NFormItem label="上架状态">
          <NSelect style="width: 200px;" v-model:value="model.status" :options="WelfareGoodsStateOptions" clearable />
        </NFormItem>
        <NFormItem label="spu编码">
          <JSearchInput
            v-model:value="model.spu"
            placeholder="请输入spu编码"
            @search="handlerSearch"
          />
        </NFormItem>
      </NForm>
    </template>

    <!-- 表格顶部操作按钮 -->
    <template #tableHeaderBtn>
      <NButton @click="refresh" class="store-button">刷 新</NButton>
      <NButton v-if="hasWelfareGoodsAddAuth" type="primary" @click="openCreateWelfareGoods('add')">新建福利品</NButton>
    </template>

    <!-- 表格底部按钮 -->
    <template #tableFooterBtn="scope" v-if="hasWelfareGoodsPublishAuth">
		  <!-- 批量上架 -->
		  <n-popconfirm @positive-click="handleBatchUnmountGoods(scope.selectedListIds, scope.selectedList, '1')" :positive-button-props="{
        loading: isBatchLoading
      }">
		  	<template #trigger>
		  		<n-button ghost type="primary" size="small">批量上架</n-button>
		  	</template>
		  	此操作将上架选中的商品，是否继续？
		  </n-popconfirm>
		  <!-- 批量下架 -->
		  <n-popconfirm @positive-click="handleBatchUnmountGoods(scope.selectedListIds, scope.selectedList, '0')" :positive-button-props="{
        loading: isBatchLoading
      }">
		  	<template #trigger>
		  		<n-button ghost type="error" size="small">批量下架</n-button>
		  	</template>
		  	此操作将下架选中的商品，是否继续？
		  </n-popconfirm>
    </template>
  </FormLayout>

  <!-- 新建福利品弹窗组件 -->
  <CreateWelfareGoods ref="createWelfareGoodsRef" @refresh="refresh" />
</template>

<script setup lang="tsx">
import { ref, h, watch } from "vue";
import { useMessages } from "@/hooks";
import { useTableDefault } from "@/hooks/useTableDefault";
import { WelfareGoodsStateLabels, WelfareGoodsStateOptions } from "@/constants";
import { pageCouponProduct, batchUnmountCouponProduct } from "@/services/api";
import { hasWelfareGoodsAddAuth,hasWelfareGoodsEditAuth,hasWelfareGoodsPublishAuth } from "../../authList";
/** 相关组件 */
import FormLayout from "@/layout/FormLayout.vue";
import TablePreview from "@/components/TablePreview/index.vue";
import CreateWelfareGoods from "./CreateWelfareGoods.vue";

defineOptions({
  name: "WelfareGoodsPage",
});

/** props */
const props = defineProps<{
  cateId: string;
}>();

/** emits */
const emits = defineEmits<{
  (e: 'refresh'): void;
}>();

const { createMessageError, createMessageSuccess } = useMessages();
/** 表格方法Hook */
const {
  isLoading,
  tableData,
  paginationRef,
  pageTableData,
  deleteTableData,
  editTableData,
  addTableData,
  refreshTableData,
  paginationChange,
} = useTableDefault({
  pageDataRequest: pageCouponProduct,
});

/** 表格列配置 */
const tableColumns = [
  {
    title: "图片",
    key: "img",
    align: "left",
    fixed: "left",
    width: 120,
    render: (row) => {
      if (row?.couponProductImgDTOList?.length > 0) {
        let paths = row?.couponProductImgDTOList.map(item => item.path);
        return <TablePreview src={paths} />;
      }
      return '-';
    },
  },
  {
    title: "昵称",
    key: "nickname",
    align: "left",
    width: 100,
    render: (row) => {
      let title = `${row.frontName ?? ''}`;
      return <table-tooltip row={row} nameKey="name" title={title} idKey="id" />;
    },
  },
  {
    title: "兑换条件",
    key: "condition",
    align: "left",
    width: 100,
    render: (row) => {
      const item = row.couponProductSpecList;
      if (Array.isArray(item)) {
        return h('div', { style: 'white-space: pre-line;' },
          item.map((spec, index) =>
            `规格${index + 1}：${spec.exchangeCount}张${spec.couponCateName}`
          ).join('； ')
        );
      }
    },
  },
  {
    title: "已售",
    key: "soldQty",
    align: "left",
    width: 100,
    render: (row) => {
       if (row?.couponProductSpecList?.length > 0) {
        // 计算 soldQty 的总值
        const totalAvailStocks = row?.couponProductSpecList.reduce((total, item) => total + item.soldQty, 0);
        return <span>{totalAvailStocks}</span>;
      }
    },
  },
  {
    title: "库存",
    key: "availStocks",
    width: 150,
    align: "left",
    render: row => {
      if (row?.couponProductSpecList?.length > 0) {
        // 计算 availStocks 的总值
        const totalAvailStocks = row?.couponProductSpecList.reduce((total, item) => total + item.availStock, 0);
        return <span>{totalAvailStocks}</span>;
      }
    }
  },
  {
    title: "上架",
    key: "status",
    align: "left",
    width: 100,
    render: (row) => (
      <n-flex>
        <n-tag
          bordered={false}
          size="small"
          type={row.status ? "success" : "error"}
        >
          {WelfareGoodsStateLabels[row.status]}
        </n-tag>
      </n-flex>
    ),
  },
  {
    title: "操作",
    key: "action",
    width: 150,
    align: "left",
    fixed: "right",
    render: rowData => (
      <n-flex align="center" justify="center">
        {/* 条件渲染编辑按钮 */}
        {hasWelfareGoodsEditAuth && (
          <n-button
            text
            size="small"
            type="primary"
            onClick={() => openCreateWelfareGoods('edit', rowData)}
          >
            编辑
          </n-button>
        )}
      </n-flex>
    ),
  },
];

/** 初始化查询参数 */
const initParams = {
  couponName: null,
  status: null,
  isCategory: null,
  cateId: null,
  spu:null
};
const model = ref({ ...initParams });

/** 搜索 */
const handlerSearch = () => {
  tableSearch();
};

/** 获取查询参数 */
const getParams = () => ({ ...model.value });

/** 表格查询方法 */
const tableSearch = async () => {
  await pageTableData(getParams(), paginationRef.value);
  emits('refresh');
};

/** 筛选条件变化处理 */
const filtersChange = ({ searchParams }) => {
  model.value = { ...model.value, ...searchParams };
  tableSearch();
};

/** 刷新表格 */
const refresh = () => tableSearch();

/** 批量上下架 */
const isBatchLoading = ref(false);
const handleBatchUnmountGoods = async (rowListIds: Array<string>, rowList: Array<any>,  isPublish: '0' | '1') => {
  try {
    isBatchLoading.value = true;
    let cateListIds = rowList.map(item => item?.categoryId).reduce((acc, current) => {
      if (!acc.includes(current)) {
        acc.push(current);
      }
      return acc;
    }, []);
    let _params = {
      productIds: rowList.map(item => item?.id).join(',') || '',
      isPublish,
      cateIds: cateListIds.join(','),
    };
    await batchUnmountCouponProduct(_params);
    createMessageSuccess('操作成功');
    refresh();
  } catch (error) {
    createMessageError('操作成功失败：' + error);
  } finally {
    isBatchLoading.value = false;
  }
};

/** 打开新建/编辑福利品弹窗 */
const createWelfareGoodsRef = ref<InstanceType<typeof CreateWelfareGoods> | null>(null);
const openCreateWelfareGoods = (type: 'add' | 'edit', row?: object) => {
  createWelfareGoodsRef.value?.acceptParams({
    categoryId: null,
    type,
    row,
    productTypes: null
  });
};

/** 监听 */
watch(() => props.cateId, (newVal) => {
  // 关闭抽屉
  createWelfareGoodsRef.value?.close();
  model.value.cateId = newVal;
  tableSearch();
}, { immediate: true });

/** 监听 */
watch(() => [model.value.status], (newVal) => {
  if (newVal) {
    tableSearch();
  }
});
</script>

<style lang="less" scoped>
@import "@/styles/default.less";
:deep(.tagId .n-base-selection-input__content) {
  font-size: 11px;
}
</style>
