import {defHttp} from '@/services';

/** 机构管理 */
export const enum OrganizationApi {
    /** 机构列表 */
    organizationList = '/institutionManagements/page',
    /** 新增机构 */
    addOrganization = '/institutionManagements/add',
    /** 编辑机构 */
    editOrganization = '/institutionManagements/update',
    /** 删除机构 */
    deleteOrganization = '/institutionManagements/delete',
}

/** 机构列表 */
export function organizationList(params) {
    return defHttp.post({
        url: OrganizationApi.organizationList,
        params
    })
}

/** 新增机构 */
export function addOrganization(params) {
    return defHttp.post({
        url: OrganizationApi.addOrganization,
        params
    })
}

/** 编辑机构 */
export function editOrganization(params) {
    return defHttp.put({
        url: OrganizationApi.editOrganization,
        params
    })
}

/** 删除机构 */
export function deleteOrganization(params) {
    return defHttp.delete({
        url: OrganizationApi.deleteOrganization,
        requestConfig:{
            isQueryParams:true
        },
        params
    })
}
