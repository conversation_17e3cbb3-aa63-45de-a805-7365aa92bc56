<!-- 经销商分页下拉组件 -->
<template>
  <n-input-group
    :style="{
			width: isString(props.width) ? props.width : `${props.width}px`,
		}"
  >
    <JDealerGroupsSelect
      size="small"
      v-if="props.isCascade"
      v-model:value="selectedGroupIdList"
      :disabled="props.isDisabled"
      :clearable="props.isClearable"
      :defaultValue="props.defaultDealerGroupIds"
      :displayNoGroupOption="true"
      :is-default-expand-all="props.isDefaultExpandAll"
      :is-delete-option="props.isDeleteOption"
      :style="{width: props.proportion[0]}"
    />
    <JSelect
      size="small"
      :value="props.value"
      :loading="isLoading"
      :onFocus="handlerFocus"
      :options="dealerList"
      :onClear="handleClear"
      @update:value="onChange"
      placeholder="请选择经销商"
      @scroll="handleScroll"
      @update:show="handleDropdownToggle"
      :reset-menu-on-options-change="isResetSelectStatusRef"
      :multiple="props.isMultiple"
      :max-tag-count="maxTagCount"
      :display-quantity="props.isMultiple === false ? 0 : maxTagCount"
      :disabled="props.isDisabled"
      :clearable="props.isClearable"
      :filterable="!props.filterable"
      :closable="props.isClosable"
      :style="{width: props.proportion[1]}"
    >
      <template #header v-if="props.filterable">
        <p class="header-notice">
          数据范围：{{ (isNullOrUnDef(asyncSearchInputValRef) || isEmpty(asyncSearchInputValRef)) ? '全部' : asyncSearchInputValRef }}
        </p>
      </template>
      <template v-if="props.filterable" #action>
        <div style="display:flex;align-items: center;">
          <n-input
            ref="searchInputRef"
            v-model:value="searchInputValRef"
            placeholder="请输入关键字"
            @keydown.enter="handleSearch"
            @update:value="handleInputChange"
            clearable
            size="small"
          />
          <n-button v-if="isTouchDevice()" style="margin-left: 10px;" size="tiny" type="primary" @click="handleSearch">
            搜索
          </n-button>
        </div>
      </template>
    </JSelect>
  </n-input-group>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from "vue";
import JSelect from "@/components/JSelect/index.vue";
import { getSgDealerInfo } from "@/services/api";
import { isArray, isString, isTouchDevice, isNullOrUnDef, isEmpty } from "@/utils";
import { useMessages } from "@/hooks";
import JDealerGroupsSelect from "./JDealerGroupsSelect.vue";

/* 提示 */
const message = useMessages();

/* Props */
const props = withDefaults(
	defineProps<{
		isImmediately?: boolean; // 是否立即加载
		isMultiple?: boolean; // 是否多选 --> 非必传，默认值为false
		isCascade?: boolean; // 是否级联选择 --> 非必传，默认值为true
		value: Array<string | number> | string | number | null; // 选择的值
		isClearable?: boolean;// 是否可清除选项
		width?: string | number;
		defaultDealerGroupIds?: Array<string | number> | string | number | null; // 默认经销商分组Id
		isDisabled?: boolean; // 是否禁用
		isClosable?: boolean; // 是否可删除
		isDefaultExpandAll?: boolean; // 是否展开经销商分组
		isDeleteOption?: boolean; // 是否可删除选项
		filterable?: boolean; // 是否可过滤
		proportion?: [string, string]; // 样式占比 默认['50%', '50%']
	}>(),
	{
		isImmediately: false,
		isMultiple: false,
		isCascade: true,
		isClearable: true,
		width: 420,
		isDisabled: false,
		isClosable: true,
		isDefaultExpandAll: false,
		isDeleteOption: true,
		filterable: true,
		proportion: () => ['48%', '62%']
	},
);

/** emits */
const emits = defineEmits<{
	(e: "update:value", selectValue: any): void; // 更新选择值事件
}>();

/* 是否加载 */
const isLoading = ref(false);
const isResetSelectStatusRef = ref(false); // 重置选择状态
const dealerList = ref([]); // 经销商列表
let _resultTempList = []; // 临时结果列表
const maxTagCount = ref(1);
const selectedGroupIdList = ref(null); // 级联选择
const searchInputValRef = ref<string>('');
const asyncSearchInputValRef = ref<string>('');

/* 执行搜索返回的内容*/
let recordsTotal = 1; // 总记录数

/** 经销商分页参数 */
const params: {
	data: any;
	pageVO: { current: number; size: number };
} = {
	data: {
		searchValue: "",
		groupIdList: props.defaultDealerGroupIds,
	},
	pageVO: {
		current: 1, // 当前页
		size: 500, // 每页大小
	},
};

/** 处理数据 */
function handleData(filterData: Array<any>) {
	let dataList = [];
	dataList = filterData.map((item) => {
		return {
			label: `${item.name ?? item.nickname ?? '-'}` + ":" + (item.mobile ?? item.id),
			value: item.id
		};
	});
	return dataList;
}

/* 获取经销商列表 */
async function pageDealerList() {
	try {
		isLoading.value = true;
		const { total, current, size, records } = await getSgDealerInfo(params);
		params.pageVO.current = Number(current);
		params.pageVO.size = Number(size);
		recordsTotal = Number(total);
		// 如果是第一页
		if (params.pageVO.current == 1) {
			isResetSelectStatusRef.value = true; // 重置选择状态为true
			dealerList.value = handleData(records);
		} else {
			isResetSelectStatusRef.value = false; // 重置选择状态为false
			handleData(records).forEach((item) => {
				// 如果列表中不存在该项
				if (!dealerList.value.find((temp) => temp.value == item.value)) {
					// 添加到列表中
					dealerList.value.push(item);
				}
			});
		}
		// 如果有搜索值
		if (params.data.searchValue) {
			// 遍历经销商列表
			dealerList.value.forEach((item) => {
				// 如果临时结果列表中不存在该项
				if (!_resultTempList.find((temp) => temp.value == item.value)) {
					// 添加到临时结果列表中
					_resultTempList.push(item);
				}
			});
		} else {
			// 遍历临时结果列表
			_resultTempList.forEach((item) => {
				// 如果经销商列表中不存在该项
				if (!dealerList.value.find((temp) => temp.value == item.value)) {
					// 添加到经销商列表中
					dealerList.value.push(item);
				}
			});
		}
	} catch (error) {
		message.createMessageError("获取经销商列表失败: " + error);
	} finally {
		isLoading.value = false;
	}
};

/** 选择值改变事件处理函数 */
function onChange(value: Array<string | number> | null) {
	emits("update:value", value);
}

/** 清空事件处理函数 */
const handleClear = () => {
	params.data.searchValue = "";
	emits("update:value", null);
};

/** 滚动事件处理函数 */
function handleScroll(e) {
	const currentTarget = e.currentTarget as HTMLElement;
	if (
		currentTarget.scrollTop + currentTarget.offsetHeight >=
		currentTarget.scrollHeight
	) {
		// 如果当前页乘以每页大小小于总记录数
		if (params.pageVO.current * params.pageVO.size < recordsTotal) {
			params.pageVO.current++; // 当前页加1
			pageDealerList();
		}
	}
}

/** 搜索事件处理函数 */
function handleSearch(event) {
	params.data.searchValue = event.target.value || "";
	params.pageVO.current = 1;
	pageDealerList();
	asyncSearchInputValRef.value = searchInputValRef.value;
}

/** 输入框值 change 时触发 */
function handleInputChange(value: string | [string, string]) {
	if (!value) {
		params.data.searchValue = "";
	    params.pageVO.current = 1;
	    pageDealerList();
		asyncSearchInputValRef.value = searchInputValRef.value;
	}
}

/** 聚焦事件处理函数 */
function handlerFocus() {
	// 如果经销商列表为空
	if (!dealerList.value.length) {
		pageDealerList();
	}
}

/** 关闭清空 */
const searchInputRef = ref(null);
function handleDropdownToggle(show) {
  if (!show) {
    if(searchInputValRef.value) {
		searchInputValRef.value = '';
		asyncSearchInputValRef.value = searchInputValRef.value;
	} 
  }
  else{
    nextTick(()=>{
      nextTick(()=>{
        searchInputRef.value && searchInputRef.value.focus()
      })
    })
  }
}

/** 监听 */
watch(
	() => selectedGroupIdList.value,
	(newVal) => {
		params.data.groupIdList = newVal;
		params.pageVO.current = 1;
		emits("update:value", null);
		pageDealerList();
	},
);

watch(() => props.value, (newVal) => {
	if (newVal && isArray(newVal)) {
		if(newVal.length > 1)  maxTagCount.value = 1;
	}
}, { immediate: true })
</script>

<style scoped lang="less"></style>
