<template>
  <div class="main-bg exceptionWrapper">
    <div class="infoWrapper">
      <img :src="exceptionInfo.imgSrc" alt="" />
      <div class="notice">{{ exceptionInfo.notice }}</div>
      <div class="notice-sub" v-if="notice">错误信息 : {{ notice }}</div>
    </div>
    <n-space>
      <n-button type="primary" @click="goToHomepage">
          返回首页
      </n-button>
    </n-space>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from "vue-router";
import { toRefs, watch, reactive, ref } from "vue";
import Img404Src from "@/assets/image/exception/404.png";
import Img403Src from "@/assets/image/exception/403.png";

const Default_Exception_Info = {
  "404": {
    imgSrc: Img404Src,
    notice: "页面好像丢失了，刷新一下吧",
  },
  "403": {
    imgSrc: Img403Src,
    notice: "页面不可访问哦",
  },
};

const route = useRoute();
const router = useRouter();
const { notice = "" } = toRefs(route.params);
const { code } = toRefs(route.meta);
const exceptionInfo = reactive({
  imgSrc: "",
  notice: "",
});
watch(
  code,
  (newCode: string) => {
    exceptionInfo.imgSrc = Default_Exception_Info[newCode].imgSrc;
    exceptionInfo.notice = Default_Exception_Info[newCode].notice;
  },
  {
    immediate: true,
  }
);

const goToHomepage = () => {
  location.href = location.origin;
};
</script>

<style scoped lang="less">
@import "@/styles/default.less";
.exceptionWrapper {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  img {
    height: 270px;
    margin-bottom: 16px;
  }
}
.notice {
  color: #333333;
  line-height: 29px;
  font-size: 18px;
}
.notice-sub {
  color: #999999;
  line-height: 34px;
  font-size: 16px;
}
.infoWrapper {
  width: 100%;
  text-align: center;
  padding: 70px;
  box-sizing: border-box;
}
</style>
