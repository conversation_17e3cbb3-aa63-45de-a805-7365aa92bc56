import { defHttp } from "@/services";

export const enum FileApi{
  upload = "/common/upload",
  uploadProductImg = "/common/upload/productImg",
  uploadRichTextResouce = '/common/upload/spec',
  uploadVideo = '/common/upload/video',
  deleteFile = '/common/deleteFile',
  uploadCiccImg = '/allocationAccount/upload/ciccImg',
  uploadCdn = "/common/uploadCdn",
}

export function fileUpload(isThumb: boolean, params: FormData, onUploadProgress?: (percent) => void) {
  return defHttp.post({
    url: FileApi.upload + `?thumb=${isThumb}`,
    params,
    options: {
        timeout: 0,
        onUploadProgress: onUploadProgress ? onUploadProgress : function () {},
    },
    requestConfig: {
        requestContentType: "form-data",
    },
  });
}

interface UploadProductImgResponse {
  img: string,
  path: string
}
export function UploadProductImg(params: FormData, onUploadProgress?: (percent) => void) {
  return defHttp.post<UploadProductImgResponse[]>({
    url: FileApi.uploadProductImg,
    params,
    options: {
        timeout: 0,
        onUploadProgress: onUploadProgress ? onUploadProgress : function () {},
    },
    requestConfig: {
        requestContentType: "form-data",
    },
  });
}
export function uploadRichTextResouce(params: FormData, onUploadProgress?: (percent) => void) {
  return defHttp.post<Array<string>>({
    url: FileApi.uploadRichTextResouce,
    params,
    options: {
        timeout: 0,
        onUploadProgress: onUploadProgress ? onUploadProgress : function () {},
    },
    requestConfig: {
        requestContentType: "form-data",
    },
  });
}

export function uploadVideoFile(params: FormData, onUploadProgress?: (percent) => void) {
  return defHttp.post<string>({
    url: FileApi.uploadVideo,
    params,
    options: {
        timeout: 0,
        onUploadProgress: onUploadProgress ? onUploadProgress : function () {},
    },
    requestConfig: {
        requestContentType: "form-data",
    },
  });
}

export function deleteFile (objectName:string){
  return defHttp.get({
    url:FileApi.deleteFile + '?objectName=' + objectName
  })
}

export function fileUploadCiccImg(isMerchantId: string, params: FormData, onUploadProgress?: (percent) => void) {
  return defHttp.post({
    url: FileApi.uploadCiccImg + `?merchantId=${isMerchantId}`,
    params,
    options: {
        timeout: 0,
        onUploadProgress: onUploadProgress ? onUploadProgress : function () {},
    },
    requestConfig: {
        requestContentType: "form-data",
    },
  });
}

export function fileUploadCdn(isThumb: boolean, params: FormData, onUploadProgress?: (percent) => void) {
    return defHttp.post({
        url: FileApi.uploadCdn +`?thumb=${isThumb}`,
        params,
        options: {
            timeout: 0,
            onUploadProgress: onUploadProgress ? onUploadProgress : function () {},
        },
        requestConfig: {
            requestContentType: "form-data",
        },
    });
}
