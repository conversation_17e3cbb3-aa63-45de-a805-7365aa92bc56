<template>
  <JModal
    v-model:show="isShow"
    width="800"
    title="选择经销商"
    @after-leave="closeModal"
    @positive-click="_submit"
    :positiveButtonProps="{
        loading: isModalLoading
    }"
  >
    <div>
      <n-popconfirm v-if="thirdDealer && thirdDealer.name" @positive-click="handlePositiveClick">
        <template #trigger>
          <n-button icon-placement="right" secondary>
            <template #icon>
              <NIcon>
                <PersonRemoveOutline />
              </NIcon>
            </template>
            {{thirdDealer.name}}
          </n-button>
        </template>
        确认解绑该经销商?
      </n-popconfirm>
      <p class="hint">一个个门店只能绑定一个经销商，一个经销商可以绑定多个门店</p>
      <div class="wrapper">
        <n-spin :show="loading || isGetLoading" size="small" style="width: 46%;height: 100%;">
          <div class="left">
            <n-tree
              multiple
              block-line
              cascade
              checkable
              check-on-click
              :data="treeData"
              :default-checked-keys="checkedKeysRef"
              @update:checked-keys="handleCheckedKeysFn"
            >
              <template #empty>
                <JEmpty title="暂无经销商分组信息" />
              </template>
            </n-tree>
          </div>
        </n-spin>
        <div class="right">
          <div class="header">
            <JSearchInput
              v-model:value="searchValue"
              placeholder="请输入经销商id、姓名或昵称"
              @search="handleSearch"
              size="small"
              width="100%"
            />
          </div>
          <!-- 经销商列表 -->
          <div class="dealer-list-wrapper">
            <transition appear name="fade" mode="out-in">
              <template v-if="dealerList.length > 0">
                <div class="dealer-selected-wrapper">
                  <n-spin :show="isGetLoading" size="small" style="height: 100%;">
                    <div class="dealer-selected-list" @scroll="handleScroll">
                      <n-checkbox-group v-model:value="checkDealerKeysRef" @update:value="handleUpdateValue">
                        <n-space item-style="display: flex;" vertical>
                          <n-checkbox
                            class="dealer-list-item"
                            v-for="item in dealerList"
                            :value="item.id"
                            :key="item.id"
                            :disabled="(thirdDealerId && item.id!==thirdDealerId)?true:false"
                          >
                            <template #default>
                              <CustomOptions :infoItem="item" />
                            </template>
                          </n-checkbox>
                        </n-space>
                      </n-checkbox-group>
                    </div>
                  </n-spin>
                </div>
              </template>
              <template v-else>
                <JEmpty />
              </template>
            </transition>
          </div>
        </div>
      </div>
    </div>
  </JModal>
</template>

<script setup lang="tsx">
import { ref,computed, watch, onMounted } from 'vue';
import { useMessages } from '@/hooks';

import { useGetSgDealer } from "@/views/StoreModule/StoreConfig/modules/OperatorManagement/hooks";
import { SystemSetting } from "@/settings/systemSetting";
/** 相关组件 */
import CustomOptions from "@/views/StoreModule/StoreConfig/modules/OperatorManagement/components/CustomOptions.vue";
import JEmpty from "@/views/StoreModule/StoreConfig/modules/OperatorManagement/components/JEmpty.vue";
import { bindThirdDealer, getStoreDesc, unbindThirdDealer } from "@/services/api";
import { PersonRemoveOutline } from "@vicons/ionicons5";
const message = useMessages();
/** 经销商数据 */
const {
    loading,
    isGetLoading,
    treeData,
    dealerList,
    checkedKeysRef,
    searchValue,
    _params,
    getDealerGroupsSelectList,
    getSgDealerInfoList,
    handleScroll
} = useGetSgDealer();

const isModalLoading = ref<boolean>(false)
const id = ref<string>('')
const isShow = ref(false)
// 确认删除回调
const handlePositiveClick = async () => {
  try {
    let params = {
      data: {
        id: id.value,
        thirdDealerId: thirdDealer.value.id
      }
    }
    await unbindThirdDealer(params)
    thirdDealer.value = null
    message.createMessageSuccess('解绑经销商成功')
    checkDealerKeysRef.value = [];
    thirdDealerId.value = "";
  } catch (err) {
    message.createMessageError('解绑经销商成功:'+ err)
  }
};
/** 选中的经销商id */
const checkDealerKeysRef = ref<(string | number)[]>([]);
const thirdDealerId = ref<string>('')
// 关闭按钮
const closeModal = () => {
  isShow.value = false;
  checkedKeysRef.value = [];
  checkDealerKeysRef.value = [];
  searchValue.value = '';
}

/** 搜索 */
const handleSearch = () => {
    // 清空当前页码
    _params.pageVO.current = 1;
    _params.pageVO.size = SystemSetting.pagination.pageSize;
    getSgDealerInfoList();
};

/** 选项组的值改变时的回调 */
function handleUpdateValue(value,meta) {
  checkDealerKeysRef.value = [meta.value]
  thirdDealer.value = dealerList.value.filter(item => item.id == meta.value)[0]
}

async function handleCheckedKeysFn(keys, option, meta) {
  checkedKeysRef.value = keys;
  _params.data.groupIdList = keys;
  await getSgDealerInfoList();
  checkDealerKeysRef.value = [thirdDealerId.value]
}
// 确认按钮
const _submit = async () => {
  try {
    let params = {
      data:{
        id:id.value,
        thirdDealerId:checkDealerKeysRef.value[0]
      }
    }
    await bindThirdDealer(params)
    message.createMessageSuccess('绑定经销商成功')
    closeModal()
  } catch (err) {
    message.createMessageError('绑定经销商失败:'+ err)
  }
}
/** 当前旋转人员名称 */
const thirdDealer = ref(null)
const getThirdDealer = async () => {
  try {
    var data = await getStoreDesc(id.value)
    thirdDealerId.value = data.storeEntityDTO.thirdDealerId
    checkDealerKeysRef.value = [data.storeEntityDTO.thirdDealerId]
    thirdDealer.value = dealerList.value.filter(item => item.id == checkDealerKeysRef.value[0])[0]
  } catch (err) {
    message.createMessageError(err)
  }
}
/* 接收父组件参数 */
const acceptParams = (params) => {
  isShow.value = true;
  id.value = params
  getThirdDealer()
};

onMounted(()=>{
  /** 初始化 */
  _params.data.groupIdList = []
  checkedKeysRef.value = [];
  checkDealerKeysRef.value = [];
  getDealerGroupsSelectList();
  getSgDealerInfoList();
})

defineExpose({
  acceptParams,
});
</script>
<style lang="less" scoped>
.hint{
  color: #b5b5b5;
  font-size: 14px;
}
@import "@/styles/scrollbar.less";

.wrapper {
  width: 100%;
  height: 288px;
  display: flex;
  border: 1px solid #eee;
  :deep(.n-spin-content) {
      width: 100%;
      height: 100%;
  }

  .left {
      height: 100%;
      border-right: 1px solid #EEEEEE;
      overflow-y: auto;
      box-sizing: border-box;
      padding: 8px 12px;
      .scrollbar();
  }

  .right {
      width: 54%;
      height: 100%;
      box-sizing: border-box;

      .header {
          height: 28px;
          padding: 8px 12px;
      }

      .dealer-list-wrapper {
          height: calc(100% - 52px);
          padding-left: 12px;
          padding-bottom: 8px;
          box-sizing: border-box;

          .dealer-selected-wrapper {
              width: 100%;
              height: 100%;

              .check-all {
                  width: 100%;
                  height: 44px;
                  display: flex;
                  align-items: center;
                  padding: 2px 8px;
                  box-sizing: border-box;
              }

              :deep(.n-spin-content) {
                  height: 100%;
              }

              .dealer-selected-list {
                  height: 100%;
                  overflow-y: auto;
                  .scrollbar();

                  .dealer-list-item {
                      width: 100%;
                      display: flex;
                      align-items: center;
                      padding-left: 8px;
                      border-radius: 2px;

                      &:hover {
                          background-color: rgb(231, 241, 255);
                      }
                  }
              }
          }
      }
  }
}

:deep(.n-tree.n-tree--block-line .n-tree-node:not(.n-tree-node--disabled).n-tree-node--selected) {
    background-color: #fff;
}
</style>