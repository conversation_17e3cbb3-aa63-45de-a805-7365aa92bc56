import { MediaType } from "@volcengine/rtc";

export const enum StreamHostEventEnum{
    /** 被挤下线 */
    kickedOffline = 0,
    /** 网络状态更新 */
    networkQualityUpdate,
    /** 远端会员发布流 */
    userPublishStream,
    /** 远端会员取消发布流 */
    userUnpublishStream,
    /** 远端会员音频或视频改变 */
    userMediaStatusChange,
    streamBanned
}


export interface RTCLiveUserMediaStatus{
    videoEl?:string,
    index:number;
    /** 用户Id */
    userId:string;
    /**媒体流类型 */
    mediaType: MediaType;
    x?:number | string;
    y?:number | string;
    width?:number | string;
    height?:number | string;
    zIndex?:number
}
export interface RTCLiveDeviceStatus{
    /** 设备Id */
    id:string;
    /** 是否开启 */
    enabled:boolean;
    /**是否推流 */
    isPublishing:boolean;
}
/**RTC房间状态 */
export const enum RoomStatusEnum{
    /**未初始化 */
    IDLE = -1,
    /**进入房间 */
    READY = 0,
    /**离开房间 */
    LEAVE = 1,
}
export interface RTCBaseConfig{
    /**当前RTC应用id */
    appId:string;
    /**当前房间Id*/
    roomId:string;
    /** 当前用户Id */
    userId:string;
    /** 当前用户Token */
    token:string;
}
export interface RTCLiveStatus extends RTCBaseConfig{
    /** 当前房间状态 */
    roomStatus:RoomStatusEnum;
    /** 主视角的视频元素Id */
    videoElementId:string;
    /** 是否正在推流 */
    isPublishing: boolean;
    /**视频状态 */
    cameraStatus:RTCLiveDeviceStatus;
    /**音频状态 */
    micStatus:RTCLiveDeviceStatus;
    /**RTC房间人数 */
    userCount:number;
    /**RTC房间用户信息 */
    userMediaList:Array<RTCLiveUserMediaStatus>;
    /**合流转推配置 */
    mixLayout:{},
    /**视频推流配置 */
    encoderConfig: {
        maxKbps:number,
        frameRate:number,
        height:number,
        width:number
    },
    wrapperConfig:{
        height:number | string,
        width:number | string
    }
} 