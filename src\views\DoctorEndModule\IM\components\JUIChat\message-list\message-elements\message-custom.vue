<template>
  <div class="custom">
    <template v-if="customData.businessID === CHAT_MSG_CUSTOM_TYPE.SERVICE">
      <div>
        <h1>
          <label>{{ extension.title }}</label>
          <a
              v-if="extension?.hyperlinks_text"
              :href="extension?.hyperlinks_text.value"
              target="view_window"
          >{{ extension?.hyperlinks_text.key }}</a>
        </h1>
        <ul v-if="extension.item && extension.item.length > 0">
          <li
              v-for="(item, index) in extension.item"
              :key="index"
          >
            <a
                v-if="isUrl(item.value)"
                :href="item.value"
                target="view_window"
            >{{ item.key }}</a>
            <p v-else>
              {{ item.key }}
            </p>
          </li>
        </ul>
        <article>{{ extension.description }}</article>
      </div>
    </template>
    <template v-else-if="customData.businessID === CHAT_MSG_CUSTOM_TYPE.ORDER">
      <div
          class="order"
          @click="openLink(customData.link)"
      >
        <img
            :src="customData.imageUrl"
        >
        <main>
          <h1>{{ customData.title }}</h1>
          <p>{{ customData.description }}</p>
          <span>{{ customData.price }}</span>
        </main>
      </div>
    </template>
    <template v-else-if="customData.businessID === CHAT_MSG_CUSTOM_TYPE.LINK">
      <div class="textLink">
        <p>{{ customData.text }}</p>
        <a
            :href="customData.link"
            target="view_window"
        >{{
            TUITranslateService.t("message.custom.查看详情>>")
          }}</a>
      </div>
    </template>
    <template v-else>
      <span v-html="content.custom"/>
    </template>
  </div>
</template>

<script lang="ts" setup>
import {watchEffect, ref} from 'vue';
import {TUITranslateService, IMessageModel} from '@tencentcloud/chat-uikit-engine';
import {CHAT_MSG_CUSTOM_TYPE, ICustomMessagePayload} from "@/views/DoctorEndModule/IM/types";
import {isUrl, JSONToObject, safeParse} from "@/views/DoctorEndModule/IM/utils/IMUtils";

interface Props {
  messageItem: IMessageModel;
  content: any;
}

const props = withDefaults(defineProps<Props>(), {
  messageItem: undefined,
  content: undefined,
});

const custom = ref();
const message = ref<IMessageModel>();
const extension = ref<any>();
const customData = ref<ICustomMessagePayload>({
  businessID: '',
});

watchEffect(() => {
  custom.value = props.content;
  message.value = props.messageItem;
  const {payload} = props.messageItem;
  customData.value = payload.data || '';
  customData.value = safeParse(payload.data);
  if (payload.data === CHAT_MSG_CUSTOM_TYPE.SERVICE) {
    extension.value = safeParse(payload.extension);
  }
});
const openLink = (url: any) => {
  window.open(url);
};
</script>
<style lang="less" scoped>

a {
  color: #679ce1;
}

.custom {
  font-size: 14px;

  h1 {
    font-size: 14px;
    color: #000;
  }

  h1,
  a,
  p {
    font-size: 14px;
  }

  .evaluate {
    ul {
      display: flex;
      padding: 10px 0;
    }

    &-list {
      display: flex;
      flex-direction: row;

      &-item {
        padding: 0 2px;
      }
    }
  }

  .order {
    display: flex;

    main {
      padding-left: 5px;

      p {
        font-family: PingFangSC-Regular;
        width: 145px;
        line-height: 17px;
        font-size: 14px;
        color: #999;
        letter-spacing: 0;
        margin-bottom: 6px;
        word-break: break-word;
      }

      span {
        font-family: PingFangSC-Regular;
        line-height: 25px;
        color: #ff7201;
      }
    }

    img {
      width: 67px;
      height: 67px;
    }
  }
}
</style>
