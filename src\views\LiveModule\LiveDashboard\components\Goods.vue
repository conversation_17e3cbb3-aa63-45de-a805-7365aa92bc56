<template>
    <template v-if="goodsDataListRef.length">
        <div class="goods-wrapper" v-for="(item,index) in goodsDataListRef" :key="item.productId">
            <div class="goods-left">
                <img :src="item.productImgPath" alt="">
            </div>
            <div class="goods-right">
                <span class="name">{{ item.frontName }}</span>
                <span class="sum" >成交{{ item.totalProducts }}件</span>
                <div class="make">
                    <span class="tit">成交总额</span>
                    <span class="money">￥{{ item.totalMoney ? Number(item.totalMoney / 100).toFixed(2):0 }}</span>
                </div>
            </div>
            <div class="ranking" v-if="index <= 2" :class="[index+1==1?'ranking-one':index+1==2?'ranking-two':index+1==3?'ranking-three':'']">{{ index+1 }}</div>
        </div>
    </template>
    <div v-else class="empty-data">暂无商品</div>
</template>

<script setup lang="ts">
import useLiveDashboard from "../hook/useLiveDashboard";
const { goodsDataListRef } = useLiveDashboard()

</script>

<style lang="less" scoped>
.goods-wrapper {
    padding: 10px;
    margin-bottom: 9px;
    background-color:#162556;
    border-radius: 6px;
    display: flex;
    position: relative;
    .ranking{
        position: absolute;
        left: 0;
        top: 0;
        color: #fff;
        padding: 6px 12px 8px 10px;
        font-size: 12px;
        border-radius: 0px 0px 16px 0px;
    }
    .ranking-one{
        background: #FF4626;
    }
    .ranking-two{
        background: #FFA91B;
    }
    .ranking-three{
        background: #FFD719;
    }
    .goods-left{
        width: 68px;
        height: 68px;
        border-radius: 6px;
        overflow: hidden;
        margin-right: 10px;
        img{
            width: 100%;
            height: 100%;
        }
    }
    .goods-right{
        flex: 1;
        min-width: 0;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        font-size: 14px;
        .name{
            min-width: 0;
            width: 100%;
            white-space: nowrap;    
            overflow: hidden;       
            text-overflow: ellipsis;
            color: #cacbcc;
        }
        .sum{
            color: #e28b34;
            font-size: 12px;
        }
        .make{
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            .tit{
                line-height: 18px;
                color: #61636d;
                font-size: 12px; 
            }
            .money{
                font-size: 14px;
            }
        }
    }
}
.empty-data{
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
