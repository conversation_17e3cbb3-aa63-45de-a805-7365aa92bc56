<template>
  <div class="wrapper">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :isNeedCollapse="false"
      :isTableSelection="false"
      :isTablePagination="false"
      @selectedKeysChange="selectedKeysChange"
      @table-sorter-change="tableSorterChange"
    >
      <!-- 操作项 -->
      <template #tableHeaderBtn>
        <n-button @click="tableSearch" class="store-button">刷 新</n-button>
        <n-button v-if="hasOrderStoreconfigAddressSetDefault" @click="setDefaultAddress()" type="primary">设置默认地址</n-button>
        <JAddButton v-if="hasOrderStoreconfigAddressNew" @click="rechargeRecords(false)" type="primary">新增地址</JAddButton>
      </template>
    </FormLayout>
    <!-- 新建地址 -->
    <GroupConfigurationShow ref="groupSlideShow" />
    <!-- 设置默认地址 -->
    <SetDefaultAddress v-model:show="isShowDefaultAddress" />
  </div>
</template>

<script setup lang="tsx" name="CollectStatement">
import { onMounted, ref } from "vue";
import { useMessages } from "@/hooks";
import FormLayout from "@/layout/FormLayout.vue";
import SetDefaultAddress from "@/views/StoreModule/StoreConfig/components/SetDefaultAddress.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import {
  returnAddressAdd,
  returnAddressUpdate,
  returnAddressDelete,
  returnAddressList,
  addressList,
  addressAdd,
  addressUpdate,
  addressDelete,
} from "@/services/api";
import GroupConfigurationShow from "@/views/StoreModule/StoreConfig/components/GroupConfigurationShow.vue";
import dayjs from "dayjs";
import {
  hasOrderStoreconfigAddressSetDefault,
  hasOrderStoreconfigAddressNew,
  hasOrderStoreconfigAddressEdit,
  hasOrderStoreconfigAddressDelete,
} from "../hooks/authList";
import { AddressSceneLabels } from "@/constants/business";
const { createMessageSuccess, createMessageError } = useMessages();
/* 表格方法Hook */
const {
  isLoading,
  tableData,
  getTableData,
  refreshTableData,
  sortTableData,
} = useTableDefault({
  getDataRequest: addressList,
});

/* 表格项 */
const tableColumns = [
  {
    title: "地址",
    key: "imgPath",
    width: 300,
    align: 'center',
    resizable:true,
    render: rowData => {
      return `${rowData.province + ' ' + rowData.cityName + ' ' + rowData.area + ' ' + rowData.address}`
    },
  },
  {
    title: "联系人",
    key: "name",
    align: "center",
    summaryTitle:"",
    width: 100,
  },
  {
    title: "联系电话",
    key: "mobile",
    align: "center",
    summaryTitle:"",
    width: 200,
  },
  {
    title: '使用场景',
    key: 'type',
    align: 'left',
    summaryTitle: '',
    width: 160,
    render: (rowData) => {
      return <span>{AddressSceneLabels[rowData.type]}</span>;
    },
  },
  {
    title: "启用",
    key: "isActivated",
    align: "center",
    summaryTitle:"",
    width: 100,
    render: rowData => {
      return <n-tag size='small' bordered={false} type={rowData.isActivated == 0 ? 'error' : 'success'}>
        {rowData.isActivated == 0 ? '不启用' : '启用'}
      </n-tag>
    },
  },
  {
    title: "操作",
    key: "action",
    width: 120,
    align: "center",
    fixed: "right",
    render: rowData => {
      return (
        <n-space align="center" justify="center">
          <n-button v-show={hasOrderStoreconfigAddressEdit} text size="small" onClick={()=>rechargeRecords(rowData)} type="primary">
            编辑
          </n-button>
          {hasOrderStoreconfigAddressDelete?<n-popconfirm
            onPositiveClick={() => {DelCarouselImg(rowData.id)}}
          >
            {{
              trigger: () => (
                <a style={{ color: 'red', cursor: 'pointer' }}>删除</a>
              ),
              default: () => <span style={{width:'300px'}}>是否确定删除该数据？</span>
            }}
          </n-popconfirm>:null}
          <span v-show={!hasOrderStoreconfigAddressEdit && !hasOrderStoreconfigAddressDelete}>-</span>
        </n-space>
      );
    },
  },
];


/** 排序 */
const tableSorterChange = (info: { sort: string, sortAsc: "ascend" | "descend" }) => {
  sortTableData(info.sort, info.sortAsc);
};

/** 选中行数据 */
const rowData = ref([]);
/** 获取选中行Key */
function selectedKeysChange(key, tableData) {
  rowData.value = tableData.map(({ _dummyId, ...rest }) => rest);
}

/* 初始化参数 */
const initParams = {
  condition: '',
  gender: null,
  status: null,
  searchType:'name',
  tagIds: null
};
const model = ref({ ...initParams });

/* 刷新列表 */
const tableSearch = () => {
  getTableData({
    type:0
  });
};
const groupSlideShow = ref()
const rechargeRecords = (rowData:any) =>{
  let row = {
    row:rowData,
    api:rowData?addressUpdate:addressAdd,
    refreshTable: tableSearch,
  }
  groupSlideShow.value.acceptParams(row)
}

const isShowDefaultAddress = ref(false)
/** 设置默认地址 */
const setDefaultAddress = () => {
  isShowDefaultAddress.value = true
  
}

const DelCarouselImg = async (id) => {
  try {
    await addressDelete({ id:id })
    createMessageSuccess(`删除地址配置成功`);
    setTimeout(tableSearch,500)
  }catch (e) {
    createMessageError(`删除地址配置失败： ${e}`);
  }
}
/* 组件挂载 */
onMounted(() => {
  tableSearch()
});
</script>

<style lang="less" scoped>
@import "@/styles/default.less";
@import "@/styles/defaultVar.less";

.wrapper {
  width: 100%;
  height: 100%;
}

</style>
