import { WelfareTypeEnum, WelfareStatusEnum,WelfarePageTypeEnum,ReceiveRecordEnum } from "./types";
import { WelfareDrawStateLabels } from "@/constants";
import dayjs from "dayjs";
import { NSpace, NTag } from "naive-ui";
const welfareTypeMap = {
  [WelfareTypeEnum.WELFARE]: '福利券',
  [WelfareTypeEnum.TIME]: '时长券',
};
const welfareStatusMap = {
  [WelfareStatusEnum.UNISSUED]: '未发放',
  [WelfareStatusEnum.ISSUING]: '发放中',
  [WelfareStatusEnum.ENDED]: '停止发放',
};

export const createWelfareTicketManageColumns = ({welfarePageType,operation}) => {
  const columns = [
    {
      title: "发放批次ID",
      key: "batchId",
      width: 230,
      align: "left",
    },
    {
      title: "福利券名称",
      key: "name",
      width: 120,
      align: "left",
    },
    {
      title: "福利券分类名称",
      key: "categoryName",
      width: 120,
      align: "left",
    },
    {
      title: "福利券类型",
      key: "type",
      width: 100,
      align: "left",
      render(row) {
        return welfareTypeMap[row.type] || '- ';
      },
    },
    {
      title: "发行总数",
      key: "totalQuantity",
      width: 80,
      align: "left",
      render(row) {
        return row.totalQuantity == -1 ? "无限制" : row.totalQuantity;
      },
    },
    {
      title: "领取总数",
      key: "receivedQuantity",
      width: 80,
      align: "left",
    },
    {
      title: "剩余总数",
      key: "remainingQuantity",
      width: 80,
      align: "left",
      render(row) {
        return row.remainingQuantity == -1 ? "无限制" : row.remainingQuantity;
      },
    },
    {
      title: "已使用",
      key: "usedQuantity",
      width: 80,
      align: "left",
    },
    {
      title: "发放状态",
      key: "cityName",
      width: 80,
      align: "left",
      render(row) {
        return (
          <NSpace justify='center'>
              <NTag
                  bordered={false}
                  size="small"
                  type={row.status === WelfareStatusEnum.UNISSUED ? "info" : row.status === WelfareStatusEnum.ISSUING ? "success" : "default"}
              >
                  {welfareStatusMap[row.status]}
              </NTag>
          </NSpace>
        );
      },
    },
    {
      title: "有效期",
      key: "validUntil",
      width: 180,
      align: "left",
      render(row) {
        if(welfarePageType == WelfarePageTypeEnum.PAFGE){
          return dayjs(row.validUntil).format('YYYY-MM-DD');
        }else{
          return dayjs(row.validUntil).format('YYYY-MM-DD HH:mm:ss');
        }
      },
    },
    {
      title: "操作",
      key: "action",
      width: 200,
      fixed: "right",
      align: "left",
      render(row) {
        return operation(row);
      },
    },
  ]
  //sourceType 1 商城 2 直播
  const LiveAndStore= {
    title: "直播间ID/店铺名称",
    key: "storeAndLive",
    width: 180,
    align: "left",
    render(row) {
      return row.sourceType == 2 ? row.liveRoomId : row.storeName;
    },
  }
  if(welfarePageType == WelfarePageTypeEnum.PAFGE){
    columns.splice(3, 0, LiveAndStore);
  }
  const Liveconditions =    {
    title: "领取条件",
    key: "receiveConditionType",
    width: 120,
    align: "left",
    render(row) {
      return row?.receiveConditionType ? row.receiveConditionType == 1 ?
      <span>无条件</span>:
      <span>观看{row.watchDuration}分钟</span>
      :'-'
    },
  }
  if(welfarePageType == WelfarePageTypeEnum.LIVEROOM){
    columns.splice(4, 0, Liveconditions);
  }
  return columns
}

export const createReceiveRecordColumns = ({welfarePageType}) => {
  const columns = [

    {
      title: "用户信息",
      key: "user",
      width: 210,
      align: "left",
      render(row){
        return (
          <span>
            <p>{row.userName}</p>
            <p>用户ID：{row.userShortId}</p>
          </span>
        )
      } 
    },
    {
      title: "领取时间",
      key: "receiveTime",
      width: 180,
      align: "left",
    },
    {
      title: "使用状态",
      key: "useStatus",
      width: 70,
      align: "left",
      render(row){
        return (
          <n-space>
            <n-tag bordered={false} size="small" type={row.useStatus == ReceiveRecordEnum.UNUSED ? 'info' :row.useStatus == ReceiveRecordEnum.USE? 'success' : 'default'}>
              {WelfareDrawStateLabels[row.useStatus]}
            </n-tag>
          </n-space>
        )
      } 
    },
    {
      title: "有效期",
      key: "expiredTime",
      width: 150,
      align: "left",
    },
    {
      title: "核销时间",
      key: "usedTime",
      width: 150,
      align: "left",
    },
    {
      title: "备注",
      key: "remark",
      width: 210,
      align: "left",
    }
  ]
  const CouponId=   {
    title: "券ID",
    key: "id",
    width: 180,
    fixed: "left",
    align: "left",
  }
  if(welfarePageType == WelfarePageTypeEnum.LIVEROOM){
    columns.splice(0, 0, CouponId);
  }
  return columns
}
