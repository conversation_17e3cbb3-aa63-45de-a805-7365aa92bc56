<template>
  <div class="inner-page-height">
    <div class="table-wrapper">
      <div
        class="header-wrapper"
        ref="headerLeftDomRef"
        :style="{
        borderRadius: `5px`
      }"
      >
        <div class="header" :style="{
        position: 'relative',
        height: 'auto',
        boxSizing: 'border-box',
        paddingRight:'10px'
      }">
          <div :style="`width:100%;`">
            <div ref="headerLeftDomRefkd" class="search-form">
              <n-form
                ref="formRef"
                :model="formValue"
                :show-feedback="false"
                label-placement="left"
                label-width="auto"
                require-mark-placement="right-hanging"
                size="small"
                :style="{ width: '100%' }"
              >
                <n-form-item label="组织名称">
                  <n-input v-model:value="formValue.name" placeholder="请输入组织名称"  style="width: 170px !important;" @keyup.enter.native="formSearch" clearable/>
                </n-form-item>
                <n-form-item label="组织状态">
                  <n-select
                    v-model:value="formValue.useStatus"
                    :options="storeStatusRadio"
                    placeholder='请选择组织状态'
                    style="width: 170px;"
                    clearable
                  />
                </n-form-item>
              </n-form>
            </div>
          </div>
        </div>
        <div class="btn" ref="headerBtnDomRef">
          <n-space align="center">
            <n-button @click="refresh" class="store-button" :loading="isLoading">刷 新</n-button>
            <n-button v-if="hasStructreDepartmentIndexCreateMainAuth" type="primary" @click="handleOrganize(OrganizationalStructure.FirstLevelOrganization)">创建一级组织</n-button>
          </n-space>
        </div>
      </div>
      <n-data-table
        ref="expandedTable"
        :style="tableStyle"
        :columns="tableColumns"
        :data="tableData"
        :max-height="tableHeight"
        :scroll-x="820"
        :row-key="(row)=>{return row.id}"
        @load="fetchChildrenInParallel"
        :expanded-row-keys="expandedKeys"
        @update-expanded-row-keys="getexpandedKeys"
        :loading="isLoading"
      >
        <template #empty>
          <div class="infoWrapper">
            <img style="height: 210px;" :src="EmptyDataSrc" alt="" />
            <div class="notice">暂无数据</div>
          </div>
        </template>
      </n-data-table>
      <OrganizeModal ref="organizeModalShow"/>
      <distribute-welfare-voucher-modal ref="distributeWelfareVoucherModalRef"></distribute-welfare-voucher-modal>
      <QR ref="QrModalShow"/>
      <RegionalManagerRegistrationCode ref="regionalManagerRegistrationCodeRef"></RegionalManagerRegistrationCode>
      <DealerModal ref="dealerModalRef"/>
      <DealerCode ref="dealerCodeRef" :isDownload="true" />
    </div>
  </div>
</template>
<script setup lang="tsx" name="OrganizationalStructure">
import { ref, onMounted, onUnmounted } from 'vue';
import OrganizeModal from './components/organizeModal.vue';
import { OrganizationalStructure, DepartmentType } from "@/enums";
import { listStructures, addSubOrganization, addHeadOrganization, structureUpdate, structureDelete, getStoreMain } from "@/services/api";
import {hasStructreDepartmentIndexCreateMainAuth, hasStructreDepartmentIndexDeleteAuth, hasStructreDepartmentIndexEditAuth, hasStructreDepartmentIndexCreateSubAuth, hasRegionalManagerRegistrationCodeAuth, hasDealerCodeAuth} from '../authList'
import EmptyDataSrc from "@/assets/image/exception/emptyData.png";
import { NButton, NSpace, NTag, useDialog } from "naive-ui";
import QR from "@/components/JModal/QRModal.vue";
import DealerModal from "./components/DealerModal.vue";
import DistributeWelfareVoucherModal
  from "@/views/Distribution/OrganizationalStructure/components/distributeWelfareVoucherModal.vue";
import Organize from "@/views/Distribution/OrganizationalStructure/hooks/organize";
import RegionalManagerRegistrationCode from "./components/RegionalManagerRegistrationCode.vue"
import DealerCode from "@/views/ShopModule/DealerManagement/components/DealerCode.vue";
const { storeStatusRadio, DepartmentType_options} = Organize()
// 计算表格的动态样式
const tableStyle = ref({
  height: 'calc(100% - 74px)',  // 定义表格的高度
  padding: '0px 12px 12px 12px',
  paddingTop: '0px',
  boxSizing: 'border-box',
  borderRadius: '0px',
});

const initParams = {
  name:undefined,
  parentCode:'0',
  useStatus:null,
};

const formValue = ref({ ...initParams });

//刷新
const refresh = () =>{
  expandedKeys.value = []
  getTable()
}
const expandedKeys = ref([])
const isLoading = ref(false)
/** 搜索 */
const formSearch = () =>{
  // 重置展开行
  expandedKeys.value = []
  getTable()
}
/** 获取参数 */
const getParams = () => {
  const { name,parentCode, useStatus } = formValue.value;
  if (name){
    return {
      name,
      useStatus
    };
  }else {
    return {
      parentCode,
      useStatus
    };
  }
};
/** 请求表格数据 */
async function getTable() {
    isLoading.value = true
    // 获取顶层结构
    try {
      let data = getParams()
      let table_list = await listStructures({ data:data });
      if (data.name){
        tableData.value = []
        table_list.forEach((e,index)=>{
          if (e.parents.length>0){
            const parents = e.parents
            tableData.value[index] = { ...parents[0] }
            tableData.value[index].Location = index
            expandedKeys.value.push(parents[0].id)
            setTable(parents,tableData.value[index],e)
          }else {
            tableData.value[index] = e
            tableData.value[index].Location = index
          }
        })
      }else{
        console.log(2222);
        tableData.value = table_list
        tableData.value.forEach((e,index)=>{
          e.isLeaf = !e.hasChildren
          e.Location = index
        })
      }
    }catch (e) {
      tableData.value = [];
    }finally {
      isLoading.value = false
    }
}
function setTable(parents,obj,data,leval = 1){
  if (parents?.[leval]){
    parents[leval].Location = obj.Location + '-' + 0
    obj.children = [
      {
        ...parents?.[leval]
      }
    ]
    expandedKeys.value.push(parents[leval].id)
    setTable(obj, obj.children[0], data, leval + 1)
  }else {
    data.Location =  obj.Location + '-' + 0
    obj.children = [
      {
        ...data
      },
    ]
  }
  console.log(tableData);
}
// 更新坐标
function setLocation(arr,Location=0) {
  arr.forEach((e,index)=>{
    e.Location = Location?Location + '-' + index:index
    if (e.children){
      setLocation(e.children,e.Location)
    }
  })
}
function fetchChildrenInParallel(row: any) {
  return new Promise(  async (resolve, reject) => {
    try {
      const resp = await listStructures({
        data: {
          parentCode: row.code
        }
      });
      row.children = resp
      // 添加展开符号
      row.children.forEach((e,index) => {
        e.isLeaf = !e.hasChildren
        e.Location = row.Location + '-' +index
      })
      expandedKeys.value.push(row.id)
      resolve(resp);
    } catch (e) {
      row.isLeaf = true
      reject(e);
    }
  });
}
function getexpandedKeys(e) {
  expandedKeys.value = e
}
async function UpdataTable(res, data, str) {
  if (str == OrganizationalStructure.FirstLevelOrganization) {
    tableData.value.unshift(res)
    setLocation(tableData.value)
  } else {
    const indices = String(data.Location).split('-')
    const tableDataStatic = tableData.value
    if (str === OrganizationalStructure.Delete){
      if (indices.length === 1){
        tableData.value.splice(Number(indices[0]),1)
      }else {
        // 删除在上一个leave删除
        await modifyArrayElement(tableDataStatic, indices, str, res, 2)
      }
    }else {
      await modifyArrayElement(tableDataStatic, indices, str, res, 1)
    }
  }
}
const tableData = ref([])
const expandedTable = ref()
const tableColumns = [
    {
      title: '组织名称',
      key: 'name',
      width: 500,
      tree:true
    },
    {
      title: "部门类型",
      key: "departmentType",
      width: 200,
      render: rowData => {
        return DepartmentType_options[rowData.departmentType]?.label
      },
    },
    {
      title: "组织状态",
      key: "useStatus",
      width: 200,
      render: (row) => {
        return (
          <NSpace>
            <NTag
              bordered={false}
              size="small"
              type={row.useStatus == 0 ? "error" : "success"}
            >
              {row.useStatus == 0 ? "禁用" : "正常"}
            </NTag>
          </NSpace>
        );
      }
    },
    {
      title: "创建时间",
      key: "createTime",
      width: 200,
    },
    {
      title: "操作",
      key: "action",
      width: 120,
      fixed: "right",
      render: rowData => {
          return (
              <n-space align="center" justify="center">
                  {
                    rowData.departmentType === DepartmentType.STORE && rowData.level<=9?
                    <>
                      <n-button text size="small" type="primary" onClick ={()=>QrModal(rowData.id)}> 门店二维码 </n-button>
                      <n-button text size="small" type="primary" onClick ={()=>bindingDealerModal(rowData.id)}> 绑定社群经销商</n-button>
                    </>
                       : null
                  }
                  {
                    rowData.departmentType === DepartmentType.STORE && rowData.level<=9?
                      <n-button
                        text
                        size="small"
                        type="primary"
                        onClick ={()=>distributeWelfareVoucher(rowData.id)}>
                        发放福利券
                      </n-button> : null
                  }
                  {
                    hasStructreDepartmentIndexDeleteAuth ?
                    <n-button
                    text
                    size="small"
                    type="error"
                    onClick ={()=>handleOrganize(OrganizationalStructure.Delete,rowData)}>
                     删除
                    </n-button> : null
                  }
                  {
                    hasStructreDepartmentIndexEditAuth ?
                    <n-button
                    text
                    size="small"
                    type="primary"
                    onClick ={()=>handleOrganize(OrganizationalStructure.Edit,rowData)}>
                     编辑
                    </n-button> : null
                  }
                  {
                    rowData.departmentType !== DepartmentType.STORE && hasStructreDepartmentIndexCreateSubAuth && rowData.level<=9?
                    <n-button
                    text
                    size="small"
                    type="primary"
                    onClick ={()=>handleOrganize(OrganizationalStructure.SubOrganizations,rowData)}>
                     创建子组织
                    </n-button> : null
                  }
                  {
                  rowData.departmentType === DepartmentType.REGION && hasRegionalManagerRegistrationCodeAuth?
                    <n-button
                    text
                    size="small"
                    type="primary"
                    onClick ={()=>handleRegionalManagerRegistrationCode(rowData)}>
                     区域经理注册码
                    </n-button> : null
                  }
                  {
                  rowData.departmentType === DepartmentType.AREA && hasDealerCodeAuth?
                    <n-button
                    text
                    size="small"
                    type="primary"
                    onClick ={()=>handleOpenDealerCode(rowData)}>
                     经销商注册码
                    </n-button> : null
                  }
              </n-space>
          );
       },
    }
]
const setLoading = (btn) => {
  isLoading.value = btn
}
/** 下标检索修改 */
async function modifyArrayElement(arr, indices, str, newValue, delectIndex, currentIndex = 0) {
  if (currentIndex === indices.length - delectIndex) {
    // 到达最后一个索引，修改值
    switch (str) {
      case OrganizationalStructure.SubOrganizations:
        if (!arr[indices[currentIndex]].children){
          arr[indices[currentIndex]].isLeaf = false
        }else {
          arr[indices[currentIndex]].children.unshift({
            ...newValue,
          })
          // 重新更新坐标
          setLocation(arr[indices[currentIndex]].children,arr[indices[currentIndex]].Location)
        }
        break
      case OrganizationalStructure.Edit:
        arr[indices[currentIndex]] = {
          ...arr[indices[currentIndex]],
          ...newValue
        }
        break
      case OrganizationalStructure.Delete:
        if (arr[indices[currentIndex]].children.length>1){
          arr[indices[currentIndex]].children.splice(Number(indices[currentIndex+1]),1)
          setLocation(arr[indices[currentIndex]].children,arr[indices[currentIndex]].Location)
        }else {
          arr[indices[currentIndex]].children = null
          arr[indices[currentIndex]].isLeaf = true
          expandedKeys.value.filter(e=>{
            return e!= arr[indices[currentIndex]].id
          })
        }
        break
    }
  } else {
    // 递归调用函数，处理下一个索引
    await modifyArrayElement(arr[indices[currentIndex]].children, indices, str, newValue, delectIndex, currentIndex + 1);
  }
}
/** 组织、编辑、删除 */
const organizeModalShow = ref()
const handleOrganize = (type,rowData) =>{
  let api = null
  switch (type) {
    case '创建一级组织':
      api = addHeadOrganization
      break
    case '创建子组织':
      api = addSubOrganization
      break
    case '编辑组织':
      api = structureUpdate
      break
    case '删除组织':
      api = structureDelete
      break
  }
  let params = {
     type,
     rowData,
     updataTable:UpdataTable,
     api,
     setLoading
  }
  organizeModalShow.value.acceptParams(params)
}
/** 查看门店二维码 */
const QrModalShow = ref()
const QrModal = (id) =>{
  let params = {
    title:'门店二维码',
    api:getStoreMain,
    data:id,
    qrCode:true
  }
  QrModalShow.value.showModal(params)
}
/**绑定社群经销商 */
const dealerModalRef = ref()
const bindingDealerModal = (id)=>{
  dealerModalRef.value.acceptParams(id)
}

const distributeWelfareVoucherModalRef = ref()
const distributeWelfareVoucher = (id) =>{
  distributeWelfareVoucherModalRef.value.acceptParams(id)
}

const windowWidth = ref(window.innerWidth)
const windowHeight = ref(window.innerHeight)
const tableHeight = ref(0); // 用于存储表格高度

const handleResize = () => {
  windowWidth.value = window.innerWidth
  windowHeight.value = window.innerHeight
  tableHeight.value = expandedTable.value.$el.offsetHeight - 65;
}
const regionalManagerRegistrationCodeRef = ref()
const handleRegionalManagerRegistrationCode = (row) => {
  regionalManagerRegistrationCodeRef.value.acceptParams(row)
}
const dealerCodeRef = ref()
const handleOpenDealerCode = (row) => {
  dealerCodeRef.value.acceptParams(row);
}
/* 组件挂载 */
onMounted(() => {
  getTable()
  handleResize()
  window.addEventListener('resize', handleResize)
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>
<style scoped lang="less">
@import "@/styles/defaultVar.less";
.default-bg {
  height: 100%;
  max-height: @inner-bg-height;
  background-color: @blank-background-color;
  }
.table-wrapper {
  height: 100%;
  background: #ffffff;
  width: 100%;
  box-sizing: border-box;
  position: relative;
  .overview-wrapper{

  }
  .header-wrapper{
    padding: 12px 12px 0 12px;
  }
  .header-wrapper,
  .breadcrumb-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    height: auto;
    // height: 34px;
  }
  .header {
    line-height: 22px;
    color: @primary-color;
    font-size: @font-size-base;
    line-height: 34px;
    flex:1;
    .label {
      color: @secondary-text-color;
    }
  }
  .btn {
    flex-shrink: 0;
  }
  .infoWrapper {
    width: 100%;
    text-align: center;
    padding: 70px;
    box-sizing: border-box;
  }
  .notice {
    color: #333333;
    line-height: 29px;
    font-size: 16px;
  }
  img {
    height: 210px;
  }
  .footer-slot-wrapper {
    position: absolute;
    bottom: 19px;
    height: 44px;
    width: calc(100% - 24px);
    background: rgba(255, 255, 255, 0.6);
    display: flex;
    align-items: center;
    padding-right: 17px;
    box-sizing: border-box;
    border-top: 1px solid #eee;
    background: #fff;
    left:12px;
    .footer-selected-count {
      font-weight: 600;
      color: @primary-color;
    }
  }
}
:deep(.search-form .n-form) {
  display: flex;
  flex-wrap: wrap;
}
:deep(.search-form .n-form .n-form-item ) {
  flex-shrink: 0;
  flex-grow: 0;
  margin-bottom: 10px;
  margin-right: 24px;
  margin-left: 0px;
}
:deep(.search-form .n-form-item.n-form-item--left-labelled .n-form-item-label) {
  width: auto !important;
}
:deep(.n-data-table-base-table){
  height: 100vh;
}

</style>