<template>
  <FormLayout
    :isLoading="isLoading"
    :tableData="tableData"
    :tableColumns="tableColumns"
    :isNeedCollapse="false"
    :pagination="paginationRef"
    @paginationChange="paginationChange"
    @filtersChange="filtersChange"
    @selectedKeysChange="selectedKeysChange"
    @table-sorter-change="tableSorterChange"
  >
    <template #searchForm>
      <!-- 表单 -->
      <n-form
        ref="formRef"
        :model="model"
        :show-feedback="false"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
        size="small"
        :style="{ width: '100%', marginLeft: '12px' }"
      >
        <n-form-item label="客户昵称/ID">
          <JSearchInput
            v-model:value.trim="model.nickNameOrId"
            placeholder="请输入客户昵称/ID"
            @search="tableSearch"
            :isShowSearchIcon="false"
          />
        </n-form-item>

        <n-form-item label="手机号">
          <JSearchInput v-model:value.trim="model.condition" placeholder="请输入手机号" @search="tableSearch" />
        </n-form-item>
        <n-form-item label="角色">
          <n-select
            v-model:value="model.role"
            :options="customerRoleOptions"
            placeholder="请选择角色"
            style="width: 170px;"
            clearable
          />
        </n-form-item>
        <n-form-item label="创建时间">
          <j-date-range-picker
            style="flex: 1;"
            v-model:value="model.creationTime"
            type="datetimerange"
            format="yyyy-MM-dd"
            :default-time="['00:00:00', '23:59:59']"
            clearable
          />
        </n-form-item>
        <n-form-item label="门店名称/ID">
          <JSearchInput
            v-model:value.trim="model.storeNameOrId"
            placeholder="请输入门店名称/ID"
            @search="tableSearch"
            :isShowSearchIcon="false"
          />
        </n-form-item>
        <n-form-item label="店员昵称/ID">
          <JSearchInput
            v-model:value.trim="model.staffNameOrId"
            placeholder="请输入店员昵称/ID"
            @search="tableSearch"
            :isShowSearchIcon="false"
          />
        </n-form-item>
        <n-form-item label="经销商姓名/ID">
          <JSearchInput
            v-model:value.trim="model.structDealerNameOrId"
            placeholder="请输入经销商姓名/ID"
            @search="tableSearch"
            :isShowSearchIcon="false"
          />
        </n-form-item>
      </n-form>
    </template>
    <!-- 操作项 -->
    <template #tableHeaderBtn>
      <n-button v-if="hasExportAccountList" type="primary" :loading="exportLoading" @click="exportUserList">
        导出会员列表
      </n-button>
      <n-button v-if="hasVirtualAccount" type="primary" @click="addVirtualUser">新建虚拟账号</n-button>
      <n-button @click="refresh" class="store-button">刷 新</n-button>
    </template>
  </FormLayout>
  <DrawerEditCustomer ref="drawerEditCustomer" />
  <AddVirtualUserShow ref="addVirtualUserShow" />
  <CreditChangesModel ref="CreditChangesModelShow" />
  <ShowSocialCommunity :csId="csId" v-model:showModal="showModal" />
  <ChangeDealerModelShow ref="changeDealerModelShow" />
</template>

<script setup lang="tsx" name="ManagementReport">
import { ref, onMounted, watch, h, type VNode } from "vue";
import { useMessages } from "@/hooks";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import DrawerEditCustomer from "./DrawerEditCustomer.vue";
import ShowSocialCommunity from "./ShowSocialCommunity.vue";
import ChangeDealerModelShow from "./ChangeDealerModelShow.vue";
import {
  customerEntityPage,
  customerEntityGet,
  customerTagGetBindTag,
  gmMemberRelateList,
  customerEntityUpdateCommentState,
  customerEntityExportCustomers,
  customerEntityBlackList,
  customerEntityPageCount
} from "@/services/api";
import { NTooltip, type SelectOption } from "naive-ui";
import AddVirtualUserShow from "./AddVirtualUserShow.vue";
import CreditChangesModel from "./CreditChangesModel.vue";
import {
  hasVirtualAccount,
  hasDisableComment,
  hasIncreaseAndDecrease,
  hasExportAccountList,
  hasChangeAccountOwner,
  hasDisableOrEnableAccount,
} from "../authList";
import { useRoute } from "vue-router";
import dayjs from "dayjs";
import type { PaginationProps } from "@/components/LTable/type";

enum IsBlacklistedType {
  Block = 0,  //正常
  NotBlocked = 1  //拉黑
}

const { createMessageError, createMessageSuccess } = useMessages();
const renderOption = ({ node, option }: { node: VNode; option: SelectOption }) =>
  h(NTooltip, null, {
    trigger: () => node,
    default: () => option.label,
  });

/** emits */
const emits = defineEmits<{
  (e: "afterSuccessfulRequest"): void;
}>();

/* 表格方法Hook */
const {
  isLoading,
  tableData,
  paginationRef,
  pageTableData,
  paginationChange
} = useTableDefault({
  pageDataRequest: customerEntityPage,
  getTableTotalRequest:customerEntityPageCount
});

/** 表格下拉标签value */
const tableLabelValue = ref([]);

type CustomerRoleOptionsItem = {
  label: string;
  value: string;
  type: string
  mapValue: number
}

enum CustomerRoleType {
  //组织架构中的类型
  Struct = "structIdentityType",
  //店铺商城中的类型
  Store = "storeIdentityType"
}

enum CustomerRoleStructMapValue {
  //区域经理
  AreaManager = 1,
  //经销商
  Dealer = 2,
  //店长 TODO customerRoleOptions不需要添加
  StoreManager = 3
}

enum CustomerRoleStoreMapValue {
  //店长
  StoreManager = 1,
  //店员
  StoreEmployee = 2,
  //会员
  Member = 3
}

//客户角色
const customerRoleOptions: CustomerRoleOptionsItem[] = [
  {
    label: "店长",
    value: "1",
    type: CustomerRoleType.Store,
    mapValue: CustomerRoleStoreMapValue.StoreManager,
  },
  {
    label: "店员",
    value: "2",
    type: CustomerRoleType.Store,
    mapValue: CustomerRoleStoreMapValue.StoreEmployee,
  },
  {
    label: "普通会员",
    value: "3",
    type: CustomerRoleType.Store,
    mapValue: CustomerRoleStoreMapValue.Member,
  },
  {
    label: "区域经理",
    type: CustomerRoleType.Struct,
    value: "4",
    mapValue: CustomerRoleStructMapValue.AreaManager,
  },
  {
    label: "经销商",
    value: "5",
    type: CustomerRoleType.Struct,
    mapValue: CustomerRoleStructMapValue.Dealer,
  },
];

//获取value对应的mapValue
const getRoleMapValueItem = (value: string) => {
  return customerRoleOptions.find(item => item.value === value);
};
//通过type和mapValue获取label
const getRoleLabel = (type: string, mapValue: number) => {
  return customerRoleOptions.find(item => item.type === type && item.mapValue === mapValue)?.label ?? null;
};

/* 表格项 */
const tableColumns = [
  {
    title: "客户昵称/ID",
    key: "nickname",
    align: "left",
    width: 150,
    render: rowData => {
      return (
        <div>
          <div>{rowData.nickname}</div>
          {rowData.shortId && <div>ID:{rowData.shortId}</div>}
        </div>
      );
    },
  },
  {
    title: "手机号",
    key: "mobile",
    align: "left",
    width: 160,
  },
  {
    title: "角色",
    key: "",
    align: "left",
    width: 160,
    render: rowData => {
      const structLabel = getRoleLabel(CustomerRoleType.Struct, rowData[CustomerRoleType.Struct]);
      const storeLabel = getRoleLabel(CustomerRoleType.Store, rowData[CustomerRoleType.Store]);
      //同时存在两个店长身份显示店铺中的店长名字
      if (rowData[CustomerRoleType.Struct] === CustomerRoleStructMapValue.StoreManager && rowData[CustomerRoleType.Store] === CustomerRoleStoreMapValue.StoreManager) {
        return <div>{storeLabel}</div>;
      } else {
        return (
          <div>
            {structLabel && <div>{structLabel}</div>}
            {storeLabel && <div>{storeLabel}</div>}
            {!structLabel && !storeLabel && <div>普通会员</div>}
          </div>);
      }
    },
  },
  {
    title: "归属门店/ID",
    key: "storeShortId",
    align: "left",
    width: 150,
    render: rowData => {
      return (
        <div>
          <div>{rowData.storeName}</div>
          {rowData.storeShortId && <div>ID:{rowData.storeShortId}</div>}
        </div>
      );
    },
  },
  {
    title: "归属店员昵称/ID",
    key: "staffShortId",
    align: "left",
    width: 150,
    render: rowData => {
      return (
        <div>
          <div>{rowData.staffNickname}</div>
          {rowData.staffShortId && <div>ID:{rowData.staffShortId}</div>}
        </div>
      );
    },
  },
  {
    title: "经销商姓名/ID",
    key: "structDealerShortId",
    align: "left",
    width: 150,
    render: rowData => {
      return (
        <div>
          <div>{rowData.structDealerName}</div>
          {rowData.structDealerShortCsId && <div>用户ID:{rowData.structDealerShortCsId}</div>}
        </div>
      );
    },
  },
  {
    title: "积分总额",
    key: "totalPoints",
    align: "left",
    sorter: true,
    isSortDefault: false,
    summaryTitle: "积分总额",
    width: 100,
    render: rowData => {
      return rowData.totalPoints + "积分";
    },
  },
  {
    title: "积分余额",
    key: "availPoints",
    align: "left",
    sorter: true,
    isSortDefault: false,
    summaryTitle: "积分总额",
    width: 100,
    render: rowData => {
      return rowData.availPoints + "积分";
    },
  },
  // {
  //   title: "社群关联",
  //   key: "watchUserCount",
  //   align: "left",
  //   width: 120,
  //   render: rowData => {
  //     return (
  //     <n-tag
  //       bordered={false}
  //       type="info"
  //       size='small'
  //       onClick={() => clickSocialCommunity(rowData.id)}
  //       style="width: 100px;justify-content: center;cursor: pointer;"
  //     >查看关联</n-tag>
  //     );
  //   },
  // },
  // {
  //   title: "标签",
  //   key: "tagId",
  //   align: "left",
  //   ellipsis: false,
  //   width: 160,
  //   render: rowData => {
  //       return (
  //       <JSelect
  //         size="small"
  //         class="tagId"
  //         value="查看客户标签"
  //         clearable={false}
  //         filterable={false}
  //         options={tableLabelValue.value}
  //         onFocus={()=> onFocusLabel(rowData.id)}
  //       ></JSelect>
  //   )},
  // },
  {
    title: "客户状态",
    key: "isBlacklisted",
    align: "left",
    width: 100,
    render: rowData => {
      return rowData?.isBlacklisted === IsBlacklistedType.NotBlocked ? "已拉黑" : "正常";
    },
  },
  {
    title: "创建时间",
    key: "createTime",
    align: "left",
    width: 100,
    sorter: false,
    isSortDefault: true,
  },
  {
    title: "操作",
    key: "action",
    width: 150,
    align: "left",
    fixed: "right",
    render: rowData => {
      return (
        <n-space align="center" justify="center">
          {hasEditCustomerValue.value ?
            <n-button text size="small" type="primary" onClick={() => editInformation(rowData)}>
              编辑
            </n-button> : null}
          {hasDisableComment ? (rowData.isEnableComment ? <n-popconfirm
            onPositiveClick={() => {
              UpdateCommentState(rowData, 0);
            }}
          >
            {{
              trigger: () => (
                <a style={{ color: "blue", cursor: "pointer" }}>解除禁止评论</a>
              ),
              default: () => <span style={{ width: "300px" }}>是否确定解除禁止该客户评论？</span>,
            }}
          </n-popconfirm> : null) : null}
          {hasDisableComment ? (!rowData.isEnableComment ? <n-popconfirm
            onPositiveClick={() => {
              UpdateCommentState(rowData, 1);
            }}
          >
            {{
              trigger: () => (
                <a style={{ color: "red", cursor: "pointer" }}>禁止评论</a>
              ),
              default: () => <span style={{ width: "300px" }}>是否确定禁止该客户评论？</span>,
            }}
          </n-popconfirm> : null) : null}
          {hasIncreaseAndDecrease ?
            <n-button text size="small" type="primary" onClick={() => handleIntegralEvents(rowData)}>
              增减积分
            </n-button> : null
          }
          {hasDisableOrEnableAccount ? (rowData.isBlacklisted ? <n-popconfirm
            onPositiveClick={() => {
              UpdateAccountIsBlacklisted(rowData, IsBlacklistedType.Block);
            }}
          >
            {{
              trigger: () => (
                <a style={{ color: "blue", cursor: "pointer" }}>取消拉黑</a>
              ),
              default: () => <span style={{ width: "300px" }}>是否取消拉黑该客户？</span>,
            }}
          </n-popconfirm> : null) : null}
          {hasDisableOrEnableAccount ? (!rowData.isBlacklisted ? <n-popconfirm
            onPositiveClick={() => {
              UpdateAccountIsBlacklisted(rowData, IsBlacklistedType.NotBlocked);
            }}
          >
            {{
              trigger: () => (
                <a style={{ color: "red", cursor: "pointer" }}>拉黑</a>
              ),
              default: () => <span style={{ width: "300px" }}>是否拉黑该客户？</span>,
            }}
          </n-popconfirm> : null) : null}
          {hasChangeAccountOwner && isNormalMember(rowData) ?
            <n-button text size="small" type="primary" onClick={() => handleChangeDealer(rowData)}>
              修改归属店员
            </n-button> : null
          }
        </n-space>
      );
    },
  },
];


/** 排序 */
const tableSorterChange = (info: { sort: string, sortAsc: "ascend" | "descend" }) => {
  /** 积分排序方式 0积分总额降序，1积分总额升序，2剩余积分降序 3剩余积分升序 */
  const sortMapping = {
    totalPoints: { ascend: 1, descend: 0 },
    availPoints: { ascend: 3, descend: 2 },
    createTime: { ascend: null, descend: null },
  };
  model.value.pointsOrder = sortMapping[info.sort]?.[info.sortAsc] ?? null;
  tableSearch();
};

/** 选中行数据 */
const rowData = ref([]);

/** 获取选中行Key */
function selectedKeysChange(key, tableData) {
  const newTableData = tableData.map(({ _dummyId, ...rest }) => rest);
  rowData.value = newTableData;
}

const route = useRoute();
/* 初始化参数 */
const initParams = {
  nickNameOrId: "",
  staffNameOrId: route?.query?.staffNameOrId ?? "",
  structDealerNameOrId: "",
  pointsOrder: null,
  condition: "",
  role: null,
  tagIds: null,
  storeNameOrId: route?.query?.storeShortId ?? "",
  creationTime: null,
};
const model = ref({ ...initParams });

/** 获取搜索参数 */
const getParams = () => {
  const {
    nickNameOrId,
    staffNameOrId,
    structDealerNameOrId,
    condition,
    role,
    tagIds,
    pointsOrder,
    storeNameOrId,
    creationTime,
  } = model.value;
  const params = {
    nickNameOrId,
    staffNameOrId,
    structDealerNameOrId,
    condition,
    tagIds,
    storeNameOrId,
    pointsOrder,
    startTime: creationTime ? dayjs(creationTime?.[0]).format("YYYY-MM-DD HH:mm:ss") : null,
    endTime: creationTime ? dayjs(creationTime?.[1]).format("YYYY-MM-DD HH:mm:ss") : null,
  };
  if (role) {
    const roleItem = getRoleMapValueItem(role);
    if (roleItem) {
      params[roleItem.type] = roleItem.mapValue;
    }
  }

  return params;
};

/* 表格搜索 */
const tableSearch = async () => {
  await pageTableData(getParams(), paginationRef.value)
  emits("afterSuccessfulRequest");
};

const filtersChange = ({ searchParams }) => {
  model.value = {
    ...model.value,
    ...searchParams,
  };
  tableSearch();
};

/* 刷新表格 */
const refresh = () => {
  tableSearch();
};

const hasEditCustomerValue = ref(null);
/* 接收父组件传过来的参数 */
const acceptParamsReport = (params, hasEditCustomer) => {
  model.value.tagIds = params.tagIdsList;
  hasEditCustomerValue.value = hasEditCustomer;
  tableSearch();
};

const addVirtualUserShow = ref(null);
const addVirtualUser = () => {
  addVirtualUserShow.value?.acceptParams({ refreshTable: tableSearch });
};

const exportLoading = ref<boolean>(false);
const exportUserList = () => {
  exportLoading.value = true;
  customerEntityExportCustomers({
    data: { ...getParams() },
    pageVO: {
      current: paginationRef.value.current,
      size: paginationRef.value.pageSize,
    },
  })
    .then(res => {
      createMessageSuccess("导出成功");
    })
    .catch(err => {
      createMessageError(`导出失败:${err?.message??"导出数量不能超过10000"}`);
    })
    .finally(() => {
      exportLoading.value = false;
    });
};
/**编辑客户信息 */
const drawerEditCustomer = ref(null);
const editInformation = (row) => {
  const _params = {
    row,
    getInfoApi: customerEntityGet,
    socialCommunityApi: gmMemberRelateList,
    refresh: refresh,
  };
  drawerEditCustomer.value?.acceptParams(_params);
};
/** 修改客户评论权限 */
const UpdateCommentState = async (row, isEnableComment) => {
  const _params = {
    data: {
      id: row.id,
      isEnableComment: isEnableComment,
    },
  };
  try {
    await customerEntityUpdateCommentState(_params);
    createMessageSuccess(`修改客户评论权限成功`);
    setTimeout(tableSearch, 500);
  } catch (e) {
    createMessageError(`修改客户评论权限失败： ${e}`);
  }
};
/** 拉黑用户 */
const UpdateAccountIsBlacklisted = async (row, isBlacklisted: IsBlacklistedType) => {
  const _params = {
    data: {
      id: row.id,
      isBlacklisted,
    },
  };
  const messageText = isBlacklisted ? "拉黑" : "取消拉黑";
  try {
    await customerEntityBlackList(_params);
    createMessageSuccess(`客户${messageText}成功`);
    setTimeout(tableSearch, 500);
  } catch (e) {
    createMessageError(`客户${messageText}失败:${e}`);
  }
};

/** 判断是否普通会员
 * 修改归属店只针对普通会员
 * */
const isNormalMember = (row) => {
  //没有任何角色时默认是会员
  return (!row[CustomerRoleType.Store] && !row[CustomerRoleType.Struct]) || row[CustomerRoleType.Store] === CustomerRoleStoreMapValue.Member;
};
/** 聚焦标签 */
const onFocusLabel = async (id) => {
  let tableLabelList = [];
  try {
    const data = await customerTagGetBindTag({ csId: id });
    data.map((item) => {
      tableLabelList.push(item);
    });
    tableLabelValue.value = tableLabelList.map(obj => {
      return {
        label: obj.tagName,
        value: obj.id,
      };
    });
  } catch (err) {
    tableLabelValue.value = [];
    createMessageError(err || "获取下拉标签失败");
  }
};

/** 显示关联社群 */
const csId = ref(null);
const showModal = ref(false);
const clickSocialCommunity = (id) => {
  csId.value = id;
  showModal.value = true;
};

/** 增减积分事件 */
const CreditChangesModelShow = ref();
const handleIntegralEvents = (rowData) => {
  const _params = {
    row: rowData,
    refresh: refresh,
  };
  CreditChangesModelShow.value?.acceptParams(_params);
};
/** 修改归属店员 */
const changeDealerModelShow = ref();
const handleChangeDealer = (rowData) => {
  const _params = {
    row: rowData,
    refresh: refresh,
  };
  changeDealerModelShow.value?.acceptParams(_params);
};


/* 组件挂载 */
onMounted(() => {
  // tableSearch()
});

watch([
    () => model.value.role,
  ],
  () => {
    tableSearch();
  });

defineExpose({
  acceptParamsReport,
});
</script>

<style lang="less" scoped>
@import "@/styles/default.less";

:deep(.tagId .n-base-selection-input__content) {
  font-size: 11px;
}
</style>
