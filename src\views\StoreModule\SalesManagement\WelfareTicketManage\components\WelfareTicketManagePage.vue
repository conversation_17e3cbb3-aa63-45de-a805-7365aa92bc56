<template>
  <div style="height: 100%;">
      <FormLayout
        :isLoading="isLoading"
        :tableData="tableData"
        :tableColumns="tableColumns"
        :pagination="paginationRef"
        @paginationChange="paginationChange"
        :isNeedCollapse="false"
        :isDisplayIndex="false"
        :isTableSelection="false"
        @table-sorter-change="tableSorterChange"
      >
        <!-- 表单 -->
        <template #searchForm>
          <n-form
            ref="formRef"
            label-placement="left"
            label-width="auto"
            :show-feedback="false"
            require-mark-placement="right-hanging"
            size="small"
            :style="{ width: '100%' }"
          >
            <n-form-item :span="12" label="福利券名称" path="">
              <j-search-input v-model:value.trim="model.name" placeholder="请输入名称" :maxlength="30" @search="handlerSearch"/>
            </n-form-item>
            <n-form-item :span="12" label="福利券分类" path="">
              <JWelfareClassify v-model:value="model.categoryId" isImmediately style="width: 100%;" placeholder="请选择分类"></JWelfareClassify>
            </n-form-item>
            <template v-if="props.welfarePageType == WelfarePageTypeEnum.PAFGE">
              <n-form-item :span="12" label="发放批次ID" path="">
                <j-search-input v-model:value.trim="model.batchId" placeholder="请输入发放批次ID" :maxlength="30" @search="handlerSearch"/>
              </n-form-item>
              <n-form-item label="发行情景" path="range">
                <n-input-group>
                  <n-select v-model:value="model.distributionScenarios" placeholder="请选择" :options="welfareRollSendOptions" style="width: 135px;"/>
                  <JSearchInput :inputNumber="model.distributionScenarios == 2" v-model:value="model.searchKeywords" placeholder="请输入关键字" @search="handlerSearch" :maxlength="30" :width="240"/>
                </n-input-group>
              </n-form-item>
            </template>
          </n-form>
        </template>
    
        <template #tableHeaderBtn>
          <n-button @click="refresh" :loading="isLoading" class="store-button">刷 新</n-button>
          <JAddButton v-if="props.welfarePageType == WelfarePageTypeEnum.LIVEROOM" type="primary" @click="handleWelfareModal(WelfareModalTypeEnum.ADD)">新增福利券</JAddButton>
        </template>
      </FormLayout>
      <ReceiveRecord v-model:show="isRecordShow" :couponBatchId="couponBatchId" @refresh="refresh" :welfarePageType="props.welfarePageType" />
      <WelfareModal 
        :welfareModalType="welfareModalType"
        :welfarePageType="props.welfarePageType" 
        v-model:show="isWelfareShow" 
        :row="rowValidity" 
        @refresh="refresh" 
        @onSubmit="handleAddCouponBatchFn" 
        :isAddLoading="isAddLoadingRef" />
  </div>
</template>

<script lang="tsx" setup name="DoctorManagement">
import { onMounted, ref, watch } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import Popconfirm from "@/components/Popconfirm/index.vue";
import ReceiveRecord from "../components/ReceiveRecord.vue";
import WelfareModal from "../components/WelfareModal.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { createWelfareTicketManageColumns } from "../TableColumns";
import { pageCouponBatch,pagePCCouponBatch, syncCouponBatchStatus } from "@/services/api";
import { useMessages } from '@/hooks';
import { LiveStatusEnum, WelfareModalTypeEnum } from '../types';
import JWelfareClassify from '@/components/JSelect/JWelfareClassify.vue';
import { useWelfareTicketManage } from "../hook/useWelfareTicketManage";
import { WelfareStatusEnum,WelfareTypeEnum } from "../types";
import { WelfarePageTypeEnum } from "../types";
import { hasWelfareManagementSendAuth,hasUpdateValidDateAuth,hasSelectRecord } from "../../authList"
const { createMessageSuccess, createMessageError } = useMessages();
const { 
        isPopconfirmLoadingRef,
        isAddLoadingRef, 
        onCouponBatchFn,
        updateCouponValidityFn, 
        deleteCouponBatchFn, 
        sendHandleFn
  } = useWelfareTicketManage(); 

const props = withDefaults(defineProps<{
  liveRoomId?: string;
  welfarePageType?:WelfarePageTypeEnum;
  liveRoomStatus?:LiveStatusEnum
}>(), {
  liveRoomId: '',
  welfarePageType:WelfarePageTypeEnum.PAFGE,
  liveRoomStatus:LiveStatusEnum.NOTSTART
});
const emits = defineEmits<{
    (e: 'update:show', value: boolean): void;
    (e: 'refresh'): void;
}>();

/** 表格hook */
const {
  isLoading,
  tableData,
  paginationRef,
  pageTableData,
  refreshTableData,
  paginationChange,
} = useTableDefault({
  pageDataRequest:  props.welfarePageType == WelfarePageTypeEnum.LIVEROOM ? pageCouponBatch:pagePCCouponBatch,
});

/* 表格列表项 */
const tableColumns = createWelfareTicketManageColumns({
  welfarePageType:props.welfarePageType,
  operation(row) {
    return (
      //sourceType 1 商城 2 直播
      <n-space style="padding: 5px 0;">
        {(props.welfarePageType == WelfarePageTypeEnum.LIVEROOM || hasSelectRecord) && 
          <n-button text type="primary" onClick={() => handleRecord(row)}>领取记录</n-button>
        }
        {row.sourceType == 2 && props.welfarePageType == WelfarePageTypeEnum.PAFGE ? null : (
          <>
            {(props.welfarePageType == WelfarePageTypeEnum.LIVEROOM && row.status !== WelfareStatusEnum.ENDED) || (props.welfarePageType == WelfarePageTypeEnum.PAFGE && hasWelfareManagementSendAuth) ? 
              <Popconfirm 
                onHandleClick={() => handleSendEnable(row.status === WelfareStatusEnum.ISSUING ? 0 : 1, row)} 
                loading={isPopconfirmLoadingRef.value} 
                buttonContent={row.status === WelfareStatusEnum.ISSUING ? '停止发放' : '发放'} 
                type={row.status === WelfareStatusEnum.ISSUING ? 'error' : 'primary'} 
                promptContent={promptContent(row)} /> : null  
            }
            {props.welfarePageType == WelfarePageTypeEnum.LIVEROOM  ? 
              <n-space>
                {
                  row.status == WelfareStatusEnum.UNISSUED ? 
                  <n-button text type="primary" onClick={() => handleWelfareModal(WelfareModalTypeEnum.EDIT, row)}>编辑</n-button> : null
                }
                { row.status == WelfareStatusEnum.UNISSUED || (props.liveRoomStatus == LiveStatusEnum.NOTSTART && row.receiveConditionType == WelfareTypeEnum.TIME)  ? 
                  <Popconfirm 
                  onHandleClick={() => handleDelete(row)}
                  promptContent="你确定要删除该福利券吗?"
                  loading={isPopconfirmLoadingRef.value}
                  buttonContent="删除"
                  type="error"
                />
                :null
                }
                
              </n-space>
              : null
            }
            {(props.welfarePageType == WelfarePageTypeEnum.LIVEROOM && row.status == WelfareStatusEnum.ISSUING) || ( props.welfarePageType == WelfarePageTypeEnum.PAFGE && hasUpdateValidDateAuth) ? 
              <n-button text type="primary" onClick={() => handleWelfareModal(WelfareModalTypeEnum.VALIDITY, row)}>修改有效期</n-button> : null
            }
            {props.welfarePageType == WelfarePageTypeEnum.LIVEROOM && row.status != WelfareStatusEnum.UNISSUED ? 
              <Popconfirm 
                onHandleClick={() => handleReissue()}
                promptContent={'是否确定检查补发福利券? 注意请勿在直播中补发！'}
                loading={isPopconfirmLoadingRef.value}
                buttonContent="检查补发"
                type="primary"
              /> : null
            }
          </>
        )}
      </n-space>
    )
  }
})

/** 福利券 */
// const welfareRollSendOptions = [
//   {
//     label: "直播间ID",
//     value: "liveRoomId",
//   },
//   {
//     label: "店铺名称",
//     value: "storeName",
//   },
// ];
const welfareRollSendOptions = [
{
    label: "全部",
    value:0
  },
  {
    label: "直播间",
    value:2
  },
  {
    label: "门店",
    value:1
  },
];
const isRecordShow = ref(false);
const isWelfareShow = ref(false);

const couponBatchId = ref('');
const handleRecord = (rowData?:any) => {
  couponBatchId.value = rowData.id;
  isRecordShow.value = true;
};

// 初始化
const initParams = {
  name:"",
  categoryId: null,
  batchId:'',
  // searchType:'liveRoomId',
  // searchValue:props.liveRoomId,
  distributionScenarios:0,
  searchKeywords:''
};
/** 参数 */
const model = ref({...initParams});

/** 获取参数 */
const getParams = () => {
  const { name,categoryId,batchId,distributionScenarios,searchKeywords } = model.value;
  const liveRoomId = props.welfarePageType == WelfarePageTypeEnum.LIVEROOM ? {liveRoomId:props.liveRoomId} : {}
  const pageSearch = props.welfarePageType == WelfarePageTypeEnum.PAFGE ? {distributionScenarios,searchKeywords} : {}
  return {
    name,categoryId,batchId,
    ...pageSearch,...liveRoomId
  };
};

/** 搜索 */
const handlerSearch = () => {
  tableSearch();
};

/** 表格刷新 */
function refresh(){
  isWelfareShow.value = false;
  tableSearch();
}

/* 表格搜索 */
const tableSearch = () => {
  pageTableData(getParams(), paginationRef.value);
};

/** 表格排序 */
const tableSorterChange = (info)=>{
    // let params = {
    //     orderBy: info.sort,
    //     isAsc: info.sortAsc === 'ascend' ? true : false,
    // }
    // tableSearch();
}
const rowValidity = ref(null);
const welfareModalType = ref<WelfareModalTypeEnum>(WelfareModalTypeEnum.ADD)
const handleWelfareModal = (type:WelfareModalTypeEnum, rowData?:any) => {
  welfareModalType.value = type
  isWelfareShow.value = true;
  rowValidity.value = rowData;
};

/** 新增 编辑福利券 修改福利券有效期  */
const handleAddCouponBatchFn = (params) => {
  if(params.type != WelfareModalTypeEnum.VALIDITY){
    onCouponBatchFn(welfareModalType.value,props.liveRoomId,params.data, refresh)
  }else{
    updateCouponValidityFn(props.welfarePageType,{id:params.data.id,validUntil:params.data.validUntil}, refresh)
  }
}

/** 批次发放开关 1-开启发放 0-停止发放 */
const handleSendEnable = async (sendEnable:0 | 1, row:any) => {
    sendHandleFn(props.welfarePageType,sendEnable, row, refresh)
}

const promptContent = (row:any) => {
  if(row.status === WelfareStatusEnum.ISSUING){
    return '你确定要停止发放该福利券吗?'
  }else{
    return props.liveRoomId ? '你确定要对当前直播间发放该福利券吗?' : '你确定要对当前店铺发放该福利券吗?'
  }
}

/**检查补发 */
const handleReissue = ()=>{
  isPopconfirmLoadingRef.value = true
  setTimeout(()=>{
    isPopconfirmLoadingRef.value = false
    createMessageSuccess("成功补发0张福利券")
  },2000)
}
/** 删除 */
const handleDelete = (row:any) => {
  deleteCouponBatchFn(row.id, refresh)
}
/** 同步卡券 */
const handleSyncCouponBatchStatus = async()=>{
  try {
    await syncCouponBatchStatus(props.liveRoomId)
  } catch (error) {
    console.log("同步异常"+error);
    // createMessageError('同步异常')
  }
}
/** 组件挂载 */
onMounted(async() => {
  if(props.welfarePageType == WelfarePageTypeEnum.LIVEROOM){
   await handleSyncCouponBatchStatus()
  }
  await tableSearch();
});
watch(()=>model.value.categoryId, (newVal, oldVal) => { 
  paginationRef.value.current = 1;
  tableSearch();
})
</script>

<style lang="less" scoped>
@import "@/styles/defaultVar.less";
</style>