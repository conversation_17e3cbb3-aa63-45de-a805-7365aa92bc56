<template>
  <div :class="isEnableMaskLayer() ? 'maskLayer' : 'notMaskLayer'"></div>
  <n-layout content-style="height:100vh;width:100vw;" :native-scrollbar="false">
    
    <n-layout-header >
      <MainHeader />
    </n-layout-header>
    <n-layout-content :native-scrollbar="false">
      <MultipleTabsLayout />
    </n-layout-content>
    <!-- <n-layout-footer>
            <MainFooter />
        </n-layout-footer> -->
        <DeliveryExceptionModal />
  </n-layout>
</template>

<script setup lang="ts">
import MainHeader from "./components/MainHeader/index.vue";
// import MainFooter from "./components/MainFooter/index.vue";
import MultipleTabsLayout from "../MultipleTabsLayout/index.vue";
import useCreateNotification from "@/hooks/useCreateNotification"
import { useGlobalConfig } from "@/hooks/business"
import { onMounted, onUnmounted, watch , computed} from "vue";
import { SystemStoreType } from "@/enums";
import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
import DeliveryExceptionModal from "@/components/DeliveryExceptionModal/index.vue";
import { FirstLogin, EasyPasswords } from "@/enums"

const systemStore = useSystemStoreWithoutSetup();
const { openNotification , destroyNotification} = useCreateNotification();
const { openReceptionNotification, destroyReceptionNotification }  = useCreateReceptionNotification()
const { globalConfig, getGlobalConfiguration } = useGlobalConfig();


import { PrescriptionManagementAuth, OrderManagementAuth } from "@/enums/authKeys";
import { hasAuth } from "@/utils/auth/authUtils";
import useCreateReceptionNotification from "@/views/DoctorEndModule/hooks/useCreateReceptionNotification";
import {isDoctorEnv} from "@/utils/envUtils";

let distributionType = systemStore._globalConfig['visitDataType'];

const isEnableMaskLayer = () =>{
  if(systemStore._globalConfig['isInitialPassword'] == FirstLogin.isFirstLogin){
    return true
  }else if(systemStore._globalConfig['isSimplePassword'] == EasyPasswords.isEasyPasswords){
    return true
  }else{
    return false
  }
}

onMounted(async ()=>{
  await getGlobalConfiguration();
  systemStore.setStystemGlobalConfig(globalConfig.value);
  const isAuth = hasAuth(PrescriptionManagementAuth.prescriptionManagementIndexAdd.key);
  const hasManagementShip = hasAuth(OrderManagementAuth.orderManagementIndexShip.key);
  /** 医药商城并且有开方权限 */
  const isPharmacy = systemStore._globalConfig['marketplaceType'] === SystemStoreType.PHARMACEUTICALMALL && isAuth ;
  if ((isPharmacy || hasManagementShip) && (systemStore._globalConfig['isInitialPassword'] != FirstLogin.isFirstLogin && systemStore._globalConfig['isSimplePassword'] != EasyPasswords.isEasyPasswords)) openNotification(isPharmacy,hasManagementShip);
  
  if(isDoctorEnv() && (Number(distributionType)) == 4){
    openReceptionNotification(true)
  }
});

onUnmounted(()=>{
  destroyNotification()
  
  if (isDoctorEnv() && (Number(distributionType)) == 4){
    destroyReceptionNotification()
  }
});

</script>
<style scoped lang="less">
@import "@/styles/default.less";
@import "@/styles/defaultVar.less";
.maskLayer{
  display:block ;
  position: absolute;
  right: 0px;
  top: 0px;
  width: 100vw;
  height: 100vh;
  background: #fff;
  z-index: 4;
}
.notMaskLayer{
  display:none
}
</style>
