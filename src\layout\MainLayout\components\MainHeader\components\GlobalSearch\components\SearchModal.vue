<template>
  <n-modal
    v-model:show="show"
    :segmented="{ footer: 'soft' }"
    :closable="false"
    preset="card"
    footer-style="padding: 0; margin: 0"
    style="width: 630px;position: fixed;top: 50px;left: 0;right: 0;"
    @after-leave="handleClose"
  >
    <!-- 搜索框 -->
    <n-input-group>
      <n-input ref="inputRef" v-model:value="keyword" clearable placeholder="请输入关键词搜索" @input="handleSearch">
        <template #prefix>
          <n-button text @click="handleSearch">
            <n-icon 
              size="14" 
              :component="SearchIcon" 
              color="rgba(194, 194, 194, 1)"
            ></n-icon>
          </n-button>
        </template>
      </n-input>
      <n-button v-if="isTouchDevice()" type="primary" @click="handleClose">取消</n-button>
    </n-input-group>
    <!-- 内容 -->
    <div style="margin-top: 20px;">
      <!-- 暂无搜索结果 -->
      <div v-if="resultOptions.length === 0" class="empty-wrapper">
				<img :src="EmptySrc" alt="" />
				<p>暂无搜索结果</p>
			</div>
      <SearchResult
        v-else 
        v-model:value="activePathName" 
        :options="resultOptions" 
        @enter="handleEnter"
       />
    </div>
    <template #footer>
      <SearchFooter v-if="!isTouchDevice()" />
    </template>
  </n-modal>
</template>

<script lang="ts" setup>
import { computed, nextTick, ref, shallowRef, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
import { isTouchDevice, _debounce } from '@/utils';
import EmptySrc from "@/assets/image/exception/empty.png";
import { SearchOutline as SearchIcon } from '@vicons/ionicons5';
import SearchResult from './SearchResult.vue';
import SearchFooter from './SearchFooter.vue';

defineOptions({ name: 'SearchModal' });

interface Props {
  /** 弹窗显隐 */
  value: boolean;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'update:value', val: boolean): void;
}

const emit = defineEmits<Emits>();

const router = useRouter();
const systemStore = useSystemStoreWithoutSetup();

const keyword = ref('');
const activePathName = ref('');
const resultOptions = shallowRef([]);
const inputRef = ref<HTMLInputElement>();

const handleSearch = _debounce(search, 300);

const show = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit('update:value', val);
  }
});

/** 查询 */
function search() {
  resultOptions.value = systemStore.searchMenus.filter(menu => {
    const trimKeyword = keyword.value.toLocaleLowerCase().trim();
    const title = menu.meta.title.toLocaleLowerCase();
    return trimKeyword && title.includes(trimKeyword);
  });
  activePathName.value = resultOptions.value[0]?.name ?? '';
}

function handleClose() {
  show.value = false;
  /** 延时处理防止用户看到某些操作 */
  setTimeout(() => {
    resultOptions.value = [];
    keyword.value = '';
  }, 200);
}

/** key up */
function handleUp() {
  const { length } = resultOptions.value;
  if (length === 0) return;
  const index = resultOptions.value.findIndex(item => item.name === activePathName.value);
  if (index === 0) {
    activePathName.value = resultOptions.value[length - 1].name;
  } else {
    activePathName.value = resultOptions.value[index - 1].name;
  }
}

/** key down */
function handleDown() {
  const { length } = resultOptions.value;
  if (length === 0) return;
  const index = resultOptions.value.findIndex(item => item.name === activePathName.value);
  if (index + 1 === length) {
    activePathName.value = resultOptions.value[0].name;
  } else {
    activePathName.value = resultOptions.value[index + 1].name;
  }
}

/** key enter */
function handleEnter() {
  const { length } = resultOptions.value;
  if (length === 0 || activePathName.value === '') return;
  // 判断是否是一级路由
  let option = resultOptions.value.find(item => item?.name == activePathName.value);
  if (option?.path.startsWith('/')) {
    router.push({
      name: option.children.length > 0 ? option.children[0].name : null,
    });
  } else {
    router.push({
      name: activePathName.value
    });
  }
  handleClose();
}

/**
 * 监听键盘按键并触发相应的处理函数
 * @param key - 要监听的键名
 * @param handler - 对应的处理函数
 */
 function onKeyStroke(key, handler) {
  document.addEventListener('keydown', (event) => {
    if (event.key === key) {
      handler();
    }
  });
}

onKeyStroke('Escape', handleClose);
onKeyStroke('Enter', handleEnter);
onKeyStroke('ArrowUp', handleUp);
onKeyStroke('ArrowDown', handleDown);

/** 监听 */
watch(show, async val => {
  if (val) {
    /** 自动聚焦 */
    await nextTick();
    inputRef.value?.focus();
  }
});
</script>

<style lang="less" scoped>
  .empty-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  		img {
  			height: 120px;
  		
  		p {
  			font-size: 16px;
  			text-align: center;
        font-weight: 700;
  		}
  	}
  }
</style>
