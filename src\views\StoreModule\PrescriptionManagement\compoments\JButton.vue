<template>
    <NButton 
        v-if="props.isShow"
        text 
        size="small" 
        type="primary"
        style="margin-right: 8px;"
        @click="emits('click')"
        >
        <slot></slot>
    </NButton>
</template>

<script lang="ts" setup name=''>
import { NButton } from "naive-ui";

defineOptions({ name: 'JButt<PERSON>' });

/** props */
const props = withDefaults(defineProps<{
    isShow?: boolean;
}>(), {
    isShow: true
});

/** emits */
const emits = defineEmits<{
    (e: 'click'): void;
}>();

</script>


<style lang="less" scoped>

</style>