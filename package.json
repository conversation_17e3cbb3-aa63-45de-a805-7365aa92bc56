{"name": "store-admin", "version": "0.0.0", "scripts": {"dev": "vite --host", "bootstrap": "pnpm install", "clean:lib": "rimraf node_modules", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite", "reinstall": "rimraf pnpm-lock.yaml && rimraf package.lock.json && rimraf node_modules && npm run bootstrap", "build": "run-p type-check build-only", "preview": "vite preview --port 4173", "build-only": "vite build", "build-only:prod": "cross-env UAT=$npm_config_uat vite build --mode prod", "build-only:test": "vite build --mode test", "type-check": "vue-tsc --noEmit", "prettier:all": "npx prettier --write ./src/", "lint:all": "npx eslint . --ext .vue,.js,.ts,.jsx,.tsx --fix", "lint:src": "npx eslint --cache --max-warnings 0  \"{src,mock}/**/*.{vue,ts,tsx}\"", "lint:lint-staged": "npx lint-staged -c ./.husky/lintstagedrc.js"}, "dependencies": {"@csstools/normalize.css": "^12.0.0", "@tencentcloud/chat-uikit-engine": "^2.4.4", "@tencentcloud/tui-core": "^2.4.0", "@tencentcloud/universal-api": "^2.4.0", "@volcengine/rtc": "^4.66.16", "axios": "^1.1.2", "clipboard": "^2.0.11", "crypto-js": "^4.1.1", "dayjs": "^1.11.6", "deepmerge-ts": "^4.2.2", "echarts": "^5.4.1", "json-bigint": "^1.0.0", "html2canvas": "^1.4.1", "moment": "^2.29.4", "nprogress": "^0.2.0", "pdfjs-dist": "^5.2.133", "pinia": "^2.0.21", "qrcode": "^1.5.3", "qs": "^6.11.0", "quill": "2.0.0-rc.3", "vue": "^3.2.38", "vue-cropper": "^1.1.4", "vue-echarts": "^6.5.4", "vue-router": "^4.1.5", "vuedraggable": "^4.1.0"}, "devDependencies": {"@commitlint/cli": "^17.5.1", "@commitlint/config-conventional": "^17.4.4", "@types/node": "^16.11.56", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "@vicons/ionicons5": "^0.12.0", "@vitejs/plugin-legacy": "^4.0.4", "@vitejs/plugin-vue": "^4.2.3", "@vitejs/plugin-vue-jsx": "^3.0.1", "@vue/tsconfig": "^0.1.3", "cross-env": "^7.0.3", "eslint": "^8.37.0", "eslint-plugin-vue": "^9.10.0", "less": "^4.1.3", "lint-staged": "^13.2.0", "naive-ui": "^2.38.1", "npm-run-all": "^4.1.5", "prettier": "^2.8.7", "rimraf": "^3.0.2", "terser": "^5.16.1", "typescript": "~4.7.4", "unplugin-auto-import": "^0.11.5", "unplugin-vue-components": "^0.22.11", "vfonts": "^0.0.3", "vite": "^4.5.2", "vite-plugin-compression": "^0.5.1", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^0.40.7"}}