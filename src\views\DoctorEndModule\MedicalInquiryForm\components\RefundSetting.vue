<template>
  <JModal
    v-model:show="isShow"
    width="680"
    title="退款"
    positiveText="提交"
    @after-leave="closeModal"
    @positive-click="_save"
    :positiveButtonProps="{
      loading: isLoading,
    }"
  >
    <n-form
      ref="formRef"
      :rules="rules"
      :model="model"
      label-width="auto"
      label-placement="left"
      require-mark-placement="right-hanging"
      :style="{
        width: '100%',
      }"
    >
      <n-grid :cols="8" :x-gap="24">
        <n-form-item-gi :span="24" label="售后类型" path="type" required>
          <n-select :value="model.type" :options="[{ label: '仅退款', value: 1 }]" disabled />
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="退款金额" path="refundAmount" required>
          <n-input :value="convertFenToYuanStr(model.refundAmount)" type="text" disabled />
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="联系电话" path="phone">
          <n-input v-model:value="model.phone" type="text" />
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="退款原因" path="reason" required>
          <n-select :value="model.reason" :options="[{ label: '其他', value: 0 }]" disabled />
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="具体原因" path="reasonDescription">
          <n-input v-model:value="model.reasonDescription" maxlength="200" show-count type="textarea" />
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="上传图片" path="afterSalelmgVOList ">
          <UploadProductImg
            ref="uploadProductImgRef"
            v-model:value="model.afterSaleImgVOList"
            accept="image/*"
            :fileListSize="9"
            is-multiple
          />
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </JModal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { useMessages } from "@/hooks";
import UploadProductImg from "@/components/UploadProductImg/index.vue";
import { afterSaleRecordRefund } from "@/services/api";
const { createMessageSuccess, createMessageError } = useMessages();

const props = withDefaults(
  defineProps<{
    data?: any;
    show: boolean;
  }>(),
  {
    data: () => {
      return {};
    },
    show: false,
  },
);
const emits = defineEmits<{
  (e: "update:show", value: boolean): void;
  (e: "refresh"): void;
}>();

const isShow = computed({
  get: () => props.show,
  set: (value: boolean) => {
    emits("update:show", value);
  },
});
const uploadProductImgRef = ref<InstanceType<typeof UploadProductImg> | null>(null);
watch(
  () => props.show,
  (newVal, oldVal) => {
    if (newVal) {
      console.log("props.data", props.data);
      model.value.refundAmount = props.data.fee;
    }
  },
);
const initialData = {
  id: null,
  type: 1,
  refundAmount: "",
  phone: "",
  reason: 0,
  reasonDescription: "",
  afterSaleImgVOList: [],
};
const model = ref({ ...initialData });

/* 表单规则 */
const rules = {};

const isLoading = ref(false);

// 关闭按钮
const closeModal = () => {
  isShow.value = false;
  initialData.afterSaleImgVOList = [];
  model.value = {
    ...initialData,
  };
};
 function convertFenToYuanStr(fen: number | string): string {
  let str = String(fen || 0).replace(/[^0-9]/g, '');
  if (str.length < 2) str = '00' + str; // 补零
  else if (str.length === 1) str = '0' + str;

  const len = str.length;
  const integer = str.slice(0, len - 2);
  const decimal = str.slice(len - 2);

  return `${integer || '0'}.${decimal}`;
}
// 确认按钮
const formRef = ref(null);
const _save = () => {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      if (uploadProductImgRef.value?.isUploadLoading) {
        createMessageError("文件正在上传中，请稍等！");
        return;
      }
      isLoading.value = true;
      const _params = {
        data: {
          orderCode: props.data?.code,
          type: model.value.type,
          reason: model.value.reason,
          reasonDescription: model.value.reasonDescription,
          refundAmount: model.value.refundAmount,
          phone: model.value.phone,
          afterSaleImgVOList: model.value.afterSaleImgVOList,
          recordType: 2,
        },
      };
      try {
        const res = afterSaleRecordRefund(_params);
        createMessageSuccess("操作成功");
        emits("refresh");
        closeModal();
      } catch (error) {
        createMessageError(`${error}`);
      } finally {
        isLoading.value = false;
      }
    }
  });
};
</script>
