<template>
  <n-popconfirm
      @positive-click="handlePositiveClick"
      @negative-click="handleNegativeClick"
  >
    <template #trigger>
      <n-button style="margin-left: 8px" size="tiny" type="info">提前结束</n-button>
    </template>
    结束后，医患双方都不能继续发送消息，请确认当前已解答好用户提问并取得用户的同意
  </n-popconfirm>

</template>

<script setup lang="ts">
import {toRef, inject} from 'vue'
import type {IConversationModel} from "@tencentcloud/chat-uikit-engine";
import {endConversation} from "@/services/api/doctorEndApi";
import {useUserStore} from "@/stores/modules/user";
import {useMessages} from "@/hooks/useMessage";
import {deepClone} from "@/utils";

const emits = defineEmits<{
  (e: 'handleFinishConversation'): void;
}>();

const {createMessageSuccess, createMessageError} = useMessages();

/** 用户 store */
const userStore = useUserStore();
const {currentConversationData, changeCurrentConversation} = inject('currentConversationData');

function handlePositiveClick() {
  handleEndConversation()
}

function handleNegativeClick() {

}

// 删除会话
const handleDeleteIMConversation = () => {
  const conversationModel: IConversationModel = currentConversationData.value;
  if (!conversationModel || !conversationModel.conversationID) {
    return;
  }
  conversationModel?.deleteConversation();
};

// 结束会话
async function handleEndConversation() {
  try {
    if (!currentConversationData.value?.inquiryId) {
      return
    }
    const params = {
      // 问诊单ID
      id: currentConversationData.value?.inquiryId
    }
    await endConversation(params)
    createMessageSuccess('结束会话成功')
    let tempConversation = deepClone(currentConversationData.value)
    tempConversation.status = 'finish'
    changeCurrentConversation(tempConversation)
    emits('handleFinishConversation')
  } catch (e) {
    createMessageError(`结束会话失败:${e}`)
  }
}

defineExpose({
  handleEndConversation,
});

</script>

<style scoped lang="less">

</style>
