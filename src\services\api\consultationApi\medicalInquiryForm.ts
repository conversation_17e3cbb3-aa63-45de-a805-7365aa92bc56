import { defHttp } from "@/services";

export const enum MedicalInquiryFormApi {
  getMedicalInquiryForm = "/pres/pageForConsultation",
  getMedicalInquiryFormDetail = "/pres/getConsultationDetail",
  medicalInquiryFormExport = "/pres/export",
  medicalInquiryFormRefund = "/afterSaleRecord/refundConsultation",
  addPatients = "/customerDrugUser/addPreBook",
  getPatients = "/customerDrugUser/list",
  addReservationForm = "/pres/create",
  cancelReservationForm = "/pres/cancelPreBook",
}

/** 获取问诊单列表 */
export function getMedicalInquiryForm(params) {
  return defHttp.post({
    url: MedicalInquiryFormApi.getMedicalInquiryForm,
    params,
  });
}
/** 获取问诊单详情 */
export function getMedicalInquiryFormDetail(params) {
  return defHttp.get({
    url: MedicalInquiryFormApi.getMedicalInquiryFormDetail,
    params,
  });
}
/** 获取问诊单导出 */
export function medicalInquiryFormExport(params) {
  return defHttp.post({
    url: MedicalInquiryFormApi.medicalInquiryFormExport,
    requestConfig: {
      responeseType: "stream",
    },
    params,
  });
}
/** 问诊单退款 */
export function medicalInquiryFormRefund(params) {
  return defHttp.post({
    url: MedicalInquiryFormApi.medicalInquiryFormRefund,
    params,
  });
}
/** 新增用药人 */
export function addPatients(params) {
  return defHttp.post({
    url: MedicalInquiryFormApi.addPatients,
    params,
  });
}
/** 用药人列表 */
export function getPatients(params) {
  return defHttp.post({
    url: MedicalInquiryFormApi.getPatients,
    params,
  });
}
/** 创建预约单 */
export function addReservationForm(params) {
  return defHttp.post({
    url: MedicalInquiryFormApi.addReservationForm,
    params,
  });
}
/** 取消预约单 */
export function cancelReservationForm(params) {
  return defHttp.delete({
    url: MedicalInquiryFormApi.cancelReservationForm,
    params,
    requestConfig: {
      isQueryParams: true,
    },
  });
}
