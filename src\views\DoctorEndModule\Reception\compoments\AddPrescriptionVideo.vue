<template>
  <n-modal
      v-model:show="show"
      preset="card"
      style="width: 500px;"
      :bordered="false"
      :closable="false"
      :auto-focus="false"
      size="small"
      @after-leave="closeModal">
    <template #header>
      <div style="display: flex;justify-content: space-between;">
        <div>上传视频</div>
        <CloseOutline
            v-if="videoPromptStatus != UploadVideoTips.uploading"
            style="width: 25px;cursor: pointer;" @click="closeModal"/>
        <n-popconfirm
            v-if="videoPromptStatus == UploadVideoTips.uploading"
            @positive-click="handlePositiveClick"
        >
          <template #trigger>
            <CloseOutline style="width: 25px;cursor: pointer;"/>
          </template>
          当前视频未上传完成，确定关闭弹窗吗
        </n-popconfirm>
      </div>
    </template>
    <n-form
        ref="formRef"
        :model="model"
        :rules="rules"
        label-width="auto"
        label-placement="left"
        require-mark-placement="right-hanging"
        size="small"
        :style="{width: '100%',}">
      <n-grid :cols="6" :x-gap="24">
        <!-- 上传视频 -->
        <n-form-item-gi
            :span="6"
            label="上传视频/音频">
          <n-upload
              ref="uploadRef"
              class="uploadWrapper"
              :custom-request="customRequest"
              :default-upload="false"
              v-model:value="model.mediaPath"
              v-model:duration="model.mediaSeconds"
              :maxFileSize="1000"
              :fileListSize="1"
              :max="1"
              :show-file-list="false"
              accept="video/mp4,audio/mp3"
              @preview="videoPreview"
              @change="handleUploadChange"
              @before-upload="beforeUpload">
            <n-upload-dragger style="width: 150px;height: 150px" v-if="!previewVideoSrcRef">
              <div style="margin-bottom: 12px">
                <n-icon size="24" :depth="3">
                  <ArchiveIcon/>
                </n-icon>
              </div>
              <n-text style="font-size: 12px">
                点击或者拖动文件到该区域来上传
              </n-text>
            </n-upload-dragger>
            <template v-else>
              <div class="preview">
                <VideoPreviewModal :src="previewVideoSrcRef"></VideoPreviewModal>
                <n-progress
                    v-if="videoPromptStatus != UploadVideoTips.notUploaded"
                    type="line"
                    :percentage="uploadProgressRef"
                    :show-indicator="false"
                    :height="3"
                    :status="progressStatus"
                    class="uploadProgress"
                />
              </div>
            </template>
          </n-upload>
        </n-form-item-gi>
      </n-grid>
    </n-form>
    <template #footer>
      <n-space justify="end">
        <n-button
            size="small"
            v-if="videoPromptStatus != UploadVideoTips.uploading"
            @click="closeModal">
          关闭
        </n-button>
        <n-popconfirm
            v-if="videoPromptStatus == UploadVideoTips.uploading"
            @positive-click="handlePositiveClick">
          <template #trigger>
            <n-button size="small">取消</n-button>
          </template>
          注意:关闭弹出会导致之前上传完成内容需重新上传
        </n-popconfirm>
        <n-button size="small" type="primary" :loading="isLoading" @click="_save">开始上传</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import {ref, computed} from 'vue'
import {CloseOutline} from "@vicons/ionicons5";
import {useMessages} from "@/hooks";
import {receptionPresUploadVideo} from "@/services/api/doctorEndApi";
import {isFunction} from "@/utils";
import type {UploadInst, UploadCustomRequestOptions, UploadFileInfo} from 'naive-ui'
import {ArchiveOutline as ArchiveIcon} from '@vicons/ionicons5'
import VideoPreviewModal from "@/views/ContentModule/VideoManagement/components/VideoPreviewModal/index.vue";
import {uploadType} from "@/views/ContentModule/VideoManagement/type";

//上传视频提示内容
const enum UploadVideoTips {
  notUploaded = "notUploaded",
  uploading = "uploading",
  uploadSucceeded = "uploadSucceeded",
  uploadError = 'uploadError'
}

defineOptions({
  name: 'AddPrescriptionVideo'
})


/* 表单实例 */
const formRef = ref(null);
/* 提示信息 */
const message = useMessages();
/* 表单参数初始化 */
const initParams = {
  mediaPath: '',
  mediaSeconds: 0,
};
const model = ref({...initParams});
/* 上传组件实例 */
const uploadRef = ref<UploadInst | null>(null)
/* 上传视频等待状态 */
const videoPromptStatus = ref(UploadVideoTips.notUploaded)
/* 上传视频预览框 */
const showVideoModalRef = ref(false);
/* 上传视频进度 */
const uploadProgressRef = ref(0)
/* 视频文件 */
const previewVideoSrcRef = ref();
/* 模态框显隐状态 */
const show = ref(false);
/* 最大上传容量，单位MB */
const maxFileSize = 1000

/* 确认--保存 */
const isLoading = ref(false);
/* 行数据 */
let rowData = null;
/* 刷新表格方法 */
let refreshFn = null

/* 表单规则 */
const rules = {
  mediaPath: {
    type: "string",
    required: true,
    trigger: ["blur", "change"],
    message: () => {
      if (!model.value.mediaPath || !model.value.mediaPath[0]) {
        return '请上传视频'
      }
      return true
    }
  },
};

/* 接收父组件参数 */
const acceptParams = (params) => {
  rowData = params.rowData;
  refreshFn = params.refresh
  show.value = true;
};

/* 清空表单 */
const formDataReset = () => {
  model.value = {...initParams};
  previewVideoSrcRef.value = null
  videoPromptStatus.value = UploadVideoTips.notUploaded
  showVideoModalRef.value = false
  uploadProgressRef.value = 0
};

/* 关闭弹窗 */
const closeModal = () => {
  /** 判断是否为正在上传 */
  if (videoPromptStatus.value == 'uploading') {
    window.location.reload()
    return
  }
  show.value = false
  formDataReset();
};

const handlePositiveClick = () => {
  closeModal()
}

function videoPreview({url}: UploadFileInfo) {
  showVideoModalRef.value = true;
}

function handleUploadChange(options: { fileList: UploadFileInfo[], file: UploadFileInfo, }) {
  previewVideoSrcRef.value = options.file.file
}

function beforeUpload(data: {
  file: UploadFileInfo;
  fileList: UploadFileInfo[];
}) {
  const {
    file: {size, type},
  } = data.file;
  if (size > maxFileSize * 1024 * 1024) {
    message.createMessageError(`该文件超出大小，大小限制为 ${maxFileSize} MB`);
    return false;
  }
  return true;
}

const _save = (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      startUpload()
    }
  });
};

async function handleSaveData(
    formData: FormData,
    onFinish: () => void,
    onError: () => void,
    onProgress: (e: {
      percent: number;
    }) => void) {
  try {
    isLoading.value = true;
    videoPromptStatus.value = UploadVideoTips.uploading
    await receptionPresUploadVideo(rowData.id, formData, ({progress}) => {
      let uploadProgress = Number((progress * 100).toFixed(2)) - 10
      uploadProgressRef.value = uploadProgress
      onProgress({percent: Number((progress * 100).toFixed(2))});
    })
    uploadProgressRef.value = 100
    videoPromptStatus.value = UploadVideoTips.uploadSucceeded
    onFinish()
    setTimeout(() => {
      message.createMessageSuccess(`添加成功`);
      show.value = false;
      // 刷新表格数据
      if (refreshFn && isFunction(refreshFn)) {
        refreshFn()
      }

    }, 300)
  } catch (e) {
    message.createMessageError(`添加失败： ${e}`);
    videoPromptStatus.value = UploadVideoTips.uploadError
    onError()
  } finally {
    isLoading.value = false;

  }
}

/* 自定义上传 */
async function customRequest(options: UploadCustomRequestOptions) {
  const {file, onFinish, onError, onProgress} = options;
  const _formData = new FormData();
  _formData.append("file", file.file);

  await handleSaveData(_formData, onFinish, onError, onProgress)
}

/* 非受控手动提交 */
function startUpload() {
  uploadRef.value?.submit()
}

const progressStatus = computed(() => {
  if (videoPromptStatus.value == UploadVideoTips.uploading) {
    return 'default'
  }
  if (videoPromptStatus.value == UploadVideoTips.uploadSucceeded) {
    return 'success'
  }
  if (videoPromptStatus.value == UploadVideoTips.uploadError) {
    return 'error'
  }
  return 'default'
})

defineExpose({
  acceptParams,
});

</script>

<style scoped lang="less">
:deep(.n-upload-trigger.n-upload-trigger--disabled) {
  cursor: auto;
}

.preview {
  width: 150px;
  height: 150px;
  position: relative;

  .uploadProgress {
    position: absolute;
    bottom: 10px;
    width: 90%;
    left: 5%;
    z-index: 99;
  }
}

</style>
