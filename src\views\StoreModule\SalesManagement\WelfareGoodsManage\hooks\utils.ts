/**
 * 根据 parentId 和 id 生成树形结构数据
 * @param {Array} data - 原始扁平数据数组
 * @returns {Array} - 生成的树形结构数据
 */
export function buildTree(data) {
  // 创建一个映射，方便查找
  const map = {};
  data.forEach(item => {
    map[item.id] = { ...item, children: [] }; // 初始化每个节点，并添加 children 属性
  });

  // 构建树形结构
  const tree = [];
  data.forEach(item => {
    if (item.parentId) {
      // 如果有 parentId，则将当前节点添加到其父节点的 children 中
      const parent = map[item.parentId];
      if (parent) {
        parent.children.push(map[item.id]);
      }
    } else {
      // 如果没有 parentId，则为根节点
      tree.push(map[item.id]);
    }
  });

  return tree;
}

/**
 * 遍历数据并返回具有子节点的项的 ID 数组
 * @param {Array} items - 要遍历的项
 * @returns {Array} - 包含具有子节点的项的 ID 的数组
 */
export function getExpandKeys(items) {
  const expandKeys = []; // 存储展开项的数组

  items.forEach(item => {
    if (item.children && item.children.length > 0) {
      expandKeys.push(item.key); // 添加当前项的 key
      // 递归处理子节点并合并结果
      expandKeys.push(...getExpandKeys(item.children));
    }
  });

  return expandKeys; // 返回展开项的数组
}
