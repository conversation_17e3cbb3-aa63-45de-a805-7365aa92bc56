<template>
  <div class="spec-group">
    <!-- <div class="spec-group__title">商品规格设置<span style="color: #FF4D4F;">*</span> （规格1*规格2*规格3的数量至多不超过450）</div> -->
    <div class="spec-group__item" v-for="(spec,index) in listData" :key="index">
        <div class="spec-group__item-delete" >
        <n-icon class="spec-group__item-close" v-if="listData.length > 1" @click="handleDeleteSpec(spec)" :component="TrashOutline" color="#1677FF" size="20" /></div>
      <div class="spec-group__item-header">
        <span class="spec-group__item-tag">规格名{{ index + 1 }}</span>
        <div class="spec-group__item-input">
          <n-input v-model:value="spec.attributeName" placeholder="请输入规格名" autosize style="min-width: 200px" />
        </div>
      </div>
      <div class="spec-group__item-values">
        <span class="spec-group__item-tag">规格值</span>
        <div class="spec-group__values-wrapper">
          <div class="spec-group__value-item" v-for="(item,index) in spec.specValue" :key="index">
            <n-input v-model:value="item.attributeValue" placeholder="请输入规格值" autosize style="min-width: 200px" />
            <n-icon class="spec-group__item-close" :component="TrashOutline" color="#1677FF" @click="handleDeleteSpecValue(spec, item)" v-if="spec.specValue.length > 1" />
            <n-button type="primary" size="small" v-if="spec.specValue.length === index + 1 && !isOver450" @click="handleAddSpecValue(spec)" :disabled="isLoading" >添加规格值</n-button>
          </div>
        </div>
      </div>
    </div>
    <n-button type="primary" :disabled="isLoading" v-if="listData.length < 3" @click="handleAddSpec"  >新增规格</n-button>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, toRefs, onMounted, computed, watch} from 'vue'
import { TrashOutline } from "@vicons/ionicons5";
import type { SpecList } from '../types';
import { useBoolean, useMessages } from '@/hooks';
import { createDummyId, deepClone, isArray } from "@/utils";

const { createMessageError } = useMessages();
import { addProductSpecId } from "@/services/api"
// const addProductSpecId = ()=>{
//     /** 0.5秒自动resolve */
//     return new Promise((resolve, reject) => {
//         setTimeout(() => {
//             resolve(createDummyId())
//         }, 100)
//     })
// }


const props = withDefaults(defineProps<{
    value: Array<SpecList>;
    type: 'add' | 'edit' | 'view';
}>(), {
    value: () => [],
    type: 'add'
})
const emits = defineEmits<{
    (e: "update:value", value: Array<any>): void;
}>();

const listData = computed({
    get() {
        return props.value;
    },
    set(val) {
        emits('update:value', val);
    }
})

onMounted(()=>{
    if(props.type === 'add') {
        handleAddSpec()
    }
})

const isLoading = ref(false);

/** 添加规格值 */
const handleAddSpecValue = (spec) => {
    isLoading.value = true;
    addProductSpecId().then(res=>{
        spec.specValue.push({
            attributeValue:"",
            id:res
        })
    }).catch(err=>{
        createMessageError(`新增规格失败：${err}`)
    }).finally(()=>{
        isLoading.value = false;
    })
}
/**  删除规格值 */
const handleDeleteSpecValue = (spec, item) => {
    spec.specValue = spec.specValue.filter(i => i !== item);
}

/** 添加规格 */
const handleAddSpec = () => {
    isLoading.value = true;
    addProductSpecId().then(res=>{
        listData.value.push({
            attributeName: '',
            specValue:[{
                attributeValue:"",
                id:res
            }]
        })
    }).catch(err=>{
        createMessageError(`新增规格失败：${err}`)
    }).finally(()=>{
        isLoading.value = false;
    })
}
/** 删除规格 */
const handleDeleteSpec = (spec) => {
    listData.value = listData.value.filter(item => item !== spec);
}

/** 规格乘积是否超过450 */
const isOver450 = computed(() => {
    return listData.value.reduce((total, item) => total * item.specValue.length, 1) > 450;
})

</script>
<style scoped lang="less">
.spec-group {
  &__title {
    margin-bottom: 16px;
    font-weight: 500;
  }

  &__item {
    width: calc(100% - 100px);
    padding: 16px;
    margin-bottom: 16px;
    box-sizing: border-box;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    position: relative;
    &-delete{
        position: absolute;
        top: 10px;
        right: 10px;
        cursor: pointer;
    }
  }

  &__item-header {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  &__item-tag {
    min-width: 60px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 15px;
    color: #666666;
    line-height: 28px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  &__item-close {
    cursor: pointer;
  }

  &__item-input {
    flex: 1;
  }

  &__item-values {
    display: flex;
    align-items: flex-start;
    gap: 12px;
  }

  &__values-wrapper {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
  }

  &__value-item {
    // 规格值项样式
    display: flex;
    align-items: center;
    gap: 5px;
  }
}
</style>
