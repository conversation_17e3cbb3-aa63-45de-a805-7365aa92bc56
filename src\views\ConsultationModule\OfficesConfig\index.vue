<template>
  <div class="wrapper inner-page-height">
    <n-space reverse class="header">
      <jAddButton type="primary" @click="handleOrganize(OfficesConfigEnum.Add)" v-if="hasDepartmentConfigAddFirstLevelAuth" >新增一级科室</jAddButton>
      <n-button @click="refresh" class="store-button" :loading="isLoading">刷 新</n-button>
    </n-space>
    <n-data-table
      ref="expandedTable"
      :columns="columns"
      :data="data"
      :row-key="rowKey"
      default-expand-all
      :style="{height: 'calc(100vh - 150px)'}"
      :max-height="tableHeight"
      :loading="isLoading"
    >
      <template #empty>
        <div class="infoWrapper">
          <img style="height: 210px;" :src="EmptyDataSrc" alt="" />
          <div class="notice">暂无数据</div>
        </div>
      </template>
    </n-data-table>
    <officesSetting v-model:show="isShow" :row="handleRow" @refresh="refresh" :type="handleType" />
  </div>
</template>

<script lang="tsx" setup>
import type { DataTableColumns } from 'naive-ui'
import EmptyDataSrc from "@/assets/image/exception/emptyData.png";
import { hasDepartmentConfigAddFirstLevelAuth, hasDepartmentConfigAddSecondLevelAuth, hasDepartmentConfigDeleteAuth, hasDepartmentConfigEditAuth } from './authList';
import officesSetting from './components/officesSetting.vue';
import { OfficesConfigEnum } from './type';
import { onMounted, ref,onUnmounted } from 'vue';
import Popconfirm from '@/components/Popconfirm/index.vue'
import { departmentList,deleteDepartment } from '@/services/api';
import { useMessages } from "@/hooks";
import JImage from "@/components/JImage/index.vue";
const message = useMessages();

// 监听表格高度
onMounted(()=>{
  handleResize();
  window.addEventListener('resize', handleResize);
  getDepartmentList();
});

const isLoading = ref(false);
/** 获取科室列表 */
const getDepartmentList = () => {
  isLoading.value = true;
  departmentList().then((res) => {
    data.value = res
  }).catch((err)=>{
    message.createMessageError(`获取科室列表失败：${err}`);
  }).finally(() => {
    isLoading.value = false;
  })
}
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
const tableHeight = ref(0); // 用于存储表格高度
const expandedTable = ref(null);
const handleResize = () => {
  tableHeight.value = expandedTable.value.$el.offsetHeight - 55;
}

interface RowData {
  name: string
  id: string
  children?: RowData[]
}
const rowKey = (row: RowData) => row.id;
const data = ref<RowData[]>([])
const columns: DataTableColumns<RowData> = [
  {
    title: '科室名称',
    key: 'name',
    className:"name-column",
    render: rowData => {
      return (
        <n-space align="center" justify="left">
          { rowData?.img ? <JImage img-path={rowData?.img} /> : null}
         {rowData.name}
         </n-space>
      )
    },
  },
  {
    title: '创建时间',
    key: 'createTime'
  },
  {
    title: "操作",
    key: "action",
    width: 200,
    fixed: "right",
    render: rowData => {
      return (
        <n-space align="center" justify="center">
          {
            hasDepartmentConfigDeleteAuth ?
            <Popconfirm
              onHandleClick={() =>clickDelete(rowData.id)} 
              loading={deleteLoading.value} 
              buttonContent ={'删除'} 
              type={'error'} 
              promptContent={'如有下级科室一并删除，删除后该科室的医生将变更为无科室状态。'}/> : null
          }
          {
            hasDepartmentConfigEditAuth ?
            <n-button
            text
            size="small"
            type="primary"
            onClick ={()=>handleOrganize(OfficesConfigEnum.Edit,rowData)}>
            编辑
            </n-button> : null
          }
          {
            hasDepartmentConfigAddSecondLevelAuth && rowData.level === 1 ?
            <n-button
            text
            size="small"
            type="primary"
            onClick ={()=>handleOrganize(OfficesConfigEnum.AddSub,rowData)}>
            创建二级科室
            </n-button> : null
          }
        </n-space>
      );
    },
  }
]

const deleteLoading = ref(false);
const clickDelete = (id: string) => {
  deleteLoading.value = true;
  deleteDepartment({id}).then((res: any) => {
    message.createMessageSuccess('删除成功');
    refresh();
  }).catch(err=>{
    message.createMessageError(`${err}`);
  }).finally(()=>{
    deleteLoading.value = false;
  })
};

const handleRow = ref<RowData>({} as RowData);
const handleType = ref<OfficesConfigEnum>(OfficesConfigEnum.Add);
const handleOrganize = (type: OfficesConfigEnum, rowData?: RowData) => {
  isShow.value = true;
  handleRow.value = rowData;
  handleType.value = type
};

const isShow = ref(false);
const refresh = () => {
  getDepartmentList()
};
</script>

<style lang="less" scoped>
@import "@/styles/defaultVar.less";
.wrapper{
  background: #ffffff;
  padding: 10px;
  box-sizing: border-box;
  .header{
    margin-bottom: 10px;
  }
  .infoWrapper {
    width: 100%;
    text-align: center;
    padding: 70px;
    box-sizing: border-box;
    .notice {
      color: #333333;
      line-height: 29px;
      font-size: 16px;
    }
  }
  :deep(.n-data-table-base-table){
    height: 100vh;
    .name-column {
      display: flex;
      align-items: center;
    }
  }

}
</style>
