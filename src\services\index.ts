import { TAxios } from "@/utils/http/Axios/index.js";
import { getApiUrlPrefix } from "@/utils/http/urlUtils";
import type { RequestOptions } from "@/utils/http/Axios/type";

const requestOptions: RequestOptions = {
  withToken: true,
  isRetry: false,
  isQueryParams: false,
  isReturnRawResponse: false,
  errorMsgMode: "message",
  requestContentType: "json",
  responeseType: "json",
};

export const defHttp = new TAxios({
  withCredentials: false,
  timeout: 30000,
  baseURL: getApiUrlPrefix(),
  requestOptions,
});