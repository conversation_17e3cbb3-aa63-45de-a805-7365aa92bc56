
import { ref } from "vue";
import { useMessages } from "@/hooks";
import { updateSystemConfig, getSystemPointEnable, updateIntegralEnabled } from "@/services/api";

export default function useTriggerPointsShop() {
    const { createMessageError, createMessageSuccess } = useMessages();
    const isPointEnable = ref(false);
    const editId = ref(null);
    const editKey = ref(null);

    /** 获取是否启用积分功能 */
    async function getPointsShop() {
        try {
          const { value, id, key } = await getSystemPointEnable();
          isPointEnable.value = value === 'true';
          editId.value = id;
          editKey.value = key;
        } catch (error) {
          createMessageError('获取系统配置失败：' + error);
        }
    }

    /** 修改系统配置 */
    async function setPointsSystem(value: boolean) {
        try {
            let _params = {
                data: {
                    id: editId.value,
                    value: value ? 'true' : 'false',
                    key: editKey.value
                }
            };
            await updateIntegralEnabled(_params);
            createMessageSuccess(value ? "启用积分商城成功！" : "关闭积分商城成功！");
            // 更新
            getPointsShop();
        } catch (error) {
            createMessageError('修改系统配置失败：' + error);
        }
    }

    return {
        isPointEnable,
        getPointsShop,
        setPointsSystem,
    }
}