<script setup lang="ts">
import { ref, toRefs, computed, watch, type Ref } from "vue";
import { isString, isArray, isNumber } from "@/utils/isUtils";
import {
  ChevronUp as UpIcon,
  ChevronDown as DownIcon,
} from "@vicons/ionicons5";

type filterOptions = {
  label: string;
  value: string | number;
};

const props = withDefaults(
  defineProps<{
    title: string;
    isMultiple?: boolean;
    titleChange?: boolean;
    emptyValue?: string | number | null;
    options: Array<filterOptions> | null;
    typeState?:boolean
    businessType?:boolean
  }>(),
  {
    isMultiple: false,
    titleChange: false,
    emptyValue: null,
    typeState:false,
    businessType:false
  }
);

const emits = defineEmits<{
  (e: "update:filterValue", value): void;
  (e: "update:typeState", value): void;
  (e: "update:businessType", value): void;
  (e: "update:show", value): void;
}>();

const { isMultiple, titleChange, emptyValue, options } = toRefs(props);
const title = ref(props.title);
const iconUpOrDowm = ref(false);
const iconColorShow = ref(false)
const checkedValue = ref("") as Ref<string | number | Array<string | number>> ;
const status = ref(false)
//emptyValue代理
const defaultEmptyValue = "";

const filterOptions = computed(() => {
  const data: Array<filterOptions> = [
    { label: "全部", value: defaultEmptyValue },
  ];
  if (isArray(options.value)) return data.concat(options.value);
  else return null;
});

const multiple = computed(() => {
  if (isMultiple.value && titleChange.value)
    throw new Error("isMultiple and titleChange cannot both be set to 'true'");
  return isMultiple.value ? (titleChange.value ? false : true) : false;
});

function PopoverShow(value: boolean) {
  if(status.value){
    checkedValue.value = "";
    emits("update:typeState", false)
    emits("update:businessType", false)
  }
  iconUpOrDowm.value = value;
  emits("update:show", value);
}

function filterChange(value, option) {
  let newValue = [];
  if (isString(value) || isNumber(value)) {
    if (value === defaultEmptyValue) {
      newValue.push(emptyValue.value);
      checkedValue.value = defaultEmptyValue;
    } else {
      newValue.push(value);
      checkedValue.value = value;
    }
  } else if (isArray(value)) {
    if (isMultiple.value && value.includes(defaultEmptyValue)) {
      newValue = [emptyValue.value];
      checkedValue.value = [defaultEmptyValue];
    } else {
      newValue = value;
      checkedValue.value = value;
    }
  }

  if (titleChange.value) {
    if (option.value === defaultEmptyValue) title.value = props.title;
    else title.value = option.label;
  }
  emits("update:filterValue", newValue);
}
watch(()=>{return checkedValue.value},(newValue)=>{
  if(isString(newValue) || isNumber(newValue)) {
    if(newValue != "")  iconColorShow.value = true
    else iconColorShow.value = false
    
  } else if(isArray(newValue)) {
    if(!newValue.includes(defaultEmptyValue)) iconColorShow.value = true
    else iconColorShow.value = false
  }
})

watch(()=>{
  return props.typeState
},(newVal)=>{
  status.value = newVal
  if(newVal)  PopoverShow()
})

watch(()=>{
  return props.businessType
},(newVal)=>{
  status.value = newVal
  if(newVal)  PopoverShow()
})
</script>

<template>
  <div class="centerBox">
    <p>{{ title }}</p>
    <n-popselect
      v-bind="$attrs"
      :value="checkedValue"
      :options="filterOptions"
      :multiple="multiple"
      trigger="click"
      scrollable
      @update:value="filterChange"
      @update:show="PopoverShow"
    >
      <n-button text class="centerBox" style="padding-left: 2px">
        <n-icon size="16" :color="iconColorShow ? '#1677FF':''">
          <component :is="iconUpOrDowm ? UpIcon : DownIcon" />
        </n-icon>
      </n-button>
    </n-popselect>
  </div>
</template>
<style scoped>
.centerBox {
  display: flex;
  align-items: center;
}

</style>
