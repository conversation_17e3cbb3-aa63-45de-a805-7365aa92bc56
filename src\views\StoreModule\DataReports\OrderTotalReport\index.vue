<template>
  <div class="order-wrapper">
    <!--介绍 -->
    <n-card title="全平台订单统计" size="small" class="header" :bordered="false">
      按订单创建日期区间或月份统计平台所有商品的订单数据。
    </n-card>
    <!-- 表格 -->
    <div class="order-content">
      <TabsLayout
        v-model:value="tabNameRef"
        :tabsData="tabsData"
        onlyTabs
        height="100%"
        paneClass="orderTotalReport-wrapper-tabs"
      >
        <keep-alive>
          <component :is="currentPage" />
        </keep-alive>
      </TabsLayout>
    </div>
  </div>
</template>

<script lang="ts" setup name="OrderTotalReport">
import { ref, computed } from "vue";
import { OrderTotalReportTypeEnum } from "@/enums";
/** 相关组件 */
import TabsLayout from "@/layout/TabsLayout.vue";
import DailyOrderTotalReport from "./modules/DailyOrderTotalReport/index.vue";
import MonthlyOrderTotalReport from "./modules/MonthlyOrderTotalReport/index.vue";

const tabNameRef = ref(OrderTotalReportTypeEnum.Daily);

/** Tab */
const tabsData = [
  {
    label: "按日统计",
    key: OrderTotalReportTypeEnum.Daily,
  },
  {
    label: "按月统计",
    key: OrderTotalReportTypeEnum.Monthly,
  }
];

/** 组件映射 */
const pageMap = {
  [OrderTotalReportTypeEnum.Daily]: DailyOrderTotalReport,
  [OrderTotalReportTypeEnum.Monthly]: MonthlyOrderTotalReport
}

const currentPage = computed(() => pageMap[tabNameRef.value])
</script>

<style lang="less" scoped>
@import "@/styles/default.less";
.order-wrapper {
  width: 100%;
  height: 100%;
  .header {
    height: 86px;
    border-bottom: 1px solid @default-border-color;
  }
  .order-content {
    height: calc(100% - 86px);
  }
}
:deep(.orderTotalReport-wrapper-tabs) {
  height: calc(100% - 36px) !important;
}
</style>
