import {defHttp} from '@/services';

export const enum BaseSettingApi {
    /** 基础设置 */
    getBaseSetting = '/interrogationBasicSettings/page',
    /** 编辑基础设置 */
    editBaseSetting = '/interrogationBasicSettings/update',
}

/** 基础设置 */
export function getBaseSetting(params) {
    return defHttp.post({
        url: BaseSettingApi.getBaseSetting,
        params
    })
}

/** 编辑基础设置 */
export function editBaseSetting(params) {
    return defHttp.put({
        url: BaseSettingApi.editBaseSetting,
        params
    })
}
