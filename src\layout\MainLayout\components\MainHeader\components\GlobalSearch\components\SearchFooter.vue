<template>
  <div style="height: 44px;display: flex;align-items: center;padding-left: 24px;padding-right: 24px;">
    <span style="margin-right: 14px;display: flex;align-items: center;">
      <n-icon size="18" class="icon">
        <SvgIcon localIcon="keyboardReturn" />
      </n-icon>
      <span>确认</span>
    </span>
    <span style="margin-right: 14px;display: flex;align-items: center;">
      <n-icon size="18" class="icon">
        <SvgIcon localIcon="arrowUpThin" />
      </n-icon>
      <n-icon size="18" class="icon">
        <SvgIcon localIcon="arrowDownThin" />
      </n-icon>
      <span>切换</span>
    </span>
    <span style="display: flex;align-items: center;">
      <n-icon size="18" class="icon">
        <SvgIcon localIcon="keyboardEsc" />
      </n-icon>
      <span>关闭</span>
    </span>
  </div>
</template>

<script lang="ts" setup>
import SvgIcon from "@/components/SvgIcon/index.vue";

defineOptions({ name: 'SearchFooter' });
</script>

<style lang="less" scoped>
.icon {
  padding: 2px;
  margin-right: 6px;
  box-shadow:
    inset 0 -2px #cdcde6,
    inset 0 0 1px 1px #fff,
    0 1px 2px 1px #1e235a66;
}
</style>
