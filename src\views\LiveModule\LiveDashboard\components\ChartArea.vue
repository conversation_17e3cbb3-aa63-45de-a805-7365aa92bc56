<template>
    <div class="chart-area-wrapper">
        <div class="chart-area-header">
            <h3>整体综合趋势</h3>
            <div class="chart-area-header-tabs" >
                <n-tabs type="segment" animated pane-class="tab-pane" v-model:value="activeTab">
                  <n-tab-pane 
                    v-for="tab in tabs" :key="tab.value" :name="tab.value" :tab="tab.label"></n-tab-pane>
                </n-tabs>
            </div>
        </div>
        <div class="chart-area-content">
            <div ref="chartRef" class="chart-container"></div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onBeforeUnmount, nextTick } from 'vue';
import * as echarts from 'echarts';
import dayjs from "dayjs";
import { TimeRangeEnum } from "../type"
import useLiveDashboard from "../hook/useLiveDashboard";
import { useMessages } from "@/hooks";
import {liveActivityOrderDataLine,liveActivityOrderDataLineList} from "@/services/api"
import { useAutoRefresh,useAutoTime } from "../hook/useAutoTime"

const { liveRoomDataRef } = useLiveDashboard()
const { completeTimeData,completeFiveMinuteData } = useAutoTime()
const message = useMessages();
const activeTab = ref<TimeRangeEnum>(TimeRangeEnum.FIVE_MIN)
const chartRef = ref<HTMLElement | null>(null);
let chartInstance = null;
let resizeObserver = null;

const tabs = [
  { value: TimeRangeEnum.FIVE_MIN, label: '近5分钟' },
  { value: TimeRangeEnum.THIRTY_MIN, label: '近半小时' },
  { value: TimeRangeEnum.ONE_HOUR, label: '近1小时' },
  { value: TimeRangeEnum.ALL, label: '全部' }
];
// 图表配置数据
const chartConfig = {
  // 图例配置
  legends: [
    { name: '成交金额', color: '#1ef3c6', yAxisIndex: 0 },
    { name: '在线人数', color: '#00eaff', yAxisIndex: 1 },
    { name: '购买人数', color: '#11aa5e', yAxisIndex: 1 },
    { name: '成交订单数', color: '#fb5540', yAxisIndex: 1 },
    { name: '成交商品数', color: '#9e81bd', yAxisIndex: 1 }
  ],
  // 数据键名映射
  dataKeys: ['totalMoney', 'onlineNumber', 'totalCustomers', 'totalOrders', 'totalProducts']
};
// 示例数据
const xData = ref<string[]>([]);
const seriesData = ref({
  totalMoney: [],
  onlineNumber: [],
  totalCustomers: [],
  totalOrders: [],
  totalProducts: []
});
// 生成 series 配置
const generateSeries = () => {
  return chartConfig.legends.map((legend, index) => {
    if (activeTab.value === TimeRangeEnum.FIVE_MIN && legend.name === '在线人数') {
      return null;
    }
    return {
      name: legend.name,
      type: 'line',
      data: seriesData.value[chartConfig.dataKeys[index]],
      yAxisIndex: legend.yAxisIndex,
      smooth: true,
      showSymbol: false,
      itemStyle: { color: legend.color },
      lineStyle: { type: 'solid' },
      areaStyle: { 
        color: `${legend.color}26`
      }
    };
  })
};

const option = {
  tooltip: { trigger: 'axis' },
  legend: {
    data: chartConfig.legends.map(item => ({
      name: item.name,
      icon: 'circle'
    })),
    top: 0,
    textStyle: { color: '#fff' }
  },
  splitLine: {
    show: true,
  },
  grid: { left: 60, right: 60, top: 80, bottom: 20 },
  xAxis: {
    type: 'category',
    data: xData.value,
    axisLine: { 
      lineStyle: { color: '#444' }
    },
    axisLabel: { 
      color: '#d4d7e0',
      interval: (index, value) => {
        if (xData.value.length <= 20) return true;
        const interval = Math.ceil(xData.value.length / 20);
        return index % interval === 0;
      }
    },
    boundaryGap: false,
    axisTick: { show: false },
  },
  yAxis: [
    {
      type: 'value',
      name: '金额(元)',
      nameTextStyle: {
        padding: [0, 0, 6, 50],
        fontSize: 16,
      },
      position: 'left',
      min: 0,
      axisLine: { lineStyle: { color: '#d4d7e0' } },
      axisLabel: { 
        color: '#d4d7e0',
        formatter: function(value) {
          if (value >= 10000) {
            return (value / 10000).toFixed(1) + 'w';
          } else if (value >= 1000) {
            return (value / 1000).toFixed(1) + 'k';
          }
          return value;
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#444',
          type: 'dashed',
        }
      },
    },
    {
      type: 'value',
      name: '人数/订单数/商品数',
      nameTextStyle: {
        padding: [0, 150, 6, 0],
        fontSize: 16,
      },
      position: 'right',
      min: 0,
      axisLine: { lineStyle: { color: '#d4d7e0' } },
      axisLabel: { color: '#d4d7e0' },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#444',
          type: 'dashed',
        }
      },
    }
  ],
  series: generateSeries()
};

function renderChart() {
  if (chartRef.value) {
    if (!chartInstance) {
      chartInstance = echarts.init(chartRef.value);
    }
    chartInstance.clear();
    option.xAxis.data = xData.value;
    option.series = generateSeries();
    chartInstance.setOption(option, true);
  }
}

function resizeChart() {
  if (chartInstance) {
    chartInstance.resize({
      animation: {
        duration: 300
      }
    });
  }
}

  onMounted(() => {
  // 确保在 DOM 更新后再初始化图表
  nextTick(async() => {
    if (chartRef.value) {
      resizeObserver = new ResizeObserver(() => {
        resizeChart();
      });
      resizeObserver.observe(chartRef.value);
    }
  });
});

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
});
/**获取直播间订单线形图数据（近5分钟） */
async function getLiveActivityOrderDataLine(params) {
    try {
        let aeraData;
        const api = activeTab.value == TimeRangeEnum.FIVE_MIN ? liveActivityOrderDataLine : liveActivityOrderDataLineList
        const res = await api(params)
        if(activeTab.value == TimeRangeEnum.FIVE_MIN){
          aeraData = completeTimeData(res,params.data.startTime,params.data.endTime)
        }else{
          aeraData = completeFiveMinuteData(res,params.data.startTime,params.data.endTime)
        }
        xData.value = aeraData.map(item => dayjs(item.timeMinute).format('HH:mm'));
        seriesData.value = {
          totalMoney: aeraData.map(item => Number((item.totalMoney / 100).toFixed(2))),
          onlineNumber: aeraData.map(item => item.onlineNumber),
          totalCustomers: aeraData.map(item => item.totalCustomers),
          totalOrders: aeraData.map(item => item.totalOrders),
          totalProducts: aeraData.map(item => item.totalProducts)
        };
        // 使用 nextTick 确保 DOM 更新后再渲染图表
        nextTick(() => {
          renderChart();
        });
    } catch (error) {
        // message.createMessageError('图表异常'+error)
        console.log("图表异常"+error);
        
    }
}
// 5分钟调用一次
const timer = useAutoRefresh([200,300],()=>getLiveActivityOrderDataLine(getParams(calculateTimeRange(activeTab.value))))
// tab切换
const calculateTimeRange = (tabVal:TimeRangeEnum) => {
  const now = dayjs();
  let startTime;
  let endTime = now.subtract(1, 'minute'); // 结束时间 = 当前时间 - 1分钟
  switch (tabVal) {
    case TimeRangeEnum.FIVE_MIN:
      startTime = endTime.subtract(5, 'minute');
      break;
    case TimeRangeEnum.THIRTY_MIN:
      startTime = endTime.subtract(30, 'minute');
      break;
    case TimeRangeEnum.ONE_HOUR:
      startTime = endTime.subtract(1, 'hour');
      break;
    case TimeRangeEnum.ALL:
      startTime = liveRoomDataRef.value.liveStartTime 
      break;
    default:
      return null;
  }
  return { 
    startTime:tabVal == TimeRangeEnum.ALL?startTime:startTime.format('YYYY-MM-DD HH:mm:ss'), 
    endTime:endTime.format('YYYY-MM-DD HH:mm:ss') 
  };
};
const getParams = (time)=>{
  const params = {
    data:{
      startTime:time.startTime,
      endTime:time.endTime,
    }
  }
  if(activeTab.value == TimeRangeEnum.FIVE_MIN){
    params.data['id'] = liveRoomDataRef.value.id
  }else{
    params.data['liveActivityId'] = liveRoomDataRef.value.id
  }
  return params
}

// 监听 tab 切换，确保图表正确渲染
watch(activeTab, () => {
  nextTick(() => {
    timer.triggerRefresh()
  });
});
watch(
  () => liveRoomDataRef.value.id,
  (newVal) => {
    // 初始化时获取一次数据
    timer.triggerRefresh()
  }
);
</script>

<style lang="less" scoped>
.chart-area-wrapper{
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    
    .chart-area-header{
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 16px 4px 16px;
        flex-shrink: 0;
        
        .chart-area-header-tabs{
            width: 320px;
            :deep(.n-tabs-rail){
                background: #162556;
            }
            :deep(.n-tabs-tab){
                color: #65708c;
            }
            :deep(.n-tabs-capsule){
                background: #2277e5;
            }
            :deep(.n-tabs-tab--active){
                color: #fff;
            }
        }
    }

    .chart-area-content {
        flex: 1;
        min-height: 0;
        padding-bottom:16px;
        
        .chart-container {
            width: 100%;
            height: 100%;
        }
    }
}
</style>

