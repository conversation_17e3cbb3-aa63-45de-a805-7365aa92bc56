import { defHttp } from "@/services";

/** 积分规则配置 */
export const enum integralRuleApi {
  get = "/pointRule/list",
  add = "/pointRule/saveRule",
}

/**
 * @description 积分规则列表
 */
export function getPointRule(params) {
    return defHttp.post({
        url: integralRuleApi.get,
        params,
    });
}

/**
 * @description 保存积分规则
 */
export function addPointRule(_params) {
    return defHttp.post({
        url: integralRuleApi.add,
        params: {
            data: _params,
        },
    });
}

