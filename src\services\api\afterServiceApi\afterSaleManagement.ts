import { defHttp } from '@/services';

/** 售后管理 */
export const enum AfterSaleApi {
    afterSaleRecordPage = "afterSaleRecord/page",
    afterSaleRecordDoAction = "afterSaleRecord/doAction",
    afterSaleRecordGetDetail = "afterSaleRecord/getDetail",
    afterSaleRecordGetPageStats = "afterSaleRecord/getPageStats"
}


//分页查询列表
export function afterSaleRecordPage(params) {
    return defHttp.post({
      url: AfterSaleApi.afterSaleRecordPage,
      params
    });
}

//执行售后动作
export function afterSaleRecordDoAction(params) {
    return defHttp.post({
      url: AfterSaleApi.afterSaleRecordDoAction,
      params
    });
}

//获取售后记录详情
export function afterSaleRecordGetDetail(params) {
  return defHttp.get({
    url: AfterSaleApi.afterSaleRecordGetDetail + '?recordNo=' + params.recordNo,
  });
}

//获取分页表头统计

export function afterSaleRecordGetPageStats() {
  return defHttp.get({
    url: AfterSaleApi.afterSaleRecordGetPageStats,
  });
}