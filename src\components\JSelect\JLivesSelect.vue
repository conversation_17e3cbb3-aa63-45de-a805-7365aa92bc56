
<template>
    <JSelect
	        size="small"
	    	:value="props.value"
	    	:loading="isLoading"
	    	:onFocus="handlerFocus"
	    	:options="liveList"
	    	:onClear="handleClear"
	    	@update:value="onChange"
	    	placeholder="请选择直播间名称"
	    	:filter="customFilter"
	    	@scroll="handleScroll"
	    	@keydown.enter="handleSearch"
	    	:reset-menu-on-options-change="isResetSelectStatusRef"
	    	:disabled="props.isDisabled"
	    	:clearable="props.isClearable"
	    	:filterable="props.filterable"
	    	:style="{ width }"
	    />
</template>

<script lang="ts" setup name='JLivesSelect'>
import { ref } from "vue";
import { isString } from "@/utils";
import JSelect from "@/components/JSelect/index.vue";
import { getLivePage } from "@/services/api";
import { useMessages } from "@/hooks";
import { useSystemStoreWithoutSetup } from "@/stores/modules/system";

/** @description 商品下拉组件, 暂时不支持多选 */
/* Props */
const props = withDefaults(
	defineProps<{
		value: Array<string | number> | string | number | null; // 选择的值
		isImmediately?: boolean; // 是否立即加载
		isMultiple?: boolean; // 是否多选 --> 非必传，默认值为false
		width?: string | number; // 组件宽度 --> 非必传，默认值为460
		isClearable?: boolean;// 是否可清除选项
		isDisabled?: boolean; // 是否禁用
		filterable?: boolean; // 是否可过滤
	}>(),
	{
		isImmediately: false,
		isMultiple: false,
		width: 460,
		isClearable: true,
		isDisabled: false,
		filterable: true,
	},
);

/** emits */
const emits = defineEmits<{
	(e: "update:value", selectValue: any): void; // 更新选择值事件
	(e: 'update:selectedOptions',keys: Array<any>): void; // 选中项
}>();

const systemStore = useSystemStoreWithoutSetup();

/* 提示 */
const message = useMessages();
/** 是否加载 */
const isLoading = ref(false);
/** 重置选择状态 */
const isResetSelectStatusRef = ref(false);
/** 商品列表 */
const liveList = ref([]);
const _resultTempList = [];
/* 执行搜索返回的内容*/
let recordsTotal = 1; // 总记录数
/** 商品分页参数 */
const _params = {
	data: {
		name: null, // 直播名
	},
	pageVO: {
		current: 1,
		size: 100,
	},
};
/** 筛选并转化 */
const handleData = (filterData: any[]) => {
	return filterData.map(item => {
        let label = `${item.name}(ID:${item.id})`;
        return { label, value: item.id, disabled: false };
	});
};

/** 聚焦事件处理函数 */
function handlerFocus() {
	// 如果直播列表为空
	if (!liveList.value.length) {
		getLivesList();
	}
};

/** 清空事件处理函数 */
function handleClear() {
	_params.data.name = null;
	emits("update:value", null);
};

/** 选择值改变事件处理函数 */
function onChange(value) {
	_params.data.name = null;
	let tempGoodsList = liveList.value.filter(item => value.includes(item.id));
	emits("update:value", value);
	emits("update:selectedOptions", tempGoodsList);
};

/** 自定义过滤函数 */
function customFilter(keyword, options) {
	const labelMatch = options.label
		.toLowerCase()
		.includes(keyword.toLowerCase());
	const valueMatch = options.value
		.toLowerCase()
		.includes(keyword.toLowerCase());
	return labelMatch || valueMatch;
};

/** 滚动事件处理函数 */
function handleScroll(e) {
	const currentTarget = e.currentTarget as HTMLElement;
	if (
		currentTarget.scrollTop + currentTarget.offsetHeight >=
		currentTarget.scrollHeight
	) {
		// 如果当前页乘以每页大小小于总记录数
		if (_params.pageVO.current * _params.pageVO.size < recordsTotal) {
			_params.pageVO.current++; // 当前页加1
			getLivesList();
		}
	}
};

/** 搜索事件处理函数 */
function handleSearch(event) {
	_params.data.name = event.target.value ?? null;
	_params.pageVO.current = 1;
	getLivesList();
};

/** 获取商品数据 */
const getLivesList = async () => {
	try {
		isLoading.value = true;
		const data = await getLivePage(_params);
		const { total, current, size, records } = data;
		_params.pageVO.current = Number(current);
		_params.pageVO.size = Number(size);
		recordsTotal = Number(total);
		// 如果是第一页
		if (_params.pageVO.current == 1) {
			isResetSelectStatusRef.value = true; // 重置选择状态为true
			liveList.value = handleData(records);
		} else {
			isResetSelectStatusRef.value = false; // 重置选择状态为false
			handleData(records).forEach((item) => {
				// 如果列表中不存在该项
				if (!liveList.value.find((temp) => temp.value == item.value)) {
					// 添加到列表中
					liveList.value.push(item);
				}
			});
		}
		// 如果有搜索值
		if (_params.data.name) {
			// 遍历直播列表
			liveList.value.forEach((item) => {
				// 如果临时结果列表中不存在该项
				if (!_resultTempList.find((temp) => temp.value == item.value)) {
					// 添加到临时结果列表中
					_resultTempList.push(item);
				}
			});
		} else {
			// 遍历临时结果列表
			_resultTempList.forEach((item) => {
				// 如果直播列表中不存在该项
				if (!liveList.value.find((temp) => temp.value == item.value)) {
					// 添加到直播列表中
					liveList.value.push(item);
				}
			});
		}
	} catch (error) {
		message.createMessageError("获取直播列表失败：" + error);
	} finally {
		isLoading.value = false;
	}
};
</script>


<style lang="less" scoped>

</style>