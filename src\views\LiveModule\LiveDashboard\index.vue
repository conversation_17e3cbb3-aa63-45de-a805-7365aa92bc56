<template>
    <n-spin :show="isLoadingRef" size="small" style="width: 100%;height: 100%;">
        <div class="live-dashboard-wrapper">
            <!-- 左侧商品成交榜 -->
            <div class="left-sidebar basics">
                <div class="sidebar-item-top">
                    <div class="item-content">
                        <p>近5分钟进直播人数</p>
                        <span>{{ fiveMinuteDataRef.onlineNumber || 0}}</span>
                    </div>
                    <div class="item-content">
                        <p>近5分钟成交金额</p>
                        <span>{{ fiveMinuteDataRef.totalMoney || 0}}</span>
                    </div>
                    <div class="line"></div>
                </div>
                <div class="sidebar-item-bottom">
                    <h3>商品成交榜</h3>
                    <div class="goods-list">
                        <Goods></Goods>
                    </div>
                </div>
            </div>
            <!-- 中间主要内容区域 -->
            <div class="center-content">
                <div class="top-header">
                    <span :class="['state',stateClassMap[liveRoomDataRef.status]]">{{ liveRoomDataRef.status == LiveStateTypeEnum.START?"未开始":liveRoomDataRef.status == LiveStateTypeEnum.PROCEED?"直播中":"已结束" }}</span>
                    <div class="time">
                        <div class="hint">
                            <span class="start">开播时间：{{ liveRoomDataRef.liveActualStartTime || "-"  }}</span>
                            <span>直播时长：{{ liveRoomDataRef.duration || "-"  }}分钟</span>
                        </div>
                        <span>{{ liveRoomDataRef.name }}</span>
                    </div>
                </div>
                <!-- 整体综合数据 -->
                <div class="live-gmv basics">
                    <LiveGmv></LiveGmv>
                </div>
                <!-- 整体综合趋势图表 -->
                <div class="chart-area basics">
                    <ChartArea></ChartArea>
                </div>
            </div>
    
            <!-- 右侧边栏 -->
            <div class="right-sidebar">
                <div class="right-top basics">
                    <div class="view-duration-stats">
                        <h3>人均观看时长</h3>
                        <WatchTime></WatchTime>
                    </div>
                    <div class="view-duration-distribution">
                        <h3>观看时长分布</h3>
                        <WatchPlan></WatchPlan>
                    </div>
                </div>
                <div class="right-bottom basics">
                    <div class="live-video ">
                        <!-- 直播画面 -->
                        <iframe  width="100%" height="100%" :src="pcViewUrlRef"></iframe>
                    </div>
                </div>
            </div>
        </div>
    </n-spin>
</template>

<script setup lang="ts">
import useLiveDashboard from "./hook/useLiveDashboard";
import Goods from './components/Goods.vue';
import LiveGmv from './components/LiveGmv.vue';
import WatchPlan from './components/WatchPlan.vue';
import WatchTime from './components/WatchTime.vue';
import ChartArea from './components/ChartArea.vue';
import { LiveStateTypeEnum } from "./type"
import { onMounted } from "vue";
import { useRoute } from 'vue-router'

const route = useRoute()
const liveId = route.query.id // 直播间id
const { liveRoomDataRef,isLoadingRef,pcViewUrlRef,fiveMinuteDataRef,
    getLiveActivity,
 } = useLiveDashboard()

const stateClassMap = {
    [LiveStateTypeEnum.START]:'begin',
    [LiveStateTypeEnum.PROCEED]:'proceed',
    [LiveStateTypeEnum.END]:'end',
}
onMounted(()=>{
    getLiveActivity(liveId as string)
})
</script>

<style lang="less" scoped>
.live-dashboard-wrapper {
    display: flex;
    height: 100vh;
    width: 100vw;
    min-width: 1024px;
    min-height: 600px;
    padding: 16px;
    box-sizing: border-box;
    background-color: #0a1752;
    color: #fff;
    .basics{
        background-color: #1a2861;
        padding: 16px;
        border-radius: 8px;
    }
    .left-sidebar {
        width: 20%;
        margin-right: 16px;
        flex-shrink: 0;
        .sidebar-item-top{
            height: 14%;
            display: flex;
            justify-content: space-between;
            position: relative;
            .item-content{
                display: flex;
                flex-direction: column; 
                font-size: 14px;
                color: #99a0c7;
                span{
                    line-height: 30px;
                    font-size: 18px;
                    color: #fff;
                }
            }
            .line{
                position: absolute;
                height: 10px;
                width: calc(100% + 28px);
                border-radius: 5px;
                background-color: #0a1752;
                bottom: 10px;
                left: -14px;
            }
        }
        .sidebar-item-bottom{
            height: 86%;
            display: flex;
            flex-direction: column;
            .goods-list{
                margin-top: 16px;
                flex: 1;
                overflow-y: auto
            }
        }
    }
    .center-content {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        gap: 16px;
        .top-header {
            display: flex;
            align-items: center;
            .state{  
                font-size: 12px;
                height: 26px;
                line-height: 24px;
                padding: 0 10px;
                border-radius: 4px;
                &.begin{
                    background: #3498db;
                    color: #fff;
                }
                &.proceed{
                    background: #d9001b;
                    color: #fff;
                }
                &.end{
                    background: #bdc3c7;
                    color: #2c3e50;
                }
            }
            .time{
                margin-left: 10px;
                flex: 1;
                display: flex;
                justify-content: space-between;
                flex-wrap: wrap;
                .start{
                    margin-right: 10px;
                }
            }
        }
        .live-gmv {
            // background: ;
            background: linear-gradient(to bottom, #3498db, #1b78f4);
        }
        .chart-area {
            flex-grow: 1;
            padding:0px;
        }
    }

    .right-sidebar {
        width: 22%;
        margin-left: 16px;
        display: flex;
        flex-direction: column;
        gap: 16px;
        flex-shrink: 0;

        .right-top {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .right-bottom {
            flex-grow: 1;
            .live-video {
                width: 100%;
                height: 100%;
                background-color: #000;
                border-radius: 8px;
                overflow: hidden;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }
    }
    /* Webkit浏览器中的滚动条样式 */
    ::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    /* 滚动条轨道（背景） */
    ::-webkit-scrollbar-track {
        background: #162556;
        border-radius: 10px;
    }

    /* 滚动条滑块 */
    ::-webkit-scrollbar-thumb {
        background: #333d5e;
        border-radius: 10px;
    }

    /* 滑块悬停时的样式 */
    ::-webkit-scrollbar-thumb:hover {
        background: #ff6600;
    }
}

// /* 添加媒体查询，在小屏幕上调整布局 */
// @media screen and (max-width: 1200px) {
//     .live-dashboard-wrapper {
//         .left-sidebar {
//             width: 25%;
//         }
//         .right-sidebar {
//             width: 30%;
//         }
//     }
// }

// @media screen and (max-width: 1000px) {
//     .live-dashboard-wrapper {
//         .left-sidebar {
//             width: 30%;
//         }
//         .right-sidebar {
//             width: 35%;
//         }
//     }
// }
</style>