import { StoreBasicConfigAuth, StoreOperationFlowConfigAuth } from "@/enums/authKeys";
import { hasAuth } from "@/utils/auth/authUtils";


/** 角色权限 */
export const hasAuthRolePageAuth = function(){
    return hasAuth(StoreBasicConfigAuth.storeBasicConfigRoles.key);
}()
/** 操作员管理权限 */
export const hasOperatorsPageAuth = function(){
    return hasAuth(StoreBasicConfigAuth.storeBasicConfigOperators.key);
}()

/** 业务流程页面 */
export const hasOperationFlowPageAuth = function(){
  return hasAuth(StoreOperationFlowConfigAuth.storeConfigOperationFlowPage.key);
}()


