<template>
  <n-dropdown :options="options" @select="handleDropdown" v-if="userStore.imConfig?.userID">
    <div class="userInfo-wrapper">
      <div class="userInfo-trigger">
        <span class="username">{{ onlineStatus }}</span>
        <n-text type="info" style="font-size: 14px;">【切换】</n-text>
      </div>
    </div>
  </n-dropdown>
</template>

<script lang="ts" setup name='UserAvatar'>
import {onMounted, ref, h, type Component, type ComputedRef, computed} from "vue";
import {NIcon, type DropdownOption} from "naive-ui";
import {useUserStore} from "@/stores/modules/user";
import {transformMinioSrc} from "@/utils";
import {afterLogout} from "@/utils/accountUtils";
import avatarSrc from "@/assets/image/system/avatar.png";
import {KeyOutline as KeyOutIcon, LogOutOutline as LogoutIcon, CloudDoneOutline, CloudOutline} from "@vicons/ionicons5";
import UserPwdEditModal from '@/components/UserPwdEditModal/index.vue';
import {useSystemStoreWithoutSetup} from "@/stores/modules/system"
import {FirstLogin, EasyPasswords} from "@/enums"
import {doctorOnlineStatusChange} from "@/views/DoctorEndModule/IM/utils/IMUtils";
import {useMessages} from "@/hooks/useMessage";


const systemStore = useSystemStoreWithoutSetup()

/* 点击key enums */
type DropdownKey = "online" | "offline";

/** 用户 store */
const userStore = useUserStore();
const onlineStatus = ref('在线')
const message = useMessages()

/** 渲染图标Icon */
const renderIcon = (icon: Component) => {
  return () => {
    return h(NIcon, null, {
      default: () => h(icon)
    })
  }
};

/* 下拉菜单选项 */
const options: ComputedRef<Array<DropdownOption>> = computed(() => {
  return [
    {
      label: "上线",
      key: "online",
      disabled: onlineStatus.value == '在线',
      icon: renderIcon(CloudDoneOutline)
    },
    {
      type: "divider",
      key: "divider",
    },
    {
      label: "下线",
      key: "offline",
      disabled: onlineStatus.value == '已下线',
      icon: renderIcon(CloudOutline)
    },  
  ];
})

const pwdModalClosable = ref(false);
const pwdModalShow = ref(false);

/* 处理点击选项回调 */
const handleDropdown = (optionKey: string) => {
  const key = optionKey as DropdownKey;
  // 退出
  if (key === "online") {
    if (onlineStatus.value == '在线') return
    doctorOnlineStatusChange(1).then((res) => {
      message.createMessageSuccess('切换上线状态成功')
      onlineStatus.value = '在线'
    })

  }
  if (key == 'offline') {
    if (onlineStatus.value == '已在线') return
    doctorOnlineStatusChange(0).then(res => {
      message.createMessageSuccess('切换下线状态成功')
      onlineStatus.value = '已下线'
    })
  }

};


</script>

<style lang="less" scoped>
.userInfo-wrapper {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-left: 12px;

  &:hover {
    background-color: #f6f6f6;
  }

  .userInfo-trigger {
    display: flex;
    align-items: center;
    cursor: pointer;

    .username {
      font-size: 14px;
      color: #000000;
      padding-right: 10px;
    }
  }
}


</style>
