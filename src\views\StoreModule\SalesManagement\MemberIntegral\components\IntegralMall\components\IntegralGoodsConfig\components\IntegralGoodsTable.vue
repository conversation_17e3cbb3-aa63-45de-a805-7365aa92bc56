<template>
  <FormLayout
    class="inner-page-height"
    :isLoading="isLoading"
    :tableData="tableData"
    :tableColumns="tableColumns"
    :pagination="paginationRef"
    @paginationChange="paginationChange"
    :isNeedCollapse="false"
    table-row-key="productId"
  >
    <!-- 表单 -->
    <template #searchForm>
      <n-form
        ref="formRef"
        label-placement="left"
        label-width="auto"
        :show-feedback="false"
        require-mark-placement="right-hanging"
        size="small"
        :style="{ width: '100%' }"
      >
        <!-- 上架状态 -->
        <n-form-item :span="12" label="上架状态">
          <n-select
            style="width: 160px;"
            v-model:value="model.isPointPublish"
            :options="shelfStatusOptions"
            clearable
          />
        </n-form-item>
        <!-- 商品名称 -->
        <n-form-item :span="12" label="商品名称">
          <JSearchInput v-model:value="model.frontName" placeholder="请输入商品名称" @search="handlerSearch" />
        </n-form-item>
        <!-- 商品分类 -->
        <n-form-item :span="12" label="商品分类">
          <n-select style="width: 160px;" v-model:value="model.isCate" :options="goodsCategoryOptions" clearable />
        </n-form-item>
      </n-form>
    </template>
    <template #tableHeaderBtn>
      <n-button @click="refresh" class="store-button">刷 新</n-button>
      <!-- 新增 -->
      <JAddButton type="primary" @click="openGoodsModal">新 增</JAddButton>
    </template>
    <!-- 表格底部按钮 -->
    <template #tableFooterBtn="scope">
      <!-- 批量删除 -->
      <n-popconfirm
        @positive-click="handleDelete(scope.selectedListIds)"
        :positive-button-props="{
    loading: isDeleteLoading
  }"
      >
        <template #trigger>
          <n-button ghost type="error" size="small">批量删除</n-button>
        </template>
        此操作将删除选中的商品，是否继续？
      </n-popconfirm>
      <!-- 批量上架 -->
      <n-popconfirm
        @positive-click="handleBatchUnmountGoods(scope.selectedListIds, scope.selectedList, 1)"
        :positive-button-props="{
    loading: isBatchLoading
  }"
      >
        <template #trigger>
          <n-button ghost type="primary" size="small">批量上架</n-button>
        </template>
        此操作将上架选中的商品，是否继续？
      </n-popconfirm>
      <!-- 批量下架 -->
      <n-popconfirm
        @positive-click="handleBatchUnmountGoods(scope.selectedListIds, scope.selectedList, 0)"
        :positive-button-props="{
    loading: isBatchLoading
  }"
      >
        <template #trigger>
          <n-button ghost type="error" size="small">批量下架</n-button>
        </template>
        此操作将下架选中的商品，是否继续？
      </n-popconfirm>
    </template>
  </FormLayout>
  <!-- 新增积分商品 -->
  <GoodsModal
    ref="goodsModalRef"
    :type="GoodsCategoryType.GENERAL"
    @update:selected-options="handleSelectOptions"
  />
  <!-- 编辑积分商品 -->
  <IntegralGoodsEdit 
    ref="integralGoodsEditRef" 
    :refresh="refresh"
    @after-success="handleAfterSuccess"
  />
</template>

<script lang="tsx" setup name="IntegralGoodsTable">
import { ref, watch, nextTick } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import {
  getPointSpecPage,
  batchUnmountProduct,
  deletePointGoodsById
} from "@/services/api";
import TablePreview from "@/components/TablePreview/index.vue";
import { useLoading, useMessages } from '@/hooks';
import { shelfStatusOptions, shelfStatusLabels, goodsCategoryOptions } from "@/constants";
import type { GoodsType } from "@/enums";
import { GoodsCategoryType } from "@/enums";
import { isArray, isNullOrUnDef } from "@/utils";
/** 相关组件 */
import GoodsModal from "@/views/business/GoodsModal/index.vue";
import IntegralGoodsEdit from "./IntegralGoodsEdit.vue";

const { createMessageSuccess, createMessageError } = useMessages();

/** props */
const props = defineProps<{
  type: GoodsType | null,
  cateId: string | null;
  minPoints: number | null; // 最低积分
  maxPoints: number | null; // 最高积分
}>();

/** emits */
const emits = defineEmits<{
  (e: "successful"): void;
}>();

/** 表格hook */
const {
  isAddLoading,
  isEditLoading,
  isLoading,
  tableData,
  paginationRef,
  pageTableData,
  deleteTableData,
  editTableData,
  addTableData,
  refreshTableData,
  paginationChange,
} = useTableDefault({
  pageDataRequest: getPointSpecPage,
});

/* 表格列表项 */
const tableColumns = [
  {
    title: "图片",
    key: "img",
    align: "left",
    fixed: "left",
    width: 120,
    render: (row) => {
      if (row?.productImgDTOList?.length > 0) {
        let paths = row?.productImgDTOList.map(item => item.path);
        return <TablePreview src={paths}></TablePreview>;
      }
      return '-';
    },
  },
  {
    title: "商品",
    key: "frontName",
    width: 200,
    align: "left",
  },
  {
    title: "兑换最低条件",
    key: "pointSpecs",
    width: 200,
    align: "left",
    render: (row) => {
      const { min } = findMinMaxExchangePoints(row?.pointSpecs);
      return (
        <span>
          {min.exchangePoints !== 0 && `${min['exchangePoints']}积分`}
          {min.exchangePoints !== 0 && min.exchangePrice !== 0 && ' + '}
          {min.exchangePrice !== 0 && formatPrice(min['exchangePrice'])}
        </span>
      );
    }
  },
  {
    title: "兑换最高条件",
    key: "pointSpecs",
    width: 200,
    align: "left",
    render: (row) => {
      const { max } = findMinMaxExchangePoints(row?.pointSpecs);
      if (!max) {
        return '-';
      }
      return (
        <span>
          {max.exchangePoints !== 0 && `${max['exchangePoints']}积分`}
          {max.exchangePoints !== 0 && max.exchangePrice !== 0 && ' + '}
          {max.exchangePrice !== 0 && formatPrice(max['exchangePrice'])}
        </span>
      );
    }
  },
  {
    title:"已售",
    key: "soldQty",
    width: 150,
    align: "left",
    render: row => {
      const allSoldQty = row?.pointSpecs.reduce((total, item) => total + item.soldQty, 0);
      return <span>{allSoldQty}</span>;
    }
  },
  {
    title: "库存",
    key: "availStocks",
    width: 80,
    align: "left",
    render: (row) => {
      // 计算 availStocks 的总值
      const totalAvailStocks = row?.pointSpecs.reduce((total, item) => total + item?.availStocks, 0);
      return <span>{totalAvailStocks}</span>;
    }
  },
  {
    title: "上架",
    key: "isPointPublish",
    width: 80,
    align: "left",
    render: (row) => {
      return (
        <n-tag
          bordered={false}
          size="small"
          type={row?.isPointPublish === 1 ? "success" : "error"}
        >
          {shelfStatusLabels[row?.isPointPublish]}
        </n-tag>
      );
    },
  },
  {
    title: "是否包邮",
    key: "cateName",
    width: 80,
    align: "left",
    render: (row) => {
      return (
        <n-tag
          bordered={false}
          size="small"
          type="success"
        >
        是
        </n-tag>
      );
    },
  },
  {
    title: "操作",
    key: "action",
    width: 120,
    fixed: "right",
    align: "left",
    render: row => {
      return (
        <n-space>
          {/* 编辑 */}
          <n-button text type="primary" onClick={() => openIntegralGoodsEdit('edit', null, row?.productId)}>
            编辑
          </n-button>
          {/* 删除 */}
          <n-popconfirm 
            onPositiveClick={() => handleDelete(row?.productId)}
            positive-button-props={{
              loading: isDeleteLoading.value,
            }}
          >
	    	  	{{
	    	  		default: () => {
	    	  			return (
	    	  				<span>{`确认删除《${row?.frontName}》积分商品吗？`}</span>
	    	  			);
	    	  		},
	    	  		trigger: () => {
	    	  			return (
	    	  				<n-button text type="error">
	    	  					删除
	    	  				</n-button>
	    	  			);
	    	  		},
	    	  	}}
	    	  </n-popconfirm>
        </n-space>
      );
    },
  },
];

/** 找到最小积分或最大积分 */
function findMinMaxExchangePoints(pointSpecs: Array<any>) {
  if (pointSpecs.length === 1) {
    return { min: pointSpecs[0], max: null };
  }

  let minItem = pointSpecs[0];
  let maxItem = pointSpecs[0];

  for (let i = 1; i < pointSpecs.length; i++) {
    if (pointSpecs[i].exchangePoints < minItem.exchangePoints) {
      minItem = pointSpecs[i];
    }
    if (pointSpecs[i].exchangePoints > maxItem.exchangePoints) {
      maxItem = pointSpecs[i];
    }
  }

  return { min: minItem, max: maxItem };
}

/** 定义一个函数来格式化价格 */
const formatPrice = (price) => `￥${(price / 100).toFixed(2)}`;

/** 保存成功回调 */
function handleAfterSuccess() {
  goodsModalRef.value && goodsModalRef.value?.tableSearch();
}

/** 添加、批量添加回调 */
function handleSelectOptions(goodsList: ApiStoreModule.Goods[]) {
  openIntegralGoodsEdit('add', goodsList[0]);
}

/** 打开新增积分商品 */
const goodsModalRef = ref<InstanceType<typeof GoodsModal> | null>(null);
const openGoodsModal = () => {
  goodsModalRef.value?.acceptParams();
};

/** 打开设置积分商品 */
const integralGoodsEditRef = ref<InstanceType<typeof IntegralGoodsEdit> | null>(null);
const openIntegralGoodsEdit = (type: 'add' | 'edit', row: Partial<ApiStoreModule.Goods> = {}, id?: string) => {
  integralGoodsEditRef.value?.acceptParams({
    type,
    row,
    id
  });
};

/** 参数 */
const model = ref({
  frontName: '',
  isPointPublish: null,
  isCate: null,
});

/** 获取参数 */
const getParams = () => {
  const { frontName, isPointPublish, isCate } = model.value;
  return {
    frontName,
    isPointPublish,
    isCate: isNullOrUnDef(isCate) ? null : isCate ? true : false,
    type: props.type,
    cateId: props.cateId ?? null,
    minPoints: props.minPoints,
    maxPoints: props.maxPoints,
  };
};

/** 批量上下架 */
const isBatchLoading = ref(false);
const handleBatchUnmountGoods = async (rowListIds: Array<string>, rowList: Array<any>, isPublish: 0 | 1) => {
  try {
    isBatchLoading.value = true;
    let cateListIds = rowList.map(item => item?.pointCateId).reduce((acc, current) => {
      if (!acc.includes(current)) {
        acc.push(current);
      }
      return acc;
    }, []);
    let _params = {
      productIds: rowList.map(item => item?.productId).join(',') || '',
      isPublish,
      cateIds: cateListIds.join(','),
    };
    await batchUnmountProduct(_params);
    createMessageSuccess('操作成功');
    refresh();
  } catch (error) {
    createMessageError('操作成功失败：' + error);
  } finally {
    isBatchLoading.value = false;
  }
};

/** 删除积分商品 */
const { loading: isDeleteLoading, startLoading, endLoading } = useLoading();
async function handleDelete(rowListIds: Array<string> | string) {
  try {
    startLoading();
    let _params = {
      pointProductIds: isArray(rowListIds) ? rowListIds.join(',') : rowListIds,
    };
    await deletePointGoodsById(_params);
    createMessageSuccess('删除积分商品成功！');
    refresh();
  } catch (error) {
    createMessageError('删除积分商品失败：' + error);
  } finally {
    endLoading();
  }
}

/** 搜索 */
const handlerSearch = () => {
  tableSearch();
};

/** 表格刷新 */
function refresh() {
  tableSearch();
};

/* 表格搜索 */
const tableSearch = async () => {
  try {
    await pageTableData(getParams(), paginationRef.value);
  } catch (error) {
    console.log("积分商品查询失败" + error);
  } finally {
    emits("successful");
  }
};

/** 监听 */
watch(() => [
  props.cateId,
  props.minPoints,
  props.maxPoints
], (newVal, oldVal) => {
  // 关闭抽屉
  nextTick(() => {
    tableSearch();
  })
}, { immediate: true });

/** 监听 */
watch(() => [
  model.value.isPointPublish,
  model.value.isCate
], (newVal) => {
  if (newVal) {
    tableSearch();
  }
});
</script>

<style lang="less" scoped></style>
