<template>
	<JModal
		v-model:show="show"
		:title="title"
		width="680"
		@after-leave="closeModal"
		@positive-click="handleSave"
		:positiveButtonProps="{
			loading: isLoading
		}"
	>
		<n-form
			ref="formRef"
			:model="model"
			:rules="rules"
			label-width="auto"
			label-placement="left"
			require-mark-placement="right-hanging"
			size="small"
			:style="{
				width: '100%',
			}"
		>
			<n-grid :cols="12" :x-gap="12" responsive="self">
				<!-- 分类名称 -->
				<n-form-item-gi :span="12" label="商品分类名称" path="name">
					<n-input
						v-model:value="model.name"
						@blur="model.name=($event.target as HTMLInputElement)?.value.trim()"
						placeholder="请输入商品分类名称"
						:maxlength="50"
					/>
				</n-form-item-gi>
                <!-- 归属商品类型 -->
                <n-form-item-gi
				    v-if="props.storeType === SystemStoreType.PHARMACEUTICALMALL && modalProps.type !== 'addChild' && modalProps.type !== 'editChild'"
				    :span="12"
				    label="归属商品类型"
				    path="type"
				    :show-feedback="false"
				>
                    <div style="display: flex;flex-direction: column;margin-bottom: 8px;">
                        <n-radio-group v-model:value="model.type" name="radiogroup" :disabled="modalProps.type == 'edit'">
                          <n-space>
                            <n-radio 
							    v-for="item in merchandiseTypeOptions" 
							    :key="item.value" 
							    :value="Number(item.value)"
							>
                                {{ item.label }}
                            </n-radio>
                          </n-space>
                        </n-radio-group>
                        <span>注：选定后不能更改</span>
                    </div>
				</n-form-item-gi>
                <!-- 排序号 -->
                <n-form-item-gi :span="12" label="排序号">
					<n-input-number
						v-model:value="model.sort"
						placeholder="序号越大排位越前,最多3位数"
						:max="999"
                        :min="0"
                        clearable
                        :show-button="false"
                        style="width: 100%;"
						:precision="0"
					/>
				</n-form-item-gi>
                <!-- 图标 -->
                <n-form-item-gi :span="12" label="图标" path="iconPath">
                    <UploadProductImg 
                       v-model:value="model.iconPath" 
                       :maxFileSize="0.5"
                       accept="image/*" 
                       :fileListSize="1" 
                       :max="1"
                    />
                </n-form-item-gi>
				<!-- 提示 -->
				<n-gi :span="12" style="margin-left: 92px;margin-bottom: 12px;">
					<span>注：图片需小于500K，支持png、jpg、JPEG、GIF格式，建议尺寸：20*20px</span>
				</n-gi>
                <!-- 在商品分类列表页展示此分类 -->
                <n-form-item-gi :span="12" :show-label="false" :show-feedback="false">
                    <div style="margin-left: 92px;">
                        <JCheckbox v-model:checked="model.isShowList">
                            在商品分类列表页展示此分类
                        </JCheckbox >
                    </div>
                </n-form-item-gi>
                <!-- 是否首页展示该分类 -->
                <n-form-item-gi :span="12" :show-label="false" :show-feedback="false">
                    <div style="margin-left: 92px;">
                        <JCheckbox v-model:checked="model.isShow" @update:checked="handlerUpdateCkecked">
                            在商城首页首屏展示此分类
                        </JCheckbox >
                    </div>
                </n-form-item-gi>
			</n-grid>
		</n-form>
	</JModal>
</template>

<script setup lang="ts" name="addBootcamp">
import { ref, computed } from "vue";
import type { FormRules } from "naive-ui";
import { useMessages, useBoolean } from '@/hooks';
import type { TreeOption } from 'naive-ui';
import { addGoodsClassification, goodsClassificationUpdate } from "@/services/api";
import { isArray, isObject, deepClone } from "@/utils";
import { merchandiseTypeOptions } from "@/constants";
import type { GoodsType, StoreModelType } from "@/enums";
import { GoodsCategoryType, SystemStoreType } from "@/enums";

const { createMessageSuccess, createMessageError, createMessageWarning } = useMessages();

export type ModalType = 'add' | 'edit' | 'addChild' | 'editChild';;

interface ModalProps {
	type: ModalType;
	row?: TreeOption & { isMenu?: boolean } & Partial<ApiStoreModule.GoodsClassification>; 
};

/** props */
const props = defineProps<{
	storeType?: StoreModelType; // 商城类型
}>();

/** emits */
const emits = defineEmits<{
  (e: "afterSuccess", value: string, type: GoodsType ): void;
}>();

/** 标题 */
const title = computed(() => {
  const titleMap: Record<ModalType, string> = {
    add: '新建商品分类',
    edit: '编辑商品分类',
	addChild: "新建子分类",
	editChild: "编辑子分类",
  };
  return titleMap[modalProps.value.type];
});

const { bool: show, setFalse, setTrue } = useBoolean(false);
const modalProps = ref<ModalProps>({
	type: 'add',
});

/* 接收父组件传过来的参数 */
const acceptParams = (params: ModalProps) => {
	modalProps.value = params;
	let row = params.row;
	// 处理行数据
	if (isObject(row) && Object.keys(row).length !== 0) {
		Object.assign(model.value, {
            id: row.id ?? null,
            name: row.name ?? '',
            type: row.type ?? 1,
            sort: row.sort ?? null,
            iconPath: row.iconPath ? [{ path: row.iconPath }] : [],
            isShow: row.isShow === 1,
            isShowList: row.isShowList === 1,
            parentId: row.parentId ?? undefined,
        });
	}
	setTrue();
};

/* 表单参数初始化 */
const initParams = {
	id: null,
	name: "",
    type: GoodsCategoryType.DRUG,
    sort: null,
    iconPath: [],
    isShow: false,
	isShowList: false, // 是否在列表页展示
	parentId: undefined,
};

const model = ref(deepClone(initParams));

/* 表单实例 */
const formRef = ref();

/* 表单规则 */
const rules: FormRules = {
	name: {
      required: true,
      trigger: ['blur', 'change'],
      message: '请输入商品分类名称',
    },
	type: {
      type: 'number',
      required: true,
      trigger: ['change'],
      message: '请选择归属商品类型',
    },
	iconPath: {
      type: 'array', 
      required: true,
      trigger: ['blur', 'change'],
      message: '请选择图标',
    },
};

/** 勾选回调 */
function handlerUpdateCkecked(value: boolean) {
	if(value) model.value.isShowList = value;
}

/* 清空表单 */
const formDataReset = () => {
	model.value = deepClone(initParams);
};

/* 关闭弹窗之后 */
const closeModal = () => {
	formDataReset();
};

/** 获取参数 */
const _getParams = (): {
    name: string;
    type: GoodsType;
    iconPath: string;
    sort: number;
    isShow: 0 | 1;
	isShowList: 0 | 1;
	parentId?: number;
} => {
	const { name, type, iconPath, sort, isShow, isShowList, parentId } = model.value;
	return {
		name, 
		type: props.storeType === SystemStoreType.PHARMACEUTICALMALL ? type : GoodsCategoryType.GENERAL, 
		iconPath: isArray(iconPath) ? iconPath[0]?.path : '',
		sort, 
		isShow: isShow ? 1 : 0 ,
		isShowList: isShowList ? 1 : 0 ,
		parentId,
	}
};

/**
 * @description 表单校验函数
 */
 function validateForm(formData) {
    let errors = "";
    // 校验必填字段
    if (formData?.isShow && !formData?.isShowList) {
        errors = "请同时勾选'在商品分类列表页展示此分类'!";
    }
    return {
        isValid: Object.keys(errors).length === 0,
        errors
    };
}

/* 确认--保存 */
const isLoading = ref(false);
const handleSave = (e: MouseEvent) => {
	e.preventDefault();
	formRef.value?.validate(async (errors: any) => {
		if (!errors) {
			try {
				isLoading.value = true;
				let _params = _getParams();
				// 表单校验
				const { isValid, errors } = validateForm(_params);
                if (!isValid) {
                    // console.log("表单校验失败:", errors);
					createMessageWarning(errors);
					return;
                }

				// 新增
				if (['add', 'addChild'].includes(modalProps.value.type)) {
					const data = await addGoodsClassification(_params);
					createMessageSuccess('新建分类成功');
					// 更新的商品分类Id
				    emits('afterSuccess', data?.id, model.value.type);
				} 
				else {
					await goodsClassificationUpdate({
						id: model.value.id,
						..._params
					});
					// 更新的商品分类Id
				    emits('afterSuccess', model.value.id, model.value.type);
				}

				// 刷新
				setFalse();
			} catch (error) {
				createMessageError(modalProps.value.type === 'add' ? `新建分类失败: ${error}` : `修改分类失败: ${error}`);
			} finally {
				isLoading.value = false;
			}
		}
	});
};

defineExpose({
	acceptParams,
});
</script>

<style scoped lang="less"></style>
