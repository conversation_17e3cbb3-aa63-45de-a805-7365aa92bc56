import type { GlobalThemeOverrides } from "naive-ui";
export const themeOverrides: GlobalThemeOverrides = {
  common: {
    primaryColor: "#1677FF",
    primaryColorHover: "#4592FF",
    primaryColorPressed: "#146BE6",

    successColor: "#00B42A",
    successColorHover: "#33C355",
    successColorPressed: "#00A226",

    infoColor: "#165DFF",
    infoColorHover: "#457DFF",
    infoColorPressed: "#1454E6",

    errorColor: "#FF4D4F",
    errorColorHover: "#FF7172",
    errorColorPressed: "#E64547",

    textColor1: "#333333",
    textColor2: "#666666",
    textColor3: "#999999",
    textColorDisabled: "#ADADAD",

    dividerColor: "#EEEEEE",
    borderColor: "#EEEEEE",
    scrollbarWidth:'8px',
    scrollbarHeight:'8px'
  },
  Form: {
    labelTextColor: "#666666",
    labelPaddingHorizontal:"0"
  },
  Pagination: {
    // itemColorActiveHover:'#FFB384',
    // itemBorderHover:'1px solid #1677FF',
    itemColorActive: "#E7F1FF",
    itemBorderActive: "1px solid transparent",
  },
  Tag: {
    colorChecked: "#e7f1ff",
    textColorChecked: "#1677FF",
    colorCheckedHover: "#fff",
  },
  Menu: {
    itemTextColorChildActive: "#1677FF",
    itemColorHover: "#E7F1FF",
    // itemTextColorHover:'#333333',
    // itemTextColorChildActiveHover:'#333333',
    // arrowColorChildActiveHover:'#333333',
    itemTextColorHover: "#333333",
    itemTextColorChildActiveHover: "#1677FF",
    arrowColorChildActiveHover: "#1677FF",
    borderColorHorizontal: "#1677FF",
    itemHeight: "40px",
  },
  DatePicker: {
    itemBorderRadius: "50%",
    itemCellWidth: "46px",
    itemCellHeight: "40px",
  },
  DataTable: {
    thIconColor: "#333333",
    thPaddingSmall:'5px 5px',
    tdPaddingSmall:'5px 5px',
    tdTextColor:'#666666',

    // tdPaddingSmall:'8px 16px',
  },
  Tabs:{
    tabPaddingSmallLine:"8px 0",
    tabGapSmallLine:"32px",
    tabTextColorLine:"#666666",
    panePaddingMedium:'0',
  },
  Checkbox:{
    sizeMedium:'18px',
  },
  Input:{
    heightLarge:"48px"
  },
  Button:{
    heightLarge:"48px",
    borderRadiusSmall: "5px",
    borderRadiusMedium: "5px",
    paddingSmall: "4px 11px",
    paddingMedium: "4px 11px",
  },
  Space:{
    gapMedium:"0px 10px"
  }
};
