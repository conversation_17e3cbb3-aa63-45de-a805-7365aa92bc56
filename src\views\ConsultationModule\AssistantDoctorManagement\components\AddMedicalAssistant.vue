<template>
  <JModal
    v-model:show="isShow"
    width="680"
    :title="data.id ? '编辑医助' : '新增医助'"
    positiveText="确定"
    @after-leave="closeModal"
    @positive-click="_save"
    :positiveButtonProps="{
      loading: isLoading,
    }"
  >
    <n-form
      ref="formRef"
      :rules="rules"
      :model="model"
      label-width="auto"
      label-placement="left"
      require-mark-placement="right-hanging"
      :style="{
        width: '100%',
      }"
    >
      <n-grid :cols="8" :x-gap="24">
        <n-form-item-gi :span="24" label="姓名" path="name" required>
          <n-input v-model:value="model.name" :maxlength="15"></n-input>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="关联小程序端用户" path="customerId">
          <JSelectCustomer v-model:value="model.customerId" />
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="启动状态" path="status" required>
          <n-radio-group v-model:value="model.status" name="radiogroup">
            <n-space>
              <n-radio :value="1">启用</n-radio>
              <n-radio :value="0">停用</n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </JModal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { useMessages } from "@/hooks";
import JSelectCustomer from "@/components/JSelect/JSelectCustomer.vue";
import { editAssistantDoctor,addAssistantDoctor } from "@/services/api";
const { createMessageSuccess, createMessageError } = useMessages();
const props = withDefaults(
  defineProps<{
    show: boolean;
    data: any
  }>(),
  {
    show: false,
    data: () => ({
      name: null,
      customerId: null,
      status: null,
      id:null
    }),
  },
);
const emits = defineEmits<{
  (e: "update:show", value: boolean): void;
  (e: "refresh"): void;
}>();

const isShow = computed({
  get: () => {
    model.value.name = props.data.name
    model.value.status = props.data.status
    model.value.customerId = props.data?.customerId
    return props.show
  },
  set: (value: boolean) => {
    emits("update:show", value);
  },
});
const initialData = {
  name: null,
  customerId: null,
  status: null,
};
const model = ref({ ...initialData });
const showPatientInformation = ref(false);
/* 表单规则 */
const rules = {
  name: {
    type: "string",
    required: true,
    trigger: ["blur", "change"],
    message: "请输入姓名",
  },
  status: {
    required: true,
    trigger: ["blur", "change"],
    message: "请选择启用状态",
    validator: () => {
      return model.value.status !== null;
    },
  },
};

const isLoading = ref(false);

// 关闭按钮
const closeModal = () => {
  isShow.value = false;
  model.value = {
    ...initialData,
  };
};
const handlerCreatePatient = () => {
  showPatientInformation.value = true;
};
// 确认按钮
const formRef = ref(null);
const _save = () => {
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      isLoading.value = true;
      const _params = {
        data: {
          name: model.value.name,
          customerId: model.value.customerId,
          status: model.value.status,
          id: props.data.id
        },
      };
      try {
        props.data.id ? await editAssistantDoctor(_params) : await addAssistantDoctor(_params);
        createMessageSuccess("操作成功");
        emits("refresh");
        closeModal();
      } catch (error) {
        createMessageError(`${error}`);
      } finally {
        isLoading.value = false;
      }
    }
  });
};
</script>
