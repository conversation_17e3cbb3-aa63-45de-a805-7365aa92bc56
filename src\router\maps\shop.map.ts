import BlankLayout from "@/layout/BlankLayout.vue";
import { RoutesName } from "@/enums/routes";
import type { RouteLocation } from "vue-router";
export const Default = {
  [RoutesName.Shop]: {
    path: "shop",
    component: BlankLayout,
    meta: {
      title: "门店",
    },
  },
  [RoutesName.ShopManagement]: {
    path: "shopManagement",
    component: () => import("@/views/ShopModule/ShopManagement/index.vue"),
    meta: {
      title: "门店管理",
      icon: "store",
    }
  },
  [RoutesName.PersonnelManagement]: {
    path: "personnelManagement",
    component: () => import("@/views/ShopModule/PersonnelManagement/index.vue"),
    meta: {
      title: "店员店长",
      icon: "shop-manager",
    }
  },
  [RoutesName.ShopLogistics]: {
    path: "shopLogistics",
    component: () => import("@/views/ShopModule/ShopLogistics/index.vue"),
    meta: {
      title: "门店物流",
      icon: "logistics",
    },
  },
  [RoutesName.ShopReturnLogistics]: {
    path: "shopReturnLogistics",
    component: () => import("@/views/ShopModule/ShopReturnLogistics/index.vue"),
    meta: {
      title: "门店退货物流",
      icon: "return-logistics",
    },
  },
  [RoutesName.DealerManagement]: {
    path: "dealerManagement",
    component: () => import("@/views/ShopModule/DealerManagement/index.vue"),
    meta: {
      title: "经销商管理",
      icon: "distributor-management",
    },
  },
  [RoutesName.DealerGroup]: {
    path: "dealerGroup",
    component: () => import("@/views/ShopModule/DealerGroup/index.vue"),
    meta: {
      title: "经销商分组",
      icon: "organizational-structure",
    },
  },
};
