<script setup lang="ts">
import {
  TrendingDownOutline as DownIcon,
  TrendingUpOutline as UpIcon,
} from "@vicons/ionicons5";
import { toRefs, watch, ref } from "vue";

type Size = "small" | "medium" | "large";

const props = withDefaults(
  defineProps<{
    size?: Size | number;
    value: string | number;
    isDisplayIcon?: boolean;
    isDisplayPercent?: boolean;
    isUseNumberFont?: boolean;
    threeDigits?:boolean
  }>(),
  {
    size: "small",
    isDisplayIcon: true,
    isDisplayPercent: true,
    isUseNumberFont: false,
  }
);
const { value, size, isDisplayIcon, isDisplayPercent, isUseNumberFont } =
  toRefs(props);
const isUpTrend = ref(false);
watch(
  value,
  (newVal) => {
    isUpTrend.value = newVal > 0 ? true : false;
  },
  {
    immediate: true,
  }
);
</script>
<template>
  <span
    :class="[
      'rateSpan',
      { 'number-font': isUseNumberFont },
      { up: isUpTrend },
      { down: !isUpTrend },
    ]"
  >
    {{ 
    props.threeDigits == true ? Number(value).toFixed(3) : Number(value).toFixed(2)
    }}
    <template v-if="isDisplayPercent">%</template>
    <n-icon v-if="isDisplayIcon" :component="isUpTrend ? UpIcon : DownIcon"></n-icon>
  </span>
</template>
<style lang="less" scoped>
@import "@/styles/default.less";
@import "@/styles/defaultVar.less";
.rateSpan {
  font-weight: 400;
  &.up {
    color: @primary-color;
  }
  &.down {
    color: @success-color;
  }
}
</style>
