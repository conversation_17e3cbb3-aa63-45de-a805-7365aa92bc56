<template>
  <NFlex vertical style="gap: 23px;">
    <NCard :bordered="false" style="border-radius: 16px; height: 100%;">
      <NSpin :show="isLoading" size="small">
        <NSpace vertical :size="12">
          <NFlex justify="space-between" align="center">
            <NFlex vertical>
              <div class="header-title">数据统计</div>
              <div class="header-desc">只统计已支付或已退款成功订单</div>
            </NFlex>
            <NFlex>
              <div class="date-warpper">
                <TagSelectGroup :data="dataRangeList" v-model:value="modal.dateType" />
              </div>
              <JDateRangePicker :value="modal.rangeTime" @update:value="handleChangeDate" :width="320"
                format="YYYY-MM-DD HH:mm:ss"  :default-time="['00:00:00', '23:59:59']" :clearable="false" />
            </NFlex>
          </NFlex>
          <!-- 数据 -->
          <NGrid cols="s:1 m:2 l:4" responsive="screen" :x-gap="28" :y-gap="16">
            <NGi v-for="(item, key) in dataPanel" :key="key">
              <NCard :bordered="false"
                :style="{ height: '165px', backgroundImage: `url(${item.bgImg})`, backgroundSize: '100% 100%' }">
                <div class="data-content">
                  <span class="data-title">{{ item.title }}</span>
                  <div class="data-num" :style="{ color: item.numColor }">
                    <n-number-animation :from="0" :precision="numPnelKeys.includes(key)? 2:0" :to="item.num" />
                  </div>
                </div>
              </NCard>
            </NGi>
          </NGrid>
        </NSpace>
      </NSpin>
    </NCard>
    <NCard :bordered="false" style="border-radius: 16px; height: 100%;">
      <NSpin :show="isLoadingPanel" size="small">
        <NSpace vertical :size="12">
          <NGrid cols="m:2 l:2 xl:4" responsive="screen" :x-gap="2" :y-gap="2">
            <NGi v-for="(item, key) in chartPanel" :key="key">
              <ChartPanel :title="item.title" :data="item.data" :tipLeft="item.tipLeft" :tip="item.tip"
                style="height: 300px;" />
            </NGi>
          </NGrid>
        </NSpace>
      </NSpin>
    </NCard>
  </NFlex>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from "vue";
import dayjs from "dayjs";
import { isObject } from "@/utils/isUtils";
import { getEcommerceStats, getEcommerceTrendChart } from "@/services/api/homeApi/index";
import TagSelectGroup from "@/components/TagSelectGroup/index.vue";
import ChartPanel from "./components/ChartPanel.vue";
import cardBg from "@/assets/image/system/home/<USER>/cardBg.png";
import { isNullOrUnDef } from "@/utils/isUtils";
import cardBgYellow from "@/assets/image/system/home/<USER>/cardBgYellow.png";
import cardBgPink from "@/assets/image/system/home/<USER>/cardBgPink.png";
import cardBgPurple from "@/assets/image/system/home/<USER>/cardBgPurple.png";
import { DateRangeQuick, DateRangeQuickSelectEnum } from "@/utils/dateUtils";
import { useMessages } from "@/hooks/useMessage";
const message = useMessages();
const enum DateTypeEnum {
  /**全部 */
  ALL,
  /**今日 */
  TODAY,
  /**昨天 */
  YESTERDAY,
  /**本周 */
  WEEK,
  /**本月 */
  MONTH,
  /**自定义 */
  CUSTOM,
  /**近七天 */
  SEVEN_DAYS,
}

const dataRangeList = [
  {
    label: "今日",
    value: DateTypeEnum.TODAY,
  },
  {
    label: "近七天",
    value: DateTypeEnum.SEVEN_DAYS,
  },
  {
    label: "全部",
    value: DateTypeEnum.ALL,
  },
];
const isLoading = ref<boolean>(false);
const isLoadingPanel = ref<boolean>(false);
const initParams = {
  dateType: DateTypeEnum.SEVEN_DAYS,
  rangeTime: [DateRangeQuick[DateRangeQuickSelectEnum.last_seven_days].startTime,DateRangeQuick[DateRangeQuickSelectEnum.last_seven_days].endTime],
}
const modal = ref({ ...initParams })
const dataPanel = ref({
  //销售
  productQuantitySold: {
    title: '销量（商品件数）',
    num: 0,
    bgImg: cardBg,
    numColor: '#4DA4FF',
    isAuth: true,
  },
  //销售额
  salesAmount: {
    title: '销售额（元）',
    num: 0,
    bgImg: cardBgYellow,
    numColor: '#FF9636',
    isAuth: true,
  },
  //退款金额
  refundAmount: {
    title: '退款金额（元）',
    num: 0,
    bgImg: cardBgPink,
    numColor: '#FF6864',
    isAuth: true,
  },
  //会员数
  newCustomerCount: {
    title: '新增会员数（门店）',
    num: 0,
    bgImg: cardBgPurple,
    numColor: '#817FFA',
    isAuth: true,
  },
})
const chartPanel = ref({
  orderList: {
    title: '近七日销量',
    desc: '销售',
    tip: '销售',
    tipLeft: -15,
    data: {
      list: [],
      times: [],
    },
  },
  orderAmountList: {
    title: '近七日销售额',
    desc: '销售额',
    tip: '销售额',
    tipLeft: -21,
    data: {
      list: [],
      times: [],
    },
  },
  orderRefundList: {
    title: '近七日退款金额',
    desc: '退款金额',
    tip: '退款金额',
    tipLeft: -30,
    data: {
      list: [],
      times: [],
    },
  },
  customerList: {
    title: '近七日新增会员数（门店）',
    desc: '新增会员数',
    tip: '新增会员数',
    tipLeft: -35,
    data: {
      list: [],
      times: [],
    },
  },
})

const numPnelKeys = ['salesAmount','refundAmount']

const handleChangeDate = (val) => {
  modal.value.rangeTime = val
  modal.value.dateType = null
  handleSearch()
}
const getChartPanel = async () => {
  try {
    isLoadingPanel.value = true
    const res = await getEcommerceTrendChart()
    if (!res || !isObject(res)) {
      return
    }
    chartPanel.value.orderList.data = res.orderList.reduce((pre, item) => {
      pre.list.push(item.orderCount)
      pre.times.push(dayjs(item.day).format('MM-DD'))
      return pre
    }, { list: [], times: [] })
    chartPanel.value.orderAmountList.data = res.orderList.reduce((pre, item) => {
      pre.list.push(Number(item.saleMoney)/100)
      pre.times.push(dayjs(item.day).format('MM-DD'))
      return pre
    }, { list: [], times: [] })
    chartPanel.value.orderRefundList.data = res.refundList.reduce((pre, item) => {
      pre.list.push(Number(item.refundMoney)/100)
      pre.times.push(dayjs(item.day).format('MM-DD'))
      return pre
    }, { list: [], times: [] })
    chartPanel.value.customerList.data = res.customerList.reduce((pre, item) => {
      pre.list.push(item.newCustomerCount)
      pre.times.push(dayjs(item.day).format('MM-DD'))
      return pre
    }, { list: [], times: [] })
  } catch (error) {
    message.createMessageError(`获取失败：${error}`)
  } finally {
    isLoadingPanel.value = false
  }
}

const getChartStats = async () => {
  try {
    isLoading.value = true
    const params: any = {
      data: {
        dateType: modal.value.dateType,
        dateStart: modal.value.rangeTime ? dayjs(modal.value.rangeTime[0]).format("YYYY-MM-DD HH:mm:ss") : null,
        dateEnd: modal.value.rangeTime ? dayjs(modal.value.rangeTime[1]).format("YYYY-MM-DD HH:mm:ss") : null,
      }
    }
    if(isNullOrUnDef(modal.value.dateType)){
      params.data.dateType = DateTypeEnum.CUSTOM
    }else{
      params.data.dateType = modal.value.dateType
      params.data.dateStart = null
      params.data.dateEnd = null
    }

    const res = await getEcommerceStats(params)
    if (!res || !isObject(res)) {
      return
    }
    for (const key in res) {
      const num = !numPnelKeys.includes(key)? res[key] : Number(res[key] / 100).toFixed(2)
      dataPanel.value[key].num = num
    }
  } catch (error) {
    message.createMessageError(`获取失败：${error}`)
  } finally {
    isLoading.value = false
  }
}
function handleSearch() {
  getChartStats()
  getChartPanel()
}
watch(() => modal.value.dateType, () => {
  if (modal.value.dateType === DateTypeEnum.ALL) {
    modal.value.rangeTime = null
  }
  if (modal.value.dateType === DateTypeEnum.SEVEN_DAYS) {
    const dayTime = DateRangeQuick[DateRangeQuickSelectEnum.last_seven_days]
    modal.value.rangeTime = [dayTime.startTime, dayTime.endTime]
  }
  if (modal.value.dateType === DateTypeEnum.TODAY) {
    const dayTime = DateRangeQuick[DateRangeQuickSelectEnum.today]
    modal.value.rangeTime = [dayTime.startTime, dayTime.endTime]
  }
  handleSearch()
}, {
  deep: true
})
onMounted(() => {
  handleSearch()
})
</script>

<style scoped lang="less">
@import "@/styles/default.less";

.header-title {
  font-size: 20px;
  font-weight: bold;
  color: #333333;

  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 16px;
    background: #4DA4FF;
    border-radius: 12px;
    margin-right: 4px;
  }
}

.header-desc {
  color: #666;
}

.data-content {
  display: flex;
  flex-direction: column;

  .data-title {
    font-size: 16px;
    color: #666666;
    display: flex;
    align-items: center;
    margin-top: 12px;
    cursor: pointer;
  }

  .data-num {
    font-size: 56px;
    font-family: Bebas Neue, Bebas Neue;
    letter-spacing: -3px;
  }
}
</style>