import { hasAuth } from "@/utils/auth/authUtils";
import { DiseaseManagementAuth } from "@/enums/authKeys";

/** 新增 */
export const hasDiseaseManagementAddAuth = function(){
    return hasAuth(DiseaseManagementAuth.DiseaseManagementAdd.key);
}()

/** 编辑 */
export const hasDiseaseManagementEditAuth = function(){
    return hasAuth(DiseaseManagementAuth.DiseaseManagementEdit.key);
}()

/** 删除 */
export const hasDiseaseManagementDeleteAuth = function(){
    return hasAuth(DiseaseManagementAuth.DiseaseManagementDelete.key);
}()