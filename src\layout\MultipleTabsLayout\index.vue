<template>
  <n-layout :native-scrollbar="false">
    <n-layout :has-sider="sideMenu.length > 0">
      <n-layout-sider
        v-if="isShowSideMenu"
        :collapsed="isSiderCollapsed"
        content-style="padding: 0px 0px 10px;"
        bordered
        collapse-mode="width"
        width="150"
      >
        <n-scrollbar style="height: calc(100% - 32px);border-top: solid 2px #eee;">
          <n-menu
            :collapsed="isSiderCollapsed"
            v-model:value="activeKey"
            :options="sideMenu"
            :indent="18"
            :default-expand-all="true"
            :expand-icon="renderExpandIcon"
          />
        </n-scrollbar>
        <!-- <div style="text-align: end; padding: 0px 10px; height: 30px">
          <n-icon size="26" @click="toggleSiderCollapseStatus" style="cursor: pointer">
            <SvgIcon :localIcon="isSiderCollapsed ? 'expand' : 'collapse'"></SvgIcon>
          </n-icon>
        </div> -->
      </n-layout-sider>
      <n-layout-content>
        <div v-if="SystemSetting.multipleTabs">multipleTabs</div>
        <!-- 主体 -->
        <div class="inner-layout-wrapper">
          <n-scrollbar style="height: 100%" :native-scrollbar="false">
            <div class="blank-wrapper">
              <router-view v-slot="{ Component, route }">
                <transition appear name="fade-slide" mode="out-in">
                  <component 
                    :is="Component" 
                    :key="route.fullPath"
                    class="animation-container"
                  ></component>
                </transition>
              </router-view>
            </div>
          </n-scrollbar>
        </div>
      </n-layout-content>
    </n-layout>
  </n-layout>
</template>

<script setup lang="ts">
import { SystemSetting } from "@/settings/systemSetting";
import {ref, h, computed} from "vue";
import { useMenu } from "@/hooks/useMenu";
import { NIcon } from "naive-ui";
import { ChevronDownOutline } from '@vicons/ionicons5';
import {useSystemStore} from "@/stores/modules/system";
import {storeToRefs} from "pinia";
import {SystemStoreType} from "@/enums";
import { isDoctorEnv } from "@/utils/envUtils";

defineOptions({ name: 'MultipleTabsLayout' });

const isSiderCollapsed = ref(false);

const toggleSiderCollapseStatus = () => {
  isSiderCollapsed.value = !isSiderCollapsed.value;
};

/** 菜单 */
const { sideMenu, activeKey } = useMenu();

function renderExpandIcon(menuOption){
  if(menuOption.children.filter(item=>item.show === false).length){
    return undefined
  }
  else return h(NIcon, null, { default: () => h(ChevronDownOutline)})
}

/** 监听 */
// watch(
//   activeKey,
//   async (newVal) => {
//     await nextTick();
//     try {
//       const domList = document.getElementsByClassName("n-submenu");
//       domList.forEach((dom) => {
//         const subMenuItemDom = dom.getElementsByClassName("n-menu-item")[0];
//         const subMenuChildrenDom =
//           dom.getElementsByClassName("n-submenu-children")[0];
//         if (!subMenuChildrenDom?.getElementsByClassName("n-menu-item").length) {
//           subMenuItemDom.getElementsByClassName(
//             "n-menu-item-content__arrow"
//           )[0].style.display = "none";
//         }
//       });
//     } catch (e) {
//       console.log(e);
//     }
//   },
//   { immediate: true }
// );

const systemStore = useSystemStore()
const {_globalConfig} = storeToRefs(systemStore);

// 当前登录是否为医师或者药师端
const isDoctorEndMode = computed(()=>{
  // return _globalConfig.value.marketplaceType == SystemStoreType.DOCTOREND
  return true
})

// 如果是医师或者药师端，则侧边菜单不显示
const isShowSideMenu = computed(()=>{
  return sideMenu.value.length > 0 && !isDoctorEnv()
})
</script>

<style scoped lang="less">
@import "@/styles/defaultVar.less";
.inner-layout-wrapper {
  height: @main-content-height;
}
.blank-wrapper {
  min-height: @main-content-height;
  width: 100%;
  padding: @blank-page-padding;
  background-color: @blank-background-color;
  box-sizing: border-box;
}
.animation-container {
  transition-property: color, background-color, border-color, outline-color,
    text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter,
    backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}
</style>
