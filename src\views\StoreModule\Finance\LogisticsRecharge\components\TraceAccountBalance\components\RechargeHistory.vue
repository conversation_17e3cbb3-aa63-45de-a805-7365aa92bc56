<template>
	<JModal
		v-model:show="show"
		width="1200"
    height="600"
		title="充值历史"
		@after-leave="closeModal"
    @after-enter="tableSearch"
    :positive-text="null" 
    :negative-text="null"
	>
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :pagination="paginationRef"
      :isTableSelection="false"
      @paginationChange="paginationChange"
    >
    </FormLayout>
	</JModal>
</template>

<script lang="tsx" setup name="RechargeHistory">
import { ref, onMounted } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { getRechargeHistory } from "@/services/api";

export interface ModalProps {
	businessId: string;
}

/** 表格方法Hook */
const {
  isLoading,
  tableData,
  pageTableData,
  paginationRef,
  paginationChange,
  refreshTableData,
} = useTableDefault({
  pageDataRequest: getRechargeHistory,
});

/* 表格列表项 */
const tableColumns = ref([
    {
        title: "充值金额",
        key: "money",
        width: 150,
        align: "left",
        render: (row) => {
          return <amount-conver value={row.money} />;
        },
    },
    {
        title: "充值单数",
        key: "times",
        width: 180,
        align: "left",
        render: (row) => {
		    	return row.times ?? "-";
		    },
    },
    {
        title: "充值后总单数",
        key: "totalTimes",
        width: 150,
        align: "left",
        render: (row) => {
		    	return row.totalTimes ?? "-";
		    },
    },
    {
        title: "充值时间",
        key: "createTime",
        width: 150,
        align: "left",
        render: (row) => {
		    	return row.createTime ?? "-";
		    },
    },
    {
        title: "操作员",
        key: "createBy",
        width: 150,
        align: "left",
        render: (row) => {
		    	return row.createBy ?? "-";
		    },
    },
]);

/* 模态框显隐状态 */
const show = ref(false);

/* 父组件传过来的参数 */
const parameter = ref<ModalProps>({
    businessId: ''
});

/* 接收父组件参数 */
const acceptParams = (params: ModalProps) => {
  parameter.value = { ...parameter.value, ...params };
	show.value = true;
};

/** 获取参数 */
const getParams = () => {
  const { businessId } = parameter.value;
  return {
    businessId
  };
};

/* 表格搜索 */
const tableSearch = () => {
  pageTableData(getParams(), paginationRef.value);
};

/* 清空表单 */
const formDataReset = () => {
	tableData.value = [];
};

/* 关闭弹窗 */
const closeModal = () => {
	formDataReset();
};

defineExpose({
	acceptParams,
});
</script>

<style lang="less" scoped></style>
