<template>
    <JSelect
      :value="props.value"
      :loading="isLoading"
      :onFocus="handlerFocus"
      :onBlur="handlerBlur"
      :options="shipCompanyList"
      :onClear="handleClear"
      :multiple="isMultiple"
      :max-tag-count="maxTagCount"
      :display-quantity="props.isMultiple === false ? 0 : maxTagCount"
      @update:value="onChange"
      :style="{width: props.width+'px'}"
      placeholder="请选择快递公司"
      :filter="customFilter"
      @scroll="handleScroll"
      @keydown.enter="handleSearch"
      :reset-menu-on-options-change="isResetSelectStatusRef"
    />
  </template>
  <script setup lang="ts">
  import { ref, watch, computed } from "vue";
  import JSelect from "@/components/JSelect/index.vue";
  import { shipCompanyPage } from "@/services/api";
  import { useMessages } from "@/hooks";
  import { isArray } from "@/utils/isUtils";
  /* Props */
  const props = withDefaults(
    defineProps<{
      isImmediately?: boolean; // 是否立即加载
      value: Array<string | number> | string | number | null; // 选择的值
      isMultiple?: boolean; // 是否多选 --> 非必传，默认值为false
      forbiddenId?: Array<string> | null; // 禁用的id
      width?: string | number ; // 宽度
    }>(),
    {
      isImmediately: false,
      isMultiple: false,
      forbiddenId: null,
      width:140
    },
  );
  
  const emits = defineEmits<{
    (e: "update:value", selectValue: any): void; // 更新选择值事件
    (e: "update:isSelectAll", val: boolean): void; // 更新是否全选
    (e: "update:shipCompanyName", val: boolean): void; // 更新快递公司编码
  }>();
  
  /* 提示 */
  const message = useMessages();
  
  /* 是否加载 */
  const isLoading = ref(false);
  const isResetSelectStatusRef = ref(false); // 重置选择状态
  const shipCompanyList = ref([]); // 列表
  const _resultTempList = []; // 临时结果列表
  const maxTagCount = ref(2);
  const isSelectAll = ref(false); // 是否全选
  const selectedValue = ref([]) //已经被选中的selectedValue
  
  /* 执行搜索返回的内容*/
  let recordsTotal = 1; // 总记录数
  
  const params: { data:{name:string|number},pageVO: { current: number; size: number } } = {
    data:{
      name:'',//名称
    },
    pageVO: {
      current: 1, // 当前页
      size: 100, // 每页大小
    },
  };
  
  const _tempMemberTagList = computed(() => {
    return shipCompanyList.value.filter(item => item.value !== "all");
  });
  
  /* 筛选、转化{label: '', value: ''} */
  function handleData(filterData: Array<ApiStoreModule.ExpressCompanyClassification>) {
    let dataList = [];
    dataList = filterData.map(item => {
      if (isArray(props.forbiddenId) && props.forbiddenId.includes(item.id)) {
        return { label: item.name, value: item.code, disabled: true };
      } else {
        return { label:item.name,  value: item.code, disabled: false };
      }
    });
    // 是否开启多选，添加全部选项
    if (props.isMultiple) {
      dataList.unshift({ label: "全部", value: "all", disabled: false });
    }
    return dataList;
  }
  
  /* 获取商品分类列表 */
  async function getMemberTagList() {
    try {
      isLoading.value = true;
      const { total, current, size, records } = await shipCompanyPage(params);
      params.pageVO.current = Number(current);
      params.pageVO.size = Number(size);
      recordsTotal = Number(total);
      // 如果是第一页
      if (params.pageVO.current == 1) {
        isResetSelectStatusRef.value = true; // 重置选择状态为true
        shipCompanyList.value = handleData(records);
      } else {
        isResetSelectStatusRef.value = false; // 重置选择状态为false
        handleData(records).forEach(item => {
          // 如果列表中不存在该项
          if (!shipCompanyList.value.find(temp => temp.value == item.value)) {
            // 添加到列表中
            shipCompanyList.value.push(item);
          }
        });
      }

      // 判断是否有已选中内容
      if(selectedValue.value.length && props.value){
        selectedValue.value.forEach(item => {
          if (!shipCompanyList.value.some(existingItem => existingItem.value === item.value)) {
            shipCompanyList.value.push(item);
            selectedValue.value = []
          }
        });
      }
    } catch (error) {
      message.createMessageError("获取快递公司信息失败：" + error);
    } finally {
      isLoading.value = false;
      if (isSelectAll.value) {
          handleSelectAll();
      }
    }
  }
  
  /** 自定义过滤函数 */
  function customFilter(keyword, options) {
    const labelMatch = options.label.toLowerCase().includes(keyword.toLowerCase());
    const valueMatch = options.value.toLowerCase().includes(keyword.toLowerCase());
    return labelMatch || valueMatch;
  }
  
  /** 选择值改变事件处理函数 */
  function onChange(value,label) {
    // 额外的全选逻辑
    if (props.isMultiple) {
      if (value?.includes("all")) {
        isSelectAll.value = true;
        let newVal = shipCompanyList.value.filter(item => item.value !== "all").map(item => item.value);
        emits("update:value", newVal);
        emits("update:isSelectAll", isSelectAll.value);
        return;
      }
    }
    isSelectAll.value = false;
    selectedValue.value = [label]
    emits("update:value", value);
    emits("update:isSelectAll", isSelectAll.value);
    emits("update:shipCompanyName", label.label);
    handleClearName()
  }
  
  /** 处理全选 */
  const handleSelectAll = () => {
      let newVal = shipCompanyList.value.filter(item => item.value !== "all").map(item => item.value);
      emits("update:value", newVal);
  };
  
  /** 清空事件处理函数 */
  const handleClear = () => {
    emits("update:value", null);
    handleClearName()
  };
  
  /** 滚动事件处理函数 */
  function handleScroll(e) {
    const currentTarget = e.currentTarget as HTMLElement;
    if (currentTarget.scrollTop + currentTarget.offsetHeight >= currentTarget.scrollHeight) {
      // 如果当前页乘以每页大小小于总记录数
      if (params.pageVO.current * params.pageVO.size < recordsTotal) {
        params.pageVO.current++; // 当前页加1
        getMemberTagList();
      }
    }
  }
  
  /** 聚焦事件处理函数 */
  function handlerFocus() {
    // 如果会员标签列表为空
    if (!shipCompanyList.value.length) {
      getMemberTagList();
    }
  }

  /** 失焦事件处理函数 */
  function handlerBlur(){
    handleClearName()
  }
  
  /** 搜索事件处理函数 */
  function handleSearch(event) {
    params.pageVO.current = 1;
    params.data.name = event.target.value;
    getMemberTagList();
  }
  
  /** 是否禁用全选 */
  const setSelectAll = (value: any) => {
    if (props.isMultiple && value?.length === _tempMemberTagList.value.length) {
      shipCompanyList.value.find(item => {
        if (item.value === "all") {
          item.disabled = true;
        }
      });
    } else {
      shipCompanyList.value.find(item => {
        if (item.value === "all") {
          item.disabled = false;
        }
      });
    }
  };

  /** 清除搜索名称函数 */
  const handleClearName = () =>{
    if(params.data.name != ''){
      params.data.name = ''
      getMemberTagList();
    }
  }
  
  /** 监听 */
  watch(
    () => props.isImmediately,
    newVal => {
      if (newVal) {
        getMemberTagList();
      }
    },
    { immediate: true },
  );
  
  watch(
    () => props.value,
    newVal => {
      props.isMultiple && setSelectAll(newVal);
    },
  );
  </script>
  
  <style scoped lang="less"></style>
  