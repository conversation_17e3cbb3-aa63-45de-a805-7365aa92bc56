<template>
    <div class="wrapper inner-page-height">
        <FormLayout
          :isLoading="isLoading"
          :tableData="tableData"
          :tableColumns="tableColumns"
          :pagination="paginationRef"
          @paginationChange="paginationChange"
          :isNeedCollapse="false"
          :isTableSelection="false"
        >
          <!-- 表单 -->
          <template #searchForm>
            <n-form
              ref="formRef"
              label-placement="left"
              label-width="auto"
              :show-feedback="false"
              require-mark-placement="right-hanging"
              size="small"
              :style="{ width: '100%' }"
            >
              <n-form-item :span="12" :show-label="false" path="">
                <n-radio-group
                   v-model:value="model.clickState"
                   name="left-size"
                   style="margin-bottom: 12px"
                   size="small"
                >
                  <n-radio-button  
                   v-for="(item,index) in  afterSalesStatusScreeningOptions" 
                   :key="index" 
                   :value="item.value"
                   :disabled="isLoading">
                    {{item.label + (index != 0 ? '(' +  (getPageStats.length != 0 ? getPageStats[index - 1]?.value : 0) +')' : '')}}
                  </n-radio-button>
                </n-radio-group>
              </n-form-item>
              <n-form-item :span="12" :show-label="false" path="">
                <n-select 
                  v-model:value="model.pullDownState" 
                  :options="afterSalesStatusOptions.filter(item => item.value !== 0 && item.value !== 1 && item.value !== 5 && item.value !== 8)" 
                  placeholder='请选择更多状态'
                  style="width: 170px;" 
                  clearable
                  :disabled="isLoading"
                />
              </n-form-item>
              <n-grid cols="4 m:12 l:18 xl:24" :x-gap="32" responsive="screen">
                <n-gi :span=5>
                   <!-- 售后单号 -->
                   <n-form-item label="">
                    <n-input-group>
                      <n-select
                        v-model:value="model.noStatic"
                        placeholder="请选择"
                        :options="noStaticOpt"
                        style="width: 120px;"
                        :render-option="renderOption"
                      />
                      <j-search-input
                          v-model:value="model.noNum"
                          placeholder="请输入单号"
                          @search="handlerSearch"
                      />
                     </n-input-group>
                   </n-form-item>
                </n-gi>
                <n-gi :span="4">
                    <!-- 售后类型 -->
                    <n-form-item label="售后类型">
                        <n-select 
                         v-model:value="model.type" 
                         :options="afterSalesLabelsOptions" 
                         placeholder='请选择售后类型'
                         style="width: 170px;" 
                         clearable
                        />
                    </n-form-item>
                </n-gi>
                <n-gi :span="4">
                    <!-- 退款原因 -->
                   <n-form-item label="退款原因">
                        <n-select 
                         v-model:value="model.reason" 
                         :options="aReasonForRefundOptions" 
                         placeholder='请选择退款原因'
                         style="width: 170px;" 
                         clearable
                        />
                   </n-form-item>
                </n-gi>
                <n-gi :span="6">
                    <!-- 创建时间 -->
                    <n-form-item label="创建时间">
                        <j-date-range-picker style="flex: 1;" v-model:value="model.time"    type="datetimerange" format="yyyy-MM-dd" :default-time="['00:00:00', '23:59:59']" clearable />
                    </n-form-item>
                </n-gi>

                <n-gi :span="6">
                  <n-form-item  label="其他" path="range" >  
                    <n-input-group>
                      <n-select
                        v-model:value="model.belongType"
                        placeholder="请选择"
                        :options="belongOptions" 
                        style="width: 110px;"
                        :render-option="renderOption"
                      />
                    <j-search-input
                        v-model:value="model.belongSearchValue"
                        placeholder="请输入关键字信息"
                        @search="handlerSearch"
                    />
                    </n-input-group>
                  </n-form-item>
                </n-gi>
              </n-grid>
            </n-form>
          </template>
          <template #tableHeaderBtn>
            <n-button :disabled="isLoading" @click="refresh" class="store-button">刷 新</n-button>
          </template>
        </FormLayout>
        <ConfirmContentModal ref="confirmContentShow"/>
        <AfterServiceDetails ref="afterServiceDetailsRef"/>
        <SupplierProductRefund v-model:show="showSupplierProductRefund" :info="supplierProductRefundInfo" @success="refresh"  />
    </div>
  </template>
  
  <script lang="tsx" setup name="DoctorManagement">
  import { onMounted, ref, watch, h, type VNode } from "vue";
  import { useTableDefault } from "@/hooks/useTableDefault";
  import { 
    afterSaleRecordPage, 
    afterSaleRecordGetDetail,
    afterSaleRecordDoAction,
    afterSaleRecordGetPageStats 
  } from "@/services/api/afterServiceApi";
  import { useMessages } from '@/hooks';
  import { 
    afterSalesStatusScreeningOptions,
    afterSalesStatusOptions,
    afterSalesLabelsOptions,
    aReasonForRefundOptions,
    aReasonForRefundLabels,
    afterSalesLabels,
    afterSalesStatusLabels,
    deliveryStatusLabels 
  } from "@/constants";
  import moment from "moment";
  import { afterSaleStateType } from "@/enums";
  /** 相关组件 */
  import FormLayout from "@/layout/FormLayout.vue";
  import JButton from "@/views/StoreModule/PrescriptionManagement/compoments/JButton.vue";
  import ConfirmContentModal from './components/ConfirmContentModal.vue';
  import AfterServiceDetails from './components/AfterServiceDetails.vue';
  import SupplierProductRefund from './components/SupplierProductRefund.vue';
  import {hasAfterServiceManagementIndexOperation,hasAfterDetails} from './authList'
  import { NTooltip, type SelectOption } from 'naive-ui';
import { isEmpty } from "@/utils";
  const { createMessageSuccess, createMessageError } = useMessages();

  const renderOption = ({ node, option }: { node: VNode; option: SelectOption }) =>
        h(NTooltip, null, {
          trigger: () => node,
          default: () => option.label
   })
  
  onMounted(()=>{
    getHeaderStatistics()
  })
  
  /** 表格hook */
  const {
    isAddLoading,
    isEditLoading,
    tableData,
    isLoading,
    paginationRef,
    pageTableData,
    deleteTableData,
    editTableData,
    addTableData,
    refreshTableData,
    paginationChange,
  } = useTableDefault({
    pageDataRequest: afterSaleRecordPage,
  });

  // 类型下拉项
  const noStaticOpt = [
    {
      label: "售后单号",
      value: 'recordNo',
    },
    {
      label: "支付单号",
      value: 'payNo',
    },
    {
      label: "订单编号",
      value: 'orderCode',
    },
    {
      label: "退货物流单号",
      value: 'trackingNo',
    },
  ]

  /** 归属类型 */
const belongOptions  = [
  {
    label: "归属经销商",
    value: "thirdDealerName",
  },
  {
    label: "归属群管",
    value: "thirdGroupMgrName",
  },
  {
    label: "归属课程",
    value: "thirdCourseName",
  },
  {
    label: "归属分销员",
    value: "distributorName",
  },
  {
    label:"归属供应商",
    value:"supplierName"
  }
]

  /* 表格列表项 */
  const tableColumns = [
    {
      title: "售后单号",
      key: "recordNo",
      align: "left",
      fixed: "left",
      width: 100,
    },
    {
      title: "售后商品",
      key: "doctorName",
      width: 200,
      align: "left",
      render: rowData => {
        if(rowData?.recordType === 2) {
          return "问诊单"
        }
        if(rowData.orderItemNameList?.length > 1 ){
          const orderItemNameArray = []
          rowData.orderItemNameList.forEach((item,index)=>{
            if(index != 0){
              orderItemNameArray.push(item)
            }
          })
          const title =  '等'+ (rowData.orderItemNameList.length)+ '种商品'
          return <div>
            <p>
              <n-ellipsis>{rowData.orderItemNameList[0]}</n-ellipsis> 
            </p>
            <n-popover trigger="hover">
                      {{
                          default: () => `${orderItemNameArray.join(',')}`,
                          trigger: () => (
                           <span style={'font-size: 12px;color:red;cursor: pointer;'}>{title}</span>
                          )
                      }}
            </n-popover>
          </div>
        }else{
          return rowData.orderItemNameList?.length > 0  ? rowData.orderItemNameList : "-"
        }
      },
    },
    {
      title: "发货状态",
      key: "shipStatus",
      width: 130,
      align: "left",
      render: row => {
        if(deliveryStatusLabels[row['shipStatus']]){
          return <span>{deliveryStatusLabels[row['shipStatus']]}</span>
        }else{
          return '-';
        }
      }
    },
  
    {
      title: "售后类型",
      key: "type",
      width: 130,
      align: "left",
      render: row => {
        if(afterSalesLabels[row['type']]){
          return <span>{afterSalesLabels[row['type']]}</span>
        }else{
          return '-';
        }
      }
    },
    {
      title: "退款原因",
      key: "reason",
      width: 130,
      align: "left",
      render: row => {
        if(aReasonForRefundLabels[row['reason']]){
          return <span>{aReasonForRefundLabels[row['reason']]}</span>
        }else{
          return '-';
        }
      }
    },
    {
      title: "退款金额",
      key: "refundAmount",
      width: 130,
      align: "left",
      render: row => {
        let refundAmount = row.refundAmount ?? 0;
        // 分转元，并保留两位小数
        const formattedPrice = `￥${(refundAmount / 100).toFixed(2)}`;
        return <span>{formattedPrice}</span>;
      }
    },
    {
      title: "售后状态",
      key: "state",
      width: 130,
      align: "left",
      render: row => {
        if(afterSalesStatusLabels[row['state']]){
          return <span>{afterSalesStatusLabels[row['state']]}</span>
        }else{
          return '-';
        }
      }
    },
    {
      title: "归属经销商",
      key: "thirdDealerName",
      align: "left",
      width: 100,
    },
    {
      title: "归属群管",
      key: "thirdGroupMgrName",
      align: "left",
      width: 100,
    },
    {
      title: "归属课程",
      key: "thirdCourseName",
      align: "left",
      width: 100,
    },
    {
      title: "归属分销员",
      key: "distributorName",
      align: "left",
      width: 100,
    },
    {
      title: "归属供应商",
      key: "supplierName",
      align: "left",
      width: 100,
    },
    {
      title: "创建时间",
      key: "createTime",
      width: 150,
      align: "left",
    },
    {
      title: "操作",
      key: "action",
      width: 100,
      fixed: "right",
      align: "left",
      render: (row) => {
        return(
            <n-space>
                 {hasAfterServiceManagementIndexOperation && (
                  <>
                    {afterSalesStatusLabels[row['state']] === afterSalesStatusLabels[1] && (
                        <JButton onClick={() => clickOperationEvent(row, '同意')}>同意</JButton>
                    )}
                    {(afterSalesStatusLabels[row['state']] === afterSalesStatusLabels[1] || afterSalesStatusLabels[row['state']] === afterSalesStatusLabels[5]) && (
                        <JButton onClick={() => clickOperationEvent(row, '拒绝')}>拒绝</JButton>
                    )}
                    {afterSalesStatusLabels[row['state']] == afterSalesStatusLabels[5]  ? <JButton onClick={() => clickOperationEvent(row,'收货并退款')}>收货并退款</JButton> : null
                    } 
                    {afterSalesStatusLabels[row['state']] == afterSalesStatusLabels[8] && row?.action.includes(10) ? <JButton onClick=   {() => clickOperationEvent(row,'再次发起退款')}>再次发起退款</JButton>: null
                    } 
                    {afterSalesStatusLabels[row['state']] == afterSalesStatusLabels[8] && row?.action.includes(11)  ? <JButton onClick=   {() => clickOperationEvent(row,'已线下退款')}>已线下退款</JButton> : null
                    }
                    {afterSalesStatusLabels[row['state']] == afterSalesStatusLabels[4]  ? <JButton onClick=   {() => clickOperationEvent(row,'录入物流信息')}>录入物流信息</JButton> : null
                    }  
                   </>
                 )}
                 {
                  hasAfterDetails ? <JButton onClick={() => clickDetails(row)}>详情</JButton> : null
                 }
            </n-space>
        )
      }
    },
  ];

  /** 参数 */
  const model = ref({
    clickState: afterSaleStateType.ALL,
    pullDownState: undefined,
    state: undefined, // 售后状态
    noNum: '', // 售后单号
    type: undefined, // 售后类型
    reason: undefined, // 退款原因
    time: null, // 创建时间
    noStatic: 'recordNo', // 查询单号类型,
    belongType:'thirdDealerName',
    belongSearchValue:'',
  });
  
  /** 获取分页表头统计 */
  const getPageStats = ref([])
  const getHeaderStatistics = async() =>{
    try{
      const data = await afterSaleRecordGetPageStats();
      getPageStats.value = Object.entries(data).map(([key, value]) => ({ name: key, value }))
    }catch(err){
      createMessageError('获取分页表头统计失败:' + err)
    }
  }

  /** 获取参数 */
  const belongSearchValue = ref()
  const getParams = () => {
    const { noNum, noStatic, type, reason, time,state} = model.value;
    const createStartTime = time ? moment(time[0]).format(`YYYY-MM-DD HH:mm:ss`) : null
    const createEndTime = time ? moment(time[1]).format(`YYYY-MM-DD HH:mm:ss`) : null
    
    if(model.value.belongType == 'thirdDealerName'){
          belongSearchValue.value = 'thirdDealerName'
    }
    if(model.value.belongType == 'thirdGroupMgrName'){
          belongSearchValue.value = 'thirdGroupMgrName'
    }
    if(model.value.belongType == 'thirdCourseName'){
          belongSearchValue.value = 'thirdCourseName'
    }
    if(model.value.belongType == 'distributorName'){
          belongSearchValue.value = 'distributorName'
    }
    if(model.value.belongType == 'supplierName'){
          belongSearchValue.value = 'supplierName'
    }
    
    return {
      [noStatic]:noNum,
      type,
      reason,
      state,
      createStartTime,
      createEndTime,
      [belongSearchValue.value] : model.value.belongSearchValue
    };
  };
  
  /** 搜索 */
  const handlerSearch = () => {
    tableSearch();
  };

  /** 表格刷新 */
  const refresh = () =>{
    getHeaderStatistics()
    tableSearch();
  };

  /* 表格搜索 */
  const tableSearch = () => {
    pageTableData(getParams(), paginationRef.value);
  };

  /** 点击操作事件 */
  const confirmContentShow = ref();
  const showSupplierProductRefund = ref(false);
  const supplierProductRefundInfo = ref({})
  const clickOperationEvent = (row,type) =>{
    console.log('row',row);
    /** 退货退款情况下的同意 并且有绑定供应商 */
    if (type == '同意' && row.type == 2 && !isEmpty(row.supplierId) ) {
      showSupplierProductRefund.value = true;
      supplierProductRefundInfo.value = row;
      return;
    }
    const _params = {
      row,
      type,
      getInfoApi: afterSaleRecordDoAction,
      refresh: refresh,
    }
    switch (type) {
    case '同意':
      for (let i = 1; i <= 5; i++) {
        if (row.action.includes(i)) {
          _params['code'] = i;
          break;
        }
      }
      break;
    case '拒绝':
      _params['code'] = 7;
      break;
    case '收货并退款':
      for (let i = 8; i <= 9; i++) {
        if (row.action.includes(i)) {
          _params['code'] = i;
          break;
        }
      }
      break;
    case '再次发起退款':
      _params['code'] = 10;
      break;
    case '已线下退款':
      _params['code'] = 11;
      break;
    case '录入物流信息':
      _params['code'] = 23;
    break;
    default:
      break;
    }
    confirmContentShow.value?.acceptParams(_params);
  }

  /** 详情 */
  const afterServiceDetailsRef = ref<InstanceType<typeof AfterServiceDetails> | null>(null);
  const clickDetails  = (row) =>{
    const _params = {
      row,
      getInfoApi: afterSaleRecordGetDetail,
      getDoActionApi:afterSaleRecordDoAction,
      refresh: refresh,
      permissionControl:hasAfterServiceManagementIndexOperation
    };
    afterServiceDetailsRef.value?.acceptParams(_params);
  }

  /** 组件挂载 */
  onMounted(() => {
    tableSearch();
  });

  watch(() => [
    model.value.state,
    model.value.time,
    model.value.reason,
    model.value.type
  ], (newVal) => {
    tableSearch()
  });

  watch(() =>model.value.clickState,
  (newVal)=>{
    if(newVal != null){
      model.value.state = newVal == 0 ? null : newVal
      model.value.pullDownState = null
    }
  })

  watch(() =>model.value.pullDownState,
  (newVal)=>{
    if(newVal != null){
      model.value.state = newVal
      model.value.clickState = null
    }
    if(model.value.clickState == null && newVal == null){
      model.value.state = newVal
    }
  })
  </script>
  
<style lang="less" scoped>
@import "@/styles/defaultVar.less";
.action-wrapper {
  display: flex;
  align-items: center;
}
</style>
  