<template>
    <JModal
    v-model:show="isShow"
    width="1000"
    height="600"
    title="领取记录"
    @after-leave="closeModal"
    :negativeText="false"
    :positiveText="false"
  >
  <div class="wrapper">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :pagination="paginationRef"
      @paginationChange="paginationChange"
      :isNeedCollapse="false"
      :isDisplayIndex="false"
      :is-table-selection="false"
    >
      <template #searchForm>
        <n-radio-group v-model:value="useStatus" size="small">
          <n-radio-button :value="-1" :disabled="isLoading">全部</n-radio-button>
          <n-radio-button  
            v-for="(item,index) in  WelfareDrawStateOptions" 
            :key="index" 
            :value="item.value"
            :disabled="isLoading">
            {{ item.label }}
          </n-radio-button>
        </n-radio-group>
      </template>
    </FormLayout>
  </div>
  </JModal>
</template>

<script setup lang="tsx">
import { ref,computed, watch } from 'vue';
import { pageByCouponBatchId } from '@/services/api';
import { ReceiveRecordEnum } from "../types"
import { useTableDefault } from "@/hooks/useTableDefault";
import FormLayout from "@/layout/FormLayout.vue";
import { WelfareDrawStateOptions  } from "@/constants";
import { WelfarePageTypeEnum } from "../types";
import { createReceiveRecordColumns } from "../TableColumns";
/** 表格hook */
const {
  isLoading,
  tableData,
  paginationRef,
  pageTableData,
  paginationChange,
} = useTableDefault({
  pageDataRequest: pageByCouponBatchId,
});
const useStatus = ref<ReceiveRecordEnum>(ReceiveRecordEnum.ALL);
const props = withDefaults(defineProps<{
    couponBatchId: string;
    show: boolean;
    welfarePageType: WelfarePageTypeEnum;
}>(), {
    couponBatchId: '',
    show: false,
    welfarePageType: WelfarePageTypeEnum.PAFGE,
});
const emits = defineEmits<{
    (e: 'update:show', value: boolean): void;
}>();

const isShow = computed({
    get: () => props.show,
    set: (value: boolean) => {
      emits('update:show', value);
    }
});

/* 表格列表项 */
const tableColumns = createReceiveRecordColumns({welfarePageType: props.welfarePageType})

// 关闭按钮
const closeModal = () => {
    isShow.value = false;
    useStatus.value = ReceiveRecordEnum.ALL
}

/** 获取参数 */
const getParams = () => {
  return {
    useStatus:useStatus.value,
    couponBatchId:props.couponBatchId
  };
};

/* 表格搜索 */
const tableSearch = () => {
  pageTableData(getParams(), paginationRef.value);
};


watch(()=>props.show, (newVal, oldVal) => {
  if(newVal){
    tableSearch()
  }
});

watch(()=>useStatus.value, () => {
  if(props.show){
    tableSearch()
  }
});

</script>
<style lang="less" scoped>
@import "@/styles/defaultVar.less";
.wrapper{
  height:  100%;
}
</style>