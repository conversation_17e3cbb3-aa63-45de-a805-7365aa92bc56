<template>
  <n-modal v-model:show="modalShow" :auto-focus="false" @after-leave="handleClose">
    <n-card
      class="regional-manager-registration-code-card"
      style="width: 420px"
      :bordered="false"
      size="small"
      title="经销商注册码"
      closable
      @close="handleClose"
    >
      <n-spin :show="loading" description="加载中...">
        <div class="poster-container" id="poster">
          <img class="poster-img" :src="DealerCodeBg" />
          <n-qr-code v-if="linkCode" class="link-code-img" :value="linkCode" error-correction-level="Q" size="200" />
        </div>
       <div v-if="isDownload" class="button-container">
         <n-button type="primary" @click="downloadImg" :loading="isDownLoading">下载</n-button>
       </div>
      </n-spin>
    </n-card>
  </n-modal>
</template>
<script setup lang="ts">
import { computed, reactive, ref, onMounted } from "vue";
import { useMessages } from "@/hooks/useMessage";
import { downloadFile } from "@/utils/fileUtils";
import DealerCodeBg from "@/assets/image/system/dealer_code_bg.png";
import { getSubStructQrCode, getDealerQrCode } from "@/services/api";
import html2canvas from "html2canvas";
const { createMessageError, createMessageSuccess } = useMessages();
const linkCode = ref();
const loading = ref(false);
const emit = defineEmits(["update:show"]);
const props = defineProps({
  isDownload: {
    type: Boolean,
    default: false,
  },
});
const modalShow = ref(false);
const isDownLoading = ref(false);
const handleClose = () => {
  linkCode.value = undefined;
  modalShow.value = false;
};

const acceptParams = async params => {
  modalShow.value = true;
  await getSubStructQrCodeFn(params?.id);
};
const getSubStructQrCodeFn = async (structureId) => {
  // linkCode.value = "http://*************:8080/api/v1/distribution/getRegionQrCode?structureId=1";
  loading.value = true;
  try {
    linkCode.value = structureId ? await getSubStructQrCode(structureId) : await getDealerQrCode();
  } catch (error) {
    console.log(error);
  }finally {
    loading.value = false
  }
};
const downloadImg = async () => {
  isDownLoading.value = true;
  const el = document.getElementById("poster");
  // 配置
  let options = {
    width: el.offsetWidth,
    height: el.offsetHeight,
    useCORS: true,
    allowTaint: false,
  };
  html2canvas(el, options)
    .then(canvas => {
      // 二次绘制圆角（保险措施）
      const roundCanvas = document.createElement("canvas");
      const ctx = roundCanvas.getContext("2d");
      roundCanvas.width = canvas.width;
      roundCanvas.height = canvas.height;

      // 创建圆角路径
      ctx.beginPath();
      ctx.moveTo(26, 0);
      ctx.arcTo(canvas.width, 0, canvas.width, canvas.height, 26);
      ctx.arcTo(canvas.width, canvas.height, 0, canvas.height, 26);
      ctx.arcTo(0, canvas.height, 0, 0, 26);
      ctx.arcTo(0, 0, canvas.width, 0, 26);
      ctx.closePath();

      // 裁剪并绘制原图
      ctx.clip();
      ctx.drawImage(canvas, 0, 0);

      downloadFile(roundCanvas.toDataURL("image/png"), "经销商注册码");
    })
    .catch(err => {
      createMessageError("下载失败");
    })
    .finally(() => {
      isDownLoading.value = false;
    });
};
defineExpose({
  acceptParams,
});
</script>

<style lang="less">
.regional-manager-registration-code-card {
  .n-card__content {
    display: flex;
    flex-direction: column;
    align-items: center;
    .poster-container {
      position: relative;
      border-radius: 16px;
      img {
        display: block;
      }
      .poster-img {
        width: 400px;
      }
      .link-code-img {
        position: absolute;
        left: 50%;
        top: 39%;
        transform: translateX(-50%);
      }
    }
    .button-container {
      display: flex;
      justify-content: center;
    }
    .n-button {
      margin-top: 8px;
      padding: 4px 25px;
    }
  }
}
</style>
