<script setup lang="ts">
import TabsLayout from "@/layout/TabsLayout.vue";
import {ref} from "vue";
import PrescriptionTab from './compoments/prescriptionTab.vue';
import {DoctorEndPrescriptionSelection, PrescriptionSelection} from '@/enums';

const tabNameRef = ref<string>(PrescriptionSelection.unopened);

const tableDataUpdate = ref()

const tabsData = ref([
  {
    label: "待审核",
    key: DoctorEndPrescriptionSelection.waitCheck,
  },
  {
    label: "可使用",
    key: DoctorEndPrescriptionSelection.canUse,
  },
  {
    label: "已使用",
    key: DoctorEndPrescriptionSelection.haveUsed,
  },
  {
    label: "审核不通过",
    key: DoctorEndPrescriptionSelection.checkNoPassed,
  },
  {
    label: "已失效",
    key: DoctorEndPrescriptionSelection.lostEfficacy,
  },
  {
    label: "全部",
    key: DoctorEndPrescriptionSelection.all,
  },
]);

</script>

<template>
  <n-layout>
    <n-layout-content id="prescription-management">
      <TabsLayout v-model:value="tabNameRef" :tabsData="(tabsData as any)" :onlyTabs="true"
                  class="prescription-management-tabsLayout">
        <PrescriptionTab ref="tableDataUpdate" :tabNameRef="tabNameRef"/>
      </TabsLayout>
    </n-layout-content>
  </n-layout>
</template>

<style lang="less">
// @import "@/styles/default.less";
.n-tabs.prescription-management-tabsLayout {
  .n-tabs-wrapper {
    height: 100%;
  }

  .n-tabs-tab-pad, .n-tabs-bar {
    display: none;
  }

  .n-tabs-tab-wrapper + .n-tabs-tab-wrapper {
    margin-left: 8px;
  }

  .n-tabs-tab {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 90px;
    background: #f2f3f5;
    border-radius: 4px 4px 0px 0px;
    border: 1px solid #e5e6eb;
    border-bottom: none;
    box-sizing: border-box;

    &::after {
      content: "";
      position: absolute;
      display: block;
      width: 90px;
      height: 1px;
      bottom: -1px;
      background: #e5e6eb;
    }
  }

  .n-tabs-tab.n-tabs-tab--active {
    background: #fff;

    &::after {
      background: #fff;
    }
  }
}


</style>
