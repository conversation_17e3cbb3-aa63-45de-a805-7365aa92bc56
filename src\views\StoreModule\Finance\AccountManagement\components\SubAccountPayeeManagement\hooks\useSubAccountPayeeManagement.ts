import { useMessages } from "@/hooks";
/* 提示信息 */
const message = useMessages();
const { createMessageError,createMessageSuccess,createMessageWarning } = message
export default function useSubAccountPayeeManagement() {
    
    /** 验证身份证号码的格式和校验码 */
    const validateIDNumber = (idNumber) => {
    const idPattern = /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2]\d|3[0-1])\d{3}[\dXx]$/;
    if (!idPattern.test(idNumber)) {
      return false; // 格式不符合
    }
  
    // 校验码验证（仅针对 18 位身份证号）
    if (idNumber.length === 18) {
        const factors = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
        const checksum = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
        const nums = idNumber
          .slice(0, 17)
          .split("")
          .map((num) => parseInt(num, 10));
        const sum = nums.reduce((acc, num, idx) => acc + num * factors[idx], 0);
        const expectedCheckCode = checksum[sum % 11];
    
        return expectedCheckCode === idNumber[17].toUpperCase(); // 校验码匹配
      }
    
      return true; // 如果是 15 位身份证号（旧式身份证），直接返回 true
    };

    /** 判断输入的手机号码是否正确 */
    const numberVerification = (value) =>{
      const phoneRegex = /^[1]([3-9])[0-9]{9}$/; // 手机号正则
      if(!phoneRegex.test(value) && value != null){
          createMessageWarning('请输入有效的手机号码');
          return false
      }
      return true
    }

    /** 判断输入的邮箱是否正确 */
    const validateEmail = (value) => {
      const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      if (!emailPattern.test(value)) {
        createMessageWarning('请输入正确的邮箱');
        return false
      }
      return true
    };

     /** 判断输入的卡号是否正确 */
     const validateBankCardNo = (value) => {
      const regex = /^[0-9]{10,19}$/; // 一般银行卡号为10-19位数字
      if (!regex.test(value)) {
        createMessageWarning('银行卡号格式不正确');
        return false
      }
      return true
    };

    return {
        validateIDNumber,
        numberVerification,
        validateEmail,
        validateBankCardNo
    }
};