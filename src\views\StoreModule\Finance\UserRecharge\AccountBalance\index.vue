<template>
  <div class="wrapper inner-page-height">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :pagination="paginationRef"
      :isTableSelection="false"
      @paginationChange="paginationChange"
      :isNeedCollapse="false"
      :isDisplayIndex="false"
    >
      <!-- 表单 -->
      <template #searchForm>
        <n-form
          ref="formRef"
          label-placement="left"
          label-width="auto"
          :show-feedback="false"
          require-mark-placement="right-hanging"
          size="small"
          :style="{ width: '100%' }"
        >
          <n-form-item :span="12" label="用户昵称" path="">
            <j-search-input v-model:value.trim="model.nickname" placeholder="请输入用户昵称" @search="handlerSearch" />
          </n-form-item>
        </n-form>
      </template>
      <template #tableHeaderBtn>
        <JAddButton v-if="hasFinanceStoreConfigAccountCreateAuth" @click="showCreateUserAccountRef" type="primary">
          开通用户充值账户
        </JAddButton>
        <n-button @click="refresh" :loading="isLoading" class="store-button">刷 新</n-button>
      </template>
    </FormLayout>
    <CreateUserAccount ref="createUserAccountRef" />
    <OfflineRecharge ref="OfflineRechargeRef" />
    <OfflineRefund ref="offlineRefundRef" />
  </div>
</template>

<script lang="tsx" setup name="AccountBalance">
import { onMounted, ref } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { userAccountGetPage } from "@/services/api/financialApi/userRecharge";
import { useMessages } from "@/hooks";
import CreateUserAccount from "./components/CreateUserAccount.vue";
import OfflineRecharge from "./components/OfflineRecharge.vue";
import OfflineRefund from "./components/OfflineRefund.vue";
import {
  hasFinanceStoreConfigAccountCreateAuth,
  hasFinanceStoreConfigAccountCreateOfflineRefundAuth,
  hasFinanceStoreConfigAccountOfflineRechargeAuth,
} from "@/views/StoreModule/Finance/authList";
import { useUnitConversion } from "../hooks/unitConversion";
const { toYuanString } = useUnitConversion();

const createUserAccountRef = ref(null);
const OfflineRechargeRef = ref(null);
const offlineRefundRef = ref(null);
const { createMessageSuccess, createMessageError } = useMessages();
/** 表格hook */
const {
  isLoading,
  tableData,
  paginationRef,
  pageTableData,
  paginationChange,
} = useTableDefault({
  pageDataRequest: userAccountGetPage,
});

/* 表格列表项 */
const tableColumns = ref([
  {
    title: "用户昵称/ID",
    key: "nickname",
    width: 150,
    align: "left",
    render: (rowData) => {
      let title = `${rowData.nickname ?? ""}`;
      return <table-tooltip row={rowData} nameKey="name" title={title} idKey="id" />;
    },
  },
  {
    title: "账户余额（元）",
    key: "accountBalance",
    width: 100,
    align: "left",
    render: rowData => {
      return toYuanString(rowData?.accountBalance)
    },
  },
  {
    title: "操作",
    key: "action",
    width: 120,
    fixed: "right",
    align: "left",
    render: rowData => {
      return (
        <n-space align="center" justify="center">
          <n-button text size="small" v-show={hasFinanceStoreConfigAccountOfflineRechargeAuth}
                    onClick={() => showOfflineRechargeRef(rowData)} type="primary">
            线下充值
          </n-button>
          <n-button text size="small" v-show={hasFinanceStoreConfigAccountCreateOfflineRefundAuth}
                    onClick={() => showOfflineRefundRef(rowData)} type="primary">
            线下退款
          </n-button>
        </n-space>
      );
    },
  },
]);

/** 参数 */
const model = ref({
  nickname: "",
});

/** 搜索 */
const handlerSearch = () => {
  tableSearch();
};

/** 表格刷新 */
function refresh() {
  tableSearch();
}

const getParams = () => {
  return model.value;
};

/* 表格搜索 */
const tableSearch = () => {
  pageTableData(getParams(), paginationRef.value);
};

const showCreateUserAccountRef = () => {
  let params = {
    refreshTable: refresh,
  };
  createUserAccountRef.value.acceptParams(params);
};
const showOfflineRefundRef = (row) => {
  let params = {
    row,
    refreshTable: refresh,
  };
  offlineRefundRef.value.acceptParams(params);
};
const showOfflineRechargeRef = (row) => {
  let params = {
    row,
    refreshTable: refresh,
  };
  OfflineRechargeRef.value.acceptParams(params);
};

/** 组件挂载 */
onMounted(() => {
  tableSearch();
});
</script>

<style lang="less" scoped>
@import "@/styles/defaultVar.less";
</style>
