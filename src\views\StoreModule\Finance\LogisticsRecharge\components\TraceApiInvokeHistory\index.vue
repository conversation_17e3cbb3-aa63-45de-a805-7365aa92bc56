<template>
  <div class="inner-page-height">
    <FormLayout :isLoading="isLoading" :tableData="tableData" :tableColumns="tableColumns" :pagination="paginationRef"
      :isTableSelection="false" @paginationChange="paginationChange">
      <!-- 操作项 -->
      <template #tableHeaderBtn>
        <n-button @click="refresh" class="store-button">刷 新</n-button>
      </template>
    </FormLayout>
  </div>
</template>

<script lang="tsx" setup name="TraceApiInvokeHistory">
import { ref, onMounted } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { getTrackCaller } from "@/services/api";

/** 表格方法Hook */
const {
  isLoading,
  tableData,
  pageTableData,
  paginationRef,
  paginationChange,
  refreshTableData,
} = useTableDefault({
  pageDataRequest: getTrackCaller,
});

/* 表格列表项 */
const tableColumns = ref([
  {
    title: "订单编号",
    key: "orderCode",
    width: 150,
    align: "left",
    render: (row) => {
      return row.orderCode ?? "-";
    },
  },
  {
    title: "快递单号",
    key: "trackingNo",
    width: 180,
    align: "left",
    render: row => {
      return <table-tooltip row={row} nameKey="trackingNo" idKey="trackingNo" />;
    }
  },
  {
    title: "快递公司",
    key: "shipCompanyName",
    width: 150,
    align: "left",
    render: (row) => {
      return row.shipCompanyName ?? "-";
    },
  },
  {
    title: "快递调用平台方名字",
    key: "platformName",
    width: 150,
    align: "left",
    render: (row) => {
      return row.platformName ?? "-";
    },
  },
  {
    title: "首次调用时间",
    key: "createTime",
    width: 150,
    align: "left",
    render: (row) => {
      return row.createTime ?? "-";
    },
  },
]);

/** 表格刷新 */
function refresh() {
  tableSearch();
}

/* 表格搜索 */
const tableSearch = () => {
  pageTableData({}, paginationRef.value);
};

/** 组件挂载 */
onMounted(() => {
  tableSearch();
});
</script>

<style scoped lang="less"></style>
