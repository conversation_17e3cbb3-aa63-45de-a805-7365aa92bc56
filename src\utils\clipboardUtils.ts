export function initClipboardCopyDefaultEvent(){
    // 禁止复制时自动添加的制表符
    document.addEventListener('copy', (e:ClipboardEvent) => {
        let clipboardData = e.clipboardData;
        if(!clipboardData) return;
        let selectionText = window.getSelection().toString();
        if( selectionText ){
            e.preventDefault();
            clipboardData.setData('text/plain', selectionText.trim().replace('\t',''));
        }
    })
}

export async function copyText(text:string){
    const isCanUseClipboard = navigator.clipboard ? true : false;
    if(isCanUseClipboard){
        try {
            await navigator.clipboard.writeText(text);
            return true
        } 
        catch (e) {
            return false
        }
    }
    else{
        const input = document.createElement('input');
        input.style.position = 'absolute';
        input.style.left = '-99999px';
        input.style.bottom = '-99999px';
        input.value = text;
        document.body.appendChild(input);
        input.focus();
        input.select();
        document.execCommand('Copy');
        document.body.removeChild(input);
        return true
    }
}