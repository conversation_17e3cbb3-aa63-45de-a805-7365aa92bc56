<template>
  <JModal
    v-model:show="show"
    width="680"
    title="编辑组织"
    positiveText="确定"
    @after-leave="closeModal"
    @positive-click="_save"
    :positiveButtonProps="{
      loading: isLoading,
    }"
  >
    <n-form
      ref="formRef"
      :rules="rules"
      :model="model"
      label-width="100"
      label-placement="left"
      require-mark-placement="right-hanging"
      :style="{
        width: '100%',
        maxHeight: '80vh',
        overflowY: 'auto',
      }"
    >
      <n-grid cols="12">
        <n-form-item-gi required label="组织名称" span="12" path="name">
          <n-input
            v-model:value="model.name"
            @blur="model.name = $event.target.value.trim()"
            :show-button="false"
            maxlength="30"
            placeholder="请输入名称"
            clearable
            show-count
          />
        </n-form-item-gi>
        <n-form-item-gi label="部门类型" span="12">
          <n-select
            v-model:value="model.departmentType"
            :options="DepartmentTypeOptions"
            :disabled="model.title === '编辑组织'"
          />
        </n-form-item-gi>
        <n-form-item-gi label="组织状态" span="12">
          <n-radio-group
            v-model:value="model.storeEntityVO.storeStatus"
            name="storeStatus"
            @click.prevent="handleRadioClick"
          >
            <n-radio v-for="song in storeStatusRadio" :key="song.value" :value="song.value" style="padding-right: 10px">
              {{ song.label }}
            </n-radio>
          </n-radio-group>
        </n-form-item-gi>
        <n-form-item-gi label="门店头像" span="12">
          <div style="width: 100%">
            <CustomizeUpload
              v-model:value="model.storeEntityVO.storeAvatar"
              :imageUploadType="['image/jpg', 'image/jpeg', 'image/gif', 'image/png']"
              enableTypeValidation
              accept="image/jpg,image/jpeg,image/gif,image/png"
              :fileListSize="1"
              :max="1"
            />
            <div style="width: 98%; margin-top: 12px">图片需小于1M，支持png、jpg、JPEG、GIF格式</div>
          </div>
        </n-form-item-gi>
        <n-form-item-gi label="门店联系人" span="12">
          <n-input
            v-model:value="model.storeEntityVO.contactName"
            @blur="model.storeEntityVO.contactName = $event.target.value.trim()"
            :show-button="false"
            maxlength="30"
            placeholder="请输门店联系人名称"
            clearable
            show-count
          />
        </n-form-item-gi>
        <n-form-item-gi label="联系电话" span="12">
          <n-input
            v-model:value="model.storeEntityVO.contactPhone"
            @blur="blurMobilePhone"
            :show-button="false"
            maxlength="30"
            placeholder="请输门店联系电话"
          />
        </n-form-item-gi>
        <n-form-item-gi label="营业时间" span="12">
          <n-input
            v-model:value="model.storeEntityVO.businessHours"
            @blur="model.storeEntityVO.businessHours = $event.target.value.trim()"
            :show-button="false"
            maxlength="50"
            placeholder="请输门店营业时间"
          />
        </n-form-item-gi>
        <n-form-item-gi label="地址" span="12">
          <JAreaSelect v-model:value="model.storeEntityVO.addressOptions" />
        </n-form-item-gi>
        <n-form-item-gi label="详细地址" span="12">
          <n-input
            v-model:value="model.storeEntityVO.addressDetail"
            @blur="model.storeEntityVO.addressDetail = $event.target.value.trim()"
            :show-button="false"
            maxlength="30"
            placeholder="请输详细地址"
          />
        </n-form-item-gi>
        <n-form-item-gi label="绑定店长" span="12">
          <JSelectContact v-model:value="model.storeEntityVO.managerId"></JSelectContact>
        </n-form-item-gi>
        <!-- 提示 -->
        <n-gi :span="8" style="margin-left: 100px; margin-bottom: 12px">
          <span style="color: #999">设置新店长后，原来的店长身份会自动变更为店员</span>
        </n-gi>
      </n-grid>
    </n-form>
  </JModal>
</template>

<script setup lang="ts" name="EditOrganizeModal">
import { ref, watch } from "vue";
import { useMessages } from "@/hooks";
import { useDialog, type DropdownOption } from "naive-ui";
import type { organizeForm } from "@/views/Distribution/OrganizationalStructure/hooks/type";
import Organize from "@/views/Distribution/OrganizationalStructure/hooks/organize";
import { DepartmentStatus, DepartmentType, MemberType } from "@/enums";
import JAreaSelect from "@/components/JAreaSelect/index.vue";
import JSelectContact from "@/components/JSelect/JSelectContact.vue";
import { getStoreDesc } from "@/services/api";
import useSubAccountPayeeManagement from "@/views/StoreModule/Finance/AccountManagement/components/SubAccountPayeeManagement/hooks/useSubAccountPayeeManagement";
const { numberVerification, validateBankCardNo } = useSubAccountPayeeManagement();
const { DepartmentType_options, storeStatusRadio, organizeFormObj, assignmentObj } = Organize();
export type AddOrEdit = "add" | "edit";
export interface AddCompanyModalProps {
  rowData: any;
}

/** 初始化参数 */
const model = ref<organizeForm>(JSON.parse(JSON.stringify(organizeFormObj)) as organizeForm);

const managerId = ref<string>("");
/* 提示信息 */
const message = useMessages();
// 店员列表
const storeStaffRelationList = ref([]);
/* 模态框显隐状态 */
const show = ref(false);
/* 父组件传过来的参数 */
const parameter = ref<AddCompanyModalProps>({} as AddCompanyModalProps);
const DepartmentTypeOptions = ref([...DepartmentType_options]);
const acceptParams = async params => {
  // managerId.value = "";
  // parameter.value = params;
  // storeStaffRelationList.value = [];

  // model.value.name = params.rowData.name;
  // model.value.departmentType = parameter.value.rowData?.departmentType || 0;
  // if (parameter.value.rowData.departmentType == DepartmentType.STORE) {
  //   try {
  //     var data = await getStoreDesc(params.rowData.id);
  //     model.value = assignmentObj(data);
  //     managerId.value = model.value.storeEntityVO.managerId || "";
  //     storeStaffRelationList.value = model.value.storeEntityVO.storeStaffRelationList;
  //   } catch (err) {
  //     message.createMessageError(err);
  //   }
  // }
  show.value = true;
};

/* 表单规则 */
const rules = {
  name: {
    required: true,
    trigger: ["blur", "change"],
    message: "请输入组织名称",
  },
};
/* 表单实例 */
const formRef = ref(null);
/* 清空表单 */
const formDataReset = () => {
  model.value = JSON.parse(JSON.stringify(organizeFormObj)) as organizeForm;
  DepartmentTypeOptions.value = [...DepartmentType_options];
};

/* 关闭弹窗 */
const closeModal = () => {
  formDataReset();
};

/** 判断输入的手机号码是否正确 */
const blurMobilePhone = () => {
  if (!numberVerification(model.value.storeEntityVO.contactPhone) && model.value.storeEntityVO.contactPhone != null) {
    model.value.storeEntityVO.contactPhone = null;
  }
};

/* 确认--保存 */
const isLoading = ref(false);
const _save = async (e: MouseEvent) => {
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      // model.value.storeEntityVO = {
      //   ...model.value.storeEntityVO,
      //   province: model.value.storeEntityVO.addressOptions.province,
      //   provinceId: model.value.storeEntityVO.addressOptions.provinceId,
      //   city: model.value.storeEntityVO.addressOptions.cityName,
      //   cityId: model.value.storeEntityVO.addressOptions.cityId,
      //   area: model.value.storeEntityVO.addressOptions.area,
      //   districtId: model.value.storeEntityVO.addressOptions.areaId,
      //   storeAvatar: Array.isArray(model.value.storeEntityVO.storeAvatar)
      //     ? model.value.storeEntityVO.storeAvatar[0]
      //     : model.value.storeEntityVO.storeAvatar,
      //   storeName: model.value.name,
      // };
      // model.value.useStatus = model.value.storeEntityVO.storeStatus;
      // if (managerId.value) {
      //   delete model.value.storeEntityVO.storeStaffRelationList;
      // } else {
      //   if (model.value.storeEntityVO.managerId) {
      //     model.value.storeEntityVO.storeStaffRelationList = [
      //       ...storeStaffRelationList.value,
      //       {
      //         memberType: 1,
      //         customerId: model.value.storeEntityVO.managerId,
      //         memberId: model.value.storeEntityVO.managerId,
      //       },
      //     ];
      //   } else {
      //     delete model.value.storeEntityVO.storeStaffRelationList;
      //   }
      // }
      // const params = {
      //   data: {
      //     parentCode: parameter.value.rowData.parentCode,
      //     level: parameter.value.rowData.level + 1,
      //     code: parameter.value.rowData.code,
      //     id: parameter.value.rowData.id,
      //     ...model.value,
      //   },
      // };
      // isLoading.value = true;
      // console.log(params);
      // try {
      //   const res = await parameter.value.api(params);
      //   parameter.value.updataTable(params.data, parameter.value.rowData, model.value.title);
      //   model.value.name = "";
      //   show.value = false;
      //   formDataReset();
      // } catch (err) {
      //   message.createMessageError(err);
      // } finally {
      //   isLoading.value = false;
      // }
    }
  });
};
/** 确认框 */
const dialog = useDialog();
/** 点击radio */
const handleRadioClick = e => {
  console.log(e.target.value);
  if (model.value.title === "编辑组织") {
    dialog.warning({
      title: "确定修改门店状态",
      positiveText: "确定",
      negativeText: "取消",
      onPositiveClick: () => {
        model.value.storeEntityVO.storeStatus = Number(e.target.value);
      },
    });
  } else {
    model.value.storeEntityVO.storeStatus = Number(e.target.value);
  }
};
watch(
  () => model.value.storeEntityVO.storeAvatar,
  newVal => {
    if (newVal[0] == "") model.value.storeEntityVO.storeAvatar = "";
  },
);
defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less"></style>
