<!-- 开户行行号 -->
<template>
    <JSelect
      :value="props.value"
      :loading="isLoading"
      :onFocus="handlerFocus"
      :options="doctorList"
      :onClear="handleClear"
      @update:value="onChange"
      placeholder="请选择开户行行号"
      :disabled="props.disabled"
    />
  </template>
  <script setup lang="ts">
  import { ref, watch } from "vue";
  import JSelect from "@/components/JSelect/index.vue";
  import { bankInfoList } from "@/services/api";
  import { useMessages } from "@/hooks";
  
  /* Props */
  const props = withDefaults(
    defineProps<{
      isImmediately?: boolean; // 是否立即加载
      value: Array<string | number> | string | number | null; // 选择的值
      parentId?:string | number,
      provinceCode?:string | number,
      urbanCode?:string | number,
      disabled?:boolean,
      payPlatForm?:string | number
      accountOpeningNameLoading?:boolean
      /** 是否为绑卡 */
      isBindCardcard?:boolean
    }>(),
    {
      isImmediately: false,
      disabled:false,
      isBindCardcard:false
    },
  );
  
  const emits = defineEmits<{
    (e: "update:value", selectValue: any): void; // 更新选择值事件
    (e: "update:bankNo",bankNoValue: any): void; // 更新开户行行号
    (e: "update:label",labelValue: any): void; // 更新开户名称
    (e: "update:accountOpeningNameLoading",accountOpeningNameLoading: any): void; // 开户名称请求Loading状态
  }>();
  
  /* 提示 */
  const message = useMessages();
  
  /* 是否加载 */
  const isLoading = ref(false);
  const doctorList = ref([]); // 列表
  
  /* 筛选、转化{label: '', value: ''} */
  function handleData(filterData: BankModule.BankEntity[]) {
    let dataList = [];
    dataList = filterData.map(item => {
      return { 
          label: item.name, 
          value: item.id,
          no:item.no
        };
      });
    return dataList;
  }
  
  /* 获取开户行行号列表 */
  async function getDoctorList() {
    const param = { 
      data:{
        parentId:props.parentId,
        provinceId:props.provinceCode,
        cityId:props.urbanCode,
        payPlatForm:props.payPlatForm
      }
    }
    try {
      isLoading.value = true;
      const data = await bankInfoList(param);
      handleData(data).forEach(item => {
          doctorList.value.push(item);
      });
      if(props.isBindCardcard){
        console.log('doctorList',doctorList.value);
        emits("update:value", doctorList.value[0].value);
        emits("update:bankNo", doctorList.value[0].no);
        emits("update:label", doctorList.value[0].label);
      }
    } catch (error) {
      message.createMessageError("获取开户行行号信息失败：" + error);
    } finally {
      isLoading.value = false;
    }
  }
  
  /** 选择值改变事件处理函数 */
  function onChange(value,text) {
    emits("update:value", value);
    emits("update:bankNo", text.no);
    emits("update:label", text.label);
  }
  
  
  /** 清空事件处理函数 */
  const handleClear = () => {
    emits("update:value", null);
    emits("update:bankNo", null);
    emits("update:label", null);
  };
  /** 聚焦事件处理函数 */
  function handlerFocus() {
    // 如果开户行列表为空
    if (!doctorList.value.length && !isLoading.value) {
      getDoctorList();
    }
  }
  
  /** 监听 */
  watch(
    () => props.isImmediately,
    newVal => {
      if (newVal) {
        getDoctorList();
      }
    },
    { immediate: true },
  );
  /** 监听 */
  watch(
    () => [
      props.parentId,
      props.urbanCode,
      props.provinceCode,
    ],
    newVal => {
        const allNull = newVal.every(item => item === null);
        if(!allNull){
          doctorList.value.length = 0
          getDoctorList();
        }
    },
  );

  /** 监听 支付渠道*/
  watch(
    () => 
      props.payPlatForm,
      newVal => {
          doctorList.value.length = 0
      },
  );

  watch(
    () => 
      isLoading.value,
      newVal => {
        emits("update:accountOpeningNameLoading",newVal)
      },
  );

  </script>
    
  <style scoped lang="less"></style>