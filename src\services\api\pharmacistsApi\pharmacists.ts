import {defHttp} from '@/services';

export const enum PharmacistsApi {
    pharmacistEntityPage = '/pharmacistEntity/page',
    pharmacistEntityPageUnauthorized = '/pharmacistEntity/pagePharmacists',
    pharmacistEntityAdd = '/pharmacistEntity/add',
    pharmacistEntityUpdate = '/pharmacistEntity/update',
    pharmacistEntityGet = '/pharmacistEntity/get'
}


//分页查询列表
export function pharmacistEntityPage(params) {
  return defHttp.post({
    url: PharmacistsApi.pharmacistEntityPage,
    params
  });
}
//分页查询列表(不鉴权)
export function pharmacistEntityPageUnauthorized(params) {
  return defHttp.post({
    url: PharmacistsApi.pharmacistEntityPageUnauthorized,
    params
  });
}

//新增药师
export function pharmacistEntityAdd(params) {
  return defHttp.post({
    url: PharmacistsApi.pharmacistEntityAdd,
    params
  });
}

//编辑药师
export function pharmacistEntityUpdate(params) {
  return defHttp.put({
    url: PharmacistsApi.pharmacistEntityUpdate,
    params
  });
}

//查询药师
export function pharmacistEntityGet(id) {
  return defHttp.get({
    url: PharmacistsApi.pharmacistEntityGet + '?id=' + id,
  });
}
