<template>
  <div class="wrapper">
    <div class="left">
      <div class="header">
        <JSearchInput
          v-model:value="searchValue"
          placeholder="请输入群管id、群管名称或微信昵称"
          @search="handleSearch"
          size="small"
          width="100%"
        />
      </div>
      <n-spin :show="loading" size="small" style="height: calc(100% - 52px);">
        <div class="group-tube-wrapper" @scroll="handleScroll">
          <transition appear name="fade" mode="out-in">
            <template v-if="selectDataList.length > 0">
              <n-checkbox-group v-model:value="checkValues" @update:value="handleUpdateValue">
                <n-space item-style="display: flex;" vertical>
                  <n-checkbox class="group-tube-item" v-for="item in selectDataList" :value="item.id" :key="item.id">
                    <template #default>
                      <CustomOptions :infoItem="item" />
                    </template>
                  </n-checkbox>
                </n-space>
              </n-checkbox-group>
            </template>
            <template v-else>
              <JEmpty />
            </template>
          </transition>
        </div>
      </n-spin>
    </div>
    <div class="right">
      <div class="header">
        <span>
          已选择
          <span class="num">{{ checkValues?.length ?? 0 }}</span>
          个群管
        </span>
        <!-- 清空 -->
        <JTextButton type="primary" size="small" @click="handleRemoveAll">清空</JTextButton>
      </div>
      <!-- 选中群管 -->
      <div class="group-tube-selected">
        <transition appear name="fade" mode="out-in">
          <template v-if="checkOptions.length > 0">
            <n-flex vertical :size="0">
              <CustomOptions v-for="item in checkOptions" :infoItem="item" :key="item.id" />
            </n-flex>
          </template>
          <template v-else>
            <JEmpty title="请选择群管" />
          </template>
        </transition>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, effectScope, watch, onScopeDispose } from "vue";
import { useGetSgGmInfo } from "../hooks";
import { SystemSetting } from "@/settings/systemSetting";
import { isArray } from "@/utils";
/** 相关组件 */
import CustomOptions from "./CustomOptions.vue";
import JEmpty from "./JEmpty.vue";

defineOptions({ name: 'JTubeTransfer' });

/** props */
const props = withDefaults(defineProps<{
    dealerIds?: string[] | null;
    gmIds?: string[] | null;
    checkGmOptions?: Array<Partial<{
        id: string;
        img: string;
        nickname: string;
    }>>;
}>(), {
    dealerIds: null,
    gmIds: null,
    checkGmOptions: () => []
});

/** emits */
const emits = defineEmits<{
    (e: 'update:gmIds', gmIds: string[]): void;
}>();

/** 群管数据 */
const {
    loading,
    _params,
    searchValue,
    selectDataList,
    getSgGmInfoList,
    handleScroll
} = useGetSgGmInfo();

/** 选中值 */
const checkValues = ref(null);
/** 选中项 */
const checkOptions = ref([]);

/** 选项组的值改变时的回调 */
const handleUpdateValue = (value: (string | number)[], meta: { actionType: 'check' | 'uncheck', value: string | number }) => {
    const selectedOption = selectDataList.value.find(option => option.id === meta.value);
    // 避免后续不必要的操作
    if (!selectedOption) return;
    if (meta.actionType === 'check') {
        checkOptions.value.push(selectedOption);
    } else if (meta.actionType === 'uncheck') {
        // 如果是 uncheck，删除对应选项
        checkOptions.value = checkOptions.value.filter(option => option.id !== meta.value);
    }

    // 更新 gmIds
    const gmIds = checkOptions.value.map(option => option.id);
    emits('update:gmIds', gmIds);
};

/** 搜索 */
const handleSearch = () => {
    // 清空当前页码
    _params.pageVO.current = 1;
    _params.pageVO.size = SystemSetting.pagination.pageSize;
    getSgGmInfoList();
}

/** 清空 */
function handleRemoveAll() {
    checkValues.value = null;
    checkOptions.value = [];

    emits('update:gmIds', null);
};

/** 初始化 */
function init() {
    getSgGmInfoList();
}
init();

/** 创建effectScope */
const scope = effectScope();

/** 作用域内运行一组副作用 */
scope.run(() => {
    // 监听
    watch(() => props.dealerIds, (newVal) => {
        if (newVal) {
            if (isArray(newVal)) {
                _params.data.dealerIds = newVal.join(',');
                getSgGmInfoList();
            }
        }
    });

    // 监听
    watch(() => props.gmIds, (newVal) => {
        if (newVal) {
            if (isArray(newVal)) {
                checkValues.value = newVal;
            }
        }
    });

    // 监听
    watch(() => props.checkGmOptions, (newVal) => {
        if (newVal) {
            if (isArray(newVal)) {
                checkOptions.value = newVal;
            }
        }
    });
});

/** 作用域销毁时，停止作用域 */
onScopeDispose(() => {
    scope.stop();
});
</script>

<style lang="less" scoped>
@import "@/styles/scrollbar.less";
@import "@/styles/default.less";

.wrapper {
    width: 100%;
    height: 288px;
    display: flex;
    border: 1px solid #eee;

    .left {
        width: 50%;
        height: 100%;
        border-right: 1px solid #EEEEEE;
        box-sizing: border-box;

        :deep(.n-spin-content) {
            height: 100%;
        }

        .header {
            height: 28px;
            padding: 8px 12px;
        }

        .group-tube-wrapper {
            height: 100%;
            overflow-y: auto;
            padding-left: 12px;
            padding-bottom: 8px;
            .scrollbar();

            .group-tube-item {
                width: 100%;
                display: flex;
                align-items: center;
                padding-left: 8px;
                border-radius: 2px;

                &:hover {
                    background-color: rgb(231, 241, 255);
                }
            }
        }
    }

    .right {
        width: 50%;
        height: 100%;
        box-sizing: border-box;

        .header {
            height: 28px;
            padding: 8px 12px;
            line-height: 28px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .num {
                font-weight: 600;
                font-size: 18px;
                color: @primary-color;
            }
        }

        .group-tube-selected {
            height: calc(100% - 52px);
            overflow-y: auto;
            padding-left: 12px;
            padding-bottom: 8px;
            .scrollbar();
        }
    }
}

:deep(.n-tree.n-tree--block-line .n-tree-node:not(.n-tree-node--disabled).n-tree-node--selected) {
    background-color: #fff;
}
</style>
