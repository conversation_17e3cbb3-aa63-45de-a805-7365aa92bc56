import { defHttp } from "@/services";

/** 经销商统计 */
export const enum CourseReportsApi {
  courseStatData = "/report/courseStatData",
  courseStatDataExport = "/report/courseStatDataExport",
}

/** 获取经销商统计 */
export function getcourseStatData(params) {
  return defHttp.post({
    url: CourseReportsApi.courseStatData,
    params,
  });
}

/** 经销商统计导出 */
export function courseStatDataExport(params) {
  return defHttp.post({
    url: CourseReportsApi.courseStatDataExport,
    requestConfig: {
      responeseType: "stream",
    },
    params,
  });
}
