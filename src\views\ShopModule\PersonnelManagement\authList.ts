import { PersonnelManagementAuth } from "@/enums/authKeys";
import { hasAuth } from "@/utils/auth/authUtils";

/** 修改角色 */
export const hasEditRoleAuth = (function () {
  return hasAuth(PersonnelManagementAuth.EditRole.key);
})();

/** 修改归属门店 */
export const hasEditShopAuth = (function () {
  return hasAuth(PersonnelManagementAuth.EditShop.key);
})();

/** 一键转会员 */
export const hasChangeMembershipAuth = (function () {
  return hasAuth(PersonnelManagementAuth.ChangeMembership.key);
})();
