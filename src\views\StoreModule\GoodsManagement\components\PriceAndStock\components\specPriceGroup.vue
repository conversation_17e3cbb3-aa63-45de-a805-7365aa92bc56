<template>
  <div class="spec-price-group">
    <NDataTable
      v-if="tableData.length > 0"
      :single-line="false"
      :columns="dynamicColumns"
      :data="tableData"
      :pagination="false"
      size="small"
    />
    <div class="spec-price-group__empty" v-else>请先设置商品规格</div>
    <!-- 商品活动价设置 -->
    <JActivePrice ref="activePriceRef" @update:activityPrice="handleActivityPrice" />
    <!-- 付款方式设置 -->
    <PaymentMethod v-model:show="show" v-model:value="model" :price="price" @save-successfully="handleSave" />
  </div>
</template>

<script setup lang="tsx">
import { ref, computed, watch, toRaw, nextTick, h } from 'vue'
import { NInputNumber, NInput } from 'naive-ui'
import type { SpecList, SpecPriceItem } from '../types'
import { MAXPOINT, MINPOINT } from "../../../types/index";
import { useBoolean, useMessages } from '@/hooks';
const { createMessageError } = useMessages();
import { isArray } from "@/utils";
import useGenerateCombinations  from "@/views/StoreModule/GoodsManagement/hooks/useGenerateCombinations";
const { generateCombinations } = useGenerateCombinations();

import JActivePrice from "../../JActivePrice.vue";
import type { ActivityPriceVO } from "../../JActivePrice.vue";
import InventoryDetail from "../../InventoryDetail.vue";
import PaymentMethod from "../../PaymentMethod.vue";

// 生成UUID的简单函数
const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c == 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

const props = defineProps<{
  specList: SpecList[]
  specPriceList?: SpecPriceItem[];
  isVirtual?: boolean;
}>()


const emits = defineEmits<{
  'update:modelValue': [value: SpecPriceItem[]]
}>()

// 计算rowSpan值
const calculateRowSpan = (specIndex: number, specList: SpecList[]) => {
  let rowSpan = 1
  for (let i = specIndex + 1; i < specList.length; i++) {
    // 修改：不过滤空值，计算所有规格值的数量
    rowSpan *= specList[i].specValue.length
  }
  return rowSpan
}


// 生成规格组合
const specCombinations = computed((): SpecPriceItem[] => {
  if (!props.specList?.length) return []

  const combinations = generateCombinations(props.specList)
  return combinations.map((combo, index) => {
    const name = combo.map(item => item.attributeValue).join(';')
    const comboIds = combo.map(item => item.id).join('-')

    const existingItem = props.specPriceList?.find(item => {
        const productSpecIds = [item.firstAttrId, item.secondAttrId, item.thirdAttrId].filter(Boolean)
        const comboIds = combo.map(item => item.id)

        return productSpecIds.sort().join('-') === comboIds.sort().join('-')
    })

    const frontendId = existingItem?.id || `${generateUUID()}-${index}`

    return {
      frontendId,
      name,
      productId: existingItem?.productId || null,
      pointConfigId: existingItem?.pointConfigId || null,
      id: existingItem?.id || null,
      specCombination: combo.map(item => item.attributeValue),
      specCombinationIds: comboIds,
      firstAttrId: combo[0]?.id || null,
      secondAttrId: combo[1]?.id || null,
      thirdAttrId: combo[2]?.id || null,
      price: existingItem?.price || null,
      costPrice: existingItem?.costPrice || null,
      availStocks: existingItem?.availStocks || null,
      marketPrice: existingItem?.marketPrice || null,
      lockedStocks: existingItem?.lockedStocks || 0,
      upper: existingItem?.upper || 999,
      initSaled: initSaledCache.value || existingItem?.initSaled || 0,
      sku: existingItem?.sku || null,
      activityPriceVOList: existingItem?.activityPriceVOList || [],
      isDownPayment: existingItem?.isDownPayment || 0,
      isCashOnDelivery: existingItem?.isCashOnDelivery || 0,
      downPayment: existingItem?.downPayment || null,
      points: existingItem?.points || null,
      dealerCommission: existingItem?.dealerCommission || null,
      sourceId: existingItem?.sourceId || null,
      pointIsDeleted: existingItem?.pointIsDeleted || null,
    }
  })
})

// 处理表格数据，添加rowSpan和显示标记
const tableData = computed(() => {
  if (!specCombinations.value.length || !props.specList.length) return []

  const data = specCombinations.value.map((item, index) => {
    const showSpec: boolean[] = []
    const rowSpan: number[] = []

    for (let specIndex = 0; specIndex < props.specList.length; specIndex++) {
      const rowSpanValue = calculateRowSpan(specIndex, props.specList)
      const shouldShow = index % rowSpanValue === 0

      showSpec.push(shouldShow)
      rowSpan.push(shouldShow ? rowSpanValue : 0)
    }

    return {
      ...item,
      showSpec,
      rowSpan
    }
  })

  return data
})/** 缓存本次设置初始已售值 */
const initSaledCache = ref<number>(0);
// 更新值
const handleUpdateValue = (frontendId: string, key: keyof SpecPriceItem, value: any) => {

  const updatedData = specCombinations.value.map(item => {
    if(key == 'initSaled') {
        initSaledCache.value = value;
        return { ...item, [key]: value }
    }
    if (item.frontendId === frontendId) {
      return { ...item, [key]: value }
    }
    return item
  })
  emits('update:modelValue', updatedData)
}

// 库存列所需
const { bool: showPopover, setFalse, setTrue } = useBoolean();
const currentFocusId = ref(null);
function handleFocus(id: string) {
    currentFocusId.value = id;
    setTrue();
}
function handleBlur() {
    currentFocusId.value = null;
    setFalse();
}

// 活动价列所需
/** 打开活动价设置 */
const activePriceRef = ref<InstanceType<typeof JActivePrice> | null>(null);
const openJActivePrice = (row: Partial<SpecPriceItem>) => {
    if (!row.price) {
        createMessageError("请先输入售价！");
        return;
    }
    row.id = row.id || row.frontendId
    activePriceRef.value?.acceptParams({
        row
    });
};
/** 活动价设置回调 */
function handleActivityPrice(id: string, activityPriceList: ActivityPriceVO[]) {

    // 使用统一的更新方法，保证数据流一致性
    const updatedData = specCombinations.value.map(item => {
        if (item.id === id || item.frontendId === id) {
            return {
                ...item,
                activityPriceVOList: [...activityPriceList] // 创建新数组避免引用问题
            }
        }
        return item
    })

    emits('update:modelValue', updatedData)
}

// 付款方式相关
/** 付款方式展示 */
const show = ref(false);
/** 售价 */
const price = ref(null);

/** 表单参数 */
const model = ref<{
  id: string;
  isDownPayment: 0 | 1;
  isCashOnDelivery: 0 | 1;
  downPayment: number;
}>({
  id: null,
  isDownPayment: 0,
  isCashOnDelivery: 0,
  downPayment: null,
});

/** 设置付款方式 */
const handleSetPaymentMethod = (row: Partial<{
    id: string;
    price: number; // 售价
    isDownPayment: 0 | 1; // 是否支持定金支付。0=否；1=是
    isCashOnDelivery: 0 | 1; // 是否支持物流代收。0=否；1=是
    downPayment: number; // 定金单价，单位分
}>) => {
    if (!row?.price) {
        createMessageError("请先输入售价！");
        return;
    }
    // 判断是否是虚拟商品
    if (props.isVirtual) {
        createMessageError("虚拟商品只能线上付款，不支持设置付款方式!");
        return;
    }
    price.value = Number(row.price);
    model.value.id = row.id;
    model.value.isCashOnDelivery = row.isCashOnDelivery;
    model.value.isDownPayment = row.isDownPayment;
    model.value.downPayment = row.downPayment;
    show.value = true;
};

/** 付款方式保存成功回调 */
function handleSave() {
  const updatedData = specCombinations.value.map(item => {

      return {
        ...item,
        isDownPayment: model.value.isDownPayment,
        isCashOnDelivery: model.value.isCashOnDelivery,
        downPayment: model.value.downPayment,
      }
  })
  emits('update:modelValue', updatedData)
  show.value = false;
}

// 动态生成表格列
const dynamicColumns = computed(() => {
  const columns = []

  // 添加规格列(动态列)
  props.specList.forEach((spec, specIndex) => {
    columns.push({
      title: spec.attributeName,
      key: `spec_${specIndex}`,
      width: 100,
      fixed: 'left',
      rowSpan: (row, rowIndex) => {
        // 修改：确保 rowSpan 计算正确
        return (row.showSpec && row.showSpec[specIndex]) ? row.rowSpan[specIndex] : 0
      },
      render: (row: any) => {
        // 显示规格值，空值显示为 "-"
        return row.specCombination[specIndex] || '-'
      }
    })
  })

  // 添加价格库存列(固定列)
  columns.push(
    {
      title(column){
        return h('div', ['售价(元)', h('span', {style:{color:'red'}},'*')])
      },
      key: 'price',
      width: 120,
      render: (row: any) => (
        <NInputNumber
          value={row.price}
          onUpdateValue={(value) => handleUpdateValue(row.frontendId, 'price', value)}
          placeholder="请输入售价"
          min={0}
          max={999999}
          precision={2}
          showButton={false}
          style="width: 100%;"
        />
      )
    },
    {
      title(){
        return h('div', ['库存', h('span', {style:{color:'red'}},'*')])
      },
      key: 'availStocks',
      resizable: true,
      width: 100,
      render: (row) => {
        return (
          <div style="width: 100%; display: flex;">
            <n-popover trigger="hover" raw show={showPopover.value && currentFocusId.value === row.frontendId}>
              {{
                default: () => <InventoryDetail quantityInStock={{lockedStocks: row?.lockedStocks ?? 0, availStocks: row?.availStocks ?? 0 }} />,
                trigger: () => (
                  <n-input-number
                    value={row?.availStocks}
                    onUpdateValue={(value) => handleUpdateValue(row.frontendId, 'availStocks', value)}
                    onFocus={() => handleFocus(row.frontendId)}
                    onBlur={handleBlur}
                    placeholder="请输入库存"
                    min={0}
                    max={9999999}
                    show-button={false}
                    precision={0}
                    style="width: 100%;"
                  />
                )
              }}
            </n-popover>
          </div>
        );
      }
    },
    {
      title: '采购单价（元）',
      key: 'costPrice',
      resizable: true,
      minWidth: 120,
      render: (row) => {
        return <n-input-number
          precision={2}
          value={row?.costPrice}
          onUpdateValue={(value) => handleUpdateValue(row.frontendId, 'costPrice', value)}
          placeholder="请输入采购单价"
          show-button={false}
          min={0}
          max={999999}
        />
      }
    },
    {
      title: '划线价（元）',
      key: 'marketPrice',
      resizable: true,
      minWidth: 100,
      render: (row) => {
        return <n-input-number
          precision={2}
          value={row?.marketPrice}
          onUpdateValue={(value) => handleUpdateValue(row.frontendId, 'marketPrice', value)}
          placeholder="请输入划线价"
          show-button={false}
          min={0}
          max={999999}
        />
      }
    },
    {
      title: '活动价（元）',
      key: 'activePrice',
      width: 100,
      resizable: true,
      render: (row) => {
        let activityPrice = null;
        if (isArray(row['activityPriceVOList']) && row['activityPriceVOList'].length > 0) {
          activityPrice = (row['activityPriceVOList'][0]?.activityPrice / 100).toFixed(2);
        }
        return (
          <n-flex>
            {activityPrice ? <span>{ activityPrice }</span> : null }
            <n-button text type="primary" onClick={() => openJActivePrice(row)}>设置</n-button>
          </n-flex>
        );
      }
    },
    {
      title: '每订单上限',
      key: 'upper',
      width: 120,
      render: (row: any) => (
        <NInputNumber
          value={row.upper}
          onUpdateValue={(value) => handleUpdateValue(row.frontendId, 'upper', value)}
          placeholder="请输入上限"
          min={1}
          max={999999}
          precision={0}
          showButton={false}
          style="width: 100%;"
        />
      )
    },
    {
      title: '初始已售（前端显示）',
      key: 'initSaled',
      width: 120,
      rowSpan: (rowData, rowIndex) => tableData.value.length, // 合并所有行
      render: (row: any) => (
        <NInputNumber
          value={row.initSaled}
          onUpdateValue={(value) => handleUpdateValue(row.frontendId, 'initSaled', value)}
          placeholder="请输入已售"
          min={0}
          max={9999999}
          precision={0}
          showButton={false}
          style="width: 100%;"
        />
      )
    },
    {
      title: '可获得积分',
      key: 'points',
      resizable: true,
      minWidth: 100,
      render: (row) => {
        return <n-input-number
          value={row?.points}
          onUpdateValue={(value) => handleUpdateValue(row.frontendId, 'points', value)}
          placeholder="请输入可获得积分"
          show-button={false}
          precision={0}
          min={MINPOINT}
          max={MAXPOINT}
        />
      }
    },
    {
      title: '经销商分佣',
      key: 'dealerCommission',
      resizable: true,
      minWidth: 100,
      render: (row) => {
        return <n-space wrap={false} align="center" size={0} >
          <n-input-number
          value={row?.dealerCommission}
          onUpdateValue={(value) => handleUpdateValue(row.frontendId, 'dealerCommission', value)}
          placeholder="请输入分佣比例"
          show-button={false}
          precision={2}
          min={0}
          max={100}
        />%
        </n-space>
      }
    },
    {
      title: '编码',
      key: 'sku',
      width: 150,
      render: (row: any) => (
        <NInput
          value={row.sku}
          onUpdateValue={(value) => handleUpdateValue(row.frontendId, 'sku', value)}
          maxlength={40}
          placeholder="请输入编码"
          style="width: 100%;"
          allow-input={(value)=>!value || /^[a-zA-Z0-9]+$/.test(value)}
        />
      )
    },
    {
      title: '操作',
      key: 'operation',
      rowSpan: (rowData, rowIndex) => tableData.value.length, // 合并所有行
      width: 100,
      render: (row, index) => {
        return (
          <n-button text type="primary" onClick={() => handleSetPaymentMethod(row)}>付款方式设置</n-button>
        )
      },
    }
  )

  return columns
})


watch(() => props.specList, async (newVal, oldVal) => {
      emits('update:modelValue', specCombinations.value)

}, { deep: true })
</script>

<style scoped lang="less">
.spec-price-group {
  margin-top: 24px;

  &__title {
    margin-bottom: 16px;
    font-weight: 500;
    font-size: 16px;
    color: #333;
  }

  &__table {
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    overflow: hidden;
  }

  &__empty {
    padding: 40px;
    text-align: center;
    color: #999;
    background: #fafafa;
    border: 1px dashed #d9d9d9;
    border-radius: 8px;
  }
}
</style>
