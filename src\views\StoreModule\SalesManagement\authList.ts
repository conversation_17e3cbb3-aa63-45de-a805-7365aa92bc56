import { SalesManagementAuth } from "@/enums/authKeys";
import { hasAuth } from "@/utils/auth/authUtils";


/** 会员积分 */
export const hasMemberPointsPageAuth = function(){
    return hasAuth(SalesManagementAuth.salesManagementPoints.key);
}()

/** 会员积分之积分商城启用 */
export const hasPointsMallEnableAuth = function(){
    return hasAuth(SalesManagementAuth.salesManagementPointsMallenable.key);
}()

/** 会员积分之积分兑换商品配置 */
export const hasPointsProductConfigAuth = function(){
    return hasAuth(SalesManagementAuth.salesManagementPointsProductconfig.key);
}()

/** 会员积分之积分规则配置 */
export const hasPointsRuleconfigAuth = function(){
    return hasAuth(SalesManagementAuth.salesManagementPointsRuleconfig.key);
}()

/** 会员积分之会员等级配置 */
export const hasPointsLevelconfigAuth = function(){
    return hasAuth(SalesManagementAuth.salesManagementPointsLevelconfig.key);
}()
/** 会员积分之更多规则配置 */
export const hasMoreConfigurationsAuth = function(){
    return hasAuth(SalesManagementAuth.salesManagementMoreConfigurations.key);
}()
/** 福利券分类 */
export const hasWelfareClassifyPageAuth = function(){
    return hasAuth(SalesManagementAuth.salesManagementWelfareClassify.key);
}()

/** 福利券分类 - 新增 */
export const hasWelfareClassifyAddAuth = function(){
    return hasAuth(SalesManagementAuth.salesManagementWelfareClassifyAdd.key);
}()

/** 福利券分类 - 编辑 */
export const hasWelfareClassifyEditAuth = function(){
    return hasAuth(SalesManagementAuth.salesManagementWelfareClassifyEdit.key);
}()

/** 福利券分类 - 删除 */
export const hasWelfareClassifyDeleteAuth = function(){
    return hasAuth(SalesManagementAuth.salesManagementWelfareClassifyDelete.key);
}()

/** 福利券管理 */
export const hasWelfareManagementPageAuth = function(){
    return hasAuth(SalesManagementAuth.salesWelfareManagement.key);
}()

/** 福利券管理 - 领取记录 */
export const hasSelectRecord = function(){
    return hasAuth(SalesManagementAuth.selectRecord.key);
}()

/** 福利券管理 - 发放/停止发放 */
export const hasWelfareManagementSendAuth = function(){
    return hasAuth(SalesManagementAuth.salesWelfareManagementSend.key);
}()

/** 福利券管理 - 修改有效期 */
export const hasUpdateValidDateAuth = function(){
    return hasAuth(SalesManagementAuth.updateValidDate.key);
}()

/** 福利品管理 */
export const hasWelfareGoodsManagementPageAuth = function(){
    return hasAuth(SalesManagementAuth.salesWelfareGoods.key);
}()

/** 福利品管理 - 新建分类 */
export const hasWelfareGoodsClassifyAddAuth = function(){
    return hasAuth(SalesManagementAuth.salesWelfareGoodsClassifyAdd.key);
}()

/** 福利品管理 - 编辑分类 */
export const hasWelfareGoodsClassifyEditAuth = function(){
    return hasAuth(SalesManagementAuth.salesWelfareGoodsClassifyEdit.key);
}()

/** 福利品管理 - 删除分类 */
export const hasWelfareGoodsClassifyDeleteAuth = function(){
    return hasAuth(SalesManagementAuth.salesWelfareGoodsClassifyDelete.key);
}()

/** 福利品管理 - 新增 */
export const hasWelfareGoodsAddAuth = function(){
    return hasAuth(SalesManagementAuth.salesWelfareGoodsAdd.key);
}()

/** 福利品管理 - 编辑 */
export const hasWelfareGoodsEditAuth = function(){
    return hasAuth(SalesManagementAuth.salesWelfareGoodsEdit.key);
}()

/** 福利品管理 - 批量上下架*/
export const hasWelfareGoodsPublishAuth = function(){
    return hasAuth(SalesManagementAuth.salesWelfareGoodsPublish.key);
}()

