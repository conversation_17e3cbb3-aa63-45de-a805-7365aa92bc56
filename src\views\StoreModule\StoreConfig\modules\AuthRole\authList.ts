import { StoreBasicConfigAuth } from "@/enums/authKeys";
import { hasAuth } from "@/utils/auth/authUtils";


/** 新增角色 */
export const hasAddAuthRoleAuth = function(){
    return hasAuth(StoreBasicConfigAuth.storeBasicConfigRolesNewrole.key);
}()
/** 复制角色 */
export const hasCopyAuthRoleAuth = function(){
    return hasAuth(StoreBasicConfigAuth.storeBasicConfigRolesCopyrole.key);
}()
/** 编辑角色 */
export const hasEditAuthRoleAuth = function(){
    return hasAuth(StoreBasicConfigAuth.storeBasicConfigRolesEditrole.key);
}()
/** 删除角色 */
export const hasDeleteAuthRoleAuth = function(){
    return hasAuth(StoreBasicConfigAuth.storeBasicConfigRolesDeleterole.key);
}()

