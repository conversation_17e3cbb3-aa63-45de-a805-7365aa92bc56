<template>
      <div class="tab-header inner-page-height">
        <TabsLayout v-model:value="activeTab" :tabsData="tabsData" :onlyTabs="true" class="tabsLayout" @update:value="handleTabChange">
          <!-- 商品管理内容 -->
          <div v-if="activeTab === 'all'" class="goods-tab-content">
            <div class="goods-layout">
              <!-- 左侧分类树 -->
              <div class="classify-container ">
                <!-- header -->
                <div class="header">
                  <n-space justify="space-between" style="margin-bottom: 12px;flex-wrap: nowrap;">
                    <n-button size="small" :loading="isGetLoading" @click="refresh" class="store-button">刷 新</n-button>
                    <n-button v-if="hasAddGoodsClassAuth" size="small" type="primary" @click="openAddGoodsClassification('add')">新建分类</n-button>
                  </n-space>
                  <JSearchInput
                    v-model:value="searchValue"
                    @search="handleSearch"
                    width="100%"
                    size="small"
                    placeholder="请输入分类名称"
                    clearable
                  />
                </div>
                <!-- 商品分类 tree -->
                <div class="classification-tree-container">
                  <n-spin :show="isGetLoading" size="small" style="height: 100%;">
                    <n-scrollbar style="height: 100%;" @scroll="handleScroll">
                      <ClassificationTree
                        v-model:value="model.selectedValue"
                        :tree-data="treeData"
                        :menu-options="options"
                        @menu-select="handleMenuSelect"
                        @update:selected-keys="handleUpdateSelectKeys"
                        :selected-keys="selectedKeys"
                        :defaultExpandKeys="defaultExpandKeys"
                      />
                    </n-scrollbar>
                  </n-spin>
                </div>
              </div>
              
              <!-- 右侧商品列表 -->
              <div class="goods-container">
                <ProductList
                  :cate-id="model.cateId"
                  :type="model.type"
                  @successful="isGetLoading = false"
                  :name="props.goodsName"
                  :is-publish="props.isPublish"
                  :is-sellout="props.isSellout"
                  :storeType="storeType"
                />
              </div>
            </div>
          </div>
          
          <!-- 待审核商品内容 -->
          <div v-if="activeTab === 'pending'" class="pending-tab-content">
            <FormLayout
              class="inner-page-height"
              :isLoading="isPendingLoading"
              :tableData="pendingData"
              :tableColumns="pendingColumns"
              :pagination="{
                current: pendingPagination.page,
                pageSize: pendingPagination.pageSize,
                total: pendingPagination.total
              }"
              :isNeedCollapse="false"
              @paginationChange="handlePendingPaginationChange"
            >
              <!-- 搜索表单 -->
              <template #searchForm>
                <n-form
                  ref="formRef"
                  label-placement="left"
                  label-width="auto"
                  :show-feedback="false"
                  require-mark-placement="right-hanging"
                  size="small"
                  :style="{ width: '100%', marginLeft: '12px' }"
                >
                  <!-- 商品名称 -->
                  <n-form-item :span="12" label="商品名称">
                    <JSearchInput
                      v-model:value="pendingModel.name"
                      placeholder="请输入商品名称"
                      @search="getPendingGoodsData"
                    />
                  </n-form-item>
                  <!-- 商品分类 -->
                  <n-form-item :span="12" label="商品分类">
                    <n-select 
                      v-model:value="pendingModel.categoryId"
                      style="width: 160px;" 
                      :options="categoryOptions" 
                      clearable 
                    />
                  </n-form-item>
                  <!-- spu编码 -->
                  <n-form-item :span="12" label="spu编码">
                    <n-input
                      v-model:value="pendingModel.spu"
                      placeholder="请输入商品spu"
                      :maxlength="40"
                      clearable
                      style="width: 160px;"
                      :allow-input="(value)=>!value || /^[a-zA-Z0-9]+$/.test(value)"
                      @keyup.enter.native="getPendingGoodsData"
                    />
                  </n-form-item>
                  <!-- 供应商名称 -->
                  <n-form-item :span="12" label="供应商名称">
                    <n-select
                      v-model:value="pendingModel.supplierId"
                      :options="supplierOptions"
                      style="width: 160px;"
                      clearable
                      placeholder="请选择供应商"
                      filterable
                    />
                  </n-form-item>
                  <!-- 申请开始时间 -->
                  <n-form-item :span="12" label="申请开始时间">
                    <n-date-picker
                      v-model:value="pendingModel.applyStartTime"
                      type="datetime"
                      style="width: 160px;"
                      placeholder="开始时间"
                      clearable
                    />
                  </n-form-item>
                  <!-- 申请结束时间 -->
                  <n-form-item :span="12" label="申请结束时间">
                    <n-date-picker
                      v-model:value="pendingModel.applyEndTime"
                      type="datetime"
                      style="width: 160px;"
                      placeholder="结束时间"
                      clearable
                    />
                  </n-form-item>
                </n-form>
              </template>
              <!-- 表格头部按钮 -->
              <template #tableHeaderBtn>
                <n-button @click="getPendingGoodsData" class="store-button" type="primary">刷 新</n-button>
               
              </template>
            </FormLayout>
          </div>
          
          <!-- 审核不通过商品内容 -->
          <div v-if="activeTab === 'rejected'" class="rejected-tab-content">
            <FormLayout
              class="inner-page-height"
              :isLoading="isRejectedLoading"
              :tableData="rejectedData"
              :tableColumns="rejectedColumns"
              :pagination="{
                current: rejectedPagination.page,
                pageSize: rejectedPagination.pageSize,
                total: rejectedPagination.total
              }"
              :isNeedCollapse="false"
              @paginationChange="handleRejectedPaginationChange"
            >
              <!-- 搜索表单 -->
              <template #searchForm>
                <n-form
                  ref="rejectedFormRef"
                  label-placement="left"
                  label-width="auto"
                  :show-feedback="false"
                  require-mark-placement="right-hanging"
                  size="small"
                  :style="{ width: '100%', marginLeft: '12px' }"
                >
                  <!-- 商品名称 -->
                  <n-form-item :span="12" label="商品名称">
                    <JSearchInput
                      v-model:value="rejectedModel.name"
                      placeholder="请输入商品名称"
                      @search="getRejectedGoodsData"
                    />
                  </n-form-item>
                  <!-- 商品分类 -->
                  <n-form-item :span="12" label="商品分类">
                    <n-select 
                      v-model:value="rejectedModel.categoryId"
                      style="width: 160px;" 
                      :options="categoryOptions" 
                      clearable 
                    />
                  </n-form-item>
                  <!-- spu编码 -->
                  <n-form-item :span="12" label="spu编码">
                    <n-input
                      v-model:value="rejectedModel.spu"
                      placeholder="请输入商品spu"
                      :maxlength="40"
                      clearable
                      style="width: 160px;"
                      :allow-input="(value)=>!value || /^[a-zA-Z0-9]+$/.test(value)"
                      @keyup.enter.native="getRejectedGoodsData"
                    />
                  </n-form-item>
                  <!-- 供应商名称 -->
                  <n-form-item :span="12" label="供应商名称">
                    <n-select
                      v-model:value="rejectedModel.supplierId"
                      :options="supplierOptions"
                      style="width: 160px;"
                      clearable
                      placeholder="请选择供应商"
                      filterable
                    />
                  </n-form-item>
                  <!-- 申请开始时间 -->
                  <n-form-item :span="12" label="申请开始时间">
                    <n-date-picker
                      v-model:value="rejectedModel.applyStartTime"
                      type="datetime"
                      style="width: 160px;"
                      placeholder="开始时间"
                      clearable
                    />
                  </n-form-item>
                  <!-- 申请结束时间 -->
                  <n-form-item :span="12" label="申请结束时间">
                    <n-date-picker
                      v-model:value="rejectedModel.applyEndTime"
                      type="datetime"
                      style="width: 160px;"
                      placeholder="结束时间"
                      clearable
                    />
                  </n-form-item>
                </n-form>
              </template>
              <!-- 表格头部按钮 -->
              <template #tableHeaderBtn>
                <n-button @click="getRejectedGoodsData" class="store-button" type="primary">刷 新</n-button>
              </template>
            </FormLayout>
          </div>
        </TabsLayout>


        <!-- 弹窗组件 -->
    <AddGoodsClassification 
      ref="addGoodsClassificationRef"
      :storeType="storeType"
      @after-success="handleAfterAddSuccess"
    />
    <DeleteGoodsClassification ref="deleteGoodsClassificationRef" @after-success="handleAfterSuccess" />
    
    <!-- 审核弹框 -->
    <GoodsAuditModal
      v-if="activeTab === 'pending' || activeTab === 'rejected'"
      :key="`audit-modal-${activeTab}`"
      v-model:show="auditModalVisible"
      :goods-data="currentAuditGoods"
      :mode="activeTab === 'pending' ? 'audit' : 'detail'"
      @audit-result="handleAuditResult"
    />
    
    <!-- 审核不通过原因弹框 -->
          <n-modal
        v-model:show="rejectReasonModalVisible"
        preset="dialog"
        title="审核不通过"
        positive-text="确定"
        negative-text="取消"
        @positive-click="handleRejectConfirm"
        @negative-click="handleRejectCancel"
        style="width: 500px;"
        :show-icon="false"
        :positive-button-props="{ loading: isRejectLoading }"
      >
              <div class="reject-reason-form">
          <n-form-item label="原因" required label-width="60" label-placement="left">
            <n-input
              v-model:value="rejectReason"
              type="textarea"
              :maxlength="100"
              show-count
              :rows="4"
              style="width: 100%;"
            />
          </n-form-item>
        </div>
    </n-modal>
      </div>
    
    
</template>

<script lang="tsx" setup name="GoodsManagement">
import { ref, onMounted, h, nextTick, computed } from "vue";
import { useGetGoodsClassify } from "@/hooks/business";
import type { TreeOption } from 'naive-ui';
import { GoodsCategoryType } from "@/enums";
import type { GoodsType } from "@/enums";
import TablePreview from "@/components/TablePreview/index.vue";
import { getGoodsPage, auditNotPass, auditPass } from "@/services/api/storeApi/goods";
import { goodsCategoryOptions, shelfStatusOptions } from "@/constants";
import { getSupplier } from "@/services/api";
import FormLayout from "@/layout/FormLayout.vue";
import { useMessages } from "@/hooks";
/** 相关组件 */
import TreeContentLayout from "@/components/TreeContentLayout/index.vue";
import ClassificationTree from "./components/ClassificationTree.vue";
import AddGoodsClassification from "./components/AddGoodsClassification.vue";
import type { ModalType } from "./components/AddGoodsClassification.vue";
import DeleteGoodsClassification from "./components/DeleteGoodsClassification.vue";
import ProductList from "./components/ProductList.vue";
import GoodsAuditModal from "./components/GoodsAuditModal.vue";
/** 权限 */
import { hasAddGoodsClassAuth, hasEditGoodsClassAuth, hasDeleteGoodsClassAuth, hasAuditGoodsAuth } from "./authList";
import JSearchInput from "@/components/JSearchInput/index.vue";
import TabsLayout from "@/layout/TabsLayout.vue";

/** 消息提示 */
const { createMessageSuccess, createMessageError } = useMessages();

const props = defineProps<{
  goodsName?: string, // 商品名
  isPublish?: '0' | '1', // 商品上架状态
  isSellout?: '0' | '1', // 是否售罄 0: 未售罄 1: 已售罄
}>();

/** 商品分类 hook */
const {
  model,
  searchValue, 
  selectedKeys, 
  isGetLoading, 
  getGoodsClassificationData, 
  handleScroll,
  refresh,
  treeData,
  storeType,
  defaultExpandKeys
} = useGetGoodsClassify();

/** Tab相关状态 */
const activeTab = ref('all');

/** Tab数据配置 */
const tabsData = computed(() => [
  {
    label: '商品',
    key: 'all'
  },
  {
    label: '待审核商品',
    key: 'pending',
    number: pendingPagination.value.total || 0,
    isShowNumber: true
  },
  {
    label: '审核不通过商品',
    key: 'rejected',
    number: rejectedPagination.value.total || 0,
    isShowNumber: true
  }
]);

/** Tab切换处理 */
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName;
  console.log('切换到tab:', tabName);
  
  // 切换tab时确保弹框状态重置
  if (auditModalVisible.value) {
    auditModalVisible.value = false;
    currentAuditGoods.value = null;
  }
  
  // 切换tab时重新请求数据，确保数据是最新的
  if (tabName === 'pending') {
    // 切换到待审核商品tab时，重新请求数据
    getPendingGoodsData();
  } else if (tabName === 'rejected') {
    // 切换到审核不通过商品tab时，重新请求数据
    getRejectedGoodsData();
  }
};

/** 格式化价格 */
const formatPrice = (price: number) => {
  if (typeof price !== 'number' || isNaN(price)) return '¥0.00';
  return `¥${(price / 100).toFixed(2)}`;
};

/** 上架状态标签 */
const shelfStatusLabels = {
  0: '下架',
  1: '上架'
};

/** 待审核商品相关数据 */
const pendingModel = ref({
  supplierId: null,
  name: '',
  categoryId: null,
  spu: '',
  dateRange: null,
  applyStartTime: null, // 申请开始时间
  applyEndTime: null,   // 申请结束时间
  auditStatus: 1, // 审核状态：1-待审核
});

/** 供应商选项数据 */
const supplierOptions = ref([]);

/** 商品分类选项数据 */
const categoryOptions = ref([]);

/** 待审核商品表格列配置 */
const pendingColumns = [
  
  {
    title: "序号",
    key: "index",
    width: 60,
    align: "center",
    fixed: "left",
    render: (row, index) => {
      return <span>{index + 1}</span>;
    },
  },
  {
    title: "图片",
    key: "img",
    align: "left",
    fixed: "left",
    width: 120,
    render: (row) => {
      if (row?.productImgDTOList?.length > 0) {
        let paths = row?.productImgDTOList.map(item => item.path);
        return h(TablePreview, { src: paths });
      } 
      return '-';
    },
  },
  {
    title: "商品",
    key: "name",
    width: 260,
    align: "left",
    render: (row) => {
      // 普通商品
      if (row.type == GoodsCategoryType.GENERAL) {
        let title = `${row.frontName ?? ''}`;
        return <table-tooltip row={row} nameKey="name" title={title} idKey="id" />;
      }
      // 疗法
      if (row.type == GoodsCategoryType.THERAPY) {
        let title = `${row.frontName ?? ''}`;
        return <table-tooltip row={row} nameKey="name" title={title} idKey="id" />;
      }
      // 药品
      if (row.type == GoodsCategoryType.DRUG) {
        let name = row.productSpecDTOList?.[0]?.name ?? '';
        let title = `[${row.frontName ?? ''}] ${row.name ?? ''} ${name}`;
        return <table-tooltip row={row} nameKey="name" title={title} idKey="id" />;
      }
    }
  },
  {
    title: "供应商名称",
    key: "supplierName",
    width: 150,
    align: "left",
    render: (row) => {
      return <span>{row.supplierName ?? '-'}</span>;
    }
  },
  {
    title: "价格",
    key: "price",
    width: 180,
    align: "left",
    render: row => {
      if (!row?.productSpecDTOList || !Array.isArray(row.productSpecDTOList)) {
        return <span>¥0.00</span>;
      }
      let price = row.productSpecDTOList[0]?.price ?? 0; // 确保 price 是数字
      let formattedPrice = formatPrice(price);
      if (row.type === GoodsCategoryType.GENERAL && row.productSpecDTOList.length > 1) {
          // 如果是普通商品并且有多个规格，找到最低价格
          const minPrice = row.productSpecDTOList.reduce((min, item) => Math.min(min, item.price || 0), price);
          formattedPrice = `${formatPrice(minPrice)}起`;
        }
      return <span>{formattedPrice}</span>;
    }
  },
  {
    title:"已售",
    key: "soldQty",
    width: 150,
    align: "left",
    render: row => {
      if (!row?.productSpecDTOList || !Array.isArray(row.productSpecDTOList)) {
        return h('span', {}, 0);
      }
      const allSoldQty = row.productSpecDTOList.reduce((total, item) => total + (item.soldQty || 0), 0);
      return h('span', {}, allSoldQty);
    }
  },

  {
    title: "库存",
    key: "availStocks",
    width: 150,
    align: "left",
    render: row => {
      if (!row?.productSpecDTOList || !Array.isArray(row.productSpecDTOList)) {
        return h('span', {}, 0);
      }
      // 普通商品
      if (row.type == GoodsCategoryType.GENERAL) {
        // 计算 availStocks 的总值
        const totalAvailStocks = row.productSpecDTOList.reduce((total, item) => total + (item.availStocks || 0), 0);
        return h('span', {}, totalAvailStocks);
      }
      let availStocks = row.productSpecDTOList[0]?.availStocks ?? 0; // 确保 price 是数字
      return h('span', {}, availStocks);
    }
  },
  {
    title: "上架",
    key: "isPublish",
    width: 80,
    align: "left",
   
    render: (row) => {
        return (
          <n-tag 
            bordered={false} 
            size="small" 
            type={row.isPublish === 1 ? "success" : "error"}
          >
            {shelfStatusLabels[row.isPublish]}
          </n-tag>
        );
      },
  },
  {
    title: "操作",
    key: "action",
    width: 120,
    fixed: "right",
    align: "left",
    render: row => {
      return (
        <n-space>
          {hasAuditGoodsAuth ? <n-button text type="primary" onClick={() => openAuditModal(row)}>
            审核
          </n-button> : null}
        </n-space>
      );
    },
  },
];

/** 审核不通过商品表格列配置 */
const rejectedColumns = [
 
  {
    title: "序号",
    key: "index",
    width: 60,
    align: "center",
    fixed: "left",
    render: (row, index) => {
      return <span>{index + 1}</span>;
    },
  },
  {
    title: "图片",
    key: "img",
    align: "left",
    fixed: "left",
    width: 120,
    render: (row) => {
      if (row?.productImgDTOList?.length > 0) {
        let paths = row?.productImgDTOList.map(item => item.path);
        return h(TablePreview, { src: paths });
      } 
      return '-';
    },
  },
  {
    title: "商品",
    key: "name",
    width: 260,
    align: "left",
    render: (row) => {
      // 普通商品
      if (row.type == GoodsCategoryType.GENERAL) {
        let title = `${row.frontName ?? ''}`;
        return <table-tooltip row={row} nameKey="name" title={title} idKey="id" />;
      }
      // 疗法
      if (row.type == GoodsCategoryType.THERAPY) {
        let title = `${row.frontName ?? ''}`;
        return <table-tooltip row={row} nameKey="name" title={title} idKey="id" />;
      }
      // 药品
      if (row.type == GoodsCategoryType.DRUG) {
        let name = row.productSpecDTOList?.[0]?.name ?? '';
        let title = `[${row.frontName ?? ''}] ${row.name ?? ''} ${name}`;
        return <table-tooltip row={row} nameKey="name" title={title} idKey="id" />;
      }
    }
  },
  {
    title: "供应商名称",
    key: "supplierName",
    width: 150,
    align: "left",
    render: (row) => {
      return <span>{row.supplierName ?? '-'}</span>;
    }
  },
  {
    title: "价格",
    key: "price",
    width: 180,
    align: "left",
    render: row => {
      if (!row?.productSpecDTOList || !Array.isArray(row.productSpecDTOList)) {
        return <span>¥0.00</span>;
      }
      let price = row.productSpecDTOList[0]?.price ?? 0;
      let formattedPrice = formatPrice(price);
      if (row.type === GoodsCategoryType.GENERAL && row.productSpecDTOList.length > 1) {
        const minPrice = row.productSpecDTOList.reduce((min, item) => Math.min(min, item.price || 0), price);
        formattedPrice = `${formatPrice(minPrice)}起`;
      }
      return <span>{formattedPrice}</span>;
    }
  },
  {
    title:"已售",
    key: "soldQty",
    width: 150,
    align: "left",
    render: row => {
      if (!row?.productSpecDTOList || !Array.isArray(row.productSpecDTOList)) {
        return h('span', {}, 0);
      }
      const allSoldQty = row.productSpecDTOList.reduce((total, item) => total + (item.soldQty || 0), 0);
      return h('span', {}, allSoldQty);
    }
  },
  {
    title: "库存",
    key: "availStocks",
    width: 150,
    align: "left",
    render: row => {
      if (!row?.productSpecDTOList || !Array.isArray(row.productSpecDTOList)) {
        return h('span', {}, 0);
      }
      if (row.type == GoodsCategoryType.GENERAL) {
        const totalAvailStocks = row.productSpecDTOList.reduce((total, item) => total + (item.availStocks || 0), 0);
        return h('span', {}, totalAvailStocks);
      }
      let availStocks = row.productSpecDTOList[0]?.availStocks ?? 0;
      return h('span', {}, availStocks);
    }
  },
  {
    title: "上架",
    key: "isPublish",
    width: 80,
    align: "left",
    render: (row) => {
      return (
        <n-tag 
          bordered={false} 
          size="small" 
          type={row.isPublish === 1 ? "success" : "error"}
        >
          {shelfStatusLabels[row.isPublish]}
        </n-tag>
      );
    },
  },
  {
    title: "操作",
    key: "action",
    width: 120,
    fixed: "right",
    align: "left",
    render: row => {
      return h('n-space', {}, [
        h('n-button', { 
          text: true, 
          type: "primary", 
          onClick: () => openDetailModal(row),
          style: {
            color: '#409eff',
            cursor: 'pointer'
          }
        }, '详情')
      ]);
    },
  },
];

/** 待审核商品静态数据 */
const pendingData = ref([
 
]);

/** 审核不通过商品搜索模型 */
const rejectedModel = ref({
  supplierId: null,
  name: '',
  categoryId: null,
  spu: '',
  dateRange: null,
  applyStartTime: null, // 申请开始时间
  applyEndTime: null,   // 申请结束时间
  auditStatus: 3, // 审核状态：3-审核不通过
});

/** 审核不通过商品数据 */
const rejectedData = ref([]);

/** 审核不通过商品分页配置 */
const rejectedPagination = ref({
  page: 1,
  pageSize: 10,
  total: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 30, 40],
  onChange: (page) => {
    rejectedPagination.value.page = page;
    getRejectedGoodsData();
  },
  onUpdatePageSize: (pageSize) => {
    rejectedPagination.value.pageSize = pageSize;
    rejectedPagination.value.page = 1;
    getRejectedGoodsData();
  },
});

/** 待审核商品分页配置 */
const pendingPagination = ref({
  page: 1,
  pageSize: 10,
  total: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 30, 40],
  onChange: (page) => {
    pendingPagination.value.page = page;
    getPendingGoodsData();
  },
  onUpdatePageSize: (pageSize) => {
    pendingPagination.value.pageSize = pageSize;
    pendingPagination.value.page = 1;
    getPendingGoodsData();
  },
});

/** 格式化日期时间为 YYYY-MM-DD HH:mm:ss 格式 */
const formatDateTime = (dateTime) => {
  if (!dateTime) return null;
  
  const date = new Date(dateTime);
  
  // 获取本地时间的各个部分
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

/** 获取待审核商品数据 */
const getPendingGoodsData = async () => {
  try {
    isPendingLoading.value = true; // 开始加载
    
    // 处理时间参数，确保格式正确
    const { applyStartTime, applyEndTime, ...otherData } = pendingModel.value;
    
    const params = {
      data: {
        ...otherData,
        applyStartTime: applyStartTime ? formatDateTime(applyStartTime) : null,
        applyEndTime: applyEndTime ? formatDateTime(applyEndTime) : null,
        auditStatus: 1, // 审核状态：1-待审核
      },
      pageVO: {
        current: pendingPagination.value.page,
        size: pendingPagination.value.pageSize,
      },
    };
    
    const response = await getGoodsPage(params);
    pendingData.value = response.records || [];
    pendingPagination.value.total = Number(response.total) || 0;
  } catch (error) {
    // 错误处理
  } finally {
    isPendingLoading.value = false; // 结束加载
  }
};

/** 处理待审核商品分页变化 */
const handlePendingPaginationChange = (pageInfo) => {
  console.log('分页变化:', pageInfo);
  if (pageInfo.page) {
    pendingPagination.value.page = pageInfo.page;
  }
  if (pageInfo.pageSize) {
    pendingPagination.value.pageSize = pageInfo.pageSize;
    pendingPagination.value.page = 1; // 切换页面大小时重置到第一页
  }
  getPendingGoodsData();
};

/** 处理审核不通过商品分页变化 */
const handleRejectedPaginationChange = (pageInfo) => {
  console.log('审核不通过商品分页变化:', pageInfo);
  if (pageInfo.page) {
    rejectedPagination.value.page = pageInfo.page;
  }
  if (pageInfo.pageSize) {
    rejectedPagination.value.pageSize = pageInfo.pageSize;
    rejectedPagination.value.page = 1; // 切换页面大小时重置到第一页
  }
  getRejectedGoodsData();
};

/** 重置待审核商品搜索条件 */
const resetPendingSearch = () => {
  pendingModel.value = {
    supplierId: null,
    name: '',
    categoryId: null,
    spu: '',
    dateRange: null,
    applyStartTime: null,
    applyEndTime: null,
    auditStatus: 1, // 审核状态：1-待审核
  };
  pendingPagination.value.page = 1;
  getPendingGoodsData();
};

/** 获取审核不通过商品数据 */
const getRejectedGoodsData = async () => {
  try {
    isRejectedLoading.value = true; // 开始加载
    
    // 处理时间参数，确保格式正确
    const { applyStartTime, applyEndTime, ...otherData } = rejectedModel.value;
    
    const params = {
      data: {
        ...otherData,
        applyStartTime: applyStartTime ? formatDateTime(applyStartTime) : null,
        applyEndTime: applyEndTime ? formatDateTime(applyEndTime) : null,
        auditStatus: 3, // 审核状态：3-审核不通过
      },
      pageVO: {
        current: rejectedPagination.value.page,
        size: rejectedPagination.value.pageSize,
      },
    };
    
    const response = await getGoodsPage(params);
    rejectedData.value = response.records || [];
    rejectedPagination.value.total = Number(response.total) || 0;
  } catch (error) {
    // 错误处理
  } finally {
    isRejectedLoading.value = false; // 结束加载
  }
};

/** 重置审核不通过商品搜索条件 */
const resetRejectedSearch = () => {
  rejectedModel.value = {
    supplierId: null,
    name: '',
    categoryId: null,
    spu: '',
    dateRange: null,
    applyStartTime: null,
    applyEndTime: null,
    auditStatus: 3, // 审核状态：3-审核不通过
  };
  rejectedPagination.value.page = 1;
  getRejectedGoodsData();
};

/** 获取供应商选项数据 */
const getSupplierOptions = async () => {
  try {
    const params = {
      data: {
        supplierName: null,
      },
      pageVO: {
        current: 1,
        size: 500,
      },
    };
    
    const response = await getSupplier(params);
    supplierOptions.value = response.records?.map(item => ({
      label: item.supplierName, 
      value: item.id,
      disabled: !item.status
    })) || [];
  } catch (error) {
    console.error('获取供应商数据失败:', error);
  }
};

/** 获取商品分类选项数据 */
const getCategoryOptions = async () => {
  try {
    // 使用已有的商品分类数据
    categoryOptions.value = goodsCategoryOptions;
  } catch (error) {
    console.error('获取商品分类数据失败:', error);
  }
};

/** 审核弹框相关 */
const auditModalVisible = ref(false);
const currentAuditGoods = ref(null);

/** Loading状态 */
const isPendingLoading = ref(false);
const isRejectedLoading = ref(false);
const isRejectLoading = ref(false);

/** 审核不通过相关状态 */
const rejectReasonModalVisible = ref(false);
const rejectReason = ref('');
const currentRejectGoods = ref(null);

/** 打开审核弹框 */
const openAuditModal = (row) => {
  console.log("点击审核按钮，row:", row);
  console.log("当前activeTab:", activeTab.value);
  console.log("设置前auditModalVisible:", auditModalVisible.value);
  currentAuditGoods.value = row;
  auditModalVisible.value = true;
  console.log("设置后auditModalVisible:", auditModalVisible.value);
};

/** 打开详情弹框 */
const openDetailModal = (row) => {
  console.log("点击详情按钮，row:", row);
  console.log("当前activeTab:", activeTab.value);
  console.log("设置前auditModalVisible:", auditModalVisible.value);
  currentAuditGoods.value = row;
  auditModalVisible.value = true;
  console.log("设置后auditModalVisible:", auditModalVisible.value);
};

/** 打开审核不通过原因弹框 */
const openRejectReasonModal = (row) => {
  console.log("点击审核不通过按钮，row:", row);
  currentRejectGoods.value = row;
  rejectReason.value = '';
  rejectReasonModalVisible.value = true;
};

/** 确认审核不通过 */
const handleRejectConfirm = async () => {
  if (!rejectReason.value.trim()) {
    // 显示提示：原因不能为空
    createMessageError('请输入审核不通过的原因');
    return;
  }
  
  if (!currentRejectGoods.value?.id) {
    console.error('商品ID不存在');
    return;
  }
  
  try {
    console.log("审核不通过，原因:", rejectReason.value);
    console.log("商品数据:", currentRejectGoods.value);
    
    // 显示加载状态
    isRejectLoading.value = true;
    console.log(currentRejectGoods.value.id,'currentRejectGoods.value.id')
  

    // 调用审核不通过API
    await auditNotPass({
      id: currentRejectGoods.value.id.toString(), // 转为字符串，避免数字精度丢失
      reason: rejectReason.value.trim()
    });
    
    console.log('审核不通过成功');
    
    // 显示成功提示
    createMessageSuccess('审核不通过成功');
    
    // 关闭弹框，清空数据
    rejectReasonModalVisible.value = false;
    rejectReason.value = '';
    currentRejectGoods.value = null;
    
    // 刷新当前标签页的数据
    if (activeTab.value === 'pending') {
      getPendingGoodsData();
    } else if (activeTab.value === 'rejected') {
      getRejectedGoodsData();
    }
    
  } catch (error) {
    console.error('审核不通过失败:', error);
    // 显示错误提示，留在审核页面
    createMessageError('审核不通过失败，请重试');
  } finally {
    // 隐藏加载状态
    isRejectLoading.value = false;
  }
};

/** 取消审核不通过 */
const handleRejectCancel = () => {
  rejectReasonModalVisible.value = false;
  rejectReason.value = '';
  currentRejectGoods.value = null;
};

/** 处理审核结果 */
const handleAuditResult = async (result, data) => {
  console.log('审核结果:', result, data);
  
  if (result === 'approve') {
    console.log('审核通过');
    try {
      // 调用审核通过API
      await auditPass({
        id: data.id.toString() // 转为字符串，避免数字精度丢失
      });
      
      // 显示成功提示
      createMessageSuccess('审核通过成功');
      // 关闭审核弹框
      auditModalVisible.value = false;
      currentAuditGoods.value = null;
      // 刷新数据
      if (activeTab.value === 'pending') {
        getPendingGoodsData();
      } else if (activeTab.value === 'rejected') {
        getRejectedGoodsData();
      }
    } catch (error) {
      console.error('审核通过失败:', error);
      createMessageError('审核通过失败，请重试');
    }
  } else if (result === 'reject') {
    console.log('审核不通过');
    // 这里可以调用审核不通过的API
    // 然后刷新数据
    if (activeTab.value === 'pending') {
      getPendingGoodsData();
    } else if (activeTab.value === 'rejected') {
      getRejectedGoodsData();
    }
  } else if (result === 'reject-click') {
    console.log('点击审核不通过，打开原因输入弹框');
    // 打开审核不通过原因弹框
    currentRejectGoods.value = data;
    rejectReason.value = '';
    rejectReasonModalVisible.value = true;
  }
  
  // 刷新表格数据
  // refreshPendingData();
};



/** 右键选项 */
const options =  [
  {
    label: '删除分类',
    key: 'delete',
    show: hasDeleteGoodsClassAuth
  },
  {
    label: '修改分类',
    key: 'edit',
    show: hasEditGoodsClassAuth
  },
  {
    label: '新建子分类',
    key: 'addChild',
    show: hasAddGoodsClassAuth
  }
];

/** 右键操作 */
const handleMenuSelect = (key: string | number, option: TreeOption) => {
  // console.log("option", option);
  // 编辑操作
  if (key === 'edit' && !option?.parentId) {
    openAddGoodsClassification('edit', option);
  }
  // 子分类编辑操作
  if (key === 'edit' && option?.parentId) {
    openAddGoodsClassification('editChild', option);
  }
  // 删除操作
  if (key === 'delete') {
    openDeleteGoodsClassification(option);
  }
  // 新建子分类
  if (key === 'addChild') {
    openAddGoodsClassification('addChild', { parentId: option?.id as string, type: option?.type as GoodsType });
  }
};

/** 新增商品分类成功回调 */
const handleAfterAddSuccess = (val: string, type: GoodsType) => {
  selectedKeys.value = [val];
  model.value.cateId = val;
  model.value.type = type;
  refresh();
};

/** 删除商品分类成功回调 */
const handleAfterSuccess = (val: string | null, type: GoodsType) => {
  if (val) {
    selectedKeys.value = [val];
    model.value.cateId = val;
    model.value.type = type;
  } else {
    selectedKeys.value = [String(GoodsCategoryType.ALL)];
    model.value.cateId = null;
    model.value.type = null;
  }
  refresh();
};

/** 树形节点选中项发生变化时的回调函数 */
const handleUpdateSelectKeys = (keys: Array<string | number>, option: Array<TreeOption & Partial<ApiStoreModule.GoodsClassification> | null>, meta: { node: TreeOption | null, action: 'select' | 'unselect' }) => {
  // console.log("---------------树形节点点击 start--------------");
  // console.log("keys", keys);
  // console.log("option", option);
  // console.log("meta", meta);
  // console.log("---------------树形节点点击 end--------------");
  // 赋值
  if (keys.length !== 0) {
    const firstOption = option[0];
    selectedKeys.value = keys;
    model.value.cateId = firstOption?.id ?? null;
    model.value.type = firstOption?.type ?? null;
    // 加载状态
    isGetLoading.value = true;
  }
};

/** 打开新建商品分类 */
const addGoodsClassificationRef = ref<InstanceType<typeof AddGoodsClassification> | null>(null);
const openAddGoodsClassification = (type: ModalType, option: TreeOption & { isMenu?: boolean } & Partial<ApiStoreModule.GoodsClassification> = {}) => {
  const _params = {
    type,
    row: option
  };
  addGoodsClassificationRef.value?.acceptParams(_params);
};

/** 打开删除商品分类 */
const deleteGoodsClassificationRef = ref<InstanceType<typeof DeleteGoodsClassification> | null>(null);
const openDeleteGoodsClassification = (option: TreeOption & { isMenu?: boolean } & Partial<ApiStoreModule.GoodsClassification> = {}) => {
  const _params = {
    row: option,
  };
  deleteGoodsClassificationRef.value?.acceptParams(_params);
};

/** 搜索分类 */
const handleSearch = () => {
  getGoodsClassificationData();
};

/** 组件挂载 */
onMounted(() => {
  getGoodsClassificationData();
  
  // 确保弹框状态初始化
  auditModalVisible.value = false;
  currentAuditGoods.value = null;
  
  // 初始加载待审核商品数据
  getPendingGoodsData();
  
  // 初始加载审核不通过商品数据
  getRejectedGoodsData();
  
  // 加载供应商和商品分类选项数据
  getSupplierOptions();
  getCategoryOptions();
});
</script>

<style lang="less" scoped>
.tab-header {
  
  padding: 0 16px 0;
  background: #fff;
  box-sizing: border-box;

  :deep(.n-tabs-tab) {
    font-size: 14px;
    font-weight: 500;
  }
  
  :deep(.n-tabs-tab-pad) {
    padding: 0 24px;
  }
}

.tabsLayout {
  :deep(.n-tabs-tab) {
    font-size: 14px;
    font-weight: 500;
  }
  
  :deep(.n-tabs-tab-pad) {
    padding: 0 24px;
  }
}

.goods-tab-content {
  // padding: 16px;
  height: calc(100vh - 120px);
  overflow: hidden;
}

.goods-layout {
  display: flex;
  height: 100%;
  gap: 16px;
}

.classify-container {
  width: 240px;
  flex-shrink: 0;
  .header {
    padding: 12px;
  }
  .classification-tree-container {
    width: 100%;
    height: calc(100% - 104px);
    :deep(.n-spin-content) {
			height: 100%;
    }
  }
}

.goods-container {
  flex: 1;
  height: 100%;
  overflow: hidden;
}

.pending-tab-content,
.rejected-tab-content {
  padding: 16px;
  height: calc(100vh - 120px);
  overflow: hidden;
  box-sizing: border-box;
}

.search-filter-section {
  background: #fff;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table-section {
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  flex: 1;
  overflow: hidden;
}

.tab-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #999;
  font-size: 14px;
  
  p {
    margin: 8px 0;
    text-align: center;
  }
}

// 确保tab内容区域有足够高度
:deep(.n-tabs-tab-pane) {
  height: 100%;
}

:deep(.n-tabs-content) {
  height: 100%;
}
</style>
