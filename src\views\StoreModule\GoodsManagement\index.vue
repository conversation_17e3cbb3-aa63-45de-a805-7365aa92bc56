<template>
  <TreeContentLayout :treeWidth="240">
    <template #tree-content>
      <div class="classify-container inner-page-height">
        <!-- header -->
        <div class="header">
          <n-space justify="space-between" style="margin-bottom: 12px;flex-wrap: nowrap;">
            <n-button size="small" :loading="isGetLoading" @click="refresh" class="store-button">刷 新</n-button>
            <n-button v-if="hasAddGoodsClassAuth" size="small" type="primary" @click="openAddGoodsClassification('add')">新建分类</n-button>
          </n-space>
          <JSearchInput
            v-model:value="searchValue"
            @search="handleSearch"
            width="100%"
            size="small"
            placeholder="请输入分类名称"
            clearable
          />
        </div>
        <!-- 商品分类 tree -->
        <div class="classification-tree-container">
          <n-spin :show="isGetLoading" size="small" style="height: 100%;">
            <n-scrollbar style="height: 100%;" @scroll="handleScroll">
              <ClassificationTree
                v-model:value="model.selectedValue"
                :tree-data="treeData"
                :menu-options="options"
                @menu-select="handleMenuSelect"
                @update:selected-keys="handleUpdateSelectKeys"
                :selected-keys="selectedKeys"
                :defaultExpandKeys="defaultExpandKeys"
              />
            </n-scrollbar>
          </n-spin>
        </div>
      </div>
    </template>
    <template #default>
      <div class="goods-container">
        <ProductList
          :cate-id="model.cateId"
          :type="model.type"
          @successful="isGetLoading = false"
          :name="props.goodsName"
          :is-publish="props.isPublish"
          :is-sellout="props.isSellout"
          :storeType="storeType"
        />
      </div>
      <!-- 新建分类 -->
      <AddGoodsClassification 
        ref="addGoodsClassificationRef"
        :storeType="storeType"
        @after-success="handleAfterAddSuccess"
      />
      <!-- 删除分类 -->
      <DeleteGoodsClassification ref="deleteGoodsClassificationRef" @after-success="handleAfterSuccess" />
    </template>
  </TreeContentLayout>
</template>

<script lang="ts" setup name="GoodsManagement">
import { ref, onMounted } from "vue";
import { useGetGoodsClassify } from "@/hooks/business";
import type { TreeOption } from 'naive-ui';
import { GoodsCategoryType } from "@/enums";
import type { GoodsType } from "@/enums";
/** 相关组件 */
import TreeContentLayout from "@/components/TreeContentLayout/index.vue";
import ClassificationTree from "./components/ClassificationTree.vue";
import AddGoodsClassification from "./components/AddGoodsClassification.vue";
import type { ModalType } from "./components/AddGoodsClassification.vue";
import DeleteGoodsClassification from "./components/DeleteGoodsClassification.vue";
import ProductList from "./components/ProductList.vue";
/** 权限 */
import { hasAddGoodsClassAuth, hasEditGoodsClassAuth, hasDeleteGoodsClassAuth } from "./authList";

const props = defineProps<{
  goodsName?: string, // 商品名
  isPublish?: '0' | '1', // 商品上架状态
  isSellout?: '0' | '1', // 是否售罄 0: 未售罄 1: 已售罄
}>();

/** 商品分类 hook */
const {
  model,
  searchValue, 
  selectedKeys, 
  isGetLoading, 
  getGoodsClassificationData, 
  handleScroll,
  refresh,
  treeData,
  storeType,
  defaultExpandKeys
} = useGetGoodsClassify();

/** 右键选项 */
const options =  [
  {
    label: '删除分类',
    key: 'delete',
    show: hasDeleteGoodsClassAuth
  },
  {
    label: '修改分类',
    key: 'edit',
    show: hasEditGoodsClassAuth
  },
  {
    label: '新建子分类',
    key: 'addChild',
    show: hasAddGoodsClassAuth
  }
];

/** 右键操作 */
const handleMenuSelect = (key: string | number, option: TreeOption) => {
  // console.log("option", option);
  // 编辑操作
  if (key === 'edit' && !option?.parentId) {
    openAddGoodsClassification('edit', option);
  }
  // 子分类编辑操作
  if (key === 'edit' && option?.parentId) {
    openAddGoodsClassification('editChild', option);
  }
  // 删除操作
  if (key === 'delete') {
    openDeleteGoodsClassification(option);
  }
  // 新建子分类
  if (key === 'addChild') {
    openAddGoodsClassification('addChild', { parentId: option?.id as string, type: option?.type as GoodsType });
  }
};

/** 新增商品分类成功回调 */
const handleAfterAddSuccess = (val: string, type: GoodsType) => {
  selectedKeys.value = [val];
  model.value.cateId = val;
  model.value.type = type;
  refresh();
};

/** 删除商品分类成功回调 */
const handleAfterSuccess = (val: string | null, type: GoodsType) => {
  if (val) {
    selectedKeys.value = [val];
    model.value.cateId = val;
    model.value.type = type;
  } else {
    selectedKeys.value = [String(GoodsCategoryType.ALL)];
    model.value.cateId = null;
    model.value.type = null;
  }
  refresh();
};

/** 树形节点选中项发生变化时的回调函数 */
const handleUpdateSelectKeys = (keys: Array<string | number>, option: Array<TreeOption & Partial<ApiStoreModule.GoodsClassification> | null>, meta: { node: TreeOption | null, action: 'select' | 'unselect' }) => {
  // console.log("---------------树形节点点击 start--------------");
  // console.log("keys", keys);
  // console.log("option", option);
  // console.log("meta", meta);
  // console.log("---------------树形节点点击 end--------------");
  // 赋值
  if (keys.length !== 0) {
    const firstOption = option[0];
    selectedKeys.value = keys;
    model.value.cateId = firstOption?.id ?? null;
    model.value.type = firstOption?.type ?? null;
    // 加载状态
    isGetLoading.value = true;
  }
};

/** 打开新建商品分类 */
const addGoodsClassificationRef = ref<InstanceType<typeof AddGoodsClassification> | null>(null);
const openAddGoodsClassification = (type: ModalType, option: TreeOption & { isMenu?: boolean } & Partial<ApiStoreModule.GoodsClassification> = {}) => {
  const _params = {
    type,
    row: option
  };
  addGoodsClassificationRef.value?.acceptParams(_params);
};

/** 打开删除商品分类 */
const deleteGoodsClassificationRef = ref<InstanceType<typeof DeleteGoodsClassification> | null>(null);
const openDeleteGoodsClassification = (option: TreeOption & { isMenu?: boolean } & Partial<ApiStoreModule.GoodsClassification> = {}) => {
  const _params = {
    row: option,
  };
  deleteGoodsClassificationRef.value?.acceptParams(_params);
};

/** 搜索分类 */
const handleSearch = () => {
  getGoodsClassificationData();
};

/** 组件挂载 */
onMounted(() => {
  getGoodsClassificationData();
});
</script>

<style lang="less" scoped>
.classify-container {
  width: 100%;
  .header {
    padding: 12px;
  }
  .classification-tree-container {
    width: 100%;
    height: calc(100% - 104px);
    :deep(.n-spin-content) {
			height: 100%;
    }
  }
}

.goods-container {
  width: 100%;
  height: 100%;
}
</style>
