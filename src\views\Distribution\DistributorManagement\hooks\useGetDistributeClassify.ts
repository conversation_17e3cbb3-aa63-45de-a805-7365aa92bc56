import { ref, h } from "vue";
import { useMessages } from "@/hooks";
import type { TreeOption } from 'naive-ui';
import { NImage } from 'naive-ui';
import { GoodsCategoryType, SystemStoreType } from "@/enums";
import type { GoodsType } from "@/enums";
import { distributePage } from "@/services/api";
import { deepClone, transformMinioSrc } from "@/utils";
import Folder from "@/assets/image/system/folder.png";
import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
import { buildTree, getExpandKeys } from "@/hooks/business/utils";

/**
 * @description 获取分销商品分类
 */
export default function useGetDistributeClassify(selectKey: GoodsType = GoodsCategoryType.ALL) {
  const message = useMessages();
  /** 商城类型 */
  const systemStore = useSystemStoreWithoutSetup();
  let storeType = systemStore._globalConfig['marketplaceType'];

  const defaultSelectKey: GoodsType = storeType === SystemStoreType.PHARMACEUTICALMALL ? selectKey : GoodsCategoryType.GENERAL;
  const isGetLoading = ref(false);
  const searchValue = ref({
    distributeId:'',
    // cateType:storeType,
    isAdd:0
  });
  /** 当前SelectedKeys */
  const selectedKeys = ref<Array<string | number>>([String(defaultSelectKey)]);

  /** 默认展开项 */
  const defaultExpandKeys = ref<Array<string | number>>([]);

  /** 参数 */
  const model = ref({
    selectedValue: '',
    cateId: null, // 当前选中的商品分类Id
    type: storeType === SystemStoreType.PHARMACEUTICALMALL ? null : GoodsCategoryType.GENERAL, // 商品分类类型
    pharmaceuticalsCommodities:false //是否为药品与普通商品
  });

  /** 分页总数 */
  let recordsTotal = 1;

  /** 商品分类搜索参数 */
  // const _params = {
  //   data: {
  //     name: searchValue.value,
  //   },
  //   pageVO: {
  //     current: 1,
  //     size: 300,
  //   },
  // };

  /** 医药商城 */
  let pharmaceuticalMall: Array<TreeOption & { isMenu: boolean } & Partial<ApiStoreModule.GoodsClassification>>  = [
    {
      key: String(GoodsCategoryType.ALL),
      label: '全部',
      type: null,
      isMenu: false,
    },
    {
      key: String(GoodsCategoryType.DRUG),
      label: '药品',
      type: GoodsCategoryType.DRUG,
      isMenu: false,
      prefix: () => h(NImage, {
        width: '20',
        height: '20',
        previewDisabled: true,
        src: Folder,
        lazy: true
      }, {}),
      children: []
    },
    // {
    //   key: String(GoodsCategoryType.THERAPY),
    //   label: '疗法',
    //   type: GoodsCategoryType.THERAPY,
    //   isMenu: false,
    //   prefix: () => h(NImage, {
    //     width: '20',
    //     height: '20',
    //     previewDisabled: true,
    //     src: Folder,
    //     lazy: true
    //   }, {}),
    //   children: []
    // },
    {
      key: String(GoodsCategoryType.GENERAL),
      label: '普通商品',
      type: GoodsCategoryType.GENERAL,
      isMenu: false,
      prefix: () => h(NImage, {
        width: '20',
        height: '20',
        previewDisabled: true,
        src: Folder,
        lazy: true
      }, {}),
      children: []
    },
    // {
    //   key: String(GoodsCategoryType.INTEGRAL),
    //   label: '积分商品',
    //   type: GoodsCategoryType.INTEGRAL,
    //   isMenu: false,
    //   prefix: () => h(NImage, {
    //     width: '20',
    //     height: '20',
    //     previewDisabled: true,
    //     src: Folder,
    //     lazy: true
    //   }, {}),
    //   children: []
    // },
  ];

  /** 普通商城 */
  let generalMall: Array<TreeOption & { isMenu: boolean } & Partial<ApiStoreModule.GoodsClassification>>  = [
    {
      key: String(GoodsCategoryType.GENERAL),
      label: '普通商品',
      type: GoodsCategoryType.GENERAL,
      isMenu: false,
      prefix: () => h(NImage, {
        width: '20',
        height: '20',
        previewDisabled: true,
        src: Folder,
        lazy: true
      }, {}),
      children: []
    },
  ];

  /** 树形数据初始化 */
  const initTreeData: Array<TreeOption & { isMenu: boolean } & Partial<ApiStoreModule.GoodsClassification>> = storeType === SystemStoreType.PHARMACEUTICALMALL ? [...pharmaceuticalMall] : [...generalMall];

  /** 树形数据 */
  const treeData = ref<Array<TreeOption & { isMenu: boolean }>>(deepClone(initTreeData));

  /** 处理数据 */
  const handleData = (dataList: Array<ApiStoreModule.GoodsClassification>, treeData: Array<TreeOption & { isMenu: boolean } & Partial<ApiStoreModule.GoodsClassification>>) => {
    const newTreeData = deepClone(treeData);
    const newDatalist = buildTree(dataList);

    newDatalist.forEach(item => {
      // 药品
      if (item.type == GoodsCategoryType.DRUG) {
        const drugTree = newTreeData.find(tree => tree.key == GoodsCategoryType.DRUG);
        if (drugTree) {
          drugTree.children.push({
            ...item,
            key: item.id,
            label: item.name,
            isMenu: true,
            prefix: () => h(NImage, {
              width: '20',
              height: '20',
              previewDisabled: true,
              src: transformMinioSrc(item.iconPath)
            }, {}),
            children: item?.children?.map(child => ({
              ...child,
              key: child.id,
              label: child.name,
              isMenu: true,
              prefix: () => h(NImage, {
                width: '20',
                height: '20',
                previewDisabled: true,
                src: transformMinioSrc(child.iconPath)
              }, {}),
              isLeaf: child?.children?.length === 0,
            })),
            isLeaf: item?.children?.length === 0,
          });
        }
      }
      // 疗法
      else if (item.type == GoodsCategoryType.THERAPY) {
        const therapyTree = newTreeData.find(tree => tree.key == GoodsCategoryType.THERAPY);
        if (therapyTree) {
          therapyTree.children.push({
            ...item,
            key: item.id,
            label: item.name,
            isMenu: true,
            prefix: () => h(NImage, {
              width: '20',
              height: '20',
              previewDisabled: true,
              src: transformMinioSrc(item.iconPath)
            }, {}),
            children: item?.children?.map(child => ({
              ...child,
              key: child.id,
              label: child.name,
              isMenu: true,
              prefix: () => h(NImage, {
                width: '20',
                height: '20',
                previewDisabled: true,
                src: transformMinioSrc(child.iconPath)
              }, {}),
              isLeaf: child?.children?.length === 0,
            })),
            isLeaf: item?.children?.length === 0,
          });
        }
      }
      // 普通商品
      else if (item.type == GoodsCategoryType.GENERAL) {
        const therapyTree = newTreeData.find(tree => tree.key == GoodsCategoryType.GENERAL);
        if (therapyTree) {
          therapyTree.children.push({
            ...item,
            key: item.id,
            label: item.name,
            isMenu: true,
            prefix: () => h(NImage, {
              width: '20',
              height: '20',
              previewDisabled: true,
              src: transformMinioSrc(item.iconPath)
            }, {}),
            children: item?.children?.map(child => ({
              ...child,
              key: child.id,
              label: child.name,
              isMenu: true,
              prefix: () => h(NImage, {
                width: '20',
                height: '20',
                previewDisabled: true,
                src: transformMinioSrc(child.iconPath)
              }, {}),
              isLeaf: child?.children?.length === 0,
            })),
            isLeaf: item?.children?.length === 0,
          });
        }
      }
      // 积分商品
      else if (item.type == GoodsCategoryType.INTEGRAL) {
        const therapyTree = newTreeData.find(tree => tree.key == GoodsCategoryType.INTEGRAL);
        if (therapyTree) {
          therapyTree.children.push({
            ...item,
            key: item.id,
            label: item.name,
            isMenu: true,
            prefix: () => h(NImage, {
              width: '20',
              height: '20',
              previewDisabled: true,
              src: transformMinioSrc(item.iconPath)
            }, {}),
            children: item?.children?.map(child => ({
              ...child,
              key: child.id,
              label: child.name,
              isMenu: true,
              prefix: () => h(NImage, {
                width: '20',
                height: '20',
                previewDisabled: true,
                src: transformMinioSrc(child.iconPath)
              }, {}),
              isLeaf: child?.children?.length === 0,
            })),
            isLeaf: item?.children?.length === 0,
          });
        }
      }
    });
    // 使用 filter 去重
    let newDefaultExpandKeys = getExpandKeys(newTreeData).filter((value, index, self) => self.indexOf(value) === index);
    defaultExpandKeys.value.push(...newDefaultExpandKeys);

    return newTreeData;
  };

  /** 获取商品分类数据 */
  const getGoodsClassificationData = async (callBack?: () => void) => {
    try {
      isGetLoading.value = true;
      const data = await distributePage(searchValue.value);
      if (data.length > 0) {
        treeData.value = handleData(data, initTreeData);
        if(model.value.pharmaceuticalsCommodities){
          treeData.value = treeData.value.filter((item) =>
            item.label !== '疗法' && item.label !== '积分商品'
          )
        }
        console.log(treeData.value);
        if (treeData.value[2].children.length == 0){
          treeData.value.splice(2,1)
        }
        console.log(treeData.value);
        if (treeData.value[1].children.length == 0){
          treeData.value.splice(1,1)
        }
        console.log(treeData.value);
      } else {
        treeData.value = [];
      }
      // 友情提示
      // if (searchValue.value && !records.length) {
      //   message.createMessageInfo(`不存在《${searchValue.value}》商品分类！`);
      // }
    } catch (err) {
      message.createMessageError("获取商品分类列表失败: " + err);
    } finally {
      isGetLoading.value = false;
      callBack && callBack();
    }
  };

  /** 滚动加载 */
  // const handleScroll = (e) => {
  //   const currentTarget = e.currentTarget as HTMLElement;
  //   if (
  //     currentTarget.scrollTop + currentTarget.offsetHeight >=
  //     currentTarget.scrollHeight
  //   ) {
  //     if (_params.pageVO.current * _params.pageVO.size < recordsTotal) {
  //       _params.pageVO.current++;
  //       getGoodsClassificationData();
  //     }
  //   }
  // };

  /** 刷新 */
  const refresh = () => {
    getGoodsClassificationData();
  };

  return {
    isGetLoading,
    model,
    searchValue,
    getGoodsClassificationData,
    // handleScroll,
    refresh,
    treeData,
    selectedKeys,
    storeType,
    defaultExpandKeys
  }
}