<template>
  <div class="wrapper">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :isNeedCollapse="false"
      :isTableSelection="false"
      :pagination="paginationRef"
      @paginationChange="paginationChange"
      @selectedKeysChange="selectedKeysChange"
      @table-sorter-change="tableSorterChange"
    >
      <template #tableHeaderBtn>
        <n-button @click="tableSearch" class="store-button">刷 新</n-button>
        <!-- 操作项 -->
      </template>
    </FormLayout>
    <GroupModel ref="groupModel" @tableSearch="tableSearch"/>
  </div>
</template>

<script setup lang="tsx" name="MessageGroup">
import { onMounted, ref, toRef, watch } from "vue";
import dayjs from "dayjs";
import { useMessages } from "@/hooks";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { pageVideoConfigs, updateVideoConfig} from "@/services/api";
import GroupModel from "@/views/StoreModule/StoreConfig/components/GroupModel.vue";
import mallConfiguration from "@/views/StoreModule/StoreConfig/hooks/mallConfiguration"
import { hasContentConfigVideoConfigEdit } from "@/views/ContentModule/ContentConfig/authList";
const {selectList} = mallConfiguration()
const { createMessageSuccess, createMessageError } = useMessages();
/** props */
/* 表格方法Hook */
const {
  isLoading,
  tableData,
  paginationRef,
  pageTableData,
  paginationChange,
  sortTableData,
} = useTableDefault({
  pageDataRequest: pageVideoConfigs,
});

/* 表格项 */
const tableColumns = [
  {
    title: "配置名",
    key: "configName",
    width: 100,
    align: 'left',
    resizable: true,
  },
  {
    title: "配置编码",
    key: "key",
    align: "left",
    summaryTitle: "",
    width: 100,
  },
  {
    title: "配置值",
    key: "value",
    align: "left",
    summaryTitle: "",
    width: 100,
    render: rowData => {
      if (rowData.type === 5){
        return <n-tag size='small' type={rowData.value == 'true' ? 'success' : 'error'} bordered={false}>
          {rowData.value == 'true' ? '是' : '否'}
        </n-tag>
      }else if (rowData.type === 8) {
        if (selectList[rowData.key][0].value == 1){
          return <span>{selectList[rowData.key][rowData.value - 1].label}</span>;
        }else {
          return <span>{selectList[rowData.key][rowData.value].label}</span>;
        }
      }else if (rowData.type === 10) {
        return <span>{rowData.value?JSON.parse(rowData.value).name:'-'}</span>;
      } else {
        return <span>{rowData.value}</span>
      }

    },
  },
  {
    title: "描述",
    key: "desc",
    align: "left",
    summaryTitle: "",
    width: 160,
  },
  {
    title: "更新者",
    key: "updateName",
    align: "left",
    summaryTitle: "",
    width: 100,
  },
  {
    title: "更新时间",
    key: "updateTime",
    align: "left",
    summaryTitle: "",
    width: 100,
    render: rowData => {
      return `${dayjs(rowData.updateTime).format('YYYY-MM-DD HH:mm')}`
    }
  },
  {
    title: "操作",
    key: "action",
    width: 120,
    align: "left",
    fixed: "right",
    render: rowData => {
      if (hasContentConfigVideoConfigEdit){
        return (
          <n-space align="center" justify="center">
            <n-button text size="small" onClick={() => rechargeRecords(rowData)} type="primary">
              编辑配置值
            </n-button>
          </n-space>
        );
      }else {
        return <span>-</span>;
      }
    },
  },
];

/** 排序 */
const tableSorterChange = (info: { sort: string, sortAsc: "ascend" | "descend" }) => {
  sortTableData(info.sort, info.sortAsc);
};

/** 选中行数据 */
const rowData = ref([]);

/** 获取选中行Key */
function selectedKeysChange(key, tableData) {
  rowData.value = tableData.map(({ _dummyId, ...rest }) => rest);
}

/* 刷新列表 */
const tableSearch = () => {
  pageTableData({}, paginationRef.value);
};
const groupModel = ref()
const rechargeRecords = (rowData: any) => {
  groupModel.value.showModal({ ...rowData,api:updateVideoConfig})
}

onMounted(() => {
  tableSearch()
});
</script>

<style lang="less" scoped>
@import "@/styles/default.less";
:deep(.tagId .n-base-selection-input__content) {
  font-size: 10px;
}

.wrapper {
  width: 100%;
  height: 100%;
}
</style>
