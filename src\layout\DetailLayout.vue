<template>
    <div class="detail-layout-wrapper inner-page-height" v-bind="$attrs">
        <n-card size="small" :bordered="false" style="margin-bottom: 4px;" content-style="padding:5px 12px;">
            <div class="header-wrapper">
                <n-button text class="mr-12" @click="emits('onGoBackBtnClick')">
                    <template #icon>
                      <ArrowBackOutline size="18" />
                    </template>
                </n-button>
                <p>
                    <slot name="header"></slot>
                </p>
            </div>
        </n-card>
        <div class="content-wrapper">
            <n-scrollbar style="max-height: 100%;">
                <slot></slot>
            </n-scrollbar>
        </div> 
    </div>
</template>
<script setup lang="ts" name="DetailLayout">
import { ArrowBackOutline } from "@vicons/ionicons5";

const emits = defineEmits<{
    (e:"onGoBackBtnClick"):void
}>()

</script>
<style lang="less" scoped>
@import "@/styles/default.less";
@import "@/styles/defaultVar.less";
.detail-layout-wrapper {
  width: 100%;
  .header-wrapper{
    height: 28px; 
    display: flex; 
    align-items: center;
    overflow: hidden;
  }
  .content-wrapper{
    border-radius: @default-border-radius;
    overflow: hidden;
    height: calc(100% - 38px - 4px);
  }
}
:deep(.n-scrollbar-content){
    height: 100%;
}
</style>