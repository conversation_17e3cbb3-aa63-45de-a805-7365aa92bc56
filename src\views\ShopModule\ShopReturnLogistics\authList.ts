import { ShopReturnGoodsLogisticsAuth } from "@/enums/authKeys";
import { hasAuth } from "@/utils/auth/authUtils";

/** 门店退货物流导出 */
export const hasExportAuth = (function () {
  return hasAuth(ShopReturnGoodsLogisticsAuth.export.key);
})()

/** 详情 */
export const hasDetailsAuth = (function () {
  return hasAuth(ShopReturnGoodsLogisticsAuth.details.key);
})()

/** 查看物流 */
export const hasLogisticsAuth = (function () {
  return hasAuth(ShopReturnGoodsLogisticsAuth.logistics.key);
})()
/** 审核 */
export const hasReviewAuth = (function () {
  return true
  // return hasAuth(ShopReturnGoodsLogisticsAuth.review.key);
})()
