<template>
    <div class="wrapper inner-page-height">
        <FormLayout
          :isLoading="isLoading"
          :tableData="tableData"
          :tableColumns="tableColumns"
          :pagination="paginationRef"
          @paginationChange="paginationChange"
          :isNeedCollapse="false"
          :isTableSelection="false"
          :isDisplayIndex="false"
        >
          <template #tableHeaderBtn>
            <n-button @click="refresh" class="store-button">刷 新</n-button>
            <JAddButton  type="primary" @click="handlePharmacistTreatment('add')">新增药师</JAddButton>
          </template>
        </FormLayout>
        <PharmacistsDrawer ref="pharmacistsDrawerShow"/>
    </div>
</template>
<script lang="tsx" setup>
import { onMounted, ref } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import PharmacistsDrawer from './compoments/pharmacistsDrawer.vue'
import { pharmacistEntityPage } from "@/services/api";
import { OfficeLabels } from "@/constants";
import { hasPharmacistManagementAddAuth, hasPharmacistManagementEditAuth} from "./authList";
/** 表格hook */
const {
   isLoading,
   tableData,
   paginationRef,
   pageTableData,
   paginationChange,
 } = useTableDefault({
   pageDataRequest: pharmacistEntityPage,
});

/* 表格列表项 */
const tableColumns = ref([
  {
    title: "姓名",
    key: "pharmacistName",
    width: 150,
    align: "left",
  },
  {
    title: "职务",
    key: "title",
    width: 250,
    align: "left",
    render: rowData => {
        return <span>{OfficeLabels[rowData.title] ?? '-'}</span>;
    },
  },
  {
    title: "启用状态",
    key: "isActived",
    width: 150,
    align: "left",
    render: rowData => {
        let value 
        if(rowData.isActived){
          value = '是'
        }
        if(!rowData.isActived){
          value = '否'
        }
        return value
    },
  },
  {
    title: "创建时间",
    key: "createTime",
    width: 150,
    align: "left",
  },
  {
    title: "操作",
    key: "action",
    width: 100,
    fixed: "right",
    align: "left",
    render: row => {
      return (
        <n-space style="padding: 5px 0;">
          {<n-button text type="primary" onClick={() => handlePharmacistTreatment('edit',row)}>
            编辑
          </n-button>}
        </n-space>
      );
    },
  },
]);


/** 表格刷新 */
function refresh(){
  tableSearch();
};

/* 表格搜索 */
const tableSearch = () => {
   pageTableData({},paginationRef.value);
};

/** 打开新建医生抽屉 */
const pharmacistsDrawerShow = ref()
const handlePharmacistTreatment = (type,row) => {
     const _params = {
      type,
      row,
      refresh:refresh
    }
    pharmacistsDrawerShow.value?.acceptParams(_params);
};

/** 组件挂载 */
onMounted(() => {
   tableSearch();
});
</script>