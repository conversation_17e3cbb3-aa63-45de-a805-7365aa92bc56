<template>
  <n-spin :show="setLoading">
    <div class="wrapper inner-page-height">
      <div class="content_container">
        <StoreTitle title="门店分佣规则" style="margin-bottom: 12px;" />
        <NForm
          ref="formRef"
          :model="model"
          :rules="rules"
          label-placement="left"
          label-width="120"
          label-align="left"
          size="small"
        >
          <NFormItem label="分佣对象：">
            <NRadioGroup
              v-model:value="model[StoreCommissionRuleKey.COMMISSION_TARGET]"
              @update:value="handleCommissionTargetChange"
            >
              <NFlex :size="20">
                <NRadio :value="1">不分佣</NRadio>
                <NRadio :value="2">店长店员</NRadio>
                <NRadio :value="3">门店归属经销商</NRadio>
              </NFlex>
            </NRadioGroup>
          </NFormItem>

          <NFormItem label="分佣规则：" v-if="model[StoreCommissionRuleKey.COMMISSION_TARGET] !== 1">
            <NRadioGroup v-model:value="model[StoreCommissionRuleKey.COMMISSION_RULE]">
              <NFlex vertical :size="[0,20]">
                <NFlex vertical>
                  <NRadio :value="1" :disabled="model[StoreCommissionRuleKey.COMMISSION_TARGET] === 3">
                    按订单商品总额实付款计算佣金
                  </NRadio>
                  <NFlex :size="[30,0]" v-if="model[StoreCommissionRuleKey.COMMISSION_TARGET] === 2">
                    <NFlex align="center">
                      店长
                      <n-input-number
                        :precision="2"
                        v-model:value="model[StoreCommissionRuleKey.STORE_MANAGER_COMMISSION]"
                        placeholder="店长分账比例"
                        :show-button="false"
                        :min="0"
                        :max="100 - (model[StoreCommissionRuleKey.STORE_STAFF_COMMISSION] || 0)"
                        style="width: 80px;"
                        size="small"
                      />
                      %
                    </NFlex>

                    <NFlex align="center">
                      店员
                      <n-input-number
                        :precision="2"
                        v-model:value="model[StoreCommissionRuleKey.STORE_STAFF_COMMISSION]"
                        placeholder="店员分账比例"
                        :show-button="false"
                        :min="0"
                        :max="100 - (model[StoreCommissionRuleKey.STORE_MANAGER_COMMISSION] || 0)"
                        style="width: 80px;"
                        size="small"
                      />
                      %
                    </NFlex>
                  </NFlex>
                </NFlex>

                <NFlex vertical>
                  <NRadio :value="2" :disabled="model[StoreCommissionRuleKey.COMMISSION_TARGET] === 2">
                    按商品维度设置的分佣比例分佣
                  </NRadio>
                  <div style="font-size: 14px; color: #999;">注：按每个商品的实付款计算佣金</div>
                </NFlex>
              </NFlex>
            </NRadioGroup>
          </NFormItem>

          <NFormItem label="分佣结算天数：" v-if="model[StoreCommissionRuleKey.COMMISSION_TARGET] !== 1">
            <NFlex vertical>
              <n-input-number
                :precision="0"
                v-model:value="model[StoreCommissionRuleKey.SETTLEMENT_DAYS]"
                placeholder="结算天数"
                :show-button="false"
                :min="0"
                :max="365"
                style="width: 100px;"
              />
              <div style="font-size: 14px; color: #999;">
                当订单完成n天后，该订单的分销佣金才会结算到分销商余额，如果设置为0天则订单完成时就结算
                <br />
                建议佣金结算天数大于订单完成后允许发起售后申请的天数，如果用户申请退款成功（含部分退款）则不结算佣金
              </div>
            </NFlex>
          </NFormItem>

          <NFormItem label="打款方式：" v-if="model[StoreCommissionRuleKey.COMMISSION_TARGET] !== 1">
            <NRadioGroup v-model:value="model[StoreCommissionRuleKey.PAYOUT_MODE]">
              <NRadio :value="1">用户提现</NRadio>
              <NRadio :value="2">线下结算</NRadio>
              <div style="font-size: 14px; color: #999;">注：选择线下结算，用户账户余额将不体现佣金收入</div>
            </NRadioGroup>
          </NFormItem>
        </NForm>
      </div>

      <div class="footer-wrapper">
        <NButton v-if="hasStoreCommissionRuleAuth" type="info" @click="_confirm" :loading="setLoading"  style="width: 80px;">保 存</NButton>
      </div>
    </div>
  </n-spin>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useMessages } from "@/hooks";
import type { FormItemRule, FormInst } from "naive-ui";
import { commissionRule, saveCommissionRule } from "@/services/api";
import { StoreCommissionRuleKey } from "./type";
import { hasStoreCommissionRuleAuth } from "@/views/StoreModule/Finance/authList";

interface StoreCommissionRuleConfig {
  [StoreCommissionRuleKey.COMMISSION_TARGET]: number;
  [StoreCommissionRuleKey.COMMISSION_RULE]: number;
  [StoreCommissionRuleKey.STORE_STAFF_COMMISSION]: number;
  [StoreCommissionRuleKey.STORE_MANAGER_COMMISSION]: number;
  [StoreCommissionRuleKey.SETTLEMENT_DAYS]: number;
  [StoreCommissionRuleKey.PAYOUT_MODE]: number;
}

onMounted(() => {
  getCommissionRule();
});

const configList = ref<any[]>([]);

/** 获取门店分佣规则 */
const getCommissionRule = async () => {
  setLoading.value = true;
  try {
    const data = await commissionRule();
    configList.value = data.records;
    console.log(data);
    data.records.forEach(item => {
      model.value[item.key] = Number(item.value);
    });
  } catch (error) {
    createMessageError("获取门店分佣规则失败：" + error);
  } finally {
    setLoading.value = false;
  }
};

const _confirm = async () => {

    setLoading.value = true;
    // TODO: 调用API保存配置
    configList.value.forEach(item => {
      item.value = model.value[item.key];
    });
    saveCommissionRule(
      { data: configList.value }
    ).then(res => {
      console.log(res);
    }).catch(err => {
      createMessageError(`保存失败：${err}`);
    }).finally(() => {
      setLoading.value = false;
    })
};

const { createMessageSuccess, createMessageError } = useMessages();
const formRef = ref<FormInst | null>(null);

const model = ref<StoreCommissionRuleConfig>({
  [StoreCommissionRuleKey.COMMISSION_TARGET]: 1,
  [StoreCommissionRuleKey.COMMISSION_RULE]: 2,
  [StoreCommissionRuleKey.STORE_STAFF_COMMISSION]: 3,
  [StoreCommissionRuleKey.STORE_MANAGER_COMMISSION]: 0.5,
  [StoreCommissionRuleKey.SETTLEMENT_DAYS]: 15,
  [StoreCommissionRuleKey.PAYOUT_MODE]: 1
});

const setLoading = ref(false);

// 表单验证规则
const rules = {};

// 处理分佣对象变化
const handleCommissionTargetChange = (value: number) => {
  if (value === 2) {
    // 选择店长店员时，强制选择按订单计算佣金，并设置默认值
    model.value[StoreCommissionRuleKey.COMMISSION_RULE] = 1;
  } else if (value === 3) {
    // 选择门店归属经销商时，强制选择按商品维度分佣
    model.value[StoreCommissionRuleKey.COMMISSION_RULE] = 2;
  }
};
</script>

<style scoped lang="less">
@import "@/styles/defaultVar.less";

.wrapper{
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  .content_container {
    flex: 1;
    padding: 12px 16px;
    overflow-y: auto;
  }
  .footer-wrapper {
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    box-sizing: border-box;
    margin-right: 24px;
  }
}
</style>
