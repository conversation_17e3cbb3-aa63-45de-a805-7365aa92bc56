import { defHttp } from "@/services";

export const enum RTCApi{
  getRTCToken = "/hs/rtc/getRtcToken",
}

export const enum ConsultationStatusEnum {
    PENDING_PAYMENT = 1,    // 待支付
    PENDING_CONSULTATION = 2, // 待接诊
    IN_CONSULTATION = 3,    // 咨询中
    COMPLETED = 4,          // 已完成
    CANCELLED = 5           // 已取消
}

export interface RTCTokenResponse{
    "appId": string,
    "roomId": string,
    "userId": string,
    "token": string,
    "consultationStatus": ConsultationStatusEnum,
    'receiveEndTime'?: string,
}

export function getRTCToken(presId:string) {
  return defHttp.get<RTCTokenResponse>({
    url: RTCApi.getRTCToken,
    params:{presId},
  });
}
