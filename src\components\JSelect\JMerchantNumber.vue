<!-- 商户号 -->
<template>
    <JSelect
      :value="props.value"
      :loading="isLoading"
      :onFocus="handlerFocus"
      :options="doctorList"
      :onClear="handleClear"
      @update:value="onChange"
      placeholder="请选择商户号"
    />
  </template>
  <script setup lang="ts">
  import { ref, watch } from "vue";
  import JSelect from "@/components/JSelect/index.vue";
  import { merchantListFuiouEnabledMers } from "@/services/api";
  import { useMessages } from "@/hooks";
  
  /* Props */
  const props = withDefaults(
    defineProps<{
      isImmediately?: boolean; // 是否立即加载
      value: Array<string | number> | string | number | null; // 选择的值
      payPlatform:number
    }>(),
    {
      isImmediately: false,
    },
  );
  
  const emits = defineEmits<{
    (e: "update:value", selectValue: any): void; // 更新选择值事件
  }>();
  
  /* 提示 */
  const message = useMessages();
  
  /* 是否加载 */
  const isLoading = ref(false);
  const doctorList = ref([]); // 列表
  
  /* 筛选、转化{label: '', value: ''} */
  function handleData(filterData: AddressModule.AddressEntity[]) {
    let dataList = [];
    dataList = filterData.map(item => {
      return { 
          label: item, 
          value: item
        };
      });
    return dataList;
  }
  
  /* 获取商户号列表 */
  async function getDoctorList() {
    try {
      isLoading.value = true;
      const data = await merchantListFuiouEnabledMers(props.payPlatform);
      handleData(data).forEach(item => {
          doctorList.value.push(item);
      });
    } catch (error) {
      message.createMessageError("获取商户号信息失败：" + error);
    } finally {
      isLoading.value = false;
    }
  }
  
  /** 选择值改变事件处理函数 */
  function onChange(value,text) {
    emits("update:value", value);
  }
  
  
  /** 清空事件处理函数 */
  const handleClear = () => {
    emits("update:value", null);
  };
  /** 聚焦事件处理函数 */
  function handlerFocus() {
    // 如果开户行列表为空
    if (!doctorList.value.length) {
      getDoctorList();
    }
  }
  
  /** 监听 */
  watch(
    () => props.isImmediately,
    newVal => {
      if (newVal) {
        getDoctorList();
      }
    },
    { immediate: true },
  );

  /** 监听支付渠道 */
  watch(
    () => props.payPlatform,
    newVal => {
        doctorList.value.length = 0
        emits("update:value", null);
      }
  );
  
  </script>
    
  <style scoped lang="less"></style>