/**
 * @description 系统参数KEY
 */
export const enum SystemEnum {
    // 积分商城使用
    STO_POINT_ENABLE = "sto_point_enable",
    // 商城类型
    STO_MARKETPLACE_TYPE = "sto_marketplace_type"
}

// 商城类型 1: 普通商城 2: 医药商城 3:医师、药师后台
export type StoreModelType = 1 | 2 | 3;
export type StoreVisitDataType = 0 | 1 | 2 | 3;
export type FirstLoginType = 0 | 1;
export type EasyPasswordsType = 0 | 1;
export type authRoleType = 10 | 11 | 12;

/**
 * @description 系统商城类型
 */
export const enum SystemStoreType {
    /** 普通商城 */
    GENERALMALL = 1,
    /** 医药商城 */
    PHARMACEUTICALMALL = 2,
    /** 医师端*/
    DOCTOREND = 3
}

export const enum ManagementType {
    /** 全平台数据 */
    FULLPLATFORMDATA = 0,
    /** 经销商数据 */
    DEALERDATA = 1,
    /** 群管数据 */
    GROUPMANAGEMENTDATA = 2,
    /** 分销员 */
    DISTRIBUTIONMALL = 3
}

/**
 * @description 系统配置
 */
export type GlobalConfig = {
    // 商城类型 1: 普通商城 2: 医药商城 3:医师、药师后台
    marketplaceType?: StoreModelType;
    // 是否开启T9商城订单同步
    jtintegration?: boolean;
    // 是否开启虚拟商品配置
    virtualProduct?: boolean;
    // 数据权限 0=全平台数据；1=经销商数据；2=群管数据；3=分销员数据
    visitDataType?: StoreVisitDataType,
    // 账号密码登录状态
    isInitialPassword?: FirstLoginType,
    // 账号密码简易状态
    isSimplePassword?: EasyPasswordsType
    // 角色状态 10=小程序后台角色 11=超级管理员角色 12=开发者角色
    authRoleType?: authRoleType
    // OSS域名
    ossDomain?: string;
    // 是否必填身份证
    isAddIdNo?: string;
}

/**
 * @description 操作员
 */
export const enum SystemOperatorType {
    /* 平台操作员 **/
    SYSTEM = 'SYSTEM',
}

/**
 * @description 操作员数据权限
 */
export const enum VisitDataType {
    // 全平台数据
    AllPlatformData = 0,
    // 经销商数据
    DealerData = 1,
    // 群管数据
    GroupData = 2,
    // 分销员数据
    DistributorsData = 3,
    // 医生数据
    DoctorData = 4,
    /** 供应商数据 */
    SupplierData = 5
}

export const VisitDataMap = {
    "全平台数据": VisitDataType.AllPlatformData,
    "经销商数据": VisitDataType.DealerData,
    "群管数据": VisitDataType.GroupData,
    "分销员数据": VisitDataType.DistributorsData,
    "医生数据": VisitDataType.DoctorData,
    "供应商数据": VisitDataType.SupplierData
}

/**
 * @description 系统角色类型
 */
export const enum SystemRoleType {
    /* 基础系统角色 **/
    BASESYSTEMROLE = 1,
    /** 小程序后台角色 */
    SMALLPROGRAMROLE = 10,
    /** 小程序超级管理员 */
    ADMINISTRATOR = 11,
    /** 小程序开发者角色 */
    SMALLPROGRAMDEVROLE = 12,
    /** 社群平台角色 */
    SOCIALPLATFORMROLE = 20,
    /** 社群经销商角色 */
    COMMUNITYDEALERROLE = 21,
}

/**
 * @description 积分商品
 */
export const enum PointsGoodsType {
    // 1 是
    isPoints = 1,
    // 0 否
    noPoints = 0,
}

export type GoodsType = 1 | 2 | 3 | 4 | 5 | 9;

/**
 * @description 商品分类
 */
export const enum GoodsCategoryType {
    // 1：药品
    DRUG = 1,
    // 2：疗法
    THERAPY = 2,
    // 3：普通商品
    GENERAL = 3,
    // 4：积分商品
    INTEGRAL = 4,
    // 5：积分标签
    INTEGRALLABEL = 5, // 业务需要，数据表并无type
    // 全部
    ALL = 9,
}

/**
 * @description 普通商品是否虚拟商品
 */
export const enum GoodsIsVirtualType {
    // 1 是
    ISVIRTUAL = 1,
    // 0 否
    NOVIRTUAL = 0,
}
/**
 * @description 提货方式
 */
export const enum PickUpGoodsType {
    // 邮寄
    EXPRESSAGE = 1,
    // 自提
    PICKUP = 2
}
/** 
 * @description 自提子项
 * 1: 下单后待核销
 * 2: 下单自动核销
 * 3: 下单门店到货后核销
 */
export const enum PickUpGoodsSubType {
    // 下单后待核销
    ORDER = 1,
    // 下单自动核销
    AUTOMATIC = 2,
    // 下单门店到货后核销
    STOREARRIVAL = 3
}

export type ReportsType = 1 | 2 | 3 | 4 | 5 | 6;

/**
 * @description 数据报表
 */
export const enum DataReportsType {
    /** 全平台订单 */
    ALLPLATFORMORDER = 1,
    /** 门店数据统计 */
    STORESTATISTICS = 2,
    /** 经销商数据统计 */
    DEALERDATASTATISTICS = 3,
    /** 商品销售榜 */
    COMMODITYSALES = 4,
    /** 会员消费榜 */
    MEMBERCONSUMPTION = 5,
    /** 社群群管销量 */
    COMMUNITYSALES = 6,
    /** 社群经销商销量 */
    COMMUNITYDEALERSALES = 7,
    /** 社群课程销量 */
    COMMUNITYCOURSES = 8,
}

export type StoreConfigType = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10;

/**
 * @description 数据报表
 */
export const enum StoreConfigMenuType {
    SLIDESHOW = 1,
    ADDRESS = 2,
    DEAL = 3,
    DEVELOP = 4,
    HOMELOGO = 5,
    APPLETNAV = 6,
    AUTHROLE = 7,
    OPERATOR = 8,
    DELIVERTOOL = 9,
    FLOW = 10,
}

export type SalesType = 1;

/**
 * @description 营销管理
 */
export const enum SalesManagementType {
    MEMBERPOINTS = 1,
    WELFARE_TICKET_CLASS = 2,
    WELFARE_TICKET_MANAGE = 3,
    WELFARE_GOODS_MANAGE = 4,
}

/**
 * @description 积分配置来源类型
 * 2: 购物返还
 * 3: 签到
 * 4: 每日来访
 * 5: 查看商品
 */
export const enum IntegralConfigType {
    SHOPPING = 2,
    SIGNIN = 3,
    DAILYVISIT = 4,
    VIEWPRODUCT = 5,
}

/**
 * @description 订单管理
 */
export const enum OrderManagementSelection {
    'all' = '0',//全部
    'waitFeliverGoods' = '2', // 待发货
    'waitTakeOverGoods' = '3', // 待收货
    'accomplish' = '4', // 已完成
    'waitPay' = '1',  // 待支付
    'cancelled' = '5'  // 已取消
}

/**
 * @description 处方管理
 */
export const enum PrescriptionSelection {
    'unopened' = '0', // 待开方
    'alreadyOpen' = '1', // 已开方
    'placeAnOrder' = '2',// 已下单 
    'uploaded' = '3', // 已上传
    'cancelled' = '4'  // 已取消
}

/**
 * @description 问诊处方
 */
export const enum MedicalInquiryPrescriptionSelection {
    'pendingReview' = '0', // 待审核
    'available' = '1', // 可使用
    'haveBeenUsed' = '2',// 已使用 
    'notApproved' = '3', // 审核不通过
    'lostEfficacy' = '4'  // 已失效
}

/**
 * @description 处方处理类型
 */
export const enum PrescriptionType {
    beginPrescription = '开处方',
    editPrescription = '编辑',
    detailsPrescription = '详情',
}

/**
 * @description 导入发货类型
 */
export const enum ImportShipmentType {
    startImporting = 0, //导入发货
    importInProgress = 1, //数据导入中
    importFailed = 2,//导入失败
    importSucceeded = 3,//导入成功
    deliveryResult = 4//发货结果
}

/**
 * @description 售后管理状态
 */
export const enum afterSaleStateType {
    /** 全部 */
    ALL = 0,
    /** 待受理 */
    TOBEPROCESSED = 1,
    /** 待收货 */
    WAITFORRECEIVING = 5,
    /** 待打款 */
    FUNDSTOBEREMITTED = 8,
}

/**
 * @description 订单统计
 */
export const enum OrderTotalReportTypeEnum {
    /** 按日 */
    Daily = 0,
    /** 按月 */
    Monthly = 1,
}

/**
 * @description 财务管理
 */
export const enum FinanceTypeEnum {
    /** 商户号管理 */
    Merchant = 1,
    /** 用户充值 */
    FinanceMenu = 2,
    /** 分账管理*/
    AccountManagement = 3,
    /** 物流充值 */
    LogisticsRecharge = 3,
}
/**
 * @description 分账管理
 */
export const enum AccountManagementTypeEnum {
    /** 分账方规则设置 */
    SubAccountPayeeRuleSetting = 0,
    /** 分账入账方管理 */
    SubAccountPayeeManagement = 1,
    /** 分账单管理 */
    SubBillManagement = 2,
    /** 分账单结算 */
    SplitBillSettlement = 3,
}
/**
 * @description 物流充值
 */
export const enum LogisticsRechargeTypeEnum {
    /** 轨迹查询余额 */
    TraceAccountBalance = 0,
    /** 轨迹调用 */
    TraceApiInvokeHistory = 1,
}
/**
 * @description 用户充值
 */
export const enum FinanceMenuType {
    RechargeRecord = 1,
    ExpenditureRecord = 2,
    AccountBalance = 3,
}
/**
 * @description 用户充值类型
 */
export const enum FinanceBusinessType {
    OfflineRecharge = 1, //线下充值
    OfflineRefund = 2, //线上退款
}

export type FinanceType = 1 | 2 | 3;
/**
 * @description 商户号管理
 */
export const enum MerchantTypeEnum {
    /** 商户号管理 */
    Merchant = 0,
}

/**
 * @description 分账规则
 */
export const enum SubAccountPayeeRuleSettingEnum {
    /** 按商品分账 */
    Commodity = 1,
    /** 按订单分账 */
    Order = 2,
    /** 分销-按总额分佣 */
    Distribution = 10,
}

/**
 * @description 商品销售场景
 */
export const enum ProductSalesSceneEnum {
    /** 通用（全选） */
    ALL = 0,
    /** 商城 */
    Store = 1,
    /** 社群 */
    Social = 2,
    /** 在商城中隐藏 */
    IsHideInStore = 3,
    /** 门店 */
    Storefront = 4,
}

/**
 * @description 福利商品兑换场景
 */
export const enum WelfareExchangeSceneEnum {
    /** 门店 */
    Storefront = 1,
}

/**
 * @description 在商城中隐藏
 */
export const enum IsHideInStoreEnum {
    /** 不隐藏 */
    NotHide = 0,
    /** 隐藏 */
    Hide = 1,
}

/**
 * @description 药物类型
 */
export const enum DrugTypeEnum {
    /** OTC */
    OTC = 0,
    /** 处方药 */
    Prescription = 1,
    /** 处方药 */
    ChineseHerbalMedicine = 2
}

/**
 * @description 视频管理
 */
export const enum VideoTypeEnum {
    /** 待审核 */
    AuditPending = 0,
    /** 审核通过 */
    AuditResolve = 1,
    /** 审核不通过 */
    AuditReject = 2
}

/**
 * @description 敏感词库
 */
export const enum LexiconTypeEnum {
    /** 官方敏感词库 */
    Official = 0,
    /** 自定义敏感词库 */
    Custom = 1,
}

/**
 * @description 内容配置
 */
export const enum ContentTypeEnum {
    /** 视频配置 */
    VideConfig = 0,
}

/**
 * @description 评论管理
 */
export const enum CommentTypeEnum {
    /** 待审核 */
    AuditPending = 0,
    /** 审核通过 */
    AuditResolve = 1,
    /** 审核不通过 */
    AuditReject = 2
}

/**
 * @description 财务管理
 */
export const enum financeSaleStateType {
    /** 全部 */
    ALL = 0,
    /** 待受理 */
    TOBEPROCESSED = 1,
}

/**
 * @description 组织架构
 */
export const enum OrganizationalStructure {
    /** 创建一级组织 */
    FirstLevelOrganization = '创建一级组织',
    /** 创建子组织 */
    SubOrganizations = '创建子组织',
    /** 编辑 */
    Edit = '编辑组织',
    /** 删除 */
    Delete = '删除组织'
}

/**
 * @description 部门类型
 */
export const enum DepartmentType {
    /** 经销商 */
    DEALER = 0,
    /** 门店 */
    STORE = 1,
    /** 公司 */
    COMPANY = 2,
    /** 直播间 */
    LIVE_ROOM = 3,
    /** 市场 */
    MARKET = 4,
    /** 运营 */
    OPERATION = 5,
    /** 大区 */
    REGION = 6,
    /** 区域 */
    AREA = 7,
    /** 财务 */
    FINANCE = 8
}

/**
 * @description 组织架构状态类型
 */
export const enum DepartmentStatus {
    /** 停用状态 */
    DISABLED = 0,
    /** 启用状态 */
    ENABLED = 1
}

/**
 * @description 人员类型
 */
export const enum MemberType {
    /** 店长 */
    MANAGER = 1,
    /** 店员 */
    CLERK = 2,
    /** 门店用户 */
    STORE_USER = 3
}

/**
 * @description 分销员管理
 */
export const enum DistributorType {
    /** 添加分销员 */
    AddDistributor = '添加分销员',
    /** 冻结分销员 */
    FreezeDistributor = '冻结分销员',
    /** 解冻分销员 */
    ThawDistributor = '解冻分销员',
    /** 删除分销员 */
    DeleteDistributor = '删除分销员',
    /** 归属组织设置 */
    AttributionOrganizationSettings = '归属组织设置'
}

/**
 * @description 发货异常订单
 */
export const enum DeliveryExceptionType {
    /** 待处理 */
    ToBeProcessed = 0,
    /** 已处理 */
    Processed = 1,
}

/**
 * @description 发货渠道
 */
export const enum DeliveryChannelEnum {
    /** 中通快递管家 */
    ZTExpress = 1,
}

/**
 * @description 异常订单状态
 */
export const enum DeliveryExceptionStateEnum {
    /** 待创建 */
    ToBeCreated = 11,
    /** 已创建 */
    Created = 12,
    /** 创建失败 */
    CreateFailed = 13,
    /** 发货成功 */
    DeliverySuccess = 21,
    /** 发货失败 */
    DeliveryFailed = 22,
    /** 标记为已处理 */
    MarkedAsProcessed = 23,
}

/**
 * @description 首次登录
 */

export const enum FirstLogin {
    /** 不是首次登录 */
    isNotirstLogin = 0,
    /** 是首次登录 */
    isFirstLogin = 1

}

/**
 * @description 简易密码
 */
export const enum EasyPasswords {
    /** 不是简易密码 */
    sEasyPasswords = 0,
    /** 是简易密码 */
    isEasyPasswords = 1
}


/**
 * @description 敏感数据
 */
export const enum SensitiveType {
    /** 不可查看 */
    notViewable = 0,
    /** 可查看 */
    viewable = 1
}

/**
 * @description 角色状态
 */
export const enum AuthRoleType {
    /** 小程序 */
    miniPrograms = 10,
    /** 超级管理员 */
    superManagement = 11,
    /** 开发者 */
    developers = 12
}


/**
 * @description 处方管理
 */
export const enum DoctorEndReceptionSelection {
    'reception' = "reception", // 待接诊
    'processing' = 'processing', // 处理中
    'completed' = 'completed',// 已完成
    'wait_finish_video' = 'wait_finish_video' // 待完成视频单
}


/**
 * @description 医生端处方管理
 */
export const enum DoctorEndPrescriptionSelection {
    'waitCheck' = '0', // 待审核
    'canUse' = '1', // 可使用
    'haveUsed' = '2',// 已使用   
    'checkNoPassed' = '3', // 审核不通过
    'lostEfficacy' = '4',  // 已失效
    'all' = 5 // 全部
}
/**
 * @description 销售场景门店商品类型
 */
export const enum SalesSceneStoreGoodsTypeEnum {
    /** 普通商品 */
    Normal = 1,
    /** 福利商品 */
    Welfare = 2
}

/**
 * @description 门店可见范围
 */
export const enum StoreVisibleRangeEnum {
    /** 全部 */
    All = '0',
    /** 普通用户 */
    Normal = '1',
    /** 店员&店长 */
    StoreKeeper = '2',
    /** 不可见 */
    NotVisible = '3'
}

/**
 * @description 门店店铺(全部)
 */
export const enum StoreShopEnum {
    /** 全部 */
    All = '0',
}

/**
 * @description 支付服务商
 */
export const enum PayServiceEnum {
    /** 微信支付 */
    WeChatPay = 1,
    /** 富友支付 */
    FuiouPay = 2,
    /** 中金支付 */
    ZhongJinPay = 3,
    /** 汇聚支付 */
    HuiJuPay = 4
}

/**
 * @description 验签模式
 */
export const enum SignatureModeEnum {
    /** 平台证书模式 */
    PlatformCertificate = 1,
    /** 公钥模式 */
    PublicKey = 2
}

/**
 * @description 订单支付方式
 */
export const enum OrderPayTypeEnum {
    /** 暂无 */
    NONE = 0,
    /** 在线支付 */
    ONLINE = 1,
    /** 物流代收 */
    LOGISTICS = 2,
    /** 支付定金 */
    DEPOSIT = 3,
    /** 纯积分支付 */
    POINTS = 4,
    /** 积分 + 现金 */
    POINTS_AND_CASH = 5,
    /** 福利券兑换 */
    COUPON = 6
}

/**
 * @description 订单状态
 */
export const enum OrderStatusEnum {
    /** 待支付 */
    Unpaid = 1,
    /** 待发货 */
    Unshipped = 2,
    /** 待收货 */
    Unreceived = 3,
    /** 已完成 */
    Completed = 4,
    /** 已取消 */
    Canceled = 5,
}

/**
 * @description 门店-门店退货物流
 */
export const enum ShopReturnLogisticsEnum {
    /** 待审核 */
    WaitingForReview = 'needMyApproval',
    /** 进行中 */
    InProgress = 'running',
    /** 已完成 */
    Completed = 'approved',
    /** 审核不通过 */
    NotPassed = 'rejected',
}

/**
 * @description 店铺管理订单类型
 */
export const enum StoreOrderTypeEnum {
    /** 普通订单 */
    NORMAL = 1,
    /** 代下单订单 */
    ORDER_BY_OTHERS = 2,
    /** 积分订单 */
    INTEGRAL = 3,
    /** 福利券订单 */
    COUPON = 4,
}

/**
 * @description 店铺管理商品类型
 */
export const enum StoreGoodsTypeEnum {
    /** 普通商品 */
    REGULAR_PRODUCTS = 3,
    /** 积分商品 */
    POINT_PRODUCT = 4,
    /** 福利券商品 */
    COUPON = 5,
}