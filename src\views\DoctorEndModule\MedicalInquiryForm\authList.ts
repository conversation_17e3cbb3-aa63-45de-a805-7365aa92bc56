import { hasAuth } from "@/utils/auth/authUtils";
import { MedicalInquiryFormManagementAuth } from "@/enums/authKeys";

/** 详情 */
export const hasManagementDetails = function(){
    return true
    // return hasAuth(MedicalInquiryFormManagementAuth.Details.key);
}()

/** 退款 */
export const hasManagementRefund = function(){
    return true
    // return hasAuth(MedicalInquiryFormManagementAuth.Refund.key);
}()

/** 导出 */
export const hasManagementExport = function(){
    return true
    // return hasAuth(MedicalInquiryFormManagementAuth.Refund.key);
}()
