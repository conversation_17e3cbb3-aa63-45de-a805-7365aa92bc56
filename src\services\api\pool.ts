import { defHttp } from "@/services";

export function getPoolPrefixLink(url:string){
  try{
    return `${import.meta.env.VITE_DOMIAN_API}${url}`
  }
  catch(e){
    return `${import.meta.env.VITE_DOMIAN_API}${url}`
  }
}



const enum PoolApiEnum {
  createPoolLink = "/url/create",
}


interface GetPoolLinkProps{
    scene:number,
    dealerId:string,
    groupId:string,
    state:string
}


export async function getPoolLink(params:GetPoolLinkProps,originUrl:string) {
  return defHttp.get({
    url: getPoolPrefixLink(PoolApiEnum.createPoolLink),
    params,
    options:{
        headers:{
            ['x-referer']:originUrl
        }
    },
    requestConfig: {
      isQueryParams: true,
      withToken: true,
    },
  });
}
