export type MenuAuthTableDataItem = {
    id:string,
    type:number,
    code:string,
    moduleType:string,
    moduleName?:string,
    menuName?:string,
    authName?:string,
    authList?:Array<MenuAuthTableDataOptTypeItem>,
    children?:Array<MenuAuthTableDataMenuTypeItem>,
}
export type MenuAuthTableDataMenuTypeItem = {
    id:string,
    code:string,
    type:number,
    menuName:string,
    authList?:Array<MenuAuthTableDataOptTypeItem>,
}
export type MenuAuthTableDataOptTypeItem = {
    id:string,
    type:number,
    authName:string,
}

export type MenuAuthDataResponseItem = {
    id:string,
    code:string,
    name:string,
    type:1 | 2 | 3,
    level:1 | 2 | 3,
    children?:Array<MenuAuthDataResponseItem>,
}

export type MenuAuthDataRecord = Record<string,Omit<MenuAuthDataResponseItem,'children'> & {
    parentCode?:string,
    childrenCodeList?:Array<string>,
    isCascade:boolean
}>