<template>
  <div class="daily-order-wrapper">
    <div class="daily-order-header">
      <NFlex justify="space-between">
        <NForm
          ref="formRef"
          :model="model"
          :show-feedback="false"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          size="small"
        >
          <NFormItem label="统计区间">
            <JDateRangePicker
              v-model:value="model.rangeTime"
              style="flex: 1;"
              type="monthrange"
              format="YYYY-MM"
              :default-time="['00:00:00', '23:59:59']"
              :clearable="false"
              :maxDays="366"
              isMonthDisableRight
            />
          </NFormItem>
        </NForm>
        <NFlex>
          <NButton @click="_refresh" class="store-button">刷 新</NButton>
          <NButton type="primary" :loading="exportLoading" v-if="hasReportsOrderExportAuth" @click="exportOrderReports">导出查询到的数据</NButton>
        </NFlex>
      </NFlex>
    </div>
    <div class="daily-order-content">
      <OrderSelect v-model:value="VerticalSelected" style="margin-bottom: 12px;" />
      <div class="content">
        <transition name="fade-transform" appear mode="out-in">
          <OrderReportTable
            v-if="VerticalSelected === OrderWayEnum.REPORT"
            v-model:model="model"
            ref="orderReportTableRef"
          />
          <OrderReportChart v-else v-model:model="model" ref="orderReportChartRef" />
        </transition>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup name="MonthlyOrderTotalReport">
import { ref } from "vue";
import { NForm, NFormItem, NFlex, NButton } from "naive-ui";
import dayjs from "dayjs";
import { useLoading, useMessages } from "@/hooks";
import { OrderTotalReportTypeEnum } from "@/enums";
import { OrderWayEnum } from "../../type";
import { exportOrderReportsPage } from "@/services/api";
/** 相关组件 */
import OrderSelect from "../OrderSelect/index.vue";
import OrderReportTable from "../OrderReportTable/index.vue";
import OrderReportChart from "../OrderReportChart/index.vue";
import { hasReportsOrderExportAuth  } from "@/views/StoreModule/DataReports/authList"

const message = useMessages();

/* 初始化参数 */
const initParams = {
    rangeTime: [
        dayjs().startOf('month').subtract(11, 'month').valueOf(),
        dayjs().endOf("month").valueOf(),
    ],
    type: OrderTotalReportTypeEnum.Monthly,
    orderBy: 'reportDate',    // 排序字段
    isAsc: false,     // 是否升序
};
const model = ref({ ...initParams });

/** 表格实例 */
const orderReportTableRef = ref<InstanceType<typeof OrderReportTable> | null>(null);
/** 图表实例 */
const orderReportChartRef = ref<InstanceType<typeof OrderReportChart> | null>(null);

const VerticalSelected = ref(OrderWayEnum.REPORT);

function _refresh() {
    if (VerticalSelected.value === OrderWayEnum.REPORT) {
        orderReportTableRef.value?._refresh && orderReportTableRef.value?._refresh()
    } else {
        // 触发子组件事件
        orderReportChartRef.value?._refresh && orderReportChartRef.value?._refresh()
    }
}

/** 获取参数 */
function getParams() {
    const { rangeTime, isAsc, type, orderBy } = model.value;
    const [startTime, endTime] = rangeTime;
    return {
        startTime: dayjs(startTime).format('YYYY-MM-DD 00:00:00'),
        endTime: dayjs(endTime).format('YYYY-MM-DD 23:59:59'),
        isAsc,
        type,
        orderBy
    }
}

/** 导出 */
const { loading: exportLoading, startLoading, endLoading } = useLoading();
const exportOrderReports = () => {
    const _params = {
        data: { ...getParams() },
        pageVO: {
            current: orderReportTableRef.value?.paginationRef.current,
            size: orderReportTableRef.value?.paginationRef.pageSize
        }
    }
    startLoading();
    exportOrderReportsPage(_params).then(() => {
        message.createMessageExportSuccess("导出成功");
    }).catch((err) => {
        message.createMessageError("导出失败: " + err);
    }).finally(() => {
        endLoading();
    });
}
</script>

<style lang="less" scoped>
.daily-order-wrapper {
    width: 100%;
    height: 100%;
    padding: 12px;
    box-sizing: border-box;
    overflow: hidden;

    .daily-order-header {
        width: 100%;
        height: 36px;
    }

    .daily-order-content {
        width: 100%;
        height: calc(100% - 36px);
        box-sizing: border-box;
        display: flex;
        flex-direction: column;

        .content {
            flex: 1;
        }
    }
}
</style>
