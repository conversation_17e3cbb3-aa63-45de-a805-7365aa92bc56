<template>
  <div class="wrapper">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :isNeedCollapse="false"
      :isTableSelection="false"
      :isTablePagination="false"
      :isDisplayIndex="false"
      @selectedKeysChange="selectedKeysChange"
      @table-sorter-change="tableSorterChange"
    >
      <!-- 操作项 -->
      <template v-if="tableData?.length < 5 && hasOrderStoreconfigMiniprogramnavigationNew" #tableHeaderBtn>
        <JAddButton @click="rechargeRecords('')" type="primary">新增导航</JAddButton>
      </template>
    </FormLayout>
    <!-- 新建轮播图 -->
    <AppletNavigationGroup ref="appletNavigationGroup" />
  </div>
</template>

<script setup lang="tsx" name="CollectStatement">
import { onMounted, ref } from "vue";
import { useMessages } from "@/hooks";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import {
  addNavigationConfig,
  updateNavigationConfig,
  listNavigationConfig,
  deleteNavigationConfig,
} from "@/services/api";
import AppletNavigationGroup from "@/views/StoreModule/StoreConfig/components/AppletNavigationGroup.vue";
import { mallConfiguration } from "../hooks"
import {
  hasOrderStoreconfigMiniprogramnavigationNew,
  hasOrderStoreconfigMiniprogramnavigationEdit,
  hasOrderStoreconfigMiniprogramnavigationDelete,
} from "../hooks/authList";
const {pointPageList} = mallConfiguration()
const { createMessageSuccess, createMessageError } = useMessages();
/* 表格方法Hook */
const {
  isLoading,
  tableData,
  getTableData,
  refreshTableData,
  sortTableData,
} = useTableDefault({
  getDataRequest: listNavigationConfig,
});

/* 表格项 */
const tableColumns = [
  {
    title: "导航名称",
    key: "name",
    align: "left",
    summaryTitle:"",
    width: 160,
  },
  {
    title: "排序号",
    key: "sort",
    align: "left",
    summaryTitle:"",
    width: 160,
  },
  {
    title: "指向页面",
    key: "pointPage",
    align: "left",
    summaryTitle:"",
    width: 100,
    render: rowData => {
      return <span>{pointPageList[pointPageList.findIndex(e=>{return rowData.pointPage === e.value})].label}</span>
    },
  },
  {
    title: "操作",
    key: "action",
    width: 120,
    align: "left",
    fixed: "right",
    render: rowData => {
      return (
        <n-space align="center" justify="center">
          <n-button v-show={hasOrderStoreconfigMiniprogramnavigationEdit} text size="small" onClick={()=>rechargeRecords(rowData.id)} type="primary">
            修改
          </n-button>
          {hasOrderStoreconfigMiniprogramnavigationDelete?<n-popconfirm
            onPositiveClick={() => {DelNavigation(rowData.id)}}
          >
            {{
              trigger: () => (
                <a style={{ color: 'red', cursor: 'pointer' }}>删除</a>
              ),
              default: () => <span style={{width:'300px'}}>是否确定删除该数据？</span>
            }}
          </n-popconfirm>:null}
          <span v-show={!hasOrderStoreconfigMiniprogramnavigationEdit && !hasOrderStoreconfigMiniprogramnavigationDelete}>-</span>
        </n-space>
      );
    },
  },
];


/** 排序 */
const tableSorterChange = (info: { sort: string, sortAsc: "ascend" | "descend" }) => {
  sortTableData(info.sort, info.sortAsc);
};

/** 选中行数据 */
const rowData = ref([]);
/** 获取选中行Key */
function selectedKeysChange(key, tableData) {
  rowData.value = tableData.map(({ _dummyId, ...rest }) => rest);
}

/* 初始化参数 */
const initParams = {
  condition: '',
  gender: null,
  status: null,
  searchType:'name',
  tagIds: null
};
const model = ref({ ...initParams });

/* 刷新列表 */
const tableSearch = () => {
  getTableData({});
};
const appletNavigationGroup = ref()
const rechargeRecords = (id:any) =>{
  let row = {
    id:id,
    api:id?updateNavigationConfig:addNavigationConfig,
    refreshTable: tableSearch,
  }
  appletNavigationGroup.value.acceptParams(row)
}

const DelNavigation = (id) => {
  try {
    deleteNavigationConfig({ id })
    createMessageSuccess(`删除导航配置成功`);
    setTimeout(tableSearch,500)
  }catch (e) {
    createMessageError(`删除导航配置失败： ${e}`);
  }
}
/* 组件挂载 */
onMounted(() => {
  tableSearch()
});
</script>

<style lang="less" scoped>
@import "@/styles/default.less";
@import "@/styles/defaultVar.less";

.wrapper {
  width: 100%;
  height: 100%;
}

</style>
