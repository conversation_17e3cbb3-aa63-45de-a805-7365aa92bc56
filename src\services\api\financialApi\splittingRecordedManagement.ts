import { defHttp } from '@/services';

/** 分账入账方管理 */
export const enum SplittingRecordedApi {
    allocationAccountPage = "/allocationAccount/page",
    allocationAccountAdd = '/allocationAccount/add',
    allocationAccountDelete =  '/allocationAccount/delete',
    allocationAccountGetDetail = '/allocationAccount/getDetail',
    activateAccountGetDetail = '/allocationAccount/activateAccount',
    bankInfoListAccountBank = '/bankInfo/listAccountBank',
    bankInfoList = '/bankInfo/list',
    allocationAccountCardUpdateIsDefault = '/allocationAccountCard/updateIsDefault',
    allocationAccountUserAdd = '/allocationAccountUser/add',
    allocationAccountUserDeleteById = '/allocationAccountUser/deleteByUserId',
    allocationAccountUserGetIsUsed = '/allocationAccountUser/getIsUsed',
    allocationAccountReSendMsg = '/allocationAccount/reSendMsg',
    allocationAccountUpdateBink = '/allocationAccount/updateBink',
    allocationAccountBinkAuth = '/allocationAccount/binkAuth',
    allocationAccountGetBinkInfo = 'allocationAccount/getBinkInfo'
}

/** 分账单结算 */
export const enum SplitBillSettlementApi{
    allocationSettlementPage = '/allocationSettlement/page',
    allocationSettlementGetDetails = '/allocationSettlement/getDetails',
    allocationSettlementRetryAllocation = '/allocationSettlement/retry/allocation',
    allocationSettlementOfflinePayment  = 'allocationSettlement/offlinePayment',
    allocationSettlementOfflinePaymentComplete  = 'allocationSettlement/offlinePayment/complete',
    allocationSettlementExport = '/allocationSettlement/search/export'
}

/** 分页查询用户列表 */
export function allocationAccountPage(params) {
    return defHttp.post({
        url: SplittingRecordedApi.allocationAccountPage,
        params,
    });
}

/** 新增入账方 */
export function allocationAccountAdd(params) {
    return defHttp.post({
        url: SplittingRecordedApi.allocationAccountAdd,
        params,
    });
}

/** 删除入账方 */
export function allocationAccountDelete(params) {
    return defHttp.delete({
        url: SplittingRecordedApi.allocationAccountDelete,
        params,
        requestConfig:{
          isQueryParams:false
        }
    });
}

/** 获取入账方详情 */
export function allocationAccountGetDetail(id) {
    return defHttp.get({
        url: SplittingRecordedApi.allocationAccountGetDetail + '?id=' + id,
    });
}

/** 激活 */
export function activateAccountGetDetail(params) {
    return defHttp.post({
        url: SplittingRecordedApi.activateAccountGetDetail,
        params,
    });
}

/** 重发短信 */
export function allocationAccountReSendMsg(params){
    return defHttp.post({
        url: SplittingRecordedApi.allocationAccountReSendMsg,
        params,
    });
}

/** 银行列表 */
export function bankInfoListAccountBank() {
    return defHttp.get({
        url: SplittingRecordedApi.bankInfoListAccountBank,
    });
}

/** 开户银行列表 */
export function bankInfoList(params) {
    return defHttp.post({
        url: SplittingRecordedApi.bankInfoList,
        params:params
    });
}

/** 修改默认银行卡 */
export function allocationAccountCardUpdateIsDefault(params) {
    return defHttp.post({
        url: SplittingRecordedApi.allocationAccountCardUpdateIsDefault,
        params,
    });
}

/** 新增经销商 */
export function allocationAccountUserAdd(params) {
    return defHttp.post({
        url: SplittingRecordedApi.allocationAccountUserAdd,
        params,
    });
}

/** 删除经销商 */
export function allocationAccountUserDeleteById(id) {
    return defHttp.delete({
        url: SplittingRecordedApi.allocationAccountUserDeleteById + '?userId=' + id,
        requestConfig:{
          isQueryParams:true
        }
    });
}

/** 获取已被绑定的经销商 */
export function allocationAccountUserGetIsUsed() {
    return defHttp.get({
        url: SplittingRecordedApi.allocationAccountUserGetIsUsed,
    });
}

/** 分页查询列表 */
export function allocationSettlementPage(params) {
    return defHttp.post({
        url: SplitBillSettlementApi.allocationSettlementPage,
        params,
    });
}

/** 获取结算单详情 */
export function allocationSettlementGetDetails(settlementNo) {
    return defHttp.get({
        url: SplitBillSettlementApi.allocationSettlementGetDetails + '?settlementNo=' + settlementNo,
    });
}

/** 重新发起 */
export function allocationSettlementRetryAllocation(id){
    return defHttp.post({
        url: SplitBillSettlementApi.allocationSettlementRetryAllocation + '?id=' + id,
    });
}

/** 转线下打款 */
export function allocationSettlementOfflinePayment(id){
    return defHttp.post({
        url: SplitBillSettlementApi.allocationSettlementOfflinePayment + '?id=' + id,
    });
}

/** 已线下打款 */
export function allocationSettlementOfflinePaymentComplete(id){
    return defHttp.post({
        url: SplitBillSettlementApi.allocationSettlementOfflinePaymentComplete + '?id=' + id,
    });
}

/** 导出 */
export function allocationSettlementExport(params) {
    return defHttp.post({
        url: SplitBillSettlementApi.allocationSettlementExport,
        requestConfig:{
            responeseType:'stream'
          },
        params,
    });
}

/** 修改白名单绑卡信息 */
export function allocationAccountUpdateBinkApi(params) {
    return defHttp.post({
        url: SplittingRecordedApi.allocationAccountUpdateBink,
        params,
    });
}

/** 中金绑卡校验 */
export function allocationAccountBinkAuth(params) {
    return defHttp.post({
        url: SplittingRecordedApi.allocationAccountBinkAuth,
        params,
    });
}

/** 获取绑定卡号数据 */
export function allocationAccountGetBinkInfo(id){
    return defHttp.get({
        url: SplittingRecordedApi.allocationAccountGetBinkInfo + '?id=' + id,
    });
}