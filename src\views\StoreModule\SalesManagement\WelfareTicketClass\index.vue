<template>
  <div class="wrapper inner-page-height">
      <FormLayout
        :isLoading="isLoading"
        :tableData="tableData"
        :tableColumns="tableColumns"
        :pagination="paginationRef"
        @paginationChange="paginationChange"
        :isNeedCollapse="false"
        :isDisplayIndex="false"
        @table-sorter-change="tableSorterChange"
      >
        <!-- 表单 -->
        <template #searchForm>
          <n-form
            ref="formRef"
            label-placement="left"
            label-width="auto"
            :show-feedback="false"
            require-mark-placement="right-hanging"
            size="small"
            :style="{ width: '100%' }"
          >
            <n-form-item :span="12" label="福利券分类" path="">
              <j-search-input
                v-model:value.trim="model.name"
                placeholder="请输入分类名称"
                @search="handlerSearch"
              />
            </n-form-item>
          </n-form>
        </template>
    
        <template #tableHeaderBtn>
          <n-button @click="refresh" :loading="isLoading" class="store-button">刷 新</n-button>
          <JAddButton v-if="hasWelfareClassifyAddAuth" type="primary" @click="handleFn(true)">新增福利券分类</JAddButton>
        </template>
        <template #tableFooterBtn="scoped">
          <n-popconfirm
            v-if="hasWelfareClassifyDeleteAuth"
            @positive-click="()=>handlerBatchDelete(scoped.selectedListIds)"
            :positive-button-props="{
              loading: deleteLoading
            }"
          >
            <template #trigger>
              <n-button ghost type="error" size="small">批量删除</n-button>
            </template>
            此操作将删除选中的福利券分类，是否继续？
          </n-popconfirm>
        </template>
      </FormLayout>
      <WelfareTicketClassSetting v-model:show="isShow" :Add_active="isAddActive" :row="row" @refresh="refresh" />
  </div>
</template>

<script lang="tsx" setup name="DoctorManagement">
import { onMounted, ref } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { pageCouponCate, batchDeleteCouponCate } from "@/services/api";
import { useMessages } from '@/hooks';
import WelfareTicketClassSetting from "./components/WelfareTicketClassSetting.vue";
import Popconfirm from "@/components/Popconfirm/index.vue";
import TablePreview from "@/components/TablePreview/index.vue";
import { transformMinioSrc } from "@/utils/fileUtils"
import { hasWelfareClassifyAddAuth,hasWelfareClassifyEditAuth,hasWelfareClassifyDeleteAuth } from "../authList"

const { createMessageSuccess, createMessageError } = useMessages();

/** 表格hook */
const {
  isAddLoading,
  isEditLoading,
  isLoading,
  tableData,
  paginationRef,
  pageTableData,
  deleteTableData,
  editTableData,
  addTableData,
  refreshTableData,
  sortTableData,
  paginationChange,
} = useTableDefault({
  pageDataRequest: pageCouponCate,
});

/* 表格列表项 */
const tableColumns = ref([
  {
    title: "ID",
    key: "id",
    width: 120,
    align: "left",
  },
  {
    title: "名称",
    key: "name",
    width: 150,
    align: "left",
  },

  {
    title: "描述",
    key: "description",
    width: 180,
    align: "left",
  },
  {
    title: "图片",
    key: "area",
    width: 150,
    align: "left",
    render: (row) => {
      if(row.imageUrl){
        return <TablePreview src={transformMinioSrc(row.imageUrl)}></TablePreview>;;
      }else{
        return "-"
      }
    },
  },
   {
    title: "现金抵扣券金额",
    key: "area",
    width: 150,
    align: "left",
    render: (row) => {
      return <div>{  row.cashCouponFlag === 1 ? (row.cashCouponAmt ? `${(row.cashCouponAmt / 100).toFixed(2)}` : '0.00') : '-' }</div>
    },
  },
  {
    title: "创建时间",
    key: "createTime",
    width: 150,
    align: "left",
    sorter: true,
    isSortDefault: true,
  },
  {
    title: "操作",
    key: "action",
    width: 120,
    fixed: "right",
    align: "left",
    render: row => {
      return (
        <n-space style="padding: 5px 0;">
          {
            hasWelfareClassifyEditAuth ? <n-button text type="primary" onClick={() => handleFn(false, row)}>编辑</n-button> : null
          }
          {
            hasWelfareClassifyDeleteAuth ? <Popconfirm 
              onHandleClick={() =>handlerBatchDelete([row.id])} 
              loading={deleteLoading.value} 
              buttonContent ={'删除'} 
              type={'error'} 
              promptContent={'你确定要删除该福利券分类吗?'}/> : null
          }
        </n-space>
      );
    },
  },
]);

const isShow = ref(false);
const isAddActive = ref(true);

const row = ref(null);
const handleFn = (isAdd:boolean, rowData?:any) => {
  isAddActive.value = isAdd;
  row.value = rowData;
  isShow.value = true; 
};

/** 删除 批量删除 */
const deleteLoading = ref(false);
const handlerBatchDelete = async(ids:string[] | string) => {
  try {
    deleteLoading.value = true
    let couponCateIds = Array.isArray(ids)? ids.join(','): ids
    await batchDeleteCouponCate({couponCateIds});
    createMessageSuccess('删除成功');
    refresh();
  } catch (err) {
    createMessageError(err);
  } finally {
    deleteLoading.value = false;
  }
};

/** 参数 */
const model = ref({
  name: ''
});

/** 获取参数 */
const getParams = () => {
  const { name } = model.value;
  return {
    name
  };
};

/** 搜索 */
const handlerSearch = () => {
  tableSearch();
};

/** 表格刷新 */
function refresh(){
  tableSearch();
}

/* 表格搜索 */
const tableSearch = () => {
  pageTableData(getParams(), paginationRef.value);
};

/** 表格排序 */
const tableSorterChange = (info: { sort: string, sortAsc: "ascend" | "descend" }) => {
    const { sort, sortAsc } = info;
    if (sort) {
        tableData.value = [...tableData.value].sort((a, b) => {
            const valA = a[sort];
            const valB = b[sort];
            if (sort.toLowerCase().includes('time')) {
                if (sortAsc === 'ascend') {
                    return valA.localeCompare(valB);
                } else {
                    return valB.localeCompare(valA);
                }
            }
            return 0;
        });
    }
};

/** 组件挂载 */
onMounted(() => {
  tableSearch();
});
</script>

<style lang="less" scoped>
@import "@/styles/defaultVar.less";
</style>