import {defHttp} from '@/services';
import type {GoodsType} from "@/enums";

export const enum DoctorEndPrescriptionApi {
    presPage = '/formulary/page',
    presGetDetail = '/formulary/get',
    hospitalPresGetDetail = '/formulary/get/hospital', // 获取处方详情
    getDoctorDrugPage = 'product/manage/page/doctor',
    addPres = '/formulary/add',
    addForPicture = '/formulary/addForPicture', // 开处方-图文问诊
    updatePres = '/formulary/update',
    pageForDoctor = 'product/cate/pageForDoctor',
    diseaseFormularyPage = '/diseaseManagements/page/createFormulary', // 医生端-医患会话-创建处方-分页检索病种
}

//医生端-处方-分页查询
export function DoctorEndPresPage(params) {
    return defHttp.post({
        url: DoctorEndPrescriptionApi.presPage,
        params
    });
}

//医生端-处方-详情
export function doctorEndGetPrescriptionDetail(params) {
    return defHttp.get({
        url: DoctorEndPrescriptionApi.hospitalPresGetDetail + '?id=' + params,
    })
}


/**
 * 获取医生端-处方-药品分页
 * @param params
 */
export function getDoctorDrugPage(params) {
    return defHttp.post({
        url: DoctorEndPrescriptionApi.getDoctorDrugPage,
        params
    });
}

/**
 * 添加处方-图文问诊
 * @param params
 */
export function doctorEndAddPres(params) {
    return defHttp.post({
        url: DoctorEndPrescriptionApi.addForPicture,
        params
    });
}


/**
 * 修改处方
 * @param params
 */
export function doctorEndUpdatePres(params) {
    return defHttp.put({
        url: DoctorEndPrescriptionApi.updatePres,
        params
    });
}

/** 医生端商品分类分页 */
export function getGoodsClassificationPageForDoctor(params: {
    data: {
        name: string;
        type?: GoodsType;
    };
    pageVO: {
        current: number;
        size: number;
    };
}) {
    return defHttp.post({
        url: DoctorEndPrescriptionApi.pageForDoctor,
        params,
    });
}


/** 医生端-分页检索病种 */
export function getDiseaseFormularyPage(params: {
    data: {
        searchCondition: string
    };
    pageVO: {
        current: number;
        size: number;
    };
}) {
    return defHttp.post({
        url: DoctorEndPrescriptionApi.diseaseFormularyPage,
        params,
    });
}
