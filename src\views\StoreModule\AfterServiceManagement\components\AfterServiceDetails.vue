<template>
  <n-drawer
    v-model:show="drawerVisible"
    :mask-closable="false"
    width="100%"
    height="100%"
    id="camp-detail"
    to=".table-wrapper"
    :content-style="{ padding: 0 }"
  >
    <n-drawer-content :body-content-style="{ padding: 0 }">
      <div style="height: 100%;">
        <!--头部-->
        <div class="detailTop">
          <div class="detailTop-left">
            <div style="height: 28px; display: flex; align-items: center;">
              <n-button text class="mr-12" @click="handleReturn">
                <template #icon>
                  <ArrowBackOutline size="18" />
                </template>
              </n-button>
            </div>
            <div>
              <p>{{ afterSalesStatusLabels[model.state] }}</p>
              <span v-if="afterSalesStatusLabels[model.state] == afterSalesStatusOptions[1].label">请及时处理</span>
              <span v-if="afterSalesStatusLabels[model.state] == afterSalesStatusOptions[7].label">
                第三方支付平台退款中
              </span>
              <span
                v-if="refundTypeLabels[model.refundType] == '线上退款' && afterSalesStatusLabels[model.state] == afterSalesStatusOptions[8].label"
              >
                第三方支付平台退款失败，失败原因：{{ model.refundResult ? model.refundResult : '暂无' }}
              </span>
              <Countdown
                v-if="countdownDisplayFn()"
                :show="countdownDisplayFn()"
                :countdown="model.returnsDeadline"
              />
            </div>
          </div>
          <div class="detailTop-right" v-if="drawerPermissionControl">
            <n-button
              v-if="afterSalesStatusLabels[model.state] == afterSalesStatusOptions[1].label"
              style="margin: 0 5px;"
              type="info"
              size="medium"
              @click="handleCarryOut(model.action, '同意')"
            >
              同意
            </n-button>

            <n-button
              v-if="afterSalesStatusLabels[model.state] == afterSalesStatusOptions[5].label"
              style="margin: 0 5px;"
              type="info"
              size="medium"
              @click="handleCarryOut(model.action, '收货并退款')"
            >
              收货并退款
            </n-button>

            <n-button
              v-if="afterSalesStatusLabels[model.state] == afterSalesStatusOptions[1].label || afterSalesStatusLabels[model.state] == afterSalesStatusOptions[5].label"
              style="margin: 0 5px;"
              type="info"
              size="medium"
              @click="handleCarryOut(model.action, '拒绝')"
            >
              拒绝
            </n-button>

            <n-button
              v-if="afterSalesStatusLabels[model.state] == afterSalesStatusOptions[8].label && model.action.includes(10) "
              style="margin: 0 5px;"
              type="info"
              size="medium"
              @click="handleCarryOut(model.action, '再次发起退款')"
            >
              再次发起退款
            </n-button>

            <n-button
              v-if="afterSalesStatusLabels[model.state] == afterSalesStatusOptions[8].label && model.action.includes(11) "
              style="margin: 0 5px;"
              type="info"
              size="medium"
              @click="handleCarryOut(model.action, '已线下退款')"
            >
              已线下退款
            </n-button>

            <n-button
              v-if="afterSalesStatusLabels[model.state] == afterSalesStatusOptions[4].label"
              style="margin: 0 5px;"
              type="info"
              size="medium"
              @click="handleCarryOut(model.action, '录入物流信息')"
            >
            录入物流信息
            </n-button>
          </div>
        </div>
        <!--内容-->
        <div class="detailContent">
          <n-scrollbar style="height: 100%;">
            <n-spin :show="loadShow" size="small">
              <n-grid cols="24" responsive="screen">
                <n-grid-item :span="16">
                  <div class="detail-content-left">
                    <n-card title="售后信息" :bordered="false" size="small" class="detail-content-left-top">
                      <n-form
                        ref="formRef"
                        :model="model"
                        label-placement="left"
                        label-width="100"
                        require-mark-placement="right-hanging"
                        size="small"
                        :style="{ width: '100%' }"
                      >
                        <n-grid :cols="12">
                          <!-- 姓名 -->
                          <n-form-item-gi :span="12" label="售后单号" v-if="model.recordNo">
                            <n-input-group>
                              <n-input v-model:value="model.recordNo" type="text" readonly />
                              <n-button v-copy="model.recordNo">复制</n-button>
                            </n-input-group>
                          </n-form-item-gi>
                          <!-- 售后类型 -->
                          <n-form-item-gi :span="12" label="售后类型" v-if="model.type">
                            <n-select v-model:value="model.type" disabled :options="afterSalesLabelsOptions" />
                          </n-form-item-gi>
                          <!-- 退款金额 -->
                          <n-form-item-gi :span="12" label="退款金额" v-if="model.refundAmount">
                            {{ model.refundAmount ? `￥${(model.refundAmount / 100).toFixed(2)}` : '￥0.00' }}
                          </n-form-item-gi>
                          <!-- 申请原因 -->
                          <n-form-item-gi :span="12" label="申请原因" v-if="model.reason">
                            <n-select v-model:value="model.reason" disabled :options="aReasonForRefundOptions" />
                          </n-form-item-gi>
                          <!-- 具体描述 -->
                          <n-form-item-gi :span="12" label="具体描述" v-if="model.reasonDescription">
                            <n-input
                              v-model:value="model.reasonDescription"
                              type="textarea"
                              placeholder="暂无具体描述"
                              readonly
                              :autosize="{ minRows: 3,maxRows: 5 }"
                            />
                          </n-form-item-gi>
                          <!-- 图片 -->
                          <n-form-item-gi :span="12" label="图片" v-if="model.afterSalesPictures.length != 0">
                            <n-image-group show-toolbar-tooltip>
                              <n-space v-if="model.afterSalesPictures.length != 0">
                                <n-image v-for="item in model.afterSalesPictures" width="80" :src="item" />
                              </n-space>
                              <n-space v-else="model.afterSalesPictures.length == 0">
                                <span style="color: rgb(255, 71, 113);">{{ '暂无图片' }}</span>
                              </n-space>
                            </n-image-group>
                          </n-form-item-gi>
                          <!-- 联系电话 -->
                          <n-form-item-gi :span="12" label="联系电话" v-if="model.phone">
                            <n-input-group>
                              <n-input v-model:value="model.phone" type="text" readonly />
                              <n-button v-copy="model.phone">复制</n-button>
                            </n-input-group>
                          </n-form-item-gi>
                          <!-- 申请时间 -->
                          <n-form-item-gi :span="12" label="申请时间" v-if="model.createTime">
                            {{ model.createTime ? model.createTime : '-' }}
                          </n-form-item-gi>
                          <!-- 售后商品 -->
                          <n-form-item-gi :span="12" label="售后商品" v-if="model.afterSaleGoods.length != 0">
                            <n-card style="width: 100%;background-color: rgba(216, 216, 216,0.3);border: transparent">
                              <div :class="afterSaleGoodsFn(inden) " v-for="(item, inden) in model.afterSaleGoods" :key="item.id">
                                <div class="afterSale-goods-left">
                                  <JImage :imgPath="item.productImgPath" />
                                  <span style="vertical-align: top;margin-left: 10px;">
                                    {{ item.type == 1 ? item?.productFrontName + item.productName + item.specName : item.productFrontName }}
                                  </span>
                                </div>
                                <div class="afterSale-goods-right">
                                  <p>
                                    {{payTypeLabels[item.payType] == '积分 + 现金' ?
                                     (item.exchangePrice ? `￥${(item.exchangePrice / 100).toFixed(2)}` : '￥0.00') : 
                                     (item.price ? `￥${(item.price / 100).toFixed(2)}` : '￥0.00')
                                    }}
                                  </p>
                                  <span>x{{ item.count }}</span>
                                </div>
                              </div>
                            </n-card>
                          </n-form-item-gi>

                          <!-- 退款操作员 -->
                          <n-form-item-gi :span="12" label="退款操作员" v-if="model.operatorName">
                            <n-input-group>
                              <n-input v-model:value="model.operatorName" type="text" readonly />
                              <n-button v-copy="model.operatorName">复制</n-button>
                            </n-input-group>
                          </n-form-item-gi>
                        </n-grid>
                      </n-form>
                    </n-card>
                    <n-card title="协商记录" :bordered="false" size="small" class="detail-content-left-bottom">
                      <n-space vertical v-if="model.afterSalesRecordHistory.length">
                        <n-steps vertical>
                          <n-step v-for="item in model.afterSalesRecordHistory" :title="item?.title">
                            <span
                              v-if="stateChangeRecordLabels[item?.state] == '商家拒绝退款' || stateChangeRecordLabels[item?.state] == '商家拒绝取消订单'"
                            >
                              {{ '拒绝原因：'}}{{ model.rejectionReasonDescription ? model.rejectionReasonDescription : '无' }}
                            </span>
                            <p>{{ item?.updateTime }}</p>
                          </n-step>
                        </n-steps>
                      </n-space>
                      <n-space vertical v-if="!model.afterSalesRecordHistory.length">
                        <n-steps vertical>
                          <n-step title="暂无协商记录"></n-step>
                        </n-steps>
                      </n-space>
                    </n-card>
                  </div>
                </n-grid-item>
                <n-grid-item :span="8">
                  <div class="detail-content-right">
                    <n-card title="订单信息" :bordered="false" size="small" class="detail-content-right-top">
                      <n-form
                        ref="formRef"
                        :model="model"
                        label-placement="left"
                        label-width="100"
                        require-mark-placement="right-hanging"
                        size="small"
                        :style="{ width: '100%' }"
                      >
                        <n-grid :cols="12">
                          <!-- 订单编号 -->
                          <n-form-item-gi :span="12" label="订单编号">
                            <n-input-group>
                              <n-input v-model:value="model.code" type="text" readonly />
                              <n-button v-copy="model.code">复制</n-button>
                            </n-input-group>
                          </n-form-item-gi>
                          <!-- 订单状态 -->
                          <n-form-item-gi :span="12" label="订单状态">
                            <n-select v-model:value="model.status" disabled :options="orderStatusOptions" />
                          </n-form-item-gi>
                          <!-- 商品总额 -->
                          <n-form-item-gi v-if="model.recordType !== 2" :span="12" label="商品总额">
                            {{ model.goodsAmount ? model.goodsAmount : '￥0.00' }}
                          </n-form-item-gi>
                          <!-- 邮费 -->
                          <n-form-item-gi  v-if="model.recordType !== 2" :span="12" label="邮费">
                            {{ model.shippingFee ? model.shippingFee : '￥0.00' }}
                          </n-form-item-gi>
                          <!-- 订单总金额 -->
                          <n-form-item-gi :span="12" label="订单总金额">
                            {{ model.money ? model.money : '￥0.00' }}
                          </n-form-item-gi>
                          <!-- 支付方式 -->
                          <n-form-item-gi v-if="model.recordType !== 2" :span="12" label="支付方式">
                            <n-select v-model:value="model.payType" disabled :options="payTypeOptions" />
                          </n-form-item-gi>
                          <template v-else>
                            <!-- 支付方式 -->
                            <n-form-item-gi  :span="12" label="支付方式">
                              <n-select v-model:value="model.payType" disabled :options="[{label:'未支付',value:0},{label:'在线支付',value:1}]" />
                            </n-form-item-gi>
                            <!-- 支付状态 -->
                            <n-form-item-gi v-if="model.payType == 1" :span="12" label="支付状态">
                              <n-select v-model:value="model.payStatus" disabled :options="payStatusOptions" />
                            </n-form-item-gi>
                            <!-- 在线支付金额 -->
                            <n-form-item-gi  v-if="model.payType == 1" :span="12" label="在线支付金额">
                              {{ model.onlinePayment ? model.onlinePayment : '￥0.00' }}
                            </n-form-item-gi>
                          </template>
                          
                          <!-- 支付状态 -->
                          <n-form-item-gi v-if="model.recordType !== 2"  :span="12" label="支付状态">
                            <n-select v-model:value="model.payStatus" disabled :options="payStatusOptions" />
                          </n-form-item-gi>
                          <!-- 在线支付金额 -->
                          <n-form-item-gi v-if="model.recordType !== 2"  :span="12" label="在线支付金额">
                            {{ model.onlinePayment ? model.onlinePayment : '￥0.00' }}
                          </n-form-item-gi>
                          <!-- 物流代收金额 -->
                          <n-form-item-gi  v-if="model.recordType !== 2" :span="12" label="物流代收金额">
                            {{ model.cashOnDelivery ? model.cashOnDelivery : '￥0.00' }}
                          </n-form-item-gi>
                          <!-- 订单创建时间 -->
                          <n-form-item-gi :span="12" label="订单创建时间">
                            {{ model.orderInformationCreateTime ? model.orderInformationCreateTime : '-' }}
                          </n-form-item-gi>
                          <!-- 订单支付时间 -->
                          <n-form-item-gi :span="12" label="订单支付时间">
                            {{ model.payTime ? model.payTime : '-' }}
                          </n-form-item-gi>
                        </n-grid>
                      </n-form>
                    </n-card>
                    <n-card
                      v-if="model.address"
                      title="退货物流"
                      :bordered="false"
                      size="small"
                      class="detail-content-right-bottom"
                    >
                      <n-form
                        ref="formRef"
                        :model="model"
                        label-placement="left"
                        label-width="100"
                        require-mark-placement="right-hanging"
                        size="small"
                        :style="{ width: '100%' }"
                      >
                        <n-grid :cols="12">
                          <!-- 快递公司 -->
                          <n-form-item-gi :span="12" label="快递公司">
                            <n-input
                              v-model:value="model.shipCompanyName"
                              type="text"
                              readonly
                              placeholder="暂无快递公司"
                            />
                          </n-form-item-gi>
                          <!-- 快递单号 -->
                          <n-form-item-gi :span="12" label="快递单号">
                            <n-input-group>
                              <n-input
                                v-model:value="model.trackingNo"
                                type="text"
                                readonly
                                placeholder="暂无快递单号"
                              />
                              <n-button v-copy="model.trackingNo">复制</n-button>
                            </n-input-group>
                          </n-form-item-gi>
                          <!-- 退货地址 -->
                          <n-form-item-gi :span="12" label="退货地址">
                            <n-input
                              v-model:value="model.address"
                              type="textarea"
                              placeholder="暂无退货地址"
                              readonly
                              :autosize="{ minRows: 3, maxRows: 5 }"
                            />
                          </n-form-item-gi>
                          <!-- 收件人 -->
                          <n-form-item-gi :span="12" label="收件人">
                            <n-input v-model:value="model.name" type="text" readonly placeholder="暂无收件人" />
                          </n-form-item-gi>
                          <!-- 联系方式 -->
                          <n-form-item-gi :span="12" label="联系方式">
                            <n-input-group>
                              <n-input v-model:value="model.mobile" type="text" readonly placeholder="暂无联系方式" />
                              <n-button v-copy="model.mobile">复制</n-button>
                            </n-input-group>
                          </n-form-item-gi>
                        </n-grid>
                      </n-form>
                    </n-card>
                  </div>
                </n-grid-item>
              </n-grid>
            </n-spin>
          </n-scrollbar>
        </div>
      </div>
    </n-drawer-content>
  </n-drawer>
  <ConfirmContentModal ref="confirmContentShow" />
</template>

<script setup lang="tsx" name="AfterServiceDetails">
import { ref } from "vue";
import { useMessages } from "@/hooks/useMessage";
import { deepClone } from "@/utils";
import Countdown from './Countdown.vue'
import { ArrowBackOutline } from '@vicons/ionicons5';
import ConfirmContentModal from './ConfirmContentModal.vue'
import { afterSalesStatusLabels, afterSalesStatusOptions, stateChangeRecordLabels, afterSalesLabels, aReasonForRefundLabels, payTypeLabels, payStatusLabels, orderStatusLabels, refundTypeOptions, afterSalesLabelsOptions, aReasonForRefundOptions, orderStatusOptions, payTypeOptions, payStatusOptions, refundTypeLabels, refundStateLabels } from "@/constants";
const { createMessageError } = useMessages();

const initParams = {
  recordNo: null, // 售后单号
  type: null, //售后类型
  refundAmount: null, //退款金额
  reason: null,//申请原因
  reasonDescription: null,//具体描述
  rejectionReasonDescription: null,//协商记录的拒绝原因
  createTime: null,//申请时间
  phone: null,//联系电话
  afterSalesPictures: [],//图片名
  productImgPath: null,//商品首图路径
  afterSaleGoods: [],//售后商品
  returnsDeadline: null,//退款过期时间
  shipCompanyCode: null,//退货快递公司编码
  addressId: null,//退货地址 ID
  action: null,//可执行动作
  refundResult: null,//退款结果
  refundType: null,//
  operatorName:null,//退款操作员


  /** 订单信息 */
  code: null,//订单编号
  status: null,//订单状态
  goodsAmount: null,//商品总金额
  shippingFee: null,//邮费
  money: null,//订单总金额
  payType: null,//支付方式
  payStatus: null,//支付状态
  onlinePayment: null,//在线支付金额
  cashOnDelivery: null,//物流代收金额
  orderInformationCreateTime: null,//订单信息创建事件
  payTime: null,//订单支付时间
  refundStatus: null,//退款状态

  /** 退货物流 */
  shipCompanyName: null,//退货快递公司名称
  trackingNo: null,//退货快递单号
  name: null,//收件人姓名
  address: null,//地址
  mobile: null,//联系电话

  /** 协商记录 */
  afterSalesRecordHistory: [],//售后历史
  state: null,//售后状态
  //  afterServiceCreateTime:null,//售后创建时间
  reasonsForRefusal: null,//拒绝原因(未编辑)
  reasonsForRefusalGoing: null,//拒绝原因(编辑中)
  recordType:null //记录类型 1：售后记录 2：问诊记录
}

const model = ref(deepClone(initParams));

/** 抽屉状态 */
const drawerVisible = ref(false);
const drawerProps = ref();
const drawerApi = ref()
const drawerDoActionApi = ref()
const drawerPermissionControl = ref()
const loadShow = ref(false)

/* 接收父组件传过来的参数 */
const acceptParams = (params) => {
  drawerVisible.value = true;
  drawerProps.value = params;
  drawerApi.value = params.getInfoApi
  drawerDoActionApi.value = params.getDoActionApi
  drawerPermissionControl.value = params.permissionControl
  model.value.operatorName = drawerProps.value.row?.operatorName
  getDetails()
};

const getDetails = async () => {
  loadShow.value = true
  const _params = {
    recordNo: drawerProps.value.row.recordNo
  }
  try {
    const data = await drawerApi.value(_params);
    console.log('data',data);
    
    returnToDetailsRelatedContent(data)
  } catch (err) {
    createMessageError('获取详情数据失败: ' + err);
  } finally {
    loadShow.value = false
  }
}

/** 同意 */
const confirmContentShow = ref()
const handleCarryOut = (action, type) => {
  const _params = {
    row: {
      recordNo: model.value.recordNo,
      action: model.value.action,
      addressId: model.value.addressId,
      shipCompanyCode: model.value.shipCompanyCode,
      trackingNo: model.value.trackingNo,
      refundAmount: model.value.refundAmount
    },
    type,
    getInfoApi: drawerDoActionApi.value,
    refresh: getDetails,
  }
  switch (type) {
    case '同意':
      for (let i = 1; i <= 5; i++) {
        if (action.includes(i)) {
          _params['code'] = i;
          break;
        }
      }
      break;
    case '拒绝':
      _params['code'] = 7;
      break;
    case '收货并退款':
      for (let i = 8; i <= 9; i++) {
        if (action.includes(i)) {
          _params['code'] = i;
          break;
        }
      }
      break;
    case '再次发起退款':
      _params['code'] = 10;
      break;
    case '已线下退款':
      _params['code'] = 11;
      break;
    case '录入物流信息':
      _params['code'] = 23;
      break;
    default:
      break;
  }
  confirmContentShow.value?.acceptParams(_params);
}

//返回列表
const handleReturn = () => {
  model.value = { ...initParams };
  drawerVisible.value = false
  drawerProps.value.refresh()
}

//详情返回字段相关内容
const returnToDetailsRelatedContent = (data) => {
  const { recordNo, type, refundAmount, reason, rejectionReasonDescription, afterSaleImgDTOList, phone, createTime, orderItemDTOList, orderEntityDTO, customerAddressDTO, shipCompanyName, trackingNo, afterSaleHistoryDTOList, refundType, returnsDeadline, shipCompanyCode, addressId, state, action, reasonDescription, refundResult, refundStatus, recordType, presEntityDTO } = data

  /** 售后商品 */
  if (orderItemDTOList) {
    model.value.afterSaleGoods = orderItemDTOList ? orderItemDTOList : []
  }

  /** 售后记录图片 */
  if (afterSaleImgDTOList) {
    const afterSalesPicturesImg = []
    afterSaleImgDTOList.map((item) => {
      if (item.path != '') {
        afterSalesPicturesImg.push(item.path)
      }
    })
    model.value.afterSalesPictures = afterSalesPicturesImg
  }

  /** 售后信息 */
  model.value.recordNo = recordNo ? recordNo : null
  model.value.type = type ? afterSalesLabels[type] : null
  model.value.refundAmount = refundAmount ? refundAmount : null
  model.value.reason = aReasonForRefundLabels[reason]
  model.value.rejectionReasonDescription = rejectionReasonDescription ? rejectionReasonDescription : null
  model.value.phone = phone ? phone : null
  model.value.createTime = createTime ? createTime : null
  model.value.returnsDeadline = returnsDeadline ? returnsDeadline : null
  model.value.shipCompanyCode = shipCompanyCode ? shipCompanyCode : null
  model.value.addressId = addressId ? addressId : null
  model.value.state = state ? state : null
  model.value.action = action ? action : null
  model.value.reasonDescription = reasonDescription ? reasonDescription : null
  model.value.refundResult = refundResult ? refundResult : null
  model.value.refundStatus = refundStatus ? refundStatus : null
  model.value.refundType = refundType ? refundType : null
  model.value.recordType = recordType ? recordType : null

  /** 订单信息 */
  if (orderEntityDTO) {
    const { code, status, goodsAmount, shippingFee, money, payType, payStatus, onlinePayment, cashOnDelivery, createTime, payTime } = orderEntityDTO
    model.value.code = code ? code : null
    model.value.status = status ? orderStatusLabels[status] : null
    model.value.goodsAmount = goodsAmount ? `￥${(goodsAmount / 100).toFixed(2)}` : null
    model.value.shippingFee = shippingFee ? `￥${(shippingFee / 100).toFixed(2)}` : null
    model.value.money = money ? `￥${(money / 100).toFixed(2)}` : null
    model.value.payType = payType ? payTypeLabels[payType] : null
    model.value.payStatus = payStatus ? payStatusLabels[payStatus] : null
    model.value.onlinePayment = onlinePayment ? `￥${(onlinePayment / 100).toFixed(2)}` : null
    model.value.cashOnDelivery = cashOnDelivery ? `￥${(cashOnDelivery / 100).toFixed(2)}` : null
    model.value.orderInformationCreateTime = createTime ? createTime : null
    model.value.payTime = payTime ? payTime : null
  }
  // 问诊单订单信息
  if(presEntityDTO) {
    const { code, consultationStatus, fee, createTime, payType, payTime } = presEntityDTO
     model.value.code = code ? code : null
     model.value.status = consultationStatus ? orderStatusLabels[consultationStatus] : null
     model.value.payType = payType ? ( payType == 1 ? 1 : 0 ) : null
     model.value.orderInformationCreateTime = createTime ? createTime : null
     model.value.money = fee ? `￥${(fee / 100).toFixed(2)}` : null
     model.value.payTime = payTime ? payTime : null
     model.value.onlinePayment =  fee ? `￥${(fee / 100).toFixed(2)}` : null
     model.value.payStatus = 2
  }
  /** 退货物流 */
  model.value.shipCompanyName = shipCompanyName ? shipCompanyName : null
  model.value.trackingNo = trackingNo ? trackingNo : null
  if (customerAddressDTO) {
    const { name, mobile, company, province, cityName, area, town, address } = customerAddressDTO
    model.value.name = name ? name : null
    model.value.mobile = mobile ? mobile : null
    model.value.address = `${company ? company : ''}${province ? province : ''}${cityName ? cityName : ''}${area ? area : ''}${town ? town : ''}${address ? address : ''}`;
  }

  /** 售后记录历史 */
  if (afterSaleHistoryDTOList) {
    model.value.afterSalesRecordHistory = afterSaleHistoryDTOList ? afterSaleHistoryDTOList : null
    model.value.afterSalesRecordHistory = model.value.afterSalesRecordHistory.map(item => {
      if (item.state == 1 || item.state == 2 || item.state == 3 || item.state == 4 || item.state == 5 || item.state == 6 || item.state == 10 || item.state == 11) {
        return { ...item, title: stateChangeRecordLabels[item.state] };
      } else if (item.state == 9) { //退款成功
        const refundLabel = JSON.parse(stateChangeRecordLabels[9])
        if (refundTypeLabels[refundType] == '线上退款') {
          return { ...item, title: refundLabel[0] };
        } else {
          return { ...item, title: refundLabel[1] };
        }
      } else if (item.state == 7) { //退款中
        const refundStatusContent = JSON.parse(stateChangeRecordLabels[7])
        if (afterSalesLabels[type] == '仅退款') {
          return { ...item, title: refundStatusContent[0] };
        } else {
          return { ...item, title: refundStatusContent[1] };
        }
      } else if (item.state == 8) { //待打款
        const contentOfPendingPayment = JSON.parse(stateChangeRecordLabels[8])
        if (refundStateLabels[item.refundStatus] == '线上退款失败') {
          return { ...item, title: contentOfPendingPayment[0] };
        } else if (afterSalesLabels[type] == '仅退款') {
          return { ...item, title: contentOfPendingPayment[1] };
        } else if (afterSalesLabels[type] == '退货退款') {
          return { ...item, title: contentOfPendingPayment[2] };
        }
      }
    })
  }
}

/** 倒计时显示隐藏 */
const  countdownDisplayFn = () => {
  if(afterSalesStatusLabels[model.value.state] == afterSalesStatusOptions[4].label){
    return true
  }
  return false
}

/** 售后商品类名 */
const afterSaleGoodsFn = (index) =>{
  if((index + 1) != model.value.afterSaleGoods.length){
    return 'afterSale-goods lowerBorder'
  }
  return 'afterSale-goods'
}

defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less">
@import "@/styles/default.less";
@import "@/styles/defaultVar.less";

/**头部 */
.detailTop {
  display: flex;
  justify-content: space-between;
  width: 100%;
  background-color: #fff;
  align-items: center;
  padding: 12px 24px;
  box-sizing: border-box;
  border-bottom: 1px solid @default-border-color;

  .detailTop-right {
    display: flex;
    justify-content: space-between;
  }

  .detailTop-left {
    display: flex;
    justify-content: center;
    align-items: center;

    p {
      font-size: 20px;
      font-weight: 700;
    }

    span {
      font-size: 14px;
    }
  }
}

/** 内容 */
.detailContent {
  height: calc(100% - 108px);
  display: flex;
  justify-content: space-between;

  :deep(.n-spin-content) {
    height: 100%;
  }

  .detail-content-left {
    padding: 12px 6px 12px 12px;
    .detail-content-left-top {
      box-shadow: rgba(0, 0, 0, 0.15) 0px 2px 8px;
    }

    .detail-content-left-bottom {
      margin-top: 12px;
      box-shadow: rgba(0, 0, 0, 0.15) 0px 2px 8px;
    }
  }

  .detail-content-right {
    padding: 12px 16px 12px 6px;
    .detail-content-right-top {
      box-shadow: rgba(0, 0, 0, 0.15) 0px 2px 8px;

    }

    .detail-content-right-bottom {
      margin-top: 12px;
      box-shadow: rgba(0, 0, 0, 0.15) 0px 2px 8px;
    }
  }
}

.afterSale-goods {
  display: flex;
  justify-content: space-between;
  padding: 4px;

  .afterSale-goods-right {
    text-align: end;
  }
}
.lowerBorder{
  border-bottom: 1px solid #DDD;
}
</style>
