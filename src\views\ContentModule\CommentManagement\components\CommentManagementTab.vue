<template>
    <FormLayout :isLoading="isLoading" :tableData="tableData" :tableColumns="tableColumns" :pagination="paginationRef"
        @paginationChange="paginationChange" :isTableSelection="true" @selectedKeysChange="selectedKeysChange"
        id="member">
        <template #searchForm>
            <n-form ref="formRef" :model="formValue" :show-feedback="false" label-placement="left" label-width="auto"
                require-mark-placement="right-hanging" size="small" :style="{ width: '100%' }">

                <n-form-item label="视频ID">
                    <n-input v-model:value="formValue.videoId" type="text" placeholder="请输入视频ID"
                        @keyup.enter.native="formSearch" style="width:170px" clearable :allow-input="onlyAllowNumber" :maxlength="19" />
                </n-form-item>
                
                <n-form-item label="评论者ID">
                    <n-input v-model:value="formValue.userId" type="text" placeholder="请输入评论者ID"
                        @keyup.enter.native="formSearch" style="width:170px" clearable :allow-input="onlyAllowNumber" :maxlength="19" />
                </n-form-item>
                <n-form-item label="评论者昵称">
                    <n-input v-model:value="formValue.nickname" type="text" placeholder="请输入评论者昵称"
                        @keyup.enter.native="formSearch" style="width:170px" clearable />
                </n-form-item>
            </n-form>
        </template>
        <template #tableHeaderBtn>
            <n-button @click="refresh" class="store-button" :loading="isLoading" >刷 新</n-button>
        </template>
        <template #tableFooterBtn="scope">
            <!-- 批量删除 -->
            <n-popconfirm @positive-click="handleDelete(scope.selectedListIds, scope.selectedList)"
                :positive-button-props="{
                    loading: isBatchLoading
                }" v-if="tabNameRef!=0 && hasManagementDelete " >
                <template #trigger  >
                    <n-button ghost type="primary"  size="small">删除</n-button>
                </template>
                此操作将删除所有选中的视频，是否继续？
            </n-popconfirm>
        </template>
    </FormLayout>
</template>
<script setup lang="tsx">
import { ref, reactive, watch, h, type VNode } from 'vue';
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { commentPage, updateCommentType, deleteComment } from '@/services/api';
import { useMessages } from "@/hooks";
import { CommentTypeEnum } from '@/enums';
import moment from "moment";
import { NTooltip, type SelectOption } from 'naive-ui';
import { hasManagementDelete , hasManagementAudit } from "../authList";
import JImage from "@/components/JImage/index.vue";
import TablePreview from "@/components/TablePreview/index.vue";
import { getOssFileUrlPrefix } from "@/utils/http/urlUtils";
import { transformMinioSrc } from "@/utils/fileUtils"

import { _debounce } from "@/utils";
import { videoTypeLabel } from "@/constants";



const renderOption = ({ node, option }: { node: VNode; option: SelectOption }) =>
    h(NTooltip, null, {
        trigger: () => node,
        default: () => option.label
    })
interface prescriptionProps {
    tabNameRef: CommentTypeEnum //tab标签值
}

const props = withDefaults(defineProps<prescriptionProps>(), {
    tabNameRef: CommentTypeEnum.AuditResolve
});

const tabValue = ref(null)
const { createMessageSuccess, createMessageError } = useMessages();
/* 初始化参数 */
const initParams = {
    videoId: '',     // 视频ID
    userId: '',      // 用户ID
    nickname: '',    // 用户昵称
};
const formValue = reactive({ ...initParams, });

/* 表格方法Hook */
const {
    isLoading,
    tableData,
    paginationRef,
    paginationChange,
    pageTableData,
} = useTableDefault({
    pageDataRequest: commentPage,
});

//刷新
const refresh = () => {
    getTableData()
}
const showAddDrawer = ref<boolean>(false)
// /** 新增视频 */
// const addVideo = () => {
//     console.log('点击新增');
//     showAddDrawer.value = true

// }





const showVidelDetail = ref<boolean>(false)
const isBatchLoading = ref<boolean>(false)
/** 批量删除  */
const handleDelete = (selectedListIds:string[], selectedList=[]) => {
    isLoading.value = true
    deleteComment(selectedListIds).then(res=>{
        createMessageSuccess('删除成功')
    }).catch(err=>{
        createMessageError(`删除失败:${err}`)
        console.log(err,'err');
    }).finally(()=>{
        isLoading.value = false;
        refresh();
    })


}
/** 搜索 */
const formSearch = () => {
    getTableData()
}

function getTableData() {
    const _params = {
        ...formValue,
        auditState:tabValue.value
    }

    pageTableData(_params, paginationRef.value, true);
}

const onlyAllowNumber = (value: string) => !value || /^\d+$/.test(value);



/* 表格项 */
const tableColumns = reactive([
    {
        title: "评论者昵称",
        key: "nickname",
        align: "left",
        width: 100,
        render: (row) => {
            return <table-tooltip row={row} nameKey="nickname" title={row['nickname']} idKey="userId"/>;
        }
    },
    
    {
        title: "评论内容",
        key: "comment",
        align: "center",
        width: 200,
    },
    {
    title: "视频文件",
    key: "img",
    align: "left",
    width: 100,
    render: row => {
        return <n-space align="center" justify="center">
                    <TablePreview src={row.videoPath} type='video' ></TablePreview>
                    <table-tooltip row={row} nameKey="" idKey="videoId" />
                </n-space>
            
    },
  },
   
    
    {
        title: "操作",
        key: "action",
        width: 150,
        align: "left",
        fixed: "right",
        render: (row) => {
            return (
                <n-space align="center" justify="center">

                    {
                        hasManagementAudit && tabValue.value==0 ? <n-button text size="small" type="primary" class="mt-4 mr-4 edit-icon" onClick={() => clickCommentType(row, 1)}>审核通过
                        </n-button> : null
                    }
                    {hasManagementAudit && tabValue.value==0 ? <n-button text size="small" type="error" class="mt-4 mr-4 edit-icon" onClick={() => clickCommentType(row,2)}>审核不通过
                    </n-button> : null}

                    {hasManagementDelete && tabValue.value!=0 ? <n-popconfirm
                        onPositiveClick={() => { handleDelete([row.id]) }}
                    >
                        {{
                            trigger: () => (
                                <n-button text size="small" type="error" >删除</n-button>
                            ),
                            default: () => <span style={{ width: '300px' }}>是否确定删除该数据？</span>
                        }}
                    </n-popconfirm> : null}

                </n-space>
            );
        },
    },
])



//勾选
const codeListData = ref([])
const selectedKeysChange = (value, row) => {
    const codeList = []
    row.forEach(item => {
        codeList.push(item.code)
    })
    codeListData.value = codeList
}

/** 发布 或 取消发布 */
const clickCommentType = (row,type) => {
    isLoading.value = true;
    changeVideoType(row.id,type);
}
const changeVideoType = (id:string,auditState:1|2=1)=>{
    updateCommentType({id,auditState}).then(res=>{
        createMessageSuccess(auditState != 1 ? '取消发布成功':'发布成功')
        refresh()
    }).catch(err=>{
        createMessageError(`${auditState != 1 ? '取消发布失败':'发布失败'}:${err}`)
    }).finally(()=>{
        isLoading.value = false;
    })
}





const debounceGetTableData = _debounce(getTableData,500)

// watch([
//     () => formValue.userId,
//     () => formValue.nickname,
//     () => formValue.videoId
// ], () => {
//     debounceGetTableData();
// });

watch(
    () => props.tabNameRef,
    (newVal) => {
        tabValue.value = newVal
        getTableData()
    },
    {
        immediate: true
    }
)
</script>
<style lang="less" scoped>
@import "@/styles/default.less";
</style>