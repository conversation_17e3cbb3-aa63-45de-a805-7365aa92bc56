<template>
    <div :class="[
        'overview-dataCard-wrapper',
        {'small':props.valueList.length<=2},
        {'default':props.valueList.length>2}
    ]">
        <div class="title">
            <img class='icon' :src="props.titleIconSrc" alt="">
            <p>{{ props.title }}</p>
        </div>
        <div 
            :class="['content']"
        >
            <template v-for="(item,index) in props.valueList" :key="item.title">
                <div class="content-item">
                    <div class="title">{{ item.title }}</div>
                    <div class="value">
                        <n-number-animation 
                            :show-separator="isNullOrUnDef(item.showSeparator)?false:item.showSeparator"
                            :duration="isNullOrUnDef(props.isAnimation)?3000:props.isAnimation?3000:0"
                            :to="item.value" 
                            :precision="isNullOrUnDef(item.toFixed)?0:item.toFixed" 
                        />
                        <span v-if="item.isDisplayPercent">%</span>
                    </div>
                </div>
                <div v-if="index != props.valueList.length - 1 " class="border"></div>
            </template>
        </div>
    </div>
</template>
<script setup lang="ts">
import { isNullOrUnDef } from '@/utils/isUtils';
type ValueListItem = {
    //值的title
    title:string,
    //对应值
    value:number,
    //是否显示百分号，默认不显示
    isDisplayPercent?:boolean,
    //保留几位小数，默认为0
    toFixed?:number,
    //是否显示数值分隔符，默认不显示
    showSeparator?:boolean
}
export type OverviewDataCardProps = {
    //卡片的icon src
    titleIconSrc:string,
    //卡片的title
    title:string,
    //是否开启数值动画，默认开启
    isAnimation?:boolean,
    //卡片值
    valueList:Array<ValueListItem>
}
const props = defineProps<OverviewDataCardProps>()

</script>
<style lang="less" scoped>
@import "@/styles/defaultVar.less";
.overview-dataCard-wrapper{
    &.small{
        min-width:300px;
    }
    &.default{
        min-width:350px;
    }
    flex:1;
    padding:12px 24px;
    box-sizing: border-box;
    background-color: #fff;
    border-radius: @default-border-radius;
    margin: 4px 4px 4px 0px;
    .title{
        display: flex;
        align-items: center;
        .icon{
            height: 32px;
            width: 32px;;
        }
        p{
            color: #333333;
            height: 24px;
            font-size: 16px;
            padding-left:4px;
        }
    }
    .content{
        display: flex;
        justify-content: space-around;
        align-items: center;
        padding-top:8px;
        .title{
            color:#666666;
            height: 21px;
            font-size: 14px;
        }
        .value{
            text-align: center;
            color:@primary-color;
            font-family: "BebasNeue";
            font-size: 20px;
        }
        .border{
            width: 0px;
            height: 16px;
            opacity: 1;
            border: 1px solid #EEEEEE;
        }
    }
}




</style>