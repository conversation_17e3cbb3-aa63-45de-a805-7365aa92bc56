<template>
    <div class="wrapper inner-page-height">
        <FormLayout
          :isLoading="isLoading"
          :tableData="tableData"
          :tableColumns="tableColumns"
          :pagination="paginationRef"
          @paginationChange="paginationChange"
          :isNeedCollapse="false"
        >
          <!-- 表单 -->
          <template #searchForm>
            <n-form
              ref="formRef"
              label-placement="left"
              label-width="auto"
              :show-feedback="false"
              require-mark-placement="right-hanging"
              size="small"
              :style="{ width: '100%' }"
            >
              <n-form-item :span="12" :show-label="false" path="">
                <j-search-input
                  v-model:value.trim="model.doctorName"
                  placeholder="请输入医生姓名"
                  @search="handlerSearch"
                />
              </n-form-item>
              <!-- 来源 -->
              <n-form-item label="在线状态">
                <n-select 
                  v-model:value="model.onlineStatus" 
                  :options="InterrogationOnlinePresenceOptions" 
                  style="width: 170px;" 
                  clearable
                />
              </n-form-item>
            </n-form>
          </template>
      
          <template #tableHeaderBtn>
            <n-button @click="refresh" class="store-button">刷 新</n-button>
            <JAddButton v-if="hasAddDoctorAuth" type="primary" @click="handleBasicInformation('add')">新建医生</JAddButton>
          </template>
        </FormLayout>
         <InformationManagement ref="informationManagementShow"/>
    </div>
  </template>
  
  <script lang="tsx" setup name="DoctorManagement">
  import { onMounted, ref, watch } from "vue";
  import FormLayout from "@/layout/FormLayout.vue";
  import { useTableDefault } from "@/hooks/useTableDefault";
  import InformationManagement from './components/InformationManagement.vue'
  import { doctorEntityPage } from "@/services/api";
  import { useMessages } from '@/hooks';
  import JImage from "@/components/JImage/index.vue";
  import { hasAddDoctorAuth, hasEditDoctorAuth, hasEnableChangeDoctorAuth } from "./authList";
  import { InterrogationOnlinePresenceOptions,InterrogationOnlinePresenceLabels,JobTitleLabels } from "@/constants";

  const { createMessageSuccess, createMessageError } = useMessages();
  
  /** 表格hook */
  const {
    isAddLoading,
    isEditLoading,
    isLoading,
    tableData,
    paginationRef,
    pageTableData,
    deleteTableData,
    editTableData,
    addTableData,
    refreshTableData,
    paginationChange,
  } = useTableDefault({
    pageDataRequest: doctorEntityPage,
  });
  
  /* 表格列表项 */
  const tableColumns = ref([
    {
      title: "头像",
      key: "img",
      align: "left",
      fixed: "left",
      width: 100,
      render: rowData => {
          return <JImage imgPath={rowData.img} />;
      },
    },
    {
      title: "姓名",
      key: "doctorName",
      width: 150,
      align: "left",
    },
    {
      title: "科室",
      key: "departmentName",
      width: 180,
      align: "left",
    },
  
    {
      title: "职称",
      key: "title",
      width: 150,
      align: "left",
      render: rowData => {
        return <span>{JobTitleLabels[rowData.title] ?? '-'}</span>;
      },
    },
    {
      title: "医院",
      key: "institutionName",
      width: 150,
      align: "left",
    },
    {
      title: "在线状态",
      key: "onlineStatus",
      width: 150,
      align: "left",
      render: rowData => {
        return <span>{InterrogationOnlinePresenceLabels[rowData.onlineStatus] ?? '-'}</span>;
      },
    },
    {
      title: "启用状态",
      key: "isActived",
      width: 150,
      align: "left",
      render: rowData => {
        let value = '-'
        if(rowData.isActived == 0){
          value = '否'
        }
        if(rowData.isActived == 1){
          value = '是'
        }
        return value
      },
    },
    {
      title: "创建时间",
      key: "createTime",
      width: 150,
      align: "left",
    },
    {
      title: "操作",
      key: "action",
      width: 120,
      fixed: "right",
      align: "left",
      render: row => {
        return (
          <n-space style="padding: 5px 0;">
            {hasEditDoctorAuth ? <n-button text type="primary" onClick={() => handleBasicInformation('edit', row)}>
              编辑
            </n-button> : null}
          </n-space>
        );
      },
    },
  ]);

  /** 打开新建医生抽屉 */
  const informationManagementShow = ref()
  const handleBasicInformation = (type,row) => {
    const _params = {
      type,
      row,
      refresh:refresh
    }
    informationManagementShow.value?.acceptParams(_params);
  };
  
  /** 参数 */
  const model = ref({
    doctorName: '',
    onlineStatus:null
  });

  /** 获取参数 */
  const getParams = () => {
    const { doctorName, onlineStatus } = model.value;
    return {
      doctorName, 
      onlineStatus
    };
  };
  
  /** 搜索 */
  const handlerSearch = () => {
    tableSearch();
  };

  /** 表格刷新 */
  function refresh(){
    tableSearch();
  };

  /* 表格搜索 */
  const tableSearch = () => {
    pageTableData(getParams(), paginationRef.value);
  };

  /** 在线状态 */
  const stateOptions = [
    {
      label:'是',
      value:1
    },
    {
      label:'否',
      value:0
    }
  ]

  /** 监听 */
  watch(() => [
      model.value.onlineStatus,
    ], 
    () => {
      tableSearch();
    }
  );

  /** 组件挂载 */
  onMounted(() => {
    tableSearch();
  });
  </script>
  
  <style lang="less" scoped>
  @import "@/styles/defaultVar.less";
</style>
  