<template>
  <JDrawer
    v-model:show="drawerVisible"
    title="问诊单详情"
    :isGetLoading="isGetLoading"
    :isShowFooter="false"
    @after-leave="closeDrawer"
    to=".table-wrapper"
    :mainStyle="{
      padding: '16px',
      height: 'calc(100% - 50px - 4px)',
      backgroundColor: '#F6F7FA',
    }"
  >
    <template #content>
      <div class="medical-inquiry-form-details">
        <!-- 问诊信息部分 -->
        <div class="info-card">
          <div class="card-title">问诊信息</div>
          <div class="card-content">
            <div v-if="data.name" class="info-item">
              <span class="label">患者姓名：</span>
              <span class="value">{{ data.name }}</span>
            </div>
            <div class="info-item">
              <span class="label">肝功能异常：</span>
              <span class="value">{{ data.isLiver === 1 ? "是" : "否认" }}</span>
            </div>
            <div v-if="data.doctorName" class="info-item">
              <span class="label">医生姓名：</span>
              <span class="value">{{ data.doctorName }}</span>
            </div>

            <div v-if="data.gender" class="info-item">
              <span class="label">患者性别：</span>
              <span class="value">{{ data.gender }}</span>
            </div>
            <div class="info-item">
              <span class="label">肾功能异常：</span>
              <span class="value">{{ data.isKidney === 1 ? "是" : "否认" }}</span>
            </div>
            <div v-if="data.departmentName" class="info-item">
              <span class="label">科室：</span>
              <span class="value">{{ data.departmentName }}</span>
            </div>

            <div v-if="data.age" class="info-item">
              <span class="label">患者年龄：</span>
              <span class="value">{{ data.age }}岁</span>
            </div>
            <div v-if="data.isAllergyHi" class="info-item">
              <span class="label">过敏史：</span>
              <span class="value">{{ data.isAllergyHi }}</span>
            </div>
            <div v-if="data.institutionName" class="info-item">
              <span class="label">机构：</span>
              <span class="value">{{ data.institutionName }}</span>
            </div>

            <div v-if="data.chiefComplaint" class="info-item">
              <span class="label">主诉：</span>
              <span class="value">{{ data.chiefComplaint }}</span>
            </div>
            <div v-if="data.isPersonalMedicalHi" class="info-item">
              <span class="label">个人病史：</span>
              <span class="value">
                {{ data.isPersonalMedicalHi }}
              </span>
            </div>
            <div v-if="data.isHomeMedicalHi" class="info-item">
              <span class="label">家族病史：</span>
              <span class="value">{{ data.isHomeMedicalHi }}</span>
            </div>

            <div v-if="data.period" class="info-item">
              <span class="label">本次患病多久：</span>
              <span class="value">{{ periodMap.get(data.period) }}</span>
            </div>
            <div class="info-item">
              <span class="label">备孕、妊娠、哺乳：</span>
              <span class="value">{{ data.isPreparePregnant === 1 ? "有" : "无" }}</span>
            </div>
          </div>
        </div>

        <!-- 订单信息部分 -->
        <div class="info-card">
          <div class="card-title">问诊单信息</div>
          <div class="card-content">
            <div class="info-item">
              <span class="label">问诊单状态：</span>
              <span class="value">
                {{ medicalInquiryFormStatusMap.get(data.consultationStatus) }}
              </span>
            </div>
            <div v-if="data.consultationStatus" class="info-item">
              <span class="label">问诊单号：</span>
              <span class="value">{{ data.code }}</span>
            </div>
            <div v-if="data.createTime" class="info-item">
              <span class="label">创建时间：</span>
              <span class="value">{{ data.createTime }}</span>
            </div>

            <div v-if="data.successTime" class="info-item">
              <span class="label">支付时间：</span>
              <span class="value">{{ data.successTime }}</span>
            </div>
            <div v-if="data.receiveStartTime" class="info-item">
              <span class="label">接诊时间：</span>
              <span class="value">{{ data.receiveStartTime }}</span>
            </div>
            <div v-if="data?.receiveEndTime && data?.consultationStatus == 4" class="info-item">
              <span class="label">完成时间：</span>
              <span class="value">{{ data.receiveEndTime }}</span>
            </div>

            <div v-if="data?.cancelTime && data?.consultationStatus == 5" class="info-item">
              <span class="label">取消时间：</span>
              <span class="value">{{ data.cancelTime }}</span>
            </div>
            <div v-if="data?.cancelReason && data?.consultationStatus == 5" class="info-item">
              <span class="label">取消原因：</span>
              <span class="value">{{ data.cancelReason }}</span>
            </div>
            <div class="info-item">
              <span class="label">订单金额：</span>
              <span class="value">￥ {{ convertFenToYuanStr(data.fee) }}</span>
            </div>

            <div v-if="data.wxPayNo" class="info-item">
              <span class="label">支付单号：</span>
              <span class="value">{{ data.wxPayNo }}</span>
            </div>
            <div v-if="data.mchId" class="info-item full-width">
              <span class="label">商户号：</span>
              <span class="value">{{ data.mchId }} [{{ data.merchantPlatformName }}, {{ data.merchantCompany }}]</span>
            </div>
          </div>
        </div>

        <!-- 预约信息部分 -->
        <div v-if="data.type === 2" class="info-card">
          <div class="card-title">预约信息</div>
          <div class="card-content">
            <div class="info-item">
              <span class="label">预约时间：</span>
              <span class="value">
                {{ data.preBookTime }}
              </span>
            </div>
            <div class="info-item">
              <span class="label">预约单创建人：</span>
              <span class="value">{{ data.preBookUserName }}</span>
            </div>
            <div v-if="data.physicianAssistantName" class="info-item">
              <span class="label">医助：</span>
              <span class="value">{{ data.physicianAssistantName }}</span>
            </div>
          </div>
        </div>
      </div>
    </template>
  </JDrawer>
</template>

<script lang="ts" setup name="NewDoctor">
import { ref, watch } from "vue";
import type { MedicalInquiryFormDetailData } from "@/typings/api.d";
import { getMedicalInquiryFormDetail } from "@/services/api";
import {
  periodMap,
  medicalInquiryFormStatusMap
} from "@/views/ConsultationModule/MedicalInquiryForm/types";
const initialData: MedicalInquiryFormDetailData = {
  age: 0,
  chiefComplaint: "",
  code: "",
  consultationStatus: 0,
  createTime: "",
  departmentName: "",
  doctorName: "",
  fee: 0,
  gender: "",
  institutionName: "",
  isAllergyHi: "",
  isHomeMedicalHi: "",
  isKidney: 0,
  isLiver: 0,
  isPersonalMedicalHi: "",
  isPreparePregnant: 0,
  mchId: "",
  merchantCompany: "",
  merchantPlatformName: "",
  name: "",
  period: 0,
  receiveStartTime: "",
  receiveEndTime: "",
  successTime: "",
  wxPayNo: "",
  cancelTime: "",
  cancelReason: "",
  type:1,
  preBookTime:"",
  preBookUserName:"",
  physicianAssistantName:""
};
const data = ref({ ...initialData });
const props = withDefaults(defineProps<{
    convertFenToYuanStr?: (fen:string | number) => string;
}>(), {
});

/** 获取参数 */
const isGetLoading = ref(false);

/** 抽屉状态 */
const drawerVisible = ref(false);

const drawerProps = ref();
const acceptParams = async ({ row, convertFenToYuanStr }) => {
  drawerVisible.value = true;
  isGetLoading.value = true;
  const res = await getMedicalInquiryFormDetail({ id: row?.id });
  console.log("res", res);
  data.value = {
    ...data.value,
    ...res,
  };
  isGetLoading.value = false;
  drawerProps.value = row;
};

/** 关闭抽屉 */
const closeDrawer = () => {
  data.value = { ...initialData };
  drawerVisible.value = false;
};

defineExpose({
  acceptParams,
  closeDrawer,
  drawerVisible,
});
</script>

<style lang="less" scoped>
@small-desktop: ~"(min-width: 1024px) and (max-width: 1279px)";
@medium-desktop: ~"(min-width: 1280px) and (max-width: 1599px)";
@large-desktop: ~"(min-width: 1600px)";

.medical-inquiry-form-details {
  background-color: #f5f5f5;
  height: 100%;
  .info-card {
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 12px;
    overflow: hidden;
    padding: 16px 40px;
    .card-title {
      font-size: 24px;
      color: #333333;
      margin-bottom: 28px;
    }
    .card-content {
      display: grid;
      grid-template-columns: repeat(3, (100% / 3));
      gap: 10px;
      .info-item {
        .label {
          color: #666;
          margin-right: 4px;

          @media @large-desktop {
            font-size: 15px;
          }
        }

        .value {
          color: #333;
          word-break: break-word;

          @media @large-desktop {
            font-size: 15px;
          }

          &.price {
            color: #f56c6c;
            font-weight: bold;
          }
        }
      }
    }
  }
}
</style>
