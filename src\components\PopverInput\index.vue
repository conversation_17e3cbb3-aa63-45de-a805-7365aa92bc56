<template>
  <n-popover style="padding: 6px 12px;" trigger="focus" placement="top-start" :disabled="props.isDisabled">
    <template #trigger>
        <n-input 
          v-if="!props.isNumber"
          v-bind="$attrs"
          :style="{width: isNumber(props.width) ? `${props.width}px` : props.width }"
          size="small" 
          type="text"
          v-model:value="inputValue"
          @blur="handleChangeValue"
          clearable
         />
        <n-input-number
          v-else
          v-bind="$attrs"
          :show-button="false"
          :style="{width: isNumber(props.width) ? `${props.width}px` : props.width }"
          size="small" 
          v-model:value="inputValue"
          @blur="handleChangeValue"
         />
    </template>
    <span>{{ inputValue }}</span>
  </n-popover>
</template>

<script lang="ts" setup name="PopverInput">
import { ref, watch } from "vue";
import { isNumber } from "@/utils/isUtils";

const inputValue = ref<string | number>('');

const props = withDefaults(defineProps<{
  value: string | number;
  isDisabled?: boolean;
  width?: string | number;
  isNumber?: boolean;
}>(), {
  isDisabled: false,
  width: 160,
  isNumber: false
});

const emits = defineEmits<{
  (e: 'UpdateValue', value: string | number): void
}>();

const handleChangeValue = () => {
  emits('UpdateValue', inputValue.value);
}

watch(() => props.value, (newVal) => {
  inputValue.value = newVal;
}, {
  immediate: true
});
</script>

<style lang="less" scoped></style>
