
/** 
 * @des 积分配置
 */
export type PointCofigType = 1 | 2 | 3;
export const enum PointCofigEnum {
   /** 每日来访 */
   DAILYVISIT = 1,
   /** 查看商品 */
   VIEWPRODUCT = 2,
   /** 签到 */
   SIGNIN = 3,
}

/** 最大积分 */
export const MAXPOINT = 99999;

/** 会员等级最低积分 -- 最高输入 小于100万 */
export const MEMBERMAXPOINT = 999999;

/** 最小积分 */
export const MINPOINT = 1;

/** 最小查看时间 */
export const MINVIEWTIME = 1;

/** 
 * @des 会员积分
 */
export type MemberPointsType = 1 | 2;
export const enum MemberPointsEnum {
   /** 会员积分介绍页 */
   INTRODUCTION = 1,
   /** 会员积分配置页 */
   CONFIGURATION = 2,
}