<template>
  <div
    class="table-wrapper"
    ref="domRef"
    :style="{
      height: typeof height === 'string' ? height : `${height}px`,
    }"
  >
    <div
      class="header-wrapper"
      ref="headerLeftDomRef"
      v-if="isDisplayHeader"
      :style="{
        borderRadius: isUseOverview?`5px`:'0px',
      }"
    >
      <div class="header" :style="{
        position: 'relative',
        height: 'auto',
        width:`${headerLeftWrapperWidth}px`,
        boxSizing: 'border-box',
        paddingRight:'10px'
      }">
        <!-- <n-scrollbar x-scrollable :style="`width:${headerLeftWrapperWidth}px;padding-bottom: 10px;padding-right: 5px;position: absolute;height: 40px;box-sizing: border-box;top: 3px;`">
          <div :style="{width:totalFormWidth +'px'}" ref="headerLeftDomRefkd">
           <slot name="headerLeftBtn"></slot>
          </div>
        </n-scrollbar> -->
        <div :style="`width:100%;`">
          <div ref="headerLeftDomRefkd" class="search-form">
            <slot name="headerLeftBtn"></slot>
          </div>
        </div>
        <!-- <n-space>
                    <slot name="headerLeftBtn"></slot>
                    <template v-for="data in headerData">
                        <span class="label">{{data.label}}</span>
                        <span class="value">{{data.value}} {{data.unit}}</span>
                    </template>
                </n-space> -->
      </div>
      <div class="btn" ref="headerBtnDomRef">
        <n-space align="center">
          <!-- <n-button @click="refShow = true">自定义列</n-button> -->
          <slot name="btn"></slot>
        </n-space>
      </div>
    </div>
    <div class="breadcrumb-wrapper" v-if="isUseBreadcrumb">
      <slot name="breadcrumb"></slot>
    </div>
    <div class="overview-wrapper" v-if="isUseOverview" ref="overviewDomRef">
      <slot name="overview"></slot>
    </div>
    <n-data-table
      :remote="!defaultExpandAll"
      size="small"
      ref="table"
      :columns="cols"
      :data="tableData"
      :loading="isLoading"
      flex-height
      :pagination="isPagination?defaultPagination:false"
      :style="{
        height: `${tableInnerHeight}px`,
        padding:'0px 12px 12px 12px',
        paddingTop:isUseOverview?`12px`:'0px',
        boxSizing:'border-box',
        borderRadius: isUseOverview?`5px`:'0px',
      }"
      @update:page="handlePageChange"
      @update:page-size="handlePageSizeChange"
      :max-height="tableInnerHeight"
      :bordered="true"
      :single-line="false"
      :scroll-x="totalWidthRef"
      striped
      @update:checked-row-keys="handleCheck"
      :rowKey="setRowKey"
      :row-props="tableRowProps"
      :summary="tableSummaryFn"
      :default-checked-row-keys="defaultCheckedKeys"
      :checked-row-keys="_checkedRowsKeys"
      @update:filters="handleFiltersChange"
      @update:sorter="handleSorterChange"
      :default-expand-all="defaultExpandAll"
    >
      <template #empty>
        <div class="infoWrapper">
          <img :src="EmptyDataSrc" alt="" />
          <div class="notice">暂无数据</div>
        </div>
      </template>
    </n-data-table>
    <n-modal
      v-model:show="batchDeleteConfirmShow"
      :auto-focus="false"
      :show-icon="false"
      preset="dialog"
      :title="batchDeleteConfirmMainNotice"
      :content="batchDeleteConfirmSubNotice"
      :positive-text="batchDeleteConfirmText"
      :positive-button-props="{
        type: 'error',
      }"
      negative-text="取消"
      @positive-click="handleBatchDeleteConfirm"
    />
    <template v-if="customCols">
      <CompactHeaders v-model:show="refShow" v-model:columns="cols" :tableKey="key"></CompactHeaders>
    </template>
    <Transition appear name="fade-transform" mode="out-in">
      <div class="footer-slot-wrapper" v-show="isShowFooterWrapperRef">
        <n-space align="center">
          <div :style="`width:${footerSelectorWrapperWidthRef}px;text-align: center;`">
            <n-checkbox
              :indeterminate="isFooterCheckBoxIndeterminateRef"
              @update:checked="handlerFooterCheckboxChecked"
              :checked="footerCheckBoxValueRef"
            ></n-checkbox>
          </div>
          <span>
            已选择
            <span class="footer-selected-count">
              {{
              _checkedRowsKeys.length
              }}
            </span>
            项
          </span>
          <template v-if="isBatchDelete" >
            <!-- <n-button type="error" size="small" ghost @click="handleBatchDelete">{{ batchDeleteText }}</n-button> -->
          </template>
          <slot name="footer-btn"></slot>
        </n-space>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import {
  toRefs,
  reactive,
  watch,
  ref,
  nextTick,
  onUnmounted,
  useSlots,
  toRef,
  toRaw,
  computed,
  onMounted,
} from "vue";
import CompactHeaders from "@/components/CompactHeaders/CompactHeaders.vue";

import { SystemSetting } from "@/settings/systemSetting";
import type { HeaderData, PaginationProps, TableDataType } from "./type";
import { useCompactHeaders } from "@/hooks/useCompactHeaders";

import {useFooterCheck} from "./hooks/useFooterCheck";





import type {
  DataTableBaseColumn,
  DataTableColumn,
  DataTableCreateSummary,
  DataTableFilterState,
  DataTableSortState,
} from "naive-ui";
import { isArray, isString, isVue3SlotExist } from "@/utils/isUtils";
import EmptyDataSrc from "@/assets/image/exception/emptyData.png";
import { useOverviweHeightResizeObserver } from "./hooks/useOverviweHeightResizeObserver";
const {overviewDomRef,overviewDomHeightRef} = useOverviweHeightResizeObserver()
export interface LTableProps {
  height?: string | number;
  headerData?: HeaderData[];
  isLoading: boolean;
  isBatchDelete?: boolean;
  batchDeleteText?: string;
  batchDeleteConfirmText?: string;
  batchDeleteConfirmMainNotice?: string;
  batchDeleteConfirmSubNotice?: string;
  tableColumns: Array<any>;
  tableData: Array<TableDataType | null>;
  pagination: PaginationProps;
  defaultCheckedKeys?: Array<string>;
  summary?: object;
  tableKey?: string;
  customCols?: boolean;
  isSelection?: boolean;
  isUseBreadcrumb?: boolean;
  isUseOverview?:boolean;
  isPagination?:boolean;
  isDisplayIndex?:boolean;
  isDisplayHeader?:boolean;
  defaultExpandAll?:boolean;
  rowKey?:string;
  rowProps?:()=>{},
  isRowClick?:boolean,
  totalRendering?:boolean
}
const domRef = ref(null);
const headerLeftDomRef = ref(null);
const headerBtnDomRef = ref(null)
const batchDeleteConfirmShow = ref(false);
const setRowKey = (row) => row[props.rowKey];
const btnSlots = useSlots().btn();
const breadcrumbSlots = useSlots().breadcrumb();
const isBtnSlotsExistRef = ref<boolean>(
  btnSlots.length === 1 ? isVue3SlotExist(btnSlots[0]) : true
);
const isBreadcrumbSlotsExistRef = ref<boolean>(
  breadcrumbSlots.length === 1 ? isVue3SlotExist(breadcrumbSlots[0]) : true
);
const props = withDefaults(defineProps<LTableProps>(), {
  headerData: () => {
    return [];
  },
  isLoading: false,
  isBatchDelete: true,
  batchDeleteText: "删除",
  batchDeleteConfirmText: "确认",
  batchDeleteConfirmMainNotice: "是否从列表中移除",
  batchDeleteConfirmSubNotice: "移除后这些内容将不可见",
  height: 600,
  customCols: false,
  isSelection: true,
  isPagination:true,
  isDisplayIndex:true,
  isDisplayHeader:true,
  defaultExpandAll:false,
  isUseOverview:false,
  isRowClick:false,
  tableColumns: () => {
    return [];
  },
  tableData: () => {
    return [];
  },
  pagination: () => {
    return {
      current: 1,
      total: 1,
      pageSize: SystemSetting.pagination.pageSize,
    };
  },
  defaultCheckedKeys: () => {
    return [];
  },
  isUseBreadcrumb: false,
  rowKey: "id",
  totalRendering:true
});
const emit = defineEmits<{
  (e: "paginationChange", pagination: PaginationProps): void;
  (
    e: "filtersChange",
    fileterProps: {
      filters: DataTableFilterState;
      initiatorColumn: DataTableBaseColumn;
      searchParams: Object;
    }
  ): void;
  (
    e: "selectedKeysChange",
    selectedKeys: Array<string>,
    tableData: Array<object>
  ): void;
  (e: "batchDelete", selectedKeys: Array<string>): void;
  (e: "sorterChange", value: { sort: string; sortAsc: "descend" | "ascend" | false }): void;
  (e: "selectedKeysChangeInfo",
  keys: Array<string | number>,
    rows: object[],
    meta: {
      row: object | undefined,
      action: 'check' | 'uncheck' | 'checkAll' | 'uncheckAll'
  }): void; // checked-row-keys 值改变时触发的回调函数
}>();

const {
  headerData,
  tableData,
  isLoading,
  pagination,
  defaultCheckedKeys,
  customCols,
  summary,
  isUseBreadcrumb,
  isPagination,
  isDisplayHeader,
  defaultExpandAll,
  tableColumns,
  totalRendering
} = toRefs(props);

const key = toRef(props, "tableKey");
const _checkedRowsKeys = ref([...defaultCheckedKeys.value]);
const { cols, openModal, totalWidth, refShow, tableSummaryFn, sorterInfo } =
  useCompactHeaders({
    columns: tableColumns.value,
    isDisplayIndex: props.isDisplayIndex,
    key: key.value,
    customCols: props.customCols,
    summary: summary,
    isSelection: props.isSelection,
  });


const allIdsList = computed(() => {
  return tableData.value.map((data) => data.id);
});

  const {
    isShowFooterWrapperRef,
    footerCheckBoxValueRef,
    isFooterCheckBoxIndeterminateRef,
    footerSelectorWrapperWidthRef,
    handlerFooterCheckboxChecked
  } = useFooterCheck({
    checkedRowsKeysRef:_checkedRowsKeys,
    allIdsListRef:allIdsList,
    checkedHandle:checkedHandle
  })




const totalWidthRef = ref(totalWidth);
watch(cols, (newVal) => {
  if (newVal) {
    let widthSum = 0;
    newVal.forEach((item) => {
      widthSum = widthSum + item.width;
    });
    totalWidthRef.value = widthSum;
  }
});
watch(tableData,(newVal)=>{
  if(newVal && newVal.length){
    nextTick(()=>{
      handlerAlignRightPadding()
      if(totalRendering.value){
        setSummaryHeader()
      }
    })
  }
})


function setSummaryHeader(){
  const _headerDom = document.querySelector('.n-data-table-tr.n-data-table-tr--summary.summaryHeader')
  if(_headerDom){
    _headerDom.remove()
  }
  const _dom = document.querySelector('.n-data-table-tr.n-data-table-tr--summary')
  if(_dom){
    const _cloneDom = _dom.cloneNode(true)
    _cloneDom.classList.add('summaryHeader')
    _cloneDom.style.bottom = '33px'
    for (let el of _cloneDom.children){
      const colKey = el.attributes['data-col-key']?.value
      if(colKey && (colKey !== 'operation' && colKey !== 'action')){
        const targetCol = tableColumns.value.find(item=>item.key == colKey)
        if(targetCol){
          const _spanDom = el.getElementsByClassName('summaryText')[0]
          if(_spanDom){

            const _title = isString(targetCol.summaryTitle)? targetCol.summaryTitle:targetCol.colName
            const _content = targetCol.key == 'index'?'合计表头':_title
            _spanDom.textContent = _content
            _spanDom.title = _content
          }
        }
      }
    }
    _dom.parentElement.insertBefore(_cloneDom,_dom)
  }
}


watch(tableColumns,(newVal)=>{
  if(isArray(newVal) && newVal.length){
    const { cols:temCols, totalWidth } =
    useCompactHeaders({
      columns: newVal,
      isDisplayIndex: props.isDisplayIndex,
      key: key.value,
      customCols: props.customCols,
      summary: summary,
      isSelection: props.isSelection,
    });
    cols.value = temCols.value;
    totalWidthRef.value = totalWidth
  }
})
const headerLeftWrapperWidth = ref(0);
const tableInnerHeight = ref<number>(500);
const calcTableInnerHeight = () => {
  tableInnerHeight.value = domRef.value
    ? domRef.value.offsetHeight -
      (isDisplayHeader.value ? headerLeftDomRef.value.offsetHeight : 0) -
      (isBtnSlotsExistRef.value ? 34 : 0) -
      10 -
      (isUseBreadcrumb.value ? 34 : 0) - (props.isUseOverview ? overviewDomHeightRef.value : 0 ) + 24
    : 0;
};

const calcHeaderLeftDomWidth = ()=>{
  if(isDisplayHeader.value){
    let btnWidth = headerBtnDomRef.value.offsetWidth;
    headerLeftWrapperWidth.value = headerLeftDomRef.value?headerLeftDomRef.value.offsetWidth - btnWidth - 5 - 24:1000;
  }
}

const createTableResizeObserver = () => {
  return new ResizeObserver(() => {
    calcTableInnerHeight();
    calcHeaderLeftDomWidth()
  });
};
const tableResizeObserver = createTableResizeObserver();
/**  DOM 更新循环结束之后执行延迟回调 */
let timer = null;
nextTick().then(() => {
		// 清除前一个定时器
		if (timer) {
			clearTimeout(timer);
		}
		timer = setTimeout(() => {
			try {
				calcTableInnerHeight();
				calcHeaderLeftDomWidth();
				// 观察表格是否调整大小
				if (domRef.value) {
					tableResizeObserver.observe(domRef.value as HTMLElement);
				}
			} catch (error) {
				// 处理或者抛出错误
				console.error(error);
			} finally {
				clearTimeout(timer);
				timer = null;
			}
		}, 0);
	})
	.catch((error) => {
		// 处理或者抛出 nextTick 的错误
		console.error(error);
});
function handlerAlignRightPadding(){
  cols.value.forEach((item)=>{
    if(item.align == 'right'){
      if(item.sorter){
        const targetDomList = document.querySelectorAll(`td[data-col-key=${item.key}]`)
        if(targetDomList){
          targetDomList.forEach((dom)=>{
            dom.style.paddingRight = '27px'
          })
        }
      }
    }
  })
}
onUnmounted(() => {
  tableResizeObserver.disconnect();
});

watch(pagination, ({ current, total, pageSize }) => {
  defaultPagination.page = current;
  defaultPagination.itemCount = total;
  defaultPagination.pageSize = pageSize;
});
watch(isLoading, (newVal) => {
  if (newVal) _checkedRowsKeys.value = [...defaultCheckedKeys.value];
});
const defaultPagination = reactive({
  page: 1,
  itemCount: 0,
  pageSize: SystemSetting.pagination.pageSize,
  pageCount: 100,
  pageSizes: [
    {
      label: "30/页",
      value: 30,
    },
    {
      label: "50/页",
      value: 50,
    },
    {
      label: "100/页",
      value: 100,
    },
    {
      label: "200/页",
      value: 200,
    },
    {
      label: "300/页",
      value: 300,
    },
  ],
  showSizePicker: true,
  showQuickJumper: true,
  prefix({ itemCount }) {
    return `共${itemCount}条记录`;
  },
  suffix() {
    return "页";
  },
});

const handlePageChange = (page: number) => {
  emit("paginationChange", {
    current: page,
    pageSize: defaultPagination.pageSize,
  });
};
const handlePageSizeChange = (pageSize: number) => {
  emit("paginationChange", {
    current: 1,
    pageSize: pageSize,
  });
};
const handleFiltersChange = (
  filters: DataTableFilterState,
  initiatorColumn: DataTableBaseColumn
) => {
  const searchParams = {};
  for (let key in filters) {
    const _targetColInfo = props.tableColumns.find((item) => item.key === key);
    if (!_targetColInfo.searchKey)
      throw new Error(`${_targetColInfo.title}该列无设置searchKey参数`);
    else {
      searchParams[_targetColInfo.searchKey] = filters[key];
    }
  }
  emit("filtersChange", {
    filters,
    initiatorColumn,
    searchParams,
  });
};
const handleCheck = (keys: Array<string | number>, rows: object[], meta: { row: object | undefined, action: 'check' | 'uncheck' | 'checkAll' | 'uncheckAll' }) => {
  _checkedRowsKeys.value = keys;
  emit(
    "selectedKeysChange",
    keys,
    tableData.value.filter((item) => keys.includes(item[props.rowKey]))
  );
  emit("selectedKeysChangeInfo", keys, rows, meta);
};
function tableRowProps(row) {
  if (props.rowProps){
    return props.rowProps()
  }else if(props.isRowClick){
    return {
      style: "cursor: pointer;",
      onClick: (e) => {
        if (e.target.className === 'n-checkbox-box__border'){
          return;
        }
        const _rowIndex = _checkedRowsKeys.value.indexOf(row[props.rowKey])
        if (_rowIndex > -1){
          _checkedRowsKeys.value.splice(_rowIndex,1);
        }else{
          if (tableColumns.value?.length > 0 && tableColumns.value[0]?.multiple === false){
            _checkedRowsKeys.value = [];
          }
          _checkedRowsKeys.value.push(row[props.rowKey])
        }
        emit(
          "selectedKeysChange",
          toRaw(_checkedRowsKeys.value),
          tableData.value.filter((item) => _checkedRowsKeys.value.includes(item[props.rowKey]))
        );
      },
    };
  }
}
function checkedHandle(keys: Array<string | number>){
  emit(
    "selectedKeysChange",
    keys,
    tableData.value.filter((item) => keys.includes(item[props.rowKey]))
  );
}
const handleBatchDelete = () => {
  batchDeleteConfirmShow.value = true;
};
const handleBatchDeleteConfirm = () => {
  batchDeleteConfirmShow.value = false;
  emit("batchDelete", _checkedRowsKeys.value);
};
const Sorter_Order_List = ["descend", "ascend", false];
let _lastSortKey = sorterInfo.value.defaultKey;
const handleSorterChange: (
  options: DataTableSortState | DataTableSortState[] | null
) => void = (options) => {
  const { columnKey } = options;
  if (_lastSortKey && _lastSortKey != columnKey) {
    sorterInfo.value.type = false;
  }
  _lastSortKey = columnKey;
  const sortKey = columnKey;
  const index = Sorter_Order_List.indexOf(sorterInfo.value.type) + 1;
  const nextType =
    Sorter_Order_List[index < Sorter_Order_List.length ? index : 0];
  if (nextType === false) {
    sorterInfo.value.key = sorterInfo.value.defaultKey;
    _lastSortKey = sorterInfo.value.defaultKey;
    sorterInfo.value.type = "descend";
  } else {
    sorterInfo.value.key = sortKey;
    sorterInfo.value.type = nextType;
  }
  emit("sorterChange", {
    sort: sorterInfo.value.key,
    sortAsc: sorterInfo.value.type,
  });
};
const totalFormWidth = ref(null)
const headerLeftDomRefkd = ref(null)
onMounted(() => {
  if(isDisplayHeader.value){
    const totalWidth = headerLeftDomRefkd.value.scrollWidth + 100
    totalFormWidth.value = totalWidth
  }

  // console.log('所有 n-form-item 元素的总宽度为：', totalWidth);
});
</script>

<style scoped lang="less">
@import "@/styles/defaultVar.less";
.table-wrapper {
  background: #ffffff;
  // border-radius: @default-border-radius;
  // border-radius: @default-border-radius;
  // padding: 12px;
  width: 100%;
  box-sizing: border-box;
  position: relative;
  .overview-wrapper{

  }
  .header-wrapper{
    padding: 12px 12px 0px 12px;
  }
  .header-wrapper,
  .breadcrumb-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    height: auto;
    // height: 34px;
  }
  .header {
    line-height: 22px;
    color: @primary-color;
    font-size: @font-size-base;
    line-height: 34px;
    flex:1;
    .label {
      color: @secondary-text-color;
    }
  }
  .btn {
    flex-shrink: 0;
  }
  .infoWrapper {
    width: 100%;
    text-align: center;
    padding: 70px;
    box-sizing: border-box;
  }
  .notice {
    color: #333333;
    line-height: 29px;
    font-size: 16px;
  }
  img {
    height: 210px;
  }
  .footer-slot-wrapper {
    position: absolute;
    bottom: 19px;
    height: 44px;
    width: calc(100% - 24px);
    background: rgba(255, 255, 255, 0.6);
    display: flex;
    align-items: center;
    padding-right: 0px 17px;
    box-sizing: border-box;
    border-top: 1px solid #eee;
    background: #fff;
    left:12px;
    .footer-selected-count {
      font-weight: 600;
      color: @primary-color;
    }
  }
}
:deep(.search-form .n-form) {
  display: flex;
  flex-wrap: wrap;
}
:deep(.search-form .n-form .n-form-item ) {
  flex-shrink: 0;
  flex-grow: 0;
  margin-bottom: 10px;
  margin-right: 24px;
  margin-left: 0px;
}
:deep(.search-form .n-form-item.n-form-item--left-labelled .n-form-item-label) {
  width: auto !important;
}

</style>
