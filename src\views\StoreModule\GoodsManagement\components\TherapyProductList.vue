<template>
    <div class="wrapper">
        <!-- 表头 -->
        <div v-if="props.type !== 'view'" class="header">
            <JTextButton text type="primary" @click="openAddTherapyProductList">新增产品</JTextButton>
        </div>
        <n-data-table 
            :columns="columns" 
            :data="tableDataRef" 
            :row-key="row => row.id" 
            default-expand-all
            :style="{ height: `${240}px` }" 
            flex-height
            :single-line="false"
            size="small" 
            style="background-color: #fff;"
        >
        </n-data-table>
        <!-- 新增产品 -->
        <AddTherapyProductList 
            ref="addTherapyProductListRef"
            :checked-keys="checkedkeys"
            @check-add-product="handleAddProuct"
         />
    </div>
</template>

<script setup lang="tsx" name="TherapyProductList">
import { ref, watch, computed } from "vue";
/** 相关组件 */
import AddTherapyProductList from "./AddTherapyProductList/index.vue";
import JImage from "@/components/JImage/index.vue";

type GoodsSpecificationTable = {
    type: 'add' | 'edit' | 'view';
    value: Array<{
        id: string;
        dosage: string;
        imgPath: string;
        name: string;
    }>,
}

/** Props */
const props = withDefaults(defineProps<GoodsSpecificationTable>(), {});

/** emits */
const emits = defineEmits<{
    (e: 'update:value', value: Array<{
        id: string;
        dosage: string;
        imgPath: string;
        name: string;
    }>,
    ): void;
}>();

/** 表格数据 */
const tableDataRef = ref([]);

/** 已选的产品Id */
const checkedkeys = computed(() => {
    let checkedId = [];
    tableDataRef.value.forEach(item => {
        checkedId.push(item?.id);
    });
    return checkedId;
});

/** 表单项 */
const columns = [
    {
      title: "序号",
      width: 120,
      fixed: "left",
      key: "index",
      align: "center",
      render: (renderData: object, index: number) => {
        return `${index + 1}`;
      },
    },
    {
      title: "产品图",
      key: "imgPath",
      align: "left",
      width: 160,
      render: row => {
        return <JImage imgPath={row.imgPath} />;
      },
    },
    {
        title: '产品名称',
        key: 'name',
        align: "left",
    },
    {
        title: '服用方式',
        key: 'dosage',
        width: 120,
        align: "left",
    },
    {
        title: '操作',
        key: 'operation',
        width: 120,
        fixed: "right",
        render: (row, index) => {
            return (
                <>
                    {/* 删除 */}
                    <n-popconfirm onPositiveClick={() => handleDeleteProuct(row.id)}>
	    	        	{{
	    	        		default: () => {
	    	        			return (
	    	        				<span>{`确认删除《${row.name}》产品吗？`}</span>
	    	        			);
	    	        		},
	    	        		trigger: () => {
	    	        			return (
	    	        				<n-button text type="error">
	    	        					删除
	    	        				</n-button>
	    	        			);
	    	        		},
	    	        	}}
	    	        </n-popconfirm>
                </>
            )
        },
    }
];

/** 打开新建商品分类 */
const addTherapyProductListRef = ref<InstanceType<typeof AddTherapyProductList> | null>(null);
const openAddTherapyProductList = () => {
    const _params = {};
	addTherapyProductListRef.value?.acceptParams(_params);
};

/** 新增商品 */
const handleAddProuct = (row: {
    id: string;
    dosage: string;
    imgPath: string;
    name: string;
}) => {
    tableDataRef.value.push(row);
    emits("update:value", tableDataRef.value);
};

/** 删除商品 */
const handleDeleteProuct = (id: string) => {
    tableDataRef.value = tableDataRef.value.filter(item => item?.id !== id);
    emits("update:value", tableDataRef.value);
};

/** 监听 */
watch(() => props.value, (newVal) => {
    if (newVal) {
       tableDataRef.value = [...newVal];
    }
}, { immediate: true });
</script>


<style lang="less" scoped>
.wrapper {
    position: relative;
    .header {
        position: absolute;
        top: -38px;
        right: 0;
    }
}
</style>