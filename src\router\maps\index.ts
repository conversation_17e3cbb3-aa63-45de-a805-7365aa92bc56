import { deepmerge } from "deepmerge-ts";
const MapsModules = import.meta.glob('./*.map.ts',{ eager: true })
const MapsObject = {}
Object.values(MapsModules).forEach((modules)=>{
  for(let key in modules as object){
    Object.assign(MapsObject,modules[key])
  }
})
function addRouteNameByKey(routesMap) {
  const _temp = deepmerge({}, routesMap);
  for (const name in _temp) {
    _temp[name].name = name;
  }
  return _temp;
}

export const routesMap = addRouteNameByKey({
  ...MapsObject,
});
