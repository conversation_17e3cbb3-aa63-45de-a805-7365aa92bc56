export const dosageUnitsMap = new Map([
    [1, "粒"],
    [2, "片"],
    [3, "包"],
    [4, "袋"],
    [5, "盒"],
    [6, "瓶"],
    [7, "板"],
    [8, "支"],
    [9, "台"],
    [10, "件"],
    [11, "套"],
    [12, "个"],
    [13, "罐"],
    [14, "管"],
    [15, "双"],
    [16, "卷"],
    [17, "把"],
    [18, "对"],
    [19, "条"],
    [20, "贴"],
    [21, "张"],
    [22, "只"],
    [23, "筒"],
    [24, "组"],
    [25, "付"],
    [26, "本"],
    [27, "μg"],
    [28, "mg"],
    [29, "g"],
    [30, "kg"],
    [31, "ml"],
    [32, "L"]
]);

// 给药方式
export const routeOfAdministrationMap = new Map<number, string>([
    [1, '口服'],
    [2, '外用'],
    [3, '吸入'],
    [4, '舌下给药'],
    [5, '直肠给药'],
    [6, '静脉注射'],
    [7, '肌肉注射'],
    [8, '皮下注射'],
    [9, '水煎服'],
    [10, '冲服'],
    [0, '其他']
])

// 用药频次
export const frequencyOfAdministrationMap = new Map([
    [1, "1次/天"],
    [2, "2次/天"],
    [3, "3次/天"],
    [4, "4次/天"],
    [5, "1次/小时"],
    [6, "1次/3小时"],
    [7, "1次/6小时"],
    [8, "1次/8小时"],
    [9, "1次/12小时"],
    [10, "1次/周"],
    [11, "2次/周"],
    [12, "隔天1次"],
    [13, "每月1次"],
    [14, "每3月1次"],
    [15, "每日一剂"],
    [16, "每日两剂"],
    [17, "隔日一剂"],
    [0, "其他"]
]);
export const chineseDosageCountMap = new Map<number, number>();
for (let i = 1; i <= 100; i++) {
    chineseDosageCountMap.set(i, i);
}