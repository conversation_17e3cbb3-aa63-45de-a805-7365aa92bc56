<template>
  <div class="wrapper">
    <div class="doctor-select">
      <div class="header">
        <JSearchInput
          v-model:value="searchValue"
          placeholder="请输入供应商姓名"
          @search="handleSearch"
          size="small"
          width="100%"
        />
      </div>
      <!-- 供应商列表 -->
      <div class="doctor-list-wrapper">
        <transition appear name="fade" mode="out-in">
          <template v-if="supplierList.length > 0">
            <div class="doctor-selected-wrapper">
              <!-- <n-checkbox
                class="check-all"
                v-model:checked="model.checkedAllRef"
                label="全选"
                :disabled="selectAll"
                @update:checked="handleCheckedAllChange"
              /> -->
              <n-spin :show="isGetLoading" size="small" style="height: calc(100% - 36px)">
                <div class="doctor-selected-list" @scroll="handleScroll">
                  <n-space item-style="display: flex;" vertical>
                    <n-checkbox
                      class="doctor-list-item"
                      v-for="item in supplierList"
                      :value="item.id"
                      :key="item.id"
                      :checked="checkDoctorId === item.id"
                      @update:checked="handleUpdateValue(item)"
                      :disabled="item.disabled"
                    >
                      <template #default>
                        <CustomOptions :infoItem="item">
                          <template #Info>
                            <n-ellipsis style="max-width: 200px">
                              {{ item.doctorName }}
                            </n-ellipsis>
                            <span class="info-id">
                              {{ item?.institutionName ?? "-" }} {{ item?.departmentName ?? "-" }}
                              {{ JobTitleLabels[item.title] ?? "-" }}
                            </span>
                          </template>
                        </CustomOptions>
                      </template>
                    </n-checkbox>
                  </n-space>
                </div>
              </n-spin>
            </div>
          </template>
          <template v-else>
            <JEmpty />
          </template>
        </transition>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, toRaw, effectScope, watch, onScopeDispose, onMounted } from "vue";
import { useGetSupplier } from "../hooks";
import { SystemSetting } from "@/settings/systemSetting";
import { JobTitleLabels } from "@/constants";
/** 相关组件 */
import CustomOptions from "./CustomOptions.vue";
import JEmpty from "./JEmpty.vue";
defineOptions({ name: "JDealerTransfer" });

/** props */
const props = withDefaults(
  defineProps<{
    supplierId: string | number;
  }>(),
  {
    supplierId: null,
  },
);

/** emits */
const emits = defineEmits<{
  (
    e: "update:checked-keys",
    meta: {
      action: "check" | "uncheck" | "checkAll" | "uncheckAll";
      currentId: string | number | Array<string | number>;
      currentItem: Object | Array<Object>;
    },
  ): void;
}>();

/** 供应商数据 */
const { isGetLoading, supplierList, searchValue, _params, getDoctorList, handleScroll } = useGetSupplier();

/** 初始化 */
const model = ref({
  checkedAllRef: false,
  treeValue: null,
});

/** 选中的供应商id */
const checkDoctorId = ref<string | number | null>(null);

/** 搜索 */
const handleSearch = () => {
  // 清空当前页码
  _params.pageVO.current = 1;
  _params.pageVO.size = SystemSetting.pagination.pageSize;
  getDoctorList();
};

/** 全选 */
function handleCheckedAllChange(checked: boolean) {
  const currentId = supplierList.value.map(item => item.id);
  if (checked) {
    // 触发事件
    emits("update:checked-keys", { action: "checkAll", currentId, currentItem: toRaw(supplierList.value) });
  } else {
    // 触发事件
    emits("update:checked-keys", { action: "uncheckAll", currentId, currentItem: toRaw(supplierList.value) });
  }
}

/** 选项组的值改变时的回调 */
function handleUpdateValue(item) {
  if (checkDoctorId.value === item.id) {
    // 如果点击的是已选中的项，取消选中
    checkDoctorId.value = null;
  } else {
    // 否则设置为当前项
    checkDoctorId.value = item.id;
  }
  emits("update:checked-keys", {
    action: checkDoctorId.value === null ? "uncheck" : "check",
    currentId: checkDoctorId.value,
    currentItem: item,
  });
}

/** 初始化 */
function init() {
  getDoctorList();
}
init();

/** 创建effectScope */
const scope = effectScope();

/** 全选按钮是否禁用 */
const selectAll = ref(false);

/** 作用域内运行一组副作用 */
scope.run(() => {
  // 监听
  watch(
    () => props.supplierId,
    newVal => {
      checkDoctorId.value = newVal;
    },
    { immediate: true },
  );

  // 监听
  watch(
    () => [checkDoctorId.value, supplierList.value],
    newVal => {
      if (supplierList.value.every(item => checkDoctorId.value === item.id)) {
        model.value.checkedAllRef = true;
      } else {
        model.value.checkedAllRef = false;
      }
    },
  );
});

onMounted(() => {});
/** 作用域销毁时，停止作用域 */
onScopeDispose(() => {
  scope.stop();
});
</script>

<style lang="less" scoped>
@import "@/styles/scrollbar.less";

.wrapper {
  width: 100%;
  height: 288px;
  display: flex;
  border: 1px solid #eee;
  :deep(.n-spin-content) {
    width: 100%;
    height: 100%;
  }

  .left {
    height: 100%;
    border-right: 1px solid #eeeeee;
    overflow-y: auto;
    box-sizing: border-box;
    padding: 8px 12px;
    .scrollbar();
  }

  .doctor-select {
    width: 100%;
    height: 100%;
    box-sizing: border-box;

    .header {
      height: 28px;
      padding: 8px 12px;
    }

    .doctor-list-wrapper {
      height: 100%;
      padding-left: 12px;
      padding-bottom: 8px;
      box-sizing: border-box;

      .doctor-selected-wrapper {
        width: 100%;
        height: 100%;

        .check-all {
          width: 100%;
          height: 44px;
          display: flex;
          align-items: center;
          padding: 2px 8px;
          box-sizing: border-box;
        }

        :deep(.n-spin-content) {
          height: 100%;
        }

        .doctor-selected-list {
          height: 100%;
          overflow-y: auto;
          .scrollbar();

          .doctor-list-item {
            width: 100%;
            display: flex;
            align-items: center;
            padding-left: 8px;
            border-radius: 2px;

            &:hover {
              background-color: rgb(231, 241, 255);
            }
          }
        }
      }
    }
  }
}

:deep(.n-tree.n-tree--block-line .n-tree-node:not(.n-tree-node--disabled).n-tree-node--selected) {
  background-color: #fff;
}
</style>
