<template>
  <n-select
      v-bind="$attrs"
      :value="props.value"
      filterable
      multiple
      placeholder="请输入临床诊断"
      remote
      tag
      :loading="getDiseaseOptionLoadingRef"
      :onClear="handleClear"
      :options="diseaseOptionsRef"
      :disabled="props.disabled"
      :reset-menu-on-options-change="false"
      @update:value="onChange"
      @blur="handlerBlur"
      @focus="handlerFocus"
      @keydown.enter="handleDiseaseSelectSearch"
      @search="handleDiseaseSelectSearch"
      @scroll="handleDiseaseFormularyScroll"
  />
</template>

<script setup lang="ts">
import {ref} from 'vue'
import {getDiseaseFormularyPage} from "@/services/api/doctorEndApi";
import {useMessages} from "@/hooks";
import {isArray} from "@/utils";

/* Props */
const props = withDefaults(
    defineProps<{
      value: Array<any> // 选择的值
      valueType: 'id' | 'code' | 'name'
      disabled: boolean,
      forbiddenId: Array<string | number> // 被禁用的id列表
    }>(),
    {
      value: () => [],
      valueType: 'id',
      disabled: false,
      forbiddenId: () => []
    },
);

// 定义响应式数据
const message = useMessages();
const diseaseOptionsRef = ref([]); // 处理后的最终数据
const getDiseaseOptionLoadingRef = ref(false)

const emits = defineEmits<{
  (e: "update:value", selectValue: any): void; // 更新选择值事件
}>();

/** 选中值回调 */
function onChange(value) {
  emits("update:value", value);
}

function handlerFocus() {
  // 如果列表为空
  if (!diseaseOptionsRef.value.length) {
    getDiseaseFormularyData();
  }
}

/** 失焦事件处理函数 */
function handlerBlur() {
  handleClearName()
}


/** 清空事件处理函数 */
const handleClear = () => {
  emits("update:value", null);
  handleClearName()
};

/** 清除搜索名称函数 */
const handleClearName = () => {
  if (diseaseFormularyParams.data.searchCondition != '') {
    diseaseFormularyParams.data.searchCondition = ''
    getDiseaseFormularyData();
  }
}

// 临床诊断检索数据
function handleDiseaseSelectSearch(query: string) {
  if (!query.length) {
    diseaseOptionsRef.value = []
    return
  }
  getDiseaseFormularyData(query)
}

const diseaseFormularyParams: {
  data: {
    searchCondition: string
  },
  pageVO: {
    current: number;
    size: number
  }
} = {
  data: {
    searchCondition: '',// 检索名称
  },
  pageVO: {
    current: 1, // 当前页
    size: 100, //每页大小

  },
};

let recordsTotal: number = 0 // 总数

// 分页获取临床诊断病种数据
async function getDiseaseFormularyData(query: string = '') {
  try {
    getDiseaseOptionLoadingRef.value = true
    diseaseFormularyParams.data.searchCondition = query
    const result = await getDiseaseFormularyPage(diseaseFormularyParams)
    if (result) {
      const {total, current, size, records} = result
      diseaseFormularyParams.pageVO.current = Number(current);
      diseaseFormularyParams.pageVO.size = Number(size);
      recordsTotal = Number(total)
      if (diseaseFormularyParams.pageVO.current === 1) {
        diseaseOptionsRef.value = handleData(records)
      } else {
        handleData(records).forEach(item => {
          // 如果列表中不存在该项
          if (!diseaseOptionsRef.value.find(temp => temp.value == item.value)) {
            // 添加到列表中
            diseaseOptionsRef.value.push(item);
          }
        });
      }
    }
  } catch (e) {
    message.createMessageError(`获取临床诊断数据失败:${e}`)
  } finally {
    getDiseaseOptionLoadingRef.value = false
  }
}

function handleDiseaseFormularyScroll(e: Event) {
  const currentTarget = e.currentTarget as HTMLElement
  if (currentTarget.scrollTop + currentTarget.offsetHeight >= currentTarget.scrollHeight) {
    if (diseaseFormularyParams.pageVO.current * diseaseFormularyParams.pageVO.size < recordsTotal) {
      diseaseFormularyParams.pageVO.current++;
      getDiseaseFormularyData();
    }
  }
}

/* 筛选、转化{label: '', value: ''} */
function handleData(filterData: Array<any>) {
  let dataList = [];
  dataList = filterData.map(item => {
    if (isArray(props.forbiddenId) && props.forbiddenId.includes(item.id)) {
      return {label: item.name, value: item[props.valueType], disabled: true};
    } else {
      return {label: item.name, value: item[props.valueType], disabled: false};
    }
  });
  // 是否开启多选，添加全部选项
  // if (props.isMultiple) {
  //   dataList.unshift({label: "全部", value: "all", disabled: false});
  // }
  return dataList;
}

</script>

<style scoped lang="less">

</style>
