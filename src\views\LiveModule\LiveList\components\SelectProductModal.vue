<template>
    <n-modal
        v-model:show="productShow" 
        :auto-focus="false" 
        @after-leave="handleClose" 
        positive-text="确定添加"
        negative-text="取消"
        @positive-click="onPositiveClick"
        @negative-click="handleClose"
    >
    <n-card
        class="product——select-card"
        style="width: 832px"
        :bordered="false"
        size="small"
        title="选择商品"
        closable
        @close="handleClose"
        >
        <FormLayout
            table-row-key="uniqueId"
            style="height:610px"
            :isTableSelection="false"
            :isMultiple="props.multiple"
            :isDisplayIndex="false"
            :isLoading="isLoading"
            :isRowClick="props.isRowClick"
            :is-need-collapse="false"
            :tableData="tableData"
            :tableColumns="tableColumns"
            :pagination="paginationRef"
            :defaultCheckedKeys="selectedKeysReactive.selectedKeys"
            @paginationChange="paginationChange"
            @selectedKeysChange="onTableSelectedKeysChange"
      >
        <template #searchForm>
          <n-form
            ref="formRef"
            :model="searchFormReactive"
            :show-feedback="false"
            label-placement="left"
            label-width="auto"
            require-mark-placement="right-hanging"
            size="small"
            :style="{ width: '100%' }"
          >
            <n-form-item :show-label="false">
              <j-search-input
                v-model:value="searchFormReactive.name"
                placeholder="请输入商品名称"
                style="width: 250px"
                @search="searchFormSearch"
              />
            </n-form-item>
          </n-form>
        </template>
        <template #tableHeaderBtn>
          <n-button @click="refreshTableData">刷新</n-button>
        </template>
      </FormLayout>
        <template #footer>
            <n-space  justify="end">
                <n-button size="small" @click="emits('update:show',false)">取消</n-button>
                <n-button size="small" type="primary" :loading="addLoading" :disabled="addLoading"  @click="onConfirm">确定添加</n-button>
            </n-space>
        </template>
    </n-card>

    </n-modal>
  </template>
  <script setup lang="tsx">
  import FormLayout from '@/layout/FormLayout.vue';
  import {useTableDefault} from '@/hooks/useTableDefault';
  import type {TreeOption} from 'naive-ui';
  import {NImage} from 'naive-ui';
  import {reactive, ref, watch, computed} from 'vue';
//   import {type VideoTplResponseItem} from '@/services/api/videoDatabase';
  import { getLiveGoodsPage,addGoods } from '@/services/api/liveStoreProductApi/liveGoods';
  import EmptyImg from '@/assets/image/system/emptyImg.jpg';
  import { useMessages } from "@/hooks";
  const { createMessageSuccess, createMessageError } = useMessages();
  interface ProductSelectModalProps {
    show: boolean,
    isShowFooter?: boolean,
    multiple?: boolean,
    selectedOptions?: Array<any>
    isRowClick?: boolean
    liveActivityId:string
  }
  
  const props = withDefaults(defineProps<ProductSelectModalProps>(), {
    show: false,
    isShowFooter: true,
    multiple: true,
    isRowClick: false,
    value: () => [],
    selectedOptions: () => [],
    liveActivityId:''
  });
  
  const emits = defineEmits<{
    (e: 'update:show', show: boolean): void,
    (e: 'update:value', keys: Array<string>): void,
    (e: 'update:selectedOptions', keys: Array<VideoTplResponseItem>): void
    (e: 'update:data', value:any): void
      
  }>();
  const productShow = computed({
    get() {
    return props.show;
    },
    set(value) {
    emits("update:show", value);
    },
});
  let initCateListRef = () => {
    return [
      {
        key: '',
        label: '商品',
        children: [
          {
            key: '1',
            label: '药品',
          },
          {
            key: '3',
            label: '普通商品',
          },
        ],
      },
      {
        key: '4',
        label: '积分商品',
      },
    ];
  };
  const cateListRef = ref<Array<TreeOption>>(initCateListRef());
function handleClose() {
    productShow.value = false;
}
function onPositiveClick () {


}
function recursionHandlerChildren(tableArr: Array<any>) {
    return tableArr.map(item => {
        let data = {label: item.name, key: item.id, type: item.type};
        if (item.children && item.children.length) {
        data['children'] = recursionHandlerChildren(item.children);
        }
        return data;
    });
}
  
  function searchLabel(str: string, cateListArr: Array<any>) {
    for (let i = 0; i < cateListArr.length; i++) {
      if (cateListArr[i].label == str) {
        return cateListArr[i];
      }
      if (cateListArr[i].children) {
        let res = searchLabel(str, cateListArr[i].children);
        if (res) {
          return res;
        }
      }
    }
    return null;
  }
  
  const {
    isLoading,
    tableData,
    pageTableData,
    paginationRef,
    paginationChange,
    refreshTableData,
  } = useTableDefault({
    pageDataRequest: (params) => {
        return getLiveGoodsPage(params);
    },
  });
  
  const initSearchFormReactive = () => {
    return {
      name: '',
      productIds: [],
    };
  };
  
  let searchFormReactive = reactive(initSearchFormReactive());
  const selectedKeysReactive = reactive({
    selectedKeys: [],
    selectedOptions: [],
  });
  
  // 自定义勾选列，选中行
  const selectionColumn = [
    {
      type: 'selection',
      multiple: props.multiple,
      // // 相同id的积分商品和非积分商品只能二选一
      // disabled(row) {
      //   // 若该商品为积分商品，则看productId是否与selectKeys中有冲突
      //   if (row.isPoint) {
      //     return selectedKeysReactive.selectedKeys.some(item => item == row.productId);
      //   }
      //   // 若该商品为非积分商品，则看积分商品中的uniqueId是否包含该行productId
      //   if (!row.isPoint) {
      //     for (let i = 0; i < selectedKeysReactive.selectedKeys.length; i++) {
      //       let item = selectedKeysReactive.selectedKeys[i];
      //       if (item.indexOf('isPoint') != -1 && item.indexOf(row.uniqueId) != -1) {
      //         return true;
      //       }
      //     }
      //   }
      //   return false;
      // },
      align: 'center',
      fixed: 'left',
      width: 40,
    },
  ];
  
  function rowSpanNumber(rowData) {
    if (rowData.appletProductSpecDTOList && rowData.appletProductSpecDTOList.length > 0) {
      return rowData.appletProductSpecDTOList.length;
    } else {
      return 1;
    }
  }
  
  let tableColumns = [
    {
      title: '商品图片',
      key: 'firstImg',
      align: 'left',
      fixed: 'left',
      width: 60,
      render: (rowData: any) => {
        return (
          <NImage height="55" width="55" src={rowData.firstImg ? (rowData.firstImg) : EmptyImg}
          />
        );
      },
    },
    {
      title: '商品名称',
      key: 'frontName',
      align: 'left',
      fixed: 'left',
      width: 120,
      render: (rowData) => {
        // 如果是药品，标题展示商品名+通用名+规格
        let showName = rowData.frontName;
        if (rowData.type === 1) {
          showName = `[${rowData.frontName}] ${rowData.name} ${rowData.appletProductSpecDTOList?.[0].name || ''}`;
        }
        return showName;
      },
    },
    {
      title: '商品售价(元)',
      key: 'appletProductSpecDTOList.price',
      align: 'left',
      width: 120,
      render: (rowData) => {
          showPrice(rowData);
          return (
            <>
              <n-flex vertical>
                <span>现价:{rowData.nowPrice}</span>
                {rowData.activityPrice &&
                  <span style={{'text-decoration': 'line-through'}}>原价:{rowData.oldPrice}</span>}
              </n-flex>
            </>
          );
        
      },
    },
    {
      title: '商品库存',
      key: 'availStocks',
      align: 'left',
      fixed: 'left',
      width: 60,
      // render: (rowData: any) => {
      //   let allStocksNum = showPointProductStocks(rowData);
      //   return (
      //     <span>{allStocksNum ?? '0'}</span>
      //   );
      // },
    },
  ];
  if (props.multiple) {
    tableColumns.unshift(...selectionColumn as any);
  }
  
  /**
   * 列中显示的价格设置
   */
  function showPrice(rowData) {
    let mixPrice = rowData.price; // 所有规格最低价
    let activityPrice = rowData.activityPrice; // 所有规格最低活动价。
    // if (rowData?.appletProductSpecDTOList && rowData.appletProductSpecDTOList.length > 0) {
    //   let arr = rowData.appletProductSpecDTOList;
    //   arr.forEach((item, index) => {
    //     if (index === 0) {
    //       mixPrice = item.price;
    //       activityPrice = item?.activityPrice || undefined;
    //       return;
    //     }
    //     if (mixPrice > item.price) {mixPrice = item.price;}
    //     if (item?.activityPrice) {
    //       if (!activityPrice) {activityPrice = item.activityPrice;}
    //       if (activityPrice > item.activityPrice) {activityPrice = item.activityPrice;}
    //     }
    //   });
    // }
    // 有活动价，则显示活动价。mixPrice是所有规格最低价，activityPrice是活动价。
    let nowPrice = activityPrice ? (activityPrice / 100).toFixed(2) :
      (mixPrice ? (mixPrice / 100).toFixed(2) : '');
    let oldPrice = activityPrice ? (mixPrice / 100).toFixed(2) : '';
  
    rowData.nowPrice = nowPrice;
    rowData.oldPrice = oldPrice;
    rowData.activityPrice = activityPrice;
  }
  
  /**
   * 列中显示的积分商品价格设置
   * @param rowData
   */
  function showPointPrice(rowData) {
    let minExchangePoint: string | number = 0; // 所有规格最低积分
    let minExchangePrice = undefined; // 所有规格最低价。
  
    let arr = null;
    if (rowData?.appletPointSpecList && rowData.appletPointSpecList.length > 0) {
      arr = rowData.appletPointSpecList;
    }
    if (rowData?.appletPointSpecDTOS && rowData.appletPointSpecDTOS.length > 0) {
      arr = rowData.appletPointSpecDTOS;
    }
  
    if (arr) {
      let minExchangePointItem = undefined; // 所有最低积分的规格
      // 找到最低积分的规格，按该规格显示
      arr.forEach((item, index) => {
        if (index === 0 || minExchangePoint > item.exchangePoints) {
          minExchangePoint = item.exchangePoints;
          minExchangePointItem = item;
        }
      });
      minExchangePrice = minExchangePointItem?.exchangePrice || undefined;
    }
    let minExchangePriceStr = minExchangePrice ?
      '+' + '￥' + (minExchangePrice / 100).toFixed(2) : '';
    let minExchangePointStr = minExchangePoint ?
      (minExchangePoint) + '积分' + minExchangePriceStr : '-';
  
    rowData.minExchangePointAndPriceStr = minExchangePointStr;
  }
  
  /**
   * 列中显示的积分商品库存
   * @param rowData
   */
  function showPointProductStocks(rowData) {
    let allStockNum: string | number = undefined; // 总库存数
  
    let arr = null;
    if (rowData?.appletProductSpecDTOList && rowData.appletProductSpecDTOList.length > 0) {
      arr = rowData.appletProductSpecDTOList;
    }
    if (rowData?.appletPointSpecList && rowData.appletPointSpecList.length > 0) {
      arr = rowData.appletPointSpecList;
    }
    if (rowData?.appletPointSpecDTOS && rowData.appletPointSpecDTOS.length > 0) {
      arr = rowData.appletPointSpecDTOS;
    }
    if (arr) {
      allStockNum = 0;
      // 每一个规格的库存数量
      arr.forEach((item) => {
        allStockNum += item.availStocks || 0;
      });
    }
    return allStockNum;
  }
  
  function onTableSelectedKeysChange(selectedKeys: Array<string>, options: Array<VideoTplResponseItem | null>) {
    selectedKeysReactive.selectedKeys = selectedKeys;
    selectedKeysReactive.selectedOptions = uniqueByProperty([...selectedKeysReactive.selectedOptions, ...options],
      'uniqueId');
  }
  
  function uniqueByProperty<T>(arr: T[], property: keyof T): T[] {
    const seen = new Set(); // 存储已存在的property值
    return arr.filter(item => {
      const value = item[property];
      if (seen.has(value)) {
        return false;
      } else {
        seen.add(value);
        return true;
      }
    });
  }
  
  function pageProductData() {
    paginationRef.value.current = 1;
      pageTableData(searchFormReactive, paginationRef.value);
  }
  
  function searchFormSearch() {
    pageProductData();
  }
  const addLoading = ref(false)
  async function onConfirm () {
    addLoading.value = true
    let params = {
        liveActivityId:props.liveActivityId,
        productIds:selectedKeysReactive.selectedKeys
    }
    try {
      await addGoods(params)
      createMessageSuccess('添加成功')
      emits('update:data', selectedKeysReactive.selectedKeys);
    } catch (error) {
      createMessageError(error)
    }finally{
      addLoading.value = false
    }
    // emits('update:value', selectedKeysReactive.selectedKeys);
  
    // let oldProduct = props.selectedOptions.map(item => {
    //   if (item.hasOwnProperty('appletPointSpecDTOS')) {
    //     // 积分商品，对uniqueId进行添加标志位处理，productId是真正的商品id
    //     item.uniqueId = item.productId + '_isPoint';
    //   } else {
    //     item.uniqueId = item.productId;
    //   }
    //   return item;
    // });
    // let _arr = uniqueByProperty([...oldProduct, ...selectedKeysReactive.selectedOptions], 'uniqueId');
  
    // // 最终的selectedOptions，需要按selectedKeys来确定是否选中
    // selectedKeysReactive.selectedOptions = _arr.filter(
    //   item => {
    //     // item.productId = item.id;
    //     // 旧商品没有uniqueId，则用id
    //     item.id = item.uniqueId || item.id;
    //     return selectedKeysReactive.selectedKeys.includes(item.uniqueId);
    //   });
  
    // emits('update:selectedOptions', selectedKeysReactive.selectedOptions);
    emits('update:show', false);
    // emits('update:data');
  }
  
  watch(() => props.show, (newVal) => {
    if (newVal) {
      // productId跟商城商品的id一致
      selectedKeysReactive.selectedOptions = props.selectedOptions.map(item => {
        if (item.hasOwnProperty('appletPointSpecDTOS')) {
          // 积分商品，对uniqueId进行添加标志位处理，productId是真正的商品id
          item.uniqueId = item.productId + '_isPoint';
        } else {
          item.uniqueId = item.productId;
        }
        return item;
      });
      selectedKeysReactive.selectedKeys = props.selectedOptions.map(item => item.uniqueId);
  
    //   refreshCateList();
      pageProductData();
    } else {
      searchFormReactive = Object.assign(searchFormReactive, initSearchFormReactive());
      cateListRef.value = initCateListRef();
      selectedKeysReactive.selectedKeys = [];
      selectedKeysReactive.selectedOptions = [];
    }
  }, {immediate: true});
  
  watch(() => tableData.value, (newVal) => {
    for (let i = 0; i < newVal.length; i++) {
      // 积分商品，对uniqueId进行添加标志位处理，productId是真正的商品id
      if (newVal[i].hasOwnProperty('appletPointSpecDTOS')) {
        newVal[i].productId = newVal[i].id;
        newVal[i].uniqueId = newVal[i].id + '_isPoint';
        newVal[i].isPoint = 1;
        newVal[i].productType = 2; // 积分商品类型
      } else {
        newVal[i].productId = newVal[i].id;
        newVal[i].uniqueId = newVal[i].id;
        newVal[i].productType = 1; // 普通商品类型
      }
    }
  });
  
  </script>

  