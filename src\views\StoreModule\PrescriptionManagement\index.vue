<script setup lang="ts">
import TabsLayout from "@/layout/TabsLayout.vue";
import { ref } from "vue";
import { PrescriptionTab } from './index';
import { PrescriptionSelection } from '@/enums';

const tabNameRef = ref<string>(PrescriptionSelection.unopened);

const tableDataUpdate = ref()

const tabsData = ref([
  {
    label: "待开方",
    key: PrescriptionSelection.unopened,
  },
  {
    label: "已开方",
    key: PrescriptionSelection.alreadyOpen,
  },
  {
    label: "已下单",
    key: PrescriptionSelection.placeAnOrder,
  },
  {
    label: "已取消",
    key: PrescriptionSelection.cancelled,
  },
]);

</script>

<template >
   <n-layout>
    <n-layout-content id="prescription-management">
      <TabsLayout  v-model:value="tabNameRef" :tabsData="(tabsData as any)"  :onlyTabs="true" class="tabsLayout">
        <PrescriptionTab ref="tableDataUpdate" :tabNameRef="tabNameRef" />
      </TabsLayout>
    </n-layout-content>
  </n-layout>
</template>

<style lang="less" scoped>
// @import "@/styles/default.less";
</style>
