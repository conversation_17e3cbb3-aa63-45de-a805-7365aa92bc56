<template>
  <JDrawer
    v-model:show="drawerVisible"
    title="问诊处方详情"
    :isGetLoading="isGetLoading"
    :isShowFooter="false"
    @after-leave="closeDrawer"
    to=".tabsLayout"
    :mainStyle="{
      padding: '16px',
      height: 'calc(100% - 50px - 4px)',
      backgroundColor: '#F6F7FA',
    }"
  >
    <template #content>
      <div class="medical-inquiry-prescription-details">
        <!-- 处方状态信息 -->
        <div class="info-card prescription">
          <div class="card-header">
            <span>处方状态:</span>
            <span
              class="status"
              :style="{ color: ['#ff3b2f', '#1677FF', '#1677FF', '#666666', '#666666'][data.status] }"
            >
              {{ ["待审核", "可使用", "已使用", "审核不通过", "已失效"][data.status] }}
            </span>
          </div>
          <div class="card-body">
            <div class="time-item">
              <span class="label">医生开方时间:</span>
              <span class="value">{{ data.diagTime }}</span>
            </div>
            <span v-if="data.auditTime && data.status !== 0" class="time-item">
              <span class="label">药师审核时间:</span>
              <span class="value">{{ data.auditTime }}</span>
            </span>
            <span v-if="data.pharmacistName && data.status !== 0" class="time-item">
              <span class="label">审核药师姓名:</span>
              <span class="value">{{ data.pharmacistName }}</span>
            </span>
            <span v-if="data.status == 2" class="time-item">
              <span class="label">处方使用时间:</span>
              <span class="value">{{ data.useTime }}</span>
            </span>
            <span v-if="data.status == 1 || data.status == 4" class="time-item">
              <span class="label">处方失效时间:</span>
              <span class="value">{{ data.expireTime }}</span>
            </span>
            <span v-if="data.status == 2" class="time-item">
              <span class="label">处方下单编号:</span>
              <span class="value">{{ data.orderCode }}</span>
            </span>
            <span v-if="data.status == 3" class="time-item">
              <span class="label">审核不通过的原因:</span>
              <span class="value">{{ data.auditFailReason }}</span>
            </span>
          </div>
        </div>

        <!-- 临床诊断信息 -->
        <div class="info-card diagnose">
          <div class="card-title">临床诊断</div>
          <div class="diagnosis-tabs">
            <div class="tab" v-for="(item, index) in data.clinicalDiagnosis" :key="index">{{ item }}</div>
          </div>
        </div>

        <!-- 药品信息 -->
        <div class="info-card drug">
          <div class="card-title">Rp</div>
          <template v-if="data.type===PrescriptionTypeEnum.WesternMedicine && presRpList?.length>0">
            <div class="medicine-list">
              <!--            <div class="medicine-header">-->
              <!--              <div class="medicine-info">-->
              <!--                <div class="info-row">-->
              <!--                  <span>{通用名}</span>-->
              <!--                  <span>{规格}</span>-->
              <!--                  <span>x{数量}</span>-->
              <!--                </div>-->
              <!--                <div class="usage-row">-->
              <!--                  <span>用法用量:</span>-->
              <!--                  <span>{给药方式} {用药频次}</span>-->
              <!--                  <span>每次{剂量}</span>-->
              <!--                  <span>{剂量单位}</span>-->
              <!--                </div>-->
              <!--              </div>-->
              <!--            </div>-->
              <div class="medicine-item" v-for="(medicine, index) in presRpList" :key="index">
                <div class="medicine-info">
                  <div class="info-row">
                    <span>{{ medicine.productName }}</span>
                    <span>{{ medicine.specName }}</span>
                    <span>x {{ medicine.count }}</span>
                  </div>
                  <div class="usage-row">
                    <span>用法用量:</span>
                    <span>
                      {{
                        // [
                        //   medicine.routeOfAdministrationOther,
                        //   "口服",
                        //   "外用",
                        //   "吸入",
                        //   "舌下给药",
                        //   "直肠给药",
                        //   "静脉注射",
                        //   "肌肉注射",
                        //   "皮下注射",
                        // ][medicine.routeOfAdministration]
                        medicine.routeOfAdministration == 0
                          ? medicine.routeOfAdministrationOther
                          : routeOfAdministrationMap.get(medicine.routeOfAdministration)
                      }}
                    </span>
                    <span>
                      {{
                        // [
                        //   medicine.frequencyOfAdministrationOther,
                        //   "每日一次",
                        //   "每日两次",
                        //   "每日三次",
                        //   "每日四次",
                        //   "隔日一次",
                        //   "每周一次",
                        //   "每周两次",
                        //   "每周三次",
                        // ][medicine.frequencyOfAdministration]
                        (medicine.frequencyOfAdministration == 0)
                          ? medicine.frequencyOfAdministrationOther
                          : frequencyOfAdministrationMap.get(medicine.frequencyOfAdministration)
                      }}
                    </span>
                    <span>每次{{ medicine.dosage }}{{ dosageUnitsMap.get(medicine.dosageUnits) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </template>
          <template v-else-if="data.type===PrescriptionTypeEnum.TraditionalChineseMedicine && presRpList?.length>0">
            <div class="medicine-container">
              <div class="item" v-for="(medicine, index) in presRpList" :key="index">
                <span class="index">{{ index + 1 }}.</span>
                <span class="name">{{ medicine.productName }}</span>
                <span class="unit">{{medicine.count}}{{ medicine.specName }}</span>
              </div>
            </div>
            <div class="medicine-footer">
              <span class="medicine-footer-item">共{{countTraditionalChineseMedicine.chineseDosageCount}}剂</span>
              <span class="medicine-footer-item">
                {{ countTraditionalChineseMedicine?.routeOfAdministration == 0 ? countTraditionalChineseMedicine?.routeOfAdministrationOther : routeOfAdministrationMap.get(countTraditionalChineseMedicine?.routeOfAdministration)}}
              </span>
              <span class="medicine-footer-item">
                {{  (countTraditionalChineseMedicine?.frequencyOfAdministration == 0) ? countTraditionalChineseMedicine?.frequencyOfAdministrationOther: frequencyOfAdministrationMap.get(countTraditionalChineseMedicine?.frequencyOfAdministration)}}
              </span>
              <span class="medicine-footer-item">
                每次{{ countTraditionalChineseMedicine?.dosage
                }}{{ dosageUnitsMap.get(countTraditionalChineseMedicine?.dosageUnits) }}
              </span>
              <span class="medicine-footer-item">{{countTraditionalChineseMedicine?.medicalAdvice}}</span>
            </div>
          </template>
        </div>

        <!-- 病历信息 -->
        <div class="info-card case">
          <div class="card-title">病历</div>
          <div class="medical-record">
            <div class="record-row">
              <div class="record-item">
                <span class="label">患者姓名:</span>
                <span class="value">{{ data?.patientName ? data?.patientName : "-" }}</span>
              </div>
              <div class="record-item">
                <span class="label">肝功能异常:</span>
                <span class="value">{{ data?.isLiver ? "异常" : "正常" }}</span>
              </div>
              <div class="record-item">
                <span class="label">医生姓名:</span>
                <span class="value">{{ data?.doctorName ? data?.doctorName : "-" }}</span>
              </div>
            </div>
            <div class="record-row">
              <div class="record-item">
                <span class="label">患者性别:</span>
                <span class="value">{{ data?.patientSex ? data?.patientSex : "-" }}</span>
              </div>
              <div class="record-item">
                <span class="label">肾功能异常:</span>
                <span class="value">{{ data?.isKidney ? "异常" : "正常" }}</span>
              </div>
              <div class="record-item">
                <span class="label">科室:</span>
                <span class="value">{{ data?.departmentName ? data?.departmentName : "无" }}</span>
              </div>
            </div>
            <div class="record-row">
              <div class="record-item">
                <span class="label">患者年龄:</span>
                <span class="value">{{ data?.patientAge ? data?.patientAge : "-" }}</span>
              </div>
              <div class="record-item">
                <span class="label">过敏史:</span>
                <span class="value">{{ data?.isAllergyHi ? data?.isAllergyHi : "无" }}</span>
              </div>
              <div class="record-item">
                <span class="label">机构:</span>
                <span class="value">{{ data?.institutionName ? data?.institutionName : "无" }}</span>
              </div>
            </div>
            <div class="record-row">
              <div class="record-item">
                <span class="label">主诉:</span>
                <span class="value">{{ data?.chiefComplaint ? data?.chiefComplaint : "无" }}</span>
              </div>
              <div class="record-item">
                <span class="label">家族病史:</span>
                <span class="value">{{ data?.isHomeMedicalHi ? data?.isHomeMedicalHi : "无" }}</span>
              </div>
              <div class="record-item">
                <span class="label">个人病史:</span>
                <span class="value">{{ data?.isPersonalMedicalHi ? data?.isPersonalMedicalHi : "无" }}</span>
              </div>
            </div>
            <div class="record-row">
              <div class="record-item">
                <span class="label">备孕、妊娠、哺乳:</span>
                <span class="value">{{ data?.isPreparePregnant ? "有" : "无" }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </JDrawer>
</template>

<script lang="ts" setup name="NewDoctor">
import { computed, ref, watch } from "vue";
import type { MedicalInquiryDetailData, PrescriptionItem } from "@/typings/api.d";
import { getStorePrescriptionDetail } from "@/services/api";
import {
  dosageUnitsMap,
  frequencyOfAdministrationMap,
  routeOfAdministrationMap
} from "@/views/DoctorEndModule/Prescription/types";
import {PrescriptionTypeEnum} from "@/enums/index"
const countTraditionalChineseMedicine = computed<PrescriptionItem | undefined>(() => presRpList.value?.[0] )
const initialData: MedicalInquiryDetailData = {
  type: PrescriptionTypeEnum.WesternMedicine,
  id: 0,
  status: 0,
  diagTime: "",
  expireTime: "",
  clinicalDiagnosis: [],
  patientName: "",
  patientSex: "",
  patientAge: "",
  chiefComplaint: "",
  period: 1,
  isLiver: 0,
  isKidney: 0,
  isAllergyHi: "",
  isPersonalMedicalHi: "",
  isHomeMedicalHi: "",
  isPreparePregnant: 0,
  doctorName: "",
  departmentName: "",
};
const data = ref({ ...initialData });

/** 获取参数 */
const isGetLoading = ref(false);

/** 抽屉状态 */
const drawerVisible = ref(false);

const drawerProps = ref({});

// 响应式药品处方列表
const presRpList = ref<PrescriptionItem[]>([]);

const acceptParams = async ({ row }) => {
  drawerVisible.value = true;
  drawerProps.value = row;
  isGetLoading.value = true;
  const res = await getStorePrescriptionDetail({ id: row.id });
  data.value = { ...data.value, ...res, clinicalDiagnosis: res.clinicalDiagnosis ? res.clinicalDiagnosis.split(",") : [] };
  presRpList.value = res.presRpList;
  isGetLoading.value = false;
};

/** 关闭抽屉 */
const closeDrawer = () => {
  initialData.clinicalDiagnosis = []
  data.value = { ...initialData };
  drawerVisible.value = false;
};

defineExpose({
  acceptParams,
  closeDrawer,
  drawerVisible,
});
</script>

<style lang="less" scoped>
@small-desktop: ~"(min-width: 1024px) and (max-width: 1279px)";
@medium-desktop: ~"(min-width: 1280px) and (max-width: 1599px)";
.medical-inquiry-prescription-details {
  background-color: #f5f5f5;
  height: 100%;
  .info-card {
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 16px;
    overflow: hidden;
    padding: 16px 40px;

    .card-header {
      font-size: 24px;
      line-height: 1;
      margin-bottom: 15px;
      font-weight: 500;
      .status {
        margin-left: 4px;
      }
    }
    .card-body {
      display: grid;
      grid-template-columns: repeat(3, (100% / 3));
      gap: 10px;
      .time-item {
        margin-right: 20px;
        &:last-child {
          margin-right: 0;
        }

        .label {
          color: #666;
          margin-right: 4px;
        }

        .value {
          color: #333;
        }
      }
    }

    .card-title {
      font-size: 24px;
      font-weight: 500;
      margin-bottom: 16px;
      color: #333333;
      line-height: 32px;
    }

    .diagnosis-tabs {
      display: flex;
      gap: 10px;

      .tab {
        padding: 4px 16px;
        border-radius: 4px;
        background-color: #ecf5ff;
        color: #1677ff;
        box-sizing: border-box;
      }
    }

    .medicine-list {
      width: 550px;
      .medicine-item,
      .medicine-header {
        margin-bottom: 16px;
        padding-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
          border-bottom: none;
        }

        .medicine-info {
          .info-row {
            display: flex;
            margin-bottom: 8px;
            color: #333;
            font-size: 20px;

            span:nth-child(1) {
              flex: 2;
              font-weight: 500;
            }

            span:nth-child(2) {
              text-align: center;
              flex: 2;
            }

            span:nth-child(3) {
              text-align: center;
              flex: 1;
            }
          }

          .usage-row {
            display: flex;
            color: #999999;
            font-size: 13px;

            span {
              margin-right: 8px;
            }
          }
        }
      }
    }
    .medicine-container{
      display: flex;
      flex-wrap: wrap;
      gap: 12px 32px;
      .item {
        .name{padding: 0 10px}
      }
    }
    .medicine-footer{
      padding: 16px 0;
      .medicine-footer-item{
        padding-right: 8px;
        font-size: 14px;
      }
    }

    .medical-record {
      .record-row {
        display: flex;
        margin-bottom: 16px;

        @media @small-desktop {
          margin-bottom: 0px;
        }
        &:last-child {
          margin-bottom: 8px;
        }
        .record-item {
          flex: 1;
          min-width: 30%;
          margin-right: 10px;

          &:last-child {
            margin-right: 0;
          }

          .label {
            color: #666;
            margin-right: 4px;
          }

          .value {
            color: #333;
          }
        }
      }
    }
  }
  .info-card.diagnose {
    display: flex;
    align-items: center;
    padding: 24px 40px;
    .card-title {
      margin-bottom: 0px;
      margin-right: 32px;
    }
  }
  .info-card.case {
    .card-title {
      margin-bottom: 32px;
    }
  }
}
</style>
