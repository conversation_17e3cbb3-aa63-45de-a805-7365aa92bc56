import {defHttp} from '@/services';

export const enum OrderManagementApi {
 orderPage = '/order/page',
 orderDetail = '/order/detail',
 orderArrangeShipment = '/order/arrangeShipment',
 orderArrangeSignature = '/order/arrangeSignature',
 shipCompanyPage = '/shipCompany/page',
 orderShip = '/order/ship',
 orderVerify = '/order/verify',
 orderSign = '/order/sign',
 orderExportOrders = '/order/exportOrders',
 lockOrders = '/order/lockOrders',
 orderDelete = '/order/delete',
 orderImportOrderSign = '/order/importOrderSign',

 /** 订单管理1.0.2 */
 orderImportOrderShip = '/order/importOrderShip',
 orderConfirmImportOrderShip =  '/order/confirmImportOrderShip',

 orderArrangeRefund = 'order/arrangeRefund',
 afterSaleRecordRefund = 'afterSaleRecord/refund'
}
//订单列表
export function orderPage(params) {
    return defHttp.post({
      url: OrderManagementApi.orderPage,
      params
    });
}

//订单详情
export function orderDetail(params) {
    return defHttp.get({
      url: OrderManagementApi.orderDetail+"?orderCode=" + params,
    });
}

//安排发货
export function orderArrangeShipment(params) {
    return defHttp.post({
      url: OrderManagementApi.orderArrangeShipment+"?orderCode=" + params,
    });
}

//安排签收
export function orderArrangeSignature(params) {
    return defHttp.post({
      url: OrderManagementApi.orderArrangeSignature+"?orderCode=" + params,
    });
}

//分页查询快递公司列表
export function shipCompanyPage(params) {
  return defHttp.post({
    url: OrderManagementApi.shipCompanyPage,
    params
  });
}

//发货
export function orderShip(params) {
  return defHttp.post({
    url: OrderManagementApi.orderShip,
    params
  });
}

//发货
export function orderVerify(params) {
  return defHttp.post({
    url: OrderManagementApi.orderVerify,
    params
  });
}

//签收
export function orderSign(params) {
  return defHttp.post({
    url: OrderManagementApi.orderSign,
    params
  });
}

//订单导出
export function orderExportOrders(params) {
  return defHttp.post({
    url: OrderManagementApi.orderExportOrders,
    requestConfig: {
      responeseType: 'stream'
    },
    params,
  })
}

//订单锁单
export function lockOrders(params) {
  return defHttp.post({
    url: OrderManagementApi.lockOrders,
    params,
  })
}

//删除
export function orderDelete(params) {
  return defHttp.post({
    url: OrderManagementApi.orderDelete + '?orderCode=' + params,
    
  });
}

//订单发货导入
export function orderImportOrderShip(params) {
  return defHttp.post({
    url: OrderManagementApi.orderImportOrderShip,
    params,
    requestConfig: {
      requestContentType: "form-data",
  },
  });
}


//订单签收导入
export function orderImportOrderSign(params) {
  return defHttp.post({
    url: OrderManagementApi.orderImportOrderSign,
    params,
    requestConfig: {
      requestContentType: "form-data",
    },
  });
}

//确认订单导入发货
export function orderConfirmImportOrderShip(params){
  return defHttp.post({
    url: OrderManagementApi.orderConfirmImportOrderShip,
    params,
    requestConfig: {
      requestContentType: "form-data",
    },
  });
}

//点击退款
export function orderArrangeRefund(params){
  return defHttp.post({
    url: OrderManagementApi.orderArrangeRefund+ '?orderCode=' + params,
  });
}

//提交退款
export function afterSaleRecordRefund(params){
  return defHttp.post({
    url: OrderManagementApi.afterSaleRecordRefund,
    params
  });
}