<template>
  <JDrawer
    :show="props.show"
    @after-leave="handleAfterLeave"
    @after-enter="handleAfterEnter"
    @goBack="emits('update:show', false)"
    :title="props.type === 'add' ? '新建医生' : '编辑医生'"
  >
    <!-- 表单内容 -->
    <template #content>
      <n-form
        ref="formRef"
        :model="model"
        :rules="rules"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
        size="small"
        :style="{width: '60%'}"
      >
        <n-grid :cols="12" :x-gap="12">
          <!-- 头像 -->
          <n-form-item-gi :span="8" label="头像">
            <CustomizeUpload v-model:value="model.img" accept="image/*" :fileListSize="1" :max="1" />
          </n-form-item-gi>
          <!-- 姓名 -->
          <n-form-item-gi :span="8" label="姓名" path="doctorName">
            <n-input v-model:value="model.doctorName" clearable placeholder="请输入姓名" />
          </n-form-item-gi>
          <!-- 手机号 -->
          <n-form-item-gi :span="8" label="手机号">
            <n-input v-model:value="model.mobile" clearable placeholder="请输入手机号" />
          </n-form-item-gi>
          <!-- 资格证书编码 -->
          <n-form-item-gi :span="8" label="资格证书编码">
            <n-input v-model:value="model.doctorCode" clearable placeholder="请输入资格证书编码" />
          </n-form-item-gi>
          <!-- 开方医院或平台 -->
          <n-form-item-gi :span="8" label="开方医院或平台">
            <n-input v-model:value="model.platform" clearable placeholder="请输入开方医院或平台" />
          </n-form-item-gi>
        </n-grid>
      </n-form>
    </template>
    <!-- footer -->
    <template #footer>
      <n-space justify="end">
        <n-button @click="emits('update:show', false)" class="store-button">取 消</n-button>
        <n-button type="primary" :loading="isLoading" @click="_save" class="store-button">保 存</n-button>
      </n-space>
    </template>
  </JDrawer>
</template>

<script lang="ts" setup name="NewDoctor">
import { ref } from "vue";
import { deepClone } from "@/utils";
import type { FormRules } from "naive-ui";
import { addDoctor, doctorUpdate, getDoctorEntityById } from "@/services/api";
import { isArray, isNumber } from "@/utils/isUtils";
import { useMessages } from '@/hooks';
import { REGEXP_PHONE } from "@/config";

const { createMessageSuccess, createMessageError } = useMessages();

/** props */
const props = defineProps<{
    type: 'add' | 'edit'; // 模式
    rowId?: string;
    show: boolean;
    refreshTable?: () => void; // 刷新表格数据
}>();

/** emits */
const emits = defineEmits<{
    (e: 'update:show', value: boolean): void;
}>();

/* 表单实例 */
const formRef = ref();

/* 表单参数初始化 */
const initParams = {
  img: '',
  doctorName:  null,
  mobile: null,
  doctorCode: null,
  platform: null,
};
const model = ref(deepClone(initParams));

/* 表单规则 */
const rules: FormRules = {
  doctorName: {
    type: "string",
    required: true,
    trigger: ["blur", "input"],
    message: "请输入医生姓名",
  },
};

/** 关闭抽屉回调 */
const handleAfterLeave = () => {
    // 初始化参数
    model.value = deepClone(initParams);
};

/** 抽屉出现后的回调 */
const isGetLoading = ref(false);
const handleAfterEnter = async () => {
  try {
    if (props.show && props.type === "edit") {
      isGetLoading.value = true;
      const data = await getDoctorEntityById(props.rowId);
      const { img, doctorName, doctorCode, mobile, platform } = data;
      model.value = {
        img,
        doctorName,
        mobile,
        doctorCode,
        platform
      }
    }
  } catch (error) {
    console.log(error);
  } finally {
    isGetLoading.value = false;
  }
};

/** 获取参数 */
const getParams = () => {
  const { img, doctorName, mobile, doctorCode, platform } = model.value;
  return {
    img: isArray(img) ? img.join(",") : img,
    doctorName,
    mobile,
    doctorCode,
    platform
  };
};

/* 确认--保存 */
const isLoading = ref(false);
const _save = (e: MouseEvent) => {
    e.preventDefault();
    formRef.value?.validate(async (errors: any) => {
        if (!errors) {
          // 校验手机号
          if (model.value.mobile && !isNumber(model.value.mobile) && !REGEXP_PHONE.test(model.value.mobile)) {
            createMessageError("请输入正确的手机号");
            return;
          }
          try {
            isLoading.value = true;
            // 新增
            if (props.type === 'add') {
              await addDoctor(getParams());
            } else {
              const _params = {
                ...getParams(),
                id: props.rowId
              };
              await doctorUpdate(_params);
            }
            createMessageSuccess(props.type === 'add' ? '新增医生成功' : '编辑医生成功');
            emits('update:show', false);
            props?.refreshTable();
          } catch (error) {
            createMessageError(props.type === 'add' ? '新增医生失败' : '编辑医生失败');
          } finally {
            isLoading.value = false;
          }
        }
    });
};
</script>

<style lang="less" scoped></style>
