export function useUnitConversion() {
  /**
   * 元转分
   */
  function toFen(value: number | string) {
    return Math.round(parseFloat(String(value ?? "0")) * 100);
  }

  /**
   * 分转元
   */
  function toYuan(value: number | string) {
    return parseFloat((parseFloat(String(value ?? "0")) / 100).toFixed(2));
  }

  /**
   * 分转元-string
   */
  function toYuanString(value: number | string) {
    return (parseFloat(String(value ?? "0")) / 100).toFixed(2);
  }

  /**
   * 验证小数点后两位
   */
  function verifyDecimalPoint(value: number): boolean {
    const reg = /^(\d+)(\.\d{1,2})?$/;
    return reg.test(String(value));
  }

  return {
    toFen,
    toYuan,
    toYuanString,
    verifyDecimalPoint
  };
}
