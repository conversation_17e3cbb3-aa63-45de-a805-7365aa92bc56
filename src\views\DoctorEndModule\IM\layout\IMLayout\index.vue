<script lang="ts" setup>
import {NLayout, NLayoutSider, NLayoutContent, NTooltip} from "naive-ui";
import {useBoolean} from "@/hooks";

defineOptions({
  name: 'IMLayout'
});

/** props */
const props = defineProps<{}>();
/** emits */
const emits = defineEmits<{
  (e: 'update:activeKey', value: string | number): void;
}>()

/** 是否展开 */
const {bool: collapsed, toggle, setBool} = useBoolean(false);
</script>

<template>
  <NLayout has-sider style="border-radius: 4px;">
    <NLayoutSider collapse-mode="width" :collapsed-width="66" :collapsed="collapsed" :width="320" bordered>
      <div class="conversation-wrapper">
        <div class="left">
          <slot name="conversation"></slot>
        </div>
      </div>
    </NLayoutSider>
    <NLayoutContent>
      <div class="chat-wrapper pharmacist-prescription-tabs-layout">
        <slot name="chat"></slot>
      </div>
    </NLayoutContent>
  </NLayout>
</template>

<style lang="less" scoped>
@import "@/styles/default.less";

.conversation-wrapper {
  width: 100%;
  height: 100%;
  padding: 6px 8px 0 6px;
  box-sizing: border-box;
  overflow: hidden;

  .left {
    width: 100%;
    height: 100%;
  }

  .collapsed {
    height: 40px;
    position: relative;

    &::before {
      background: rgba(31, 35, 41, .15);
      content: "";
      height: 0;
      height: 1px;
      left: 8px;
      pointer-events: none;
      position: absolute;
      right: 8px;
      top: 0;
      transform: translateY(-6px);
    }

    .title {
      font-size: 16px;
    }
  }
}

.chat-wrapper {
  box-sizing: border-box;
  height: calc(@inner-bg-height - 42px);
}

</style>
