<template>
  <JModal
    v-model:show="show"
    title="活动价设置"
    width="680"
    @after-leave="closeModal"
		@positive-click="_save"
		:positiveButtonProps="{
			loading: isLoading
		}"
    :isScale="false"
    isConfirm
  >
    <n-form
      ref="formRef"
      :model="model"
      :rules="rules"
      label-placement="left"
      label-width="auto"
      size="small"
      require-mark-placement="right-hanging"
      :style="{ width: '100%' }"
    >
      <n-grid :cols="12" :x-gap="12" responsive="self">
        <!-- 活动价（元） -->
        <n-form-item-gi :span="12" label="活动价（元）">
          <n-input-number
            v-model:value="model.activityPrice"
            placeholder="请输入活动价"
            :max="maxActivityPrice"
            :min="0.01"
            clearable
            :show-button="false"
            style="width: 88%;"
            :precision="2"
            :disabled="props.type === 'view'"
          />
        </n-form-item-gi>
        <!-- 活动期间 -->
        <n-form-item-gi :span="12" label="活动期间">
          <n-flex>
            <JDateRangePicker
              v-model:value="model.rangeTime"
              width="320"
              type="datetimerange"
              format="YYYY-MM-DD HH:mm:ss"
              :default-time="['00:00:00', '23:59:59']"
              :clearable="false"
              :disabled="props.type === 'view'"
            />
            <JTextButton v-if="showActivityPriceVO.length < 10" type="primary" text size="small" @click="_add" :disabled="props.type === 'view'">添加</JTextButton>
          </n-flex>
        </n-form-item-gi>
        <n-form-item-gi :span="12" :show-label="false">
          <n-flex vertical>
            <template v-for="item,index in showActivityPriceVO" :key="index">
              <div v-if="!item?.isDeleted" style="display: flex;align-items: center;margin-left: 92px;">
                <span style="margin-right: 12px;">{{`活动${numberToChinese(index + 1)}：`}}</span>
                <span style="margin-right: 12px;">
                  {{ dayjs(item['startTime']).format('YYYY年MM月DD HH:mm:ss') }} ~
                  {{ dayjs(item['endTime']).format('YYYY年MM月DD HH:mm:ss') }}
                </span>
                <JTextButton type="error" text size="small" @click="handleDel(item)" :disabled="props.type === 'view'">删除</JTextButton>
              </div>
            </template>
          </n-flex>
        </n-form-item-gi>
      </n-grid>
    </n-form>
    <JTextButton text type="error" @click="handleAllDel" class="del" :disabled="props.type === 'view'">
      <n-flex :size="4">
        <n-icon size="16">
          <TrashOutline />
        </n-icon>
        删除活动价
      </n-flex>
    </JTextButton>
  </JModal>
</template>

<script lang="ts" setup name="JActivePrice">
import { ref, computed } from "vue";
import { useDialog } from 'naive-ui';
import type { FormRules } from 'naive-ui';
import { TrashOutline } from "@vicons/ionicons5";
import { useMessages, useBoolean, useLoading } from '@/hooks';
import { deepClone, isArray, isObject, uuid, numberToChinese } from "@/utils";
import dayjs from "dayjs";
import { hasOverlap } from "../utils";

export interface ActivityPriceVO {
  productId?: number; // 商品ID
  specId?: number; // 规格ID
  activityPrice: number; // 活动价单位分
  startTime: string; // 开始时间
  endTime: string; // 结束时间
  isDeleted: 0 | 1;
}

const props = withDefaults(defineProps<{
  type?: 'add' | 'edit' | 'view'; // 添加类型参数
}>(), {
  type: 'edit'
});

const { createMessageError } = useMessages();

/** 最高活动价 */
const maxActivityPrice = computed(() => {
  return (Number(model.value.originalPrice) * 100 - 1) / 100;
});

/** 确认框 */
const dialog = useDialog();

/** emits */
const emits = defineEmits<{
  (e: "update:activityPrice", id: string, value: Array<ActivityPriceVO>): void;
}>();

const { bool: show, setFalse, setTrue } = useBoolean(false);

/** 展示活动区间 */
const showActivityPriceVO = computed(() => {
  // 获取 model.value.activityPriceVO，并排序
  const sortedList = model.value.activityPriceVO.sort((a, b) => a.startTime - b.startTime);

  // 过滤掉 item?.isDeleted === 1 的项
  const filteredList = sortedList.filter(item => item?.isDeleted !== 1);

  return filteredList;
});

/* 表单参数初始化 */
const initParams = {
  id: null, // 标识
  originalPrice: null, // 原价
  activityPrice: null, // 活动价
  rangeTime: null, // 活动期间
  activityPriceVO: [],
};
const model = ref(deepClone(initParams));

/* 接收父组件传过来的参数 */
const acceptParams = (_params: {
  row: object;
}) => {
  let row = _params.row;
  // 处理行数据
  if (isObject(row) && Object.keys(row).length !== 0) {
    model.value.id = row['id'];
    model.value.originalPrice = row['price'];
    if (isArray(row?.activityPriceVOList) && row?.activityPriceVOList.length > 0) {
      for (let i = 0; i < row?.activityPriceVOList.length; i++) {
        model.value.activityPriceVO.push({
          ...row?.activityPriceVOList[i],
          activityPrice: row?.activityPriceVOList[i]['activityPrice'] / 100,
          startTime: dayjs(row?.activityPriceVOList[i]['startTime']).valueOf(),
          endTime: dayjs(row?.activityPriceVOList[i]['endTime']).valueOf()
        })
      }
      model.value.activityPrice = row?.activityPriceVOList?.[0]?.['activityPrice'] ? (row.activityPriceVOList[0]['activityPrice'] / 100) : null;
    }
  }

  setTrue();
};


/* 清空表单 */
const formDataReset = () => {
  model.value = deepClone(initParams);
};

/* 关闭弹窗之后 */
const closeModal = () => {
  formDataReset();
};

/** 表单实例 */
const formRef = ref();

/* 表单规则 */
const rules: FormRules = {

};

/** 活动区间添加 */
function _add() {
  const { rangeTime, activityPriceVO, activityPrice } = model.value;
  if (isArray(rangeTime)) {
    // 校验时间是否重叠
    if (!hasOverlap(activityPriceVO, rangeTime)) {
      createMessageError("存在重叠时间，请重新选择时间！");
      return;
    };
    const [startTime, endTime] = rangeTime;
    model.value.activityPriceVO.push({
      addId: uuid(),
      activityPrice,
      startTime,
      endTime,
      isDeleted: 0,
    });
  } else {
    createMessageError("请选择时间段！");
  }
}

/** 校验 */
function handleVerify(_params: Array<ActivityPriceVO>): boolean {
  if (!isArray(_params) || _params.length === 0) {
    createMessageError("请选择时间段！");
    return false;
  }

  for (let i = 0; i < _params.length; i++) {
    if (_params[i]['activityPrice'] >= model.value.originalPrice * 100){
      createMessageError("活动价不能大于等于售价！");
      return false;
    }
    if (!_params[i]['activityPrice']) {
      createMessageError("请输入活动价！");
      return false;
    }
  }

  return true;
}

/** 删除某个活动区间 */
function handleDel(row) {
  if (row?.id) {
    const item = model.value.activityPriceVO.find(item => item?.id === row?.id);
    if (item) {
      item['isDeleted'] = 1;
    }
  } else {
    model.value.activityPriceVO = model.value.activityPriceVO.filter(item => item?.addId !== row?.addId);
  }
}

/** 删除活动价 */
function handleAllDel() {
  const confirmDialog = dialog.warning({
    title: '警告',
    content: '删除已设置的活动期间内容？',
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: async () => {
      model.value.activityPriceVO.forEach(item => {
        if (!item?.addId) {
          item['isDeleted'] = 1;
        }
      });

      model.value.activityPriceVO = model.value.activityPriceVO.filter(item => !item?.addId);
    },
    onNegativeClick: () => {
      return;
    }
  });
}


/** 获取参数 */
function _getParams() {
  const { activityPriceVO, activityPrice } = model.value;
  let activityPriceVOList = activityPriceVO.map(({ addId, startTime, endTime, ...rest }) => {
    return {
      ...rest,
      activityPrice: activityPrice * 100,
      startTime: dayjs(startTime).format('YYYY-MM-DD HH:mm:ss'),
      endTime: dayjs(endTime).format('YYYY-MM-DD HH:mm:ss')
    }
  });
  return [...activityPriceVOList];
}

/** 保存 */
const { loading: isLoading, startLoading, endLoading } = useLoading(false);
const _save = async e => {
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      try {
        startLoading();
        let _params = _getParams();
        // 校验数据
        let ckecked = handleVerify(_params);
        if (!ckecked) {
          return;
        }
        emits('update:activityPrice', model.value?.id, _params);
  
        setFalse();
      } catch (error) {
        createMessageError('保存失败：' + error);
      } finally {
        endLoading();
      }
    }
  });
};

defineExpose({
  acceptParams,
});
</script>

<style lang="less" scoped>
.del {
  position: absolute;
  left: 16px;
  bottom: 12px;
}
</style>
