<template>
  <div class="wrapper inner-page-height">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :pagination="paginationRef"
      @paginationChange="paginationChange"
      :isNeedCollapse="false"
      :isDisplayIndex="false"
      :isTableSelection="false"
    >
      <!-- 表单 -->
      <template #searchForm>
        <n-form
          ref="formRef"
          :model="formValue"
          label-placement="left"
          label-width="auto"
          :show-feedback="false"
          require-mark-placement="right-hanging"
          size="small"
          :style="{ width: '100%' }"
        >
          <n-form-item label="">
            <n-input-group>
              <n-select
                v-model:value="formValue.searchType"
                placeholder="请选择"
                :options="searchTypeOptions"
                style="width: 120px"
              />
              <j-search-input
                v-model:value="formValue.searchValue"
                placeholder="请输入"
                @search="handlerSearch"
                :width="240"
              />
            </n-input-group>
          </n-form-item>
          <n-form-item label="问诊单状态">
            <n-select
              :value="formValue.consultationStatus"
              placeholder="请选择"
              :options="orderStatusOptions"
              @update:value="orderStatusChange"
              style="width: 120px"
              clearable
            />
          </n-form-item>
          <n-form-item label="问诊单类型">
            <n-select
              v-model:value="formValue.type"
              placeholder="请选择"
              :options="typeOptions"
              @update:value="tableSearch"
              style="width: 120px"
              clearable
            />
          </n-form-item>
          <n-form-item label="问诊形式">
            <n-select
              v-model:value="formValue.form"
              placeholder="请选择"
              :options="formOptions"
              @update:value="tableSearch"
              style="width: 120px"
              clearable
            />
          </n-form-item>
          <n-form-item label="创建时间">
            <j-date-range-picker
              v-model:value="formValue.creationTime"
              type="datetimerange"
              format="yyyy-MM-dd"
              :default-time="['00:00:00', '23:59:59']"
              clearable
            />
          </n-form-item>
        </n-form>
      </template>

      <template #tableHeaderBtn>
        <n-button @click="refresh" :loading="isLoading" class="store-button">刷 新</n-button>
        <n-button
          @click="handlerCreateReservationForm"
          v-if="hasManagementCreateAppointment"
          :loading="isLoading"
          type="primary"
        >
          创建预约单
        </n-button>
        <n-button
          @click="exportData"
          v-if="hasManagementExport"
          class="store-button"
          type="primary"
          :loading="exportLoading"
        >
          导 出
        </n-button>
      </template>
    </FormLayout>
    <RefundSetting v-model:show="showRefundSetting" :data="data" @refresh="refresh" />
    <ReservationForm v-model:show="showReservationForm" @refresh="refresh" />
    <DetailsDrawer :convertFenToYuanStr="convertFenToYuanStr" ref="detailDrawerRef" />
  </div>
</template>

<script lang="tsx" setup name="DoctorManagement">
import { onMounted, ref, watch } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { getMedicalInquiryForm, medicalInquiryFormExport, cancelReservationForm } from "@/services/api";
import { useMessages } from "@/hooks";
import DetailsDrawer from "./components/detailsDrawer.vue";
import {
  hasManagementDetails,
  hasManagementRefund,
  hasManagementExport,
  hasManagementVideo,
  hasManagementCreateAppointment,
  hasManagementCancelAppointment,
} from "./authList";
import RefundSetting from "./components/RefundSetting.vue";
import ReservationForm from "./components/ReservationForm.vue";
import moment from "moment";
import { transformMinioSrc } from "@/utils/fileUtils";
import TablePreview from "@/components/TablePreview/index.vue";
import { medicalInquiryFormStatusMap } from "@/views/ConsultationModule/MedicalInquiryForm/types";
const { createMessageSuccess, createMessageError } = useMessages();

/** 表格hook */
const { isLoading, tableData, paginationRef, pageTableData, paginationChange } = useTableDefault({
  pageDataRequest: getMedicalInquiryForm,
});

const showRefundSetting = ref(false);
const showReservationForm = ref(false);

const data = ref({});

/** 搜索类型 */
const searchTypeOptions = [
  {
    label: "患者姓名",
    value: "patientName",
  },
  {
    label: "医生姓名",
    value: "doctorName",
  },
  {
    label: "用户昵称",
    value: "nickname",
  },
  {
    label: "经销商姓名",
    value: "thirdDealerName",
  },
  {
    label: "群管昵称",
    value: "thirdGroupMgrName",
  },
  {
    label: "问诊编号",
    value: "code",
  },
];

/** 订单状态 */
const orderStatusOptions = [
  {
    label: "待支付",
    value: 1,
  },
  {
    label: "待接诊",
    value: 2,
  },
  {
    label: "咨询中",
    value: 3,
  },
  {
    label: "已完成",
    value: 4,
  },
  {
    label: "已取消",
    value: 5,
  },
];
const typeOptions = [
  {
    label: "常规问诊",
    value: 1,
  },
  {
    label: "预约问诊",
    value: 2,
  },
];
const formOptions = [
  {
    label: "图文问诊",
    value: 1,
  },
  {
    label: "视频问诊",
    value: 2,
  },
];
/** 参数 */
const formValue = ref({
  searchValue: "",
  searchType: "patientName",
  consultationStatus: null,
  creationTime: null,
  type: null,
  form: null,
});
/* 表格列表项 */
const tableColumns = ref([
  {
    title: "问诊编号",
    key: "code",
    width: 300,
    align: "left",
  },
  {
    title: "用户昵称",
    key: "nickname",
    width: 180,
    align: "left",
  },
  {
    title: "患者姓名",
    key: "patientName",
    align: "left",
  },
  {
    title: "医生姓名",
    key: "doctorName",
    align: "left",
  },
  {
    title: "时长",
    key: "duration",
    align: "left",
    render: row => {
      if (!row?.duration) {
        <div>-</div>;
      }
      const seconds = row?.duration * 60;
      return <div>{seconds > 0 ? formatTimeFromSeconds(seconds) : "-"}</div>;
    },
  },
  {
    title: "费用",
    key: "fee",
    align: "left",
    render: row => {
      return <div>￥{convertFenToYuanStr(row.fee)}</div>;
    },
  },
  {
    title: "问诊单状态",
    key: "consultationStatus",
    align: "left",
    render: row => {
      return <div>{medicalInquiryFormStatusMap.get(row.consultationStatus)}</div>;
    },
  },
  {
    title: "问诊单类型",
    key: "orderType",
    align: "left",
    render: row => {
      return <div>{row.type === 1 ? "常规问诊" : "预约问诊"}</div>;
    },
  },
  {
    title: "问诊形式",
    key: "consultationFormat",
    align: "left",
    render: row => {
      return <div>{row.form === 1 ? "图文问诊" : "视频问诊"}</div>;
    },
  },
  {
    title: "经销商",
    key: "thirdDealerName",
    align: "left",
  },
  {
    title: "群管",
    key: "thirdGroupMgrName",
    align: "left",
  },
  {
    title: "创建时间",
    key: "createTime",
    align: "left",
  },
  {
    title: "操作",
    key: "action",
    width: 180,
    fixed: "right",
    align: "left",
    render: row => {
      return (
        <n-space style="padding: 5px 0;">
          {hasManagementDetails ? (
            <n-button text type="primary" onClick={() => handlerDetailDrawerClick(row)}>
              详情
            </n-button>
          ) : null}
          {hasManagementVideo && row?.videoMinioPath ? (
            <TablePreview src={transformMinioSrc(row.videoMinioPath)} type="video" name="查看视频"></TablePreview>
          ) : null}
          {hasManagementRefund && row?.action?.includes(21) ? (
            <n-button text type="primary" onClick={() => handlerRefund(row)}>
              退款
            </n-button>
          ) : null}
          {hasManagementCancelAppointment && row?.type === 2 && row?.consultationStatus === 2 ? (
            <n-popconfirm
              onPositiveClick={() => {
                handlerCancelAppointment(row.id);
              }}>
              {{
                trigger: () => (
                  <n-button text size="small" type="error">
                    取消预约
                  </n-button>
                ),
                default: () => <span style={{ width: "300px" }}>是否确定取消预约？</span>,
              }}
            </n-popconfirm>
          ) : null}
        </n-space>
      );
    },
  },
]);
watch([() => formValue.value.creationTime], () => {
  tableSearch();
});
/** 获取参数 */
const getParams = () => {
  const { searchType, searchValue, creationTime, consultationStatus, type, form } = formValue.value;
  const createStartTime = creationTime ? moment(creationTime[0]).format(`YYYY-MM-DD HH:mm:ss`) : null;
  const createEndTime = creationTime ? moment(creationTime[1]).format(`YYYY-MM-DD HH:mm:ss`) : null;
  return {
    searchType,
    searchValue,
    createStartTime,
    createEndTime,
    consultationStatus,
    type,
    form,
  };
};

/** 搜索 */
const handlerSearch = () => {
  tableSearch();
};

/** 表格刷新 */
function refresh() {
  tableSearch();
}

/* 表格搜索 */
const tableSearch = () => {
  pageTableData(getParams(), paginationRef.value);
};

const handlerRefund = row => {
  data.value = row;
  showRefundSetting.value = true;
};
/** 打开详情抽屉 */
const detailDrawerRef = ref();
const handlerDetailDrawerClick = row => {
  const _params = {
    row,
  };
  detailDrawerRef.value?.acceptParams(_params);
};
const orderStatusChange = val => {
  if (val === formValue.value.consultationStatus) {
    formValue.value.consultationStatus = null;
  } else {
    formValue.value.consultationStatus = val;
  }
  tableSearch();
};
/**
 * 将秒数时间戳转换为指定格式的时间显示
 * 规则：
 * 1. 小于等于1小时，展示分钟数，如：60分钟
 * 2. 大于1小时小于等于72小时，展示小时数。如果有不足1小时的分钟数则展示小时数+分钟数。如：24小时、24小时30分钟
 * 3. 大于72小时，展示天数。如果有不足1天的小时数或不足1小时的分钟数，则展示天数+小时数+分钟数。如：7天、7天06小时、7天06小时30分钟
 *
 */
function formatTimeFromSeconds(seconds: number): string {
  if (!seconds || seconds <= 0) {
    return "0分钟";
  }

  const duration = moment.duration(seconds, "seconds");
  const days = Math.floor(duration.asDays());
  const hours = duration.hours();
  const minutes = duration.minutes();

  // 小于等于1小时，展示分钟数
  if (seconds <= 3600) {
    return `${Math.max(1, Math.floor(duration.asMinutes()))}分钟`;
  }

  // 大于1小时小于等于72小时
  if (seconds <= 72 * 3600) {
    const totalHours = Math.floor(duration.asHours());

    // 如果有不足1小时的分钟数，则展示小时数+分钟数
    if (minutes > 0) {
      return `${totalHours}小时${minutes}分钟`;
    }

    // 否则只展示小时数
    return `${totalHours}小时`;
  }

  // 大于72小时，展示天数+小时数+分钟数
  let result = `${days}天`;

  // 如果有不足1天的小时数，则添加小时数
  if (hours > 0) {
    // 补零，确保小时数为两位数
    result += `${hours < 10 ? "0" + hours : hours}小时`;
  }

  // 如果有不足1小时的分钟数，则添加分钟数
  if (minutes > 0) {
    // 补零，确保分钟数为两位数
    result += `${minutes < 10 ? "0" + minutes : minutes}分钟`;
  }

  return result;
}
const exportLoading = ref<boolean>(false);
function convertFenToYuanStr(fen: number | string): string {
  let str = String(fen || 0).replace(/[^0-9]/g, "");
  if (str.length < 2) str = "00" + str; // 补零
  else if (str.length === 1) str = "0" + str;

  const len = str.length;
  const integer = str.slice(0, len - 2);
  const decimal = str.slice(len - 2);

  return `${integer || "0"}.${decimal}`;
}
const exportData = () => {
  exportLoading.value = true;
  medicalInquiryFormExport({
    data: { ...getParams() },
    pageVO: {
      current: paginationRef.value.current,
      size: paginationRef.value.pageSize,
    },
  })
    .then(res => {
      createMessageSuccess("导出成功");
    })
    .catch(err => {
      createMessageError(`导出失败:${err}`);
    })
    .finally(() => {
      exportLoading.value = false;
    });
};
const handlerCreateReservationForm = () => {
  showReservationForm.value = true;
};
const handlerCancelAppointment = async id => {
  isLoading.value = true;
  try {
    await cancelReservationForm({ id });
    createMessageSuccess("取消预约成功");
  } catch (err) {
    createMessageError(`取消预约失败:${err}`);
  } finally {
    isLoading.value = false;
    refresh();
  }
};
/** 组件挂载 */
onMounted(() => {
  tableSearch();
});
</script>

<style lang="less" scoped>
@import "@/styles/defaultVar.less";
</style>
