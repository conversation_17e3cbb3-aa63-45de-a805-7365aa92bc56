<template>
  <JModal
    v-model:show="show"
    width="780"
    :title="props.type === 'add' ? '创建直播' : '编辑直播'"
    @after-leave="closeModal"
    @positive-click="_save"
    :positiveButtonProps="{
      loading: isLoading,
    }"
  >
    <n-form
      ref="formRef"
      :rules="rules"
      :model="model"
      label-width="150"
      label-placement="left"
      require-mark-placement="right-hanging"
    >
      <n-grid :cols="8" :x-gap="24">
        <n-form-item-gi :span="8" label="直播间名称" path="liveName" required>
          <n-input v-model:value="model.name" placeholder="请输入直播间名字，最大150字" maxlength="150" />
        </n-form-item-gi>
        <n-form-item-gi :span="8" label="直播计划开始时间" path="startTime" required>
          <n-date-picker
            v-model:value="model.startTime"
            type="datetime"
            :is-date-disabled="isStartDateDisabled"
            :is-time-disabled="isStartTimeDisabled"
            clearable
          />
        </n-form-item-gi>
        <n-form-item-gi :span="8" label="直播计划结束时间" path="endTime" required>
          <n-date-picker
            v-model:value="model.endTime"
            type="datetime"
            :is-date-disabled="isEndDateDisabled"
            :is-time-disabled="isEndTimeDisabled"
            clearable
          />
        </n-form-item-gi>
        <n-gi :span="8" style="margin-left: 150px; margin-bottom: 12px">
          <span style="color: #999">开播时间和结束时间间隔不得短于30分钟，不得超过24小时</span>
        </n-gi>
        <n-form-item-gi :span="8" label="竖屏封面图" path="coverPicture" required>
          <UploadProductImg
            ref="uploadProductImgRef1"
            v-model:value="model.coverPicture"
            :maxFileSize="2"
            accept="image/*"
          />
        </n-form-item-gi>
        <!-- 提示 -->
        <n-gi :span="8" style="margin-left: 150px; margin-bottom: 12px">
          <span style="color: #999">竖屏封面图尺寸建议统一使用：1125 x 2250px,图片大小限制在2M以内。</span>
        </n-gi>
        <n-form-item-gi :span="8" label="分享图" path="sharePicture" required>
          <UploadProductImg
            ref="uploadProductImgRef2"
            v-model:value="model.sharePicture"
            :maxFileSize="1"
            accept="image/*"
          />
        </n-form-item-gi>
        <!-- 提示 -->
        <n-gi :span="8" style="margin-left: 150px; margin-bottom: 12px">
          <span style="color: #999">分享图尺寸建议统一使用：800 x 640px,图片大小限制在1M以内。</span>
        </n-gi>
      </n-grid>
    </n-form>
  </JModal>
</template>

<script setup lang="ts" name="AddorEditVideo">
import { ref, watch } from "vue";
import { useMessages } from "@/hooks";
import UploadProductImg from "@/components/UploadProductImg/index.vue";
import { addLiveRoom, updateLive } from "@/services/api";
import moment from "moment";
const initParams = {
  name: "",
  startTime: null,
  endTime: null,
  coverPicture: [],
  sharePicture: [],
};
const model = ref({ ...initParams });
const props = ref<any>({});
/* 提示信息 */
const { createMessageSuccess, createMessageError } = useMessages();
/* 模态框显隐状态 */
const show = ref(false);
const uploadProductImgRef1 = ref<InstanceType<typeof UploadProductImg> | null>(null);
const uploadProductImgRef2 = ref<InstanceType<typeof UploadProductImg> | null>(null);
/* 表单规则 */
const rules = {
  liveName: {
    required: true,
    trigger: ["blur", "change"],
    message: "请输入直播间名称",
    validator: () => {
      return model.value.name !== "";
    },
  },
  startTime: {
    required: true,
    trigger: ["blur", "change"],
    message: "请选择开始时间",
    validator: () => {
      return model.value.startTime !== null;
    },
  },
  endTime: {
    required: true,
    trigger: ["blur", "change"],
    message: "请选择结束时间",
    validator: () => {
      return model.value.endTime !== null;
    },
  },
  coverPicture: {
    required: true,
    trigger: ["blur", "change"],
    message: "请选择竖屏封面图",
    validator: () => {
      return model.value.coverPicture.length > 0;
    },
  },
  sharePicture: {
    required: true,
    trigger: ["blur", "change"],
    message: "请选择分享图",
    validator: () => {
      return model.value.sharePicture.length > 0;
    },
  },
};
const acceptParams = params => {
  show.value = true;
  props.value = params;
  if (props.value.type == "edit") {
    model.value.name = props.value?.row?.name;
    model.value.startTime = props.value?.row?.liveStartTime ? moment(props.value?.row?.liveStartTime).valueOf() : null;
    model.value.endTime = props.value?.row?.liveEndTime ? moment(props.value?.row?.liveEndTime).valueOf() : null;
    model.value.coverPicture = [{ path: props.value?.row?.verticalCoverImage }];
    model.value.sharePicture = [{ path: props.value?.row?.shareCoverImage }];
  }
};
/* 表单实例 */
const formRef = ref(null);
/* 表单参数初始化 */
/* 清空表单 */
const formDataReset = () => {
  initParams.coverPicture = [];
  initParams.sharePicture = [];
  model.value = { ...initParams };
};
/* 关闭弹窗 */
const closeModal = () => {
  formDataReset();

  isLoading.value = false;
  // 弹窗取消
  show.value = false;
};
/* 确认--保存 */
const isLoading = ref(false);
/** 获取参数 */
const getParams = () => {
  const { name, startTime, endTime, sharePicture, coverPicture } = model.value;
  const liveStartTime = startTime ? moment(startTime).format(`YYYY-MM-DD HH:mm:ss`) : null;
  const liveEndTime = endTime ? moment(endTime).format(`YYYY-MM-DD HH:mm:ss`) : null;
  const verticalCoverImage = coverPicture[0].path;
  const shareCoverImage = sharePicture[0].path;
  return {
    name,
    liveStartTime,
    liveEndTime,
    verticalCoverImage,
    shareCoverImage,
  };
};
const _save = (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors && !isLoading.value) {
      if (model.value.startTime > model.value.endTime) {
        createMessageError(`直播计划结束时间 不能早于 直播计划开始时间`);
        return;
      }
      const start = moment(model.value.startTime);
      const end = moment(model.value.endTime);
      const diffMinutes = end.diff(start, 'minutes');

      if (diffMinutes < 30) {
        createMessageError('直播计划结束时间与开始时间间隔不得少于30分钟');
        return;
      }

      if (diffMinutes > 1440) {
        createMessageError('直播计划结束时间与开始时间间隔不得超过24小时');
        return;
      }
      if (uploadProductImgRef1.value?.isUploadLoading || uploadProductImgRef2.value?.isUploadLoading) {
        createMessageError("文件正在上传中，请稍等！");
        return;
      }
      try {
        isLoading.value = true;
        props.value.type === "edit"
          ? await updateLive({
              ...getParams(),
              id: props.value.row.id,
            })
          : await addLiveRoom(getParams());
        createMessageSuccess(`${props.value.type === "edit" ? "编辑" : "创建"}直播成功`);
        // 刷新表格数据
        props.value.refresh();
        closeModal();
      } catch (e) {
        createMessageError(`${props.value.type === "edit" ? "编辑" : "创建"}直播失败 ${e}`);
        isLoading.value = false;
      }
    }
  });
};
const isStartDateDisabled = (ts: number) => {
  // 获取当前日期（不含时间）
  const today = moment().startOf("day");
  // 获取目标日期
  const targetDate = moment(ts).startOf("day");

  // 若目标日期早于今天，则禁用
  return targetDate.isBefore(today);
};
const isStartTimeDisabled = (ts: number) => {
  const now = moment(); // 当前时间
  const target = moment(ts); // 目标时间
  const today = moment().startOf("day"); // 今天零点
  const targetDate = moment(ts).startOf("day"); // 目标日期零点

  // 如果目标日期不是今天，不禁用时间
  if (!targetDate.isSame(today, "day")) {
    return {
      isHourDisabled: () => false,
      isMinuteDisabled: () => false,
      isSecondDisabled: () => false,
    };
  }

  // 如果是今天，禁用当前时间之前的时间
  return {
    isHourDisabled: (hour: number) => hour < now.hour(),
    isMinuteDisabled: (minute: number) => {
      if (target.hour() < now.hour()) return true; // 小时已过，分钟全禁用
      return target.hour() === now.hour() && minute < now.minute(); // 同一小时，禁用当前分钟前的
    },
    isSecondDisabled: (second: number) => {
      if (target.hour() < now.hour()) return true; // 小时已过，秒全禁用
      if (target.hour() === now.hour() && target.minute() < now.minute()) return true; // 分钟已过，秒全禁用
      return target.hour() === now.hour() && target.minute() === now.minute() && second < now.second(); // 同分禁用当前秒前的
    },
  };
};
const isEndDateDisabled = (ts: number) => {
  const now = moment().startOf("day"); // 当前日期零点
  const targetDate = moment(ts).startOf("day"); // 目标日期零点

  // 始终禁止选择早于当前日期的日期
  if (targetDate.isBefore(now)) return true;

  // 如果开始时间未设置，仅检查当前日期限制
  if (!model.value.startTime) return false;

  const startDate = moment(model.value.startTime).startOf("day"); // 开始日期零点

  // 如果开始时间已设置，同时禁止早于开始日期的日期
  return targetDate.isBefore(startDate);
};
const isEndTimeDisabled = (ts: number) => {
  const now = moment(); // 当前时间（精确到秒）
  const target = moment(ts); // 目标时间
  const targetDate = moment(ts).startOf("day"); // 目标日期零点
  const today = moment().startOf("day"); // 今天零点

  // 如果目标日期是今天，禁用当前时间之前的时间
  if (targetDate.isSame(today, "day")) {
    return {
      isHourDisabled: (hour: number) => hour < now.hour(),
      isMinuteDisabled: (minute: number) => {
        if (target.hour() < now.hour()) return true; // 小时已过，分钟全禁用
        return target.hour() === now.hour() && minute < now.minute(); // 同一小时，禁用当前分钟前的
      },
      isSecondDisabled: (second: number) => {
        if (target.hour() < now.hour()) return true; // 小时已过，秒全禁用
        if (target.hour() === now.hour() && target.minute() < now.minute()) return true; // 分钟已过，秒全禁用
        return target.hour() === now.hour() && target.minute() === now.minute() && second < now.second(); // 同分禁用当前秒前的
      },
    };
  }

  // 如果开始时间未设置，且目标日期不是今天 → 不禁用时间
  if (!model.value.startTime) {
    return {
      isHourDisabled: () => false,
      isMinuteDisabled: () => false,
      isSecondDisabled: () => false,
    };
  }

  const start = moment(model.value.startTime); // 开始时间
  const startDate = moment(model.value.startTime).startOf("day"); // 开始日期零点
  const targetDateWithStart = moment(ts).startOf("day"); // 目标日期零点

  // 如果结束日期不等于开始日期，不禁用时间
  if (!startDate.isSame(targetDateWithStart, "day")) {
    return {
      isHourDisabled: () => false,
      isMinuteDisabled: () => false,
      isSecondDisabled: () => false,
    };
  }

  // 如果结束日期等于开始日期，禁用开始时间之前的时间
  return {
    isHourDisabled: (hour: number) => hour < start.hour(),
    isMinuteDisabled: (minute: number) => {
      if (target.hour() < start.hour()) return true;
      return target.hour() === start.hour() && minute < start.minute();
    },
    isSecondDisabled: (second: number) => {
      if (target.hour() < start.hour()) return true;
      if (target.hour() === start.hour() && target.minute() < start.minute()) return true;
      return target.hour() === start.hour() && target.minute() === start.minute() && second < start.second();
    },
  };
};
defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less"></style>
