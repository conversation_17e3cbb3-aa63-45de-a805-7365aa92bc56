<template>
  <NSpin size="small" :show="show">
    <div class="inner-page-height" style="background-color: #FFF;">
      <div class="layout" style="background-color: #f2f3f5;">
        <!--介绍 -->
        <NCard class="header" size="small" :bordered="false">
          <div class="header_title">分账规则设置</div>
          <p>商城支持一级分销。管理员设置好分销规则后可以将小程序端任意一个用户设置为分销员。分销员通过推广商品或短视频等内容邀请客户注册（只能邀请未注册用户）。
          分销员在小程序分销中心页面可以查看名下绑定的客户、订单和佣金信息。</p> 
        </NCard>

        <!-- 主内容 -->
        <main class="content" style="height: 60%;">
          <NScrollbar style="max-height: calc(100vh - 260px);width: 75%;">
              <div style="padding: 10px 0;">
                 <p>
                   <span style="font-size: 18px;font-weight: 700">全局设置</span>
                 </p>
              </div>

              <!-- 启用状态 -->
              <div class="line" style="padding-top: 20px;">
                <StoreTitle>
                    <span>启用状态</span>
                    <NSwitch v-model:value="model.active" size="small" />
                </StoreTitle>
                <p style="font-size: 16px" class="marginTop">启用分销功能，分销员在小程序端能看到分销中心入口，进行商品推广会产生新的客户和分佣订单。关闭后，分销中心入口不可见，所有分销员推广不会再产生新客户和分佣订单。</p>
              </div>

              <!-- 分佣规则 -->
              <div class="line">
                 <StoreTitle title="分佣规则" />
                 <p style="display: flex;" class="marginTop" >
                   <div style="font-size: 16px;width: 130px;">
                      客户下单分佣:
                   </div>
                   <NRadioGroup v-model:value="model.ordersReciprocated">
                      <NSpace>
                        <NRadio value="10">
                          仅分绑定分销员
                        </NRadio>
                        <NRadio value="12">
                          仅分链接分享员
                        </NRadio>
                      </NSpace>
                    </NRadioGroup>
                 </p>
                 <p style="display: flex;" class="marginTop">
                   <span style="font-size: 16px;width: 130px;">
                      分销员自购分佣:
                   </span>
                   <NRadioGroup v-model:value="model.selfPurchaseCommission">
                      <NSpace>
                        <NRadio value="11">
                          优先分佣给绑定分销员，如无绑定分销员，则给下单分销员分佣，并给分销员归属的团队计算业绩。
                        </NRadio>
                        <NRadio value="12">
                          仅分链接分享员
                        </NRadio>
                      </NSpace>
                   </NRadioGroup>
                 </p>
              </div>
              
              <!-- 佣金结算 -->
              <div  class="line">
                  <StoreTitle title="佣金结算" />
                  <p style="display: flex;" class="marginTop">
                   <span style="font-size: 16px;width: 130px;">
                      佣金计算方式:
                   </span>
                   <NRadioGroup v-model:value="model.commissionCalculations">
                      <NSpace>
                        <NRadio value="10">
                          按商品销售额计算佣金，佣金金额 = 商品销售总额 * 佣金比例
                        </NRadio>
                      </NSpace>
                   </NRadioGroup>
                 </p>
                  <p style="font-size: 16px;margin-top: 10px;"> 
                     <div style="display: flex; align-items: center;">
                      <span>订单状态变更为已完成后的第</span>
                       <NInputNumber  
                        v-model:value="model.number" 
                        :show-button="false" 
                        :max="366" 
                        :min="1"
                        placeholder="可输入1-366"
                        style="width: 100px;margin: 0 5px;"
                        @blur="handleNumberDays"
                        />
                      <span>天上午8点结算佣金</span>
                     </div>
                     <div class="marginTop">
                      <p>订单支付后，分销员即可查看待结算佣金金额。到达结算时间点，佣金状态即表更为已结算，结算后的佣金可提现或线下处理。</p>
                      <!-- <p>如果设置0天，订单完成时立即结算。</p> -->
                     </div>
                  </p>
              </div>
          </NScrollbar>
          <NScrollbar style="width: 25%;height: calc(100vh - 260px);" x-scrollable>
            <div style="display: flex;justify-content: center;">
              <img style="max-width: 320px;" :src="distributionCenters" alt="无图片" />
            </div>
            <p style="font-size: 22px;font-weight: 700;text-align: center;margin-top: 10px;">分销中心页面样式</p>
           </NScrollbar>
        </main>

        <!-- 底部 -->
        <footer class="footer" style="height: 20%;">
          <NButton v-if="hasStructreSettingsIndexSaveAuth" type="primary" style="width: 100px;" @click="handleUpdate" :disabled="((model.active == model.enableStateDefaults) && !model.changeDays && !model.changeOrdersReciprocated && !model.changeelfPurchaseCommission)" :loading="saveLoading">保存</NButton>
        </footer>
      </div>
   </div>
  </NSpin>
</template>

<script lang="ts" setup>
import {onMounted, ref, watch } from 'vue';
import distributionCenters from "@/assets/image/system/distributionCenters.png"
import { globalConfigsGetDistributionConfig, globalConfigsUpdateDistributionConfig } from '@/services/api';
import { useMessages } from "@/hooks";
import { hasStructreSettingsIndexSaveAuth } from "../authList";

/* 提示信息 */
const message = useMessages();

onMounted(()=>{
  getDistributionConfig()
})
//保存按钮loading
const saveLoading = ref(false)
//显示页面加载状态
const show = ref(false)

const initParams = {
    ordersReciprocated:null,
    selfPurchaseCommission:null,
    commissionCalculations:null,
    number:null,
    active:false,
    enableStateDefaults:undefined,//默认的启用状态
    defaultNumberDays:undefined,//默认的结算佣金天数
    changeDays:false,//默认的获取的天数是否有变化
    dataList:undefined,
    defaultOrdersReciprocated:undefined,//默认的下单分佣
    defaultSelfPurchaseCommission:undefined,//默认的自购分佣
    changeOrdersReciprocated:false,//默认的获取的下单分佣是否有变化
    changeelfPurchaseCommission:false,//默认的获取的自购分佣是否有变化
};

const model = ref({ ...initParams });

//获取数据
const getDistributionConfig = async () => {
  show.value = true;
  const params = { data: {} };
  
  try {
    const { records } = await globalConfigsGetDistributionConfig(params);

    // 筛选出需要的配置项
    model.value.dataList = records.filter(item => 
      item.key === "sto_distribution_state" || item.key === "sto_distribution_settlement_day" || item.key === "sto_distribution_customer_rule" || item.key === "sto_distribution_member_rule"
    );

    //通过 find 找到各个配置项
    const stoState = records.find(item => item.key === "sto_distribution_state");
    const settlementDay = records.find(item => item.key === "sto_distribution_customer_rule");
    const selfPurchase = records.find(item => item.key === "sto_distribution_member_rule");
    const commissionCalculation = records.find(item => item.key === "sto_distribution_calculation_rule");
    const number = records.find(item => item.key === "sto_distribution_settlement_day");

    // 提取 'true' 判断，避免多次重复写
    const isActive = stoState?.value === 'true';

    // 批量赋值
    Object.assign(model.value, {
      enableStateDefaults: isActive,
      active: isActive,
      ordersReciprocated: settlementDay?.value,
      defaultOrdersReciprocated: settlementDay?.value,
      selfPurchaseCommission: selfPurchase?.value,
      defaultSelfPurchaseCommission: selfPurchase?.value,
      commissionCalculations: commissionCalculation?.value,
      number: number?.value,
      defaultNumberDays: number?.value
    });
    if(model.value.changeOrdersReciprocated){
      model.value.changeOrdersReciprocated = false
    }
    if(model.value.changeelfPurchaseCommission){
      model.value.changeelfPurchaseCommission = false
    }
  } catch (err) {
    message.createMessageError("获取分销(分佣)开发配置失败:" + err);
  } finally {
    show.value = false;
  }
};

//保存数据
const handleUpdate = async() => {
  model.value.dataList = model.value.dataList.map((item)=>{
    if (item.key === 'sto_distribution_state') {
       item.value = model.value.active ? 'true' : 'false';
    }
    if (item.key === 'sto_distribution_settlement_day') {
       item.value = String(model.value.number)
    }
    if (item.key === 'sto_distribution_customer_rule') {
       item.value = String(model.value.ordersReciprocated) 
    }
    if (item.key === 'sto_distribution_member_rule') {
       item.value = String(model.value.selfPurchaseCommission)
    }
    delete item.createTime; // 删除 createTime
    delete item.updateTime; // 删除 updateTime
    delete item.updateBy; // 删除 updateBy
    return item; // 返回修改后的项
  })
  const params = {
    data: model.value.dataList
  }
  saveLoading.value = true
  try{
      await globalConfigsUpdateDistributionConfig(params)
      message.createMessageSuccess("修改后台分销设置成功")
      getDistributionConfig()
  }catch(err){
      message.createMessageError("修改后台分销设置失败:" + err)
  }finally{
    saveLoading.value = false
  }
}

const handleNumberDays = () =>{
  if(model.value.number && !Number.isInteger(model.value.number)){
     model.value.number = Math.trunc(model.value.number); // 截取小数部分
  }
}

//监听结算佣金的计算
watch(() =>
  model.value.number,
  (newValue)=>{
    if(newValue != model.value.defaultNumberDays){
      model.value.changeDays = true
    }else{
      model.value.changeDays = false
    }
  }
)

//下单分佣
watch(() =>
  model.value.ordersReciprocated,
  (newValue)=>{
    if(newValue != model.value.defaultOrdersReciprocated){
      model.value.changeOrdersReciprocated = true
    }else{
      model.value.changeOrdersReciprocated = false
    }
  }
)

//自购分佣
watch(() =>
  model.value.selfPurchaseCommission,
  (newValue)=>{
    if(newValue != model.value.defaultSelfPurchaseCommission){
      model.value.changeelfPurchaseCommission = true
    }else{
      model.value.changeelfPurchaseCommission = false
    }
  }
)

</script>

<style lang="less" scoped>
/* 布局容器 */
.layout {
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.header {
  background-color: #fff;
  border-bottom: 1px solid #EEEEEE;
  .header_title {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 600;
    font-size: 20px;
    color: #333333;
    line-height: 32px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-bottom: 12px;
  }
  p {
    font-size: 16px;
  }
}

/* 主内容样式 */
.content {
  display: flex;
  justify-content: space-between;
  flex: 1;
  padding: 16px;
  color: #333;
  font-size: 18px;
  background-color: #fff;
}

/* 底部样式 */
.footer {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding-left: 24px;
    height: 65px;
    background-color: #fff;
}

/* 横线样式 */
.line {
    padding: 55px 0; 
    border-bottom: 2px solid #EEEEEE;
}

/* 文字加粗 */
.textBolded{
    font-size: 18px;
    font-weight: 700;
}

.marginTop{
    margin-top: 15px;
}
</style>