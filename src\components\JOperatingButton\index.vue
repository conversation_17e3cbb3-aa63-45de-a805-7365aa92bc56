<template>
    <div class="operating-button" @click="handleClick">
      <NButton v-bind="$attrs" text size="small">
        <slot></slot>
      </NButton>
    </div>
  </template>
  
  <script lang="ts" setup>
  import { NButton } from "naive-ui";
  import { defineEmits } from 'vue';
  
  // 定义事件
  const emits = defineEmits(['click']);
  
  // 处理点击事件的函数
  const handleClick = () => {
    emits('click');
  };
  
  // 组件名称
  defineOptions({ name: 'JOperatingButton' });
  </script>
  
  <style lang="less" scoped>
  .operating-button {
    width: auto;
    box-sizing: border-box;
    padding: 2px 4px;
    border-radius: 5px;
    transition: background 0.3s ease, border-color 0.3s ease, color 0.3s ease;
    
    &:hover {
      background: rgba(51, 112, 255, 0.1);
      border-color: transparent;
      color: #3370ff;
    }
  }
  </style>