<template>
  <div class="drug-information-wrapper">
    <!-- 第一项药品信息 -->
    <n-form-item style="margin-left: 10px" v-if="tableDataRef.length > 0">
      <n-grid x-gap="12" :y-gap="12" :cols="1">
        <n-gi>
          <n-space align="start" direction="vertical">
            <n-tag
              v-for="(row,index) in tableDataRef"
              :key="row.id"
              closable
              @close="handleClose(row)"
              class="spaced-tag"
            >
              <template #default>
                <!-- 使用 n-space 组件让内容在同一行 -->
                <n-space align="center">
                  <span>{{index+1}}.&nbsp;{{row.name || row.productName}}</span>
                  <!-- 给输入框设置固定宽度 -->
                  <n-input-number
                    :disabled="props.mode == 'detail'"
                    v-model:value="row.count"
                    style="width: 100px;"
                    precision="0"
                    min="1"
                    max="9999"
                  />
                  <span v-if="row?.productSpecDTOList?.length > 0">{{row.productSpecDTOList[0].name}}</span>
                  <span v-else>{{row?.specName || '-'}}</span>
                </n-space>
              </template>
            </n-tag>
          </n-space>
        </n-gi>
        <n-gi>
          <n-flex style="margin-bottom: 10px">
            <div style="width:280px">
              <n-input-group>
                <n-select
                  placeholder="选择剂数"
                  :disabled="props.mode== 'detail'"
                  v-model:value="tableDataRef[0].chineseDosageCount"
                  :options="chineseDosageCountOptions"
                />
                <n-button type="default" ghost>剂</n-button>
              </n-input-group>
            </div>
            <div style="width:280px">
              <template v-if="tableDataRef[0].routeOfAdministration == 0">
                <n-input-group>
                  <n-select
                    style="width:33%"
                    v-model:value="tableDataRef[0].routeOfAdministration"
                    :options="routeOfAdministrationOptions"
                    :disabled="props.mode == 'detail'"
                    placeholder="请选择给药方式"
                  />
                  <n-input
                    v-model:value="tableDataRef[0].routeOfAdministrationOther"
                    style="width:67%"
                    :maxlength="10"
                    show-count
                    :disabled="props.mode == 'detail'"
                    placeholder="请输入其他给药方式"
                  />
                </n-input-group>
              </template>
              <template v-else>
                <n-select
                  v-model:value="tableDataRef[0].routeOfAdministration"
                  :options="routeOfAdministrationOptions"
                  :disabled="props.mode == 'detail'"
                  placeholder="请选择给药方式"
                />
              </template>
            </div>
            <div style="width:280px">
              <template v-if="tableDataRef[0].frequencyOfAdministration == 0">
                <n-input-group>
                  <n-select
                    style="width:33%"
                    v-model:value="tableDataRef[0].frequencyOfAdministration"
                    :options="frequencyOfAdministrationOptions"
                    :disabled="props.mode == 'detail'"
                    placeholder="请选择用药频次"
                  />
                  <n-input
                    v-model:value="tableDataRef[0].frequencyOfAdministrationOther"
                    :disabled="props.mode== 'detail'"
                    :maxlength="10"
                    show-count
                    style="width:67%"
                    placeholder="请输入其他用药频次"
                  />
                </n-input-group>
              </template>
              <template v-else>
                <n-select
                  v-model:value="tableDataRef[0].frequencyOfAdministration"
                  :options="frequencyOfAdministrationOptions"
                  :disabled="props.mode == 'detail'"
                  placeholder="请选择用药频次"
                />
              </template>
            </div>
          </n-flex>
          <n-flex>
            <div style="width:280px">
              <n-input-group>
                <n-button type="default" ghost>每次</n-button>
                <n-input-number
                  placeholder="请输入剂量"
                  precision="0"
                  :disabled="props.mode == 'detail'"
                  v-model:value="tableDataRef[0].dosage"
                />
                <n-select
                  style="width: 150px"
                  placeholder="单位"
                  :disabled="props.mode== 'detail'"
                  v-model:value="tableDataRef[0].dosageUnits"
                  :options="dosageUnitsOptions"
                />
              </n-input-group>
            </div>
            <div style="width:280px">
              <n-input-group>
                <n-input
                  placeholder="请输入医嘱,可留空"
                  show-button="{false}"
                  maxlength="30"
                  :disabled="props.mode == 'detail'"
                  v-model:value="tableDataRef[0].medicalAdvice"
                />
              </n-input-group>
            </div>
          </n-flex>
        </n-gi>
      </n-grid>
    </n-form-item>
  </div>
</template>

<script setup lang="tsx">
import {ref, watch} from "vue";
import type {DataTableRowKey} from "naive-ui";
import {useBoolean, useMessages} from '@/hooks';
import {
  chineseDosageCountMap,
  dosageUnitsMap,
  frequencyOfAdministrationMap,
  routeOfAdministrationMap,
} from "@/views/DoctorEndModule/Prescription/types";
import AddBtn from "@/assets/image/opt/addBtn.png"
import SubBtn from "@/assets/image/opt/subBtn.png"

/** 相关组件 */

export interface DrugInformation {
  id: string,
  productId: string, // 商品ID
  specId: string, // 规格ID
  routeOfAdministration: number, // 给药方式
  routeOfAdministrationOther: string // 自定义给药方式
  frequencyOfAdministration: number // 用药频次
  frequencyOfAdministrationOther: string // 自定义用药频次
  dosage: number, // 剂量
  dosageUnits: number, // 剂量单位 1=粒；2=片
  count: number // 购买数量
  chineseDosageCount: number, // 中文剂量
  medicalAdvice: string, // 医嘱
  name:string // 商品名称
  productSpecDTOList:any,
  specName?:string,
  productName?:string
}

type DrugInformationTable = {
  value: Array<DrugInformation>,
  mode: 'add' | 'edit' | 'detail';
}
const {createMessageError} = useMessages();

defineOptions({
  name: 'DrugInformationTable'
})

/** Props */
const props = withDefaults(defineProps<DrugInformationTable>(), {
  value: () => [],
  mode: 'add'
});

/** emits */
const emits = defineEmits<{
  (e: 'update:value', value: Array<any>): void;
  (e: 'productSpecVOList', value: Array<any>): void;
}>();

/** 付款方式展示 */
const show = ref(false);
/** 售价 */
const price = ref(null);

/** 表格数据 */
const tableDataRef = ref<Array<Partial<DrugInformation>>>([]);

// 通用转换器（支持TS类型推断）
const mapToOptions = <T extends string | number>(map: Map<number, T>) =>
  Array.from(map, ([value, label]) => ({label, value}));

// 给药方式
const routeOfAdministrationOptions = mapToOptions(routeOfAdministrationMap);

// 用药频次选项
const frequencyOfAdministrationOptions = mapToOptions(frequencyOfAdministrationMap);

// 剂量选项
const dosageUnitsOptions = mapToOptions(dosageUnitsMap);

// 中药剂量选项
const chineseDosageCountOptions = mapToOptions(chineseDosageCountMap);

// 删除
function handleClose(row) {
  tableDataRef.value = tableDataRef.value.filter(item => item.id != row.id)
  emits('update:value', tableDataRef.value);
}

/** 监听 */
watch(() => props.value, (newVal) => {
  tableDataRef.value = newVal;
  console.log('tableDataRef====>', tableDataRef.value)
}, {immediate: true});
</script>

<style lang="less" scoped>
.drug-information-wrapper {
  position: relative;
  width: 100%;
}

:deep(.n-checkbox .n-checkbox-box) {
  width: 18px;
  height: 18px;
}

/* 定义带点和虚线的项样式 */
.dashed-item {
  position: relative;
  padding-left: 16px; /* 给虚线和点留出空间 */
}

.dashed-item::before {
  content: '';
  position: absolute;
  left: 4px;
  top: 11px;
  transform: translateY(-50%);
  width: 10px; /* 内层圆大小 */
  height: 10px;
  background-color: #409eff; /* 内层颜色为白色 */
  border-radius: 50%;
  box-shadow: 0 0 0 6px #f1f7ff; /* 外层圆环，颜色为蓝色，宽度 3px */
}

.dashed-item::after {
  content: '';
  position: absolute;
  left: 9px;
  top: 20px; /* 调整虚线起始位置 */
  bottom: 0;
  width: 1px;
  border-left: 1px dashed #c0c4cc; /* 虚线样式和颜色，可按需修改 */
}

.dashed-item:last-of-type::after {
  content: none;
}

.spaced-tag {
  margin-bottom: 8px; /* 调整底部间距 */
}
</style>
