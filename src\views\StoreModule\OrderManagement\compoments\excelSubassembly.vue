<template>
    <div>
      <input type="file" class="input-file" style="display: none" @change="handleFileUpload">
      <n-button type="primary" secondary @click="openFileExplorer" :loading="props.isLoadingShow" >{{ props.buttonName }}</n-button>
    </div>
</template>
  
<script setup lang="tsx">
import {useMessages} from "@/hooks";
const { createMessageSuccess, createMessageError } = useMessages();
const props = withDefaults(
  defineProps<{
    buttonName:string
    isLoadingShow:boolean
  }>(),
  {
    buttonName:'',
    isLoadingShow:false
  }
);
const emits = defineEmits<{
    (e: "importedData", importedData); //导入的数据返回 
  }>();
const openFileExplorer = () => {
      const fileInput =  document.querySelector(".input-file")
      if (fileInput) fileInput.click();
};
const handleFileUpload = (event) => {
      const file = event.target.files[0];
      const fileName = file.name;
      const fileType = file.type;
      if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls') || fileType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || fileType === 'application/vnd.ms-excel') {
        const reader = new FileReader();
        reader.onload = (e) => {
          const file = new File([e.target.result], fileName, { type: fileType });
          const formData = new FormData();
          formData.append('file', file); // 将二进制数据附加到表单数据中
          emits("importedData", formData);
          
        };
        reader.readAsArrayBuffer(file);
      }else{
        createMessageError('请上传.xlsx或.xls格式的文件');
      }
     
};
</script>