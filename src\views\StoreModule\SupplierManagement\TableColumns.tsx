import { NSpace, NTag } from "naive-ui";
export const createsupplierColumns = ({operation}) => {
    return[
      {
        title: "供应商ID",
        key: "id",
        width: 120,
        align: "left",
      },
      {
        title: "供应商名称",
        key: "supplierName",
        width: 180,
        align: "left",
      },
      {
        title: "联系人姓名",
        key: "contactName",
        width: 150,
        align: "left",
      },
      {
        title: "联系人电话",
        key: "contactPhone",
        width: 150,
        align: "left",
      },
      {
        title: "公司全称",
        key: "companyName",
        width: 150,
        align: "left",
      },
      {
        title: "创建时间",
        key: "createTime",
        width: 150,
        align: "left",
        sorter: true,
        isSortDefault: true,
      },
      {
        title: "状态",
        key: "status",
        width: 150,
        align: "left",
        render: (row) => {
          return (
              <NSpace>
                  <NTag
                      bordered={false}
                      size="small"
                      type={row.status === 0 ? "error" : "success"}
                  >
                      {row.status === 0 ? "禁用" : "启用"}
                  </NTag>
              </NSpace>
          );
        }
      },
      {
        title: "操作",
        key: "action",
        width: 120,
        fixed: "right",
        align: "left",
        render(row) {
          return operation(row);
        },
      },
    ]
}
