<template>
    <n-popconfirm
      v-model:show="show"
      :width="props.width"
    >
      <template #trigger >
        <n-button 
         :ghost="props.ghost" 
         :size="props.size" 
         :text="props.text" 
         :type="props.type"
         :disabled="props.disabled"
         :secondary="props.secondary"
         @click="show=true">{{props.buttonContent}}
        </n-button>
      </template>
      <template #icon>
      <n-icon color="#f0a020">
        <AlertCircleSharp />
      </n-icon>
      <div class="promptContent">
        <p v-html="props.promptContent"></p>
      </div>
      
    </template>
      <template #action style="margin-top: 0px;">
          <n-button size="small" @click="handleNegativeClick" :disabled="props.loading">
            取消
          </n-button>
          <n-button :type="props.positiveType" size="small" @click="handlePositiveClick" :loading="props.loading">
            {{props.positiveValue}}
          </n-button>
          {{ null }}
      </template>
    </n-popconfirm>
  </template>
  <script setup lang="tsx">
  import { ref, watch } from "vue";
  import {AlertCircleSharp} from '@vicons/ionicons5';
  interface Props {
    promptContent?: string | number,//提示内容
    buttonContent?: string | number,//按钮内容
    text?:boolean, //是否显示为文本按钮
    type?:string, //按钮的类型
    loading?:boolean,//加载效果
    size?:string,//按钮的尺寸
    ghost?:boolean,//按钮是否透明
    disabled?:boolean,//按钮是否禁用
    secondary?:boolean,//是否是次要按钮
    positiveValue?:string | number //确认按钮
    positiveType?:string, //确认按钮的类型
    width?:number,//
  }
  
  const props = withDefaults(defineProps<Props>(), {
     promptContent: '是否确认',
     buttonContent: '删除',
     text:true,
     type:'error',
     loading:false,
     size:'medium',//按钮的尺寸
     ghost:false,
     disabled:false,
     secondary:false,
     positiveValue:'确认',
     positiveType:'info',
     width:300,
  });
  
  const emits = defineEmits<{
    (e: 'handleClick'),
  }>();
  
  const show = ref(false)

  /** 二次确认按钮 */
  const handlePositiveClick = () =>{
    emits('handleClick')
  }
  /** 取消弹窗 */
  const handleNegativeClick = () =>{
    handleClearContent()
  }
  
  /** 清除内容 */
  const handleClearContent = () =>{
    show.value = false
  }
  
  /** 监听props.loading */
  watch(() => {
          return props.loading;
      },
      (newVal) => {
          if(!newVal){
             handleClearContent()
          }
      }
  )

  </script>
  <style lang="less" scoped>
  @import "@/styles/default.less";
  .promptContent{
      padding: 4px;
      box-sizing: border-box;
      p {
        font-size: 14px;
        color: #757575;
        margin-bottom: 10px;
      }
  }
  
  </style>
    