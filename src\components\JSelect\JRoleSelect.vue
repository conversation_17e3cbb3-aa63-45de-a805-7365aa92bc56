<template>
	<JSelect
		:value="props.value"
		:loading="isLoading"
		:onFocus="handlerFocus"
		:options="roleList"
		:onClear="handleClear"
		@update:value="onChange"
		placeholder="请选择角色"
		:filter="customFilter"
	/>
</template>

<script setup lang="ts" name="JRoleSelect">
import { ref, watch } from "vue";
import JSelect from "@/components/JSelect/index.vue";
import { isArray } from "@/utils";
import { useMessages } from "@/hooks";
import { getPlatformUser } from "@/services/api";

/* Props */
const props = withDefaults(
	defineProps<{
		value?: Array<string | number> | string | number | null;
	}>(),{},
);

/** emits */
const emits = defineEmits<{
	(e: "update:value", selectValue: any): void;
	(e: "update:label", selectValue: any): void;
}>();

/* 提示 */
const message = useMessages();

/* 是否加载 */
const isLoading = ref(false);

/** 列表 */
const roleList = ref([]);

/* 筛选、转化 */
const handleData = (filterData) => {
	return filterData.map((item) => ({ label: item.name, value: item.id }));
};

/** 获取焦点 */
function handlerFocus() {
	if (!roleList.value.length) {
		getDosageList();
	}
};

/* 获取剂型列表 */
const getDosageList = async () => {
	try {
		isLoading.value = true;
		const result = await getPlatformUser({});
		if(isArray(result)) {
			roleList.value = handleData(result);
		}
	} catch (error) {
		message.createMessageError("获取平台角色列表失败: " + error);
	} finally {
		isLoading.value = false;
	}
};

/** 前端过滤 */
const customFilter = (keyword, options) => {
	const labelMatch = options.label
		.toLowerCase()
		.includes(keyword.toLowerCase());
	return labelMatch;
};

/** 选中值回调 */
function onChange(value,select) {
  emits("update:value", value);
  emits("update:label", select?.label);
  
}

/** 清空 */
const handleClear = () => {
	emits("update:value", null);
};

/** 监听 */
watch(
	() => props.value,
	(newVal) => {
		if (newVal && roleList.value.length === 0) {
			getDosageList();
		}
	},
	{ immediate: true },
);
</script>

<style scoped lang="less"></style>
