<template>
    <TabsLayout 
        v-model:value="activeRoleTypeRef" 
        :tabsData="tabsData" 
        :onlyTabs="true"
    >
      <keep-alive>
        <component :is="currentPage" />
      </keep-alive>
    </TabsLayout>
  </template>
  
  <script setup lang="ts">
  import { ref, computed } from "vue";
  import { SystemOperatorType }  from "@/enums";
  /** 相关组件 */
  import TabsLayout from "@/layout/TabsLayout.vue";
  import SystemOperator from "./components/SystemOperator/index.vue";

  const activeRoleTypeRef = ref(SystemOperatorType.SYSTEM);

  const tabsData = [
    {
      label: "平台操作员",
      key: SystemOperatorType.SYSTEM,
    },
  ];

  const pageMap = {
    [SystemOperatorType.SYSTEM]: SystemOperator,
  }

  const currentPage = computed(() => pageMap[activeRoleTypeRef.value])
  </script>
  
  <style scoped lang="less">
  @import "@/styles/default.less";
  </style>