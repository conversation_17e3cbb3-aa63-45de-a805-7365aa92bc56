<template>
  <JModal
    v-model:show="show"
    title="数据权限说明"
    positiveText="关闭"
    width="530"
    :negative-text="null"
    @positive-click="show = false"
    :closable="false"
  >
    <p>选择“全平台数据”，用户可以查看全部数据内容。</p>
    <p>选择“经销商数据”，用户只可查看账号关联的社群端经销商的数据，譬如订单。</p>
    <p>选择“群管数据”，用户只可查看账号关联的社群端群管的数据。</p>
    <p>选择“分销员数据”，用户只可查看账号关联的分销员的数据。</p>
    <p>选择“医生数据”，用户只可查看账号关联医生的数据，目前仅应用于医生后台。</p>
    <p>选择“供应商数据”，用户只可查看账号关联供应商的数据。</p>
    <p style="margin-top: 32px; margin-bottom: 12px;">
      注：用户具体能看哪些界面的数据取决于账号关联的角色。
    </p>
  </JModal>
</template>

<script lang="tsx" setup>
import { ref } from "vue";

defineOptions({ name: 'JPermissionSpec' });

/* 模态框显隐状态 */
const show = ref(false);

/* 接收父组件参数 */
const acceptParams = () => {
  show.value = true;
};

defineExpose({
  acceptParams,
});
</script>

<style lang="less" scoped></style>
