import { defHttp } from "@/services";

const enum VideoApi {
    publishVideo = '/video/add/publish',
    videoPage = '/video/page',
    updateType = '/video/update/publish',
    deleteVideo = '/video/batchDelete',
    videoDetail = '/video/get'
}

export function publishVideo (params){
    return defHttp.post({
        url:VideoApi.publishVideo,
        params:{data:params}
    })
}

//订单列表
export function videoPage(params) {
    return defHttp.post({
      url: VideoApi.videoPage,
      params
    });
}

// 更新视频发布状态
export function updateType(params:string,state:1|2){
    return defHttp.put({
        url:`${VideoApi.updateType}?videoIds=${params}&state=${state}`,
    })
}
// 删除视频
export function deleteVideo(params:string){
    return defHttp.delete({
        url:VideoApi.deleteVideo ,

        requestConfig: {
            isQueryParams: true,
          },
        params:{
            videoIds:params
        }
    })
}
// 视频详情
export function videoDetail(params:string){
    return defHttp.get({
        url:VideoApi.videoDetail,
        requestConfig: {
            isQueryParams: true,
          },
        params:{
            id:params
        }
    })
}

export interface VideoDetail {
    /**
     * 审核人账号
     */
    account?: string;
    /**
     * 审核人ID
     */
    auditorId?: number;
    /**
     * 审核状态。0=待审核；1=审核通过；2=审核不通过
     */
    auditState?: number;
    /**
     * 审核时间
     */
    auditTime?: string;
    /**
     * 评论数
     */
    commentsCount?: number;
    /**
     * 封面路径
     */
    coverImagePath: string;
    /**
     * 用户头像
     */
    customerImg?: string;
    /**
     * 用户昵称
     */
    customerName: string;
    /**
     * 视频描述
     */
    description?: string;
    /**
     * 视频时长，单位秒
     */
    duration: number;
    /**
     * 收藏数
     */
    favoritesCount?: number;
    /**
     * 是否删除。0=否；1=是
     */
    isDeleted?: number;
    /**
     * 点赞数
     */
    likesCount?: number;
    /**
     * 视频路径
     */
    path: string;
    /**
     * 关联商品ID
     */
    productId?: number;
    /**
     * 挂载视频商品信息
     */
    productVideoDTO: ProductVideoDTO;
    /**
     * 发布者用户ID
     */
    publisherId: number;
    /**
     * 转发数
     */
    sharesCount?: number;
    /**
     * 视频大小，单位KB
     */
    size: number;
    /**
     * 0=系统用户发布；1=小程序用户发布
     */
    source?: number;
    /**
     * 发布状态 0=发布中；1=发布成功；2=取消发布
     */
    state?: 1|2;
    // 
    createTime?:string;
}

/**
 * 挂载视频商品信息
 */
export interface ProductVideoDTO {
    /**
     * 可用库存。可用库存=实际库存-冻结库存
     */
    availStocks: number;
    /**
     * 药品：商品名；疗法：前端名称
     */
    frontName?: string;
    /**
     * NAME_ 药品：药品通用名；疗法：管理名称
     */
    name?: string;
    /**
     * 规格价格。单位分
     */
    price: number;
    type:number
    /**
     * 规格名称
     */
    specName?: string;
}