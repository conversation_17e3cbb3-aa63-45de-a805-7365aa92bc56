<template>
    <div class="inner-page-height">
      <FormLayout
        :isLoading="isLoading"
        :tableData="tableData"
        :tableColumns="tableColumns"
        :pagination="paginationRef"
        :isTableSelection="false"
        @paginationChange="paginationChange"
        id="subAccountPayeeManagement"
      >
        <!-- 表单 -->
        <template #searchForm>
            <n-form
              ref="formRef"
              label-placement="left"
              label-width="auto"
              :show-feedback="false"
              require-mark-placement="right-hanging"
              size="small"
              :style="{ width: '100%' }"
            >
              <n-grid cols="4 m:12 l:18 xl:24" :x-gap="32" responsive="screen">
                <!-- 入账方名称 -->
                <n-gi :span=5>
                 <n-form-item label="入账方名称">
                     <n-input 
                       v-model:value="formValue.alias"
                       placeholder="请输入入账方名称"  
                       @keyup.enter.native="formSearch" 
                       style="width:170px" 
                       clearable
                       @blur="formValue.alias=($event.target as HTMLInputElement)?.value.trim()"
                       maxlength="128" 
                     />
                 </n-form-item>
                </n-gi>
                <!-- 状态 -->
                <n-gi :span="4">
                    <n-form-item label="状态">
                        <n-select 
                         v-model:value="formValue.state" 
                         :options="SubAccountPayeeManagementStateOptions" 
                         placeholder='请选择状态'
                         style="width: 170px;" 
                         :render-option="renderOption"
                         clearable
                        />
                    </n-form-item>
                </n-gi>
              </n-grid>
            </n-form>
        </template>
        <!-- 操作项 -->
        <template #tableHeaderBtn>
          <n-button @click="refresh" class="store-button" :loading="isLoading">刷 新</n-button>
          <n-button v-if="hasFinanceAllocationAccountManagementAddAuth" type="info" @click="handleAdd('add')" class="store-button">新 增</n-button>
        </template>
      </FormLayout>
      <AddBillToInformation ref="addBillToInformationShow"/>
      <accountDetails ref="accountDetailsShow" />
      <BindCardOrVerifyModal ref="bindCardOrVerifyShow" />
    </div>
</template>
  
<script lang="tsx" setup name="subAccountPayeeManagement">
import { ref, onMounted, watch, h ,type VNode } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { allocationAccountPage,activateAccountGetDetail,allocationAccountDelete,allocationAccountReSendMsg } from "@/services/api";
import { SubAccountPayeeManagementStateOptions,SubAccountPayeeManagementStateLabels,paymentMethodsLabels } from "@/constants";
import AddBillToInformation from './components/AddBillToInformation.vue'
import accountDetails from './components/accountDetails.vue'
import BindCardOrVerifyModal from './components/BindCardOrVerifyModal.vue'
import { useMessages } from "@/hooks";
import Popconfirm from '@/components/Popconfirm/index.vue'
import { NTooltip, type SelectOption } from 'naive-ui';
import { hasFinanceAllocationAccountManagementAddAuth, hasFinanceAllocationAccountManagementDetailAuth,
  hasFinanceAllocationAccountManagementReSendMsgAuth,
  hasFinanceAllocationAccountManagementActiveAuth,
  hasFinanceAllocationAccountManagementDeleteAuth
 } from "../../../authList";
const { createMessageSuccess, createMessageError,createMessageWarning } = useMessages();

const renderOption = ({ node, option }: { node: VNode; option: SelectOption }) =>
        h(NTooltip, null, {
          trigger: () => node,
          default: () => option.label
   })

const formValue = ref({
  alias:undefined,
  state:undefined
})

/** 表格方法Hook */
const {
  isLoading,
  tableData,
  pageTableData,
  paginationRef,
  paginationChange,
} = useTableDefault({
  pageDataRequest: allocationAccountPage,
});

/* 表格列表项 */
const tableColumns = ref([
    {
        title: "入账方名称",
        key: "alias",
        width: 150,
        align: "left"
    },
    {
        title: "入账方ID",
        key: "accountCode",
        width: 180,
        align: "left"
    },
    {
        title: "状态",
        key: "state",
        width: 150,
        align: "left",
        render: (row) => {
          return (
            <n-space>
              {SubAccountPayeeManagementStateLabels[row.state] ?
               <n-ellipsis>{ SubAccountPayeeManagementStateLabels[row.state]}</n-ellipsis>
               : '-'}
              {row.state == 3 ? 
                <n-tooltip trigger="hover" v-slots={{
                  trigger: () => <n-button type="error" text>【原因】</n-button>
                }}>
                  {row.responseMessage}
                </n-tooltip>
              : null}
            </n-space>
          )
        }
    },
    {
        title: "归属商户号",
        key: "merchantId",
        width: 150,
        align: "left"
    },
    {
        title: "商户名称",
        key: "merchantName",
        width: 150,
        align: "left"
    },
    {
        title: "支付渠道",
        key: "payPlatform",
        width: 150,
        align: "left",
        render: (row) => {
          return  paymentMethodsLabels[row.payPlatform] ? paymentMethodsLabels[row.payPlatform] : '-'
        }
    },
    {
        title: "创建人",
        key: "creatName",
        width: 150,
        align: "left"
    },
    {
        title: "创建时间",
        key: "createTime",
        width: 150,
        align: "left"
    },
    {
        title: "操作",
        key: "action",
        width: 100,
        fixed: "right",
        align: "left",
        render: (row) => {
            return (
                <n-space>
                    { hasFinanceAllocationAccountManagementDetailAuth 
                      && SubAccountPayeeManagementStateLabels[row.state] != '系统开户中' ? 
                      <n-button
                        text
                        type="primary"
                        onClick={() => clickDetail(row)}
                    >
                        详情
                    </n-button> : null
                    }

                    { 
                      (SubAccountPayeeManagementStateLabels[row.state] == '待签署协议' && hasFinanceAllocationAccountManagementReSendMsgAuth) && !ZjPay(row)?
                      <Popconfirm 
                      onHandleClick={() =>clickReissueMessage(row)} 
                      loading={isReissueMessageLoading.value} 
                      buttonContent ={'重发短信'} 
                      type={'info'} 
                      disabled={isReissueMessageDisabled.value}
                      promptContent={'是否确定重发短信?'} /> : null
                    }

                    {
                      (SubAccountPayeeManagementStateLabels[row.state] == '已失效'  && hasFinanceAllocationAccountManagementActiveAuth) && !ZjPay(row)?  
                      <Popconfirm 
                      onHandleClick={() =>clickActivation(row)} 
                      loading={isActivationLoading.value} 
                      buttonContent ={'激活'} 
                      type={'info'} 
                      disabled={isActivationDisabled.value}
                      promptContent={'是否确定激活?'}/> : null
                    }

                    {
                      hasFinanceAllocationAccountManagementDeleteAuth && !ZjPay(row) ? 
                      <Popconfirm 
                      onHandleClick={() =>clickDelete(row)} 
                      loading={isDeleteLoading.value} 
                      disabled={isDeleteDisabled.value}
                      promptContent={'是否确定删除该数据?'}/> : null
                    }

                    { 
                      ZjPay(row)
                      && ( SubAccountPayeeManagementStateLabels[row.state] == '待绑卡(个人)'
                      || SubAccountPayeeManagementStateLabels[row.state] == '待绑卡(企业)') ? <n-button
                        text
                        type="primary"
                        onClick={() => bindCardOrVerify(row,'绑卡确认')}
                      >
                        绑卡
                      </n-button>  : null
                    }

                    { 
                      (ZjPay(row) && (SubAccountPayeeManagementStateLabels[row.state] == '待打款验证' || SubAccountPayeeManagementStateLabels[row.state] == '待短信验证')) ?  <n-button
                        text
                        type="primary"
                        onClick={() => bindCardOrVerify(row,'绑卡验证')}
                    >
                        验证
                    </n-button> : null
                    }

                    {/* { 
                      ZjPay(row) && SubAccountPayeeManagementStateLabels[row.state] == '系统开户中' ? <n-button
                        text
                        type="primary"
                        onClick={() => handleAdd('edit',row)}
                    >
                        编辑
                    </n-button> : null
                    } */}
                </n-space>
            );
        },
    },
]);

/** 搜索 */
const formSearch = () =>{
    tableSearch();
}

/** 表格刷新 */
function refresh(){
  tableSearch();
}

/** 获取参数 */
const getParams = () => { 
  const { alias, state} = formValue.value;
  return {
    alias,
    state,
  };
};

/* 表格搜索 */
const tableSearch = () => {
  pageTableData(getParams(), paginationRef.value);
};

/** 详情 */
const accountDetailsShow = ref()
const clickDetail = (row) =>{
    const params = {
      row:row
    }
    accountDetailsShow.value.acceptParams(params)
}

/** 删除 */
const isDeleteLoading = ref(false)
const isDeleteDisabled = ref(false)
const clickDelete = async(row) =>{
  const params = {
    data:{
      id:row.id,
      alias:row.alias,
      accountCode:row.accountCode,
      merchantId:row.merchantId,
      state:row.state,
    }
  }
  isDeleteLoading.value = true
  isDeleteDisabled.value = true
  try{
     await allocationAccountDelete(params)
     createMessageSuccess('删除入账方数据成功')
     tableSearch();
  }catch(err){
    createMessageError('删除入账方数据失败:'+ err)
  }finally{
    isDeleteLoading.value = false
    isDeleteDisabled.value = false
  }
}

/** 重发短信 */
const isReissueMessageLoading = ref(false)
const isReissueMessageDisabled = ref(false)
const clickReissueMessage = async(row) =>{
  isReissueMessageLoading.value = true
  isReissueMessageDisabled.value = true
  const params = {
    data:{
      state:row.state,
      accountCode:row.accountCode,
      merchantId:row.merchantId,
    }
  }
  try{
    await allocationAccountReSendMsg(params)
    createMessageSuccess('重发短信成功')
    tableSearch();
  }catch(err){
    createMessageError('重发短信失败:'+ err)
  }finally{
    isReissueMessageLoading.value = false
    isReissueMessageDisabled.value = false
  }
}

/** 激活 */
const isActivationLoading = ref(false)
const isActivationDisabled = ref(false)
const clickActivation = async(row) =>{
  isActivationLoading.value = true
  isActivationDisabled.value = true
  const params = {
    data:{
      state:row.state,
      accountCode:row.accountCode,
      merchantId:row.merchantId,
    }
  }
  try{
    await activateAccountGetDetail(params)
    createMessageSuccess('激活成功')
    tableSearch();
  }catch(err){
    createMessageError('激活失败:'+ err)
  }finally{
    isActivationLoading.value = false
    isActivationDisabled.value = false
  }
}

/** 新增 */
const addBillToInformationShow = ref()
const handleAdd = (type,row) =>{
    const param = {
      row,
      type,
      tableData:tableData.value,
      refresh:refresh
    }
    addBillToInformationShow.value.acceptParams(param)
}

const bindCardOrVerifyShow = ref()
/** 绑卡 或 验证 */
const bindCardOrVerify = (row,type) =>{
  const param = {
      row,
      type:type,
      refresh:refresh
    }
    bindCardOrVerifyShow.value.acceptParams(param)
}

/** 中金支付 */
const ZjPay = (row) =>{
 return paymentMethodsLabels[row.payPlatform] == '中金支付'
}

/** 监听 */
watch(() => [formValue.value.state], (newVal) => {
  if (newVal) {
    tableSearch();
  }
});

/** 组件挂载 */
onMounted(() => {
  tableSearch();
});
</script>

<style scoped lang="less"></style>
  