<template>
  <n-tooltip placement="bottom" :trigger="isShowToolTipRef ? 'hover' : 'click'" :disabled="!isShowToolTipRef">
    <template #trigger>
      <n-button :ghost="isGhostRef" :type="typeRef" :size="sizeRef" @click="openPage">{{ labelRef }}</n-button>
    </template>
    <p style="max-width: 200px">{{ valueRef }}</p>
  </n-tooltip>
</template>

<script setup lang="ts">
import { toRefs } from "vue";
const props = withDefaults(
  defineProps<{
    value: string;
    type?:
      | "default"
      | "tertiary"
      | "primary"
      | "success"
      | "info"
      | "warning"
      | "error";
    size?: "tiny" | "small" | "medium" | "large";
    isGhost?: boolean;
    label?: string;
    isShowToolTip?: boolean;
  }>(),
  {
    value: "",
    type: "info",
    isGhost: true,
    isShowToolTip: true,
    size: "tiny",
    label: "查看链接",
  }
);
const {
  value: valueRef,
  label: labelRef,
  type: typeRef,
  isGhost: isGhostRef,
  size: sizeRef,
  isShowToolTip: isShowToolTipRef,
} = toRefs(props);
function openPage() {
  window.open(valueRef.value, "_blank");
}
</script>
<style scoped lang="less"></style>
