<template>
  <JModal
      v-model:show="show"
      :title="props.title ?? '添加商品'"
      style="width: 1280px;height: 750px;"
      @after-leave="closeModal"
      @after-enter="handleAfterEnter"
  >
    <div class="wrapper">
      <div class="left" @scroll="handleScroll">
        <n-spin :show="isGetLoading" size="small" style="height: 100%;">
          <n-tree
              style="max-height: 620px"
              v-model:value="model.selectedValue"
              :data="treeDataRef"
              @update:selected-keys="handleUpdateSelectKeys"
              :selected-keys="selectedKeys"
              block-line
              :cancelable="false"
              :default-expanded-keys="defaultExpandKeys"
          />
        </n-spin>
      </div>
      <div class="right">
        <FormLayout
            :isLoading="isLoading"
            :tableData="tableData"
            :tableColumns="GoodsColumns"
            :pagination="paginationRef"
            @paginationChange="paginationChange"
            :isNeedCollapse="false"
            :is-display-index="false"
            :is-table-selection="false"
        >
          <!-- 表单 -->
          <template #searchForm>
            <n-form
                ref="formRef"
                label-placement="left"
                label-width="auto"
                :show-feedback="false"
                require-mark-placement="right-hanging"
                size="small"
                :style="{ width: '100%' }"
            >
              <!-- 商品名称 -->
              <n-form-item :span="12" label="商品名称">
                <JSearchInput v-model:value="model.name" placeholder="请输入商品名称" @search="handlerSearch"/>
              </n-form-item>
              <!-- 商品编号 -->
              <!--              <n-form-item :span="12" label="商品编号">-->
              <!--                <JSearchInput v-model:value="model.code" placeholder="请输入商品编号" @search="handlerSearch" />-->
              <!--              </n-form-item>-->
              <!-- 上架状态 -->
              <n-form-item :span="12" label="上架状态">
                <n-select
                    style="width: 160px;"
                    v-model:value="model.isPublish"
                    :options="shelfStatusOptions"
                    clearable
                />
              </n-form-item>
<!--              <n-form-item :span="12" label="药品类型">-->
<!--                <n-select-->
<!--                    style="width: 160px;"-->
<!--                    v-model:value="model.isPres"-->
<!--                    :options="drugTypeOptions"-->
<!--                    clearable-->
<!--                    disabled-->
<!--                />-->
<!--              </n-form-item>-->
            </n-form>
          </template>
          <template #tableHeaderBtn>
            <n-button @click="refresh" class="store-button">刷 新</n-button>
          </template>
          <!-- 表格底部按钮 -->
          <template #tableFooterBtn="scope">
            <!-- 批量添加 -->
            <n-popconfirm v-if="props.isBatchAdd"
                          @positive-click="handleAdd(scope.selectedList, true)">
              <template #trigger>
                <n-button ghost type="primary" size="small">批量添加</n-button>
              </template>
              此操作将添加选中的商品，是否继续？
            </n-popconfirm>
          </template>

        </FormLayout>
      </div>
    </div>
    <template #footer>
      <n-space justify="end">
        <n-button size="small" @click="closeModal">关闭</n-button>
        <n-button
            size="small" type="primary"
            @click="closeModal">
          确定
        </n-button>
      </n-space>
    </template>
  </JModal>
</template>

<script setup lang="tsx" name="GoodsModal">
import {ref, watch, computed} from "vue";
import type {TreeOption} from 'naive-ui';
import FormLayout from "@/layout/FormLayout.vue";
import {useTableDefault} from "@/hooks/useTableDefault";
import {useGetGoodsClassify} from '@/hooks/business';
import type {GoodsType, StoreModelType} from "@/enums";
import {GoodsCategoryType, PointsGoodsType} from "@/enums";
import {shelfStatusOptions, shelfStatusLabels, drugTypeOptions} from "@/constants";
import {getGoodsPage} from "@/services/api";
/** 相关组件 */
import JModal from "@/components/JModal/index.vue";
import TablePreview from "@/components/TablePreview/index.vue";
import TableTooltip from "@/components/TableTooltip/index.vue";
import {getDoctorDrugPage} from "@/services/api/doctorEndApi";
import {ApiStoreModule} from "@/typings/api";
import {isDoctorEnv} from "@/utils/envUtils";

/** props */
const props = withDefaults(defineProps<{
  value: Array<ApiStoreModule.Goods>
  type?: GoodsType; // 筛选类型
  isBatchAdd?: boolean; // 是否允许批量添加
  formularyType: number; // 药品类型 1中药处方 2西药处方
  storeModelType: StoreModelType
  title: string
  checkedSet: Set<string | number>
}>(), {
  value: () => [],
  type: GoodsCategoryType.ALL,
  isBatchAdd: false,
  formularyType: 2,
  storeModelType: 1, // 普通商城端
  checkedSet: () => new Set()
});

/** emits */
const emits = defineEmits<{
  // (e: 'update:selectedOptions', keys: Array<ApiStoreModule.Goods>): void;
  (e: 'update:value', value: Array<ApiStoreModule.Goods>): void
}>();

/** 商品分类 HOOK */
const {
  searchValue,
  selectedKeys,
  isGetLoading,
  getGoodsClassificationData,
  handleScroll,
  treeData,
  defaultExpandKeys
} = useGetGoodsClassify(props.type);

/** 分类展示类型 */
const treeDataRef = computed(() => {
  if (props.type === GoodsCategoryType.ALL) {
    return treeData.value;
  } else {
    return treeData.value.filter(item => item.type === props.type);
  }
});

/** 表格hook */
const {
  isAddLoading,
  isEditLoading,
  isLoading,
  tableData,
  paginationRef,
  pageTableData,
  deleteTableData,
  editTableData,
  addTableData,
  refreshTableData,
  paginationChange,
} = useTableDefault({
  pageDataRequest: isDoctorEnv() ? getDoctorDrugPage : getGoodsPage,
});

const initParams = {
  selectedValue: '',
  cateId: null, // 当前选中的商品分类Id
  type: props.type === GoodsCategoryType.ALL ? null : props.type, // 商品分类类型
  name: '',
  code: '',
  isPublish: null,
  /* 医生端药品商品处理 */
  // 是否是处方药 1处方药 0 OTC(非处方药)
  isPres: 1,
}
/** 参数 */
const model = ref({...initParams});

/** 弹窗显隐 */
const show = ref(false);

/* 接收父组件传过来的参数 */
const acceptParams = () => {
  show.value = true;
};

/** 树形节点选中项发生变化时的回调函数 */
const handleUpdateSelectKeys = async (keys: Array<string | number>, option, meta: {
  node: TreeOption | null,
  action: 'select' | 'unselect'
}) => {
  if (keys.length !== 0) {
    const firstOption = option[0];
    selectedKeys.value = keys;
    model.value.cateId = firstOption?.id ?? null;
    model.value.type = firstOption?.type ?? null;
  }
  await tableSearch();
  isGetLoading.value = false;
};

/** 定义一个函数来格式化价格 */
const formatPrice = (price) => `￥${(price / 100).toFixed(2)}`;

/** 表格项 */
const GoodsColumns = [
  // {
  //   type: "selection",
  //   key: "selection",
  //   fixed: "left",
  //   width: 40,
  //   minWidth: 40,
  //   align: "center",
  //   disabled: (row) => {
  //     return row?.isPoint === PointsGoodsType.isPoints;
  //   }
  // },
  {
    title: "图片",
    key: "img",
    align: "left",
    fixed: "left",
    width: 100,
    render: row => {
      if (row?.productImgDTOList?.length > 0) {
        let paths = row?.productImgDTOList.map(item => item.path);
        return <TablePreview src={paths}></TablePreview>;
      }
      return "-";
    },
  },
  {
    title: "商品",
    key: "name",
    width: 260,
    align: "left",
    render: row => {
      // 普通商品
      if (row.type == GoodsCategoryType.GENERAL) {
        let title = `${row.frontName ?? ""}`;
        return <TableTooltip row={row} nameKey="name" title={title} idKey="id"/>;
      }
      // 疗法
      if (row.type == GoodsCategoryType.THERAPY) {
        let title = `${row.frontName ?? ""}`;
        return <TableTooltip row={row} nameKey="name" title={title} idKey="id"/>;
      }
      // 药品
      if (row.type == GoodsCategoryType.DRUG && props.formularyType == 1) {
        let title = `${row.name ?? ""}`;
        return <TableTooltip row={row} nameKey="name" title={title} idKey="id"/>;
      }else if (row.type == GoodsCategoryType.DRUG) {
        let name = row.productSpecDTOList?.[0]?.name ?? "";
        let title = `[${row.frontName ?? ""}] ${row.name ?? ""} ${name}`;
        return <TableTooltip row={row} nameKey="name" title={title} idKey="id"/>;
      }
    },
  },
  {
    title: "价格",
    key: "price",
    width: 120,
    align: "left",
    render: row => {
      let price = row.productSpecDTOList?.[0]?.price ?? 0; // 确保 price 是数字
      let formattedPrice = formatPrice(price);
      if (row.type === GoodsCategoryType.GENERAL && row?.productSpecDTOList?.length > 1) {
        // 如果是普通商品并且有多个规格，找到最低价格
        const minPrice = row.productSpecDTOList.reduce((min, item) => Math.min(min, item.price), price);
        formattedPrice = `${formatPrice(minPrice)}起`;
      }
      return <span>{formattedPrice}</span>;
    },
  },
  {
    title: "库存",
    key: "availStocks",
    width: 80,
    align: "left",
    render: row => {
      // 普通商品
      if (row.type == GoodsCategoryType.GENERAL) {
        // 计算 availStocks 的总值
        const totalAvailStocks = row?.productSpecDTOList.reduce((total, item) => total + item.availStocks, 0);
        return <span>{totalAvailStocks}</span>;
      }
      let availStocks = row.productSpecDTOList?.[0]?.availStocks ?? 0; // 确保 price 是数字
      return <span>{availStocks}</span>;
    },
  },
  {
    title: "上架",
    key: "isPublish",
    width: 60,
    align: "left",
    render: row => {
      return (
          <n-tag bordered={false} size="small" type={row.isPublish === 1 ? "success" : "error"}>
            {shelfStatusLabels[row.isPublish]}
          </n-tag>
      );
    },
  },
  {
    title: "所属分类",
    key: "cateName",
    width: 100,
    align: "left",
  },
  {
    title: "操作",
    key: "action",
    width: 80,
    fixed: "right",
    align: "left",
    render: row => {
      return (
          <n-space>
            {/* 添加 */}
            <n-button
                text
                type="primary"
                disabled={canAddDrug(row)}
                onClick={() => handleAdd([row])}>
              {props.checkedSet.has(row.id) ? '已添加' : '添加'}
            </n-button>
          </n-space>
      );
    },
  },
];

// 是否能添加该药品
function canAddDrug(row) {
  // 普通商品
  if (row.type == GoodsCategoryType.GENERAL) {
    // 计算 availStocks 的总值
    const totalAvailStocks = row?.productSpecDTOList.reduce((total, item) => total + item.availStocks, 0);
    return <span>{totalAvailStocks}</span>;
  }
  let availStocks = row.productSpecDTOList?.[0]?.availStocks ?? 0; // 确保 price 是数字
  return row?.isPublish !== 1 || availStocks == 0
}

/** 添加数据或删除数据 */
function handleAdd(goodsList: Array<ApiStoreModule.Goods>, isBatch: boolean = false) {
  // 生成待删除的 ID 集合
  const deleteIds = new Set();
  const addArr = []
  goodsList.forEach(item => {
    if (props.checkedSet.has(item.id)) {
      deleteIds.add(item.id)
    } else {
      addArr.push(item)
    }
  })

// 合并过滤后的原数据和新增数据
  let tempData = props.value.filter(item => !deleteIds.has(item.id))
  tempData.push(...addArr)
  // 触发更新
  emits("update:value", tempData);
  if (isBatch) {
    tableSearch();
  }
}

/* 关闭弹窗之后 */
const closeModal = () => {
  show.value = false
  treeData.value = [];
  tableData.value = [];
  model.value = {...initParams}
};

/** 搜索 */
const handlerSearch = () => {
  tableSearch();
};

/** 表格刷新 */
function refresh() {
  tableSearch();
}

/** 获取参数 */
const getParams = () => {
  const {name, isPublish, code, cateId, type, isPres} = model.value;
  if (props.formularyType == 1) {
    return {
      name,
      isPublish,
      code,
      type,
      cateId,
      formularyType: 1,
      medicineType: 1
    };
  }else {
    return {
      name,
      isPublish,
      code,
      type,
      cateId,
      isPres
    };
  }
};

/* 表格搜索 */
const tableSearch = async () => {
  console.log(getParams());
  console.log(model.value);
  await pageTableData(getParams(), paginationRef.value);
};

/** 弹窗打开之后回调 */
async function handleAfterEnter() {
  await getGoodsClassificationData(() => {
    tableSearch();
  }, null, 1);
}

/** 监听 */
watch(() => [model.value.isPublish], (newVal) => {
  if (newVal) {
    tableSearch();
  }
});

defineExpose({
  acceptParams,
  tableSearch
});
</script>

<style scoped lang="less">
@import "@/styles/scrollbar.less";

.wrapper {
  width: 100%;
  height: 100%;
  display: flex;

  .left {
    width: 240px;
    height: 100%;
    padding: 12px;
    margin-right: 4px;
    border: 1px solid #eeeeee;
    overflow-y: auto;
    .scrollbar();
  }

  .right {
    width: calc(100% - 200px);
  }
}
</style>
