import { addProductSpecId } from "@/services/api"
import { useMessages } from '@/hooks';
const { createMessageSuccess, createMessageError } = useMessages();

export default function useCompatibility() {
    /** 处理旧商品数据 兼容多规格 */
    const handleOldData = async (data,productId) => {
        console.log('旧数据');
        
        const productAttributeValueOld = [];
        try {
        for (let index = 0; index < data.length; index++) {
            const element = data[index];
            const newId = await addProductSpecId();
            productAttributeValueOld.push({
                attributeName: '规格',
                attributeValue: element.name,
                id: newId,
                level: 1,
                productId
            })
            element.firstAttrId = newId;
        }
        
        return {
            productAttributeValueOld,
        };
        } catch (error) {
            createMessageError('处理旧商品数据失败：请关闭重试' );
        }
        
    };

    return {
        handleOldData,
    };
}
