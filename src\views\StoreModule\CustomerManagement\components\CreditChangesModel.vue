<template>
    <JModal
      v-model:show="show"
      width="680"
      title="增减积分"
      @after-leave="closeModal"
          @positive-click="_save"
          :positiveButtonProps="{
          loading: isLoading
      }"
    >
      <n-form
        ref="formRef"
        :rules="rules"
        :model="model"
        label-width="auto"
        label-placement="left"
        require-mark-placement="right-hanging"
        :style="{
            width: '100%',
          }"
      >
        <n-grid :cols="8" :x-gap="24">
          <n-form-item-gi :span="6" label="增减积分" path="changeType">
             <n-select v-model:value="model.changeType" :options="IntegralVariationOptions" placeholder="请选择增减积分"/>
          </n-form-item-gi>
          <n-form-item-gi :span="8" label="原因" path="reason">
            <n-input maxlength="15" v-model:value="model.reason" @blur="model.reason = ($event.target as HTMLInputElement)?.value.trim()" show-count clearable placeholder="请输入原因" />
          </n-form-item-gi>
          <n-form-item-gi :span="8" label="积分数量" path="changeValue">
            <n-input-number v-model:value="model.changeValue" :show-button="false"  @blur="handleNumberPoints" style="width: 100%;" placeholder="请输入积分数量"/>
          </n-form-item-gi>
        </n-grid>
      </n-form>
    </JModal>
  </template>
  
<script setup lang="ts" name="AddorEditVideo">
import { ref, watch } from "vue";
import { useMessages } from "@/hooks";
import {IntegralVariationOptions} from "@/constants";
import { pointRecordAddOrDecPoints} from "@/services/api";
const initParams = {
  changeValue:null,
  reason:'',
  changeType:11
};
const model = ref({ ...initParams });
export interface CreditChangesProps { 
    row?: any;
    refresh?: () => void; // 刷新表格
}
/* 提示信息 */
const message = useMessages();
/* 模态框显隐状态 */
const show = ref(false);

/* 表单规则 */
const rules = {
  changeType:{
    required: true,
    trigger: ["blur", "change"],
    message: "请选择增减积分",
    validator: ()=>{
      return model.value.changeType != null;
    }
  },
  changeValue:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入积分数量",
    validator: ()=>{
      return model.value.changeValue != null;
    }
  },
  reason:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入原因",
    validator: ()=>{
      return model.value.reason != '';
    }
  },
};
const acceptParams = (params) => {
  parameter.value = params
  show.value = true
};
/* 表单实例 */
const formRef = ref(null);

/* 表单参数初始化 */
/* 清空表单 */
const formDataReset = () => {
  model.value = { ...initParams };
};

const parameter = ref<CreditChangesProps>({
});

/* 关闭弹窗 */
const closeModal = () => {
  formDataReset();
  // 弹窗取消
  show.value = false;
};

/* 确认--保存 */
const isLoading = ref(false);
const _save = (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors && !isLoading.value) {
      isLoading.value = true;
      const params = {
          data:{
            unionId:parameter.value.row.unionId,
            customerId:parameter.value.row.id,
            changeType:model.value.changeType,
            changeValue:model.value.changeValue,
            reason:model.value.reason
          }
      }
      try {
        await pointRecordAddOrDecPoints(params)
        // 刷新表格数据
        parameter.value.refresh();
        closeModal()
        message.createMessageSuccess('增减积分成功');
      } catch (e) {
        message.createMessageError(`增减积分失败： ${e}`);
      }finally{
        isLoading.value = false;
      }
    }
  });
};

/** 判断的积分输入内容是否大于0 */
const handleNumberPoints = () =>{
    if (model.value.changeValue !== null) {
        // 四舍五入到整数
        model.value.changeValue = Math.round(model.value.changeValue);
    }
    if(model.value.changeValue != null && model.value.changeValue <= 0 ){
        model.value.changeValue = null
        message.createMessageWarning('只能录入大于0的正数');
    }
}
defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less">
::v-deep .n-form-item-label{
  width: 70px !important;
}
</style>
  