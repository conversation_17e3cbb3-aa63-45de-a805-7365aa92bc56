<template>
  <JModal
    v-model:show="modalVisible"
    title="积分明细"
    width="1200"
    height="600"
    @after-leave="closeModal"
    :positive-text="null" 
    :negative-text="null"
  >
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :isNeedCollapse="false"
      :isTableSelection="false"
      :pagination="paginationRef"
      :isDisplayIndex="false"
      @paginationChange="paginationChange"
    >
      <template #searchForm>
        <n-form
          ref="formRef"
          :model="model"
          :show-feedback="false"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          size="small"
          :style="{ width: '100%' }"
        >
          <n-form-item label="类别">
            <n-select
              v-model:value="model.category"
              :options="categoryOptions"
              placeholder="请选择类别"
              style="width: 170px;"
              clearable
            />
          </n-form-item>
          <n-form-item label="渠道">
            <n-select
              v-model:value="model.channel"
              @click="handleSearch"
              clearable
              placeholder="请选择渠道"
              style="width: 170px;"
              :options="model.optionsChannel"
            />
          </n-form-item>
        </n-form>
      </template>
    </FormLayout>
  </JModal>
</template>

<script setup lang="tsx">
import { ref, watch } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { pointRecordPagePointsRecord, pointChannelListCsChannel } from '@/services/api';
import { channelLabels } from "@/constants";
import { sourceCategoryOptions } from "@/constants";

interface PointsDetails {
  Operator?:boolean,
};
/** Props */
const props = withDefaults(defineProps<PointsDetails>(), {
   Operator:null
});

/* 表格方法Hook */
const {
  isLoading,
  tableData,
  paginationRef,
  pageTableData,
  paginationChange,
} = useTableDefault({
  pageDataRequest: pointRecordPagePointsRecord,
});

/* 初始化参数 */
const initParams = {
  category: null,
  channel: null,
  optionsChannel: []
};
const model = ref({ ...initParams });

const categoryOptions = ref([
  {
    label: "支出",
    value: -1,
  },
  {
    label: "收入",
    value: 1,
  },
])


/** 获取搜索参数 */
const getParams = () => {
  const params = {
    customerId: drawerProps.value.customerId,
    channelId: model.value.channel,
    io: model.value.category
  };
  return params;
};

/* 表格搜索 */
const tableSearch = async () => {
  await pageTableData(getParams(), paginationRef.value);
};

/** 弹窗显隐 */
const modalVisible = ref(false);
const drawerProps = ref()
const acceptParams = (params) => {
  drawerProps.value = params
  modalVisible.value = true;
  tableSearch()
};

/* 表格项 */
const tableColumns = [
  {
    title: "类别",
    key: "io",
    align: "left",
    fixed: "left",
    width: 100,
    render: (row) => {
      let ioValue = null
      if (row.io == -1) {
        ioValue = '支出'
      } else if (row.io == 0) {
        ioValue = '平衡'
      } else if (row.io == 1) {
        ioValue = '收入'
      } else {
        ioValue = '-'
      }
      return ioValue;
    },
  },
  {
    title: "来源",
    key: "sourceDetail",
    align: "left",
    fixed: "left",
    width: 300,
    render: (row) => {
      if((row.source == sourceCategoryOptions[1].value || row.source == sourceCategoryOptions[2].value) && row.orderCode){
        return <table-tooltip row={row} nameKey="name" title={row.sourceDetail} idKey="orderCode" displayId={true} displayIdKey={true}/>;
      }else{
        return row.sourceDetail
      }
    }
  },
  {
    title: "渠道",
    key: "gmMgrName",
    align: "left",
    width: 100,
    render: (row) => {
      return channelLabels[row.channel];
    },
  },
  {
    title: "操作员",
    key: "operatorNickname",
    align: "left",
    width: 100,
  },
  {
    title: "时间",
    key: "createTime",
    align: "left",
    width: 100,
  },
  {
    title: "总积分",
    key: "points",
    align: "left",
    width: 100,
  },
];

/* 关闭弹窗之后 */
const closeModal = () => {
  tableData.value = []
  model.value = { ...initParams };
};

const handleSearch = async () => {
  if (model.value.optionsChannel.length == 0) {
    const res = await pointChannelListCsChannel({ unionId: drawerProps.value.unionId })
    handleData(res)
  }
}

const handleData = (data) => {
  model.value.optionsChannel = data.map(item => {
    return { label: item.channelName, value: item.channelId };
  });
}

watch([
  () => model.value.category,
  () => model.value.channel,
],
  () => {
    tableSearch()
  });

defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less">
.title {
  font-size: 20px;
  font-weight: 700;
}
</style>
