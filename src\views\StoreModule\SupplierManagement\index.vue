<template>
  <div class="wrapper inner-page-height">
      <FormLayout
        :isLoading="isLoading"
        :tableData="tableData"
        :tableColumns="tableColumns"
        :pagination="paginationRef"
        @paginationChange="paginationChange"
        :isNeedCollapse="false"
        :isDisplayIndex="false"
        @table-sorter-change="tableSorterChange"
      >
        <!-- 表单 -->
        <template #searchForm>
          <n-form
            ref="formRef"
            label-placement="left"
            label-width="auto"
            :show-feedback="false"
            require-mark-placement="right-hanging"
            size="small"
            :style="{ width: '100%' }"
          >
            <n-form-item>
              <n-input-group>
                <n-select v-model:value="model.searchType" placeholder="请选择" :options="supplierOptions" style="width: 135px;"/>
                <JSearchInput :inputNumber="model.searchType == 'id'" v-model:value="model.searchValue" placeholder="请输入关键字" @search="handlerSearch" :maxlength="model.searchType == 'id'?19:30" :width="240"/>
              </n-input-group>
            </n-form-item>
            <n-form-item :span="12" label="状态">
                <n-select
                  style="width: 160px;"
                  v-model:value="model.status"
                  :options="SupplierStateOptions"
                  clearable
                />
              </n-form-item>
          </n-form>
        </template>
    
        <template #tableHeaderBtn>
          <n-button @click="refresh" :loading="isLoading" class="store-button">刷 新</n-button>
          <JAddButton v-if="hasSupplierManagementAddAuth" type="primary" @click="handleSetSupplier(true)">新增供应商</JAddButton>
        </template>
        <template #tableFooterBtn="scoped">
            <n-popconfirm
            v-if="hasSupplierManagementDeleteAuth"
            @positive-click="()=>handlerBatchDelete(scoped.selectedListIds)"
            :positive-button-props="{
              loading: deleteLoading
            }"
          >
            <template #trigger>
              <n-button ghost type="error" size="small">批量删除</n-button>
            </template>
            此操作将删除选中的供应商，是否继续？
          </n-popconfirm>
        </template>
      </FormLayout>
      <SupplierSetting v-model:show="isShow" :Add_active="isAddActive" :row="row" @refresh="refresh" />
  </div>
</template>

<script lang="tsx" setup name="DoctorManagement">
import { onMounted, ref, watch } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import SupplierSetting from "./components/SupplierSetting.vue";
import Popconfirm from "@/components/Popconfirm/index.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { pageSupplier, batchDeleteSupplier } from "@/services/api";
import { createsupplierColumns } from "./TableColumns";
import { useMessages } from '@/hooks';
import { SupplierStateOptions } from "@/constants";
import { hasSupplierManagementAddAuth,hasSupplierManagementEditAuth,hasSupplierManagementDeleteAuth } from "./authList";
const { createMessageSuccess, createMessageError } = useMessages();

/** 表格hook */
const {
  isAddLoading,
  isEditLoading,
  isLoading,
  tableData,
  paginationRef,
  pageTableData,
  deleteTableData,
  editTableData,
  addTableData,
  refreshTableData,
  paginationChange,
} = useTableDefault({
  pageDataRequest: pageSupplier,
});

/** 供应商 */
const supplierOptions = [
  {
    label: "供应商名称",
    value: 'supplierName',
  },
  {
    label: "供应商ID",
    value: 'id',
  },
];

/* 表格列表项 */
const tableColumns = createsupplierColumns({
  operation(row) {
    return (
        <n-space style="padding: 5px 0;">
          {
            hasSupplierManagementEditAuth ? <n-button text type="primary" onClick={() => handleSetSupplier(false, row)}>编辑</n-button> : null
          }
          {
            hasSupplierManagementDeleteAuth ? <Popconfirm 
              onHandleClick={() =>handlerBatchDelete(row.id)} 
              loading={deleteLoading.value} 
              buttonContent ={'删除'} 
              type={'error'} 
              promptContent={'你确定要删除该供应商吗?'}/> : null
          }
        </n-space>
      );
    }
})

const isShow = ref(false);
const isAddActive = ref(true);

const row = ref(null);
const handleSetSupplier = (isAdd:boolean, rowData?:any) => {
  isShow.value = true;
  isAddActive.value = isAdd;
  row.value = rowData;
};

/** 删除 批量删除 */
const deleteLoading = ref(false);
const handlerBatchDelete = async(id:string[] | string) => {
  try {
    deleteLoading.value = true
    let ids = Array.isArray(id)? id.join(','): id
    await batchDeleteSupplier({ids});
    createMessageSuccess('删除成功');
    refresh();
  } catch (err) {
    createMessageError(err);
  } finally {
    deleteLoading.value = false;
  }
};

/** 参数 */
const model = ref({
  searchType: 'supplierName',
  searchValue: '',
  status:null
});

/** 获取参数 */
const getParams = () => {
  const { searchType,searchValue,status } = model.value;
  const params = {
    status,
    [searchType]:searchValue
  }
  return params;
};

/** 搜索 */
const handlerSearch = () => {
  tableSearch();
};

/** 表格刷新 */
function refresh(){
  tableSearch();
}

/* 表格搜索 */
const tableSearch = () => {
  pageTableData(getParams(), paginationRef.value);
};

/** 表格排序 */
const tableSorterChange = (info)=>{
  const { sort, sortAsc } = info;
    if (sort) {
        tableData.value = [...tableData.value].sort((a, b) => {
            const valA = a[sort];
            const valB = b[sort];
            if (sort.toLowerCase().includes('time')) {
                if (sortAsc === 'ascend') {
                    return valA.localeCompare(valB);
                } else {
                    return valB.localeCompare(valA);
                }
            }
            return 0;
        });
    }
}
/** 组件挂载 */
onMounted(() => {
  tableSearch();
});
watch(()=>model.value.status,(val)=>{
  tableSearch();
})
watch(()=>model.value.searchType,(val)=>{
  model.value.searchValue = ''
})
</script>

<style lang="less" scoped>
@import "@/styles/defaultVar.less";
</style>