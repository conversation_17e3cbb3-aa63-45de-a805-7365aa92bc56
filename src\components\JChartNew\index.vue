<template>
  <div class="echart-container">
    <div ref="echartRef" :style="{ width: containerWidth, height: height+'px' }"></div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, onUnmounted, ref, watch  } from 'vue';
import * as echarts from 'echarts';

export default defineComponent({
  name: 'Echart',
  props: {
    width: {
      type: Number,
      required: true,
    },
    height: {
      type: Number,
      required: true,
    },
    option: {
      type: Object as () => echarts.EChartsOption,
      required: true,
    },
  },
  setup(props) {
    const echartRef = ref<HTMLDivElement | null>(null);

    let containerWidth = ref<number|string>(`${props.width}px`);
    let containerHeight = ref<number|string>(`${props.height}px`);

    let chart: echarts.ECharts | null = null;

    const resizeChart = () => {
      if (chart) {
        chart.resize({
          width:props.width,
          height:props.height
         
        });
        // 获取屏幕宽高并重新计算图表的宽高
        // const screenWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
        // const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
        
        // containerWidth.value = Math.min(screenWidth, 500);
        // containerHeight.value = Math.min(screenHeight, 300);
      }
    };
    function updateChart() {
      // 清除图表数据
      chart.clear();

      // 设置新的数据和配置
      if (chart && props.option) {
        
        
        chart.setOption(props.option);
        resizeChart();
      }
    }
    onMounted(() => {
      chart = echarts.init(echartRef.value!);
      chart.setOption(props.option);

      window.addEventListener('resize', resizeChart);
    });

    onUnmounted(() => {
      if (chart) {
        chart.dispose();
        chart = null;
      }

      window.removeEventListener('resize', resizeChart);
    });

    watch(()=>props ,() => {
      updateChart()
    },{deep: true})

    watch(()=>props.height, () => {
      
      resizeChart()
    },{deep: true})

    return {
      echartRef,
      containerWidth,
      containerHeight,
    };
  },
});
</script>

<style scoped>
.echart-container {
  width: 100%;
  height: 100%;
}
</style>
