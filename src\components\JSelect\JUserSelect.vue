<template>
  <JSelect 
    :options="userDataList" 
    :render-label="renderLabel" 
    :render-tag="renderSingleSelectTag" 
    clearable 
    filterable 
    v-bind="$attrs"
    :onFocus="handlerFocus"
    value-field="id"
    :isShowTooltip="false"
    :on-search="handlerSearch"
    :loading="isLoading" 
    @scroll="handleScroll"
    remote
  />
</template>

<script setup lang="ts">
import { ref, reactive, toRefs, onMounted, computed} from 'vue';
import { defineComponent, h } from 'vue'
import type { SelectRenderLabel, SelectRenderTag } from 'naive-ui'
import { NAvatar, NTag, NText, NTooltip } from 'naive-ui';
import { customerEntityPage } from "@/services/api/customerApi";
import { color } from 'echarts';
import { _debounce } from "@/utils";
import EmptyImg from '@/assets/image/system/avatar.png';


// const props = withDefaults(defineProps<{
//     value:string;
// }>(),{
//     value:''
// });
// const emits = defineEmits<{
//     (e:'update:value',value:string) :void;
// }>()

// const selectValue = computed({
//     get(){
//         return props.value
//     },
//     set(value){
//         emits('update:value',value)
//     }
// })

// 获取焦点
const handlerFocus = ()=>{
    pageVO.current = 1
    getUserList(true);
}
const _searchValue = ref('');       // 缓存搜索数据
const handlerSearch = (value:string) =>{
    console.log(value,'搜索');
    _searchValue.value = value
    debunceGetlist(true,value);
}

const pageVO = reactive({
    current:1,
    size:100
})
const recordsTotal = ref(0)
// 加载数据
const isLoading = ref<boolean>(false)
/**
 * @param isClear 是否清空现有数据(筛选时传入)
 * @param searchValue 筛选的值
 */
const getUserList = (isClear:boolean=false,searchValue:string='')=>{
    
    isLoading.value = true
    const params = {
        data:{
            nickName:searchValue
        },
        pageVO
    }
    customerEntityPage(params).then((res)=>{
        console.log(res,'res');
        if (isClear) {
            userDataList.value = res.records
        }else{
            userDataList.value.push(...res.records)
        }
        recordsTotal.value = res.total
    }).catch(err=>{
        console.log(err,'err');
    }).finally(()=>{
        isLoading.value = false
    })
}

const debunceGetlist = _debounce(getUserList,500)

const userDataList = ref([])

const renderSingleSelectTag = ({ option }) => {
    return option.nickname
}

function handleScroll(e){
    
    const currentTarget = e.currentTarget as HTMLElement
    if (
      currentTarget.scrollTop + currentTarget.offsetHeight >=
      currentTarget.scrollHeight - 20
    ) {
        if(pageVO.current * pageVO.size < recordsTotal.value){
            pageVO.current ++ ;
            debunceGetlist()
        }
    }
}


const renderLabel: SelectRenderLabel = (option) => {

    const renderSelectItem = () => h(
        'div',
        {
            style: {
                display: 'flex',
                alignItems: 'center',
            }
        },
        [
            h(NAvatar,{
                src: option.img || EmptyImg,
                round: true,
                size: 'small',
                style:{
                    flexShrink:0 ,
                }
            }),
            h(
                'div',
                {
                    style: {
                        marginLeft: '12px',
                        padding: '4px 0',
                        overflow: 'hidden',

                    }
                },
                [
                    h('div', null, [option.nickname as string]),
                    h('div', {style:{color:'#999999'}}, {default:()=>option.mobile || '-'}),
                    h(
                        NText,
                        { depth: 3, tag: 'div' ,style:{
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                        } },
                        {
                            default: () => `用户ID:${option.id}`
                        }
                    )
                ]
            )
        ]
    )
    return h(NTooltip,{duration:0},{
        trigger:renderSelectItem,
        default:renderSelectItem,
    })
}

</script>
<style scoped lang="less">
</style>