<template>
  <n-space :size="5">
    <span
      v-if="props.nameKey"
      :title=" titleRef ? `${titleRef} ${displayIdKey ? '' : row[idKey]}` : `${row[nameKey]}${row[idKey]}`"
      class="text-ellipsis"
    >
      {{ titleRef ? titleRef : row[nameKey] || '-' }}
    </span>
    <CopyOptBtn :value="row[idKey]" :label="displayId ? ('订单编号' + row[idKey]) : 'ID'" :displayId="displayId"/>
  </n-space>
</template>

<script lang="ts" setup name="tableTooltip">
import { toRefs } from 'vue';
import CopyOptBtn from "@/components/CopyOptBtn/index.vue";

interface TableTooltipProps {
  row: any; // 行数据
  nameKey: string; // 显示name的key
  idKey: string; // 显示id的key
  title?: string; // 显示name
  labelText?:string;
  displayId?: boolean; // 显示Id
  displayIdKey?: boolean; // 显示IdKey true为不显示 false为显示
}

const props = withDefaults(defineProps<TableTooltipProps>(),{
  labelText:'ID'
})
const {
  row,
  nameKey,
  idKey,
  title: titleRef,
  displayId, 
  displayIdKey,
} = toRefs(props);
</script>

<style lang="less" scoped></style>
