import { StoreBasicConfigAuth } from "@/enums/authKeys";
import { hasAuth } from "@/utils/auth/authUtils";


/** 新增操作员 */
export const hasAddOperatorsAuth= function(){
    return hasAuth(StoreBasicConfigAuth.storeBasicConfigOperatorsNewoperator.key);
}()

/** 编辑操作员 */
export const hasEditOperatorsAuth = function(){
    return hasAuth(StoreBasicConfigAuth.storeBasicConfigOperatorsEditoperator.key);
}()

/** 重置密码 */
export const hasResetpasswordAuth = function(){
    return hasAuth(StoreBasicConfigAuth.storeBasicConfigOperatorsResetpassword.key);
}()

/** 删除操作员 */
export const hasDeleteoperatorAuth = function(){
    return hasAuth(StoreBasicConfigAuth.storeBasicConfigOperatorsDeleteoperator.key);
}()

