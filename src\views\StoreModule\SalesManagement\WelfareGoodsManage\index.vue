<template>
  <div class="welfare-goods-wrapper">
    <TreeContentLayout>
      <template #tree-content>
        <div class="label-container inner-page-height">
          <!-- Header -->
          <div class="header">
            <NSpace justify="space-between" style="flex-wrap: nowrap; margin-bottom: 12px">
              <NButton size="small" @click="refresh" class="store-button">刷 新</NButton>
              <!-- 新建福利品分类 -->
              <NButton v-if="hasWelfareGoodsClassifyAddAuth" size="small" type="primary" @click="handleClassify(GoodsClassifyTypeEnum.ADD)">新建分类</NButton>
            </NSpace>
            <!-- 福利品分类名称 -->
            <JSearchInput
              width="326"
              v-model:value="searchValue"
              placeholder="请输入分类名称"
              @search="refresh"
              size="small"
            />
          </div>
          <!-- 标签列表 -->
          <div class="classify-container" @scroll="handleScroll">
            <NSpin size="small" :show="isGetLoading" style="height: 100%">
              <WelfareGoodsClassifyTree
                v-model:value="model.selectedValue"
                :tree-data="treeData"
                :menu-options="options"
                @menu-select="handleMenuSelect"
                @update:selected-keys="handleUpdateSelectKeys"
                :selected-keys="selectedKeys"
                :defaultExpandKeys="defaultExpandKeys"
              />
            </NSpin>
          </div>
        </div>
      </template>
      <template #default>
        <div class="classify-container">
          <!-- 福利品表格内容 -->
          <WelfareGoodsPage 
            :cateId="model.cateId" 
            @refresh="handleAfterSuccessfulRequest"
          />
        </div>
      </template>
    </TreeContentLayout>
    <!-- 新建福利品分类管理 -->
    <WelfareGoodsSetting ref="goodsClassifySettingRef" @refresh="refresh" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import type { TreeOption } from 'naive-ui';
import { useMessages } from "@/hooks";
import { useGetWelfareTree } from "./hooks";
import { GoodsClassifyTypeEnum } from "./type";
import { hasWelfareGoodsClassifyAddAuth,hasWelfareGoodsClassifyDeleteAuth,hasWelfareGoodsClassifyEditAuth } from "../authList";
/** 相关组件 */
import TreeContentLayout from "@/components/TreeContentLayout/index.vue";
import WelfareGoodsClassifyTree from './components/WelfareGoodsClassifyTree.vue';
import WelfareGoodsSetting from './components/WelfareGoodsSetting.vue';
import WelfareGoodsPage from './components/WelfareGoodsPage.vue';

const { createMessageSuccess, createMessageError } = useMessages();

/** 商品分类 hook */
const {
  model,
  searchValue,
  selectedKeys,
  isGetLoading,
  getGoodsClassificationData,
  handleScroll,
  refresh,
  treeData,
  defaultExpandKeys
} = useGetWelfareTree();

/** 右键选项 */
const options =  [
  {
    label: '删除分类',
    key: 'delete',
    show: hasWelfareGoodsClassifyDeleteAuth
  },
  {
    label: '修改分类',
    key: 'edit',
    show: hasWelfareGoodsClassifyEditAuth
  }
];

/** 右键操作 */
const handleMenuSelect = (key: string | number, option: TreeOption) => {
  // 编辑操作
  if (key === 'edit' && !option?.parentId) {
    handleClassify(GoodsClassifyTypeEnum.EDIT, option);
  }
  // 删除操作
  if (key === 'delete') {
    handleClassify(GoodsClassifyTypeEnum.DELETE, option);
  }
};

/** 树形节点选中项发生变化时的回调函数 */
const handleUpdateSelectKeys = (keys: Array<string | number>, option: Array<TreeOption & Partial<{
  id: string;
  name: string;
  imageUrl: string;
  sort: number;
}> | null>, meta: { node: TreeOption | null, action: 'select' | 'unselect' }) => {
  // console.log("---------------树形节点点击 start--------------");
  // console.log("keys", keys);
  // console.log("option", option);
  // console.log("meta", meta);
  // console.log("---------------树形节点点击 end--------------");
  if (keys.length !== 0) {
    const firstOption = option[0];
    selectedKeys.value = keys;
    model.value.cateId = firstOption?.id ?? null;

    isGetLoading.value = true;
}
};

/** 福利品分类相关 */
const goodsClassifySettingRef = ref<InstanceType<typeof WelfareGoodsSetting> | null>(null)
const handleClassify = (type: GoodsClassifyTypeEnum, row?: any) => {
  goodsClassifySettingRef.value?.acceptParams(type, row);
};

/** 福利商品请求成功回调 */
const handleAfterSuccessfulRequest = () => {
  isGetLoading.value = false;
};

/** 组件挂载 */
onMounted(() => {
  getGoodsClassificationData();
});
</script>

<style lang="less" scoped>
@import "@/styles/scrollbar.less";
.welfare-goods-wrapper{
  height: 100%;
  .label-container {
    width: 100%;
    .header {
      padding: 12px;
    }
    .classify-container {
      height: calc(100% - 102px);
      overflow-y: auto;
      .scrollbar();
    }
  }
  .classify-container {
    width: 100%;
    height: 100%;
  }
}
</style>
