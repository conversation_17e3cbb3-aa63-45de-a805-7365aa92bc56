<template>
	<JModal
		v-model:show="show"
		:title="title"
		width="680"
		@after-leave="closeModal"
	    @positive-click="handleSave"
	    :positiveButtonProps="{
	    	loading: isLoading
	    }"
	>
		<n-form
			ref="formRef"
			:model="model"
			:rules="rules"
			label-width="auto"
			label-placement="left"
			require-mark-placement="right-hanging"
			:style="{
				width: '100%',
			}"
		>
			<n-grid :cols="12" :x-gap="12" responsive="self">
				<!-- 提示 -->
				<n-gi :span="12" style="margin-bottom: 12px;">
				    <span>保存后展示在积分商城首页，最多创建10个标签。</span>
				</n-gi>
				<!-- 标签名称 -->
				<n-form-item-gi :span="12" label="标签名称" path="siftName">
					<n-input
						v-model:value="model.siftName"
						@blur="model.siftName=($event.target as HTMLInputElement)?.value.trim()"
						placeholder="请输入标签名称（15字符）"
						:maxlength="15"
					/>
				</n-form-item-gi>
                <!-- 排序号 -->
                <n-form-item-gi :span="12" label="排序号" path="sort">
					<n-input-number
						v-model:value="model.sort"
						placeholder="序号越大排位越前,最多3位数"
						:max="999"
                        :min="0"
                        clearable
                        :show-button="false"
                        style="width: 100%;"
						:precision="0"
					/>
				</n-form-item-gi>
                <!-- 积分筛选 -->
                <n-form-item-gi :span="12" label="积分筛选">
					<n-input
                        pair
						v-model:value="model.points"
                        separator="-"
                        :placeholder="['最小积分', '最大积分']"
                        clearable
                    />
                </n-form-item-gi>
			</n-grid>
		</n-form>
	</JModal>
</template>

<script setup lang="ts" name="AddIntegralGoodsLabel">
import { ref, computed } from "vue";
import { useMessages, useBoolean, useLoading } from '@/hooks';
import type { TreeOption, FormRules } from 'naive-ui';
import { addPointSift, updatePointSift } from "@/services/api";
import { isObject, deepClone } from "@/utils";
import { GoodsCategoryType } from "@/enums";
import type { GoodsType } from "@/enums";

const { createMessageSuccess, createMessageError } = useMessages();

interface ModalProps {
	type: 'add' | 'edit';
	row?: TreeOption & { isMenu?: boolean } & Partial<ApiSalesManagement.PointSift>; 
};

/** emits */
const emits = defineEmits<{
  (e: "afterSuccess", value: string, type: GoodsType): void;
}>();

/** 标题 */
const title = computed(() => {
  const titleMap: Record<'add' | 'edit', string> = {
    add: '新建标签',
    edit: '编辑标签',
  };
  return titleMap[modalProps.value.type];
});

/** 显隐 */
const { bool: show, setTrue, setFalse } = useBoolean();
const modalProps = ref<ModalProps>({
	type: 'add',
});

/* 接收父组件传过来的参数 */
const acceptParams = (params: ModalProps) => {
	modalProps.value = params;
	let row = params.row;
	// 处理行数据
	if (isObject(row) && Object.keys(row).length !== 0) {
		let minPoints = row.minPoints ? String(row.minPoints) : '0';
		let maxPoints = row.maxPoints ? String(row.maxPoints) : '0';
		Object.assign(model.value, {
			id: row.id ?? null,
			siftName: row.siftName ?? '',
            sort: row.sort ?? null,
			points: [ minPoints, maxPoints],
		});
	}
	setTrue();
};

/* 表单参数初始化 */
const initParams = {
	id: null,
	siftName: "",
	points: [],
    sort: null,
};
const model = ref(deepClone(initParams));

/* 表单实例 */
const formRef = ref();

/* 表单规则 */
const rules: FormRules = {
	siftName: {
      required: true,
      trigger: ['blur', 'change'],
      message: '请输入商品标签名称',
    },
	sort: {
	  type: 'number',
      required: true,
      trigger: ['blur', 'change'],
      message: '请输入排序号',
    },
};

/* 清空表单 */
const formDataReset = () => {
	model.value = deepClone(initParams);
};

/* 关闭弹窗之后 */
const closeModal = () => {
	formDataReset();
};

/** 获取参数 */
const _getParams = () => {
	const { siftName, sort, points} = model.value;
	const [minPoints, maxPoints] = points;
	return {
		siftName, 
		sort, 
		maxPoints, 
		minPoints
	}
};

/* 确认--保存 */
const { loading: isLoading, startLoading, endLoading } = useLoading();
const handleSave = (e: MouseEvent) => {
	e.preventDefault();
	formRef.value?.validate(async (errors: any) => {
		if (!errors) {
			try {
				startLoading();
				// 校验
				const { points, id } = model.value;
				const [minPoints, maxPoints] = points;

                if (!minPoints || !maxPoints) {
                    createMessageError('最小积分与最大积分不能为空！');
                    return;
                }
                if (Number(maxPoints) < Number(minPoints)) {
                    createMessageError('最大积分不能小于最小积分！');
                    return;
                }

				// 新增
				if (modalProps.value.type === 'add') {
					let _params = _getParams();
					const data = await addPointSift(_params);
					createMessageSuccess('新建标签成功');
					// 更新
				    emits('afterSuccess', data?.id, GoodsCategoryType.INTEGRALLABEL);
				} 
				else {
					await updatePointSift({
						id: model.value.id,
						..._getParams()
					});
					createMessageSuccess('编辑标签成功');
					// 更新
				    emits('afterSuccess', id, GoodsCategoryType.INTEGRALLABEL);
				}

				// 刷新
				setFalse();
			} catch (error) {
				createMessageError(modalProps.value.type === 'add' ? `新建标签失败: ${error}` : `修改标签失败: ${error}`);
			} finally {
				endLoading();
			}
		}
	});
};

defineExpose({
	acceptParams,
});
</script>

<style scoped lang="less"></style>
