import { StructreSettingsAuth ,StructreMemberManagementAuth, StructreCommissionDeatailAuth, StructreDepartmentDeatailAuth, OrganizationalApplicationAuth} from "@/enums/authKeys";
import { hasAuth } from "@/utils/auth/authUtils";

/** 分销设置 -- 保存*/
export const hasStructreSettingsIndexSaveAuth= function(){
    return hasAuth(StructreSettingsAuth.StructreSettingsIndexSave.key);
}()

/** 分销员管理 -- 添加分销员*/
export const hasStructreMemberManagementIndexAddAuth= function(){
    return hasAuth(StructreMemberManagementAuth.StructreMemberManagementIndexAdd.key);
}()

/** 分销员管理 -- 冻结/解冻*/
export const hasStructreMemberManagementIndexEnableAuth= function(){
    return hasAuth(StructreMemberManagementAuth.StructreMemberManagementIndexEnable.key);
}()

/** 分销员管理 -- 删除*/
export const hasStructreMemberManagementIndexDeleteAuth= function(){
    return hasAuth(StructreMemberManagementAuth.StructreMemberManagementIndexDelete.key);
}()

/** 分销员管理 -- 归属组织*/
export const hasStructreMemberManagementIndexBelongAuth= function(){
    return hasAuth(StructreMemberManagementAuth.StructreMemberManagementIndexBelong.key);
}()
/** 分销员管理 -- 分销商品*/
export const hasStructreMemberManagementIndexProductAuth= function(){
    return hasAuth(StructreMemberManagementAuth.StructreMemberManagementIndexProduct.key);
}()
/** 分销员管理 -- 个人码*/
export const hasStructreMemberManagementIndexPersonalCodeAuth= function(){
    return hasAuth(StructreMemberManagementAuth.StructreMemberManagementIndexPersonalCode.key);
}()

/** 佣金明细 -- 导出*/
export const hasStructreCommissionDeatailIndexExportAuth= function(){
    return hasAuth(StructreCommissionDeatailAuth.StructreCommissionDeatailIndexExport.key);
}()

/** 佣金明细 -- 详情*/
export const hasStructreCommissionDeatailIndexDetailAuth= function(){
    return hasAuth(StructreCommissionDeatailAuth.StructreCommissionDeatailIndexDetail.key);
}()


/** 组织架构 -- 创建一级组织*/
export const hasStructreDepartmentIndexCreateMainAuth= function(){
    return hasAuth(StructreDepartmentDeatailAuth.StructreDepartmentIndexCreateMain.key);
}()

/** 组织架构 -- 删除*/
export const hasStructreDepartmentIndexDeleteAuth= function(){
    return hasAuth(StructreDepartmentDeatailAuth.StructreDepartmentIndexDelete.key);
}()

/** 组织架构 -- 编辑*/
export const hasStructreDepartmentIndexEditAuth= function(){
    return hasAuth(StructreDepartmentDeatailAuth.StructreDepartmentIndexEdit.key);
}()

/** 组织架构 -- 创建子组织*/
export const hasStructreDepartmentIndexCreateSubAuth= function(){
    return hasAuth(StructreDepartmentDeatailAuth.StructreDepartmentIndexCreateSub.key);
}()

/** 组织架构 -- 区域经理注册码*/
export const hasRegionalManagerRegistrationCodeAuth= function(){
    return true;
    // return hasAuth(StructreDepartmentDeatailAuth.RegionalManagerRegistrationCode.key);
}()
/** 组织架构 -- 经销商注册码*/
export const hasDealerCodeAuth= function(){
    return true;
    // return hasAuth(StructreDepartmentDeatailAuth.DealerCode.key);
}()
/** 组织申请 -- 审核*/
export const hasOrganizationalApplicationAuditAuth= function(){
    return hasAuth(OrganizationalApplicationAuth.Audit.key);
}()