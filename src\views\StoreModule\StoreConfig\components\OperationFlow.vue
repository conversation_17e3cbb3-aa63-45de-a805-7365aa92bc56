<template>
  <div class="wrapper">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :isNeedCollapse="false"
      :isTableSelection="false"
      :isDisplayIndex="false"
      @paginationChange="paginationChange"
      @selectedKeysChange="selectedKeysChange"
      @table-sorter-change="tableSorterChange"
    >
      <template #searchForm>
        <div class="wrapper-top">
          <h1 class="title">门店退货审核业务</h1>
          <p class="desc">
            业务描述：会员退货后，店长需要将货退回平台，退回货品产生的快递费用可申请平台报销。开启此功能请提前配置流程
          </p>
        </div>
      </template>
      <!-- 操作项 -->
      <template #tableHeaderBtn>
        <JAddButton v-if="hasOperationFlowPageAdd" @click="rechargeRecords(false)" type="primary">新建流程</JAddButton>
      </template>
    </FormLayout>
    <OperationFlowModel ref="operationFlowModel" />
  </div>
</template>

<script setup lang="tsx">
import { onMounted, ref } from "vue";
// import dayjs from "dayjs";
import { useMessages } from "@/hooks";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { addOperationFlow, updateOperationFlow, deleteOperationFlow, getOperationFlowList } from "@/services/api";
import OperationFlowModel from "@/views/StoreModule/StoreConfig/components/OperationFlowModel.vue";
import {
  hasOperationFlowPageAdd,
  hasOperationFlowPageEdit,
  hasOperationFlowPageDelete,
} from "@/views/StoreModule/StoreConfig/hooks/authList";

const { createMessageSuccess, createMessageError } = useMessages();

//添加，修改，列表必传业务标识
const bizCode = "store_return_approval";
/* 表格方法Hook */
const {
  isLoading,
  tableData,
  getTableData,
  refreshTableData,
  paginationChange,
  sortTableData,
} = useTableDefault({
  getDataRequest: getOperationFlowList,
});


/* 表格项 */
const tableColumns = [
  {
    title: "流程名称",
    key: "name",
    width: 100,
    align: "left"
  },
  {
    title: "启用",
    key: "isEnable",
    align: "left",
    summaryTitle: "",
    width: 100,
    render: rowData => {
      return <n-tag size="small" bordered={false} type={rowData.enabled == 1 ? "success" : "error"}>
        {rowData.enabled == 1 ? "是" : "否"}
      </n-tag>;
    },
  },
  {
    title: "创建日期",
    key: "createTime",
    align: "left",
    summaryTitle: "",
    width: 100,
    // render: rowData => {
    //   return `${dayjs(rowData.createTime).format("YYYY-MM-DD HH:mm")}`;
    // },
  },
  {
    title: "操作",
    key: "action",
    width: 120,
    align: "left",
    fixed: "right",
    render: rowData => {
      return (
        <n-space align="center" justify="center">
          <n-button text size="small" v-show={hasOperationFlowPageEdit} onClick={() => rechargeRecords(rowData)}
                    type="primary">
            编辑
          </n-button>
          {hasOperationFlowPageDelete ? <n-popconfirm
            onPositiveClick={() => {
              deleteFlow(rowData.id);
            }}
          >
            {{
              trigger: () => (
                <a style={{ color: "red", cursor: "pointer" }}>删除</a>
              ),
              default: () => <span style={{ width: "300px" }}>是否确定删除该数据？</span>,
            }}
          </n-popconfirm> : null}
          <span v-show={!hasOperationFlowPageEdit && !hasOperationFlowPageDelete}>-</span>
        </n-space>
      );
    },
  },
];


/** 排序 */
const tableSorterChange = (info: { sort: string, sortAsc: "ascend" | "descend" }) => {
  sortTableData(info.sort, info.sortAsc);
};

/** 选中行数据 */
const rowData = ref([]);

/** 获取选中行Key */
function selectedKeysChange(key, tableData) {
  rowData.value = tableData.map(({ _dummyId, ...rest }) => rest);
}

/* 初始化参数 */
const initParams = {
  bizCode,
};
const model = ref({ ...initParams });

/* 刷新列表 */
const tableSearch = () => {
  getTableData({ bizCode: bizCode });
};
const operationFlowModel = ref();
const rechargeRecords = (rowData: any) => {
  let row = {
    row: rowData,
    api: rowData ? updateOperationFlow : addOperationFlow,
    refreshTable: tableSearch,
    bizCode,
  };
  operationFlowModel.value.acceptParams(row);
};

const deleteFlow = async (id) => {
  try {
    await deleteOperationFlow({ id: id });
    createMessageSuccess(`删除业务流程成功`);
    setTimeout(tableSearch, 500);
  } catch (e) {
    createMessageError(`删除业务流程失败： ${e}`);
  }
};
/* 组件挂载 */
onMounted(() => {
  tableSearch();
});
</script>

<style lang="less" scoped>
@import "@/styles/default.less";
@import "@/styles/defaultVar.less";

.wrapper {
  width: 100%;
  height: 100%;
  background-color: #FFFFFF;

  .wrapper-top {
    color: #666666;

    .title {
      font-size: 24px;
      font-weight: bolder;
    }

    .desc {
    }
  }
}
</style>
