import { ref, h } from "vue";
import { useMessages } from "@/hooks";
import type { TreeOption } from 'naive-ui';
import { NImage } from 'naive-ui';
import { GoodsCategoryType, SystemStoreType,ManagementType } from "@/enums";
import type { GoodsType } from "@/enums";
import { getGoodsClassificationPage } from "@/services/api";
import { deepClone, transformMinioSrc } from "@/utils";
import Folder from "@/assets/image/system/folder.png";
import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
import { buildTree, getExpandKeys } from "./utils";

/**
 * @description 获取商品分类
 * @param selectKey 默认选中项
 * @param isGeneral 是否只获取普通商品类型
 */
export default function useGetGoodsClassify(selectKey: GoodsType = GoodsCategoryType.ALL,isGeneral:boolean = false) {
    const message = useMessages();
    /** 商城类型 */
    const systemStore = useSystemStoreWithoutSetup();
    let storeType = systemStore._globalConfig['marketplaceType'];
    let distributionType = systemStore._globalConfig['visitDataType'];

    const defaultSelectKey: GoodsType = storeType === SystemStoreType.PHARMACEUTICALMALL ? selectKey : GoodsCategoryType.GENERAL;
    const isGetLoading = ref(false);
    const searchValue = ref('');
    /** 当前SelectedKeys */
    const selectedKeys = ref<Array<string | number>>([String(defaultSelectKey)]);

    /** 默认展开项 */
    const defaultExpandKeys = ref<Array<string | number>>([]);

    /** 参数 */
    const model = ref({
      selectedValue: '',
      cateId: null, // 当前选中的商品分类Id
      type: storeType === SystemStoreType.PHARMACEUTICALMALL ? null : GoodsCategoryType.GENERAL, // 商品分类类型
      pharmaceuticalsCommodities:false //是否为药品与普通商品
    });

    /** 分页总数 */
    let recordsTotal = 1;
    
    /** 商品分类搜索参数 */
    const _params = {
    	data: {
    		name: searchValue.value,
            type: undefined,
    	},
    	pageVO: {
    		current: 1,
    		size: 300,
    	},
    };

    /** 医药商城 */
    let pharmaceuticalMall: Array<TreeOption & { isMenu: boolean } & Partial<ApiStoreModule.GoodsClassification>>  = [
        {
            key: String(GoodsCategoryType.ALL),
            label: '全部',
            type: null,
            isMenu: false,
        },
        {
            key: String(GoodsCategoryType.DRUG),
            label: '药品',
            type: GoodsCategoryType.DRUG,
            isMenu: false,
            prefix: () => h(NImage, { 
                width: '20', 
                height: '20',
                previewDisabled: true,
                src: Folder,
                lazy: true
            }, {}),
            children: []
        },
        {
            key: String(GoodsCategoryType.THERAPY),
            label: '疗法',
            type: GoodsCategoryType.THERAPY,
            isMenu: false,
            prefix: () => h(NImage, { 
                width: '20', 
                height: '20',
                previewDisabled: true,
                src: Folder,
                lazy: true
            }, {}),
            children: [] 
        },
        {
            key: String(GoodsCategoryType.GENERAL),
            label: '普通商品',
            type: GoodsCategoryType.GENERAL,
            isMenu: false,
            prefix: () => h(NImage, { 
                width: '20', 
                height: '20',
                previewDisabled: true,
                src: Folder,
                lazy: true
            }, {}),
            children: []
        },
    ];

    /** 普通商城 */
    let generalMall: Array<TreeOption & { isMenu: boolean } & Partial<ApiStoreModule.GoodsClassification>>  = [
        {
            key: String(GoodsCategoryType.GENERAL),
            label: '普通商品',
            type: GoodsCategoryType.GENERAL,
            isMenu: false,
            prefix: () => h(NImage, { 
                width: '20', 
                height: '20',
                previewDisabled: true,
                src: Folder,
                lazy: true
            }, {}),
            children: []
        },
    ];

    /** 医药商城 */
    let distributionMall: Array<TreeOption & { isMenu: boolean } & Partial<ApiStoreModule.GoodsClassification>>  = [
        {
            key: String(GoodsCategoryType.ALL),
            label: '全部',
            type: null,
            isMenu: false,
        },
        {
            key: String(GoodsCategoryType.GENERAL),
            label: '普通商品',
            type: GoodsCategoryType.GENERAL,
            isMenu: false,
            prefix: () => h(NImage, { 
                width: '20', 
                height: '20',
                previewDisabled: true,
                src: Folder,
                lazy: true
            }, {}),
            children: []
        },
    ];

    /** 树形数据初始化 */
    const initTreeData: Array<TreeOption & { isMenu: boolean } & Partial<ApiStoreModule.GoodsClassification>> = storeType === SystemStoreType.PHARMACEUTICALMALL ? (distributionType ==  ManagementType.DISTRIBUTIONMALL ? [...distributionMall] : [...pharmaceuticalMall]) : [...generalMall];

    /** 树形数据 */
    const treeData = ref<Array<TreeOption & { isMenu: boolean }>>(deepClone(initTreeData));

    /** 处理数据 */
    const handleData = (dataList: Array<ApiStoreModule.GoodsClassification>, treeData: Array<TreeOption & { isMenu: boolean } & Partial<ApiStoreModule.GoodsClassification>>) => {
        const newTreeData = deepClone(treeData);
        const newDatalist = buildTree(dataList);
        
        newDatalist.forEach(item => {
            // 药品
            if (item.type == GoodsCategoryType.DRUG) {
                const drugTree = newTreeData.find(tree => tree.key == GoodsCategoryType.DRUG);
                if (drugTree) {
                    drugTree.children.push({
                        ...item,
                        key: item.id,
                        label: item.name,
                        isMenu: true,
                        prefix: () => h(NImage, { 
                            width: '20', 
                            height: '20',
                            previewDisabled: true,
                            src: transformMinioSrc(item.iconPath)
                        }, {}),
                        children: item?.children?.map(child => ({
                            ...child,
                            key: child.id,
                            label: child.name,
                            isMenu: true,
                            prefix: () => h(NImage, { 
                                width: '20', 
                                height: '20',
                                previewDisabled: true,
                                src: transformMinioSrc(child.iconPath)
                            }, {}),
                            isLeaf: child?.children?.length === 0,
                        })),
                        isLeaf: item?.children?.length === 0,
                    });
                }
            } 
            // 疗法
            else if (item.type == GoodsCategoryType.THERAPY) {
                const therapyTree = newTreeData.find(tree => tree.key == GoodsCategoryType.THERAPY);
                if (therapyTree) {
                    therapyTree.children.push({
                        ...item,
                        key: item.id,
                        label: item.name,
                        isMenu: true,
                        prefix: () => h(NImage, { 
                            width: '20', 
                            height: '20',
                            previewDisabled: true,
                            src: transformMinioSrc(item.iconPath)
                        }, {}),
                        children: item?.children?.map(child => ({
                            ...child,
                            key: child.id,
                            label: child.name,
                            isMenu: true,
                            prefix: () => h(NImage, { 
                                width: '20', 
                                height: '20',
                                previewDisabled: true,
                                src: transformMinioSrc(child.iconPath)
                            }, {}),
                            isLeaf: child?.children?.length === 0,
                        })),
                        isLeaf: item?.children?.length === 0,
                    });
                }
            } 
            // 普通商品
            else if (item.type == GoodsCategoryType.GENERAL) {
                const therapyTree = newTreeData.find(tree => tree.key == GoodsCategoryType.GENERAL);
                if (therapyTree) {
                    therapyTree.children.push({
                        ...item,
                        key: item.id,
                        label: item.name,
                        isMenu: true,
                        prefix: () => h(NImage, { 
                            width: '20', 
                            height: '20',
                            previewDisabled: true,
                            src: transformMinioSrc(item.iconPath)
                        }, {}),
                        children: item?.children?.map(child => ({
                            ...child,
                            key: child.id,
                            label: child.name,
                            isMenu: true,
                            prefix: () => h(NImage, { 
                                width: '20', 
                                height: '20',
                                previewDisabled: true,
                                src: transformMinioSrc(child.iconPath)
                            }, {}),
                            isLeaf: child?.children?.length === 0,
                        })),
                        isLeaf: item?.children?.length === 0,
                    });
                }
            }
        });
        // 使用 filter 去重
        let newDefaultExpandKeys = getExpandKeys(newTreeData).filter((value, index, self) => self.indexOf(value) === index);
        defaultExpandKeys.value.push(...newDefaultExpandKeys);
        
        return newTreeData;
    };

    /** 获取商品分类数据
     * @param callBack 回调函数
     * @param api 请求api
     * @param goodsType 商品类型
     */
    const getGoodsClassificationData = async (callBack?: () => void,api?:Function,goodsType?:GoodsType) => {
        try {
    		isGetLoading.value = true;
            _params.data.name = searchValue.value;
            _params.data.type = goodsType 
            let actualApi = api ?? getGoodsClassificationPage
            const { total, current, size, records } = await actualApi(_params);
		    _params.pageVO.current = Number(current);
		    _params.pageVO.size = Number(size);
		    recordsTotal = Number(total);
            if (_params.pageVO.current == 1 && records.length > 0) {
                treeData.value = handleData(records, isGeneral ? generalMall : initTreeData);
                if(model.value.pharmaceuticalsCommodities){
                    treeData.value = treeData.value.filter((item) =>
                        item.label !== '全部' && item.label !== '疗法'
                    )
                }
            } else {
                treeData.value = handleData(records, treeData.value);
            }
            // 友情提示
            if (searchValue.value && !records.length) {
                message.createMessageInfo(`不存在《${searchValue.value}》商品分类！`);
            }
    	} catch (err) {
    		message.createMessageError("获取商品分类列表失败: " + err);
    	} finally {
    		isGetLoading.value = false;
            callBack && callBack();
    	}
    };

    /** 滚动加载 */
    const handleScroll = (e) => {
    	const currentTarget = e.currentTarget as HTMLElement;
    	if (
    		currentTarget.scrollTop + currentTarget.offsetHeight >=
    		currentTarget.scrollHeight
    	) {
    		if (_params.pageVO.current * _params.pageVO.size < recordsTotal) {
    			_params.pageVO.current++;
    			getGoodsClassificationData();
    		}
    	}
    };

    /** 刷新 */
    const refresh = () => {
      getGoodsClassificationData();
    };

    return {
        isGetLoading,
        model,
        searchValue,
        getGoodsClassificationData,
        handleScroll,
        refresh,
        treeData,
        selectedKeys,
        storeType,
        defaultExpandKeys
    }
}
