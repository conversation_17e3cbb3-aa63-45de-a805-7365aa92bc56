<template>
  <div class="wrapper inner-page-height">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :pagination="paginationRef"
      @paginationChange="paginationChange"
      :isNeedCollapse="false"
      :isDisplayIndex="false"
      :isTableSelection="false"
      isUseBreadcrumb
    >
      <!-- 表单 -->
      <template #searchForm>
        <n-form
          ref="formRef"
          :model="formValue"
          label-placement="left"
          label-width="auto"
          :show-feedback="false"
          require-mark-placement="right-hanging"
          size="small"
          :style="{ width: '100%' }"
        >
          <n-form-item label="门店ID">
            <j-search-input
              v-model:value="formValue.storeShortId"
              placeholder="请输入门店ID"
              @search="handlerSearch"
              :width="240"
            />
          </n-form-item>
          <n-form-item label="门店名称">
            <j-search-input
              v-model:value="formValue.storeName"
              placeholder="请输入"
              @search="handlerSearch"
              :width="240"
            />
          </n-form-item>
          <n-form-item label="经销商ID">
            <j-search-input
              v-model:value="formValue.dealerShortCsId"
              placeholder="请输入经销商ID"
              @search="handlerSearch"
              :width="240"
            />
          </n-form-item>
          <n-form-item label="经销商姓名">
            <j-search-input
              v-model:value="formValue.dealerName"
              placeholder="请输入经销商姓名"
              @search="handlerSearch"
              :width="240"
            />
          </n-form-item>

          <n-form-item label="合并项">
            <n-select
              v-model:value="formValue.storeCombinedItems"
              placeholder="请选择"
              :options="storeCombinedItemsOptions"
              @update:value="tableSearch"
              style="width: 120px"
              clearable
            />
          </n-form-item>
          <n-form-item label="统计日期">
            <j-date-range-picker
              v-model:value="formValue.settlementTime"
                style="flex: 1;" 
                type="daterange" 
                format="yyyy-MM-dd"
                :clearable="false"
                :maxDays="366"
            />
          </n-form-item>
        </n-form>
      </template>
      <template #tableBreadcrumb>
        <n-space justify="end" style="width: 100%;">
          <div style="font-size: 14px;padding-right: 20px;" v-if="lastUpdateTime" >最后更新时间：{{lastUpdateTime}}</div>
        </n-space>
      </template>
      <template #tableHeaderBtn>
        <n-button @click="refresh" :loading="isLoading" class="store-button">刷 新</n-button>
        <n-button @click="exportData" class="store-button" type="primary" :loading="exportLoading" v-if="hasStoreDataReportExportAuth">导 出</n-button>
      </template>
    </FormLayout>
  </div>
</template>

<script lang="tsx" setup name="DoctorManagement">
import { onMounted, ref, watch } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
// import { getMedicalInquiryForm, storeDataStatsExport } from "@/services/api";
import { useMessages } from "@/hooks";
import { getStoreDataStatsPage, storeDataStatsExport, getReportLastUpdate } from "@/services/api";
import moment from "moment";
const { createMessageSuccess, createMessageError } = useMessages();
import { hasStoreDataReportExportAuth } from "../authList";

/** 表格hook */
const { isLoading, tableData, paginationRef, pageTableData, paginationChange } = useTableDefault({
  pageDataRequest: getStoreDataStatsPage,
});

const isShow = ref(false);

const data = ref({});

/** 合并项选项 */
const storeCombinedItemsOptions = [
  {
    label: "合并日期",
    value: 2,
  },
  {
    label: "合并门店",
    value: 3,
  }
];
const orderStatus = ["旧版本处方记录", "待支付", "待接诊", "咨询中", "已完成", "已取消"];
/** 参数 */
const formValue = ref({
    /** 门店id */
  storeShortId: "",
  /** 经销商id */
  dealerShortCsId:"",
  /** 经销商姓名 */
  dealerName:"",
  /** 结算时间 */
  settlementTime: null,
  /** 门店名称 */
  storeName: "",
  /** 合并项 */
  storeCombinedItems: null,
});
/* 表格列表项 */
const tableColumns = ref([
{
    title: "日期",
    key: "reportDate",
    align: "left",
    fixed: "left",
    width: 100,
},
  {
    title: "门店ID",
    key: "storeShortId",
    width: 100,
    align: "left",
    fixed: "left",
  },
  {
    title: "门店名称",
    key: "storeName",
    width: 100,
    align: "left",
    fixed: "left",
  },
  {
    title: "经销商用户ID",
    key: "dealerShortCsId",
    align: "left",
  },
  {
    title: "经销商姓名",
    key: "dealerName",
    align: "left",
  },
  {
    title: "注册人数",
    key: "registerCount",
    align: "left",
    width: 80,
  },
  {
    title: "销售订单数",
    key: "salesOrderCount",
    align: "left",
    width: 80,
  },
  {
    title: "销售商品数",
    key: "salesItemCount",
    align: "left",
    width: 80,
  },
  {
    title: "销售订单额",
    key: "salesAmount",
    align: "left",
    width: 100,
    render: row => {
      return <span>{row.salesAmount ? (row.salesAmount / 100).toFixed(2) : '0.00'}</span>
    }
  },
  {
    title: "退款订单数",
    key: "refundOrderCount",
    align: "left",
    width: 80,
  },
  {
    title: "退款商品数",
    key: "refundItemCount",
    align: "left",
    width: 80,
  },
  {
    title: "退款订单额",
    key: "refundAmount",
    align: "left",
    width: 100,
    render: row => {
      return <span>{row.refundAmount ? (row.refundAmount / 100).toFixed(2) : '0.00'}</span>
    }
  },
  {
    title: "登录人数",
    key: "loginCount",
    align: "left",
    width: 80,
  }
]);
watch([() => formValue.value.settlementTime], () => {
  tableSearch();
});
/** 获取参数 */
const getParams = () => {
  const { storeShortId, dealerShortCsId, dealerName, settlementTime, storeName, storeCombinedItems } = formValue.value;
  const reportStartTime = settlementTime ? moment(settlementTime[0]).format(`YYYY-MM-DD 00:00:00`) : null;
  const reportEndTime = settlementTime ? moment(settlementTime[1]).format(`YYYY-MM-DD 23:59:59`) : null;
  return {
    storeShortId,
    reportStartTime,
    reportEndTime,
    dealerShortCsId,
    dealerName,
    storeName,
    storeCombinedItems: storeCombinedItems ? storeCombinedItems : 1 ,
  };
};

/** 搜索 */
const handlerSearch = () => {
  tableSearch();
};

/** 表格刷新 */
function refresh() {
  tableSearch();
}

/* 表格搜索 */
const tableSearch = () => {
  pageTableData(getParams(), paginationRef.value);
};

const handlerRefund = row => {
  data.value = row;
  isShow.value = true;
};

const exportLoading = ref<boolean>(false);
const exportData = () => {
  exportLoading.value = true;
  console.log(paginationRef.value);
  
  storeDataStatsExport({
    data: { ...getParams() },
    pageVO: {
      current: paginationRef.value.current,
      size: paginationRef.value.total,
    },
  })
    .then(res => {
      createMessageSuccess("导出成功");
    })
    .catch(err => {
      createMessageError(`导出失败:${err}`);
    })
    .finally(() => {
      exportLoading.value = false;
    });
};

/** 获取最后更新时间 */
const lastUpdateTime = ref('');
const getLastUpdateTime = () => {
    getReportLastUpdate().then(res => {
        lastUpdateTime.value = res;
    });
}

/** 组件挂载 */
onMounted(() => {
  tableSearch();
  getLastUpdateTime();
});
</script>

<style lang="less" scoped>
@import "@/styles/defaultVar.less";
</style>
