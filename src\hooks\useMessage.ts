import { isFunction, isNullOrUnDef, isString } from "@/utils/isUtils";
import { useMessage, type MessageReactive } from "naive-ui";
import type { MessageApiInjection } from "naive-ui/es/message/src/MessageProvider";
import { onBeforeUnmount } from "vue";
import { emitEvent, EventsBusKeys } from "@/utils/eventsbusUtils";

type MessageType = "success" | "info" | "error" | "warning";
type MessageProps = {
  content: string;
  duration?: number;
  jIndependence?: boolean;
  onAfterLeave?: () => void;
};
type MessageTempItem = {
  content: string;
  duration: number;
  type: MessageType;
  onAfterLeave: () => void;
};
let allMsgData: Array<MessageTempItem> = [];
let message: MessageApiInjection;
let _isInit: boolean = false;
let msgReactive: MessageReactive | null = null;

export function createMessageInstance() {
  if (!_isInit) {
    _isInit = true;
    message = useMessage();
  }
}

function _create() {
  if (!allMsgData.length) {
    return null;
  } else {
    const item = allMsgData[0];
    allMsgData.splice(0, 1);
    return message[item.type](item.content, {
      duration: item.duration,
      keepAliveOnHover: item.type == "error" ? true : false,
      onAfterLeave: () => {
        item.onAfterLeave();
        msgReactive = _create();
      },
    });
  }
}

export const useMessages = () => {
  if (!_isInit) throw new Error("请先初始化message");
  let isIndependence: boolean = false;
  function removeAllMessage() {
    message.destroyAll();
    msgReactive = null;
    allMsgData = [];
  }
  const createMessage = (props: MessageProps | string, type: MessageType) => {
    const _message: MessageTempItem = {
      content: "",
      duration: 2000,
      type: type,
      onAfterLeave: ()=>{},
    };

    if (isString(props)) _message.content = props;
    else {
      _message.content = props.content;
      if(!isNullOrUnDef(props.duration)) _message.duration = props.duration;
      if(!isNullOrUnDef(props.jIndependence))  isIndependence = props.jIndependence;
      if(isFunction(props.onAfterLeave)) _message.onAfterLeave =  props.onAfterLeave;
    }

    //数量限制
    if (isIndependence) {
      message[_message.type](_message.content, { duration: _message.duration,onAfterLeave: _message.onAfterLeave });
    } else {
      //信息缓存
      allMsgData.push(_message);
      if (!msgReactive) {
        msgReactive = _create();
      }
    }
    onBeforeUnmount(() => {
      removeAllMessage();
    });
  };
  return {
    createMessageSuccess: (props: string | MessageProps) => {
      createMessage(props, "success");
    },
    createMessageInfo: (props: string | MessageProps) => {
      createMessage(props, "info");
    },
    createMessageError: (props: string | MessageProps) => {
      createMessage(props, "error");
    },
    createMessageWarning: (props: string | MessageProps) => {
      createMessage(props, "warning");
    },
    createMessageExportSuccess:(msg: string) => {
      const msgProp:MessageProps = {
        content: msg,
        duration:1000,
        onAfterLeave:()=>{
          emitEvent(EventsBusKeys.ExportSuccess)
        }
      }
      createMessage(msgProp,"success");
    },
    destoryMessage: () => {
      removeAllMessage();
    },
  };
};
