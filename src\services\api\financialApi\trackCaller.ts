import { defHttp } from "@/services";

/** 财务管理之轨迹查询余额 */
export const enum TrackCallerApi {
  page = "/traceRecord/pageRecords",
}

export interface TrackCaller {
  createTime: string;
  orderCode: string;
  platformName: string;
  shipCompanyName: string;
  trackingNo: string;
}
interface TrackCallerResponse {
  total: string,
  current: string,
  size: string,
  records: Array<TrackCaller>
}
export function getTrackCaller(params = {}) {
  return defHttp.post<TrackCallerResponse>({
    url: TrackCallerApi.page,
    params,
  });
}