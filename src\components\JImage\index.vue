<template>
   <NImage 
        v-bind="$attrs" 
        lazy
        :height="props.height" 
        :width="props.width" 
        :src="path"
    />
</template>

<script lang="ts" setup>
import { computed } from "vue";
import { NImage } from "naive-ui";
import { transformMinioSrc } from "@/utils";
import EmptyImg from '@/assets/image/system/avatar.png';

defineOptions({ name: 'JImage' });

/** props */
const props = withDefaults(defineProps<{
    height?: string;
    width?: string;
    imgPath: string | undefined | null; // 图片路径
}>(), {
    height: '35',
    width: '35',
});

const path = computed(() => {
    if (props.imgPath && props.imgPath !== '') {
        if (!isHttp(props.imgPath)) {
            return transformMinioSrc(props.imgPath);
        } else {
            return props.imgPath;
        }
    }
    return EmptyImg;
});

function isHttp(url) {
    // 判断字符串是否以 'http' 开头
    return url.startsWith("http");
}
</script>


<style lang="less" scoped>

</style>