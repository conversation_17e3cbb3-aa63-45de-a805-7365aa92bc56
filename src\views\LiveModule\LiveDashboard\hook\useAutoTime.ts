
import { onUnmounted } from 'vue';
import dayjs from "dayjs";

/**
 * 时间处理
 * @returns 
 */
export function useAutoTime(){
  /**
   * 补全时间数据 近5分钟
   * @param data 返回的相关数据
   * @param startTime 开始时间
   * @param endTime 结束时间
   */
  function completeTimeData(data, startTime, endTime) {
    const result = [];
    
    const originalDataMap = {};
    data.forEach(item => {
      const minuteKey = dayjs(item.timeMinute).format('YYYY-MM-DD HH:mm');
      originalDataMap[minuteKey] = item;
    });

    // 2. 计算总分钟数（从开始到结束）
    const start = dayjs(startTime);
    const end = dayjs(endTime);
    const totalMinutes = end.diff(start, 'minute') + 1; 

    for (let i = 1; i < totalMinutes; i++) {
      const currentTime = start.add(i, 'minute');
      const minuteKey = currentTime.format('YYYY-MM-DD HH:mm');
      const fullTime = currentTime.format('YYYY-MM-DD HH:mm:ss');

      if (originalDataMap[minuteKey]) {
        result.push({ ...originalDataMap[minuteKey] });
      } else {
        result.push({
          timeMinute: fullTime,
          totalCustomers: 0,
          totalMoney: 0,
          totalOrders: 0,
          totalTransactionMoney: 0,
          totalProducts: 0,
          onlineNumber: 0
        });
      }
    }
    return result;
  }
  /**
   * 补全时间数据（5分钟间隔）
   * @param data 原始数据
   * @param startTime 开始时间
   * @param endTime 结束时间
   */
  function completeFiveMinuteData(data, startTime, endTime) {
    const result = [];
    // console.log(data, startTime, endTime,'参数======================');
    
    // 1. 创建原始数据映射（按5分钟整点作为key）
    const originalDataMap = {};
    data.forEach(item => {
      const time = dayjs(item.timeMinute);
      // 转换为5分钟整点（向下取整）
      const roundedTime = time.minute(Math.floor(time.minute() / 5) * 5).second(0);
      const minuteKey = roundedTime.format('YYYY-MM-DD HH:mm');
      originalDataMap[minuteKey] = item;
    });
    // console.log(originalDataMap,'返回加工======================');
    // 2. 调整开始和结束时间为5分钟整点
    const start = dayjs(startTime);
    const adjustedStart = start.minute(Math.floor(start.minute() / 5) * 5).second(0);
    
    const end = dayjs(endTime);
    const adjustedEnd = end.minute(Math.floor(end.minute() / 5) * 5).second(0);

    // 3. 计算总5分钟间隔数
    const totalIntervals = adjustedEnd.diff(adjustedStart, 'minute') / 5 + 1;

    // 4. 生成5分钟间隔的时间点
    for (let i = 0; i < totalIntervals; i++) {
      const currentTime = adjustedStart.add(i * 5, 'minute');
      const minuteKey = currentTime.format('YYYY-MM-DD HH:mm');
      const fullTime = currentTime.format('YYYY-MM-DD HH:mm:ss');

      if (originalDataMap[minuteKey]) {
        result.push({ ...originalDataMap[minuteKey] });
      } else {
        result.push({
          timeMinute: fullTime,
          totalCustomers: 0,
          totalMoney: 0,
          totalOrders: 0,
          totalTransactionMoney: 0,
          totalProducts: 0,
          onlineNumber: 0
        });
      }
    }
    // console.log(result,'结果======================');
    return result;
  }
  return {
    completeTimeData,
    completeFiveMinuteData
  }
}

/**
 * 定时器
 * @param intervalSecs 秒数区间
 * @param onRefresh  刷新相关方法
 * @returns 
 */
export function useAutoRefresh(intervalSecs:[number,number], onRefresh?: () => void) {
  let timeoutId: number | null = null;
  const getRandomInterval = () => {
    const [minSec, maxSec] = intervalSecs;
    const randomSec = Math.random() * (maxSec - minSec) + minSec;
    return Math.floor(randomSec * 1000);
  };
  const refresh = async () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    if (onRefresh) {
      await onRefresh();
    }
    // 设置下一次定时刷新
    timeoutId = window.setTimeout(refresh, getRandomInterval());
  };

  // 手动
  const triggerRefresh = async() => {
    await refresh();
    return false
  };

  // 停止定时器
  const stopTimer = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
  };

  // 组件卸载时自动清理
  onUnmounted(stopTimer);

  return {
    triggerRefresh,
    stopTimer,
  };
}