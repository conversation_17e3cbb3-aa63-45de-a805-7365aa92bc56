<template>
  <MenuLayout v-model:activeKey="activeTypeRef" :menuOptions="menusList">
    <component :is="currentPage" />
  </MenuLayout>
</template>

<script lang="ts" setup name="UserRecharge">
import { ref, computed, watch } from "vue";
import { FinanceMenuType, type FinanceType } from "@/enums";
import {
  hasFinanceStoreConfigRechargeRecordAuth,
  hasFinanceStoreConfigExpenseRecordAuth,
  hasFinanceStoreConfigAccountBalanceAuth,
} from "../authList";
/** 相关组件 */
import MenuLayout from "@/components/MenuLayout/index.vue";
import RechargeRecord from "@/views/StoreModule/Finance/UserRecharge/RechargeRecord/index.vue";
import ExpenditureRecord from "@/views/StoreModule/Finance/UserRecharge/ExpenditureRecord/index.vue";
import AccountBalance from "@/views/StoreModule/Finance/UserRecharge/AccountBalance/index.vue";

/** props */
const props = defineProps<{
  configurationAddressStatus?: number, // 配置地址跳转状态
}>();

/** 用户充值tab */
const menusList = ref([
  {
    label: "充值记录",
    key: FinanceMenuType.RechargeRecord,
    show: hasFinanceStoreConfigRechargeRecordAuth,
  },
  {
    label: "支出记录",
    key: FinanceMenuType.ExpenditureRecord,
    show: hasFinanceStoreConfigExpenseRecordAuth,
  },
  {
    label: "账户余额",
    key: FinanceMenuType.AccountBalance,
    show: hasFinanceStoreConfigAccountBalanceAuth,
  },
]);

const activeTypeRef = ref<FinanceType>(menusList.value.filter(item => item.show).map(item => item.key)[0]);

/** 相关组件 */
const pageMap = {
  [FinanceMenuType.RechargeRecord]: RechargeRecord,
  [FinanceMenuType.ExpenditureRecord]: ExpenditureRecord,
  [FinanceMenuType.AccountBalance]: AccountBalance,
};

/** 当前页 */
const currentPage = computed(() => pageMap[activeTypeRef.value]);
/** 监听 */
watch(() => {
  return props.configurationAddressStatus;
}, (newVal) => {
  if (newVal == 1) {
    activeTypeRef.value = FinanceMenuType.RechargeRecord;
  }
}, { immediate: true });
</script>

<style lang="less" scoped></style>
