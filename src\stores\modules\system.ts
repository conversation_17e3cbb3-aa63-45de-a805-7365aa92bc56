import { defineStore } from "pinia";
import { StoreName } from "@/enums/stores";
import { stores } from "@/stores";
import { createCacheStorage } from "@/utils/cache/storageCache";
import { CacheConfig } from "@/utils/cache/config";
import { useGlobalConfig } from "@/hooks/business";
import { getSystemConfigInfo } from "./helpers";
import { type GlobalConfig } from "@/enums";

interface SystemStore {
  _imgPrefix: string;
  _searchMenus: any[];
  _globalConfig: GlobalConfig;
}

export const useSystemStore = defineStore(StoreName.System, {
  state: (): SystemStore => {
    return {
      _imgPrefix: "",
      _searchMenus: [], // 搜索菜单
      // 全局系统配置
      _globalConfig: getSystemConfigInfo(),
    };
  },
  getters: {
    imgPrefix: state => {
      if (!state._imgPrefix) {
        try {
          const userSystemStorage = createCacheStorage(CacheConfig.System);
          const _systemCache = userSystemStorage.get();
          state._imgPrefix = _systemCache['imgPrefix'];
        } catch (e) {
          console.error(e);
        }
      }
      return state._imgPrefix;
    },
    searchMenus: state => {
      if (!state._searchMenus.length) {
        try {
          const userSystemStorage = createCacheStorage(CacheConfig.SearchMenus);
          const _systemCache = userSystemStorage.get();
          state._searchMenus = _systemCache['searchMenus'];
        } catch (e) {
          console.error(e);
        }
      }
      return state._searchMenus;
    },
  },
  actions: {
    setImgPrefix(imgPrefix: string) {
      const userSystemStorage = createCacheStorage(CacheConfig.System);
      let _systemCache = userSystemStorage.get();
      if (_systemCache) _systemCache['imgPrefix']= imgPrefix;
      else _systemCache = { imgPrefix };
      userSystemStorage.set(_systemCache);
      this._imgPrefix = imgPrefix;
    },
    // 缓存修改搜索菜单
    setSearchMenus(searchMenus) {
      const userSystemStorage = createCacheStorage(CacheConfig.SearchMenus);
      let _systemCache = userSystemStorage.get();
      if (_systemCache) _systemCache['searchMenus']= searchMenus;
      else _systemCache = { searchMenus };
      userSystemStorage.set(_systemCache);
      this._searchMenus = searchMenus;
    },
    // 缓存系统全局配置
    setStystemGlobalConfig(_globalConfig: SystemStore['_globalConfig']) {
      const userSystemStorage = createCacheStorage(CacheConfig.GlobalConfig);
      let _systemCache = userSystemStorage.get();
      if (_systemCache) _systemCache['globalConfig'] = _globalConfig;
      else _systemCache = { globalConfig: _globalConfig };
      userSystemStorage.set(_systemCache);
      this._globalConfig = _globalConfig;
    },
    // 刷新系统全局配置
    async _refreshStystemGlobalConfig() {
      const userSystemStorage = createCacheStorage(CacheConfig.GlobalConfig);
      const { globalConfig, getGlobalConfiguration } = useGlobalConfig();
      await getGlobalConfiguration();
      let _systemCache = { 
        globalConfig: globalConfig.value
      };
      userSystemStorage.set(_systemCache);
      this._globalConfig = globalConfig.value;
    },
  },
});

export function useSystemStoreWithoutSetup() {
  return useSystemStore(stores);
}
