<template>
  <div style="height: 100%;width: 100%;">
    <IMLayout>
      <template #conversation>
        <JUIConversation @handleSwitchConversation="handleSwitchConversation"/>
      </template>
      <template #chat>
        <Transition appear name="fade-transform" mode="out-in">
          <component
              :is="currentComponent"
              :key="currentConversationKey"
          />
        </Transition>
      </template>
    </IMLayout>
  </div>
</template>

<script setup lang="ts">
import {provide, toRef, readonly, ref, watch, computed, nextTick} from 'vue'
import IMLayout from './layout/IMLayout/index.vue'
import JUIConversation from '@/views/DoctorEndModule/IM/components/JUIConversation/index.vue'
import JUIChat from "@/views/DoctorEndModule/IM/components/JUIChat/index.vue"
import {isNullOrUnDef} from "@/utils";

defineOptions({
  name: 'IMPage'
})

/** props */
const props = withDefaults(defineProps<{
  tabName: string; // tab名称
}>(), {});

const tabName = toRef(props, 'tabName')
const currentConversationData = ref<any>('')
const currentConversationKey = ref();
const receptionMapRef = ref(new Map())

provide('tabName', {tabName: readonly(tabName)});
provide('receptionMapRef', {receptionMapRef: readonly(receptionMapRef), changeReceptionMap});
provide('currentConversationData', {currentConversationData: readonly(currentConversationData), changeCurrentConversation});

/**
 *
 * @param conversationID IM的会话id
 * @param mode
 * @param item  问诊卡数据
 */
function changeReceptionMap(conversationID: string, mode: 'set' | 'delete', item?: object,) {
  if (mode == 'set') {
    receptionMapRef.value.set(conversationID, item)
  }
  if (mode == 'delete') {
    receptionMapRef.value.delete(conversationID)
  }
}

function changeCurrentConversation(conversation, position) {
  console.log('修改当前的会话数据====>', conversation, position)
  currentConversationData.value = conversation
}

function handleSwitchConversation(conversation) {

}

watch(() => currentConversationData.value, (newValue, oldValue) => {
  console.log(`监听到了当前会话的修改====>`, newValue, oldValue)
  if (newValue?.conversationId !== oldValue?.conversationId) {
    currentConversationKey.value = newValue?.conversationId

  }
  if (isNullOrUnDef(newValue)) {
    nextTick(() => {
      currentConversationKey.value = ''
    })
  }
})

let currentComponent = computed(() => {
  if (currentConversationKey.value) {
    return JUIChat
  }
  return 'div'
})

</script>

<style scoped lang="less">
@import "@/styles/default.less";


</style>
