import { defHttp } from "@/services";
export const enum LiveGoodsApi {
    allProductPage = "/product/manage/getAllStoresProduct", //商品库
    liveActivityProduct = "/liveActivityProduct/page", //直播配置商品
    add = "/liveActivityProduct/add", //添加商品
    updateCartEnable = "/liveActivityProduct/updateCartEnable", //启用/关闭直播间小黄车
    delete = "/liveActivityProduct/delete",//单个删除
    batchDelete = "/liveActivityProduct/batchDelete",//批量删除
    batchPublish = "/liveActivityProduct/batchPublish",//上下架
    updateSort = "/liveActivityProduct/batchUpdateSort",//修改排序
    updateFloatingStatus="/liveActivityProduct/updateFloatingStatus"//启用关闭讲解
}

export interface GoodsPageRes {
    records: [],
    total: string;
    size: string;
    current: string;
}
/** 直播间获取所有门店商品 */
export function getLiveGoodsPage(params: {
    data: {
        name?: string,
        productIds?: string
    },
    pageVO: {
        current: number,
        size: number
    }
}) {
    return defHttp.post<GoodsPageRes>({
        url: LiveGoodsApi.allProductPage,
        params,
    });
}
/** 直播间课程商品 */
export function getLiveActivityProduct(params: {
    data: {
        liveActivityId?: string,
    },
    pageVO: {
        current: number,
        size: number
    }
}) {
    return defHttp.post<GoodsPageRes>({
        url: LiveGoodsApi.liveActivityProduct,
        params,
    });
}
/** 添加商品 */
export function addGoods(_params) {
    return defHttp.post({
        url: LiveGoodsApi.add,
        params: {
            data: _params
        },
    });
}
/** 更新 */
export function updateCartEnable(_params) {
    return defHttp.put({
      url: LiveGoodsApi.updateCartEnable,
      params: {
        data: _params
    },
    })
}
/** 删除 */
export function deleteGoodsById(_params: {
    id: string;
}) {
    return defHttp.delete({
        url: LiveGoodsApi.delete,
        requestConfig: {
            isQueryParams: true,
        },
        params: _params,
    });
}
/** 批量删除 */
export function goodsBatchDelete(_params) {
    return defHttp.delete({
      url: LiveGoodsApi.batchDelete,
      params: _params,
      requestConfig: {
        isQueryParams: true,
    },
    });
}

export function batchGoodsPublish(_params) {
    return defHttp.put({
      url: LiveGoodsApi.batchPublish,
      params: {
        data: _params
      },
    })
}
/** 修改排序 */
export function updateSort(_params) {
    return defHttp.post({
        url: LiveGoodsApi.updateSort,
        params: {
            data: _params
        },
    });
}

/** 修改直播商品讲解 */
export function updateFloatingStatus(_params) {
  return defHttp.post({
    url: LiveGoodsApi.updateFloatingStatus,
    params: {
      data: _params,
    },
  });
}