import { defHttp } from "@/services";
/** 更多配置 */
export const enum moreConfiglApi {
  addTime = "/activityTime/add",
  deleteTime = "/activityTime/delete",
  getPointProductConfig = "/globalConfigs/getPointProductConfig",
  updatePointProductConfig ="/globalConfigs/updatePointProductConfig"
}

/**
 * @description 添加一行记录
 */
export function addTime(params) {
  return defHttp.post({
    url: moreConfiglApi.addTime,
    params:{
      data:params
    }
  });
}

/**
 * @description 删除一行记录
 */
export function deleteTime(params) {
  return defHttp.delete({
    url: moreConfiglApi.deleteTime,
    params,
    requestConfig: {
      isQueryParams: true,
    },
  });
}

/**
 * @description 获取积分商品设置
 */
export function getPointProductConfig(params) {
  return defHttp.post({
    url: moreConfiglApi.getPointProductConfig,
    params:{
      data:params
    }
  });
}

/**
 * @description 修改积分商品设置
 */
export function updatePointProductConfig(params) {
  return defHttp.post({
    url: moreConfiglApi.updatePointProductConfig,
    params:{
      data:params
    }
  });
}