/**
 * @description 将数据转换为树形结构
 * @param data 数据
 * @param id 节点id
 * @returns 
 */
export function transformOptions(data, id) {
    const map = new Map();

    // 创建每个节点的基本结构
    data.forEach(item => {
        map.set(item.id, {
            key: item.id,
            label: item.name,
            disabled: false,
            isLeaf: true, // 默认是叶节点
            children: []
        });
    });

    // 构建树形结构
    const result = [];
    data.forEach(item => {
        if (item.parentId) {
            const parent = map.get(item.parentId);
            if (parent) {
                parent.children.push({
                    ...map.get(item.id),
                    parentId: item.parentId
                });
                parent.isLeaf = false; // 如果有子节点，则不是叶节点
            }
        } else {
            result.push(map.get(item.id));
        }
    });

    // 递归禁用节点及其子节点
    function disableNodeAndChildren(node) {
        node.disabled = true;
        node.children.forEach(child => disableNodeAndChildren(child));
    }

    // 如果传入的 id 匹配某个节点，则禁用该节点及其子节点
    const nodeToDisable = map.get(id);
    if (nodeToDisable) {
        disableNodeAndChildren(nodeToDisable);
    }

    return result;
}

/**
 * 遍历数据并返回具有子节点的项的 ID 数组
 * @param {Array} items - 要遍历的项
 * @returns {Array} - 包含具有子节点的项的 ID 的数组
 */
export function getExpandKeys(items) {
    const expandKeys = []; // 存储展开项的数组

    items.forEach(item => {
        if (item.children && item.children.length > 0) {
            expandKeys.push(item.key); // 添加当前项的 key
            // 递归处理子节点并合并结果
            expandKeys.push(...getExpandKeys(item.children));
        }
    });

    return expandKeys; // 返回展开项的数组
}

/**
 * @description: 查找父节点和子节点
 * @param data 数据
 * @param childKey 
 * @returns 父节点 > 子节点
 */
export function findParentAndChild(data, parentId, childKey) {
    let parentLabel = null;
    let childLabel = null;

    // 查找父节点标签并获取其子节点
    for (const item of data) {
        if (item.key === parentId) {
            parentLabel = item.label; // 获取父节点标签

            // 在父节点的 children 中查找子节点标签
            if (item.children && item.children.length > 0) {
                for (const child of item.children) {
                    if (child.key === childKey) {
                        childLabel = child.label; // 获取子节点标签
                        break;
                    }
                }
            }
            break;
        }
    }

    // 返回格式为 "父节点 > 子节点"
    if (parentLabel && childLabel) {
        return `${parentLabel} > ${childLabel}`;
    }

    return null; // 如果未找到父节点或子节点
}