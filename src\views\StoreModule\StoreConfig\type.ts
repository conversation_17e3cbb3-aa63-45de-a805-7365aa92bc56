export enum GroupNameValue {
    Transaction = 2,
    Development = 3,
}

export const enum ZtoParams {
  appName = "sto_zto_housekeeping_app_name",
  appKey = "sto_zto_housekeeping_app_key",
  appSecret = "sto_zto_housekeeping_app_secret",
  shopKey = "sto_zto_housekeeping_shop_key",
}

/** 地址类型查询 */
export const enum AddressType {
  /** 全部 */
  All = 0,
  /** 发货地址 */
  Delivery = 3,
  /** 退货地址 */
  Return = 1,
  /** 默认地址 */
  Default = 4,
}

/** 地址是否启用 */
export const enum AddressIsActivated {
  /** 不启用 */
  NotActivated = 0,
  /** 启用 */
  Activated = 1,
}
