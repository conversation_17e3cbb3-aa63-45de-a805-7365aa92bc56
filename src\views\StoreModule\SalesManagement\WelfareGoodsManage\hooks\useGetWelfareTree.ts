import { ref, h } from "vue";
import { useMessages } from "@/hooks";
import type { TreeOption } from "naive-ui";
import { NImage } from "naive-ui";
import { GoodsCategoryType, SystemStoreType, ManagementType } from "@/enums";
import type { GoodsType } from "@/enums";
import { couponProductCatePage } from "@/services/api";
import { deepClone, transformMinioSrc } from "@/utils";
import Folder from "@/assets/image/system/folder.png";
import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
import { buildTree, getExpandKeys } from "./utils";

interface WelfareGoodsTreeData {
  id: string;
  name: string;
  imageUrl: string;
  sort: number;
}

/**
 * @description 福利商品分类类型
 */
export const enum WelfareGoodsCategoryType {
  /** 全部 */
  ALL = 9,
  /** 福利品 */
  WELFARE = 1,
}

export default function useGetWelfareTree() {
  const messages = useMessages();
  /** 默认选中 */
  const defaultSelectKey = WelfareGoodsCategoryType.WELFARE;
  /** 数据加载 */
  const isGetLoading = ref(false);
  const searchValue = ref("");
  /** 当前SelectedKeys */
  const selectedKeys = ref<Array<string | number>>([String(defaultSelectKey)]);

  /** 默认展开项 */
  const defaultExpandKeys = ref<Array<string | number>>([]);

  /** 参数 */
  const model = ref({
    selectedValue: "",
    cateId: null, // 当前选中的商品分类Id
  });

  /** 分页总数 */
  let recordsTotal = 1;

  /** 商品分类搜索参数 */
  const _params = {
    data: {
      name: searchValue.value,
    },
    pageVO: {
      current: 1,
      size: 300,
    },
  };

  /** 福利品商城分类 */
  let welfareTree: Array<TreeOption & { isMenu: boolean } & Partial<WelfareGoodsTreeData>> = [
    // {
    //   key: String(WelfareGoodsCategoryType.ALL),
    //   label: "全部",
    //   type: null,
    //   isMenu: false,
    // },
    {
      key: String(WelfareGoodsCategoryType.WELFARE),
      label: "福利品",
      type: WelfareGoodsCategoryType.WELFARE,
      isMenu: false,
      prefix: () =>
        h(
          NImage,
          {
            width: "20",
            height: "20",
            previewDisabled: true,
            src: Folder,
            lazy: true,
          },
          {},
        ),
      children: [],
    },
  ];

  /** 树形数据初始化 */
  const initTreeData: Array<TreeOption & { isMenu: boolean } & Partial<WelfareGoodsTreeData>> = [...welfareTree];

  /** 树形数据 */
  const treeData = ref<Array<TreeOption & { isMenu: boolean }>>(deepClone(initTreeData));

  /** 处理数据 */
  const handleData = (
    dataList: Array<WelfareGoodsTreeData>,
    treeData: Array<TreeOption & { isMenu: boolean } & Partial<WelfareGoodsTreeData>>,
  ) => {
    const newTreeData = deepClone(treeData);
    const newDatalist = buildTree(dataList);

    // console.log("newDatalist", newDatalist);
    // console.log("newTreeData", newTreeData);

    newDatalist.forEach(item => {
      // 福利品
      const welfareTree = newTreeData.find(tree => tree.key == WelfareGoodsCategoryType.WELFARE);
      if (newTreeData) {
        welfareTree.children.push({
          ...item,
          key: item.id,
          label: item.name,
          isMenu: true,
          prefix: () =>
            h(
              NImage,
              {
                width: "20",
                height: "20",
                previewDisabled: true,
                src: transformMinioSrc(item.imageUrl),
              },
              {},
            ),
          children: item?.children?.map(child => ({
            ...child,
            key: child.id,
            label: child.name,
            isMenu: true,
            prefix: () =>
              h(
                NImage,
                {
                  width: "20",
                  height: "20",
                  previewDisabled: true,
                  src: transformMinioSrc(child.imageUrl),
                },
                {},
              ),
            isLeaf: child?.children?.length === 0,
          })),
          isLeaf: item?.children?.length === 0,
        });
      }
    });
    // 使用 filter 去重
    let newDefaultExpandKeys = getExpandKeys(newTreeData).filter((value, index, self) => self.indexOf(value) === index);
    defaultExpandKeys.value.push(...newDefaultExpandKeys);

    return newTreeData;
  };

  /**
   * 获取商品分类数据
   * @param callBack 可选的回调函数，在请求完成后执行
   */
  const getGoodsClassificationData = async (callBack?: () => void) => {
    try {
      isGetLoading.value = true;
      _params.data.name = searchValue.value;

      // 请求商品分类数据
      const { total, current, size, records } = await couponProductCatePage(_params);

      // 更新分页参数
      _params.pageVO.current = Number(current);
      _params.pageVO.size = Number(size);
      recordsTotal = Number(total);

      // 处理返回数据
      if (_params.pageVO.current === 1 && records.length > 0) {
        // 第一页且有数据时，初始化树数据
        treeData.value = handleData(records, initTreeData);
      } else {
        // 非第一页时，合并数据到现有树
        treeData.value = handleData(records, treeData.value);
      }

      // 搜索无结果时的提示
      if (searchValue.value && records.length === 0) {
        messages.createMessageInfo(`不存在《${searchValue.value}》福利商品分类！`);
      }
    } catch (err) {
      console.error("获取福利商品分类失败:", err);
      messages.createMessageError("获取福利商品分类失败: " + err);
    } finally {
      isGetLoading.value = false;
      callBack?.();
    }
  };

  /** 滚动加载 */
  const handleScroll = e => {
    const currentTarget = e.currentTarget as HTMLElement;
    if (currentTarget.scrollTop + currentTarget.offsetHeight >= currentTarget.scrollHeight) {
      if (_params.pageVO.current * _params.pageVO.size < recordsTotal) {
        _params.pageVO.current++;
        getGoodsClassificationData();
      }
    }
  };

  /** 刷新 */
  const refresh = () => {
    getGoodsClassificationData();
  };

  return {
    isGetLoading,
    model,
    searchValue,
    getGoodsClassificationData,
    handleScroll,
    refresh,
    treeData,
    selectedKeys,
    defaultExpandKeys,
  };
}
