import MainLayout from "@/layout/MainLayout/index.vue";
import { RoutesName } from "@/enums/routes";

export const Default = {
  [RoutesName.Root]: {
    path: "/",
    redirect: "/check",
    component: MainLayout,
    meta: {
      isMenu: false,
      isShow: false,
    },
  },
  [RoutesName.Login]: {
    path: "/login",
    component: () => import("@/views/default/Login/index.vue"),
    meta: {
      isMenu: false,
      title: "登录页",
    },
  },
  [RoutesName.DoctorLogin]: {
    path: "/doctor/login",
    component: () => import("@/views/DoctorEndModule/Login/index.vue"),
    meta: {
      isMenu: false,
      title: "医生端登录页",
    },
  },
  [RoutesName.Check]: {
    path: "/check",
    component: () => import("@/views/default/Check/index.vue"),
    meta: {
      isMenu: false,
      title: "加载中",
    },
  },
  [RoutesName.Exception403]: {
    path: "/403",
    component: () => import("@/views/default/Exception/index.vue"),
    meta: {
      isMenu: false,
      code: "403",
      title: "403",
    },
  },
  [RoutesName.Exception404]: {
    path: "/404",
    component: () => import("@/views/default/Exception/index.vue"),
    meta: {
      isMenu: false,
      code: "404",
      title: "404",
    },
  },
  [RoutesName.UnResiter]: {
    path: "/:pathMatch(.*)*",
    redirect: "/404",
    meta: {
      isMenu: false,
    },
  },
};
