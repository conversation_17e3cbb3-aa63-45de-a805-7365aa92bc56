import { ref } from "vue";
export default function useOrderTable() {
  /* 表格项 */
  const tableColumns = [
    {
      title: "日期",
      key: "reportDate",
      align: "left",
      fixed: "left",
      width: 100,
      sorter: true,
      summaryTitle: "",
      isSortDefault: true,
    },
    {
      title: "总订单数",
      key: "totalOrder",
      align: "left",
      width: 150,
      sorter: true,
    },
    {
      title: "已支付单数",
      key: "paidOrder",
      align: "left",
      width: 100,
      sorter: true,
    },
    {
      title: "未支付单数",
      key: "unpaidOrder",
      align: "left",
      width: 150,
      sorter: true,
    },
    {
      title: "在线支付单数",
      key: "onlinePaymentSingular",
      align: "left",
      width: 100,
    },
    {
      title: "定金支付单数",
      key: "downPaymentSingular",
      align: "left",
      width: 100,
    },
    {
      title: "物流代收单",
      key: "logisticsCollectionSingular",
      align: "left",
      width: 100,
    },
    {
      title: "已支付订单总额（元）",
      key: "totalAmount",
      align: "left",
      width: 150,
      sorter: true,
      render: row => {
        return <span>{row.totalAmount ? (row.totalAmount / 100).toFixed(2) : 0}</span>;
      },
    },
    {
      title: "线上实收款（元）",
      key: "onlinePaymentAmount",
      align: "left",
      width: 120,
      sorter: true,
      render: row => {
        return <span>{row.onlinePaymentAmount ? (row.onlinePaymentAmount / 100).toFixed(2) : 0}</span>;
      },
    },
  ];
  //汇总
  const summaryRefs = ref(null);
  // 计算合计数据
  const summaryColumn = (rowData) => {
    const _sum = {
      "totalOrder": 0,
      "paidOrder": 0,
      "unpaidOrder": 0,
      "onlinePaymentSingular": 0,
      "downPaymentSingular": 0,
      "logisticsCollectionSingular": 0,
      "totalAmount": 0,
      "onlinePaymentAmount": 0,
    };
    rowData.forEach(row => {
      for (let key in row) {
        if (Object.keys(_sum).includes(key)) {
          _sum[key] = _sum[key] + row[key]
        }
      }
    });
    _sum['totalAmount'] = Number((_sum['totalAmount'] / 100).toFixed(2));
    _sum['onlinePaymentAmount'] = Number((_sum['onlinePaymentAmount'] / 100).toFixed(2));
    return {
      ..._sum,
    }
  };

  return {
    tableColumns,
    summaryRefs,
    summaryColumn,
  };
};
