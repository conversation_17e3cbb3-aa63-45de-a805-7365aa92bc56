import { ref, h } from "vue";
import { useMessages, useBoolean, useLoading } from "@/hooks";
import type { TreeOption } from "naive-ui";
import { NImage } from "naive-ui";
import { GoodsCategoryType } from "@/enums";
import { getGoodsClassificationPage, getAllPointSift } from "@/services/api";
import { deepClone, transformMinioSrc } from "@/utils";
import Folder from "@/assets/image/system/folder.png";

const message = useMessages();

/**
 * @description 获取积分商品分类
 */
export default function useGetIntegralGoodsClass() {
  const { loading: isGetIntegralLoading, startLoading, endLoading } = useLoading();
  const searchValue = ref("");

  /** 当前SelectedKeys */
  const selectedKeys = ref<Array<string | number>>([String(GoodsCategoryType.ALL)]);

  /** 分页总数 */
  let recordsTotal = 1;

  /** 商品分类搜索参数 */
  const _params = {
    data: {
      name: searchValue.value,
      type: GoodsCategoryType.INTEGRAL,
    },
    pageVO: {
      current: 1,
      size: 300,
    },
  };

  /** 树形数据初始化 */
  const getInitialTreeData = () => [
    {
      key: String(GoodsCategoryType.ALL),
      label: "全部",
      type: null,
      isMenu: false,
    },
    {
      key: String(GoodsCategoryType.INTEGRAL),
      label: "分类",
      type: GoodsCategoryType.INTEGRAL,
      isMenu: false,
      disabled: true,
      prefix: () =>
        h(
          NImage,
          {
            width: "20",
            height: "20",
            previewDisabled: true,
            src: Folder,
            lazy: true,
          },
          {},
        ),
      children: [],
    },
    {
      key: String(GoodsCategoryType.INTEGRALLABEL),
      label: "标签",
      type: GoodsCategoryType.INTEGRALLABEL,
      isMenu: false,
      disabled: true,
      prefix: () =>
        h(
          NImage,
          {
            width: "20",
            height: "20",
            previewDisabled: true,
            src: Folder,
            lazy: true,
          },
          {},
        ),
      children: [],
    },
  ];

  /** 树形数据 */
  const treeData = ref<Array<TreeOption & { isMenu: boolean }>>(getInitialTreeData());

  /** 处理数据 */
  const handleData = (
    dataList: Array<ApiSalesManagement.IntegralGoodsClass & ApiSalesManagement.PointSift>,
    treeData: Array<
      TreeOption & { isMenu: boolean } & Partial<ApiStoreModule.GoodsClassification & ApiSalesManagement.PointSift>
    >,
  ) => {
    const newTreeData = deepClone(treeData);
    dataList.forEach(item => {
      // 商品分类（积分商品）
      if (item.type === GoodsCategoryType.INTEGRAL) {
        const drugTree = newTreeData.find(tree => tree.key == GoodsCategoryType.INTEGRAL);
        if (drugTree) {
          drugTree.children.push({
            key: item.id,
            label: item.name,
            isMenu: true,
            prefix: () =>
              h(
                NImage,
                {
                  width: "20",
                  height: "20",
                  previewDisabled: true,
                  src: transformMinioSrc(item.iconPath),
                },
                {},
              ),
            ...item,
          });
        }
      }
      // 积分商品标签（另一个接口处理条件）
      else if ("siftName" in item) {
        const therapyTree = newTreeData.find(tree => tree.key == GoodsCategoryType.INTEGRALLABEL);
        if (therapyTree) {
          therapyTree.children.push({
            key: item.id,
            label: item.siftName,
            isMenu: true,
            type: GoodsCategoryType.INTEGRALLABEL,
            ...item,
          });
        }
      }
    });
    return newTreeData;
  };

  /** 获取积分商品分类数据 */
  const getIntegralGoodsClassData = async (isRefresh = false) => {
    try {
      startLoading();
      _params.data.name = searchValue.value;

      // 如果是刷新操作，重置分页和树数据
      if (isRefresh) {
        _params.pageVO.current = 1;
        treeData.value = getInitialTreeData();
      }

      const { total, current, size, records } = await getGoodsClassificationPage(_params);
      const pointsLabelList = await getAllPointSift({});
      _params.pageVO.current = Number(current);
      _params.pageVO.size = Number(size);
      recordsTotal = Number(total);

      treeData.value = handleData([...records, ...pointsLabelList], treeData.value);

      // 友情提示
      if (searchValue.value && !records.length) {
        message.createMessageInfo(`不存在《${searchValue.value}》积分商品分类！`);
      }
    } catch (err) {
      message.createMessageError("获取积分商品分类或标签列表失败: " + err);
    } finally {
      endLoading();
    }
  };

  /** 滚动加载 */
  const handleScroll = e => {
    const currentTarget = e.currentTarget as HTMLElement;
    if (currentTarget.scrollTop + currentTarget.offsetHeight >= currentTarget.scrollHeight) {
      if (_params.pageVO.current * _params.pageVO.size < recordsTotal) {
        _params.pageVO.current++;
        getIntegralGoodsClassData();
      }
    }
  };

  /** 刷新 */
  const refresh = () => {
    getIntegralGoodsClassData(true); // 传入true表示是刷新操作
  };

  return {
    isGetIntegralLoading,
    searchValue,
    getIntegralGoodsClassData: () => getIntegralGoodsClassData(false),
    refresh,
    handleScroll,
    treeData,
    selectedKeys,
  };
}
