import {defHttp} from '@/services';
import { basicPlatformUrl } from '@/utils/http/urlUtils';

export const enum AccountApi {
  basicLogin = '/customerEntity/login',       // 于1.1.1版本更改为/userEntity/login
  userLogin = '/userEntity/login', 
  doctorLogin = '/userEntity/doctorLogin',
  modifyPassword = '/userEntity/modifyPassword',
  update = "/customerEntity/update/password",  // 于1.1.1版本更改为/userEntity/update/password
  updatePassword = "/userEntity/update/password"
}

export interface LoginParams {
  username: string;
  password: string;
}

export function userLogin({username: account, password: pwd}: LoginParams) {
  return defHttp.post({
    url: AccountApi.userLogin,
    params: {
      data: {account, pwd},
    },
    requestConfig: {
      withToken: false,
    },
  });
}

export function doctorLogin({username: account, password: pwd}: LoginParams) {
  return defHttp.post({
    url: AccountApi.doctorLogin,
    params: {
      data: {account, pwd},
    },
    requestConfig: {
      withToken: false,
    },
  });
}

export function userModifyPassword({id, password}) {
  return defHttp.put({
    url: AccountApi.modifyPassword,
    params: {
      data: {id: id, pwd: password},
    },
  });
}

/** 修改密码 */
export function userUpdatePassword({userId, firstPassword, secondPassword}) {
  return defHttp.put({
    url: AccountApi.updatePassword,
    params: {
      data: {
        userId, 
        firstPassword, 
        secondPassword
      },
    },
  });
}
