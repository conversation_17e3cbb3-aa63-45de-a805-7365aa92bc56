<template>
 <div class="inner-page-height">
    <FormLayout
        :isLoading="isLoading"
        :tableData="tableData"
        :tableColumns="tableColumns"
        :pagination="paginationRef"
        :isTableSelection="false"
        @paginationChange="paginationChange"
        :isDisplayIndex="false"
        id="distributorManagement"
      >
        <!-- 表单 -->
        <template #searchForm>
            <n-form
              ref="formRef"
              label-placement="left"
              label-width="auto"
              :show-feedback="false"
              require-mark-placement="right-hanging"
              size="small"
              :style="{ width: '100%' }"
            >
             <n-grid cols="4 m:12 l:18 xl:24" :x-gap="32" responsive="screen">
                <!-- 分销员信息 -->
                <n-gi :span="6">
                    <n-form-item label="订单搜索" path="range">
                      <n-input-group>
                        <n-select
                          v-model:value="formValue.state"
                          placeholder="请选择"
                          :options="orderSearchOptions"
                          style="width: 135px;"
                          :render-option="renderOption"
                        />
                        <JSearchInput
                          v-model:value="formValue.name"
                          placeholder="请输入关键字"
                          @search="formSearch"
                          :maxlength="nameLength[formValue.state]"
                          :width="240"
                        />
                      </n-input-group>
                    </n-form-item>
                </n-gi>
                <!-- 状态 -->
                <n-gi :span="4">
                    <n-form-item label="状态">
                        <n-select 
                         v-model:value="formValue.status"
                         :options="CommissionStatusOptions" 
                         placeholder='请选择状态'
                         style="width: 170px;" 
                         clearable
                        />
                    </n-form-item>
                </n-gi>
   
                <!-- 成为分销员时间 -->
                <n-gi :span="6">
                    <n-form-item>
                     <n-input-group>
                         <n-select
                          v-model:value="formValue.timeType"
                          placeholder="请选择"
                          :options="timeTypeOptions"
                          style="width: 135px;"
                        />
                         <j-date-range-picker 
                          style="flex: 1;" 
                          v-model:value="formValue.creationTime"  
                          type="datetimerange" format="yyyy-MM-dd" 
                          :default-time="['00:00:00', '23:59:59']" 
                          clearable />
                     </n-input-group>
                    </n-form-item>
                </n-gi>
             </n-grid>
            </n-form>
        </template>
        <!-- 操作项 -->
        <template #tableHeaderBtn>
          <n-button @click="refresh" class="store-button" :loading="isLoading">刷 新</n-button>
          <n-button v-if="hasStructreCommissionDeatailIndexExportAuth" @click="DataExport" :loading="isExportLoading" type="info" class="store-button">导出</n-button>
        </template>
      </FormLayout>
      <Detail ref="detailShow"/>
 </div>
</template>
<script setup lang="tsx" name="DistributorManagement">
import { ref, onMounted, watch, h, type VNode, reactive } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { CommissionStatusOptions } from "@/constants";
import Detail from './components/detail.vue'
import { distributionDataExport, distributionPage, exportOrderReportsPage } from "@/services/api";
import { NButton, NSpace, NTag, NTooltip, type SelectOption } from "naive-ui";
import { useMessages } from "@/hooks";
import {hasStructreCommissionDeatailIndexExportAuth, hasStructreCommissionDeatailIndexDetailAuth} from '../authList'
import moment from "moment/moment";
const message = useMessages();
const renderOption = ({ node, option }: { node: VNode; option: SelectOption }) =>
    h(NTooltip, null, {
      trigger: () => node,
      default: () => option.label
})
const isExportLoading = ref(false)
/** 定义一个函数来格式化价格 */
const formatPrice = (price) => `${(price / 100).toFixed(2)}`;
   
/** 搜索 */
const formSearch = () =>{
  tableSearch()
}

/** 表格刷新 */
function refresh(){
  tableSearch();
}
const DataExport = () => {
  const _params = {
    data: { ...getParams() },
    pageVO: {
      current: paginationRef.value.current,
      size: paginationRef.value.pageSize
    }
  }
  isExportLoading.value = true
  distributionDataExport(_params).then(() => {
    message.createMessageExportSuccess("导出成功");
  }).catch((err) => {
    message.createMessageError("导出失败: " + err);
  }).finally(() => {
    isExportLoading.value = false
  });
}
/** 获取参数 */
const getParams = () => {
  const data = {
    [formValue.value.state]:formValue.value.name,
    status:formValue.value.status,
  };
  if (formValue.value.timeType === 'creationTime'){
    data["createStartTime"] =  formValue.value.creationTime ? moment(formValue.value.creationTime[0]).format(`YYYY-MM-DD HH:mm:ss`) : null
    data["createEndTime"] = formValue.value.creationTime ? moment(formValue.value.creationTime[1]).format(`YYYY-MM-DD HH:mm:ss`) : null
  }else {
    data["allocateStartTime"] =  formValue.value.creationTime ? moment(formValue.value.creationTime[0]).format(`YYYY-MM-DD HH:mm:ss`) : null
    data["allocateEndTime"] = formValue.value.creationTime ? moment(formValue.value.creationTime[1]).format(`YYYY-MM-DD HH:mm:ss`) : null
  }
  return data
};

/* 表格搜索 */
const tableSearch = () => {
  pageTableData(getParams(), paginationRef.value);
};

/** 分佣状态*/
const distributionStatus = [
  '待结算',
  '已结算',
  '已取消'
]
const formValue = ref({
  status:undefined,
  state:'accountUserName',
  name:'',
  creationTime:null,
  timeType:'creationTime'
})
const nameLength = {
  accountUserName:30,
  accountUserId:19,
  orderCode:19,
  structureName:30
}
/** 表格方法Hook */
const {
  isLoading,
  tableData,
  pageTableData,
  paginationRef,
  paginationChange,
} = useTableDefault({
  pageDataRequest: distributionPage,
});
   
/* 表格列表项 */
const tableColumns = ref([
    {
        title: "分佣单号",
        key: "allocationNo",
        width: 150,
        align: "left"
    },
    {
      title: "分销员昵称/ID",
      key: "accountUserId",
      width: 180,
      align: "left",
      render: (row) => {
        let title = `${row.accountUserName ?? ''}`;
        return <table-tooltip row={row} nameKey="accountUserName" title={title} idKey="accountUserId" />;
      }
    },
    {
        title: "佣金(元)",
        key: "allocationTotalAmount",
        width: 150,
        align: "left",
        render: (row) => {
          return  row.allocationTotalAmount ? formatPrice(row.allocationTotalAmount) : '0.00'
        }
    },
    {
        title: "佣金状态",
        key: "status",
        width: 150,
        align: "left",
        render: (row) => {
          return distributionStatus[row.status]
        }
    },
    {
        title: "结算时间点",
        key: "allocateTime",
        width: 150,
        align: "left"
    },
    {
        title: "订单编号",
        key: "orderCode",
        width: 150,
        align: "left"
    },
    {
        title: "组织名称",
        key: "structureName",
        width: 150,
        align: "left"
    },
    {
        title: "创建时间",
        key: "createTime",
        width: 150,
        align: "left"
    },
    {
        title: "操作",
        key: "action",
        width: 100,
        fixed: "right",
        align: "left",
        render: (row) => {
            return (
                <n-space>
                    {
                      hasStructreCommissionDeatailIndexDetailAuth ? 
                      <n-button
                          text
                          type="primary"
                          onClick={() => clickDetail(row.id)}
                      >
                         详情
                      </n-button> : null
                    }
                </n-space>
            )
        }
    },
]);

/** 详情 */
const detailShow = ref()
const clickDetail = (id) =>{
    detailShow.value.acceptParams(id)
}
   
/** 分销员信息类型 */
const orderSearchOptions = [
  {
    label: "分销员昵称",
    value: "accountUserName",
  },
  {
    label: "分销员id",
    value: "accountUserId",
  },
  {
    label: "订单编号",
    value: "orderCode",
  },
  {
    label: "组织名称",
    value: "structureName",
  }
];

/** 搜索时间类型 */
const timeTypeOptions = [
  {
    label: "创建时间",
    value: "creationTime",
  },
  {
    label: "结算时间",
    value: "settlementTime",
  },
]
/* 组件挂载 */
onMounted(() => {
  tableSearch()
});
/** 监听 */
watch(() =>
    [formValue.value.status,
      formValue.value.creationTime
    ]
  , (newVal) => {
    if (newVal) {
      tableSearch();
    }
  });
</script>

<style scoped lang="less"></style>