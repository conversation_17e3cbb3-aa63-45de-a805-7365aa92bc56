<template>
  <div class="chat">
    <div class="tui-chat">
      <div :class="['tui-chat']">
        <ChatHeader :class="['tui-chat-header']"/>
        <MessageList
            ref="messageListRef"
            :class="['tui-chat-message-list']"
            :isGroup="isGroup"
            :groupID="groupID"
            :isNotInGroup="isNotInGroup"
            :isMultipleSelectMode="isMultipleSelectMode"
            @handleEditor="handleEditor"
            @closeInputToolBar="() => changeToolbarDisplayType('none')"
            @toggleMultipleSelectMode="toggleMultipleSelectMode"
        />
        <MessageInputToolbar
            :class="['tui-chat-message-input-toolbar',]"
            :displayType="inputToolbarDisplayType"
            @insertEmoji="insertEmoji"
            @changeToolbarDisplayType="changeToolbarDisplayType"
            @scrollToLatestMessage="scrollToLatestMessage"
        />
        <MessageInput
            ref="messageInputRef"
            :class="['tui-chat-message-input',]"
            :enableAt="featureConfig.InputMention"
            :isMuted="false"
            muteText="您已被管理员禁言"
            placeholder="请输入消息"
            :inputToolbarDisplayType="inputToolbarDisplayType"
            @changeToolbarDisplayType="changeToolbarDisplayType"
        />
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import {ref, onMounted, onUnmounted, computed, provide, readonly, inject, watch} from 'vue'
import ChatHeader from "./chat-header/index.vue"
import MessageList from './message-list/index.vue'
import type {ToolbarDisplayType} from "@/views/DoctorEndModule/IM/types";
import MessageInputToolbar from '@/views/DoctorEndModule/IM/components/JUIChat/message-input-toolbar/index.vue'
import MessageInput from '@/views/DoctorEndModule/IM/components/JUIChat/message-input/index.vue'
import type {ExtensionInfo} from "@tencentcloud/tui-core";
import TUIChatConfig from '@/views/DoctorEndModule/IM/utils/config'
import {
  type IConversationModel,
  type IMessageModel,
  StoreName, TUIChatEngine,
  TUIConversationService,
  TUIStore
} from "@tencentcloud/chat-uikit-engine";
import TUICore, {TUIConstants} from "@tencentcloud/tui-core";

const isUniFrameWork = false

const emits = defineEmits(['closeChat']);

const groupID = ref(undefined);
const isGroup = ref(false);
const isNotInGroup = ref(false);
const notInGroupReason = ref<number>();
const currentConversationID = ref();
const isMultipleSelectMode = ref(false);
const inputToolbarDisplayType = ref<ToolbarDisplayType>('none');
const messageInputRef = ref();
const messageListRef = ref<InstanceType<typeof MessageList>>();
const headerExtensionList = ref<ExtensionInfo[]>([]);
const featureConfig = TUIChatConfig.getFeatureConfig();

const {tabName} = inject('tabName'); // 当前的TabName,值为 processing | completed
const {currentConversationData, changeCurrentConversation} = inject('currentConversationData');

// 监听当前的会话是不是有变更
watch(currentConversationData, (newValue) => {
  // currentConversationData的conversationId是我们系统自定义的会话id
  if (newValue?.conversationId) {
    // currentConversationID.value = newValue.conversationId
    // 设置IM的currentConversationID
    currentConversationID.value = `C2C${newValue.contactImUserId}`
  }
})


onMounted(() => {
  // TUIStore.watch(StoreName.CONV, {
  //   currentConversation: onCurrentConversationUpdate,
  // });
});

onUnmounted(() => {
  reset();
});

const isInputToolbarShow = computed<boolean>(() => {
  return isUniFrameWork ? inputToolbarDisplayType.value !== 'none' : true;
});

const leaveGroupReasonText = computed<string>(() => {
  let text = '';
  switch (notInGroupReason.value) {
    case 4:
      text = '您已被管理员移出群聊';
      break;
    case 5:
      text = '该群聊已被解散';
      break;
    case 8:
      text = '您已退出该群聊';
      break;
    default:
      text = '您已退出该群聊';
      break;
  }
  return text;
});

const reset = () => {
  TUIConversationService.switchConversation('');
};

const closeChat = (conversationID: string) => {
  emits('closeChat', conversationID);
  reset();
};

const insertEmoji = (emojiObj: object) => {
  messageInputRef.value?.insertEmoji(emojiObj);
};

const handleEditor = (message: IMessageModel, type: string) => {
  if (!message || !type) return;
  switch (type) {
    case 'reference':
      // todo
      break;
    case 'reply':
      // todo
      break;
    case 'reedit':
      if (message?.payload?.text) {
        messageInputRef?.value?.reEdit(message?.payload?.text);
      }
      break;
    default:
      break;
  }
};

const handleGroup = () => {
  headerExtensionList.value[0].listener.onClicked({groupID: groupID.value});
};

function changeToolbarDisplayType(type: ToolbarDisplayType) {
  inputToolbarDisplayType.value = inputToolbarDisplayType.value === type ? 'none' : type;
}

function scrollToLatestMessage() {
  messageListRef.value?.scrollToLatestMessage();
}

function toggleMultipleSelectMode(visible?: boolean) {
  isMultipleSelectMode.value = visible === undefined ? !isMultipleSelectMode.value : visible;
}

function mergeForwardMessage() {
  messageListRef.value?.mergeForwardMessage();
}

function oneByOneForwardMessage() {
  messageListRef.value?.oneByOneForwardMessage();
}

function updateUIUserNotInGroup(conversation: IConversationModel) {
  if (conversation?.operationType > 0) {
    headerExtensionList.value = [];
    isNotInGroup.value = true;
    /**
     * 4 - be removed from the group
     * 5 - group is dismissed
     * 8 - quit group
     */
    notInGroupReason.value = conversation?.operationType;
  } else {
    isNotInGroup.value = false;
    notInGroupReason.value = undefined;
  }
}

function onCurrentConversationUpdate(conversation: IConversationModel) {
  updateUIUserNotInGroup(conversation);
  // return when currentConversation is null
  if (!conversation) {
    return;
  }
  // return when currentConversationID.value is the same as conversation.conversationID.
  if (currentConversationID.value === conversation?.conversationID) {
    return;
  }

  isGroup.value = false;
  let conversationType = TUIChatEngine.TYPES.CONV_C2C;
  const conversationID = conversation.conversationID;
  if (conversationID.startsWith(TUIChatEngine.TYPES.CONV_GROUP)) {
    conversationType = TUIChatEngine.TYPES.CONV_GROUP;
    isGroup.value = true;
    groupID.value = conversationID.replace(TUIChatEngine.TYPES.CONV_GROUP, '');
  }

  headerExtensionList.value = [];
  isMultipleSelectMode.value = false;
  // Initialize chatType
  TUIChatConfig.setChatType(conversationType);
  // While converstaion change success, notify callkit and roomkit、or other components.
  TUICore.notifyEvent(TUIConstants.TUIChat.EVENT.CHAT_STATE_CHANGED, TUIConstants.TUIChat.EVENT_SUB_KEY.CHAT_OPENED, {groupID: groupID.value});
  // The TUICustomerServicePlugin plugin determines if the current conversation is a customer service conversation, then sets chatType and activates the conversation.
  TUICore.callService({
    serviceName: TUIConstants.TUICustomerServicePlugin.SERVICE.NAME,
    method: TUIConstants.TUICustomerServicePlugin.SERVICE.METHOD.ACTIVE_CONVERSATION,
    params: {conversationID: conversationID},
  });
  // When open chat in room, close main chat ui and reset theme.
  if (TUIChatConfig.getChatType() === TUIConstants.TUIChat.TYPE.ROOM) {
    if (TUIChatConfig.getFeatureConfig(TUIConstants.TUIChat.FEATURE.InputVoice) === true) {
      TUIChatConfig.setTheme('light');
      currentConversationID.value = '';
      return;
    }
  }
  // Get chat header extensions
  if (TUIChatConfig.getChatType() === TUIConstants.TUIChat.TYPE.GROUP) {
    headerExtensionList.value = TUICore.getExtensionList(TUIConstants.TUIChat.EXTENSION.CHAT_HEADER.EXT_ID);
  }
  TUIStore.update(StoreName.CUSTOM, 'activeConversation', conversationID);
  currentConversationID.value = conversationID;
}

</script>

<style scoped lang="less">
.chat {
  display: block;
  height: 100%;
  overflow: hidden;

  .tui-chat {
    width: 100%;
    height: 100%;
    max-width: 100%;
    overflow: hidden;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    position: relative;

    .tui-chat-header {
      padding: 10px;
      box-sizing: border-box;
      display: flex;
      border-bottom: 1px solid #f4f5f9;
    }

    &-default {
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      display: flex;
    }

    &-header {
      padding: 10px;
      box-sizing: border-box;
      display: flex;
    }

    &-message-list {
      flex: 1;
      overflow: hidden;
      display: flex;
    }

    &-leave-group {
      font-size: 14px;
      height: 160px;
      border-top: 1px solid #efefef;
      justify-content: center;
      align-items: center;

      &-mobile {
        height: 50px;
      }
    }

    &-message-input {
      height: 160px;
      display: flex;
    }
  }
}

</style>
