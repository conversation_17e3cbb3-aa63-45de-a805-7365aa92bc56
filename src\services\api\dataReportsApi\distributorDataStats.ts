import { defHttp } from "@/services";

/** 分销商数据统计 */
export const enum DistributorDataStatsApi {
    /** 分页查询分销商数据统计 */
    page = "/storeDailyReport/dealerPageList",
    /** 导出分销商数据统计 */
    export = "/storeDailyReport/dealerExport",
}

/** 获取分销商数据统计 */
export function getDistributorDataStatsPage(params) {
    return defHttp.post({
        url: DistributorDataStatsApi.page,
        params,
    });
}

/** 分销商数据统计导出 */ 
export function distributorDataStatsExport(params) {
    return defHttp.post({
        url: DistributorDataStatsApi.export,
        requestConfig: {
            responeseType: "stream",
        },
        params,
    });
}