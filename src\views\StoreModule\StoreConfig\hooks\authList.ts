import { OrderStoreconfigAuth, StoreOperationFlowConfigAuth } from "@/enums/authKeys";
import { hasAuth } from "@/utils/auth/authUtils";


/** 轮播图 */
export const hasOrderStoreconfigCarousel = function(){
    return hasAuth(OrderStoreconfigAuth.orderStoreconfigCarousel.key);
}()

/** 轮播图新建 */
export const hasOrderStoreconfigCarouselNew = function(){
    return hasAuth(OrderStoreconfigAuth.orderStoreconfigCarouselNew.key);
}()

/** 轮播图编辑 */
export const hasOrderStoreconfigCarouselEdit = function(){
    return hasAuth(OrderStoreconfigAuth.orderStoreconfigCarouselEdit.key);
}()

/** 轮播图删除 */
export const hasOrderStoreconfigCarouselDelete = function(){
    return hasAuth(OrderStoreconfigAuth.orderStoreconfigCarouselDelete.key);
}()

/** 发货工具 */
export const hasOrderStoreconfigDelivery = function(){
    return hasAuth(OrderStoreconfigAuth.orderStoreconfigDelivery.key);
}()

/** 发货工具编辑 */
export const hasOrderStoreconfigDeliveryEdit = function(){
    return hasAuth(OrderStoreconfigAuth.orderStoreconfigDeliveryEdit.key);
}()

/** 发货工具开启/关闭 */
export const hasOrderStoreconfigDeliveryEnable = function(){
    return hasAuth(OrderStoreconfigAuth.orderStoreconfigDeliveryEnable.key);
}()

/** 地址配置 */
export const hasOrderStoreconfigAddress = function(){
    return hasAuth(OrderStoreconfigAuth.orderStoreconfigAddress.key);
}()

/** 设置默认地址 */
export const hasOrderStoreconfigAddressSetDefault = function(){
    return hasAuth(OrderStoreconfigAuth.orderStoreconfigAddressSetDefault.key);
}()

/** 地址配置新建 */
export const hasOrderStoreconfigAddressNew = function(){
    return hasAuth(OrderStoreconfigAuth.orderStoreconfigAddressNew.key);
}()

/** 地址配置编辑 */
export const hasOrderStoreconfigAddressEdit = function(){
    return hasAuth(OrderStoreconfigAuth.orderStoreconfigAddressEdit.key);
}()

/** 地址配置删除 */
export const hasOrderStoreconfigAddressDelete = function(){
    return hasAuth(OrderStoreconfigAuth.orderStoreconfigAddressDelete.key);
}()

/** 交易相关 */
export const hasOrderStoreconfigTransaction = function(){
    return hasAuth(OrderStoreconfigAuth.orderStoreconfigTransaction.key);
}()

/** 交易相关编辑 */
export const hasOrderStoreconfigTransactionEdit = function(){
    return hasAuth(OrderStoreconfigAuth.orderStoreconfigTransactionEdit.key);
}()

/** 开发配置 */
export const hasOrderStoreconfigDevelopment = function(){
    return hasAuth(OrderStoreconfigAuth.orderStoreconfigDevelopment.key);
}()

/** 开发配置编辑 */
export const hasOrderStoreconfigDevelopmentEdit = function(){
    return hasAuth(OrderStoreconfigAuth.orderStoreconfigDevelopmentEdit.key);
}()

/** 开发配置清除缓存 */
export const hasOrderStoreconfigDevelopmentClearRedisCache = function(){
    return hasAuth(OrderStoreconfigAuth.orderStoreconfigDevelopmentClearRedisCache.key);
}()

/** 首页Logo */
export const hasOrderStoreconfigHomelogo = function(){
    return hasAuth(OrderStoreconfigAuth.orderStoreconfigHomelogo.key);
}()

/** 小程序导航 */
export const hasOrderStoreconfigMiniprogramnavigation = function(){
    return hasAuth(OrderStoreconfigAuth.orderStoreconfigMiniprogramnavigation.key);
}()

/** 小程序导航新建 */
export const hasOrderStoreconfigMiniprogramnavigationNew = function(){
    return hasAuth(OrderStoreconfigAuth.orderStoreconfigMiniprogramnavigationNew.key);
}()

/** 小程序导航编辑 */
export const hasOrderStoreconfigMiniprogramnavigationEdit = function(){
    return hasAuth(OrderStoreconfigAuth.orderStoreconfigMiniprogramnavigationEdit.key);
}()

/** 小程序导航删除 */
export const hasOrderStoreconfigMiniprogramnavigationDelete = function(){
    return hasAuth(OrderStoreconfigAuth.orderStoreconfigMiniprogramnavigationDelete.key);
}()

/** 业务流程-新建 */
export const hasOperationFlowPageAdd = function(){
    return hasAuth(StoreOperationFlowConfigAuth.storeConfigOperationFlowAdd.key);
}()

/** 业务流程-编辑 */
export const hasOperationFlowPageEdit = function(){
    return hasAuth(StoreOperationFlowConfigAuth.storeConfigOperationFlowEdit.key);
}()

/** 业务流程-删除*/
export const hasOperationFlowPageDelete = function(){
    return hasAuth(StoreOperationFlowConfigAuth.storeConfigOperationFlowDelete.key);
}()