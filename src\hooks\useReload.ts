import { nextTick } from 'vue';
import { useBoolean } from '@/hooks';


export default function useReload() {
  let timer = null;
  
  // 重载的标志
  const { bool: reloadFlag, setTrue, setFalse } = useBoolean(true);

  /**
   * 触发重载
   * @param duration - 延迟时间(ms)
   */
  async function handleReload(duration = 0) {
    setFalse();
    await nextTick().catch((error) => {
      // 处理或者抛出 nextTick 的错误
      console.error(error);
    });

    if (duration > 0) {
      // 清除前一个定时器
      if (timer) {
        clearTimeout(timer);
      }

      timer = setTimeout(() => {
        setTrue();
      }, duration);
    }
  }

  return {
    reloadFlag,
    handleReload
  };
}
