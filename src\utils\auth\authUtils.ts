import { RoutesName } from "@/enums/routes";
import { SpecialRouteModule } from "@/router/config/special.modules";
import { useUserStoreWithoutSetup } from "@/stores/modules/user";
import { isArray } from "@/utils/isUtils";
import type { MenuAuthDataResponseItem, MenuAuthTableDataMenuTypeItem, MenuAuthTableDataItem, MenuAuthTableDataOptTypeItem, MenuAuthDataRecord } from "./type";
import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
import { SystemStoreType } from "@/enums";
import { useSkipAuthCodeList } from "@/hooks/useSkipAuthCodeList";
export function hasAuth(authKey:string | Array<string>):boolean{
    const userStore = useUserStoreWithoutSetup()
    const _authKeysList = userStore.optAuthList || [];
    if(isArray(authKey)){
        let flag = true;
        for(let i =0;i<authKey.length;i++){
            const _key = authKey[i]
            if(!_authKeysList.includes(_key)){
                flag = false;
                break;
            }
        }
        return flag;
    }
    else{
        return _authKeysList.includes(authKey)
    }
}

function setCodeIdMap(codeMap:Record<string,Array<string>>,code:string,id:string){
    const _temp  = codeMap[code] 
    if(isArray(_temp)){
        !_temp.includes(id) && _temp.push(id)
    }
    else{
        codeMap[code] = [id]
    }
    codeMap[id] = [code]
}

export function formatTableDataFromRawResponseData(responseTableData:Array<MenuAuthDataResponseItem>):{
    tableData:Array<MenuAuthTableDataItem>,
    record:MenuAuthDataRecord,
    codeMap:Record<string,Array<string>>
}{
    const {getAuthRoleTableSkipList} = useSkipAuthCodeList()
    const skipCodeList = getAuthRoleTableSkipList()
    const tableData = []
    const record:MenuAuthDataRecord = {}
    const codeMap:Record<string,Array<string>> = {}
    responseTableData.forEach(typeItem=>{
        //顶级导航
        if(!skipCodeList.includes(typeItem.code)){
        const tableModuleType:MenuAuthTableDataItem = {
            id:typeItem.id,
            code:typeItem.code,
            moduleType:typeItem.name,
            type:1
        }
        setCodeIdMap(codeMap,typeItem.code,typeItem.id)
        if(typeItem.children && typeItem.children.length){
            tableModuleType.children = []
            //左侧导航
            typeItem.children.forEach(moduleItem=>{
                if(!skipCodeList.includes(moduleItem.code)){
                const tableModuleNameItem = {
                    id:moduleItem.id,
                    code:moduleItem.code,
                    type:2,
                    moduleName:moduleItem.name
                }
                setCodeIdMap(codeMap,moduleItem.code,moduleItem.id)
                if(moduleItem.children && moduleItem.children.length){
                    tableModuleNameItem.children = []
                    moduleItem.children.forEach(menuItem=>{
                        setCodeIdMap(codeMap,menuItem.code,menuItem.id)
                        const _index = tableModuleNameItem.children.findIndex(item=>item.code === menuItem.code)
                        if(_index == -1 && !skipCodeList.includes(menuItem.code)){
                            const tableMenuNameItem={
                                id:menuItem.id,
                                code:menuItem.code,
                                type:3,
                                menuName:menuItem.name
                            }
                            if(menuItem.children && menuItem.children.length){
                                
                                tableMenuNameItem.authList = []
                                menuItem.children.forEach(optItem=>{
                                    setCodeIdMap(codeMap,optItem.code,optItem.id)
                                    const _index = tableMenuNameItem.authList.findIndex(item=>item.code === optItem.code)
                                    if(_index == -1){
                                        const tableOptItem={
                                            id:optItem.id,
                                            code:optItem.code,
                                            authName:optItem.name,
                                            type:4,
                                        }
                                        tableMenuNameItem.authList.push(tableOptItem)
                                            record[optItem.code] = {
                                                id:optItem.id,
                                                code:optItem.code,
                                                name:optItem.name,
                                                type:optItem.type,
                                                isCascade:false,
                                                parentCode:menuItem.code,
                                            }
                                    }
                                })
                            }
                            tableModuleNameItem.children.push(tableMenuNameItem)
                            record[menuItem.code] = {
                                id:menuItem.id,
                                code:menuItem.code,
                                name:menuItem.name,
                                type:menuItem.type,
                                isCascade:true,
                                parentCode:moduleItem.code,
                                childrenCodeList: tableMenuNameItem.authList?.length? tableMenuNameItem.authList.map(children=>children.code):undefined,
                            }
                        }
                    })
                }
                tableModuleType.children.push(tableModuleNameItem)
                record[moduleItem.code] = {
                    id:moduleItem.id,
                    code:moduleItem.code,
                    name:moduleItem.name,
                    type:moduleItem.type,
                    isCascade:true,
                    parentCode:typeItem.code,
                    childrenCodeList: tableModuleNameItem.children?.length? tableModuleNameItem.children.map(children=>children.code):undefined,
                }
                }
            })
        }
        tableData.push(tableModuleType)
        record[typeItem.code] = {
            id:typeItem.id,
            code:typeItem.code,
            name:typeItem.name,
            type:typeItem.type,
            isCascade:true,
            childrenCodeList:tableModuleType.children?.length?tableModuleType.children.map(children=>children.code):undefined,
        }
    }
    })
    console.log(record);
    return {
        tableData,
        record,
        codeMap
    }
}

function getAllChildrenCodeList(record:MenuAuthDataRecord,code:string):Array<string>{
    const _record = record[code]
    const _keyList = []
    if(_record.childrenCodeList && _record.childrenCodeList.length){
        _record.childrenCodeList.forEach(childrenCode=>{
            _keyList.push(childrenCode)
            if(record[childrenCode] && record[childrenCode].childrenCodeList && record[childrenCode].childrenCodeList.length){
                _keyList.push(...getAllChildrenCodeList(record,childrenCode))
            }
        })
    }
    return _keyList
}
function getAllParentsCodeList(record:MenuAuthDataRecord,code:string):Array<string>{
    const _record = record[code]
    const _keyList = []
    const parentCode = _record.parentCode || null
    if(parentCode){
        _keyList.push(parentCode)
        _keyList.push(...getAllParentsCodeList(record,parentCode))
    }
    return _keyList
}

export function getRecordAllParentsAndChildrenCodeListByCode(record:MenuAuthDataRecord,code:string):{
    allParentsCodeList:Array<string>,
    allChildrenCodeList:Array<string>,
    totalCodeList:Array<string>
}{
    const allParentsCodeList = getAllParentsCodeList(record,code)
    const allChildrenCodeList = getAllChildrenCodeList(record,code)
    return {
        allParentsCodeList,
        allChildrenCodeList,
        totalCodeList:[...allParentsCodeList,...allChildrenCodeList]
    }
}

export type AuthItemType={
    "code": string,
    "name":string,
    "children"?: Array<AuthItemType>,
    "type": 1 | 2 | 3,
}

export function authCodeListToAuthTree(authCodeList:Array<string>,authTreeList:Array<MenuAuthDataResponseItem>):Array<AuthItemType>{
    const authTree:Array<AuthItemType> = []
    authTreeList.forEach(item=>{
        const {code,name,level:type,childrenList} = item 
        if(authCodeList.includes(code)){
            const _module:AuthItemType = {
                code,
                name,
                type,
            }
            if(isArray(childrenList) && childrenList.length){
                _module.children = authCodeListToAuthTree(authCodeList,childrenList)
            }
            authTree.push(_module)
        }
    })
    return authTree
}


export function authTreeToRouteConfigAndOptAuthList(treeList:Array<MenuAuthDataResponseItem>):{
    routeConfig:Array<any>,
    optAuthList:Array<string>
}{
    const routeConfig:Array<any> = []
    const optAuthList:Array<string> = []
    const {getMarketPlaceTypeSkipList} = useSkipAuthCodeList()
    const skipCodeList = getMarketPlaceTypeSkipList()
    treeList.forEach(auth=>{
        const {code,children} = auth
        if(!skipCodeList.includes(code)){
        //type = 1
        if(SpecialRouteModule[code]){
            routeConfig.push(SpecialRouteModule[code])
        }
        else { 
            const moduleConfig = {
                name:code
            }
            if(isArray(children) && children.length){
                moduleConfig['children'] = []
              
                children.forEach(childrenItem=>{
                    //type = 2
                    const {code,children} = childrenItem
                    if(!skipCodeList.includes(code)){
                        const menuConfig = {
                            name:code
                        }
                        if(isArray(children) && children.length){
                           //type = 3
                           children.forEach(childrenItem=>{
                            if(!skipCodeList.includes(childrenItem.code)){
                                if(isArray(childrenItem.children) && childrenItem.children.length){
                                    //type = 4
                                   childrenItem.children.forEach(item=>{
                                       if(!optAuthList.includes(item.code) && !skipCodeList.includes(item.code)){
                                           optAuthList.push(item.code)
                                       }
                                   })
                               }
                               if(!optAuthList.includes(childrenItem.code)){
                                   optAuthList.push(childrenItem.code)
                               }
                            }
                           
                            })
                        }
                        if(SpecialRouteModule[code]){
                            moduleConfig['children'].push(SpecialRouteModule[code])
                        }
                        else{
                            moduleConfig['children'].push(menuConfig)
                        }
                    }
                  
                })
            }
            routeConfig.push(moduleConfig)
        }
    }
    })
    return {
        routeConfig:[{
            name: RoutesName.Root,
            children:routeConfig
        }],
        optAuthList
    }
}