/**
 * @description 经销商或群管角色配置说明
 */
export const POINTSRULE = [
  {
    topNavigation: '商城',
    leftNavigation: '首页',
    menu: '首页',
    explain: '勾选菜单【首页】，经销商或群管可以查看各自待处理订单数、待处理售后单数、客户数、订单数和平台商品数。'
  },
  {
    topNavigation: '商城',
    leftNavigation: '会员管理',
    menu: '会员管理',
    explain: '勾选菜单【会员管理】，经销商或群管可以查看各自的会员信息。'
  },{
    topNavigation: '商城',
    leftNavigation: '订单管理',
    menu: '订单管理',
    explain: '勾选菜单【订单管理】，经销商或群管可以查看各自的订单。'
  },{
    topNavigation: '商城',
    leftNavigation: '售后管理',
    menu: '售后管理',
    explain: '勾选菜单【售后管理】，经销商或群管可以查看各自的退款申请单。'
  },{
    topNavigation: '商城',
    leftNavigation: '数据报表',
    menu: '全平台订单',
    explain: '勾选菜单【全平台订单】，经销商或群管能看到名下订单数和订单总金额等统计数据。'
  },{
    topNavigation: '商城',
    leftNavigation: '数据报表',
    menu: '会员消费榜',
    explain: '勾选菜单【会员消费榜】，经销商或群管能看到名下会员的订单数和订单总金额等统计数据。'
  },{
    topNavigation: '商城',
    leftNavigation: '数据报表',
    menu: '社群群管销量',
    explain: '勾选菜单【社群群管销量】，经销商能看到名下群管的订单数和订单总金额等统计数据。'
  },{
    topNavigation: '商城',
    leftNavigation: '数据报表',
    menu: '社群经销商销量',
    explain: '勾选菜单【社群经销商销量】，上级经销商（如省代）可以查看名下经销商的订单数和订单总金额等统计数据。'
  },{
    topNavigation: '商城',
    leftNavigation: '数据报表',
    menu: '商品销售榜',
    explain: '勾选菜单【商品销售榜】，经销商或群管能看到各自产品订单数和订单总金额等统计数据。'
  },{
    topNavigation: '商城',
    leftNavigation: '数据报表',
    menu: '社群课程销量',
    explain: '勾选菜单【社群课程销量】，经销商或群管能看到每个课程带来的订单数和订单总金额等统计数据。'
  },
  {
    topNavigation: '商城',
    leftNavigation: '财务管理',
    menu: '分账方管理',
    explain: '勾选菜单【分账方管理】，经销商可以看到名下所有分账单。群管无法看见任何经销商的分账单。'
  },
  {
    topNavigation: '商城',
    leftNavigation: '财务管理',
    menu: '分账方结算',
    explain: '勾选菜单【分账方结算】，经销商可以看到名下所有分账单的打款状态。群管无法看见任何经销商的分账单的打款状态。'
  },
];

/** 积分相关规则表单项 */
export const COLUMNS = [
  {
    title: '顶级导航',
    key: 'topNavigation',
    resizable: true,
    rowSpan: (rowData, rowIndex) => (12),
  },
  {
    title: '左侧导航',
    key: 'leftNavigation',
    resizable: true,
    rowSpan: (rowData, rowIndex) => (
      rowIndex == 4 ? 6 : rowIndex == 10 ? 2 : 1
    ),
  },
  {
    title: '菜单',
    key: 'menu',
    resizable: true,

  },
  {
    title: '说明',
    key: 'explain',
    width: 300,
    fixed: "right",
    resizable: true,
  }
];