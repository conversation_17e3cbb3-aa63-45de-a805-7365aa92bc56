<template>
  <JSelect
    :value="props.value"
    :loading="isLoading"
    :onFocus="handlerFocus"
    :options="selectListDataRef"
    :onClear="handleClear"
    @update:value="onChange"
    placeholder="请选择福利券种类"
    :filter="customFilter"
  />
</template>

<script setup lang="ts">
import { toRefs } from "vue";
/** 相关组件 */
import JSelect from "@/components/JSelect/index.vue";

defineOptions({
    name: "JWelfareList",
});

/* Props */
const props = withDefaults(
    defineProps<{
        value?: Array<string | number> | string | number | null;
        selectListData?: Array<{
            id: string | number;
            name: string;
        }>;
        loading?: boolean;
    }>(),{
       selectListData: () => [],
       loading: false,
    },
);

/** emits */
const emits = defineEmits<{
    (e: "update:value", selectValue: any): void;
    (e: "focus"): void;
}>();

const { selectListData: selectListDataRef, loading: isLoading } = toRefs(props);

/** 获取焦点 */
function handlerFocus() {
    if (!selectListDataRef.value.length) {
        emits("focus");
    }
};

/** 前端过滤 */
const customFilter = (keyword, options) => {
    const labelMatch = options.label
        .toLowerCase()
        .includes(keyword.toLowerCase());
    return labelMatch;
};

/** 选中值回调 */
function onChange(value) {
  emits("update:value", value);
}

/** 清空 */
const handleClear = () => {
    emits("update:value", null);
};
</script>

<style scoped lang="less"></style>
