import { createRouter, createWebHashHistory, type Router } from "vue-router";
import { routes } from "@/router/routes";
import { clearStorage } from "@/utils/cache/storageCache";
let router: Router;
try {
  router = createRouter({
    history: createWebHashHistory(import.meta.env.BASE_URL),
    routes: routes,
  });
} catch (e) {
  clearStorage();
  location.href = location.origin;
}

export const setupRouter = app => {
  app.use(router);
};
