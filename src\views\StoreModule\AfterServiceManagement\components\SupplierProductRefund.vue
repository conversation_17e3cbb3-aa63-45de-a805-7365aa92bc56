<template>
  <n-modal
    v-model:show="showModal"
    :auto-focus="false"
    style="width: 400px"
    :bordered="false"
    size="small"
    @after-leave="closeModal"
    preset="card"
    :closable="false"
  >
    <n-space justify="space-around" style="margin-bottom: 10px">
      <span class="title">同意退货退款？</span>
    </n-space>
    <n-spin :show="isLoading" >
        <n-space>
      <JFieldItem label="供应商名称"></JFieldItem>
      <JFieldItem label="联系人姓名"></JFieldItem>
      <JFieldItem label="供应商电话"></JFieldItem>
      <JFieldItem label="供应商地址">{{ supplierAddress.address }}</JFieldItem>
    </n-space>
    </n-spin>
    
    <template #footer>
      <n-space justify="end">
        <n-button @click="closeModal" class="store-button">取消</n-button>
        <n-button @click="handleConfirm" type="primary" class="store-button" : >确定</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import {getSupplierAddress } from '@/services/api';
import { afterSaleRecordDoAction} from '@/services/api/afterServiceApi';
import { useMessages } from "@/hooks";
const { createMessageSuccess, createMessageError } = useMessages();

const props = withDefaults(defineProps<{
  show: boolean;
  info?: any;
}>(), {
  show: false,
});

const emits = defineEmits<{
  (e: 'update:show', value: boolean): void;
  (e: 'success'): void;
}>();

const showModal = computed({
  get() {
    return props.show;
  },
  set(value) {
    emits("update:show", value);
  },
});

/** 获取供应商地址 */
const isLoading = ref(false);

const supplierAddress = ref<any>({});
const getSupplierAddressInfo = async () => {
    isLoading.value = true;
    try {
      const data = await getSupplierAddress(props.info.supplierId);
      supplierAddress.value = data;
    } catch (err) {
      createMessageError("获取供应商地址失败: " + err);
    } finally {
      isLoading.value = false;
    }
  };

  watch (
    () => props.show,
    (newVal, oldVal) => {
      if (newVal) {
        getSupplierAddressInfo();
      }
    },
    {
      immediate: true
    }
  );

/** 确认退货退款 */
const isConfirmLoading = ref(false);
const handleConfirm = async () => {
    isConfirmLoading.value = true;
    try {
        const params = {
          data: {
            recordNo: props.info.recordNo,
            action: 2,
          },
        };
        await afterSaleRecordDoAction(params);
        createMessageSuccess("操作成功");
        emits("success");
        closeModal();
      } catch (err) {
        createMessageError("操作失败: " + err);
      } finally {
        isConfirmLoading.value = false;
      }
  };


const closeModal = () => {
  showModal.value = false;
};
</script>
<style scoped lang="less">
.title {
  font-size: 20px;
  font-weight: 700;
}
</style>
