<template>
    <JDrawer v-bind="$attrs" title="查看详情" :isGetLoading="loadShow" to="#VideoManagement" :contents-list="[
        {
            name: '账号信息',
            slotName: 'authorInfo'
        }, {
            name: '视频信息',
            slotName: 'videoInfo'
        }
    ]"
    >
        <template #authorInfo>
            <n-grid cols="6 m:12 l:18 xl:24" :x-gap="24" responsive="screen">
                <n-gi :span="6">
                    <div class="infoItem">
                        <span class="lable">用户头像：</span>
                        <JImage round :imgPath="videoDetailRef.customerImg" height="50" width="50" />
                    </div>
                    <div class="infoItem"><span class="lable">用户昵称：</span><span>{{ videoDetailRef.customerName }}</span>
                    </div>
                </n-gi>
                <n-gi :span="6">
                    <div class="infoItem">
                        <span class="lable">视频创建时间：</span>
                        <span class="">{{ videoDetailRef.createTime }}</span>
                    </div>
                    <div class="infoItem"><span class="lable">发布状态：</span>
                        <n-tag :bordered="false" size="small" :type="videoDetailRef.state === 1 ? 'success' : 'error'">
                            {{ videoTypeLabel[videoDetailRef.state] }}
                        </n-tag>
                    </div>
                </n-gi>
                <n-gi :span="6">
                    <div class="infoItem">
                        <span class="lable">审核状态：</span>
                        <n-tag :bordered="false" size="small"
                            :type="videoDetailRef.auditState === 1 ? 'success' : 'error'">
                            {{ videoAuditLabel[videoDetailRef.auditState] }}
                        </n-tag>
                    </div>
                    <div class="infoItem"><span class="lable">审核时间：</span><span>{{ videoDetailRef.auditTime }}</span>
                    </div>
                    <div class="infoItem"><span class="lable">审核人：</span><span>{{ videoDetailRef.account }}</span></div>
                </n-gi>
            </n-grid>
        </template>
        <template #videoInfo>

            <div class="videoInfo">
                <videoPreView :src="videoDetailRef.path" height="250px" width="250px"></videoPreView>
                <div>
                    <div class="infoItem"><span class="lable">作品描述：</span>
                        <div class="description">{{ videoDetailRef.description || '-' }}</div>
                    </div>
                    <div class="infoItem"><span class="lable">评论数：</span><span>{{ videoDetailRef.commentsCount }}
                            <n-button text type="info" @click="showCommentModal = true" >
                                查看评论
                            </n-button> </span>
                    </div>
                    <div class="infoItem"><span class="lable">点赞数：</span><span>{{ videoDetailRef.likesCount }}</span>
                    </div>
                    <div class="infoItem"><span class="lable">收藏数：</span><span>{{ videoDetailRef.favoritesCount
                            }}</span>
                    </div>
                    <div class="infoItem"><span class="lable">转发数：</span><span>{{ videoDetailRef.sharesCount }}</span>
                    </div>
                    <div class="infoItem"><span class="lable">挂载商品：</span>
                        <div v-if="videoDetailRef.productVideoDTO" class="goodsInfo">
                            <FormLayout :isLoading="loadShow" :tableData="goodsInfo" :tableColumns="tableColumns"
                                :isNeedCollapse="false" :isBatchDelete="false" :isTableSelection="false"
                                :isTablePagination="false" :isDisplayHeader="false">
                            </FormLayout>

                        </div>
                        <span v-else>-</span>
                    </div>
                </div>
            </div>
        </template>
        <template #footer>
            <div style="width: 100%;">
                <!-- <n-button :type="videoDetailRef.state === 1 ? 'error' : 'info'" @click="_confirm"> {{ videoDetailRef.state == 1 ? '取消发布' : '发布' }} </n-button> -->

                <n-popconfirm @positive-click="_confirm" :positive-button-props="{
                    loading: loadShow
                }" to="#app">
                    <template #trigger>
                        <n-button :type="videoDetailRef.state === 1 ? 'error' : 'info'" :loading="loadShow"> {{
                            videoDetailRef.state == 1 ? '取消发布' : '发布' }} </n-button>
                    </template>
                    {{ `是否${videoDetailRef.state == 1 ? '取消发布' : '发布'}?` }}
                </n-popconfirm>
            </div>
        </template>
    </JDrawer>
    <CommentModal v-model:show="showCommentModal" :videoDetail="videoDetailRef" ></CommentModal>
</template>

<script setup lang="tsx">
import { ref, reactive, toRefs, onMounted, watch, render, computed } from 'vue'
const loadShow = ref(false);
import FormLayout from "@/layout/FormLayout.vue";
import videoPreView from "./VideoPreviewModal/index.vue";
import CommentModal from "./CommentModal/index.vue";
import { videoDetail, type VideoDetail } from "@/services/api";
import { GoodsCategoryType } from "@/enums";
import { closeDrawerEnum } from "../type"
import { useMessages } from "@/hooks";
import { updateType } from '@/services/api';
import { isEmpty } from "@/utils"
const { createMessageSuccess, createMessageError } = useMessages();
import { videoTypeLabel, videoAuditLabel } from "@/constants";
const props = withDefaults(defineProps<{
    videoId: string
}>(), {
    videoId: ''
});
const showCommentModal = ref(false)
const emits = defineEmits<{
    (e: 'closeVideo', type: closeDrawerEnum, id: string): void;
}>()

const goodsInfo = computed(() => {
    return [{
        ...videoDetailRef.value.productVideoDTO,
        id: videoDetailRef.value.productId,
        showGoodsName: videoDetailRef.value.productVideoDTO.type ==
            GoodsCategoryType.DRUG
            ? `[${videoDetailRef.value.productVideoDTO.frontName}]${videoDetailRef.value.productVideoDTO.name}${videoDetailRef.value.productVideoDTO.specName}`
            : videoDetailRef.value.productVideoDTO.frontName
    }]
})

const videoDetailRef = ref<VideoDetail>({} as VideoDetail)

const getVideoDetail = () => {
    loadShow.value = true;
    videoDetail(props.videoId).then((res: VideoDetail) => {
        videoDetailRef.value = res
    }).catch(err => {
        console.log(err, 'err');
        createMessageError(`获取详情失败:${err}`)
    }).finally(() => {
        loadShow.value = false;
    })
}

const _confirm = () => {
    loadShow.value = true;
    // emits('closeVideo',closeDrawerEnum.cancel,props.videoId)
    updateType(props.videoId, videoDetailRef.value.state == 1 ? 2 : 1).then(res => {
        createMessageSuccess(videoDetailRef.value.state == 1 ? '取消发布成功' : '发布成功');
        videoDetailRef.value.state = videoDetailRef.value.state == 1 ? 2 : 1;
    }).catch(err => {
        createMessageError(`${videoDetailRef.value.state == 1 ? '取消发布失败' : '发布失败'}:${err}`)
    }).finally(() => {
        loadShow.value = false;
    })
}

const tableColumns = [
    {
        title: "商品",
        key: "typeName",
        width: 130,
        render: (row) => {
            return <table-tooltip row={row} nameKey="showGoodsName" title={row['showGoodsName']} idKey="id" />;
        }
    },
    {
        title: "价格",
        key: "price",
        width: 100,
        render: (row) => {
            return (row.price / 100).toFixed(2);
        }
    },
    {
        title: "库存",
        key: "availStocks",
        width: 100,
    }]

watch(() => props.videoId, (newV) => {
    if (isEmpty(newV)) return;
    videoDetailRef.value = {} as VideoDetail;
    getVideoDetail()
})



</script>
<style scoped lang="less">
.infoItem {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .lable {
        flex-shrink: 0
    }

    .img {
        width: 80px;
        height: 80px;
        border-radius: 50%;
    }
}

.videoInfo {
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    gap: 10%;

    .infoItem {
        align-items: baseline;
    }

    .description {
        max-width: 60%;
        font-size: 14px;
    }

    .goodsInfo {
        width: 60%;
        height: 130px;
    }
}
</style>