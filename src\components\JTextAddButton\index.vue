<template>
    <div class="button-container" @click="showPlusOne">
        <span v-for="(bubble, index) in bubbles" :key="bubble.id" class="bubble" @animationend="removeBubble(index)">
            +1
        </span>
        <n-button v-bind="$attrs">
            <slot></slot>
        </n-button>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";

defineOptions({ name: 'JTextAddButton' });

/** emits */
const emits = defineEmits<{
    (e: 'click'): void;
}>();

/** 创建上限 */
const CREATEUPPERLIMIT = 5;

const bubbles = ref<{ id: number; }[]>([]);
let bubbleId = 0;

const showPlusOne = () => {
    if (bubbles.value.length >= CREATEUPPERLIMIT) {
        bubbles.value.shift();
    }
    bubbles.value.push({ id: bubbleId++ });
    emits('click');
};

const removeBubble = (index: number) => {
    bubbles.value.splice(index, 1);
};
</script>

<style lang="less" scoped>
.button-container {
    min-width: 68px;
    min-height: 28px;
    border-radius: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    position: relative;
    margin-left: 2px;
    margin-right: 2px;
    padding: 2px 4px;
    &:hover {
        background-color: #F2F3F5;
    }
}

.bubble {
    width: 25px;
    position: absolute;
    top: 0;
    left: 55%;
    transform: translateX(-50%);
    color: #2a77ff;
    opacity: 0;
    transition: opacity 0.5s, top 0.5s;
    z-index: 999999;
}

.bubble {
    animation: bubble-animation 1s forwards;
}

@keyframes bubble-animation {
    0% {
        opacity: 1;
        top: -10px;
    }

    100% {
        opacity: 0;
        top: -50px;
    }
}
</style>
