<template>
  <JModal
    v-model:show="modalVisible"
    title="发放福利券"
    width="1500"
    height="700"
    @after-leave="closeModal"
    :positive-text="null"
    :negative-text="null"
    :isScale="false"
  >
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :isNeedCollapse="false"
      :isTableSelection="false"
      :pagination="paginationRef"
      @paginationChange="paginationChange"
    >
      <template #searchForm>
        <n-form
          ref="formRef"
          :model="model"
          :show-feedback="false"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          size="small"
          :style="{ width: '100%' }"
        >
          <n-form-item label="福利券名称">
            <j-search-input v-model:value.trim="model.name" placeholder="请输入福利券名称" :maxlength="30" @search="tableSearch"/>
          </n-form-item>
          <n-form-item label="福利券分类">
            <JWelfareClassify v-model:value="model.categoryId" isImmediately style="width: 100%;" placeholder="请选择分类"></JWelfareClassify>
          </n-form-item>
          <n-form-item label="发放批次ID">
            <j-search-input v-model:value.trim="model.batchId" placeholder="请输入发放批次ID" :maxlength="30" @search="tableSearch"/>
          </n-form-item>
        </n-form>
      </template>
      <template #tableHeaderBtn>
        <n-button type="primary" :loading="isExport" @click="handleWelfareModal(0,{})">新增</n-button>
      </template>
    </FormLayout>
  </JModal>
  <welfare-voucher-modal ref="welfareVoucherRef"></welfare-voucher-modal>
  <ReceiveRecord v-model:show="isRecordShow" :couponBatchId="couponBatchId" @refresh="tableSearch" :welfarePageType="0" />
</template>

<script setup lang="tsx">
import { ref, watch } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { pageCouponBatch, sendHandleForPc } from "@/services/api";
import { useMessages } from "@/hooks";
import { NSpace, NTag } from "naive-ui";
import {
  WelfareModalTypeEnum, WelfarePageTypeEnum,
  WelfareStatusEnum,
  WelfareTypeEnum,
} from "@/views/StoreModule/SalesManagement/WelfareTicketManage/types";
import dayjs from "dayjs";
import JWelfareClassify from "@/components/JSelect/JWelfareClassify.vue";
import WelfareVoucherModal from "@/views/Distribution/OrganizationalStructure/components/welfareVoucherModal.vue";
import ReceiveRecord from "@/views/StoreModule/SalesManagement/WelfareTicketManage/components/ReceiveRecord.vue";
import Popconfirm from "@/components/Popconfirm/index.vue";
import {
  hasSelectRecord,
  hasUpdateValidDateAuth,
  hasWelfareManagementSendAuth,
} from "@/views/StoreModule/SalesManagement/authList";
const welfareTypeMap = {
  [WelfareTypeEnum.WELFARE]: '福利券',
  [WelfareTypeEnum.TIME]: '时长券',
};
const welfareStatusMap = {
  [WelfareStatusEnum.UNISSUED]: '未发放',
  [WelfareStatusEnum.ISSUING]: '发放中',
  [WelfareStatusEnum.ENDED]: '停止发放',
};
/* 表格方法Hook */
const {
  isLoading,
  tableData,
  paginationRef,
  pageTableData,
  paginationChange,
} = useTableDefault({
  pageDataRequest:pageCouponBatch,
});

/* 初始化参数 */
const initParams = {
  name:"",
  categoryId: null,
  batchId:'',
  searchType:'',
  searchValue:"storeName",
  storeId:""
};
const message = useMessages();
const model = ref({ ...initParams });
const isExport = ref<boolean>(false)

const welfareVoucherRef = ref()

/** 获取搜索参数 */
const getParams = () => {
  const { name,categoryId,batchId,searchType,searchValue,storeId  } = model.value;
  return {
    name,categoryId,batchId,storeId,
    [searchType]: searchValue
  };
};
/* 表格搜索 */
const tableSearch = async () => {
  await pageTableData(getParams(), paginationRef.value);
};

/** 弹窗显隐 */
const modalVisible = ref(false);
const acceptParams = (id) => {
  modalVisible.value = true;
  model.value.storeId = id
  tableSearch()
};
const isPopconfirmLoadingRef = ref(false);
/* 表格项 */
const tableColumns = [
  {
    title: "发放批次ID",
    key: "batchId",
    width: 230,
    align: "left",
  },
  {
    title: "福利券名称",
    key: "name",
    width: 120,
    align: "left",
  },
  {
    title: "福利券分类名称",
    key: "categoryName",
    width: 120,
    align: "left",
  },
  {
    title: "福利券类型",
    key: "type",
    width: 100,
    align: "left",
    render(row) {
      return welfareTypeMap[row.type] || '- ';
    },
  },
  {
    title: "发行总数",
    key: "totalQuantity",
    width: 80,
    align: "left",
    render(row) {
      return row.totalQuantity == -1 ? "无限制" : row.totalQuantity;
    },
  },
  {
    title: "领取总数",
    key: "receivedQuantity",
    width: 80,
    align: "left",
  },
  {
    title: "剩余总数",
    key: "remainingQuantity",
    width: 80,
    align: "left",
    render(row) {
      return row.remainingQuantity == -1 ? "无限制" : row.remainingQuantity;
    },
  },
  {
    title: "已使用",
    key: "usedQuantity",
    width: 80,
    align: "left",
  },
  {
    title: "发放状态",
    key: "cityName",
    width: 80,
    align: "left",
    render(row) {
      return (
        <NSpace justify='center'>
          <NTag
            bordered={false}
            size="small"
            type={row.status === WelfareStatusEnum.UNISSUED ? "info" : row.status === WelfareStatusEnum.ISSUING ? "success" : "default"}
          >
            {welfareStatusMap[row.status]}
          </NTag>
        </NSpace>
      );
    },
  },
  {
    title: "有效期",
    key: "validUntil",
    width: 180,
    align: "left",
    render(row) {
      return dayjs(row.validUntil).format('YYYY-MM-DD');
    },
  },
  {
    title: "操作",
    key: "action",
    width: 200,
    fixed: "right",
    align: "left",
  // {
  //   rowData.departmentType === DepartmentType.STORE && rowData.level<=9?
  //     <n-button text type="primary" onClick={() => handleRecord(rowData)}>领取记录</n-button> : null
  // }
    render: rowData => {
      return (
        <n-space align="center" justify="center">
          {
            hasWelfareManagementSendAuth  ?
            <Popconfirm
              onHandleClick={() => sendHandleFn(rowData.status === WelfareStatusEnum.ISSUING ? 0 : 1, rowData)}
              buttonContent={rowData.status === WelfareStatusEnum.ISSUING ? '停止发放' : '发放'}
              loading={isPopconfirmLoadingRef.value}
              type={rowData.status === WelfareStatusEnum.ISSUING ? 'error' : 'primary'}
              promptContent={promptContent(rowData)} /> : null
          }
          {
            hasSelectRecord ?
            <n-button text type="primary" onClick={() => handleRecord(rowData)}>领取记录</n-button>:null
          }
          {
            hasUpdateValidDateAuth ?
            <n-button text type="primary" onClick={() => handleWelfareModal(1,rowData)}>修改有效期</n-button>:null
          }
        </n-space>
      );
    },
  },
];
const sendHandleFn = async (sendEnable: 0 | 1, row: any) => {
  isPopconfirmLoadingRef.value = true
  try {
    await sendHandleForPc({ id: row.id, sendEnable })
    tableSearch()
    message.createMessageSuccess("操作成功")
  } catch (error) {
    message.createMessageError("操作异常" + error)
  }finally {
    isPopconfirmLoadingRef.value = false
  }
}
const promptContent = (row:any) => {
  if(row.status === WelfareStatusEnum.ISSUING){
    return '你确定要停止发放该福利券吗?'
  }else{
    return'你确定要对当前店铺发放该福利券吗?'
  }
}
const handleWelfareModal = (type, rowData?:any) => {
  welfareVoucherRef.value.acceptParams({
    welfareModalType:type,
    refreshTable: tableSearch,
    storeId:model.value.storeId,
    ...rowData
  })
};
/* 关闭弹窗之后 */
const closeModal = () => {
  tableData.value = []
  model.value = { ...initParams };
};

const isRecordShow = ref(false);
const couponBatchId = ref('');

const handleRecord = (rowData?:any) => {
  couponBatchId.value = rowData.id;
  isRecordShow.value = true;
};

watch(()=>model.value.categoryId, (newVal, oldVal) => {
  paginationRef.value.current = 1;
  tableSearch();
})

defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less">
.title {
  font-size: 20px;
  font-weight: 700;
}
</style>
