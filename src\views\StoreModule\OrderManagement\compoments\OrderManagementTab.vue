<template>
    <FormLayout
      :isLoading="isLoading"
       :tableData="tableData" 
       :tableColumns="tableColumns"
       :pagination="paginationRef"
       @paginationChange="paginationChange"
       :isTableSelection="true"
       @selectedKeysChange="selectedKeysChange"
       @table-sorter-change="tableSorterChange"
       id="member"
       :isDisplayIndex="false"
      >
      <template #searchForm>
        <n-form
            ref="formRef"
            :model="formValue"
            :show-feedback="false"
            label-placement="left"
            label-width="auto"
            require-mark-placement="right-hanging"
            size="small"
            :style="{ width: '100%' }"
        >  
            <n-form-item  label="客户关键字" path="range" >
              <n-input-group>
                <n-select
                  v-model:value="formValue.searchType"
                  placeholder="请选择"
                  :options="searchTypeOptions"
                  style="width: 110px;"
                  :render-option="renderOption"
                />
              <j-search-input
                  v-model:value="formValue.searchValue"
                  placeholder="请输入姓名、手机号"
                  @search="formSearch"
              />
              </n-input-group>
            </n-form-item>
            <n-form-item label="订单编号">
                <n-input v-model:value="formValue.orderNumberValue" type="text" placeholder="请输入订单编号"  @keyup.enter.native="formSearch" style="width:170px" clearable/>
            </n-form-item>
            <n-form-item label="spu编码">
                <n-input v-model:value="formValue.spu" maxlength="40" type="text" placeholder="请输入spu编码"  @keyup.enter.native="formSearch" style="width:170px" clearable/>
            </n-form-item>
            <n-form-item label="支付单号">
                <n-input v-model:value="formValue.wxPayNo" type="text" placeholder="请输入支付单号"  @keyup.enter.native="formSearch" style="width:170px" clearable/>
            </n-form-item>
            <n-form-item label="下单时间">
              <j-date-range-picker v-model:value="formValue.ordertime"    type="datetimerange" format="yyyy-MM-dd" :default-time="['00:00:00', '23:59:59']" clearable />
            </n-form-item>
            <n-form-item label="商品名称">
                <n-input v-model:value="formValue.productName" type="text" placeholder="请输入关键字信息"  @keyup.enter.native="formSearch" style="width:170px" clearable/>
            </n-form-item>
          <n-form-item label="店铺信息">
            <n-input-group>
              <n-select
                v-model:value="formValue.shopType"
                placeholder="请选择"
                :options="shopOptions"
                style="width: 110px;"
                :render-option="renderOption"
              />
              <n-input v-model:value="formValue.storeNameOrId" type="text" :placeholder="`请输入店铺${formValue.shopType === 'storeName' ? '名称' : 'ID'}`"  @keyup.enter.native="formSearch" style="width:170px" clearable/>
            </n-input-group>
            
          </n-form-item>
          <n-form-item  label="客户信息" path="range" >
            <n-input-group>
              <n-select
                v-model:value="formValue.customerInformationType"
                placeholder="请选择"
                :options="customerInformationTypeOptions"
                style="width: 110px;"
                :render-option="renderOption"
              />
              <j-search-input
                v-model:value="formValue.customerInformation"
                placeholder="请输入关键字信息"
                @search="formSearch"
              />
            </n-input-group>
          </n-form-item>
          <n-form-item  label="锁单状态">
            <n-input-group>
              <n-select
                v-model:value="formValue.isLocked"
                placeholder="请选择"
                :options="isLockedOptions"
                style="width: 140px;"
                :render-option="renderOption"
                clearable
              />
            </n-input-group>
          </n-form-item>
            <n-form-item  label="支付方式">
              <n-input-group>
                <n-select
                  v-model:value="formValue.payType"
                  placeholder="请选择支付方式"
                  :options="payTypeOptions" 
                  style="width: 140px;"
                  :render-option="renderOption"
                  clearable
                />
              </n-input-group>
            </n-form-item>

            <n-form-item  label="支付状态">
              <n-input-group>
                <n-select
                  v-model:value="formValue.payStatus"
                  placeholder="请选择支付状态"
                  :options="payStatusOptions" 
                  style="width: 140px;"
                  :render-option="renderOption"
                  clearable
                />
              </n-input-group>
            </n-form-item>

            <n-form-item  label="其他" path="range" >  
              <n-input-group>
                <n-select
                  v-model:value="formValue.belongType"
                  placeholder="请选择"
                  :options="belongOptions" 
                  style="width: 110px;"
                  :render-option="renderOption"
                />
              <j-search-input
                  v-model:value="formValue.belongSearchValue"
                  placeholder="请输入关键字信息"
                  @search="formSearch"
              />
              </n-input-group>
            </n-form-item>
        </n-form>
      </template>
      <template #tableHeaderBtn>
        <n-button @click="refresh" class="store-button">刷 新</n-button>
        <n-button :loading="formValue.loading"  type="primary"  @click="clickOrderExport()" v-if=" orderExportDisplayFn() && hasManagementExport ">订单导出</n-button>
        <n-button v-if="hasManagementLockOrders" :loading="formValue.lockloading"  type="primary"  @click="clickLockOrders()">锁单</n-button>
        <n-popconfirm :positive-text="null" :negative-text="null">
          <template #trigger>
            <n-icon size="32" depth={2}>
              <HelpCircleSharp />
            </n-icon>
          </template>
          点击锁单按钮，未付款订单自动被取消，已付款订单用户端无法再退款
        </n-popconfirm>
        <n-button @click="handleImportShipment()" v-if="importShipmentDisplayFn() && hasManagementShip"   type="primary" secondary>导入发货</n-button>
        <excelSubassembly @importedData="importedData" v-if="signButtonDisplayFn() && hasManagementSign " buttonName = "签收"/>
      </template>
      
     </FormLayout>
     <drawerOrderStatus ref="drawerOrderStatusShow"/>
     <drawerOrderDetails ref="drawerOrderDetailsShow"/>
     <drawerImportShipment ref="drawerImportShipmentShow"/>
     <RefundModal ref="refundModalShow" />
  </template>
  <script setup lang="tsx">
  import {ref , reactive, watch, h ,type VNode} from 'vue';
  import FormLayout from "@/layout/FormLayout.vue";
  import { useTableDefault } from "@/hooks/useTableDefault";
  import {orderPage, orderDetail, orderArrangeShipment, orderArrangeSignature, orderExportOrders, orderDelete, orderImportOrderShip,orderImportOrderSign,orderShip,orderSign,orderConfirmImportOrderShip,orderArrangeRefund,lockOrders} from '@/services/api';
  import {useMessages} from "@/hooks";
  import { OrderManagementSelection } from '@/enums';
  import drawerOrderStatus from './drawerOrderStatus.vue';
  import drawerOrderDetails from './drawerOrderDetails.vue';
  import excelSubassembly from './excelSubassembly.vue';
  import moment from "moment";
  import { NSpace, NTag, NTooltip, type SelectOption } from "naive-ui";
  import { orderStatusLabels, payTypeLabels, payStatusLabels, payTypeOptions, payStatusOptions, isLockedOptions,
    pickupTypeOptions} from "@/constants";
  import drawerImportShipment from './drawerImportShipment.vue'
  import {
    hasManagementDelete,
    hasManagementDetails,
    hasManagementExport,
    hasManagementLockOrders,
    hasManagementRefund,
    hasManagementShip,
    hasManagementSign,
  } from "../authList";
  import RefundModal from './RefundModal.vue'
  import {afterSaleManagementLabels} from "@/constants";
  import { HelpCircleSharp } from '@vicons/ionicons5'
import { useRoute } from 'vue-router';


  const renderOption = ({ node, option }: { node: VNode; option: SelectOption }) =>
        h(NTooltip, null, {
          trigger: () => node,
          default: () => option.label
   })
  interface prescriptionProps {
      tabNameRef:string //tab标签值
  }
  
  const props = withDefaults(defineProps<prescriptionProps>(), {
      tabNameRef:'unopened',
  });
  const route = useRoute();
  const tabValue = ref(null)
  const message = useMessages();
  const { createMessageSuccess, createMessageError, createMessageWarning} = useMessages();
  /* 初始化参数 */
  const initParams = {
    searchValue: '', //搜索关键字
    customerInformation:'',//搜索关键字
    orderNumberValue:null,//订单编号
    spu:null,//spu编码
    ordertime:null,//下单时间
    searchType:'customerName',
    belongType:'thirdDealerName',
    belongSearchValue:'',
    payType:null,
    payStatus:null,
    productName:undefined,//商品名称
    loading:false,
    lockloading:false,
    isAsc:false,
    orderBy:'create_time_',
    wxPayNo:null,//支付单号
    storeNameOrId:route?.query?.storeShortId ?? "", //店铺名称/ID
    isLocked:null,//锁单状态
    customerInformationType:'customerNickName',
    shopType: route?.query?.storeShortId ? "storeShortId" : "storeName"
    // pickupType:1,
    };
  const formValue = reactive({ ...initParams });
  
  /* 表格方法Hook */
  const {
      isLoading,
      tableData,
      paginationRef,
      paginationChange,
      pageTableData,
  } = useTableDefault({
      pageDataRequest: orderPage,
  });

  //刷新
  const refresh = () =>{
      getTableData()
  }
  
  /** 搜索 */
  const formSearch = () =>{
    getTableData()
  }
  
  function getTableData() {
      const _params = {
        status:props.tabNameRef  != '0' ? props.tabNameRef : undefined,
        code:formValue.orderNumberValue,
        orderStartTime:formValue.ordertime ? moment(formValue.ordertime[0]).format(`YYYY-MM-DD HH:mm:ss`) : null,
        orderEndTime:formValue.ordertime ? moment(formValue.ordertime[1]).format(`YYYY-MM-DD HH:mm:ss`) : null,
        productName:formValue.productName,
        payType:formValue.payType,
        payStatus:formValue.payStatus,
        wxPayNo:formValue.wxPayNo,
        isAsc:formValue.isAsc,
        orderBy:formValue.orderBy,
        // pickupType:formValue.pickupType,
        isLocked:formValue.isLocked,
        spu:formValue.spu
      }
      if(formValue.searchType == 'customerName'){
        _params['customerName'] = formValue.searchValue
      }else{
        _params['customerMobile'] = formValue.searchValue
      }
      if(formValue.customerInformationType == 'customerNickName'){
        _params['customerNickName'] = formValue.customerInformation
      }else if (formValue.customerInformationType == 'customerShortId'){
        _params['customerShortId'] = formValue.customerInformation
      }else{
        _params['belongMemberShortId'] = formValue.customerInformation
      }
      if(formValue.shopType == 'storeName'){
        _params['storeName'] = formValue.storeNameOrId
      }else{
        _params['storeShortId'] = formValue.storeNameOrId
      }
      if(formValue.belongType == 'thirdDealerName'){
        _params['thirdDealerName'] = formValue.belongSearchValue
      }else if(formValue.belongType == 'thirdGroupMgrName'){
        _params['thirdGroupMgrName'] = formValue.belongSearchValue
      }else if(formValue.belongType == 'thirdCourseName'){
        _params['thirdCourseName'] = formValue.belongSearchValue
      }else if(formValue.belongType == 'thirdCampName'){
        _params['thirdCampName'] = formValue.belongSearchValue
      }else if(formValue.belongType == 'supplierName'){
        _params['supplierName'] = formValue.belongSearchValue
      }else{
        _params['distributorName'] = formValue.belongSearchValue
      }
      pageTableData(_params, paginationRef.value,true);
  }
  
/** 编辑drawer状态 */
const drawerOrderStatusShow = ref(null)

/** 详情drawer状态 */
const drawerOrderDetailsShow = ref(null)

/** 搜索类型 */
const searchTypeOptions = [
  {
    label: "收货姓名",
    value: "customerName",
  },
  {
    label: "收货手机号",
    value: "customerMobile",
  },
];
  /** 搜索类型 */
  const customerInformationTypeOptions = [
    {
      label: "用户昵称",
      value: "customerNickName",
    },
    {
      label: "用户ID",
      value: "customerShortId",
    },
    {
      label: "归属门店人员ID",
      value: "belongMemberShortId",
    },
  ];
  const shopOptions = [
    {
      label: "店铺名称",
      value: "storeName",
    },
    {
      label: "店铺ID",
      value: "storeShortId",
    }
  ];
/** 归属类型 */
const belongOptions  = [
  {
    label: "归属经销商",
    value: "thirdDealerName",
  },
  {
    label: "归属群管",
    value: "thirdGroupMgrName",
  },
  {
    label: "归属课程",
    value: "thirdCourseName",
  },
  {
    label: "归属分销员",
    value: "distributorName",
  },
  {
    label: "归属训练营",
    value: "thirdCampName",
  },
  {
    label: "归属供应商",
    value: "supplierName",
  },
]

/* 表格项 */
const tableColumns = reactive([
    {
      title: "订单编号",
      key: "code",
      align: "left",
      fixed: "left",
      width: 100,
    },
    {
      title: "商品名称",
      key: "orderItemNameList",
      align: "left",
      width: 100,
      render: rowData => {
        if(rowData.orderItemNameList?.length > 1 ){
          const orderItemNameArray = []
          rowData.orderItemNameList.forEach((item,index)=>{
            if(index != 0){
              orderItemNameArray.push(item)
            }
          })
          const title =  '等'+ (rowData.orderItemNameList.length)+ '种商品'
          return <div>
            <p>
              <n-ellipsis>{rowData.orderItemNameList?.length > 0?rowData.orderItemNameList[0]:'-'}</n-ellipsis>
            </p>
            <n-popover trigger="hover">
                             {{
                                default: () => `${orderItemNameArray.join(',')}`,
                                trigger: () => (
                                 <span style={'font-size: 12px;color:red;cursor: pointer;'}>{title}</span>
                            )
                        }}
         
            </n-popover>
          </div>
        }else{
          return rowData.orderItemNameList?.length > 0  ? rowData.orderItemNameList : "-"
        }
      },
    },
    {
      title: "收货姓名",
      key: "recipientName",
      align: "left",
      width: 100,
    },
    {
      title: "收货手机号",
      key: "mobile",
      align: "left",
      width: 100,
    },
    {
      title: "收货地址",
      key: "address",
      align: "left",
      width: 100,
    },
    {
      title: "订单状态",
      key: "status",
      align: "left",
      width: 100,
      render: row => {
        return <span>{orderStatusLabels[row['status']]}</span>;
      },
    },
    {
      title: "锁单状态",
      key: "status",
      align: "left",
      width: 100,
      render: row => {
        if (row.isLocked === 0 || row.isLocked === 1){
          return (
            <NSpace>
              <NTag
                bordered={false}
                size="small"
                type={row.isLocked === 0 ? "success" : "error"}
              >
                {row.isLocked === 0 ? "未锁单" : "已锁单"}
              </NTag>
            </NSpace>
          );
        }else{
          return '-';
        }
      },
    },
    {
      title: "支付方式",
      key: "payType",
      align: "left",
      width: 100,
      render: row => {
        if(payTypeLabels[row['payType']]){
          return <span>{payTypeLabels[row['payType']]}</span>
        }else{
          return '-';
        }
        
      },
    },
    {
      title: "支付状态",
      key: "payStatus",
      align: "left",
      width: 100,
      render: row => {
        return <span>{payStatusLabels[row['payStatus']]}</span>;
      },
    },
    {
      title: "订单总金额",
      key: "money_",
      align: "left",
      width: 100,
      sorter: true,
      isSortDefault: false,
      render: rowData => {
        if (rowData.fromType == 7 && rowData.totalCoupons && rowData.categoryName){
          return rowData.totalCoupons + '张' + rowData.categoryName
        }else{
          return rowData.money ? (rowData.money / 100).toFixed(2) : '-'
        }
      }
     
    },
    {
      title: "店铺信息",
      key: "thirdDealerName",
      align: "left",
      width: 100,
      render: rowData => {
        const storeEntityDTO = rowData?.storeEntityDTO || {
          province:'',
          city:'',
          area:'',
          addressDetail:''
        };
        const addressDetail = storeEntityDTO.province + storeEntityDTO.city + storeEntityDTO.area + storeEntityDTO.addressDetail
        if (rowData?.storeEntityDTO && !(!storeEntityDTO.storeName &&
          !storeEntityDTO.contactName &&
          !storeEntityDTO.contactPhone &&
          !addressDetail)) {
          return (
            <n-space justify="center">
              {storeEntityDTO.storeName ? <text>{storeEntityDTO.storeName}</text> : null}
              {storeEntityDTO.contactName ? <text>{storeEntityDTO.contactName}</text> : null}
              {storeEntityDTO.contactPhone ? <text>{storeEntityDTO.contactPhone}</text> : null}
              {
                addressDetail ? <text>{addressDetail}</text> : null
              }
            </n-space>
          )
        } else {
          return '-';
        }
      }
    },
    {
      title: "客户信息",
      key: "thirdDealerName",
      align: "left",
      width: 100,
      render: rowData => {
        if (rowData.customerName || rowData.customerShortId){
          return (
            <n-space  align="center" justify="center">
              <text>{rowData.customerName || ''}</text>
              <text>用户ID:{rowData.customerShortId || ''}</text>
            </n-space>
          )
        } else {
          return '-';
        }
      }
    },
    {
      title: "归属经销商",
      key: "thirdDealerName",
      align: "left",
      width: 100,
    },
    {
      title: "归属群管",
      key: "thirdGroupMgrName",
      align: "left",
      width: 100,
    },
    {
      title: "归属课程",
      key: "thirdCourseName",
      align: "left",
      width: 100,
    },
    {
      title: "归属训练营",
      key: "thirdCampName",
      align: "left",
      width: 100,
    },
    {
      title: "归属分销员",
      key: "distributorName",
      align: "left",
      width: 100,
    },
    {
      title: "供应商昵称",
      key: "supplierName",
      align: "left",
      width: 100,
    },
    {
      title: "创建时间",
      key: "create_time_",
      align: "left",
      sorter: true,
      isSortDefault: true,
      width: 140,
      render: rowData => {
        return rowData.createTime ? rowData.createTime : '-'
      }
    },
    {
      title: "操作",
      key: "action",
      width: 120,
      align: "left",
      fixed: "right",
      render: (row) => {
        return (
          <n-space align="center" justify="center">
            { deliveryDisplayFn(row) && hasManagementShip ? <n-button text size="small" type="primary" class="mt-4 mr-4 edit-icon" onClick={() =>clickGoodsDeal(row,'waitFeliverGoods')}>发货
            </n-button> : null}
            { signInDisplayFn(row) && hasManagementSign ? <n-button text size="small" type="primary" class="mt-4 mr-4 edit-icon" onClick={() =>clickGoodsDeal(row,'waitTakeOverGoods')}>签收
            </n-button> : null}
             
            {/* { deleteDisplayFn(row) && hasManagementDelete ? <n-popconfirm
            onPositiveClick={() => {clickDelete(row.code)}}
            >
            {{
              trigger: () => (
                <a style={{ color: 'red', cursor: 'pointer' }}>删除</a>
              ),
              default: () => <span style={{width:'300px'}}>是否确定删除该数据？</span>
            }}
            </n-popconfirm> : null} */}
            {/* { returnDisplayFn(row) ? <n-button text size="small" type="primary" class="mt-4 mr-4 edit-icon" disabled={(refunId.value != row.id ? refundLoading.value : false)}  onClick={() =>clickReturn(row)} loading={ (refunId.value == row.id ? refundLoading.value : false)}>退款 </n-button> : null} */}
            { afterSaleManagementLabels[row.action] == '客户申请退款' && row.payType != 6 && hasManagementRefund ? <n-button text size="small" type="primary" class="mt-4 mr-4 edit-icon" disabled={(refunId.value != row.id ? refundLoading.value : false)}  onClick={() =>clickReturn(row)} loading={ (refunId.value == row.id ? refundLoading.value : false)}>退款 </n-button> : null}
            {
              hasManagementDetails ? <n-button text size="small" type="primary" class="mt-4 mr-4 edit-icon" onClick={() =>clickDetails(row,tabValue.value)}>详情
              </n-button> : null
            }
          </n-space>
        );
      },
    },
])

// 订单导出
const clickOrderExport = async() =>{
  formValue.loading = true
  const params = {
    codeList:codeListData.value,
    status:tabValue.value  != '0' ? tabValue.value : undefined,
    code:formValue.orderNumberValue,
    orderStartTime:formValue.ordertime ? moment(formValue.ordertime[0]).format(`YYYY-MM-DD HH:mm:ss`) : null,
    orderEndTime:formValue.ordertime ? moment(formValue.ordertime[1]).format(`YYYY-MM-DD HH:mm:ss`) : null, 
    productName:formValue.productName,
    payType:formValue.payType,
    payStatus:formValue.payStatus,
    wxPayNo:formValue.wxPayNo,
    isAsc:formValue.isAsc,
    orderBy:formValue.orderBy,
    isLocked:formValue.isLocked,
    spu:formValue.spu
  }
  if(formValue.searchType == 'customerName'){
    params['customerName'] = formValue.searchValue
  }else{
    params['customerMobile'] = formValue.searchValue
  }
  if(formValue.customerInformationType == 'customerNickName'){
    params['customerNickName'] = formValue.customerInformation
  }else if (formValue.customerInformationType == 'customerShortId'){
    params['customerShortId'] = formValue.customerInformation
  }else{
    params['belongMemberShortId'] = formValue.customerInformation
  }
  if(formValue.shopType == 'storeName'){
        params['storeName'] = formValue.storeNameOrId
      }else{
        params['storeShortId'] = formValue.storeNameOrId
      }

  if(formValue.belongType == 'thirdDealerName'){
    params['thirdDealerName'] = formValue.belongSearchValue
  }else if(formValue.belongType == 'thirdGroupMgrName'){
    params['thirdGroupMgrName'] = formValue.belongSearchValue
  }else if(formValue.belongType == 'thirdCourseName'){
    params['thirdCourseName'] = formValue.belongSearchValue
  }else if(formValue.belongType == 'thirdCampName'){
    params['thirdCampName'] = formValue.belongSearchValue
  }else if(formValue.belongType == 'supplierName'){
    params['supplierName'] = formValue.belongSearchValue
  }else{
    params['distributorName'] = formValue.belongSearchValue
  }

  try{
    await orderExportOrders({data:params})
    createMessageSuccess('导出成功')
  }
  catch(err){
    createMessageError('导出失败:' + err)
  }finally{
    formValue.loading = false
  }
}

// 订单锁单
const clickLockOrders = async() =>{
    formValue.lockloading = true
    if (codeListData.value.length < 1){
      createMessageWarning('请选择锁单订单!')
      formValue.lockloading = false
      return
    }
    try{
      await lockOrders({data:codeListData.value})
      createMessageSuccess('锁单成功')
    }
    catch(err){
      createMessageError('锁单失败:' + err)
    }finally{
      formValue.lockloading = false
    }
  }

//勾选
const codeListData = ref([])
const selectedKeysChange = (value,row) =>{
  const codeList = []
  row.forEach(item => {
    codeList.push(item.code)
  })
  codeListData.value = codeList
}

/** 发货 或 签收 */
const clickGoodsDeal = (row,type) =>{
    const _params = {
      row,
      updateInfoApi:type == 'waitFeliverGoods' ? orderShip : orderSign,
      refresh: refresh,
      type:type
    };
    drawerOrderStatusShow.value?.acceptParams(_params);
}

/** 删除 */
const clickDelete = async(code) =>{
  try{
    await orderDelete(code)
    createMessageSuccess('删除成功')
    refresh()
  }catch(err){
    createMessageError('删除失败:' + err)
  }
}

/** 详情 */
const clickDetails  = (row,tabValue) =>{
  const _params = {
      row,
      refresh: refresh,
      tabValue: tabValue
    };
    drawerOrderDetailsShow.value?.acceptParams(_params);
}

/** 退货 */
const refundModalShow = ref()
const refundLoading =  ref(false)
const refunId = ref(null)
const clickReturn = async(row) =>{
    const _params = {
      row:row,
      isVirtual:row.isVirtual,
      payType:payTypeLabels[row['payType']],
      status:orderStatusLabels[row['status']],
      refresh: refresh,
    };
    refundModalShow.value.acceptParams(_params)
}

/** 导入数据 */
const importedData = async(file) =>{
  isLoading.value = true
  try{
    tabValue.value == OrderManagementSelection.waitFeliverGoods ? await orderImportOrderShip(file) : await orderImportOrderSign(file)
    setTimeout(() => {
      refresh()
      createMessageSuccess(tabValue.value == OrderManagementSelection.waitFeliverGoods ?  '订单发货导入成功' : '订单签收导入成功')
    }, 5000);
  }catch(err){
    createMessageError( (tabValue.value == OrderManagementSelection.waitFeliverGoods ? '订单发货导入失败:' : '订单签收导入失败:') + err)
  }
}

/** 导入发货 */
const drawerImportShipmentShow = ref()
const handleImportShipment = () =>{
  const _params = {
    getImportOrderShipApi:orderImportOrderShip,
    getConfirmOrderApi:orderConfirmImportOrderShip,
    refresh: refresh,
  };
  drawerImportShipmentShow.value.acceptParams(_params)
}

/** 发货按钮 */
const deliveryDisplayFn = (row) => {
  if(tabValue.value == OrderManagementSelection.waitFeliverGoods){
    return true 
  }
  if(orderStatusLabels[row['status']] == '待发货'){
    return true 
  }
  return false
}

/** 签收按钮 */
const signInDisplayFn = (row) =>{
  if(tabValue.value == OrderManagementSelection.waitTakeOverGoods){
    return true 
  }
  if(orderStatusLabels[row['status']] == '待收货'){
    return true 
  }
  return false
}

/** 删除按钮 */
const deleteDisplayFn  = (row) => {
  if(tabValue.value == OrderManagementSelection.waitPay){
    return true 
  }
  if(orderStatusLabels[row['status']] == '待支付'){
    return true 
  }
  return false
}

/** 退货按钮显示 */
const returnDisplayFn = (row) => {
  const payTypesToCheck = new Set(['在线支付', '支付定金', '积分 + 现金']);
  const tabValuesToCheck = new Set([
    OrderManagementSelection.waitFeliverGoods,
    OrderManagementSelection.waitTakeOverGoods,
  ]);
  const orderStatusesToCheck = new Set(['待发货', '待收货', '已完成']);

  const payType = payTypeLabels[row['payType']];
  const orderStatus = orderStatusLabels[row['status']];
  // 检查支付类型和标签页条件
  if (payTypesToCheck.has(payType)) {
    if (tabValuesToCheck.has(tabValue.value) || orderStatusesToCheck.has(orderStatus)) {
      return true;
    }
  }

  // 检查已完成状态
  if (tabValue.value === OrderManagementSelection.accomplish || orderStatus === '已完成') {
   if(payTypeLabels[row['payType']] != '纯积分支付'){
    return true;
   }
  }

  return false;
};


/** 订单导出控制显示 */
const orderExportDisplayFn = () =>{
  if((tabValue.value != OrderManagementSelection.waitPay) && (tabValue.value != OrderManagementSelection.accomplish) && (tabValue.value != OrderManagementSelection.cancelled)){
    return true
  }
  return false
} 

/** 导入发货控制显示 */
const importShipmentDisplayFn = () =>{
  if(tabValue.value == OrderManagementSelection.waitFeliverGoods){
    return true
  }
  if(tabValue.value ==  OrderManagementSelection.all){
    return true
  }
  return false
}

/** 签收按钮控制显示 */
const signButtonDisplayFn = () => {
  if(tabValue.value == OrderManagementSelection.waitTakeOverGoods){
    return true
  }
  return false
}

/** 表格排序 */
function tableSorterChange(info: { sort: string, sortAsc: "ascend" | "descend" }) {
    if (info.sortAsc === 'ascend') {
      formValue.isAsc = true;
    } else {
      formValue.isAsc = false;
    }
    formValue.orderBy = info.sort,
    getTableData();
}

  watch([
     () => formValue.ordertime,
     () => formValue.payType,
     () => formValue.payStatus,
     () => formValue.isLocked
    ],() => {
      getTableData();
  });
  
  watch(
      ()=> props.tabNameRef,
      (newVal)=>{
        if(props.storeShortId) {
          formValue.shopType = "storeShortId"
          formValue.storeNameOrId = props.storeShortId
        }
          getTableData()
          tabValue.value = newVal
      },
      {
          immediate:true
      }
  )
  </script>
  <style lang="less" scoped>
  @import "@/styles/default.less";

  </style>