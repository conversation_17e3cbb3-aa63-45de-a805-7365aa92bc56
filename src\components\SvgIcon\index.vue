<script setup lang="ts">
import { computed, useAttrs } from "vue";
import type { Component } from "vue";
import { NIcon } from "naive-ui";

defineOptions({ name: 'SvgIcon', inheritAttrs: false });

/**
 * Props
 * - 支持 vicons/ionicons5 和本地 svg 图标
 * - 如果同时传递了 icon 和 localIcon，将优先渲染 localIcon
 */
interface Props {
  /** vicons/ionicons5 图标名称 */
  icon?: Component;
  /** 本地 svg 图标名称 */
  localIcon?: string;
}

const props = defineProps<Props>();

const attrs = useAttrs();

const bindAttrs = computed<{ class: string; style: string }>(() => ({
  class: (attrs.class as string) || '',
  style: (attrs.style as string) || ''
}));

const symbolId = computed(() => {
  const defaultLocalIcon = 'no-icon';

  const icon = props.localIcon || defaultLocalIcon;

  return `#icon-${icon}`;
});

/** 如果传递了 localIcon，将优先渲染 localIcon */
const renderLocalIcon = computed(() => props.localIcon || !props.icon);
</script>

<template>
  <template v-if="renderLocalIcon">
    <svg aria-hidden="true" width="1em" height="1em" v-bind="bindAttrs">
      <use :xlink:href="symbolId" fill="currentColor" />
    </svg>
  </template>
  <template v-else>
    <NIcon v-if="icon" :component="icon" v-bind="bindAttrs" />
  </template>
</template>

<style scoped></style>

