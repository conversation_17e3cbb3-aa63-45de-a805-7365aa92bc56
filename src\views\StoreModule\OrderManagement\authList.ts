import { OrderManagementAuth } from "@/enums/authKeys";
import { hasAuth } from "@/utils/auth/authUtils";


/** 删除 */
export const hasManagementDelete = function(){
    return hasAuth(OrderManagementAuth.orderManagementIndexDelete.key);
}()
/** 详情 */
export const hasManagementDetails = function(){
    return hasAuth(OrderManagementAuth.orderManagementIndexDetails.key);
}()
/** 订单导出 */
export const hasManagementExport = function(){
    return hasAuth(OrderManagementAuth.orderManagementIndexExport.key);
}()
/** 发货 */
export const hasManagementShip = function(){
    return hasAuth(OrderManagementAuth.orderManagementIndexShip.key);
}()
/** 签收 */
export const hasManagementSign = function(){
    return hasAuth(OrderManagementAuth.orderManagementIndexSign.key);
}()

export const hasManagementLockOrders = function(){
    return hasAuth(OrderManagementAuth.orderManagementIndexLock.key);
}()

/** 退款 */
export const hasManagementRefund = (function () {
    return hasAuth(OrderManagementAuth.orderManagementIndexRefund.key);
})()
