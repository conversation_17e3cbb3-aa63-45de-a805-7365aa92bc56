<template>
  <div
      ref="toolbarItemRef"
      :class="[
      'toolbar-item-container',
      !isPC && 'toolbar-item-container-h5',
      isUniFrameWork && 'toolbar-item-container-uni',
    ]"
  >
    <div
        :class="[
        'toolbar-item-container-icon',
        isUniFrameWork && 'toolbar-item-container-uni-icon',
      ]"
        @click="toggleToolbarItem"
    >
      <Icon
          :file="props.iconFile"
          class="icon"
          :width="props.iconWidth"
          :height="props.iconHeight"
      />
    </div>
    <div
        v-if="isUniFrameWork"
        :class="['toolbar-item-container-uni-title']"
    >
      {{ props.title }}
    </div>
    <div
        v-show="showDialog"
        ref="dialogRef"
        :class="[
        'toolbar-item-container-dialog',
        isDark && 'toolbar-item-container-dialog-dark',
        !isPC && 'toolbar-item-container-h5-dialog',
        isUniFrameWork && 'toolbar-item-container-uni-dialog',
      ]"
    >
      <slot/>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {ref} from 'vue';
import {outsideClick} from '@tencentcloud/universal-api';
import Icon from '@/views/DoctorEndModule/IM/components/common/Icon/index.vue';
import TUIChatConfig from '@/views/DoctorEndModule/IM/utils/config'

const props = defineProps({
  iconFile: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: '',
  },
  needDialog: {
    type: Boolean,
    default: true,
  },
  iconWidth: {
    type: String,
    default: '20px',
  },
  iconHeight: {
    type: String,
    default: '20px',
  },
  // Whether to display the bottom popup dialog on mobile devices
  // Invalid on PC
  needBottomPopup: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['onIconClick', 'onDialogClose', 'onDialogShow']);

const isPC = true;
const isUniFrameWork = false


const isDark = ref(TUIChatConfig.getTheme() === 'dark');
const showDialog = ref(false);
const toolbarItemRef = ref();
const dialogRef = ref();

const toggleToolbarItem = () => {
  emits('onIconClick', dialogRef);
  if (isPC) {
    outsideClick.listen({
      domRefs: toolbarItemRef.value,
      handler: closeToolbarItem,
    });
  }
  if (!props.needDialog) {
    return;
  }
  toggleDialogDisplay(!showDialog.value);
};

const closeToolbarItem = () => {
  showDialog.value = false;
  emits('onDialogClose', dialogRef);
};

const toggleDialogDisplay = (showStatus: boolean) => {
  if (showDialog.value === showStatus) {
    return;
  }
  showDialog.value = showStatus;
  switch (showStatus) {
    case true:
      emits('onDialogShow', dialogRef);
      break;
    case false:
      emits('onDialogClose', dialogRef);
  }
};

const onPopupClose = () => {
  showDialog.value = false;
};

defineExpose({
  toggleDialogDisplay,
});
</script>
<style lang="less" scoped>
.toolbar-item-container {
  position: relative;

  &-icon {
    padding: 8px;
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
  }

  &-dialog {
    z-index: 5;
    position: absolute;
    background: #fff;
    box-shadow: 0 2px 4px -3px rgba(32, 77, 141, 0.03), 0 6px 10px 1px rgba(32, 77, 141, 0.06), 0 3px 14px 2px rgba(32, 77, 141, 0.05);
    width: fit-content;
    height: fit-content;
    bottom: 35px;
  }

  &-dialog-dark {
    background: #22262E;
    box-shadow: 0 8px 40px 0 rgba(23, 25, 31, 0.6), 0 4px 12px 0 rgba(23, 25, 31, 0.8);
  }
}

</style>
