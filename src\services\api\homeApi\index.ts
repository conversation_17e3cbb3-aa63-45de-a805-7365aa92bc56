import { defHttp } from '@/services';

/** 医生管理 */
export const enum HomeApi {
    productCount = "/product/manage/selectProductManage",       // 商品统计
    customerCount = "/customerEntity/selectCustomerManage",     // 客户统计
    pendingCount = "/order/select/homePending",                 // 待处理统计
    orderCount = "/order/select/homeOrderMag",                  // 订单统计
    findNoPrescribed = "/pres/findWaitingPrescribed",                 // 待开方提醒
    /** 1.1.5新增 待办事项提醒 */
    getRemind = "/remind/get",                 // 待办事项提醒
    getEcommerceTrendChart = '/pc/homeData/getEcommerceTrendChart',
    getEcommerceStats = '/pc/homeData/getEcommerceStats',
}
export function getProductCount() {
    return defHttp.post({
        url: HomeApi.productCount,
    });
}
export function getCustomerCount() {
    return defHttp.post({
        url: HomeApi.customerCount,
    });
}
export function getPendingCount() {
    return defHttp.post({
        url: HomeApi.pendingCount,
    });
}
export function getOrderCount() {
    return defHttp.post({
        url: HomeApi.orderCount,
    });
}

export interface RemindRes {
    /* 最新发货异常订单编号 */
    orderCode: string;
    /* 最新待开方处方id */
    presId: string;
}
/** 1.1.5新增 待办事项提醒 */
export function getRemind(){
    return defHttp.post<RemindRes>({
        url: HomeApi.getRemind,
    });
}

export function getEcommerceTrendChart(){
    return defHttp.post({
        url: HomeApi.getEcommerceTrendChart,
    });
}

export function getEcommerceStats(params = {}){
    return defHttp.post({
        url: HomeApi.getEcommerceStats,
        params
    });
}