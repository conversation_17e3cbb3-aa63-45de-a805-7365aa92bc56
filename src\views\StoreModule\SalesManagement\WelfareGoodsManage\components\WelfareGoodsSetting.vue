<template>
  <JModal
    v-model:show="modalVisible"
    width="640"
    :title="goodsClassifyType == GoodsClassifyTypeEnum.DELETE ? `删除【${model.name}】商品分类` : titMap[goodsClassifyType]"
    @after-leave="closeModal"
    @positive-click="_submit"
    :positiveButtonProps="{
          loading: isLoading
      }"
  >
    <NForm
      ref="formRef"
      :rules="rules"
      :model="model"
      label-width="auto"
      label-placement="left"
      require-mark-placement="right-hanging"
      :style="{
              width: '100%',
          }"
    >
      <template v-if="goodsClassifyType != GoodsClassifyTypeEnum.DELETE">
        <NFormItem label="商品分类名称" path="name">
          <NInput v-model:value="model.name" placeholder="请输入商品分类名称" :maxlength="30" clearable />
        </NFormItem>
        <NFormItem label="排序号">
          <NInputNumber
            v-model:value="model.sort"
            placeholder="序号越大排位越前,最多3位数"
            :max="999"
            :min="0"
            clearable
            :show-button="false"
            style="width: 100%;"
            :precision="0"
          />
        </NFormItem>
        <NFormItem label="图标" path="imageUrl">
          <div style="width: 100%;">
            <UploadProductImg v-model:value="model.imageUrl" accept=".png,.jpg,.jpeg,.gif" :fileListSize="1" :maxFileSize="0.5" />
            <div style="width: 98%; margin-top: 12px;">图片需小于500k，支持png、jpg、JPEG、GIF格式</div>
          </div>
        </NFormItem>
      </template>
      <template v-else>
        <div style="font-size: 14px;margin-bottom: 10px;">注：确定删除当前分类后，对该分类的商商品后续处理。</div>
        <NFormItem label="更改新分类" path="newCateId">
          <JCouponProduct
            isImmediately
            style="width: 100%;"
            placeholder="请选择分类"
            v-model:value="model.newCateId"
            :filterId="model.id"
          />
        </NFormItem>
      </template>
    </NForm>
  </JModal>
</template>

<script setup lang="ts">
import { ref, computed,watch } from 'vue';
import { useMessages } from '@/hooks';
import { addCouponProductCate, updatecouponProductCate, deletecouponProductCate } from '@/services/api';
import { GoodsClassifyTypeEnum } from "../type";
import UploadProductImg from "@/components/UploadProductImg/index.vue";
import JCouponProduct from '@/components/JSelect/JCouponProduct.vue';

const message = useMessages();
const emits = defineEmits<{
  (e: 'refresh'): void;
}>();

const initParams = {
  id: null,
  newCateId: null,
  name: '',
  sort: null,
  imageUrl: []
};

const model = ref({ ...initParams });
const isLoading = ref(false);
const modalVisible = ref(false);
const goodsClassifyType = ref<GoodsClassifyTypeEnum>(GoodsClassifyTypeEnum.ADD);
const formRef = ref(null);

// 关闭按钮
const closeModal = () => {
  modalVisible.value = false;
  model.value = { ...initParams };
  model.value.imageUrl = [];
};

const titMap = {
  [GoodsClassifyTypeEnum.ADD]: "添加福利品分类",
  [GoodsClassifyTypeEnum.EDIT]: "编辑福利品分类",
};

/* 表单规则 */
const rules = {
  name: {
    required: true,
    trigger: ["blur", "change"],
    message: "请输入商品分类名称",
  },
  imageUrl: {
    type: "array",
    required: true,
    trigger: ["blur", "change"],
    message: "请上传图标",
  },
  newCateId: {
      required: true,
      trigger: ["blur", "change"],
      message: "请选择分类",
  },
}

/* 接收父组件传过来的参数 */
const acceptParams = (type: GoodsClassifyTypeEnum, row: any) => {
  goodsClassifyType.value = type;
  if (goodsClassifyType.value !== GoodsClassifyTypeEnum.ADD) {
      model.value.name = row.label || null;
      model.value.imageUrl = row.imageUrl ? [{ path: row.imageUrl }] : []
      model.value.sort = row.sort || null;
      model.value.id = row.key || null;
  }
  // 触发弹窗
  modalVisible.value = true;
};

// 确认按钮
const _submit = () => {
  formRef.value?.validate(async (errors: any) => {
      if (!errors) {
          const { id, imageUrl, newCateId, ...other } = model.value;
          let params;

          if (goodsClassifyType.value == GoodsClassifyTypeEnum.DELETE) {
              params = {
                  oldCateId: id,
                  newCateId
              };
          } else {
              params = {
                  data: {
                      ...other,
                      imageUrl: Array.isArray(imageUrl) ? imageUrl[0].path : imageUrl,
                  }
              };
          }

          if (goodsClassifyType.value == GoodsClassifyTypeEnum.EDIT) {
              params.data["id"] = model.value.id;
          }

          const api = goodsClassifyType.value === GoodsClassifyTypeEnum.ADD ? addCouponProductCate :
                      goodsClassifyType.value === GoodsClassifyTypeEnum.EDIT ? updatecouponProductCate :
                      deletecouponProductCate;

          try {
              isLoading.value = true;
              await api(params);
              message.createMessageSuccess('操作成功');
              emits('refresh');
              closeModal();
          } catch (error) {
              message.createMessageError(`${error}`);
          } finally {
              isLoading.value = false;
          }
      }
  });
};

/** 监听 */
watch(() => model.value.imageUrl, (newVal) => {
  formRef.value?.validate()
});

defineExpose({
  acceptParams,
});
</script>
