<template>
     <div class="overview-card-wrapper">
        <OverviewDataCard
            v-for="(item, index) in props.value" :key="index"
            :titleIconSrc = "item.titleIconSrc"
            :title="item.title"
            :is-animation="item.isAnimation"
            :value-list="item.valueList"
        />
    </div>
</template>
<script setup lang="ts">
import OverviewDataCard,{type OverviewDataCardProps} from "./components/OverviewDataCard.vue"
export type OverviewDataWrapperProps = {
    value:Array<OverviewDataCardProps>
}
const props = defineProps<OverviewDataWrapperProps>()

</script>
<style lang="less" scoped>
@import "@/styles/defaultVar.less";
.overview-card-wrapper{
    display: flex;
    flex-wrap: wrap;
    background-color:#f2f3f5 ;
}



</style>