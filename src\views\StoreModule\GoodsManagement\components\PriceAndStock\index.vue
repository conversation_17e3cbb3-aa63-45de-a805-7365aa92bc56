<template>
  <div class="price-and-stock-wrapper">
    <SpecGroup v-model:value="specList" :type="type" />
    <SpecPriceGroup 
      :specList="specList" 
      :specPriceList="productSpecVOList"
      @update:modelValue="handleSpecPriceUpdate"
      :isVirtual="props.isVirtual"
      :isPointEnable="props.isPointEnable"
      :productId="productId"
      :type="props.type"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, toRefs, onMounted, computed, watch} from 'vue'
import SpecGroup from './components/specGroup.vue'
import SpecPriceGroup from './components/specPriceGroup.vue'
import type { SpecList, SpecPriceItem } from './types'
import { transformSpecData,transformFlatSpecData } from './utils/transformSpecData';

const props = withDefaults(defineProps<{
  isVirtual?: boolean;
  type: 'add' | 'edit' | 'view'; // 商品类型，用于区分不同类型商品的付款方式设置
  isPointEnable?: boolean; // 是否启用积分功能
  productSpecVOList:SpecPriceItem[]     // 规格
  productAttributeValueVOList:any[]     //  规格属性数组
  productId?:string             // 商品id
}>(),{
    isVirtual: false,
    type: 'add',
    productId: ''
});

const emits = defineEmits<{
  (e: "update:productAttributeValueVOList", value: any[]): void;
  (e: "update:productSpecVOList", value: any[]): void;
}>();

/** 实现双向绑定并格式化数据结构 */
const specList = ref<SpecList[]>([])
watch(
  () => props.productAttributeValueVOList,
  (newVal) => {
    const transformed = transformSpecData(newVal)
    if (JSON.stringify(transformed) !== JSON.stringify(specList.value)) {
      specList.value = transformed
    }
  },
  { immediate: true, deep: true }
)
watch(
  specList,
  (newVal) => {
    const flattened = transformFlatSpecData(newVal,props.productId)
    if (JSON.stringify(flattened) !== JSON.stringify(props.productAttributeValueVOList)) {
      emits('update:productAttributeValueVOList', flattened)
    }
  },
  { deep: true }
)



const handleSpecPriceUpdate = (value: SpecPriceItem[]) => {
  emits('update:productSpecVOList', value) // 触发父组件的更新事件
}
</script>
<style scoped lang="less">
.price-and-stock-wrapper{
  width: 100%;
  margin-right: 12px;
}
</style>
