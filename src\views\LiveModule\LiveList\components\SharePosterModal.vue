<template>
  <n-modal v-model:show="modalShow" :auto-focus="false" @after-leave="handleClose">
    <n-card
      class="share-poster-card"
      style="width: 420px"
      :bordered="false"
      size="small"
      title="分享海报"
      closable
      @close="handleClose"
    >
      <div class="poster-container" id="poster">
        <img class="poster-img" :src="ShareBg" />
        <div class="poster-name">{{ title }}</div>
        <div class="poster-time">{{ liveStartTime }}</div>
        <img class="link-code-img" :src="linkCodeUrl" width="180" height="180" />
      </div>
      <!-- {{ linkCodeUrl }} -->
      <n-button type="primary" @click="downloadImg" :loading="isDownloading">下载</n-button>
    </n-card>
  </n-modal>
</template>
<script setup lang="ts">
import { computed, reactive, ref, onMounted } from "vue";
import { copyText } from "@/utils/clipboardUtils";
import { useMessages } from "@/hooks/useMessage";
import { downloadFile } from "@/utils/fileUtils";
import ShareBg from "@/assets/image/system/shareBg.png";
import html2canvas from "html2canvas";
import QRcode from "qrcode";
import { getPoolLink } from "@/services/api/pool";
import { createPoolLink } from "@/utils/poolUtils";
const { createMessageError, createMessageSuccess } = useMessages();
const props = defineProps({
  show: {
    // 是否显示
    type: Boolean,
    default: false,
  },
  shareImgUrl: {
    //海报图片
    type: String,
    default: "",
  },
  linkCode: {
    //二维码
    type: String,
    default: "",
  },
  title: {
    //直播标题
    type: String,
    default: "",
  },
  liveStartTime: {
    //直播开始时间
    type: String,
    default: "",
  },
});
const currentRef = ref<number | null>(0);
const linkCodeUrl = ref("");
const emit = defineEmits(["update:show"]);
const isDownloading = ref(false);
const modalShow = computed({
  get() {
    createInviteLink();
    return props.show;
  },
  set(value) {
    emit("update:show", value);
  },
});
function handleClose() {
  modalShow.value = false;
}
const downloadImg = async () => {
  isDownloading.value = true;
  const el = document.getElementById("poster");
  // 配置
  let options = {
    width: el.offsetWidth,
    height: el.offsetHeight,
    useCORS: true,
    allowTaint: false,
  };
  html2canvas(el, options)
    .then(canvas => {
      let canvasImg = canvas.toDataURL("image/png");
      downloadFile(canvasImg, "分享海报");
    })
    .catch(err => {
      createMessageError("导出失败");
    })
    .finally(() => {
      isDownloading.value = false;
    });
};
async function createInviteLink() {
  try {
    let link = props.linkCode
    try{
      link = await createPoolLink(props.linkCode)
    }
    catch(e){
      link = props.linkCode
    }
    const url = await QRcode.toDataURL(link, { margin: 1 });
    linkCodeUrl.value = url;
  } catch (e) {
    createMessageError("生成二维码失败");
  }
}
</script>

<style lang="less">
.share-poster-card {
  .n-card__content {
    display: flex;
    flex-direction: column;
    align-items: center;
    .poster-container {
      position: relative;
      img {
        display: block;
      }
      .poster-img {
        width: 400px;
        aspect-ratio: 9/16;
      }
      .poster-name {
        position: absolute;
        left: 50%;
        top: 30%;
        transform: translateX(-50%);
        max-width: 200px;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
        white-space: nowrap;
        font-size: 16px;
        color: #333;
      }
      .poster-time {
        position: absolute;
        left: 50%;
        top: 62%;
        transform: translateX(-50%);
        font-size: 16px;
        color: #333;
      }
      .link-code-img {
        position: absolute;
        left: 50%;
        top: 35%;
        transform: translateX(-50%);
      }
    }

    .n-button {
      margin-top: 8px;
      padding: 4px 25px;
    }
  }
}
</style>
