<template>
    <div class="inner-page-height">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :isNeedCollapse="false"
      :pagination="paginationRef"
      :isBatchDelete="false"
      :is-table-selection="false"
      @paginationChange="paginationChange"
      @filtersChange="filtersChange"
    >
      <template #searchForm>
        <n-form
          ref="formRef"
          :model="model"
          :show-feedback="false"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          size="small"
          :style="{ width: '100%' }"
        >
          <n-form-item>
            <j-search-input
                width="240"
                v-model:value="model.keyword"
                placeholder="请输入角色名称或ID"
                @search="handlerSearch"
            />
          </n-form-item>
        </n-form>
      </template>
      <template #tableHeaderBtn>
        <n-button @click="refresh">刷新</n-button>
        <n-button v-if='hasAddAuthRoleAuth' type="primary" @click="openRoleEditorByMode('add')">新建角色</n-button>
      </template>
    </FormLayout>
    <RoleEditorModal
      :mode="roleEditorModalReactive.mode"
      v-model:show="roleEditorModalReactive.show"
      :params="roleEditorModalReactive.params"
      @refresh="refresh"
    ></RoleEditorModal>
</div>
  </template>
  
  <script setup lang="tsx" >
  import { ref, onMounted, watch, reactive } from "vue";
  import FormLayout from "@/layout/FormLayout.vue";
  import { useTableDefault } from "@/hooks/useTableDefault";
  import {rolePage, getAuthDetailByRoleId,type RolePageResponse, deleteAuthById, copyAuthById, getAuthListTreeV2} from "@/services/api/roleApi/index";
  import type { DataTableColumns } from "naive-ui/es/data-table";
  import { createCacheStorage } from "@/utils/cache/storageCache";
  import { CacheConfig } from "@/utils/cache/config";
  import type { RoleEditorMode } from "./components/RoleEditorModal/type";
  import type { RoleEditorModalProps } from "./components/RoleEditorModal/index.vue";
  import RoleEditorModal from "./components/RoleEditorModal/index.vue";
  import {useMessages} from "@/hooks/useMessage";
  import { isNUllString } from "@/utils/isUtils";
  import CopyOptBtn from "@/components/CopyOptBtn/index.vue";
  import {hasAddAuthRoleAuth,hasCopyAuthRoleAuth,hasEditAuthRoleAuth,hasDeleteAuthRoleAuth} from "./authList"
  const {createMessageError,createMessageSuccess} = useMessages()
  const getRoleDetailLoadingRef = ref(false)
  const isCopyRoleLoadingRef = ref(false)
  const roleEditorModalReactive:RoleEditorModalProps = reactive({
      show:false,
      mode:'add',
      params:{}
  })
  
  /* 表格方法Hook */
  const {
      isAddLoading,
      isEditLoading,
      isDeleteLoading,
      isLoading,
      tableData,
      paginationRef,
      pageTableData,
      deleteTableData,
      editTableData,
      addTableData,
      refreshTableData,
      paginationChange,
  } = useTableDefault({
      pageDataRequest: rolePage,
      addDataRequest: () => { },
      editDataRequest: () => { },
      deleteDataRequest:deleteAuthById,
  });
  
  async function openRoleEditorByMode(mode:RoleEditorMode,params?:RolePageResponse){
      if(!checkAuthTreeIsExist()){
          return
      }
      const {id:roleId,name,type} = params || {}
      roleEditorModalReactive.mode = mode
      if(mode == 'detail' || mode == 'edit'){
          try{
              getRoleDetailLoadingRef.value = true
              const _resp = await getAuthDetailByRoleId(roleId)
              roleEditorModalReactive.params = {
                  id:roleId,
                  name,
                  authCodeList:_resp.authResDTOList.map(item=>item.id),
                  type
              }              
              getRoleDetailLoadingRef.value = false
          }
          catch(e){
              createMessageError(e)
              getRoleDetailLoadingRef.value = false
              return
          }
      } else {
        roleEditorModalReactive.params = {}
      }
      roleEditorModalReactive.show = true
  }
  
  /* 表格项 */
  const tableColumns:DataTableColumns<RolePageResponse> = [
      {
          title: "角色名称",
          key: "name",
          align: "left",
          fixed: "left",
          width: 180,
      },
      {
          title: "角色ID",
          key: "id",
          align: "left",
          width: 180,
          ellipsis: true,
          render: (rowData: any) => {
            return (
              <n-space>
                {rowData.id ? (
                  <CopyOptBtn value={rowData.id} label="ID"></CopyOptBtn>
                ) : '-'}
              </n-space>
            );
          },
      },
      {
          title: "权限内容",
          key: "name",
          align: "left",
          width: 180,
          render: row => {
            
              return (
                <>
                  <n-button
                      disabled={getRoleDetailLoadingRef.value && roleEditorModalReactive.mode == 'detail' }
                      text
                      type="primary"
                      onClick={()=>openRoleEditorByMode('detail',row)}
                  >
                    查看详情
                  </n-button>
                </>
              )
          }
      },
      {
          title: "创建时间",
          key: "createTime",
          align: "left",
          width: 180,
      },
      {
          title: "更新时间",
          key: "updateTime",
          align: "left",
          width: 180,
      },
      {
          title: "操作",
          key: "action",
          width: 180,
          fixed: 'right',
          render(rowData:RolePageResponse) {
              return (
                    (rowData.type != 11 && rowData.type != 12 && rowData.type != 13) && <n-space>
                      {hasCopyAuthRoleAuth && <n-button text type="primary" disabled={isCopyRoleLoadingRef.value} onClick={()=>copyRole(rowData.id)}>复制</n-button>}
                      {(!rowData.isSystem && hasEditAuthRoleAuth) ? <n-button text type="primary"
                          disabled={ getRoleDetailLoadingRef.value && roleEditorModalReactive.mode == 'edit' }
                          onclick={() => openRoleEditorByMode('edit', rowData)}
                      >
                          编辑
                      </n-button>: null}
                      {(!rowData.isSystem && hasDeleteAuthRoleAuth)? 
                          <n-popconfirm 
                              onPositiveClick={()=>deleteTableData(rowData.id,true)} 
                              positive-button-props={{
                                  disabled:isDeleteLoading.value
                              }}
                          >
                              {{
                                default: () => {
                                  return `确认删除该角色吗？`;
                                },
                                trigger: () => {
                                  return (
                                      <n-button text type="error">删除</n-button>
                                  );
                                },
                              }}
                          </n-popconfirm>:null}
                  </n-space>
              )
          }
      },
  ];
  
//   watch(() => roleEditorModalReactive.show,(val) => {
//     if(!val) {
//         roleEditorModalReactive.params = {}
//     }
//   })
  async function copyRole(id:string){
      try{
          isCopyRoleLoadingRef.value = true
          await copyAuthById(id)
          createMessageSuccess('复制角色成功')
          refresh()
      }
      catch(e){
          createMessageError(`复制角色失败:${e}`)
      }
      finally{
          isCopyRoleLoadingRef.value = false
      }
  
  }
  
  /* 初始化参数 */
  const initParams = {
      keyword: null,
  };
  const model = ref({ ...initParams });
  
  /* 红包规则搜索 */
  const handlerSearch = () => {
      const params = {};
      if (!isNUllString(model.value.keyword)) {
        if(model.value.keyword.length == 19){
            params["roleId"] = model.value.keyword;
        }
        else{
            params["name"] = model.value.keyword;
        }
      }
      pageTableData(params, paginationRef.value);
  };
  const filtersChange = ({ searchParams }) => {
      model.value = {
          ...model.value,
          ...searchParams,
      };
      handlerSearch();
  };
  
  /* 刷新表格 */
  function refresh(){
      refreshTableData();
  }
  
  
  async function checkAuthTreeIsExist(){
      const authTreeStorage = createCacheStorage(CacheConfig.AuthListTree);
      const authTreeCache = authTreeStorage.get('platform');
      if(authTreeCache){
          return true
      }else{
          try{
              const _resp = await getAuthListTreeV2()
              authTreeStorage.set(_resp)
              return true
          }
          catch(e){
              return false
          }
      }
  }
  
  /* 组件挂载 */
  onMounted(() => {
      const params =  {};
      pageTableData(params, paginationRef.value);
      checkAuthTreeIsExist()
  });
  
  /* 监听 */
  watch(paginationRef, () => { }, { immediate: true });
  </script>
  
  <style lang="less" scoped>
  @import "@/styles/default.less";
  </style>
  
