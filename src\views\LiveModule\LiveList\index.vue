<template>
  <div class="wrapper inner-page-height">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :pagination="paginationRef"
      @paginationChange="paginationChange"
      :isNeedCollapse="false"
      :isTableSelection="false"
      :isDisplayIndex="false"
    >
      <!-- 表单 -->
      <template #searchForm>
        <n-form
          ref="formRef"
          label-placement="left"
          label-width="auto"
          :show-feedback="false"
          require-mark-placement="right-hanging"
          size="small"
          :style="{ width: '100%' }"
        >
          <n-form-item :span="12" :show-label="false" path="">
            <j-search-input v-model:value.trim="model.name" placeholder="搜索直播间标题" @search="handlerSearch" />
          </n-form-item>
          <n-form-item label="" label-placement="left">
            <j-date-range-picker
              style="flex: 1"
              v-model:value="model.creationTime"
              type="datetimerange"
              format="yyyy-MM-dd"
              :default-time="['00:00:00', '23:59:59']"
              clearable
            />
          </n-form-item>
          <n-form-item label="本人创建" label-placement="left">
            <n-switch v-model:value="model.isOneself" />
          </n-form-item>
        </n-form>
      </template>

      <template #tableHeaderBtn>
        <JAddButton v-if="hasAddLiveAuth" type="primary" @click="handleAddLive('add')">创建直播</JAddButton>
      </template>
    </FormLayout>
    <AddLive ref="AddLiveRef" />
    <ExternalStreamingModal
      v-if="data.externalModalShow"
      v-model:show="data.externalModalShow"
      :pushPath="data.pushPath"
      :streamingCode="data.streamingCode"
      :title="data.title"
    />
    <SharePosterModal
      v-if="data.sharePosterModalShow"
      v-model:show="data.sharePosterModalShow"
      :shareImgUrl="data.shareImgUrl"
      :linkCode="data.linkCode"
      :title="data.title"
      :liveStartTime="data.liveStartTime"
    />
    <ShowProductTable
      v-if="data.productManageShow"
      v-model:show="data.productManageShow"
      :liveActivityId="liveActivityId"
      :shoppingCartEnable="shoppingCartEnable"
      @refresh-table="refresh"
    />
    <LiveStreamingRateTable
      v-if="data.liveRoomLiveRateModalShow"
      v-model:show="data.liveRoomLiveRateModalShow"
      :liveActivityId="liveActivityId"
    />
    <WelfareTicketManage v-model:show="isWelfareTicketManage" :liveRoomId="liveRoomIdRef" :welfarePageType="WelfarePageTypeEnum.LIVEROOM" :liveRoomStatus="liveRoomStatus"/>
  </div>
</template>

<script lang="tsx" setup name="DoctorManagement">
import { computed, onMounted, ref, watch } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { getLivePage, getStreams, getShare, getIframeEmbeddingUrl } from "@/services/api";
import { useMessages } from "@/hooks";
import JImage from "@/components/JImage/index.vue";
import { transformMinioSrc } from "@/utils";
import AddLive from "./components/AddLive.vue";
import ExternalStreamingModal from "./components/ExternalStreamingModal.vue";
import SharePosterModal from "./components/SharePosterModal.vue";
import ShowProductTable from "./components/ShowProductTable.vue";
import WelfareTicketManage from "@/views/StoreModule/SalesManagement/WelfareTicketManage/index.vue";
import { WelfarePageTypeEnum } from "@/views/StoreModule/SalesManagement/WelfareTicketManage/types";
import LiveStreamingRateTable from './components/LiveStreamingRateTable.vue'
import { useWelfareTicketManage } from "@/views/StoreModule/SalesManagement/WelfareTicketManage/hook/useWelfareTicketManage";
import { RoutesName } from "@/enums/routes";
import { routesMap } from "@/router/maps";
import {
  hasEditLiveAuth,
  hasStreamingAddressAuth,
  hasAddLiveAuth,
  hasLiveControlAuth,
  hasLiveShareAuth,
  hasLiveCommodityAuth,
  hasLiveVoucherAuth,
  hasLiveSalesDataAuth,
  hasLiveBroadcastRateAuth,
} from "./authList";
import moment from "moment";
import { useRouter } from "vue-router";

const { createMessageSuccess, createMessageError } = useMessages();
const { liveRoomIdRef } = useWelfareTicketManage();
const liveRoomStatus = ref(null)
/** 表格hook */
const { isLoading, tableData, paginationRef, pageTableData, paginationChange } = useTableDefault({
  pageDataRequest: getLivePage,
});
const moreSelectOptions = computed(() => {
  const options = [];
  if (hasLiveSalesDataAuth) {
    options.push({
      label: "销售数据大屏",
      key: "salesDataDashboard",
    });
  }
  if (hasLiveBroadcastRateAuth) {
    options.push({
      label: "直播间直播率",
      key: "liveRoomLiveRate",
    });
  }
  return options;
});
const data = ref({
  externalModalShow: false,
  sharePosterModalShow: false,
  productManageShow:false,
  liveRoomLiveRateModalShow:false,
  title: "",
  streamingCode: "",
  pushPath: "",
  shareImgUrl: "",
  linkCode: "",
  liveStartTime:""

});
/* 表格列表项 */
const tableColumns = ref([
  {
    title: "直播封面",
    key: "verticalCoverImage",
    align: "center",
    fixed: "left",
    width: 50,
    render: rowData => {
      return <JImage width="180" height="100" imgPath={rowData.verticalCoverImage} />;
    },
  },
  {
    title: "直播标题",
    key: "name",
    width: 80,
    align: "center",
  },
  {
    title: "直播时间",
    key: "liveTime",
    align: "center",
    width: 50,
    render: rowData => {
      return (
        <div>
          {rowData.liveStartTime} - {rowData.liveEndTime}
        </div>
      );
    },
  },

  {
    title: "状态",
    key: "status",
    width: 50,
    align: "center",
    render: rowData => {
      return (
        <div
          class="live-status"
          style={{ backgroundColor: ["", "#4BE092", "#FFC107", "#989898"][rowData.status] }}>
          {["", "直播中", "未开始", "已结束"][rowData.status]}
        </div>
      );
    },
  },

  {
    title: "操作",
    key: "action",
    fixed: "right",
    align: "left",
    render: row => {
      return (
        <n-space align="center" justify="center">
          {hasStreamingAddressAuth ? (
            <n-button text type="primary" onClick={() => handleStreamingAddress(row)}>
              推流地址
            </n-button>
          ) : null}
          {hasLiveControlAuth ? (
            <n-button text type="primary" onClick={() => handleLiveControl(row)}>
              直播控制台
            </n-button>
          ) : null}
          {hasLiveShareAuth ? (
            <n-button text type="primary" onClick={() => handleSharePoster(row)}>
              分享
            </n-button>
          ) : null}
          {hasLiveCommodityAuth ? (
            <n-button text type="primary" onClick={() => handle‌ProductManagement(row)}>
              商品管理
            </n-button>
          ) : null}
          {hasLiveVoucherAuth ? (
            <n-button text type="primary" onClick={() => handleWelfareTicketManage(row)}>
              福利券管理
            </n-button>
          ) : null}

          {hasEditLiveAuth ? (
            <n-button text type="primary" onClick={() => handleAddLive("edit", row)}>
              编辑
            </n-button>
          ) : null}
          {hasLiveSalesDataAuth || hasLiveBroadcastRateAuth ? (
            <table-more-select
              options={moreSelectOptions.value}
              onOnClick={key => onClickMore(key, row)}></table-more-select>
          ) : null}
        </n-space>
      );
    },
  },
]);
const onClickMore = (key: string, row) => {
  if (key === "salesDataDashboard") {
    const _routeInfo = routesMap[RoutesName.LiveDashboard];
    const url = `${window.location.origin}/#${_routeInfo.path}?id=${row.id}`;
    window.open(url, '_blank');
  }
  if (key === "liveRoomLiveRate") {
    data.value.liveRoomLiveRateModalShow = true
    liveActivityId.value = row.id
  }
};

const isWelfareTicketManage = ref(false);
const handleWelfareTicketManage = async (row) => {
  liveRoomStatus.value = row.status;
  liveRoomIdRef.value = row.id;
  isWelfareTicketManage.value = true;
};
const AddLiveRef = ref();
const handleAddLive = (type, row?) => {
  const _params = {
    type,
    row,
    refresh: refresh,
  };
  AddLiveRef.value?.acceptParams(_params);
};
const handleStreamingAddress = async row => {
  const res = await getStreams({
    id: row.id,
  });
  data.value.externalModalShow = true;
  data.value.title = row.name;
  data.value.pushPath = res?.pushPath;
  data.value.streamingCode = res?.streamingCode;
};
const handleSharePoster = async row => {
  const res = await getShare({
    id: row.id,
  });
  data.value.sharePosterModalShow = true;
  data.value.shareImgUrl = transformMinioSrc(res?.shareCoverImage);
  data.value.linkCode = res?.link;
  data.value.title = res?.name;
  data.value.liveStartTime = res?.liveStartTime;
};
const liveActivityId = ref('')
const shoppingCartEnable = ref(0)
const handle‌ProductManagement = (row) =>{
  data.value.productManageShow = true
  liveActivityId.value = row.id
  shoppingCartEnable.value = row.shoppingCartEnable
}
const handleLiveControl = async (row) => {
const url = await getIframeEmbeddingUrl({ id: row.id })
  // 在新标签页中打开
  window.open(url, "_blank");
};
/** 参数 */
const model = ref({
  name: "",
  creationTime: null,
  isOneself: false,
});

/** 获取参数 */
const getParams = () => {
  const { name, creationTime, isOneself } = model.value;
  const liveStartTime = creationTime ? moment(creationTime[0]).format(`YYYY-MM-DD HH:mm:ss`) : null;
  const liveEndTime = creationTime ? moment(creationTime[1]).format(`YYYY-MM-DD HH:mm:ss`) : null;
  return {
    name,
    queryCurrUser: Number(isOneself),
    liveStartTime,
    liveEndTime,
  };
};

/** 搜索 */
const handlerSearch = () => {
  tableSearch();
};

/** 表格刷新 */
function refresh() {
  tableSearch();
}

/* 表格搜索 */
const tableSearch = () => {
  pageTableData(getParams(), paginationRef.value);
};

/** 监听 */
watch(
  () => [model.value.creationTime, model.value.isOneself],
  () => {
    tableSearch();
  },
);

/** 组件挂载 */
onMounted(() => {
  tableSearch();
});
</script>

<style lang="less" scoped>
@import "@/styles/defaultVar.less";
</style>
<style lang="less">
.live-status {
  width: 65px;
  height: 25px;
  line-height: 25px;
  text-align: center;
  color: #fff;
  border-radius: 4px;
}
</style>
