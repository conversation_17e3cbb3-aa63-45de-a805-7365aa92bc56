<template>
  <JDrawer
    v-model:show="show"
    :title="title"
    :isGetLoading="isGetLoading"
    @after-leave="handleAfterLeave"
    @after-enter="handleAfterEnter"
    :mainStyle="{ padding: '12px 0px 0px 12px' }"
  >
    <!-- 基本信息 -->
    <template #content>
      <NForm
        ref="formRef"
        :model="model"
        :rules="rules"
        label-placement="left"
        label-width="120"
        require-mark-placement="right-hanging"
        size="small"
        :style="{ width: '100%' }"
      >
        <NGrid :cols="12" :x-gap="12">
          <!-- 基本信息 -->
          <NGi :span="12">
            <StoreTitle title="基本信息" style="margin-bottom: 12px;" />
          </NGi>
          <!-- 管理名称 -->
          <NFormItemGi :span="10" label="管理名称" path="name">
            <NInput
              v-model:value="model.name"
              placeholder="请输入管理名称"
              :maxlength="60"
              clearable
              style="width: 680px;"
            />
          </NFormItemGi>
          <!-- 前端名称 -->
          <NFormItemGi :span="10" label="前端名称" path="frontName">
            <div style="width: 100%; display: flex; flex-direction: column;">
              <NInput
                v-model:value="model.frontName"
                placeholder="请输入前端名称"
                :maxlength="60"
                clearable
                style="width: 680px;"
              />
              <span style="margin-top: 4px;font-size: 14px;">注：前端商城小程序显示使用</span>
            </div>
          </NFormItemGi>
          <!-- 编号 1.0.5版本去掉编号 -->
          <!-- <NFormItemGi :span="10" label="编号">
            <NInput v-model:value="model.code" placeholder="请输入编号" :maxlength="60" clearable />
          </NFormItemGi> -->
          <!-- 所属分类 -->
          <NFormItemGi :span="10" label="所属分类" path="cateId">
            <JProductTreeSelector
              style="width: 680px;"
              v-model:value="model.cateId"
              :type="GoodsCategoryType.GENERAL"
              isImmediately
            />
          </NFormItemGi>
          <!-- 1.3.0版本 供应商 -->
          <NFormItemGi :span="10" label="供应商">
            <JSupplierSelector
              style="width: 680px;"
              v-model:value="model.supplierId"
              :extraId="isEditMode ? model.supplierId : ''"
            />
          </NFormItemGi>
          <!-- spu编码。可录入英文字母、数字。最多40字符，多余自动删除。默认为空。可为空 -->
          <NFormItemGi :span="10" label="spu编码">
            <NInput
              v-model:value="model.spu"
              placeholder="请输入spu编码"
              :maxlength="40"
              clearable
              style="width: 680px;"
              :allow-input="(value)=>!value || /^[a-zA-Z0-9]+$/.test(value)"
            />
          </NFormItemGi>
          <!-- 商品图片 -->
          <NGi :span="12">
            <NGrid :cols="24" :x-gap="12">
              <NFormItemGi :span="2" label="商品图片"></NFormItemGi>
              <NFormItemGi
                :span="6"
                label="首图"
                :path="model.productFirstImg.length || model.productMoreImg.length ? '' : 'productFirstImg'"
              >
                <UploadProductImg v-model:value="model.productFirstImg" accept="image/*" :fileListSize="1" />
              </NFormItemGi>
              <NFormItemGi
                :span="16"
                label="更多图片"
                :path="model.productFirstImg.length || model.productMoreImg.length ? '' : 'productMoreImg'"
              >
                <UploadProductImg
                  ref="uploadProductImgRef"
                  v-model:value="model.productMoreImg"
                  accept="image/*"
                  :fileListSize="8"
                  is-multiple
                />
              </NFormItemGi>
            </NGrid>
          </NGi>
          <!-- 商品视频 *******版本新增 -->
          <NFormItemGi :span="10" label="商品视频">
            <NFlex vertical :size="4">
              <UploadProductVideo v-model:value="model.productVideoPath" />
              <span style="font-size: 14px;">
                注：只支持mp4格式，视频时长不超过60秒，视频大小不超过200M，视频将默认展示在商品轮播图之前
              </span>
            </NFlex>
          </NFormItemGi>
          <!-- 是否虚拟商品 1.0.6版本增加 -->
          <NFormItemGi v-if="systemStore._globalConfig['virtualProduct']" :span="10" label="商品属性" path="isVirtual">
            <NRadioGroup v-model:value="model.isVirtual" name="radiogroup" :disabled="pattern === 'edit'">
              <NFlex>
                <NRadio
                  v-for="option in [{ label: '实物商品', value: GoodsIsVirtualType.NOVIRTUAL }, { label: '虚拟商品', value: GoodsIsVirtualType.ISVIRTUAL }]"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </NRadio>
              </NFlex>
            </NRadioGroup>
          </NFormItemGi>
          <!-- 每人购买上限 1.0.8版本增加 -->
          <NFormItemGi :span="10" label="每人购买上限" path="upperLimit">
            <NInputNumber
              v-model:value="model.upperLimit"
              placeholder="请输入购买上限"
              :min="0"
              :max="99999"
              :precision="0"
              :show-button="false"
            />
            <span style="margin-left: 12px;font-size: 14px;">注：0表示不设限</span>
          </NFormItemGi>
          <!-- 销售场景 1.0.9.0版本增加 -->
          <NFormItemGi
            :span="10"
            label="销售场景"
            path="sellingScenario"
            :style="{height: model.sellingScenario.includes(ProductSalesSceneEnum.Storefront) ? '168px' : 'auto' }"
          >
            <NCheckboxGroup v-model:value="model.sellingScenario" @update:value="handleSellingScenarioChange">
              <NFlex style="margin-bottom: 12px;">
                <NCheckbox :value="ProductSalesSceneEnum.Store" label="商城" />
                <NCheckbox :value="ProductSalesSceneEnum.Social">
                  <NFlex align="center" :size="2">
                    <span>社群</span>
                    <HelpPopover helpEntry="勾选社群" size="18" />
                  </NFlex>
                </NCheckbox>
              </NFlex>

              <!-- 在商城中隐藏 -->
              <NCheckbox
                :value="ProductSalesSceneEnum.IsHideInStore"
                :disabled="!model.sellingScenario.includes(ProductSalesSceneEnum.Store)"
              >
                <NFlex align="center" :size="2">
                  <span>在商城中隐藏</span>
                  <HelpPopover helpEntry="在商城中隐藏" size="18" />
                </NFlex>
              </NCheckbox>

              <!-- 门店 -->
              <NFlex style="margin-top: 12px;">
                <NCheckbox :value="ProductSalesSceneEnum.Storefront">
                  <NFlex align="center" :size="2">
                    <span>门店</span>
                    <HelpPopover helpEntry="门店" size="18" />
                  </NFlex>
                </NCheckbox>
              </NFlex>
            </NCheckboxGroup>
            <!-- 门店下拉选择 -->
            <div class="store-select" v-if="model.sellingScenario.includes(ProductSalesSceneEnum.Storefront)">
              <JStoreSelect style="width: 460px;" v-model:value="model.productStoreRelationVOList.storeId" multiple />
              <NFlex align="center" :size="2">
                <span>商品在门店中可见范围</span>
                <HelpPopover helpEntry="门店可见范围" size="18" />
              </NFlex>
              <!-- 数据可见范围 -->
              <NCheckboxGroup v-model:value="model.productStoreRelationVOList.visibleScope">
                <NCheckbox :value="StoreVisibleRangeEnum.Normal" label="普通用户" />
                <NCheckbox :value="StoreVisibleRangeEnum.StoreKeeper" label="非普通用户" />
              </NCheckboxGroup>
            </div>
          </NFormItemGi>
          <NFormItemGi :span="10" label="提货方式" required>
            <NFlex vertical>
              <NFlex>
                <NRadioGroup v-model:value="model.deliveryType" name="radiogroup">
                  <NRadio
                    v-for="option in [{ label: '到店自提', value: PickUpGoodsType.PICKUP }, { label: '快递到家', value: PickUpGoodsType.EXPRESSAGE }]"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </NRadio>
                </NRadioGroup>
                <div style="font-size: 14px;">（仅对百货门店模式生效，对小程序商城不生效）</div>
              </NFlex>
              <NFlex v-if="model.deliveryType === PickUpGoodsType.PICKUP" align="center" :size="2">
                <NRadioGroup
                  v-model:value="model.verificationType"
                  name="radiogroup"
                >
                  <NRadio v-for="option in pickUpGoodsSubTypeOptions" :key="option.value" :value="option.value">
                    {{ option.label }}
                  </NRadio>
                </NRadioGroup>
                <HelpPopover helpEntry="自提子项" size="18" />
              </NFlex>
            </NFlex>
          </NFormItemGi>
          <NFormItemGi :span="10" label="现金抵扣券配置">
            <NRadioGroup v-model:value="model.isUseCashCoupon" name="radiogroup">
              <NFlex>
                <NRadio
                  v-for="option in [{ label: '不可使用现金抵扣券', value: 0 }, { label: '可使用现金抵扣券', value: 1 }]"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </NRadio>
              </NFlex>
            </NRadioGroup>
            <div v-if="model.isUseCashCoupon === 1 || pattern === 'edit'" style="font-size: 14px;">
              <NInput
                v-model:value="model.cashCouponRatio"
                placeholder=" "
                :disabled="pattern === 'edit' && model.isUseCashCoupon === 0"
                :maxlength="2"
                clearable
                style="width: 150px;"
              />
              % （使用的现金抵扣券金额总额不得超过该笔订单商品金额*该比例值的金额）
            </div>
          </NFormItemGi>
          <!-- 售价与库存 -->
          <NGi :span="12">
            <StoreTitle style="margin-bottom: 12px;">
              <span>商品规格设置</span>
              <span style="color: #FF4D4F;">*</span>
            </StoreTitle>
          </NGi>
          <!-- 规格 -->
          <NFormItemGi :span="12" :show-label="false" path="productSpecVOList">
            <PriceAndStock
              v-if="!isGetLoading"
              :productId="model.id"
              v-model:productAttributeValueVOList="model.productAttributeValueVOList"
              v-model:productSpecVOList="model.productSpecVOList"
              :isPointEnable="props.isPointEnable"
              :isVirtual="model.isVirtual === GoodsIsVirtualType.ISVIRTUAL"
              :type="pattern"
              @productSpecVOList="handleProductSpecVOList"
            />
          </NFormItemGi>

          <!-- 图文描述 -->
          <NGi :span="12">
            <StoreTitle title="图文描述" style="margin-bottom: 12px;padding-right: 12px;" />
          </NGi>
          <!-- 描述 -->
          <NGi :span="12">
            <div style="height:520px; width: calc(100% - 12px); margin-bottom: 52px;">
              <div id="desc-richtext-container" ref="descRichTextDomRef" style="height:100%;width:100%;"></div>
            </div>
          </NGi>
          <!-- 更多设置 -->
          <NGi :span="12">
            <StoreTitle title="更多设置" style="margin-bottom: 12px;margin-top: 8px;" />
          </NGi>
          <!-- 社群经销商分账 -->
          <NGi :span="50" style="height: 34px;">
            <NGrid :cols="50" :x-gap="25">
              <NFormItemGi :span="1">
                <NCheckboxGroup v-model:value="model.splitAccountCheck">
                  <NCheckbox value="splitAccountValue" size="medium" />
                </NCheckboxGroup>
              </NFormItemGi>

              <NFormItemGi
                label-width="auto"
                :span="24"
                label="给社群端经销商分账，分账比例:"
                :path="splitAccountComputed"
              >
                <NInputNumber
                  v-model:value="model.splitAccount"
                  :show-button="false"
                  :max="80"
                  :min="1"
                  placeholder="可输入1-80"
                  style="width: 90px;"
                  :disabled="splitAccountComputed != 'splitAccount'"
                  @blur="handleSplitAccount"
                />
                <span style="font-size: 14px;margin-left: 5px;">
                  {{'%  (使用群管易社群系统的客户可在线给经销商分账)'}}
                </span>
              </NFormItemGi>
            </NGrid>
          </NGi>

          <!-- 备注 -->
          <NGi :span="12">
            <div style="font-size: 12px;margin-left: 30px;margin-bottom: 12px;">
              <p>备注:</p>
              <p>开展分账业务需在“财务管理”》“分账规则设置”界面设置分账规则</p>
            </div>
          </NGi>

          <!-- 分销员分佣 -->
          <NGi :span="50" style="height: 34px;">
            <NGrid :cols="50" :x-gap="25">
              <NFormItemGi :span="1">
                <NCheckboxGroup v-model:value="model.distributorsCheck">
                  <NCheckbox value="distributorsValue" size="medium" />
                </NCheckboxGroup>
              </NFormItemGi>

              <NFormItemGi
                label-width="auto"
                class="distributors"
                :span="39"
                label="给分销员分佣，佣金比例"
                :path="distributorsComputed"
              >
                <NInputNumber
                  v-model:value="model.distributorAccount"
                  :show-button="false"
                  :max="80"
                  :min="0"
                  placeholder="可输入0-80"
                  style="width: 90px;"
                  :disabled="distributorsComputed != 'distributorAccount'"
                  @blur="handleSplitAccount"
                />
                <span style="font-size: 14px;margin-left: 5px;">{{'%'}}</span>
              </NFormItemGi>
            </NGrid>
          </NGi>

          <!-- 备注 -->
          <NGi :span="12">
            <div style="font-size: 12px;margin-left: 30px;margin-bottom: 12px;">
              <p>备注:</p>
              <p>1、开展分销业务需在【分销】》【分销设置】界面开启分销功能</p>
              <p>2、按商品销售额计算佣金，佣金金额 = 商品销售总额 * 佣金比例</p>
              <p>3、如果社群端经销商也参与分账，分账分佣总额占订单总额的比例不能大于80%</p>
            </div>
          </NGi>

          <NGi :span="12">
            <div v-if="isDistributorChecked" style="font-size: 12px;margin-left: 30px;margin-bottom: 12px;">
              <p style="color: #FF4D4F;font-size: 14px;margin-top: 10px;">
                当前设置经销商分账和分销员佣金总额可能占订单总额的{{total}}%或以上
              </p>
              <p>
                计算公式：分账分佣总额占订单总额的比例 = （商品总售价-总成本价）*经销商分账比例 / 商品总售价 +
                分销员分佣比例
              </p>
            </div>
          </NGi>
        </NGrid>
      </NForm>
    </template>
    
    <!-- Footer -->
    <template #footer v-if="pattern !== 'view'">
      <div class="footer-wrapper">
        <!-- 商品是否上架 -->
        <JCheckbox v-model:checked="model.isPublish" style="margin-left: 24px;">
          <span style="font-size: 16px;">上架</span>
        </JCheckbox>
        <NFlex>
          <NButton @click="show = false" class="store-button">取 消</NButton>
          <NButton type="primary" :loading="isLoading" @click="_save" class="store-button">保 存</NButton>
        </NFlex>
      </div>
    </template>
  </JDrawer>
</template>

<script lang="ts" setup name="NewGeneralGoods">
import { ref, computed, nextTick, watch, reactive, effectScope, onScopeDispose } from "vue";
import { createDummyId, deepClone, isArray } from "@/utils";
import type { FormRules, FormItemRule } from "naive-ui";
import { isObject, _debounce } from "@/utils";
import { addGeneralGoods, getGeneralGoodsById, updateGeneralGoods, uploadRichTextResouce } from "@/services/api";
import { useMessages } from '@/hooks';
import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
import Quill from 'quill';
import "@/assets/quill/quill.snow.css";
// import quillDropdownIcon from 'quill/assets/icons/dropdown.svg';
import JDrawer from "@/components/JDrawer/index.vue";
import UploadProductImg from "@/components/UploadProductImg/index.vue";
import { getOssUrlPrefix } from "@/utils/http/urlUtils";
import { createVideoElement } from "@/utils/quill/createVideoElement";
import type { GoodsType } from "@/enums";
import {
  GoodsCategoryType,
  GoodsIsVirtualType,
  PickUpGoodsType,
  ProductSalesSceneEnum,
  IsHideInStoreEnum,
  SalesSceneStoreGoodsTypeEnum,
  StoreVisibleRangeEnum,
  PickUpGoodsSubType
 } from "@/enums";
import { UPPERDEFAULTVALUE } from "../types";
import useCompatibility from "../hooks/useCompatibility";
/** 相关组件 */
import PriceAndStock from "./PriceAndStock/index.vue";

/** 自提子项 */
const pickUpGoodsSubTypeOptions = [
  {
    label: '下单后待核销',
    value: PickUpGoodsSubType.ORDER
  },
  {
    label: '下单自动核销',
    value: PickUpGoodsSubType.AUTOMATIC
  },
  {
    label: '下单门店到货后核销',
    value: PickUpGoodsSubType.STOREARRIVAL
  }
];

/** props */
const props = withDefaults(defineProps<{
  to?: string; // 弹窗位置
  isPointEnable?: boolean; // 是否启用积分功能
  isT9Sync?: boolean; // 是否同步到T9
  refreshTable?: () => void; // 刷新表格数据
}>(), {
  to: '.table-wrapper',
  isPointEnable: false
});

const scope = effectScope();
const { handleOldData } = useCompatibility();
const systemStore = useSystemStoreWithoutSetup();
const { createMessageSuccess, createMessageError } = useMessages();
const descRichTextDomRef = ref(null);
/** 是否编辑模式 */
const isEditMode = computed(() => pattern.value === 'edit');

/** 显隐 */
const show = ref(false);

/* 表单实例 */
const formRef = ref();

/** 多文件上传实例 */
const uploadProductImgRef = ref<InstanceType<typeof UploadProductImg> | null>(null);

/* 表单参数初始化 */
const initParams = {
  id: null, // 编辑时，商品id
  type: GoodsCategoryType.GENERAL, // 商品类别

  name: null, // 管理名称
  frontName: null, // 前端名称
  code: null, // 编号
  isVirtual: GoodsIsVirtualType.NOVIRTUAL, // 是否虚拟商品
  cateId: null, // 所属分类
  supplierId: null, // 供应商(1.3.0版本)
  spu: null, // spu编码
  productFirstImg: [], // 首图
  productMoreImg: [], // 更多图片

  upperLimit: 0, // 每订单上限(1.0.8版本)
  sellingScenario: [ProductSalesSceneEnum.Store, ProductSalesSceneEnum.Social], // 销售场景(1.0.9.0版本)
  splitAccountCheck: [],// 分组复选框值
  distributorsCheck: [],// 分销员选框值
  distributorAccount: null,// 佣金比例
  splitAccount: null,// 分账比例
  productSpecVOList: [{
    id: createDummyId(), // 标识
    name: null, // 规格名称
    price: null, // 售价（元）
    availStocks: null, // 可用库存
    lockedStocks: 0, // 冻结库存
    upper: UPPERDEFAULTVALUE, // 每订单上限
    initSaled: 0, // 初始已售（前端展示）
    isDownPayment: 0 as 0 | 1, // 是否支持定金支付。0=否；1=是
    isCashOnDelivery: 0 as 0 | 1, // 是否支持物流代收。0=否；1=是
    downPayment: null, // 定金单价，单位分
    points: null, // 可获得积分
    activityPriceVOList: [], // 活动价设置
    sku: null, // 商品编码
    costPrice: null, //成本价
    marketPrice: null //划线价
  }], // 售价与库存

  /** 规格属性数组 */
  productAttributeValueVOList: [],
  desc: null, // 图文描述

  isPublish: false, // 商品是否上架

  tempEditSpecIds: [], // 编辑时，临时规格id

  productVideoPath: null, // *******版本 视频地址
  // 1.3.0版本
  productStoreRelationVOList: {
    storeId: null, // 门店id
    visibleScope: [StoreVisibleRangeEnum.Normal, StoreVisibleRangeEnum.StoreKeeper], // 门店可见范围(0所有 1普通用户 2店员/店长 3不可见)
    productType: SalesSceneStoreGoodsTypeEnum.Normal, // 门店商品类型(1普通商品 2福利商品)
  },
  deliveryType: PickUpGoodsType.PICKUP,
  verificationType: PickUpGoodsSubType.ORDER,
  isUseCashCoupon: 0,
  cashCouponRatio: 10
};
const model = ref(deepClone(initParams));

let quill: Quill = null;
/* 接收父组件传过来的参数 */
const pattern = ref<'add' | 'edit' | 'view'>('add');
const acceptParams = (params: {
  cateId?: string | null; // 商品分类Id
  row: Partial<ApiStoreModule.Goods>;
  type: 'add' | 'edit' | 'view';
  productTypes: GoodsType;
}) => {
  pattern.value = params.type;
  // 当前新增商品分类id
  if (params.productTypes == GoodsCategoryType.DRUG) {
    model.value.cateId = params.cateId ?? null;
  }
  if (isObject(params.row) && Object.keys(params.row).length !== 0) {
    if (params.type === 'edit' || params.type === 'view') {
      model.value.id = params.row?.id;
    }
  }
  show.value = true;
};

/** 标题 */
const title = computed(() => {
  const titleMap: Record<'add' | 'edit' | 'view', string> = {
    add: '新建普通商品',
    edit: '编辑普通商品',
    view: '普通商品详情',
  };
  return titleMap[pattern.value];
});

const isDistributorChecked  = computed (() =>{
  return (model.value.distributorsCheck.includes('distributorsValue') ? true : false) && (model.value.splitAccountCheck.includes('splitAccountValue') ? true : false)
})

/* 表单规则 */
const rules: FormRules = reactive(
  {
  isVirtual: {
    type: "number",
    required: true,
    trigger: ["blur", "input"],
    message: "请输入商品属性",
  },
  upperLimit: {
    type: "number",
    required: true,
    trigger: ["blur", "input"],
    message: "请输入每人购买上限",
  },
  name: {
    type: "string",
    required: true,
    trigger: ["blur", "input"],
    message: "请输入管理名称",
  },
  frontName: {
    type: "string",
    required: true,
    trigger: ["blur", "input"],
    message: "请输入前端名称",
  },
  cateId: {
    type: "string",
    required: true,
    trigger: ["blur", "input"],
    message: "请选择所属分类",
  },
  productFirstImg: {
    type: "array",
    required: true,
    trigger: ["blur", "input"],
    message: "请选择商品图片",
  },
  productMoreImg: {
    type: "array",
    required: true,
    trigger: ["blur", "input"],
    message: "请选择商品图片",
  },
  productSpecVOList: {
    type: "array",
    required: true,
    validator: (rule: FormItemRule, value: Array<{
      id: string,
      name: string,
      price: number,
      availStocks: number,
      upper: number,
      initSaled: number,
      sku: string,
      costPrice:number
    }>) => {
      if (model.value.productAttributeValueVOList.length === 0 || model.value.productAttributeValueVOList[0].attributeValue.length === 0) {
        return false
      }
      if (value?.length > 0) {
        let check = value?.every(product => {
          if (props.isT9Sync) {
            return (
              product.price !== null &&
              product.availStocks !== null &&
              product.upper !== null &&
              product.initSaled !== null &&
              product.sku !== null &&
              product.sku !== "" &&
              product.sku !== undefined &&
              (model.value.splitAccountCheck.includes('splitAccountValue') ? product.costPrice !== null : true)
            );
          } else {
            return (
              product.price !== null &&
              product.availStocks !== null &&
              product.upper !== null &&
              product.initSaled !== null &&
              (model.value.splitAccountCheck.includes('splitAccountValue') ? product.costPrice !== null : true)
            );
          }
        });
        return check;
      }
    },
    trigger: ['blur', 'change'],
    renderMessage:()=>{
      if (props.isT9Sync) {
        return `请输入商品规格、售价、${
          (splitAccountComputed.value == 'splitAccount' ? '成本价、' : '')
        }库存`;
      } else {
        return `请输入商品规格、售价、${
         (splitAccountComputed.value == 'splitAccount' ? '成本价、' : '')
        }库存`;
      }
    }
  },
  sellingScenario: {
    type: "array",
    required: true,
    validator: (rule: FormItemRule, value) => {
      // 校验至少选择一个
      if (!value || value.length === 0) {
        return false;
      }
      return true;
    },
    trigger: ['change'],
    message: "请选择销售场景，至少选择一个",
  },
  splitAccount:{
    type: "number",
    required: true,
    trigger: ["blur", "change"],
    message: "请输入分账比例",
  },
  distributorAccount:{
    type: "number",
    required: true,
    trigger: ["blur", "change"],
    message: "请输入佣金比例",
  }
 }
);

/** 关闭抽屉回调 */
const handleAfterLeave = () => {
  // 初始化参数
  model.value = deepClone(initParams);
};

/** 抽屉出现后的回调 */
const isGetLoading = ref(true);
const handleAfterEnter = async () => {
  try {
    if (show.value && (pattern.value === 'edit' || pattern.value === 'view')) {
      isGetLoading.value = true;
      const resp = await getGeneralGoodsById(model.value.id);
      // 解构
      const {
        productEntityVO,
        productImgVOList,
        productSpecVOList,
        pointConfigDTOSList,
        productStoreRelationVOList: productStoreRelationVOListResp,
        productAttributeValueVOList
      } = resp;
      if (productAttributeValueVOList.length==0) {
        const {  productAttributeValueOld } = await handleOldData(productSpecVOList,productEntityVO.id);
        // productSpecVOList = productSpecOld;
        productAttributeValueVOList.push(...productAttributeValueOld);

      }

      // 商品基础信息
      const {
        isPublish,
        desc,
        sellingScenario,
        isAllocation,
        dealerAllocationRatio,
        isDistribution,
        distributionRatio,
        isHidden,
        isStoreProduct
      } = productEntityVO;

      // 过滤图片与视频
      const productFirstImg = productImgVOList.filter(img => img.isFirst === 1 && img.type === 0);
      const productMoreImg = productImgVOList.filter(img => img.isFirst !== 1 && img.type === 0);
      const productVideoPath = productImgVOList.filter(img => img.type === 1);

      Object.assign(model.value, {
        ...productEntityVO,
        splitAccountCheck: isAllocation == 1 ? ['splitAccountValue'] : [],
        splitAccount: dealerAllocationRatio ? dealerAllocationRatio : null,
        distributorsCheck: isDistribution == 1 ? ['distributorsValue'] : [],
        distributorAccount: distributionRatio,
        isPublish: !!isPublish,
        sellingScenario: getSalesScenes(sellingScenario, isHidden, !!isStoreProduct), // 销售场景
        productAttributeValueVOList,
        productSpecVOList: productSpecVOList?.length > 0 ? productSpecVOList.map(({ costPrice,price,marketPrice, ...rest }) => ({ ...rest, price: price / 100, costPrice: (costPrice !==undefined ? (costPrice  / 100 ) : null),marketPrice: (marketPrice !==undefined ? (marketPrice  / 100 ) : null)})) : [
          {
            id: createDummyId(),
            name: null,
            price: null,
            availStocks: null,
            lockedStocks: 0,
            upper: UPPERDEFAULTVALUE,
            initSaled: 0,
            isDownPayment: 0 as 0 | 1,
            isCashOnDelivery: 0 as 0 | 1,
            downPayment: null,
            points: null,
            costPrice:null
          }
        ],
        productFirstImg,
        productMoreImg,
        productVideoPath: productVideoPath.length > 0 ? productVideoPath : null,
        /** 1.3.0 门店信息 */
        productStoreRelationVOList: {
          storeId: isArray(productStoreRelationVOListResp) ? productStoreRelationVOListResp.map(({ storeId }) => storeId)  : null,
          visibleScope: isArray(productStoreRelationVOListResp) ? getVisibleScope(productStoreRelationVOListResp[0]?.visibleScope) : []
        },
      });
      quill.root.innerHTML = desc;

      // 商品积分配置
      for (let i = 0; i < pointConfigDTOSList.length; i++) {
        for (let j = 0; j < model.value.productSpecVOList.length; j++) {
          if (pointConfigDTOSList[i]?.sourceId === model.value.productSpecVOList[j]?.id) {
            model.value.productSpecVOList[j]['points'] = pointConfigDTOSList[i]?.points;
            model.value.productSpecVOList[j]['sourceId'] = pointConfigDTOSList[i]?.sourceId;
            model.value.productSpecVOList[j]['pointConfigId'] = pointConfigDTOSList[i]?.id;
          }
        }
      }
    }
  } catch (error) {
    createMessageError('查询商品失败: ' + error);
  } finally {
    isGetLoading.value = false;
  }
};

/**
 * @description 编辑时，门店可见范围处理
 */
 function getVisibleScope(scope: StoreVisibleRangeEnum): StoreVisibleRangeEnum[] {
  const visibleMap: Record<StoreVisibleRangeEnum, StoreVisibleRangeEnum[]> = {
    [StoreVisibleRangeEnum.All]: [StoreVisibleRangeEnum.Normal, StoreVisibleRangeEnum.StoreKeeper],
    [StoreVisibleRangeEnum.Normal]: [StoreVisibleRangeEnum.Normal],
    [StoreVisibleRangeEnum.StoreKeeper]: [StoreVisibleRangeEnum.StoreKeeper],
    [StoreVisibleRangeEnum.NotVisible]: [],
  };

  return visibleMap[scope] ?? [];
}

/**
 * @description 编辑时，销售场景处理
 * @param {ProductSalesSceneEnum} sellingScenario
 * @param {IsHideInStoreEnum} isHidden
 * @param {boolean} isStoreProduct
 */
function getSalesScenes(
  sellingScenario: ProductSalesSceneEnum,
  isHidden: IsHideInStoreEnum,
  isStoreProduct: boolean,
): ProductSalesSceneEnum[] {
  let scenes: ProductSalesSceneEnum[] = [];
  if (sellingScenario === ProductSalesSceneEnum.ALL) {
    scenes = [
      ProductSalesSceneEnum.Store,
      ProductSalesSceneEnum.Social,
      ...(isHidden === IsHideInStoreEnum.Hide ? [ProductSalesSceneEnum.IsHideInStore] : [])
    ];
  } else {
    scenes = [
      sellingScenario,
      ...(isHidden === IsHideInStoreEnum.Hide ? [ProductSalesSceneEnum.IsHideInStore] : [])
    ];
  }

  // 判断是否门店商品
  if (isStoreProduct) {
    scenes = [...scenes, ProductSalesSceneEnum.Storefront];
  }

  return scenes;
}

/** 获取新增普通商品参数 */
const getAddParams = () => {
  const {
    name,
    type,
    cateId,
    code,
    isPublish,
    frontName,
    productFirstImg,
    productMoreImg,
    productSpecVOList,
    productVideoPath,
    isVirtual,
    upperLimit,
    sellingScenario,
    splitAccountCheck,
    splitAccount,
    distributorsCheck,
    distributorAccount,
    supplierId,
    spu,
    deliveryType,
    verificationType,
    isUseCashCoupon,
    cashCouponRatio,
    productStoreRelationVOList: { storeId, visibleScope, productType },
    productAttributeValueVOList,
  } = model.value;

  // 商品图片
  const imgList = deepClone(productMoreImg).map(item => ({ ...item, type: 0 }));
  if (productFirstImg.length > 0) {
    imgList.unshift({ ...productFirstImg[0], type: 0 });
  }
  // 商品视频
  if (productVideoPath !== null) {
    imgList.push(...productVideoPath);
  }

  // 是否是门店商品
  const isStoreProduct = sellingScenario.includes(ProductSalesSceneEnum.Storefront) ? 1 : 0;
  // 过滤门店
  const sellingScenarioNew = sellingScenario.filter(item => item !== ProductSalesSceneEnum.Storefront) as ProductSalesSceneEnum[];

  return {
    productEntityVO: {
      name,
      type,
      cateId,
      code,
      isVirtual,
      isPres: 0,
      isPublish: isPublish ? 1 : 0,
      frontName,
      desc: quill.root.innerHTML,
      upperLimit,
      sellingScenario: [ProductSalesSceneEnum.Store, ProductSalesSceneEnum.Social].every(scene => sellingScenarioNew.includes(scene)) ? 0 : sellingScenarioNew[0],
      isHidden: model.value.sellingScenario.includes(ProductSalesSceneEnum.IsHideInStore) ? 1 : 0,
      dealerAllocationRatio: splitAccount,
      isAllocation: splitAccountCheck.includes('splitAccountValue') ? 1 : 0,
      isDistribution: distributorsCheck.includes('distributorsValue') ? 1 : 0,
      distributionRatio: distributorAccount,
      /** 1.3.0 */
      supplierId, // 供应商id
      spu,
      isStoreProduct,
      deliveryType,
      verificationType,
      isUseCashCoupon,
      cashCouponRatio
    },
    productAttributeValueVOList,
    productImgVOList: imgList,
    productSpecVOList: productSpecVOList.map(({ id, price, points, costPrice,marketPrice, ...rest }) => ({
      ...rest,
      price: parseFloat((price * 100).toFixed(2)),
      dailyCost: 0,
      cycle: 0,
      lockedStocks: 0,
      soldQty: 0,
      isDeleted: 0,

      // 商品积分设置
      points,
      isEnabled: !points ? 0 : 1,
      pointIsDeleted: 0,
      costPrice:costPrice !== null ? parseFloat((costPrice * 100).toFixed(2)) : null,
      marketPrice:marketPrice !== null ? parseFloat((marketPrice * 100).toFixed(2)) : null,
    })),
    /** 1.3.0 门店 */
    productStoreRelationVOList: isStoreProduct ? storeId.map(item => ({
      storeId: item,
      visibleScope: determineVisibleScope(visibleScope),
      productType,
    })) : undefined,
  }
};

/**
 * @description 判断门店可见范围
 */
function determineVisibleScope(visibleScope: StoreVisibleRangeEnum[]): StoreVisibleRangeEnum {
  if (visibleScope.length === 0) {
    return StoreVisibleRangeEnum.NotVisible;
  }

  const hasBoth = [StoreVisibleRangeEnum.Normal, StoreVisibleRangeEnum.StoreKeeper]
      .every(scope => visibleScope.includes(scope));

  return hasBoth
      ? StoreVisibleRangeEnum.All
      : visibleScope[0];
};
/** 获取编辑普通商品参数 */
const getEditParams = () => {
  const {
    id, // 商品Id
    name,
    type,
    cateId,
    code,
    isPublish,
    frontName,
    productFirstImg,
    productMoreImg,
    productVideoPath,
    productSpecVOList,
    isVirtual,
    upperLimit,
    sellingScenario,
    splitAccountCheck,
    splitAccount,
    distributorAccount,
    supplierId,
    spu,
    deliveryType,
    verificationType,
    isUseCashCoupon,
    cashCouponRatio,
    productStoreRelationVOList: { storeId, visibleScope, productType },
    productAttributeValueVOList,
  } = model.value;

  // 商品图片
  const imgList = productMoreImg.map(item => ({ ...item, productId: model.value.id, isFirst: 0, type: 0 }));
  if (productFirstImg.length > 0) {
    imgList.unshift({ ...productFirstImg[0], productId: model.value.id, isFirst: 1, type: 0 });
  } else if (imgList.length > 0) {
    // 首图为空，设置更多图片第一张为首图
    imgList[0].isFirst = 1;
  }

  // 商品视频
  if (productVideoPath !== null && isArray(productVideoPath)) {
    const videoList = productVideoPath.map(item => ({ ...item, productId: model.value.id, isFirst: 0, type: 1 }));
    imgList.push(...videoList);
  }

  // 是否是门店商品
  const isStoreProduct = sellingScenario.includes(ProductSalesSceneEnum.Storefront) ? 1 : 0;
  // 过滤门店
  const sellingScenarioNew = sellingScenario.filter(item => item !== ProductSalesSceneEnum.Storefront) as ProductSalesSceneEnum[];

  return {
    id, // 商品Id
    productEntityVO: {
      id,
      name,
      type,
      cateId,
      code,
      isVirtual,
      isPres: 0,
      isPublish: isPublish ? 1 : 0,
      frontName,
      desc: quill.root.innerHTML,
      upperLimit,
      sellingScenario: [ProductSalesSceneEnum.Store, ProductSalesSceneEnum.Social].every(scene => sellingScenarioNew.includes(scene)) ? 0 : sellingScenarioNew[0],
      isHidden: model.value.sellingScenario.includes(ProductSalesSceneEnum.IsHideInStore) ? 1 : 0,
      dealerAllocationRatio: splitAccount,
      isAllocation: model.value.splitAccountCheck.includes('splitAccountValue') ? 1 : 0,
      distributionRatio: distributorAccount,
      isDistribution: model.value.distributorsCheck.includes('distributorsValue') ? 1 : 0,
      /** 1.3.0 */
      supplierId, // 供应商id
      spu,
      isStoreProduct,
      deliveryType,
      verificationType,
      isUseCashCoupon,
      cashCouponRatio
    },
    productImgVOList: imgList,
    productAttributeValueVOList,
    productSpecVOList: productSpecVOList.map(item => {
      const { price, id, points, costPrice,marketPrice, ...rest } = item;
      if (item['productId'] || item['pointConfigId']) {
        return ({
          ...rest,
          id,
          price: parseFloat((price * 100).toFixed(2)),
          costPrice: costPrice !== null ? parseFloat((costPrice * 100).toFixed(2)) : null,
          marketPrice: marketPrice !== null ? parseFloat((marketPrice * 100).toFixed(2)) : null,
          // 商品积分配置
          pointConfigId: item['pointConfigId'],
          sourceId: item['sourceId'],
          isEnabled: !points ? 0 : 1,
          points,
        })
      }
      // 新增的规格参数
      return ({
        ...rest,
        price: price * 100,
        dailyCost: 0,
        cycle: 0,
        lockedStocks: 0,
        soldQty: 0,
        isDeleted: 0,

        // 新加商品积分配置
        isEnabled: !points ? 0 : 1,
        points,
        costPrice: costPrice * 100,
        marketPrice: marketPrice * 100,
      })
    }),
    /** 1.3.0 门店 */
    productStoreRelationVOList: isStoreProduct ? storeId.map(item => ({
      productId: id,
      storeId: item,
      visibleScope: determineVisibleScope(visibleScope),
      productType: SalesSceneStoreGoodsTypeEnum.Normal,
    })) : undefined,
  }
};

/* 确认--保存 */
const isLoading = ref(false);
const _save = async (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      try {
        isLoading.value = true;
        /** 是否正在进行文件上传 */
        if (uploadProductImgRef.value?.isUploadLoading) {
          createMessageError('文件正在上传中，请稍等！');
          return;
        }

        const { isVirtual, productSpecVOList, sellingScenario, productStoreRelationVOList: { storeId }, cashCouponRatio, isUseCashCoupon } = model.value;
        // 是否是门店商品
        const isStoreProduct = sellingScenario.includes(ProductSalesSceneEnum.Storefront);
        // 执行保存前提条件
        if (isVirtual === GoodsIsVirtualType.ISVIRTUAL) {
          // 虚拟商品时
          let flag = false;
          productSpecVOList.forEach(spec => {
            if (spec.isDownPayment === 1 || spec.isCashOnDelivery === 1) {
              flag = true;
            }
          });
          if (flag) {
            createMessageError('虚拟商品不支持设置付款方式！');
            return;
          };
        }

        if (isStoreProduct && (storeId === null || !isArray(storeId) || !storeId.length)) {
          createMessageError('请选择门店！');
          return;
        }
        if(isUseCashCoupon === 1 ) {
          const num = Number(cashCouponRatio);
          if(!(!isNaN(num) && Number.isInteger(num) && num >= 1 && num <= 99)) {
            createMessageError('现金抵扣券金额只能录入1-99的整数，默认为10');
          }
        }
        /** 新增 */
        if (pattern.value === 'add') {
          // console.log("普通商品新增参数", getAddParams());
          await addGeneralGoods(getAddParams());
        }
        /** 编辑 */
        if (pattern.value === 'edit') {
          // console.log("普通商品编辑参数", getEditParams());
          await updateGeneralGoods(getEditParams());
        }
        createMessageSuccess(pattern.value === 'add' ? '新增商品成功' : '编辑商品成功');
        // 关闭并刷新
        show.value = false;
        props?.refreshTable();
      } catch (error) {
        createMessageError(pattern.value === 'add' ? `新增商品失败: ${error}` : `编辑商品失败: ${error}`);
      } finally {
        isLoading.value = false;
      }
    }
  });
};

/** 分账事件 */
const handleSplitAccount = () =>{
  // 确保值为整数
  if (model.value.splitAccount && !Number.isInteger(model.value.splitAccount)) {
    model.value.splitAccount = Math.trunc(model.value.splitAccount); // 截取小数部分
  }
  if(model.value.distributorAccount && !Number.isInteger(model.value.distributorAccount)){
    model.value.distributorAccount = Math.trunc(model.value.distributorAccount); // 截取小数部分
  }
}

/** 是否启用分账比例校验 */
const splitAccountComputed = ref()

/** 是否分销员分佣比例校验 */
const distributorsComputed = ref()

const prices = ref(0); // 全部售价
const costPrices = ref(0); // 全部成本价
const total = ref<string | number>(0); // 订单总额比率
const handleProductSpecVOList = (value) =>{
  handleTotal();
};

/** 设置经销商分账和分销员佣金总额 */
const handleTotal = () =>{
  prices.value = model.value.productSpecVOList.reduce((total, item) => total + (item.price || 0), 0);
  costPrices.value = model.value.productSpecVOList.reduce((total, item) => total + (item.costPrice || 0), 0);
  const splitAccountValue =  model.value.splitAccount ? (model.value.splitAccount / 100) : 0;
  const distributorAccountValue =  model.value.distributorAccount ? (model.value.distributorAccount / 100) : 0;
  const distributorCommission = isNaN((prices.value - costPrices.value) * splitAccountValue / prices.value) ?  0 : ((prices.value - costPrices.value) * splitAccountValue / prices.value);
  const proportionTotal = (distributorCommission + distributorAccountValue) * 100;
  total.value = !isFinite(proportionTotal) ? 0 : (proportionTotal >= 0 ? proportionTotal.toFixed() : 0);
};

/** 处理销售场景变化 */
const handleSellingScenarioChange = (newVal: number[]) => {
  // 检查是否包含商城选项
  const hasStore = newVal.includes(ProductSalesSceneEnum.Store);
  if (!hasStore) {
    // 如果不包含商城,则移除"在商城中隐藏"选项
    model.value.sellingScenario = newVal.filter(
      item => item !== ProductSalesSceneEnum.IsHideInStore
    );
  } else {
    // 如果包含商城,直接更新值
    model.value.sellingScenario = newVal;
  }
};

/** 关闭 */
const close = () => {
  show.value = false;
}

/** 在作用域内运行监听器 */
scope.run(() => {
  /** 监听分账比例 */
  watch(() => model.value.splitAccount,(newVal) =>{
    handleTotal();
  });

  /** 佣金比例比例 */
  watch(() => model.value.distributorAccount,(newVal) =>{
    handleTotal();
  });

  /** 监听 */
  watch(show, (newVal) => {
    if (newVal) {
      nextTick(() => {
        createVideoElement()
        quill = new Quill(descRichTextDomRef.value, {
          theme: 'snow', // 使用 snow 主题
          modules: {
            toolbar: {
              container: [
                // Include image button in the toolbar
                [{ 'size': ['small', false, 'large', 'huge'] }],  // 自定义字体大小
                ['bold', 'italic', 'underline', 'strike'],        // 加粗、斜体、下划线和删除线
                [{ 'color': [] }, { 'background': [] }],          // 字体颜色和背景颜色
                [{ 'header': '1' }, { 'header': '2' }, 'blockquote',],  // 标题、引用和代码块
                [{ 'list': 'ordered' }, { 'list': 'bullet' }, { 'indent': '-1' }, { 'indent': '+1' }], // 列表和缩进
                [{ 'direction': 'rtl' }, { 'align': [] }],        // 文本方向和对齐方式
                ['image', 'video'],            // 链接、图片、视频和公式
              ],
              // Handle image uploads
              handlers: {
                image: function () {
                  const input = document.createElement('input');
                  input.setAttribute('type', 'file');
                  input.setAttribute('accept', 'image/*');
                  input.setAttribute('multiple', 'true');
                  input.click();

                  input.onchange = () => {
                    const file = input.files[0];
                    if (file) {
                      const _formData = new FormData()
                      for (let key in input.files) {
                        if (input.files.hasOwnProperty(key)) {
                          console.log(key, input.files[key]);
                          _formData.append('files', input.files[key])
                        }
                      }
                      uploadRichTextResouce(_formData).then(res => {
                        res.forEach(e=>{
                          const range = quill.getSelection();
                          quill.insertEmbed(range.index, 'image', `${getOssUrlPrefix()}/${e}`);
                        })
                      }).catch((error) => {
                        console.error('Image upload failed:', error);
                      });
                    }
                  };
                },
                video: function () {
                  const input = document.createElement('input');
                  input.setAttribute('type', 'file');
                  input.setAttribute('accept', 'video/mp4');
                  input.click();

                  input.onchange = () => {
                    const file = input.files[0];
                    if (file) {
                      console.log(file);
                      const _formData = new FormData()
                      _formData.append('files', file)
                      uploadRichTextResouce(_formData).then(res => {
                        const videoUrl = res[0]
                        const range = quill.getSelection();
                        quill.insertEmbed(range.index, 'video', `${getOssUrlPrefix()}/${videoUrl}`);
                      }).catch((error) => {
                        console.error('Video upload failed:', error);
                      });
                    }
                  };
                }
              }
            }
          }
        });
      })
    }
  })


  watch(() => model.value.splitAccountCheck, (newVal) => {
    if(newVal && newVal.length == 0){
      model.value.splitAccount = null;
      splitAccountComputed.value = '';
    }else{
      splitAccountComputed.value = 'splitAccount'
    }
  });

  watch(() => model.value.distributorsCheck, (newVal) => {
    if(newVal && newVal.length == 0){
      model.value.distributorAccount = null;
      distributorsComputed.value = '';
    }else{
      distributorsComputed.value = 'distributorAccount';
    }
  });
});

/** 作用域销毁时清理 */
onScopeDispose(() => {
  scope.stop();
});

defineExpose({
  acceptParams,
  close
});
</script>

<style lang="less" scoped>
@import "@/styles/defaultVar.less";

:deep(.n-scrollbar-rail) {
  bottom: 8px !important;
}

.footer-wrapper {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.distributors :deep(.n-form-item-label){
  width: 164px !important;
}
.store-select {
  position: absolute;
  left: 80px;
  bottom: -68px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  span {
    font-size: 14px;
  }
}
</style>
