
import { createCacheStorage } from "@/utils/cache/storageCache";
import { CacheConfig } from "@/utils/cache/config";
import { type GlobalConfig } from "@/enums";

/** 
 * @description 获取全局系统配置 
 */
export function getSystemConfigInfo() {
    // 初始化值
    const emptyConfig: GlobalConfig = {};
    // 判断是否存在缓存
    const userSystemStorage = createCacheStorage(CacheConfig.GlobalConfig);
    const _systemCache = userSystemStorage.get();
    if (_systemCache) {
        const systemConfigInfo: GlobalConfig = _systemCache['globalConfig'];
        return systemConfigInfo;
    }

    return emptyConfig;
}