import { isDevEnv, isProdEnv } from "@/utils/envUtils";
import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
const systemStore = useSystemStoreWithoutSetup();
/**
 * 解析 url 中的参数
 * @param url
 * @returns {Object}
 */
export function parseUrlParams(url) {
  const params = {};
  if (!url || url === "" || typeof url !== "string") {
    return params;
  }
  const paramsStr = url.split("?")[1];
  if (!paramsStr) {
    return params;
  }
  const paramsArr = paramsStr.replace(/&|=/g, " ").split(" ");
  for (let i = 0; i < paramsArr.length / 2; i++) {
    const value = paramsArr[i * 2 + 1];
    params[paramsArr[i * 2]] = value === "true" ? true : value === "false" ? false : value;
  }
  return params;
}
/**获取oss前缀 */
export function getOssUrlPrefix() {
  const isProd = isProdEnv()
  if (!isProd) {
    return import.meta.env.VITE_DEV_OSS_PREFIX
  }else if (systemStore._globalConfig.ossDomain){
    return systemStore._globalConfig.ossDomain + '/store-spec'
  }
  else {
    const hostnameSplit = location.hostname.split(".")
    hostnameSplit[0] = `${hostnameSplit[0].replace('store','sg')}-oss`
    return `${location.protocol}//${hostnameSplit.join(".")}/store-spec`
  }
}

/**获取文件路径前缀 **/
export function getOssFileUrlPrefix() {
  const isProd = isProdEnv()
  if (!isProd) {
    return import.meta.env.VITE_DEV_OSS_PREFIX.slice(0, -5)
  }else if (systemStore._globalConfig.ossDomain){
    return systemStore._globalConfig.ossDomain + '/store'
  }
  else {
    const hostnameSplit = location.hostname.split(".")
    hostnameSplit[0] = `${hostnameSplit[0].replace('store','sg')}-oss`
    return `${location.protocol}//${hostnameSplit.join(".")}/store`
  }
}

/**获取store api前缀 */
export function getApiUrlPrefix() {
  const isDev = isDevEnv()
  if (isDev) {
    return import.meta.env.VITE_DEV_STORE_PREFIX
  }
  else {
    const hostnameSplit = location.hostname.split(".")
    // 去除域名中的-doctor
    hostnameSplit[0] = hostnameSplit[0].replace('-doctor', '')
    hostnameSplit[0] = `${hostnameSplit[0]}-api`
    return `${location.protocol}//${hostnameSplit.join(".")}`
  }
}
/**获取img前缀 */
export function getImgUrlPrefix() {
  const isDev = isDevEnv()
  if (isDev) {
    return import.meta.env.VITE_DEV_STORE_BASE_URL
  }
  else {
    const hostnameSplit = location.hostname.split(".");
    const firstPart = hostnameSplit[0].endsWith("-m") ? hostnameSplit[0].slice(0, -2) : hostnameSplit[0];
    hostnameSplit[0] = `${firstPart}-api`;
    const prefix = `${location.protocol}//${hostnameSplit.join(".")}`
    return prefix
  }
}

/** basic平台api */
export function basicPlatformUrl(url: string) {
  const isDev = isDevEnv();
  let PROFIX: string
  if (isDev) {
    PROFIX = `${import.meta.env.VITE_DEV_BASIC_PREFIX}`
  }
  else {
    const hostnameSplit = location.hostname.split(".")
    const firstHostnameSplit = hostnameSplit[0].split('-')
    firstHostnameSplit[0] = firstHostnameSplit[0].replace(firstHostnameSplit[0], 'basic')
    hostnameSplit[0] = `${firstHostnameSplit.join('-')}-api`
    PROFIX = `${location.protocol}//${hostnameSplit.join(".")}`
  }
  return `${PROFIX}${url}`;
}
