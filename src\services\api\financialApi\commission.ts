import { defHttp } from '@/services';

/** 分佣相关 */
export const enum CommissionApi {
    /** 分页查询查询门店分佣 */
    storeCommissionPage = '/storeCommissionDetail/page',
    /** 门店分佣明细导出 */
    storeCommissionExport = '/storeCommissionDetail/export',
    /** 门店分佣明细详情 */
    storeCommissionDetail = '/storeCommissionDetail/getStoreCommDetail',
    /** 查询分佣规则 */
    commissionRule = '/globalConfigs/getStoreCommSystemConfig',
    /** 修改分佣规则 */
    saveCommissionRule = '/globalConfigs/updateStoreCommSystemConfig',
    /** 分页查询打款审核列表 */
    payoutAuditPage = '/paymentAudit/page',
    /** 打款审核单详情 */
    paymentAuditGet = '/paymentAudit/get/detail',
    /** 审核/批量审核通过 */
    paymentAuditPass = '/paymentAudit/approved',
    /** 打款/批量打款 */
    paymentAuditPayout = '/paymentAudit/makeAPayment',
    /** 审核驳回 */
    paymentAuditReject = '/paymentAudit/overrule',

}

/** 分页查询查询门店分佣 */
export function storeCommissionPage(params) {
    return defHttp.post({
        url: CommissionApi.storeCommissionPage,
        params,
    });
}

/** 门店分佣明细导出 */
export function storeCommissionExport(params) {
    return defHttp.post({
        url: CommissionApi.storeCommissionExport,
        requestConfig: {
            responeseType: 'stream'
        },
        params,
    });
}

/** 门店分佣明细详情 */
export function storeCommissionDetail(params): Promise<StoreCommissionDetailDTO> {
    return defHttp.get({
        url: CommissionApi.storeCommissionDetail,
        params,
    });
}

/** 查询分佣规则 */
export function commissionRule() {
    return defHttp.post({
        url: CommissionApi.commissionRule,
        params: {
            data:{
                pageVO: {current: 1,size: 30}
            }
        },
    });
}

/** 修改分佣规则 */
export function saveCommissionRule(params) {
    return defHttp.post({
        url: CommissionApi.saveCommissionRule,
        params,
    });
}

/** 分页查询打款审核列表 */
export function payoutAuditPage(params) {
    return defHttp.post({
        url: CommissionApi.payoutAuditPage,
        params,
    });
}

/** 打款审核单详情 */
export function paymentAuditGet(params): Promise<PaymentAuditDTO> {
    return defHttp.get({
        url: CommissionApi.paymentAuditGet,
        params,
    });
}

/** 审核/批量审核通过 */
export function paymentAuditPass(params) {
    return defHttp.post({
        url: CommissionApi.paymentAuditPass,
        params,
    });
}

/** 打款/批量打款 */
export function paymentAuditPayout(params) {
    return defHttp.post({
        url: CommissionApi.paymentAuditPayout,
        params,
    });
}

/** 审核驳回 */
export function paymentAuditReject(params) {
    return defHttp.put({
        url: CommissionApi.paymentAuditReject,
        params,
    });
}

/** 门店分佣明细详情 */
export interface StoreCommissionDetailDTO {
    /** 门店分佣商品列表 */
    allocationItemVOList?: any[] | null;
    /** 店员佣金（单位：分） */
    clerkCommission?: number | null;
    /** 店员昵称 */
    clerkNickname?: null | string;
    /** 店员分佣比例（单位 %） */
    clerkRate?: number | null;
    /** 店员短Id */
    clerkShortId?: number | null;
    /** 店员用户ID */
    clerkUserId?: number | null;
    /** 分佣类型（1=门店店员分佣；2=经销商分佣） */
    commissionType?: number | null;
    createTime?: null | string;
    /** 用户ID */
    customerId?: number | null;
    /** 经销商佣金（单位：分） */
    dealerCommission?: number | null;
    /** 经销商昵称 */
    dealerNickname?: null | string;
    /** 经销商分佣比例（单位 %） */
    dealerRate?: number | null;
    /** 经销商用户ID */
    dealerUserId?: number | null;
    id?: number | null;
    /** 是否售后（0=否；1=是） */
    isAfterSale?: number | null;
    /** 店长佣金（单位：分） */
    managerCommission?: number | null;
    /** 店长昵称 */
    managerNickname?: null | string;
    /** 店长分佣比例（单位 %） */
    managerRate?: number | null;
    /** 店长短Id */
    managerShortId?: number | null;
    /** 店长用户ID */
    managerUserId?: number | null;
    /** 订单编号 */
    orderNo?: null | string;
    /** 组织架构ID */
    orgId?: number | null;
    /** 打款方式（1=用户提现；2=线下结算） */
    paymentMethod?: number | null;
    /** 结算状态（1=待结算；2=已结算；3=已取消;4=失败;5=结算中） */
    settlementStatus?: number | null;
    /** 结算时间点 */
    settlementTime?: null | string;
    /** 门店名称 */
    storeName?: null | string;
    /** 门店短Id */
    storeShortId?: number | null;
    updateTime?: null | string;
}

export interface PaymentAuditDTO {
    /** 账号类型(对私账户：201   对公账户：204) */
    accountType?: number;
    /** 打款金额（单位：分） */
    amount?: number;
    /** 申请单号 */
    applicationNo?: string;
    /** 申请来源（1=用户提现） */
    applicationSource?: number;
    /** 审核人ID */
    auditorId?: number;
    /** 审核人昵称 */
    auditorNickname?: string;
    /** 审核结果 */
    auditResult?: string;
    /** 审核时间 */
    auditTime?: string;
    /** 收款人银行卡卡号 */
    bankCardNo?: string;
    createTime?: string;
    /** 应答信息域（JSON 格式） */
    data?: string;
    /** 错误码 */
    errorCode?: string;
    /** 失败原因 */
    errorDesc?: string;
    /** 手续费（单位：分） */
    fee?: number;
    id?: number;
    /** 发票图片多个以;分割 */
    invoiceImageList?: string;
    /** 商户号 */
    merchantNumber?: string;
    /** 商户订单号（商户传入） */
    merchantOrderNo?: string;
    /** 打款人ID */
    payerId?: number;
    /** 打款人昵称 */
    payerNickname?: string;
    /** 打款时间 */
    paymentTime?: string;
    /** 支付平台名称(汇聚支付) */
    payPlatformName?: string;
    /** 平台流水号（全局唯一） */
    platformSerialNo?: string;
    /** 收款人银行卡持卡人名称 */
    receiverName?: string;
    /** 驳回人ID */
    rejectId?: number;
    /** 驳回人昵称 */
    rejectNickname?: string;
    /** 驳回原因 */
    rejectReason?: string;
    /** 驳回时间 */
    rejectTime?: string;
    /** 打款状态（1=待审核；2=待打款；3=打款中；4=打款失败；5=已打款；6=已驳回） */
    status?: number;
    /** 第三方状态码（代付交易状态） */
    thirdPartyStatus?: string;
    /** 联行号 */
    unionBankNo?: string;
    updateTime?: string;
    /** 用户ID */
    userId?: number;
    /** 用户昵称 */
    userNickname?: string;
    /** 用户短Id */
    userShortId?: number;
    [property: string]: any;
}
