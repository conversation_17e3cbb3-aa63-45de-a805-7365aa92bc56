import {defHttp} from '@/services';
// 敏感词配置
export const enum sensitiveWordApi {
  batchAdd = '/sensitiveWord/batchAdd', // 批量添加敏感词
  batchDelete = '/sensitiveWord/batchDelete', // 批量删除
  page = '/sensitiveWord/page' // 分页查询列表
}

export function sensitiveWordBatchAdd(params) {
  return defHttp.post({
    url: sensitiveWordApi.batchAdd,
    params
  })
}

export function sensitiveWordBatchDelete(params) {
  return defHttp.post({
    url: sensitiveWordApi.batchDelete,
    params
  })
}

export function sensitiveWordPage(params) {
  return defHttp.post({
    url: sensitiveWordApi.page,
    params
  })
}
