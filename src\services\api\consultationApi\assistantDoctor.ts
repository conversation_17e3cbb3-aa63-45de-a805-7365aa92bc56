import { defHttp } from "@/services";

/** 医助管理 */
export const enum AssistantDoctor<PERSON><PERSON> {
  /** 医助分页 */
  assistantDoctorPage = "/physicianAssistant/page",
  /** 医助列表 */
  getAssistantDoctorList = "/physicianAssistant/list",
  /** 新增医助 */
  addAssistantDoctor = "/physicianAssistant/add",
  /** 编辑医助 */
  editAssistantDoctor = "/physicianAssistant/update",
  /** 删除医助 */
  deleteAssistantDoctor = "/physicianAssistant/delete",
  // 选择用户列表
  customerEntityList = "/customerEntity/page/common",
}

/** 医助分页 */
export function assistantDoctorPage(params) {
  return defHttp.post({
    url: AssistantDoctorApi.assistantDoctorPage,
    params,
  });
}
/** 医助列表 */
export function getAssistantDoctorList(params) {
  return defHttp.post({
    url: AssistantDoctorApi.getAssistantDoctorList,
    params,
  });
}

/** 新增医助 */
export function addAssistantDoctor(params) {
  return defHttp.post({
    url: AssistantDoctorApi.addAssistantDoctor,
    params,
  });
}

/** 编辑医助 */
export function editAssistantDoctor(params) {
  return defHttp.put({
    url: AssistantDoctorApi.editAssistantDoctor,
    params,
  });
}

/** 删除医助 */
export function deleteAssistantDoctor(params) {
  return defHttp.delete({
    url: AssistantDoctorApi.deleteAssistantDoctor,
    requestConfig: {
      isQueryParams: true,
    },
    params,
  });
}

/** 选择用户列表 */
export function customerEntityList(params) {
  return defHttp.post({
    url: AssistantDoctorApi.customerEntityList,
    params,
  });
}
