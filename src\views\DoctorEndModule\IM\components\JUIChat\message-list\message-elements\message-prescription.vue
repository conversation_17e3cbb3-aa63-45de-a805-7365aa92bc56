<template>
  <div class="system-message-wrapper">
    <div class="system-message-card">
      <n-flex style="margin-bottom: 8px">
        <n-text strong class="card-title">处方单</n-text>
        <n-button size="small" text type="info" @click="handleOpenPrescriptionDetail()">详细</n-button>
      </n-flex>
      <div class="patient-info">
        <n-text depth="3">患者</n-text>
        <n-text>{{ patientName }}</n-text>
      </div>
      <div class="diagnosis-section">
        <n-text strong type="info">诊断</n-text>
        <n-text>{{ clinicalDiagnosis }}</n-text>
      </div>

      <n-divider v-if="presRpList.length > 0"/>

      <div class="prescription-section">
        <n-text strong type="error" style="width: 48px">Rp</n-text>
        <div v-if="type !== 1" style="">
          <div class="medicine-item" v-for="(item,index) in presRpList" :key="item.productId">
            <n-grid :cols="24" :x-gap="8" :y-gap="8">
              <n-gi :span="24">
                <n-flex justify="flex-start">
                  <n-text>{{ `${item.productName || ''}` + `${item.specName || ''}` }}
                    {{ item?.count ? '*' + item.count : '' }}
                  </n-text>
                </n-flex>
              </n-gi>
              <n-gi :span="24">
                <n-flex justify="flex-start">
                  <n-text class="dosage" style="font-size: 14px" depth="3">
                    {{ routeOfAdministrationMap.get(item.route) ?? item.custRoute }}
                    {{
                      `${frequencyOfAdministrationMap.get(item.freq) ?? item.custFreq}`
                    }}
                  </n-text>
                  <n-text class="dosage" style="font-size: 14px" depth="3">
                    每次{{ item.dosage }}{{ dosageUnitsMap.get(item.units) }}
                  </n-text>
                </n-flex>
              </n-gi>
            </n-grid>
          </div>
        </div>
        <div v-else style="">
          <div class="medicine-item">
            <n-grid :cols="24" :x-gap="8" :y-gap="8">
              <n-gi v-for="(item,index) in presRpList" :key="item.productId" :span="24">
                <n-flex justify="flex-start">
                  <n-text>
                    {{ item.productName || '' }}
                    &nbsp;
                    {{ `${item.count || ''}` + `${item.specName || ''}` }}
                  </n-text>
                </n-flex>
              </n-gi>
              <n-gi v-if="rpSize >= 3" :span="24">
                <n-flex justify="flex-start">
                  <n-text class="dosage" style="font-size: 14px" depth="3">
                    ...
                  </n-text>
                </n-flex>
              </n-gi>
              <n-gi :span="24">
                <n-flex justify="flex-start">
                  <n-text class="dosage" style="font-size: 14px" depth="3">
                    共{{ presRpList[0]?.chineseDosage }}剂
                  </n-text>
                  <n-text class="dosage" style="font-size: 14px" depth="3">
                    {{ routeOfAdministrationMap.get(presRpList[0]?.route) ?? presRpList[0]?.custRoute }}
                  </n-text>
                  <n-text class="dosage" style="font-size: 14px" depth="3">
                    {{
                      `${frequencyOfAdministrationMap.get(presRpList[0]?.freq) ?? presRpList[0]?.custFreq}`
                    }}
                  </n-text>
                  <n-text class="dosage" style="font-size: 14px" depth="3">
                    每次{{ presRpList[0]?.dosage }}{{ dosageUnitsMap.get(presRpList[0]?.units) }}
                  </n-text>
                </n-flex>
              </n-gi>
            </n-grid>
          </div>
        </div>
      </div>
      <!--      -->
      <div
          style="display: flex;justify-content: center"
          v-if="props.messageItem?.contentType =='PRESCRIPTION_AUDIT_NOT_PASSED'">
        <NButton
            type="primary"
            size="small"
            @click="handleOpenPrescriptionModal('edit')"
            :disabled="hasChangeRef">
          修改
        </NButton>
      </div>
    </div>
  </div>
  <PrescriptionModal
      :messageItem="props.messageItem"
      :mode="prescriptionModalMode"
      v-model:show="prescriptionModalShow"
      :params="{id:prescriptionData.id}"
      @change-message="changeMessage"
  />
  <DetailsDrawer ref="detailDrawerRef"/>
</template>

<script setup lang="ts">
import {computed, ref, watch} from 'vue'
import PrescriptionModal, {
  type prescriptionEditorMode
} from '@/views/DoctorEndModule/Prescription/compoments/prescriptionModal.vue'
import {
  dosageUnitsMap,
  frequencyOfAdministrationMap,
  routeOfAdministrationMap
} from '@/views/DoctorEndModule/Prescription/types'
import type {JChatMessageModel} from "@/views/DoctorEndModule/IM/types";
import {useRouter} from "vue-router";
import DetailsDrawer from "@/views/PharmacistPrescription/compoments/detailsDrawer.vue";

interface IProps {
  content: object;
  messageItem: JChatMessageModel;
}

const props = withDefaults(defineProps<IProps>(), {
  content: () => ({}),
  messageItem: () => ({} as JChatMessageModel),
});

const router = useRouter();
const prescriptionData = ref()
const prescriptionModalShow = ref(false)
const prescriptionModalMode = ref<prescriptionEditorMode>('detail')
const hasChangeRef = ref(false)
const detailDrawerRef = ref(null)

function handleOpenPrescriptionModal(type: string) {
  prescriptionModalShow.value = !prescriptionModalShow.value
  prescriptionModalMode.value = type
}

// 导航到处方单详情界面
function handleOpenPrescriptionDetail() {
  const _params = {
    row: {id: prescriptionData.value.id},
    refresh: () => {
    },
    mode: 'detail',
  };
  detailDrawerRef.value?.acceptParams(_params);
}

function changePrescriptionData() {
  prescriptionData.value = props.content
  if (prescriptionData.value.updateFlag == 1) {
    hasChangeRef.value = true
  }
}

changePrescriptionData()

const patientName = computed(() => {
  const age = prescriptionData.value?.patientAge ? `${prescriptionData.value?.patientAge}岁` : ''
  const sex = prescriptionData.value?.patientSex ? `${prescriptionData.value?.patientSex}` : ''
  const bracket = (age || sex) ? `(${age} ${sex})` : ''
  return `${prescriptionData.value?.patientName || '-'}${bracket}`
})

const clinicalDiagnosis = computed(() => {
  return `${prescriptionData.value.clinicalDiagnosis || '-'}`
})

const presRpList = computed(() => {
  return prescriptionData.value.presRpList ?? []
})

const type = computed(() => {
  return prescriptionData.value?.type ?? 2
})

const rpSize = computed(() => {
  return prescriptionData.value?.rpSize ?? 0
})

function changeMessage(status) {
  hasChangeRef.value = status
}


</script>

<style scoped lang="less">

.system-message-wrapper {
  display: flex;
  justify-content: center;
}

.system-message-card {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  max-width: 420px;
  margin: 12px 0;
  width: 600px;
}

.card-title {
  font-size: 1.25rem;
  display: block;
  color: #1d2129;
}

.patient-info,
.diagnosis-section {
  display: flex;
  gap: 8px;
  margin: 12px 0;
  font-size: 14px;
}

.patient-info > :first-child,
.diagnosis-section > :first-child {
  flex: 0 0 48px;
  color: #8a8f8d;
}

.n-divider {
  margin: 16px 0;
}

.prescription-section {
  display: flex;
  gap: 8px;
  margin: 12px 0;
}

.medicine-item {
  padding-bottom: 12px;
  font-size: 15px;
}

.medicine-item:last-child {
  padding-bottom: 0;
  font-size: 15px;
}

.medicine-item:last-child {
  border-bottom: none;
}


</style>
