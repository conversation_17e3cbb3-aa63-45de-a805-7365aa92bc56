<template>
  <JModal v-model:show="show" width="580" title="进入时间 - 离开时间" @after-leave="closeModal">
    <div class="view-data-details-box">
      <div class="view-data-details-item" v-for="(item, index) in 20" :key="index">
        2025-05-15 17:13:45 - 2025-05-15 17:13:45
      </div>
    </div>
    <template #footer>
      <n-flex justify="end">
        <n-button
          size="small"
          :style="{
            minWidth: '72px',
            minHeight: '32px',
            '--n-padding': '4px 11px',
            '--n-border-radius': '5px',
          }"
          @click="closeModal"
        >
          关闭
        </n-button>
      </n-flex>
    </template>
  </JModal>
</template>

<script setup lang="ts" name="AddorEditVideo">
import { ref, watch } from "vue";
import { useMessages } from "@/hooks";
import moment from "moment";
const _params = {
  data: {
    id: "",
  },
  pageVO: {
    current: 1,
    size: 10,
  },
};
const timeList = ref([]);
/* 提示信息 */
const { createMessageSuccess, createMessageError } = useMessages();
/* 模态框显隐状态 */
const show = ref(false);
const acceptParams = params => {
  show.value = true;
  _params.data.id = params.id;
};
/* 关闭弹窗 */
const closeModal = () => {
  timeList.value = [];
  // 弹窗取消
  show.value = false;
};

defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less">
@import "@/styles/scrollbar.less";
.view-data-details-box {
  height: 400px;
  font-size: 16px;
  overflow-y: auto;
  .scrollbar();
}
</style>
