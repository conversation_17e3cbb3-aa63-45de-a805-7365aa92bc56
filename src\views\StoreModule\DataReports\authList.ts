import { DataReportsAuth } from "@/enums/authKeys";
import { hasAuth } from "@/utils/auth/authUtils";


/** 订单统计 */
export const hasReportsOrderAuth = function(){
    return hasAuth(DataReportsAuth.dataReportsOrder.key);
}()
/** 订单统计导出 */
export const hasReportsOrderExportAuth = function(){
    return hasAuth(DataReportsAuth.dataReportsOrderExport.key);
}()
/** 客户统计 */
export const hasReportsCustomerAuth = function(){
    return hasAuth(DataReportsAuth.dataReportsCustomer.key);
}()
/** 客户统计导出 */
export const hasReportsCustomerExportAuth = function(){
    return hasAuth(DataReportsAuth.dataReportsCustomerExport.key);
}()
/** 群管统计 */
export const hasGroupPageAuth = function(){
    return hasAuth(DataReportsAuth.dataReportsGroup.key);
}()

/** 群管统计之导出 */
export const hasGroupExportAuth = function(){
    return hasAuth(DataReportsAuth.dataReportsGroupExport.key);
}()

/** 经销商统计 */
export const hasDealerPageAuth = function(){
    return hasAuth(DataReportsAuth.dataReportsDealer.key);
}()

/** 经销商统计之导出 */
export const hasDealerExportAuth = function(){
    return hasAuth(DataReportsAuth.dataReportsDealerExport.key);
}()

/** 商品统计 */
export const hasProductPageAuth = function(){
    return hasAuth(DataReportsAuth.dataReportsProduct.key);
}()

/** 商品统计之导出 */
export const hasProductExportAuth = function(){
    return hasAuth(DataReportsAuth.dataReportsProductExport.key);
}()

/** 课程统计 */
export const hasCoursePageAuth = function(){
    return hasAuth(DataReportsAuth.dataReportsCourse.key);
}()

/** 课程统计之导出 */
export const hasCourseExportAuth = function(){
    return hasAuth(DataReportsAuth.dataReportsCourseExport.key);
}()

/** 门店数据统计 */
export const hasStoreDataReportAuth = function(){
    return hasAuth(DataReportsAuth.dataReportsStoreDataReport.key);
}()

/** 门店数据统计之导出 */
export const hasStoreDataReportExportAuth = function(){
    return hasAuth(DataReportsAuth.dataReportsStoreDataReportExport.key);
}()

/** 经销商数据统计 */
export const hasDistributorDataReportAuth = function(){
    return hasAuth(DataReportsAuth.dataReportsDistributorDataReport.key);
}()

/** 经销商数据统计之导出 */
export const hasDistributorDataReportExportAuth = function(){
    return hasAuth(DataReportsAuth.dataReportsDistributorDataReportExport.key);
}()