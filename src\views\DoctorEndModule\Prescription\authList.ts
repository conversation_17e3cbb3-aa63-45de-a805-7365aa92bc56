import { PrescriptionManagementAuth } from "@/enums/authKeys";
import { hasAuth } from "@/utils/auth/authUtils";


/** 开处方 */
export const hasAddPrescription= function(){
    return hasAuth(PrescriptionManagementAuth.prescriptionManagementIndexAdd.key);
}()
/** 恢复/取消 */
export const hasPrescriptionRecoverOrCancel = function(){
    return hasAuth(PrescriptionManagementAuth.prescriptionManagementIndexRecoverorcancel.key);
}()
/** 编辑 */
export const hasPrescriptionEdit = function(){
    return hasAuth(PrescriptionManagementAuth.prescriptionManagementIndexEdit.key);
}()
/** 详情 */
export const hasPrescriptionDetails = function(){
    return hasAuth(PrescriptionManagementAuth.prescriptionManagementIndexDetails.key);
}()
/** 删除 */
export const hasPrescriptionDelete = function(){
    return hasAuth(PrescriptionManagementAuth.prescriptionManagementIndexDelete.key);
}()
