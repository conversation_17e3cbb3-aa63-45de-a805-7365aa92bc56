<template>
  <div class="inner-page-height">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :pagination="paginationRef"
      @paginationChange="paginationChange"
      :table-summary="summaryRefs"
      id="subBillManagement"
      @selectedKeysChange="selectedKeysChange"

    >
      <!-- 表单 -->
      <template #searchForm>
        <n-form
          ref="formRef"
          label-placement="left"
          label-width="auto"
          :show-feedback="false"
          require-mark-placement="right-hanging"
          size="small"
          :style="{ width: '100%' }"
        >
          <n-grid cols="4 m:12 l:18 xl:24" :x-gap="32" responsive="screen">
            <!-- 经销商名称 -->
            <n-gi :span="6">
              <n-form-item label="经销商名称">
                <n-input
                  v-model:value="formValue.dealerName"
                  type="text"
                  placeholder="请输入经销商名称"
                  @keyup.enter.native="formSearch"
                  style="width:170px"
                  clearable
                  @blur="formValue.dealerName=($event.target as HTMLInputElement)?.value.trim()"
                  maxlength="30"
                />
              </n-form-item>
            </n-gi>
            <!-- 订单编号 -->
            <n-gi :span="6">
              <n-form-item label="订单编号">
                <n-input
                  v-model:value="formValue.orderCode"
                  type="text"
                  placeholder="请输入订单编号"
                  @keyup.enter.native="formSearch"
                  style="width:170px"
                  clearable
                  @blur="formValue.orderCode=($event.target as HTMLInputElement)?.value.trim()"
                  maxlength="19"
                />
              </n-form-item>
            </n-gi>
            <!-- 结算状态 -->
            <n-gi :span="6">
              <n-form-item label="结算状态">
                <n-select
                  v-model:value="formValue.status"
                  :options="settlementStatusSecondOptions"
                  placeholder="请选择结算状态"
                  style="width: 170px;"
                  clearable
                  :render-option="renderOption"
                />
              </n-form-item>
            </n-gi>
            <!-- 打款状态 -->
            <n-gi :span="6">
              <n-form-item label="打款状态">
                <n-select
                  v-model:value="formValue.settlementStatus"
                  :options="payoutsStatusSecondOptions"
                  placeholder="请选择打款状态"
                  style="width: 170px;"
                  clearable
                  :render-option="renderOption"
                />
              </n-form-item>
            </n-gi>
            <!-- 创建时间 -->
            <n-gi :span="8">
              <n-form-item label="创建时间">
                <j-date-range-picker
                  style="flex: 1;"
                  v-model:value="formValue.creationTime"
                  type="datetimerange"
                  format="yyyy-MM-dd"
                  :default-time="['00:00:00', '23:59:59']"
                  clearable
                />
              </n-form-item>
            </n-gi>
          </n-grid>
        </n-form>
      </template>
      <!-- 操作项 -->
      <template #tableHeaderBtn>
        <n-button @click="refresh" class="store-button" :loading="isLoading">刷 新</n-button>
        <n-button
          type="primary"
          @click="clickOrderCostDeduction()"
          v-if="hasFinanceAllocationManagementCostDeductionAuth"
        >
          订单成本扣除
        </n-button>
        <n-button
          v-if="hasFinanceAllocationManagementExportAuth"
          class="store-button"
          :loading="exportLoading"
          type="primary"
          @click="clickAllocationExport()"
        >
          导出
        </n-button>
      </template>
      <template #tableFooterBtn="scope" >
        <!-- 批量取消结算 -->
        <Popconfirm
          v-if="isSelectedCancelSettlement"
          @handleClick="handleBatchCancelSettlement(scope.selectedListIds, scope.selectedList)"
          :text="false"
          :ghost="true"
          size="small"
          buttonContent="批量取消结算"
          promptContent="此操作将把所有选中的订单取消结算，是否继续？"
          type="error"
          :loading="batchCancelLoading"
        ></Popconfirm>
      </template>
    </FormLayout>
    <breakoutDetails ref="breakoutDetailsShow" />
    <orderCostDeduction ref="orderCostDeductionShow" @closeDrawer="closeOrderCostDeduction" v-model:show="showOrderCostDeduction" />
  </div>
</template>

<script lang="tsx" setup name="SubBillManagement">
import { ref, onMounted, watch,h, computed ,type VNode, reactive } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { allocationPageAllocation, allocationExport, allocationCancelSettlement, allocationBatchCancelSettlement } from "@/services/api";
import { settlementStatusSecondLabels, settlementStatusSecondOptions, paymentMethodsLabels, payoutsStatusSecondOptions, payoutsStatusSecondLabels } from "@/constants";
import breakoutDetails from './compontents/breakoutDetails.vue'
import moment from "moment";
import { useMessages } from "@/hooks";
import { NTooltip, type SelectOption } from 'naive-ui';
import { hasFinanceAllocationManagementDetailAuth, hasFinanceAllocationManagementExportAuth, hasFinanceAllocationManagementCancelSettlementAuth, hasFinanceAllocationManagementCostDeductionAuth } from "../../../authList";
import Popconfirm from "@/components/Popconfirm/index.vue";
import orderCostDeduction from './compontents/orderCostDeduction/index.vue'

const { createMessageSuccess, createMessageError,createMessageWarning } = useMessages();

const renderOption = ({ node, option }: { node: VNode; option: SelectOption }) =>
        h(NTooltip, null, {
          trigger: () => node,
          default: () => option.label
})

/** 定义一个函数来格式化价格 */
const formatPrice = (price) => `${(price / 100).toFixed(2)}`;

const formValue = ref({
    dealerName:null,
    orderCode:null,
    status:null,
    creationTime:null,
    settlementStatus:null
})

/** 表格方法Hook */
const {
  isLoading,
  tableData,
  pageTableData,
  paginationRef,
  paginationChange,
  summaryRef
} = useTableDefault({
  pageDataRequest: allocationPageAllocation,
});


/* 表格列表项 */
const tableColumns = ref([
    {
        title: "分账单号",
        key: "allocationNo",
        width: 150,
        align: "left",
        summaryTitle:'',
    },
    {
        title: "经销商姓名",
        key: "dealerName",
        width: 180,
        align: "left",
        summaryTitle:'',
    },
    {
        title: "分账金额(元)",
        key: "allocationTotalAmount",
        width: 150,
        align: "left",
        render: (row) => {
            return row.allocationTotalAmount ? formatPrice(row.allocationTotalAmount) : '0.00'
        }

    },
    {
        title: "结算状态",
        key: "status",
        width: 150,
        align: "left",
        summaryTitle:'',
        render: (row) => {
            return  settlementStatusSecondLabels[row.status] ? settlementStatusSecondLabels[row.status] : '-'
        }
    },
    {
        title: "结算单号",
        key: "settlementNo",
        width: 150,
        align: "left",
        summaryTitle:'',
    },
    {
        title: "打款状态",
        key: "settlementStatus",
        width: 150,
        align: "left",
        summaryTitle:'',
        render: (row) => {
            return  payoutsStatusSecondLabels[row.settlementStatus] ? payoutsStatusSecondLabels[row.settlementStatus] : '-'
        }
    },
    {
        title: "结算时间点",
        key: "allocateTime",
        width: 150,
        align: "left",
        summaryTitle:'',
    },
    {
        title: "关联订单号",
        key: "orderCode",
        width: 150,
        align: "left",
        summaryTitle:'',
    },
    {
        title: "支付渠道",
        key: "payPlatform",
        width: 150,
        align: "left",
        summaryTitle:'',
        render: (row) => {
            return  paymentMethodsLabels[row.payPlatform] ? paymentMethodsLabels[row.payPlatform] : '-'
        }
    },
    {
        title: "支付商户号",
        key: "paymentMerchantId",
        width: 150,
        align: "left",
        summaryTitle:'',
    },
    {
        title: "创建时间",
        key: "createTime",
        width: 150,
        align: "left",
        summaryTitle:'',
    },
    {
        title: "操作",
        key: "action",
        width: 150,
        fixed: "right",
        align: "left",
        render: (row) => {
            return (
                <n-space>
                    {
                      hasFinanceAllocationManagementDetailAuth ?
                      <n-button
                        text
                        type="primary"
                        onClick={() => clickDetail(row)}
                    >
                        详情
                    </n-button> : null}
                    {
                      hasFinanceAllocationManagementCancelSettlementAuth && row.status === 0 ?
                      <Popconfirm 
                        onHandleClick={() => clickCancelSettlement(row)}
                        loading={cancelLoading.value}
                        buttonContent={'取消结算'}
                        type={'error'}
                        promptContent={'取消结算后，该分账单将变更为未结算状态。'}/> : null}
                </n-space>
            );
        },
    },
]);


/** 搜索 */
const formSearch = () =>{
    tableSearch();
}

/** 表格刷新 */
function refresh(){
  tableSearch();
}

/** 获取参数 */
const getParams = () => {
  const { dealerName, orderCode, status, creationTime, settlementStatus} = formValue.value;
  const createTimeStart =  creationTime ? moment(creationTime[0]).format(`YYYY-MM-DD HH:mm:ss`) : null
  const createTimeEnd = creationTime ? moment(creationTime[1]).format(`YYYY-MM-DD HH:mm:ss`) : null
  return {
    orderCode,
    dealerName,
    status,
    createTimeStart,
    createTimeEnd,
    settlementStatus
  };
};

/* 表格搜索 */
const tableSearch = () => {
  pageTableData(getParams(), paginationRef.value);
};

/** 详情 */
const breakoutDetailsShow = ref()
const clickDetail = (row) =>{
    const param =  {
        row:row
    }
    breakoutDetailsShow.value?.acceptParams(param);
}

/** 取消结算 */
const cancelLoading = ref(false)
const clickCancelSettlement = (row) =>{
  const param = {
    id:row.id
  }
  cancelLoading.value = true
  allocationCancelSettlement(param).then(() => {
    createMessageSuccess('操作成功')
    tableSearch()
  }).catch(() => {
    createMessageError('操作失败')
  }).finally(() => {
    cancelLoading.value = false
  })

}

/** 选中行 */
const selectedArr = ref<string[]>([])
const selectedKeysChange = (selectedKeys:string[], selectedRows:any[]) =>{
  selectedArr.value = selectedRows
}

const isSelectedCancelSettlement = computed(() => {
  return selectedArr.value.some(item => item['status'] == 0)
})

/** 批量取消结算 */
const batchCancelLoading = ref(false)
const handleBatchCancelSettlement = (selectedListIds, selectedList) =>{
  const param = {
    data:selectedList.filter(item => item['status'] == 0).map(item => item['id'])
  }
  batchCancelLoading.value = true
  allocationBatchCancelSettlement(param).then(() => {
    createMessageSuccess('操作成功')
    tableSearch()
  }).catch(() => {
    createMessageError('操作失败')
  }).finally(() => {
    batchCancelLoading.value = false
  })
}

const summaryRefs = ref(null);

const summaryColumn = (totalData, summaryRef) => {
  const _sum = { allocationTotalAmount: 0 };

  // 检查数据是否有效
  if (!Array.isArray(totalData) || totalData.length === 0 || !Array.isArray(totalData[0])) {
    return _sum;
  }

  totalData[0].forEach(row => {
    for (let key in row) {
      if (Object.keys(_sum).includes(key) && typeof row[key] === 'number') {
        _sum[key] += row[key];
      }
    }
  });

  // 仅在返回前格式化
  _sum["allocationTotalAmount"] = Number((_sum.allocationTotalAmount / 100).toFixed(2)) ;

  return _sum;
}

//导出
const exportLoading = ref(false)
const clickAllocationExport = async() =>{
  exportLoading.value = true
  const _params = {
    data: getParams()
  }
  try{
    await allocationExport(_params)
    createMessageSuccess('导出成功')
  }
  catch(err){
    createMessageError('导出失败:' + err)
  }finally{
    exportLoading.value = false
  }
}

const showOrderCostDeduction = ref(false)
/** 订单成本扣除 */
const clickOrderCostDeduction = () =>{
  showOrderCostDeduction.value = true
}
/** 关闭订单成本扣除 */
const closeOrderCostDeduction = () =>{
  // showOrderCostDeduction.value = false
  tableSearch()
}

watch([tableData,summaryRef], (newTableData,newSummaryRef) => {
     summaryRefs.value = summaryColumn(newTableData,newSummaryRef);
 });

const getSummaryData = () => {
  return summaryRefs.value;
}

defineExpose({getSummaryData});

/** 组件挂载 */
onMounted(() => {
  tableSearch();
});

watch([
     () => formValue.value.status,
     () => formValue.value.creationTime,
     () => formValue.value.settlementStatus,
    ],() => {
        tableSearch();
});
</script>

<style scoped lang="less"></style>
