<template>
    <JModal
        v-model:show="show"
        width="680"
        :title="model.title"
        :positiveText="'确定'"
        @after-leave="closeModal"
            @positive-click="_save"
            :positiveButtonProps="{
                loading: isLoading
            }"
      > 
        <!-- 添加分销员 -->
        <div v-if="model.title == '添加分销员'">
         <n-space style="box-sizing: border-box;padding-left: 12px;margin-bottom: 12px;align-items: center;">
            <span>昵称:</span>
              <JDistributorsSelect @distributorsValue="distributorsValue" :tableData="tableData"/>
            </n-space>
            <div style="height: 500px;">
              <FormLayout
               :tableData="tableData"
               :tableColumns="tableColumns"
               :is-table-pagination="false"
               :is-table-selection="false"
               :is-display-header="false"
               :isDisplayIndex="false"
              >
              </FormLayout>
            </div>
            <n-space style="box-sizing: border-box;padding-left: 12px;align-items: center;">
               <span>归属组织:</span>
               <JAffiliationOrganization 
               v-model:value="model.structureId"
               />
            </n-space>
        </div>

        <!-- 冻结分销员 -->
        <div v-if="model.title == '冻结分销员'">
           <p v-for="item in freezeData">{{item}}</p>
        </div>

        <!-- 解冻分销员 -->
        <div v-if="model.title == '解冻分销员'">
           <p v-for="item in thawData">{{item}}</p>
        </div>

        <!-- 删除分销员 -->
        <div v-if="model.title == '删除分销员'">
           <p v-for="item in deleteData">{{item}}</p>
        </div>

        <!-- 归属组织设置 -->
        <div v-if="model.title == '归属组织设置'">
            <n-space style="box-sizing: border-box;padding-left: 12px;align-items: center;">
               <span>归属组织:</span>
               <JAffiliationOrganization 
                 v-model:value="model.structureId"
                 :structureName="model.structureName"
                 v-model:defaultExpandedKeys="model.defaultExpandedKeys"
               />
            </n-space>
        </div>
        
      </JModal>
    </template>
    
<script setup lang="tsx" name="GroupConfigurationShow">
import FormLayout from "@/layout/FormLayout.vue";
import { ref } from "vue";
import JAffiliationOrganization  from '@/components/JSelect/JAffiliationOrganization.vue'
import JDistributorsSelect  from '@/components/JSelect/JDistributorsSelect.vue'
import { useMessages } from "@/hooks";
import { distributorAdd, distributorDelete, distributorSuspend, distributorUpdateStruct } from '@/services/api';
export interface DistributorModalProps {
  type?: any; // 弹窗模式 --> 默认add
  row?: Partial<distributorManagement.distributorContent>;
  refreshTable?: () => void; // 刷新表格数据
}

const freezeData = [
    '冻结后：',
    '1、该分销员在分销中心仅可查看个人推广产生的客户、分佣订单数据。',
    '2、该分销员进行推广不会产生新的客户和分佣订单。',
    '3、保留已绑定的客户关系。',
    '4、已产生的分佣订单不做处理。'
]

const thawData = [
    '解冻后:',
    '1、该分销员进行推广会产生新的客户和分佣订单。',
    '2、原有客户下单也会计算分佣。'
]

const deleteData = [
    '删除后：',
    '1、原该分销员下绑定的客户都将解绑，成为无上级分销员的状态。',
    '2、不会再产生新的分佣订单和客户绑定数据，该用户也无法再进入分销中心。',
    '3、用户可再次申请成为分销员，但之前作为分销员产生的数据都将无法恢复，请谨慎操作。',
    '4、后台报表数据保留。'
]

const tableData = ref([])

/* 表格列表项 */
const tableColumns = ref([
    {
        title: "昵称",
        key: "label",
        width: 150,
        align: "left"
    },
    {
        title: "手机号",
        key: "csMobile",
        width: 180,
        align: "left"
    },
    {
        title: "操作",
        key: "action",
        width: 100,
        fixed: "right",
        align: "left",
        render: (row) => {
            return (
                <n-space>
                    <n-button
                        text
                        type="error"
                        onClick={()=>{
                          {handleDelete(row)}
                        }}
                    >
                        删除
                    </n-button>
                </n-space>
            )
        }
    },
]);


    
/** 初始化参数 */
const initParams = {
  distributorVlue:undefined,
  structureId:undefined,
  title:'',
  customerId:[],
  structureName:null,
  defaultExpandedKeys:null
};
const model = ref({ ...initParams });

/* 提示信息 */
const message = useMessages();

/* 模态框显隐状态 */
const show = ref(false);

/* 父组件传过来的参数 */
const parameter = ref<DistributorModalProps>({});
const acceptParams = async (params) => {
  parameter.value = params
  model.value.title = parameter.value.type
  model.value.structureId = parameter.value.row?.structureId 
  model.value.structureName = parameter.value.row?.structureName 
  show.value = true;
};

/* 清空表单 */
const formDataReset = () => {
    model.value = { ...initParams };
    tableData.value.length = 0
};

/* 关闭弹窗 */
const closeModal = () => {
  formDataReset();
};
  
  
/* 确认--保存 */
const isLoading = ref(false);
const _save = async() => {
  
  model.value.customerId = tableData.value.map(item => item.value);
  if(model.value.customerId.length == 0 && model.value.title == '添加分销员'){
    message.createMessageWarning('请添加分销员')
    return true
  }
  isLoading.value = true
  
  try{
    await executionInterface ()
    message.createMessageSuccess(model.value.title + '成功')
    parameter.value.refreshTable()
    show.value = false
  }catch(err){
    message.createMessageError(model.value.title + '失败' + err)
  }finally{
    isLoading.value = false
  }
};

const executionInterface = () =>{
  /** 添加分销员api */
  if(model.value.title == '添加分销员'){
    const params = {
      data:{
        addVOS: model.value.customerId.map(customerId => ({
          customerId: customerId,
          structureId: model.value.structureId
        }))
      }
    }
    return distributorAdd (params)
  }
  
  /** 删除分销员api */
  if(model.value.title == '删除分销员'){
    return distributorDelete(parameter.value.row?.id)
  }

  /** 冻结分销员与解冻分销员api */
  if(model.value.title == '冻结分销员' || model.value.title == '解冻分销员'){
    const params = {
      data:{
        id:parameter.value.row?.id,
        status:model.value.title == '冻结分销员' ? 2 : 1
      }
    }
    return distributorSuspend(params)
  }

  /** 归属组织设置api */
  if(model.value.title == '归属组织设置'){
    const params = {
      data:{
        id:parameter.value.row?.id,
        structureId:model.value.structureId
      }
    }
    return distributorUpdateStruct(params)
  }
}

const distributorsValue = (value) =>{
  tableData.value.push(value)
}

/** 删除 */
const handleDelete = (row) =>{
  tableData.value = tableData.value.filter((item) => item.value !== row.value);
}

defineExpose({
  acceptParams,
});

 </script>
   
 <style scoped lang="less"></style>
        