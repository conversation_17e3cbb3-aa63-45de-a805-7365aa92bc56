import { SupplierProductManagementAuth } from "@/enums/authKeys";
import { hasAuth } from "@/utils/auth/authUtils";

/** 供应商商品 - 新增 */
export const hasAddGoodsAuth= function(){
    return hasAuth(SupplierProductManagementAuth.supplierProductManagementIndexAdd.key);
}()

/** 供应商商品 - 编辑 */
export const hasEditGoodsAuth = function(){
    return hasAuth(SupplierProductManagementAuth.supplierProductManagementIndexEdit.key);
}()

/** 供应商商品 - 提交申请 */
export const hasApplyGoodsAuth = function(){
    return hasAuth(SupplierProductManagementAuth.supplierProductManagementIndexApply.key);
}()

/** 供应商商品 - 详情 */
export const hasDetailsGoodsAuth = function(){
    return hasAuth(SupplierProductManagementAuth.supplierProductManagementIndexDetails.key);
}()