<template>
  <NSpin v-show="getInquiryDataLoading" size="tiny" style="height: 100%"></NSpin>
  <template v-if="!getInquiryDataLoading">
    <div style="display: flex;font-size: 14px">
      <span id="countdown">{{ countdownText }}</span>
      <span v-if="receiveStartTime && countdownText !== '问诊已结束'">
        <template v-if="receiveStartTime && countdownText !== '问诊已结束'"><slot>后结束</slot></template>
      </span>
    </div>
    <IMEarlyTermination
        :ref="IMEarlyTerminationRef"
        v-if="receiveStartTime && countdownText !== '问诊已结束'"
        @handle-finish-conversation="handleAheadFinishConversation"
    />
  </template>
</template>
<script setup lang="tsx">
import {ref, onUnmounted, onMounted, inject} from "vue";
import IMEarlyTermination from "@/views/DoctorEndModule/IM/components/common/IMEarlyTermination/index.vue";
import moment from "moment";
import {getPresReceiveEndTime} from "@/services/api/doctorEndApi";
import {deepClone} from "@/utils";
import type {JConversationModel} from "@/views/DoctorEndModule/IM/types";
import {useMessages} from "@/hooks";
import ImEmitter from "@/views/DoctorEndModule/IM/utils/ImMitter";

interface IProps {
  countdown: string | number
}

const props = defineProps<IProps>()

const emits = defineEmits<{
  (e: 'handleCountdownFinish'): void;
}>();

const {createMessageSuccess, createMessageError} = useMessages();
const IMEarlyTerminationRef = ref(null)
let countDownTimer = null;
const receiveStartTime = ref<string | number>(null)
const getInquiryDataLoading = ref<boolean>(true)
const countdownText = ref("问诊已结束");
const {tabName} = inject('tabName'); // 当前的TabName,值为 processing | completed
const {currentConversationData, changeCurrentConversation} = inject('currentConversationData');

onMounted(() => {
  if (tabName.value !== 'completed') {
    handleGetPresReceiveEndTime().then(res => {
      stopCountDownTimer()
      countDownTimer = setInterval(updateCountdown, 1000)
    })
  }
})

// 获取当前会话的问诊单数据和结束时间
async function handleGetPresReceiveEndTime() {
  try {
    getInquiryDataLoading.value = true
    const params = {
      customerId: currentConversationData.value.contactUserId
    }

    const result = await getPresReceiveEndTime(params);
    let tempConversation = deepClone(currentConversationData.value)
    tempConversation.receiveStartTime = result.receiveStartTime
    tempConversation.duration = result.duration
    tempConversation.inquiryId = result.id
    changeReceiveStartTime(tempConversation)
    changeCurrentConversation(tempConversation)
  } catch (e) {
    if (e == '当前医患会话不存在问诊单记录') {
      let tempConversation = deepClone(currentConversationData.value)
      tempConversation.status = 'finish'
      changeCurrentConversation(tempConversation)
    } else {
      createMessageError(`获取问诊单数据失败:${e}`)
    }
  } finally {
    setTimeout(() => {
      getInquiryDataLoading.value = false
    }, 500)
  }
}

function changeReceiveStartTime(conversation: JConversationModel) {
  // 1. 解析原始时间
  const baseTime = moment(conversation.receiveStartTime, 'YYYY-MM-DD HH:mm:ss');
  receiveStartTime.value = baseTime.add({
    minute: conversation?.duration,
  }).valueOf()
}

function isTimestampExpired(timestamp: string | number) {
  const now = moment();
  const target = moment(Number(timestamp));
  return now.isSameOrAfter(target);
}

// 更新倒计时的函数
const updateCountdown = () => {
  if (isTimestampExpired(receiveStartTime.value)) {
    countdownText.value = "问诊已结束";
    IMEarlyTerminationRef.value?.handleEndConversation()
    receiveStartTime.value = null
    countdownText.value = ''
    // let tempConversation = deepClone(currentConversationData.value)
    // tempConversation.status = 'finish'
    // changeCurrentConversation(tempConversation)
    ImEmitter.emit('RefreshConversationList')
    stopCountDownTimer()
    emits('handleCountdownFinish')
  } else {
    // // 计算剩余的天数、小时、分钟和秒数
    const now = moment();
    const target = moment(Number(receiveStartTime.value));

    const duration = moment.duration(target.diff(now));

    const daysStr = duration.days() > 0 ? `${duration.days()}天` : '';
    const hoursStr = duration.hours() > 0 ? `${duration.hours()}小时` : '';
    const minutesStr = duration.minutes() > 0 ? `${duration.minutes()}分钟` : '';
    const secondsStr = duration.seconds() > 0 ? `${duration.seconds()}秒` : '';

    countdownText.value = `${daysStr}${hoursStr}${minutesStr}${secondsStr}`;
  }
}

const stopCountDownTimer = () => {
  if (countDownTimer) {
    clearInterval(countDownTimer)
    countDownTimer = null
  }
}

// 提前结束会话
function handleAheadFinishConversation() {
  receiveStartTime.value = null
  countdownText.value = ''
  // let tempConversation = deepClone(currentConversationData.value)
  // tempConversation.status = 'finish'
  // changeCurrentConversation(tempConversation)
  ImEmitter.emit('RefreshConversationList')
  stopCountDownTimer()
  emits('handleCountdownFinish')
}

onUnmounted(() => {
  stopCountDownTimer()
})

</script>
<style scoped lang="less">

</style>
