{
  "extends": "@vue/tsconfig/tsconfig.web.json",
  "include": ["env.d.ts", "src/**/*", "src/**/*.vue","src/**/*.tsx", "src/**/*.jsx"],
  "compilerOptions": {
    "baseUrl": ".",
    "jsx": "preserve",
    "paths": {
      "@/*": ["./src/*"]
    },
    "target": "es5",
    "lib": ["ESNext"],
    "noImplicitAny": false,
    "strict": false
  },

  "references": [
    {
      "path": "./tsconfig.config.json"
    }
  ],
}
