<template>
  <NTooltip 
    :placement="tooltipPlacement" 
    :z-index="zIndex" 
    :disabled="!tooltipContent"
    :content-style="{
      padding: '2px',
    }"
  >
    <template #trigger>
      <NButton quaternary v-bind="$attrs" style="width: 100%;height: 40px;display: flex;justify-content: flex-start;align-items: center;">
        <div style="display: flex;justify-content: center;align-items: center;gap: 8px;">
          <slot>
            <SvgIcon :icon="icon" />
          </slot>
        </div>
      </NButton>
    </template>
    {{ tooltipContent }}
  </NTooltip>
</template>

<script setup lang="ts">
import type { Component } from 'vue';
import { NTooltip, NButton } from 'naive-ui';
import type { PopoverPlacement } from 'naive-ui';
import SvgIcon from "@/components/SvgIcon/index.vue";

defineOptions({
  name: 'Menu',
  inheritAttrs: false
});

interface Props {
  /** 按钮类名 */
  class?: string;
  /** 图标 */
  icon?: Component ;
  /** 提示内容 */
  tooltipContent?: string;
  /** 提示位置 */
  tooltipPlacement?: PopoverPlacement;
  /** 层级 */
  zIndex?: number;
}

/** props */
const props = withDefaults(defineProps<Props>(), {
  class: '',
  icon: null,
  tooltipContent: '',
  tooltipPlacement: 'bottom',
  zIndex: 98
});
</script>
