import { defHttp } from '@/services';

/** 社群系统Api */
export const enum SocialStystemApi {
    /** 获取社群端 经销商分组列表 */
    dealerGroup = "/sg/listSgDealerGroupInfo",
    /** 分页搜索指定社群经销商分组下的 经销商信息 */
    dealer = "/sg/pageSearchSgDealerInfo",
    /** 分页搜索指定社群经销商下的 群管信息 */
    gmInfo = "/sg/pageSearchSgGmInfo",

    /** 分页检索分销员 */
    distributorPageForUserModule = '/distributor/pageForUserModule',
    /** 组织架构列表 */
    structureListForUserModule = '/structure/listForOthers'
}

/**
 * @description 获取经销商分组列表
 */
export function getSgDealerGroup(params) {
  return defHttp.post({
    url: SocialStystemApi.dealerGroup,
    params
  })
}

/**
 * @description 获取经销商列表
 */
export function getSgDealerInfo(params) {
  return defHttp.post({
    url: SocialStystemApi.dealer,
    params
  })
}

/**
 * @description 获取群管列表
 */
export function getSgGmInfo(params: {
  data: {
    groupMgrIdList?: Array<number | string>,
    searchValue?: string, // 模糊匹配群管id、群管名称、微信昵称
    dealerIds?: string, // 经销商ID，多个用英文','分割
  },
  pageVO: {
    current: number,
    size: number
  }
}) {
  return defHttp.post({
    url: SocialStystemApi.gmInfo,
    params
  })
}

/**
 * @description 分页检索分销员
 */
export function distributorPageForUserModule(params) {
  return defHttp.post({
    url: SocialStystemApi.distributorPageForUserModule,
    params
  })
}

/**
 * @description 组织架构列表
 */
export function structureListForUserModule(params) {
  return defHttp.post({
    url: SocialStystemApi.structureListForUserModule,
    params
  })
}
