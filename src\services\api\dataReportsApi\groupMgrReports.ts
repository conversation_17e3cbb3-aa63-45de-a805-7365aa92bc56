import { defHttp } from "@/services";

/** 群管统计 */
export const enum GroupMgrReportsApi {
  gmStatData = "/report/gmStatData",
  gmStatDataExport = "/report/gmStatDataExport",
}

/** 获取群管统计 */
export function getGmStatData(params) {
  return defHttp.post({
    url: GroupMgrReportsApi.gmStatData,
    params,
  });
}

/** 群管统计导出 */
export function gmStatDataExport(params) {
  return defHttp.post({
    url: GroupMgrReportsApi.gmStatDataExport,
    requestConfig: {
      responeseType: "stream",
    },
    params,
  });
}
