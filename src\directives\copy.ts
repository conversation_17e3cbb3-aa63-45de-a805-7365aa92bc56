import type { App, Directive, DirectiveBinding } from "vue";
import { useMessages } from "@/hooks";
import { copyText } from "@/utils/clipboardUtils";

interface ElType extends HTMLElement {
	copyData: string | number;
	__handleClick__: any;
}

const setupCopyDirective = (app: App) => {
	const copy: Directive = {
		mounted(el: ElType, binding: DirectiveBinding) {
			el.copyData = binding.value;
			el.addEventListener("click", handleClick); // 给元素绑定点击事件
		},
		// 指令所在组件的 VNode 更新时触发
		updated(el: ElType, binding: DirectiveBinding) {
			el.copyData = binding.value; // 更新需要复制的数据
		},
		// 指令与元素解绑时触发
		beforeUnmount(el: ElType) {
			el.removeEventListener("click", el.__handleClick__); // 移除元素的点击事件
		},
	};

	/* 点击事件处理函数 */
	function handleClick(this: any) {
		const { createMessageSuccess, createMessageError } = useMessages();

		if (!this.copyData) {
			createMessageError("复制内容为空，无法复制");
			return;
		}

		copyText(this.copyData.toLocaleString());
		createMessageSuccess("复制成功");
	}

	/* 注册自定义事件 */
	app.directive("copy", copy);
};

export default setupCopyDirective;
