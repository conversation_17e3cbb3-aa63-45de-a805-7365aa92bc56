<template>
    <JDrawer
      v-model:show="drawerVisible"
      title="基础信息"
      :isGetLoading = isGetLoading
      :enableFooterWrapper="true"
      @after-leave="closeDrawer"
      to=".table-wrapper"
      :contents-list="[
        {
          name: '',
          slotName: 'basic_information'
        },
        {
          name: '问诊服务',
          slotName: 'consultation_service'
        },
      ]"
    >
      <!-- 表单内容 -->
      <template #basic_information>
        <n-form
          ref="formRef"
          :model="model"
          :rules="rules"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          size="small"
          :style="{width: '60%'}"
        >
           <!-- 姓名 -->
           <n-form-item-gi :span="8" label="姓名" path="doctorName">
             <n-input v-model:value="model.doctorName" clearable placeholder="请输入姓名" />
           </n-form-item-gi>
           <!-- 科室 -->
           <n-form-item-gi :span="8" label="科室" path="departmentId">
            <JDepartment v-model:value="model.departmentId" isImmediately style="width: 100%;" placeholder="请输入科室"/>
           </n-form-item-gi>
           <!-- 职称 -->
           <n-form-item-gi :span="8" label="职称" path="title">
            <n-select 
                  v-model:value="model.title" 
                  :options="JobTitleOptions" 
                  style="width: 100%;" 
                  clearable
                  placeholder="请选择职称"
                />
           </n-form-item-gi>
           <!-- 机构 -->
           <n-form-item-gi :span="8" label="机构" path="institutionId">
              <JInstitution v-model:value="model.institutionId" isImmediately style="width: 100%;"/>
           </n-form-item-gi>
           <!-- 手机号 -->
           <n-form-item-gi :span="8" label="手机号">
             <n-input v-model:value="model.mobile" clearable placeholder="请输入手机号"  @blur="handleMobileVerify"/>
           </n-form-item-gi>
           <!-- 医生简介 -->
           <n-form-item-gi :span="8" label="医生简介">
                <n-input type="textarea" placeholder="请输入诊断说明" round clearable v-model:value="model.introduction" maxlength="500" show-count/>
           </n-form-item-gi>
           <!-- 医生擅长 -->
           <n-form-item-gi :span="8" label="医生擅长">
            <n-input type="textarea" placeholder="请输入医生擅长" round clearable v-model:value="model.beGoodAt" maxlength="500" show-count/>
           </n-form-item-gi>
           <!-- 头像 -->
           <n-form-item-gi :span="8" label="医生头像">
             <div>
              <CustomizeUpload v-model:value="model.img" accept="image/jpg,image/jpeg,image/gif,image/png" :fileListSize="1" :max="1" :imageUploadType="['image/jpg','image/jpeg','image/gif','image/png']" :enableTypeValidation="true"/>
              <span style="font-size: 14px;color: #c2c2c2;">支持JPG、JPEG、GIF、PNG格式</span>
             </div>
           </n-form-item-gi>
           <!-- 开方权限 -->
           <n-form-item-gi :span="8" label="开方权限">
            <n-radio-group v-model:value="model.presAuth">
                <n-space>
                  <n-radio :value="1">
                    是
                  </n-radio>
                  <n-radio :value="0">
                    否
                  </n-radio>
                </n-space>
            </n-radio-group>
           </n-form-item-gi>
           <!-- 签名照片 -->
           <n-form-item-gi :span="8" label="签名照片">
             <div >
                <CustomizeUpload v-model:value="model.signImg" accept=".png" :fileListSize="1" :max="1" :isSignatureImg="true" :maxFileSize="1" :isCrop="true" :title="'裁剪签名图片'" :fixedNumber="[3,1]" :imageUploadType="['image/png']" :enableTypeValidation="true"/>
                <div style="display: flex; font-size: 14px;color: #c2c2c2;margin-top: 10px;">
                  
                    <span>请上传背景透明的png格式的签名图片</span> 
                    <n-popover trigger="hover" :show-arrow="false">
                      <template #trigger>
                        <span style="color: #169bd5; cursor: pointer">【查看示例】</span>
                      </template>
                      <img 
                        style="width: 120px;
                        height: 120px;"
                        :src="ImageExample" 
                        alt="" />
                    </n-popover>
                    <span>，不能大于1M，平台审核药师必传</span>
                </div>
             </div>
           </n-form-item-gi>
           <!-- 医师资格证书编号 -->
           <n-form-item-gi :span="8" label="医师资格证书编号">
             <n-input v-model:value="model.doctorCode" clearable placeholder="请输入医师资格证书编号" />
           </n-form-item-gi>
           <!-- 第三方医师编码 -->
           <n-form-item-gi :span="8" label="第三方医师编码">
             <n-input v-model:value="model.thirdDoctorCode" clearable placeholder="请输入医师在第三方系统的唯一编码，可用于数据推送等事务" maxlength="64" show-count/>
           </n-form-item-gi>
        </n-form>
      </template>
      <template #consultation_service>
           <!-- 图文问诊 -->
           <n-form-item-gi :span="8" label="图文问诊">
            <n-switch v-model:value="model.isPictureText" />
           </n-form-item-gi>
           <!-- 问诊费用 -->
           <n-form-item-gi :span="8" label="问诊费用">
            <n-radio-group v-model:value="model.pictureFeeType">
                 <n-space>
                   <n-radio :value="0">
                     免费
                   </n-radio>
                   <n-radio :value="1">
                     收费
                   </n-radio>
                 </n-space>
             </n-radio-group>
             <n-input  
                   v-model:value="model.consultationFee" 
                   :show-button="false" 
                   :max="9999" 
                   :min="0.1"
                   placeholder="请输入收费金额"
                   style="width: 120px;margin: 0 5px;"
                   @blur="handleFeeAmountProcessing"
                   :disabled="!model.pictureFeeType"
                  />
              <span>元/次（每次问诊时长可在【基础设置】界面配置）</span>
             </n-form-item-gi>
            <!-- 视频问诊 -->
           <n-form-item-gi :span="8" label="视频问诊">
            <n-switch v-model:value="model.isVideo" />
           </n-form-item-gi>
           <!-- 问诊费用 -->
           <n-form-item-gi :span="8" label="问诊费用">
            <n-radio-group v-model:value="model.videoFeeType">
                 <n-space>
                   <n-radio :value="0">
                     免费
                   </n-radio>
                   <n-radio :value="1">
                     收费
                   </n-radio>
                 </n-space>
             </n-radio-group>
             <n-input  
                   v-model:value="model.videoConsultationFee" 
                   :show-button="false" 
                   :max="9999" 
                   :min="0.1"
                   placeholder="请输入收费金额"
                   style="width: 120px;margin: 0 5px;"
                   @blur="handleFeeAmountProcessing"
                   :disabled="!model.videoFeeType"
                  />
              <span>元/次</span>
            </n-form-item-gi>
      </template>
      <!-- footer -->
      <template #footer>
        <n-space style="align-content:center;">
          启用状态
          <n-radio-group v-model:value="model.isActived" style="margin-left: 10px;" :disabled="isGetLoading">
            <n-space>
              <n-radio :value="1">
                是
              </n-radio>
              <n-radio :value="0">
                否
              </n-radio>
              <n-checkbox v-model:checked="model.isRecommend">
                推荐医生
              </n-checkbox>
              <HelpPopover helpEntry="推荐医生" />
            </n-space>
          </n-radio-group>
        </n-space>
        <n-space justify="end">
          <n-button @click="closeDrawer" class="store-button">取 消</n-button>
          <n-button type="primary" :loading="isLoading" @click="_save" class="store-button">保 存</n-button>
        </n-space>
      </template>
    </JDrawer>
  </template>
  
<script lang="ts" setup name="NewDoctor">
import { ref, watch } from "vue";
import { deepClone } from "@/utils";
import type { FormRules } from "naive-ui";
import { useMessages } from '@/hooks';
import { JobTitleOptions } from "@/constants";
import ImageExample from "@/assets/image/system/ImageExample.png";
import { doctorEntityAdd, doctorEntityUpdate, doctorEntityGet} from "@/services/api";
import { transformMinioSrc } from "@/utils/fileUtils";
import SvgIcon from "@/components/SvgIcon/index.vue";
const { createMessageSuccess, createMessageError, createMessageWarning } = useMessages();

const avatarEditModalShowRef = ref(false);

/* 表单实例 */
const formRef = ref();

/* 表单参数初始化 */
const initParams = {
  /** 医生头像 */
  img: '',
  /** 医生姓名 */
  doctorName:null,
  /** 科室ID */
  departmentId:null,
  /** 机构ID */
  institutionId:null,
  /** 职称 */
  title:null,
  /** 手机号码 */
  mobile:null,
  /** 医生简介 */
  introduction:null,
  /** 医生擅长 */
  beGoodAt:null,
  /** 开方权限 */
  presAuth:1,
  /** 医师签名照片 */
  signImg:'',
  /** 医师资格证书编码 */
  doctorCode:null,
  /** 第三方医师编码 */
  thirdDoctorCode:null,
  /** 是否支持图文问诊 */
  isPictureText:false,
  /** 问诊费用类型 */
  pictureFeeType:0,
  /** 问诊费用 */
  consultationFee:undefined,
  /** 是否启用 */
  isActived:1,
  /** 医生id */
  id:null,
  /** 推荐医生 */
  isRecommend:false,
  /** 是否支持视频问诊 */
  isVideo:false,
  /** 视频问诊费用类型 */
  videoFeeType:0,
  /** 视频问诊费用 */
  videoConsultationFee: undefined,

};
const model = ref(deepClone(initParams));

/* 表单规则 */
const rules: FormRules = {
  doctorName: {
    required: true,
    trigger: ["blur", "input"],
    message: "请输入姓名",
  },
  departmentId: {
    required: true,
    trigger: ["blur", "change"],
    message: "请选择科室",
  },
  title: {
    type: "number",
    required: true,
    trigger: ["blur", "change"],
    message: "请选择职称",
  },
  institutionId: {
    required: true,
    trigger: ["blur", "change"],
    message: "请选择机构",
  },
};

/** 获取参数 */
const isGetLoading = ref(false)
const getParams = () => {
  let img = model.value?.img[0]
  let signImg = model.value?.signImg[0]
  let isPictureText = model.value.isPictureText ? 1 : 0
  let isVideo = model.value.isVideo ? 1 : 0
  let consultationFee = Number(model.value.consultationFee * 100)
  let videoConsultationFee = Number(model.value.videoConsultationFee * 100)
  let isRecommend = Number(model.value.isRecommend)
  const {
    doctorName,
    departmentId,
    institutionId,
    title,
    mobile,
    introduction,
    beGoodAt,
    presAuth,
    doctorCode,
    thirdDoctorCode,
    pictureFeeType,
    videoFeeType,
    isActived,
    id
  } = model.value;
  
  return {
    img,
    doctorName,
    departmentId,
    institutionId,
    title,
    mobile: mobile === oldMobile.value ? undefined : mobile,
    isUpdateMobile: mobile === oldMobile.value ? undefined : 1,
    introduction,
    beGoodAt,
    presAuth,
    signImg,
    doctorCode,
    thirdDoctorCode,
    isPictureText,
    pictureFeeType,
    consultationFee,
    isActived,
    id,
    isRecommend,
    isVideo,
    videoFeeType,
    videoConsultationFee
  };
};

/** 抽屉状态 */
const drawerVisible = ref(false);

const drawerProps = ref()
const acceptParams = async(param) => {
  drawerVisible.value = true;
  drawerProps.value = param
  if(drawerProps.value.type == 'edit'){
    doctorEntityGetData(drawerProps.value.row.id)
  }
};

/** 关闭抽屉 */
const closeDrawer = () => {
    oldMobile.value = null;
    model.value = deepClone(initParams);
    drawerVisible.value = false;
};

/** 收费金额保留小数点处理 */
const handleFeeAmountProcessing = () =>{
    model.value.consultationFee = parseFloat(model.value.consultationFee).toFixed(2);
    model.value.videoConsultationFee = parseFloat(model.value.videoConsultationFee).toFixed(2);   
}

/** 手机号码校验 */
const handleMobileVerify = () =>{
  const phoneRegex = /^[1]([3-9])[0-9]{9}$/; // 手机号正则
    if(!phoneRegex.test(model.value.mobile) && model.value.mobile != null){
        model.value.mobile = null
        createMessageWarning('请输入有效的手机号码');
    }
}
const oldMobile = ref()
/** 获取单个查询 */
const doctorEntityGetData = async(ids) =>{
  isGetLoading.value = true
  try{
   const res = await doctorEntityGet(ids)
   oldMobile.value = res?.mobile ? res.mobile : null
   model.value.pictureFeeType = res.consultationFee > 0 ? 1 : 0;
   model.value.videoFeeType = res.videoConsultationFee > 0 ? 1 : 0;
   const consultationFee =  res.consultationFee != 0 ? (res.consultationFee / 100).toFixed(2) : null;
   const videoConsultationFee =  res.videoConsultationFee != 0 ? (res.videoConsultationFee / 100).toFixed(2) : null;
   const isPictureText = Boolean(res.isPictureText);
   const isVideo = Boolean(res.isVideo);
   const signImg = res.signImg ? [res.signImg] : '';
   const img = res.img ? [res.img] : '';
    const {
     doctorName,
     departmentId,
     institutionId,
     title,
     mobile,
     introduction,
     beGoodAt,
     presAuth,
     doctorCode,
     thirdDoctorCode,
     isActived,
     id,
     isRecommend
       } = res;

    Object.assign(model.value, {
      img,
      doctorName,
      departmentId,
      institutionId,
      title,
      mobile,
      introduction,
      beGoodAt,
      presAuth,
      signImg,
      doctorCode,
      thirdDoctorCode,
      isPictureText,
      isVideo,
      consultationFee,
      videoConsultationFee,
      isActived,
      id,
      isRecommend:Boolean(isRecommend)
    });
  }catch(err){
    createMessageError('获取单个查询数据失败:' + err)
  }finally{
    isGetLoading.value = false
  }
  
}

/* 确认--保存 */
const isLoading = ref(false);
const _save = async(e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      isLoading.value = true
      if(model.value.presAuth && model.value.signImg == ''){
       createMessageWarning('开方权限为“是”,医师签名照片需“必选”')
       isLoading.value = false
       return 
      }
      if(model.value.pictureFeeType && (Number(model.value.consultationFee * 100) <= 0 || model.value.consultationFee == null)){
        createMessageWarning('请输入收费金额,且金额可输入大于0小于1万的数值')
        isLoading.value = false
        return 
      }
      if(model.value.videoFeeType && (Number(model.value.videoConsultationFee * 100) <= 0 || model.value.videoConsultationFee == null)){
        createMessageWarning('请输入收费金额,且金额可输入大于0小于1万的数值')
        isLoading.value = false
        return 
      }
      try{
        drawerProps.value.type == 'add' ? await doctorEntityAdd({data:getParams()}) : await doctorEntityUpdate({data:getParams()})
        drawerProps.value.refresh()
        drawerVisible.value = false
        createMessageSuccess((drawerProps.value.type == 'add' ? '新增' : '编辑') + '成功' )
      }catch(err){
        createMessageError((drawerProps.value.type == 'add' ? '新增' : '编辑') + '失败:' + err)
      }finally{
        isLoading.value = false
      }
    }
  })
};

watch(()=>model.value.img,(newVal)=>{
  if(newVal[0] == '') model.value.img = ''
})

watch(()=>model.value.signImg,(newVal)=>{
  if(newVal[0] == '') model.value.signImg = ''
})

watch(()=>model.value.pictureFeeType,(newVal)=>{
  if(!newVal){
    model.value.consultationFee = null
  }
})

watch(()=>model.value.videoFeeType,(newVal)=>{
  if(!newVal){
    model.value.videoConsultationFee = null
  }
})
watch(()=>model.value.consultationFee,(newVal)=>{
  if (isNaN(newVal)) {
    model.value.consultationFee = null;
  }
})

watch(()=>model.value.videoConsultationFee,(newVal)=>{
  if (isNaN(newVal)) {
    model.value.videoConsultationFee = null;
  }
})



defineExpose({
  acceptParams,
  closeDrawer,
  drawerVisible
});
  

</script>

<style lang="less" scoped></style>
  