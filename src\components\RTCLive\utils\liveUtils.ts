
/** 获取当前的全部麦克风设备 */
import VERTC from '@volcengine/rtc';
export async function getMicrophoneList():Promise<Array<MediaDeviceInfo>> {
    try{
        return await VERTC.enumerateAudioCaptureDevices()
    }
    catch(e){
        throw new Error(`获取麦克风设备异常:${e}`)
    }
}

/** 获取当前的全部摄像头设备 */
export async function getCameraList():Promise<Array<MediaDeviceInfo>> {
    try{
        return await VERTC.enumerateVideoCaptureDevices()
    }
    catch(e){
        throw new Error(`获取摄像头设备异常:${e}`)
    }
}