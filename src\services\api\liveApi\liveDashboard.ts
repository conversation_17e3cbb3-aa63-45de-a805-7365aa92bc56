import { defHttp } from "@/services";

/** 数据大屏 */
export const enum LiveDashboardApi {
    accountActivityData = "/liveActivity/getAccountActivityData",
    accountActivityDataDuration = "/liveActivity/getAccountActivityDataDuration",
    liveActivityOrderData = "/liveActivity/getLiveActivityOrderData",
    liveActivityOrderDataLine = "/liveActivity/getLiveActivityOrderDataLine",
    liveActivityOrderDataLineList = "/liveActivityOrderDataLine/list",
    liveActivityGet = "/liveActivity/get",
    liveActivityProductRanking = "/liveActivity/getLiveActivityProductRanking",
    pcViewUrl = "/liveActivity/getPcViewUrl",
    actualLiveTime = "/liveActivity/getActualLiveTime",
    getEnterUV = "/liveActivity/getEnterUV",
    getLiveActivityOrderAmount = "/liveActivity/getLiveActivityOrderAmount",
    
}

/**
 * @description 启用/禁用供应商
 * @param id 直播间id
 * @param name 名称
 * @param liveStartTime 直播开始时间
 * @param liveEndTime 直播结束时间
 */
interface accountActivityDataRes {
    data:{
        id:string,
        name:string,
        liveStartTime:string,
        liveEndTime:string,
    }
}
/**
 * @description 获取直播间实时数据
 */
export function accountActivityData(params:accountActivityDataRes) {
    return defHttp.post({
        url: LiveDashboardApi.accountActivityData,
        params,
    });
}

/**
 * @description 获取直播间观看时长分布数据
 * @param id 直播间id
 */
export function accountActivityDataDuration(id:string) {
    return defHttp.post({
        url: LiveDashboardApi.accountActivityDataDuration,
        params:{
            data:{id}
        }
    });
}

/**
 * @description 获取直播间订单数据
 * @param id 直播间id
 */
export function liveActivityOrderData(id:string) {
    return defHttp.post({
        url: LiveDashboardApi.liveActivityOrderData,
        params:{
            data:{id}
        }
    });
}

/**
 * @description 获取直播间订单线形图数据 (近5分钟)
 */
export function liveActivityOrderDataLine(params) {
    return defHttp.post({
        url: LiveDashboardApi.liveActivityOrderDataLine,
        params
    });
}
/**
 * @description 获取直播间订单线形图数据 (大于5分钟)
 */
export function liveActivityOrderDataLineList(params) {
    return defHttp.post({
        url: LiveDashboardApi.liveActivityOrderDataLineList,
        params
    });
}

/**
 * @description 获取直播间详细信息
 */
export function liveActivityGet(id:string) {
    return defHttp.get({
        url: LiveDashboardApi.liveActivityGet+ `?id=${id}`
    });
}
/**
 * @description 获取直播间商品排行榜 最多10条数据
 * @param id 直播间id
 */
export function liveActivityProductRanking(id:string) {
    return defHttp.post({
        url: LiveDashboardApi.liveActivityProductRanking,
        params:{
            data:{id},
            pageVO:{
                current:1,
                size:10
            }
        }
    });
}

/**
 * @description 获取Pc端直播间链接
 */
export function pcViewUrl(id:string) {
    return defHttp.get({
        url: LiveDashboardApi.pcViewUrl+ `?id=${id}`
    });
}

/**
 * @description 获取实际开播时间
 */
export function actualLiveTime(id:string) {
    return defHttp.get({
        url: LiveDashboardApi.actualLiveTime+ `?id=${id}`
    });
}

/**
 * @description 获取近5分钟进入直播间人数
 */
export function getEnterUV(activityId:string) {
    return defHttp.get({
        url: LiveDashboardApi.getEnterUV+ `?activityId=${activityId}`
    });
}

/**
 * @description 获取近5分钟成交金额
 */
export function getLiveActivityOrderAmount(activityId:string) {
    return defHttp.get({
        url: LiveDashboardApi.getLiveActivityOrderAmount+ `?activityId=${activityId}`
    });
}