<template>
  <div class="pdf-container">
    <div v-if="loading" class="loading">
      <div class="loading-text">图片加载中...</div>
    </div>
    <div v-else-if="error" class="error">
      <div class="error-text">图片加载失败</div>
    </div>
    <div v-else class="pdf-image">
      <n-image v-if="pdfImage" :width="width" :height="height" :src="pdfImage" :render-toolbar="renderCustomToolbar" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, h } from "vue";
import { NButton, NIcon } from "naive-ui";
import { DownloadOutline } from "@vicons/ionicons5";
import type { ImageRenderToolbarProps } from "naive-ui";
import { getDocument } from "pdfjs-dist/legacy/build/pdf.mjs";
import "pdfjs-dist/build/pdf.worker.mjs";
import { color } from "echarts";
// 设置pdf.js的worker路径
// pdfjsLib.GlobalWorkerOptions.workerSrc = `/pdf-worker/pdf.worker.mjs`;
const props = withDefaults(
  defineProps<{
    pdfUrl: string;
    scale: number;
    width: number;
    height: number;
  }>(),
  {
    pdfUrl: "",
    scale: 1,
    width: 100,
    height: 100,
  },
);
const pdfImage = ref<string>("");
const loading = ref(true);
const error = ref<string | null>(null);

// 加载PDF并转换为图片（仅第一页）
const loadPdf = async () => {
  try {
    loading.value = true;
    error.value = null;
    pdfImage.value = "";
    // 加载PDF文档
    const loadingTask = getDocument({
      url: props.pdfUrl,
      cMapUrl: "/pdf-worker/cmaps/",
      cMapPacked: true,
    });
    const pdf = await loadingTask.promise;
    // 只获取第一页
    const page = await pdf.getPage(1);
    const viewport = page.getViewport({ scale: props.scale || 1.5 });

    const canvas = document.createElement("canvas");
    const context = canvas.getContext("2d");

    canvas.height = viewport.height;
    canvas.width = viewport.width;

    await page.render({
      canvasContext: context!,
      viewport: viewport,
    }).promise;

    pdfImage.value = canvas.toDataURL("image/png");
    loading.value = false;
  } catch (err) {
    console.error("PDF加载或渲染失败:", err);
    error.value = err instanceof Error ? err.message : "未知错误";
    console.log("error", error.value);

    loading.value = false;
  }
};

// 监听URL变化重新加载
watch(
  () => props.pdfUrl,
  () => {
    if (props.pdfUrl) {
      loadPdf();
    }
  },
);
// 复用默认工具栏按钮样式
const renderCustomToolbar = ({ nodes }: ImageRenderToolbarProps) => {
  return [
    nodes.rotateCounterclockwise,
    nodes.rotateClockwise,
    nodes.resizeToOriginalSize,
    nodes.zoomOut,
    nodes.zoomIn,
    h(
      NButton,
      {
        text: true,
        style: { marginLeft: "8px", color: "#fff" }, // 与默认间距对齐
        onClick: downloadImg,
      },
      {
        default: () =>
          h(
            NIcon,
            { size: "24" }, // 关键：显式设置图标大小
            { default: () => h(DownloadOutline) },
          ),
      },
    ),
    nodes.close,
  ];
};
const downloadImg = () => {
  if (!pdfImage.value) {
    console.error("没有可下载的图片数据");
    return;
  }

  // 1. 提取 Base64 数据部分
  const base64Data = pdfImage.value.split(",")[1]; // 移除 data:image/png;base64, 前缀

  // 2. 解码 Base64 为二进制数据
  const byteString = atob(base64Data);
  const arrayBuffer = new ArrayBuffer(byteString.length);
  const int8Array = new Uint8Array(arrayBuffer);

  for (let i = 0; i < byteString.length; i++) {
    int8Array[i] = byteString.charCodeAt(i);
  }

  // 3. 创建 Blob 对象
  const blob = new Blob([int8Array], { type: "image/png" });

  // 4. 生成下载链接
  const url = URL.createObjectURL(blob);

  // 5. 创建 <a> 标签并触发下载
  const link = document.createElement("a");
  link.href = url;
  link.download = "处方图片.png"; // 指定下载文件名
  document.body.appendChild(link);
  link.click();

  // 6. 清理资源
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};
onMounted(() => {
  if (props.pdfUrl) {
    loadPdf();
  }
});
</script>

<style lang="less" scoped>
.pdf-container {
  width: 100%;
  .error {
    .error-text {
      color: red;
    }
  }
}
</style>
