
import { ref } from "vue";
import { useMessages } from "@/hooks";
import { getSystemPointEnable } from "@/services/api";

export default function useTriggerPointsShop() {
    const { createMessageError } = useMessages();
    const isPointEnable = ref(false);

    /** 获取是否启用积分功能 */
    async function getPointsEnabled() {
        try {
          const { value } = await getSystemPointEnable();
          isPointEnable.value = value === 'true';
        } catch (error) {
          createMessageError('获取系统配置失败：' + error);
        }
    }

    return {
        isPointEnable,
        getPointsEnabled,
    }
}