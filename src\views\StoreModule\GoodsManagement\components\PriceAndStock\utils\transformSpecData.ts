import type { SpecList } from '../types';

/**
 * 将扁平的规格属性列表转换为按level分组的结构
 * @param flatList 扁平的规格属性列表
 * @returns 按level分组的规格列表
 */
export function transformSpecData(flatList: Array<{
  attributeName: string;
  attributeValue: string;
  id: number;
  level: number;
}>): SpecList[] {
  // 使用Map按level分组
  const groupedMap = new Map<number, {
    attributeName: string;
    specValue: Array<{
      attributeValue: string;
      id: string;
    }>;
  }>();
  
  // 遍历扁平列表进行分组
  flatList.forEach(item => {
    if (!groupedMap.has(item.level)) {
      groupedMap.set(item.level, {
        attributeName: item.attributeName,
        specValue: []
      });
    }
    
    groupedMap.get(item.level)?.specValue.push({
      attributeValue: item.attributeValue,
      id: item.id.toString()
    });
  });
  
  // 转换为目标格式，按level排序
  const result = Array.from(groupedMap.entries())
    .sort(([a], [b]) => a - b)
    .map(([level, group]) => group);
  
  return result;
}

/** 将分组的规格属性结构 转换成扁平的规格属性数组 扁平数组中的level对应分组结构中的索引值+1 */
export function transformFlatSpecData(groupedList: SpecList[],productId:string): Array<{
  attributeName: string;
  attributeValue: string;
  id: number;
  level: number;
}>
{
  const result = [];
  groupedList.forEach((group, index) => {
    group.specValue.forEach(spec => {
      result.push({
        attributeName: group.attributeName,
        attributeValue: spec.attributeValue,
        id: spec.id,
        level: index + 1,
        productId
      });
    });
  });
  
  return result;
}


