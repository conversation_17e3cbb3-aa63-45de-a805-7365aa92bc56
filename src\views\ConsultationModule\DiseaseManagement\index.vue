<template>
  <div class="wrapper inner-page-height">
      <FormLayout
        :isLoading="isLoading"
        :tableData="tableData"
        :tableColumns="tableColumns"
        :pagination="paginationRef"
        @paginationChange="paginationChange"
        :isNeedCollapse="false"
        :isDisplayIndex="false"
        :isTableSelection="false"
      >
        <!-- 表单 -->
        <template #searchForm>
          <n-form
            ref="formRef"
            label-placement="left"
            label-width="auto"
            :show-feedback="false"
            require-mark-placement="right-hanging"
            size="small"
            :style="{ width: '100%' }"
          >
            <n-form-item :span="12" label="病种名称" path="">
              <j-search-input
                v-model:value.trim="model.searchCondition"
                placeholder="请输入病种名称"
                @search="handlerSearch"
              />
            </n-form-item>
          </n-form>
        </template>
    
        <template #tableHeaderBtn>
          <n-button @click="refresh" :loading="isLoading" class="store-button">刷 新</n-button>
          <JAddButton  type="primary" v-if="hasDiseaseManagementAddAuth" @click="handleFn(true)">新增</JAddButton>
        </template>
      </FormLayout>
      <DiseaseSetting v-model:show="isShow" :Add_active="isAddActive" :row="row" @refresh="refresh" />
  </div>
</template>

<script setup lang="tsx">
import { onMounted, ref } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { diseaseList, deleteDisease } from "@/services/api";
import { useMessages } from '@/hooks';
import JImage from "@/components/JImage/index.vue";
import { hasDiseaseManagementAddAuth, hasDiseaseManagementEditAuth, hasDiseaseManagementDeleteAuth } from "./authList";
import DiseaseSetting from "./components/diseaseSetting.vue";
import Popconfirm from "@/components/Popconfirm/index.vue";

const { createMessageSuccess, createMessageError } = useMessages();

/** 表格hook */
const {
  isAddLoading,
  isEditLoading,
  isLoading,
  tableData,
  paginationRef,
  pageTableData,
  deleteTableData,
  editTableData,
  addTableData,
  refreshTableData,
  paginationChange,
} = useTableDefault({
  pageDataRequest: diseaseList,
});

/* 表格列表项 */
const tableColumns = ref([
  {
    title: "病种名称",
    key: "name",
    width: 200,
    align: "left",
  },
  {
    title: "病种编码",
    key: "code",
    width: 150,
    align: "left",
  },
  {
    title: "操作",
    key: "action",
    width: 120,
    fixed: "right",
    align: "left",
    render: row => {
      return (
        <n-space style="padding: 5px 0;">
          {
            hasDiseaseManagementEditAuth ? <n-button text type="primary" onClick={() => handleFn(false, row)}>编辑</n-button> : null
          }
          {
            hasDiseaseManagementDeleteAuth ? <Popconfirm 
              onHandleClick={() =>deleteFn(row.id)} 
              loading={deleteLoading.value} 
              buttonContent ={'删除'} 
              type={'error'} 
              promptContent={'确认删除该病种吗？'}/> : null
          }
        </n-space>
      );
    },
  },
]);

const isShow = ref(false);
const isAddActive = ref(true);
const row = ref(null);

const handleFn = (isAdd:boolean, rowData?:any) => {
  isShow.value = true;
  isAddActive.value = isAdd;
  row.value = rowData;
};

const deleteLoading = ref(false);
const deleteFn = (id:string) => {
  deleteLoading.value = true;
  deleteDisease({id}).then(res => {
    createMessageSuccess('删除成功');
    refresh();
  }).catch(err=>{
    createMessageError(err.message);
  }).finally(() => {
    deleteLoading.value = false;
  });
};

/** 参数 */
const model = ref({
  searchCondition: ''
});

/** 获取参数 */
const getParams = () => {
  const { searchCondition } = model.value;
  return {
    searchCondition, 
  };
};

/** 搜索 */
const handlerSearch = () => {
  tableSearch();
};

/** 表格刷新 */
function refresh(){
  tableSearch();
}

/* 表格搜索 */
const tableSearch = () => {
  pageTableData(getParams(), paginationRef.value);
};

/** 组件挂载 */
onMounted(() => {
  tableSearch();
});
</script>

<style lang="less" scoped>
@import "@/styles/defaultVar.less";
</style>
