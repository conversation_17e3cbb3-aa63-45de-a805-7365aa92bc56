<template>
    <div class="table-sorter-wrapper">
        <n-icon size="10" :color="props.order === 'ascend'?SystemSetting.primaryColor:'#d7d7f6'">
            <UpIcon />
        </n-icon>
        <n-icon size="10" :color="props.order === 'descend'?SystemSetting.primaryColor:'#d7d7f6'">
            <DownIcon />
        </n-icon>
    </div>

</template>
<script setup lang="ts">
import {CaretUpOutline as UpIcon, CaretDownOutline as DownIcon} from "@vicons/ionicons5";
import { SystemSetting } from "@/settings/systemSetting";
const props = defineProps<{
    order:'ascend' | 'descend' | false
}>()
</script>
<style scoped lang="less">
.table-sorter-wrapper{
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

</style>