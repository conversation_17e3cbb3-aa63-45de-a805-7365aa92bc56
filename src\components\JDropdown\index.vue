<template>
    <!-- 右键菜单 -->
    <n-dropdown
      trigger="manual"
      placement="bottom-start"
      :show="props.show"
      :options="props.options"
      :x="props.x"
      :y="props.y"
      @select="handleSelect"
      @clickoutside="onClickoutside"
    />
</template>

<script lang="ts" setup>
import { ref } from "vue";
import type { DropdownOption, DropdownGroupOption, DropdownDividerOption, DropdownRenderOption } from "naive-ui";

defineOptions({ name: 'JDropdown' });

/** props */
const props = withDefaults(defineProps<{
    x: number; // 坐标
    y: number; // 坐标
    show: boolean; // 菜单显示
    options: Array<DropdownOption | DropdownGroupOption | DropdownDividerOption | DropdownRenderOption>;
    targetTag: any;
}>(), {
    x: 0,
    y: 0,
    options: () => [],
});

/** emits */
const emits = defineEmits<{
    (e: 'select', key: string | number, option: DropdownOption, targetTag: any): void;
    (e: 'update:show', value: boolean): void;
    (e: 'clickoutside'): void;
}>();

/** select 选中时触发的回调函数 */
const handleSelect = (key: string | number, option: DropdownOption) => {
    emits('select', key, option, props.targetTag);
};

/** 取消 */
function onClickoutside() {
    emits('update:show', false);
    emits('clickoutside');
}
</script>


<style lang="less" scoped>

</style>