<template>
  <div class="inner-page-height">
    <div class="layout">
      <!--介绍 -->
      <NCard class="header" size="small" :bordered="false">
        <div class="header_title">分账规则设置</div>
        商户可针对每一个分账业务进行配置，订单支付成功后，系统将根据配置内容生成明细账单（可在分账单管理界面查看）。
        目前仅支持付款方式是在线支付且支付渠道是富友或中金的订单使用线上分账功能，整张订单分账比例不能大于80%。
      </NCard>

      <!-- 主内容 -->
      <main class="content">
        <div class="form_wrapper">
          <StoreTitle title="给社群经销商分账" style="margin-bottom: 12px;" />
          <NSpin :show="loading">
            <NForm
              ref="formRef"
              :model="model"
              :rules="rules"
              label-placement="left"
              label-align="left"
              :label-width="150"
              require-mark-placement="right-hanging"
              size="small"
            >
              <NFormItem label="分账规则">
                <NRadioGroup
                  v-model:value="model[SubAccountPayeeRuleSetting.subAccountPayeeRule]"
                  name="radiogroup"
                  :on-update:value="radioHandleUpdate"
                >
                  <NFlex vertical :size="4">
                    <NRadio :value="SubAccountPayeeRule.NO_SUB_ACCOUNT" :key="SubAccountPayeeRule.NO_SUB_ACCOUNT">
                      <span>不分账</span>
                    </NRadio>
                    <div style="display: flex;  align-items: center;">
                      <NRadio
                        :value="SubAccountPayeeRule.SUB_ACCOUNT_BY_GOODS"
                        :key="SubAccountPayeeRule.SUB_ACCOUNT_BY_GOODS"
                      >
                        <span>
                          按商品分账，分账金额 = 订单中每个商品分账的总额，每个商品分账的金额 =（该商品总额 -
                          该商品总成本价）* 该商品经销商分账比例
                        </span>
                      </NRadio>
                      <HelpPopover helpEntry="按商品分账" size="18" />
                    </div>
                    <div style="display: flex;align-items: center;">
                      <NRadio
                        :value="SubAccountPayeeRule.SUB_ACCOUNT_BY_ORDER"
                        :key="SubAccountPayeeRule.SUB_ACCOUNT_BY_ORDER"
                      >
                        <span>
                          按订单分账，分账金额 = 【订单总额 - 商品总成本 - 用户支付的运费 - 订单线下核算成本 】*
                          分账比例
                        </span>
                      </NRadio>
                      <HelpPopover helpEntry="按订单分账" size="18" />
                    </div>
                  </NFlex>
                </NRadioGroup>
              </NFormItem>
              <NFormItem
                label="分账比例"
                span="12"
                :path="SubAccountPayeeRuleSetting.subAccountPayeeRatio"
                :required="model[SubAccountPayeeRuleSetting.subAccountPayeeRule] === SubAccountPayeeRule.SUB_ACCOUNT_BY_ORDER"
              >
                <NFlex align="center" :size="2">
                  <NInputNumber
                    v-model:value="model[SubAccountPayeeRuleSetting.subAccountPayeeRatio]"
                    :disabled="model[SubAccountPayeeRuleSetting.subAccountPayeeRule] !== SubAccountPayeeRule.SUB_ACCOUNT_BY_ORDER"
                    placeholder="请输入"
                    :style="{width: '120px'}"
                    :min="1"
                    :max="80"
                    :show-button="false"
                  />
                  <span class="label-text">% （计算方式选择按订单分账时必填）</span>
                  <HelpPopover helpEntry="分账比例" size="18" />
                </NFlex>
              </NFormItem>
              <NFormItem
                label="分账单结算时间点"
                span="12"
                :path="SubAccountPayeeRuleSetting.subAccountPayeeSettlementTime"
              >
                <NFlex align="center" :size="2">
                  <span class="label-text">订单状态变更为已完成后的第</span>
                  <NInputNumber
                    v-model:value="model[SubAccountPayeeRuleSetting.subAccountPayeeSettlementTime]"
                    placeholder="请输入"
                    :style="{width: '120px'}"
                    :min="1"
                    :show-button="false"
                  />
                  <span class="label-text">天上午8点</span>
                  <HelpPopover helpEntry="分账单结算时间点" size="18" />
                </NFlex>
              </NFormItem>
            </NForm>
          </NSpin>
        </div>
        <!-- 底部 -->
        <footer class="footer">
          <NButton
            type="primary"
            style="width: 100px;"
            @click="handleUpdate"
            :loading="loading"
            v-if="hasSubAccountPayeeRuleSettingSaveAuth"
          >
            保存
          </NButton>
        </footer>
      </main>
    </div>
  </div>
</template>

<script setup lang="tsx" name="SubAccountPayeeRuleSetting">
import { ref, onMounted } from "vue";
import { SubAccountPayeeRule, SubAccountPayeeRuleSetting } from "./type";
import type {  FormItemRule } from "naive-ui";
import { isNullOrUnDef } from "@/utils";
import { subAccountPayeeRuleSettingGet, subAccountPayeeRuleSettingUpdate } from "@/services/api";
import { useMessages } from "@/hooks";
const { createMessageError, createMessageSuccess } = useMessages();
import { hasSubAccountPayeeRuleSettingSaveAuth } from "@/views/StoreModule/Finance/authList";

onMounted(() => {
    getSubAccountPayeeRuleSetting();
})

/** 获取分账入账方规则设置 */
const getSubAccountPayeeRuleSetting = () => {
  loading.value = true;
  subAccountPayeeRuleSettingGet().then((res) => {
    settingData.value = res.records;
    res.records.forEach(item=>{
      model.value[item.key] = Number(item.value);
    })
  }).catch((error)=>{
    createMessageError(`获取分账入账方规则设置失败:${error}`);
  }).finally(()=>{
    loading.value = false;
  });
};

/** 设置数据 */
const settingData = ref([]);
const loading = ref(false);

const formRef = ref(null);
const handleUpdate = () => {
  formRef.value.validate().then(() => {
    loading.value = true;
    settingData.value.forEach(item=>{
        item.value = model.value[item.key];
    })
    subAccountPayeeRuleSettingUpdate({data:settingData.value}).then(()=>{
        createMessageSuccess("修改成功");
    }).catch((error)=>{
        createMessageError(`修改分账规则设置失败:${error}`);
        getSubAccountPayeeRuleSetting();
    }).finally(()=>{
        loading.value = false;
    });
  });
};

const model = ref({
  [SubAccountPayeeRuleSetting.subAccountPayeeRule]: SubAccountPayeeRule.NO_SUB_ACCOUNT,
  [SubAccountPayeeRuleSetting.subAccountPayeeRatio]: null,
  [SubAccountPayeeRuleSetting.subAccountPayeeSettlementTime]: null,
});

const radioHandleUpdate = (value: SubAccountPayeeRule) => {
  model.value[SubAccountPayeeRuleSetting.subAccountPayeeRule] = value;
  /** 如果不是按订单分账 分账比例设置为0 */
  if(value != SubAccountPayeeRule.SUB_ACCOUNT_BY_ORDER){
    model.value[SubAccountPayeeRuleSetting.subAccountPayeeRatio] = 0;
  }
}

const rules = {
  [SubAccountPayeeRuleSetting.subAccountPayeeRatio]: {
    validator: (rule: FormItemRule, value) => {
      // 选择按订单分账时，分账比例必填
      if(model.value[SubAccountPayeeRuleSetting.subAccountPayeeRule] === SubAccountPayeeRule.SUB_ACCOUNT_BY_ORDER){
        if(isNullOrUnDef(value)){
          return false;
        }
      }
      return true;
    },
    trigger: ['change'],
    message: "请输入分账比例",
  },
  [SubAccountPayeeRuleSetting.subAccountPayeeSettlementTime]: {
    required: true,
    trigger: ["blur", "change"],
    message: "请输入分账单结算时间点",
    validator: (rule: FormItemRule, value)=>{
      return !isNullOrUnDef(value);
    }
  },
};
</script>

<style lang="less" scoped>
/* 布局容器 */
.layout {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  background-color: #fff;
}

/* 头部样式 */
.header {
  height: auto;
  border-bottom: 1px solid #EEEEEE;
  .header_title {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 600;
    font-size: 20px;
    color: #333333;
    line-height: 32px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-bottom: 12px;
  }
}

/* 主内容样式 */
.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding: 12px 16px;
  color: #333;
  font-size: 18px;
  .form_wrapper {
    flex: 1;
    .label-text {
      font-size: 14px;
    }
  }
}

/* 底部样式 */
.footer {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  justify-content: flex-end;
  padding-right: 50px;
  background-color: #fff;
}

/* 横线样式 */
.line {
    padding: 55px 0;
    border-bottom: 2px solid #ccc;
}

/* 文字加粗 */
.textBolded{
    font-size: 18px;
    font-weight: 700;
}

.marginTop{
    margin-top: 15px;
}
</style>
