<template>
  <MenuLayout v-model:activeKey="activeTypeRef" :menuOptions="salesList">
    <component :is="currentPage"/>
  </MenuLayout>
</template>

<script lang="ts" setup>
import {ref, computed} from "vue";
// import { useSvgIcon } from "@/hooks";
import {SalesManagementType, type SalesType} from "@/enums";
/** 相关组件 */
import MenuLayout from "@/components/MenuLayout/index.vue";
import SettingInternal from "./SettingInternal/index.vue";

// const { SvgIconVNode } = useSvgIcon();

const activeTypeRef = ref<SalesType>(SalesManagementType.MEMBERPOINTS);

/** 营销管理 tab */
const salesList = ref([
  {
    label: '自动回复',
    key: SalesManagementType.MEMBERPOINTS,
    // icon: SvgIconVNode({ localIcon: 'Crown', fontSize: 18 }),
    show: true
  },
]);

/** 相关组件 */
const pageMap = {
  [SalesManagementType.MEMBERPOINTS]: SettingInternal,
}

/** 当前页 */
const currentPage = computed(() => pageMap[activeTypeRef.value])
</script>

<style lang="less" scoped></style>
