import { defHttp } from "@/services";

/** 门店管理 */
export const enum ShopReturnLogisticsApi {
  pageList = "/returnLogistics/pageList",
  detail = "/returnLogistics/detail",
  approve = "/processStepInst/approve",
  logisticsInfo = "/returnLogistics/tracesList",
  exportReturnLogistics ="/returnLogistics/export"
}

/**
 * @description 门店退货物流列表
 */
export function getShopReturnLogisticsPageList(params) {
  return defHttp.post({
    url: ShopReturnLogisticsApi.pageList,
    params,
  });
}
/**
 * @description 门店退货物流详情
 */
export function getShopReturnLogisticsDetail(id) {
  return defHttp.get({
    url: ShopReturnLogisticsApi.detail + `?id=${id}`,
  });
}
/**
 * @description 审核
 */
export function approveShopReturnLogistics(params) {
  return defHttp.post({
    url: ShopReturnLogisticsApi.approve,
    params,
  });
}

/**
 * @description 获取门店退货物流信息
 */
export function getShopReturnLogisticsInfo(params) {
  return defHttp.post({
    url: ShopReturnLogisticsApi.logisticsInfo,
    params: {
      data: params,
    },
  });
}
/**
 * @description 导出门店退货物流
 */
export function exportShopReturnLogistics(params) {
  return defHttp.post({
    url: ShopReturnLogisticsApi.exportReturnLogistics,
    requestConfig:{
      responeseType:'stream'
    },
    params
  })
}
