<script setup lang="ts">
import TabsLayout from "@/layout/TabsLayout.vue";
import {ref, watch , toRefs } from "vue";
import VideoManagementTab from './components/VideoManagementTab.vue'
import {VideoTypeEnum} from '@/enums'

const tabNameRef = ref<VideoTypeEnum>(VideoTypeEnum.AuditResolve);

const props = defineProps<{
  tabValue: VideoTypeEnum,
}>();

watch(()=>props.tabValue,(val)=>{
  if (val) {
    tabNameRef.value = val
  }
},{immediate:true})

const tableDataUpdate = ref()

const tabsData = ref([
  {
    label: "审核通过",
    key: VideoTypeEnum.AuditResolve,
  },
]);


</script>

<template >
  <n-layout>
    <n-layout-content id="VideoManagement">
      <TabsLayout  v-model:value="tabNameRef" :tabsData="(tabsData as any)"  :onlyTabs="true" class="tabsLayout">
        <VideoManagementTab ref="tableDataUpdate" :tabNameRef="tabNameRef" />
      </TabsLayout>
    </n-layout-content>
  </n-layout>
</template>

<style lang="less" scoped>
// @import "@/styles/default.less";
</style>
