import BlankLayout from "@/layout/BlankLayout.vue";
import { RoutesName } from "@/enums/routes";
import type { RouteLocation } from "vue-router";

export const Default = {
  [RoutesName.Doctor]: {
    path: "doctor",
    component: BlankLayout,
    meta: {
      title: "问诊",
    },
  },
  [RoutesName.MedicalInquiryForm]: {
    path: "medicalInquiryForm",
    component: () => import("@/views/ConsultationModule/MedicalInquiryForm/index.vue"),
    meta: {
      title: "问诊单",
      icon: "prescription-management",
    },
  },
  [RoutesName.MedicalInquiryPrescription]: {
    path: "medicalInquiryPrescription",
    component: () => import("@/views/StoreModule/MedicalInquiryPrescription/index.vue"),
    meta: {
      title: "问诊处方",
      icon: "prescription-management",
    },
  },
  [RoutesName.PrescriptionManagement]: {
    path: "prescriptionManagement",
    component: () => import("@/views/StoreModule/PrescriptionManagement/index.vue"),
    meta: {
      title: "购药处方",
      icon: "prescription-management",
    },
  },
  [RoutesName.DoctorManagement]: {
    path: "doctorManagement",
    component: () => import("@/views/ConsultationModule/DoctorManagement/index.vue"),
    meta: {
      title: "医生管理",
      icon: "doctor-management",
    },
  },
  [RoutesName.PharmacistManagement]: {
    path: "pharmacistManagement",
    component: () => import("@/views/ConsultationModule/PharmacistManagement/index.vue"),
    meta: {
      title: "药师管理",
      icon: "pharmacist-management",
    },
  },
  [RoutesName.OrganizationManagement]: {
    path: "organizationManagement",
    component: () => import("@/views/ConsultationModule/OrganizationManagement/index.vue"),
    meta: {
      title: "机构管理",
      icon: "organization-management",
    },
  },
  [RoutesName.OfficesConfig]: {
    path: "officesConfig",
    component: () => import("@/views/ConsultationModule/OfficesConfig/index.vue"),
    meta: {
      title: "科室配置",
      icon: "offices-config",
    },
  },
  [RoutesName.DiseaseManagement]: {
    path: "diseaseManagement",
    component: () => import("@/views/ConsultationModule/DiseaseManagement/index.vue"),
    meta: {
      title: "病种管理",
      icon: "disease-management",
    },
  },
  [RoutesName.BasicsSettings]: {
    path: "basicsSettings",
    component: () => import("@/views/ConsultationModule/BasicsSettings/index.vue"),
    meta: {
      title: "基础设置",
      icon: "basics-settings",
    },
  },
  [RoutesName.AssistantDoctorManagement]: {
    path: "assistantDoctorManagement",
    component: () => import("@/views/ConsultationModule/AssistantDoctorManagement/index.vue"),
    meta: {
      title: "医助管理",
      icon: "prescription-management",
    },
  },
};