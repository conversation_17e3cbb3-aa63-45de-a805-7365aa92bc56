<template>
    <JModal
        v-model:show="show"
        width="1400"
        title="添加分销商品"
        :positiveText="'完成'"
        :negativeText=" '' "
        @after-leave="closeModal"
            @positive-click="_save"
            :positiveButtonProps="{
                loading: isAddDistributionLoading
            }"
      >
       <n-layout has-sider>
         <n-layout-sider bordered content-style="padding: 24px;">
            <n-spin :show="isGetLoading" size="small" style="height: 100%;">
                  <n-scrollbar style="height: 600px;">
                    <ClassificationTree
                      v-model:value="model.selectedValue"
                      :tree-data="treeData"
                      @update:selected-keys="handleUpdateSelectKeys"
                      :selected-keys="selectedKeys"
                      :defaultExpandKeys="defaultExpandKeys"
                    />
                  </n-scrollbar>
            </n-spin>
         </n-layout-sider>
         <n-layout>
            <div style="height: 700px;">
              <FormLayout
                :isLoading="isLoading"
                :tableData="tableData"
                :tableColumns="tableColumns"
                :pagination="paginationRef"
                :isTableSelection="false"
                @paginationChange="paginationChange"
                :isDisplayIndex="false"
              >
               <template #searchForm>
                 <n-form
                   ref="formRef"
                   label-placement="left"
                   label-width="auto"
                   :show-feedback="false"
                   require-mark-placement="right-hanging"
                   size="small"
                   :style="{ width: '100%' }"
                 >
                  <n-grid cols="4 m:12 l:18 xl:24" :x-gap="32" responsive="screen">
                     <n-gi :span="6">
                         <n-form-item label="商品名称" path="productName">
                           <n-input-group>
                             <JSearchInput
                               v-model:value="distributionModal.productName"
                               placeholder="请输入商品名称"
                               @search="formSearch"
                               :width="240"
                             />
                           </n-input-group>
                         </n-form-item>
                     </n-gi>
<!--                     <n-gi :span="6">-->
<!--                         <n-form-item label="商品编号" path="productCode">-->
<!--                           <n-input-group>-->
<!--                             <JSearchInput-->
<!--                               v-model:value="distributionModal.productCode"-->
<!--                               placeholder="请输入商品编号"-->
<!--                               @search="formSearch"-->
<!--                               :width="240"-->
<!--                             />-->
<!--                           </n-input-group>-->
<!--                         </n-form-item>-->
<!--                     </n-gi>-->
                     <n-gi :span="6">
                         <n-form-item label="是否添加" >
                           <n-select v-model:value="distributionModal.isAddDistributeProduct" :options="options"/>
                         </n-form-item>
                     </n-gi>
                    </n-grid>
                   </n-form>
               </template>
              </FormLayout>
           </div>
         </n-layout>
        </n-layout>
      </JModal>
    </template>
    
<script setup lang="tsx" name="GroupConfigurationShow">
import FormLayout from "@/layout/FormLayout.vue";
import { ref,onMounted,watch } from "vue";
import { useMessages } from "@/hooks";
import type { TreeOption } from 'naive-ui';
import ClassificationTree from "@/views/StoreModule/GoodsManagement/components/ClassificationTree.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { distributeProduct, distributeProductAdd } from "@/services/api";
import TablePreview from "@/components/TablePreview/index.vue";
import { IsPublishStatusLabels } from "@/constants";
import { GoodsCategoryType } from "@/enums";
import useGetDistributeClassify from "@/views/Distribution/DistributorManagement/hooks/useGetDistributeClassify";
export interface DistributorModalProps {
  type?: any; // 弹窗模式 --> 默认add
  distributorId?: any;
  refreshTable?: () => void; // 刷新表格数据
}

/** 商品分类 hook */
const {
  model,
  searchValue,
  getGoodsClassificationData,
  // handleScroll,
  treeData,
  selectedKeys,
  defaultExpandKeys,
  refresh,
  isGetLoading
} = useGetDistributeClassify();

/** 表格方法Hook */
const {
  isLoading,
  tableData,
  pageTableData,
  paginationRef,
  paginationChange,
} = useTableDefault({
  pageDataRequest: distributeProduct,
});


/** 是否添加select */
const options = [
    {
        label: '请选择',
        value: null,
    },
    {
        label: '已添加',
        value: 1,
    },
    {
        
        label: '未添加',
        value: 0,
    }
]

/* 表格列表项 */
const tableColumns = ref([
    {
        title: "图片",
        key: "img",
        width: 150,
        align: "left",
        render: row => {
          if (row?.productImages?.length > 0) {
            let paths = row?.productImages.map(item => item.path);
            return <TablePreview src={paths}></TablePreview>;
          }
          return "-";
        },
    },
    {
        title: "商品",
        key: "productName",
        width: 180,
        align: "left",
        render: (row) => {
          // 普通商品
          if (row.type == GoodsCategoryType.GENERAL) {
            let title = `${row.frontName ?? ""}`;
            return <table-tooltip row={row} nameKey="name" title={title} idKey="productId" />;
          }
          // 疗法
          if (row.type == GoodsCategoryType.THERAPY) {
            let title = `${row.frontName ?? ""}`;
            return <table-tooltip row={row} nameKey="name" title={title} idKey="productId" />;
          }
          // 药品
          if (row.type == GoodsCategoryType.DRUG) {
            let name = row.productName ?? "";
            let title = `[${row.frontName ?? ""}] ${row.productName ?? ""} ${name}`;
            return <table-tooltip row={row} nameKey="name" title={title} idKey="productId" />;
          }
        }
    },
    {
      title: "库存",
      key: "availStocks",
      width: 150,
      align: "left"
    },
    {
      title: "上架状态",
      key: "isPublish",
      width: 150,
      align: "left",
      render: (row) => {
        return IsPublishStatusLabels[row.isPublish] ? IsPublishStatusLabels[row.isPublish] : '-'
      }
    },
    {
        title: "操作",
        key: "action",
        width: 100,
        fixed: "right",
        align: "left",
        render: (row) => {
            return (
                <n-button
                  text
                  type="primary"
                  disabled={row.isAdd}
                  onClick={() => {addDistribution(row.productId)}}
                >
                  {row.isAdd ?'已添加':'添加'}
                </n-button>
            )
        }
    },
]);

/* 表单参数初始化 */
const initParams = {
  searchValue:'',
  productName:'',
  productCode:'',
  isAddDistributeProduct:null,
  cateId:'',
  distributorId:'',
  type:null,
}
const distributionModal = ref({ ...initParams });

/* 提示信息 */
const message = useMessages();

/* 模态框显隐状态 */
const show = ref(false);

/* 父组件传过来的参数 */
const parameter = ref<DistributorModalProps>({});
const acceptParams = async (params) => {
  parameter.value = params
  distributionModal.value.distributorId = params?.distributorId ?? null;
  searchValue.value.distributeId = params?.distributorId ?? null;
  getGoodsClassificationData();
  formSearch();
  show.value = true;
};

/* 清空表单 */
const formDataReset = () => {
    distributionModal.value = { ...initParams };
    tableData.value.length = 0
};

/* 关闭弹窗 */
const closeModal = () => {
  formDataReset();
};
  
  
/* 确认--保存 */
const isAddDistributionLoading = ref(false);
const _save = async() => {
  show.value =false
  closeModal()
};

/** 树形节点选中项发生变化时的回调函数 */
const handleUpdateSelectKeys = (keys: Array<string | number>, option: Array<TreeOption & Partial<ApiStoreModule.GoodsClassification> | null>, meta: { node: TreeOption | null, action: 'select' | 'unselect' }) => {
    console.log("触发");
    
  if (keys.length !== 0) {
    const firstOption = option[0];
    selectedKeys.value = keys;
    model.value.cateId = firstOption?.id ?? null;
    distributionModal.value.cateId = firstOption?.id ?? null;
    model.value.type = model.value.cateId ?? firstOption?.type ?? null;
    distributionModal.value.type = model.value.cateId ? null : firstOption?.type;
    // 加载状态
    formSearch()
    // isGetLoading.value = true;
  }
  // setTimeout(()=>{
  //   isGetLoading.value = false;
  // })
};

/** 获取参数 */
const getParams = () => {
  const { searchValue, productName, productCode, isAddDistributeProduct, cateId, type, distributorId} = distributionModal.value;
  return {
    searchValue,
    productName,
    productCode,
    distributorId,
    isAddDistributeProduct,
    cateId,
    type
  };
};

/** 搜索 */
const formSearch = () =>{
  pageTableData(getParams(), paginationRef.value);
}

const addDistribution = async(id) => {
  isLoading.value = true
  try{
    await distributeProductAdd({
      data:{
        type:1,
        productId:id,
        distributorId:parameter.value.distributorId,
        storeId:null
      }
    })
    message.createMessageSuccess('添加分销商品成功')
    isLoading.value = false
    formSearch()
    parameter.value.refreshTable()
  }catch(err){
    message.createMessageError('添加分销商品成功失败：' + err)
  }
};
/** 监听 */
watch(() => [distributionModal.value.isAddDistributeProduct], (newVal,oldVal) => {
  if (newVal[0] !== oldVal[0]) {
    formSearch();
  }
});
/** 组件挂载 */
onMounted(() => {
  searchValue.value.isAdd = 1
  model.value.pharmaceuticalsCommodities = true
});

defineExpose({
  acceptParams,
});

 </script>
   
<style scoped lang="less">
</style>
        