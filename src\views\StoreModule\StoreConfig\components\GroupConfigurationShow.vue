<template>
  <JModal
    v-model:show="show"
    width="680"
    :title="Add_active?'新增地址':'编辑地址'"
    @after-leave="closeModal"
		@positive-click="_save"
		:positiveButtonProps="{
			loading: isLoading
		}"
  >
    <n-form
      ref="formRef"
      :rules="rules"
      :model="model"
      label-width="auto"
      label-placement="left"
      require-mark-placement="right-hanging"
      :style="{
          width: '100%',
        }"
    >
      <n-grid :cols="8" :x-gap="24">
        <n-form-item-gi :span="24" label="联系人姓名" path="name" required>
          <n-input
            v-model:value="model.name"
            style="width: 100%"
            :show-button="false"
            maxlength="20"
            :placeholder="'填写联系人姓名'"
            clearable
          />
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="联系电话" path="mobile" required>
          <n-input
            v-model:value="model.mobile"
            style="width: 100%"
            :show-button="false"
            maxlength="20"
            :placeholder="'填写联系电话号'"
            clearable
          />
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="地区" path="addressOptions" required>
          <JAreaSelect v-model:value="model.addressOptions" />
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="详细地址" path="address">
          <n-input
            v-model:value="model.address"
            style="width: 100%"
            :show-button="false"
            maxlength="100"
            placeholder="填写详细地址"
          />
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="使用场景" path="type" required>
          <n-radio-group v-model:value="model.type" name="active">
            <n-space>
              <n-radio v-for="song in UseRadio" :key="song.value" :value="song.value">
                {{ song.label }}
              </n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="启用" path="isActivated" required>
          <n-radio-group v-model:value="model.isActivated" name="active">
            <n-space>
              <n-radio v-for="song in radio" :key="song.value" :value="song.value">
                {{ song.label }}
              </n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="备注">
          <n-input v-model:value="model.remark" type="textarea" maxlength="60" placeholder="请填写备注" show-count @input="(value)=>model.remark=value.trim()"/>
        </n-form-item-gi>
        
      </n-grid>
    </n-form>
  </JModal>
</template>

<script setup lang="ts" name="GroupConfigurationShow">
import { ref } from "vue";
import { useMessages } from "@/hooks";
import {
  addressData
} from "@/services/api";
import JAreaSelect from "@/components/JAreaSelect/index.vue";

export interface AddCompanyModalProps {
  api?: (params: any) => Promise<any>;// 新增保存Api
  refreshTable?: () => void; // 刷新表格数据
  row?: Partial<ApiStoreModule.ReturnObj>;
  // id?:string
}
const Add_active = ref<boolean>(true);

/** 初始化参数 */
const initParams = {
  id:null,
  name:'',
  mobile:'',
  address:'',
  type:1,
  isActivated:1,
  remark:'',
  addressOptions: {
    provinceId: null,
    province: null,
    cityId: null,
    cityName: null,
    areaId: null,
    area: null,
    remark:null,
  }
};
const model = ref({ ...initParams });

/* 提示信息 */
const message = useMessages();
/* 模态框显隐状态 */
const show = ref(false);
/* 父组件传过来的参数 */
const parameter = ref<AddCompanyModalProps>({});
const acceptParams = async (params: AddCompanyModalProps) => {
  parameter.value = params;
  if (params.row.id) {
    const { row } = params;
    Add_active.value = false;
    
    Object.assign(model.value, {
      id: row.id,
      name: row.name ?? '',
      mobile: row.mobile ?? '',
      type: row.type ?? 1,
      isActivated: row.isActivated ?? 1,
      address: row.address ?? '',
      remark:row.remark ?? '',
      addressOptions: {
        provinceId: row.provinceId ?? '1',
        province: row.province ?? '',
        cityId: row.cityId ?? '',
        cityName: row.cityName ?? '',
        areaId: row.areaId ?? '',
        area: row.area ?? '',
      }
    });
  }
  show.value = true;
};
const radio = [
  {
    value:0,
    label:"不启用"
  },
  {
    value:1,
    label:"启用"
  }
]
const UseRadio = [
  {
    value:1,
    label:"退货地址"
  },
  {
    value:3,
    label:"发货地址"
  },
]

const phoneReg = /\d{11,20}$/

/* 表单规则 */
const rules = {
  mobile:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入正确电话号码",
    validator: ()=>{
      return model.value.mobile != '' && (model.value.mobile != null && phoneReg.test(model.value.mobile));
    }
  },
  name:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入姓名",
    validator: ()=>{
      console.log(model.value.name);
      return model.value.name != '';
    }
  },
  address:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入详细地址",
    validator: ()=>{
      console.log(model.value.address != '');
      return model.value.address != '';
    }
  },
  addressOptions:{
    required: true,
    trigger: ["blur", "change"],
    message: "请选择地区",
    validator: ()=>{
      return model.value.addressOptions.provinceId !== null && model.value.addressOptions.cityId !== null  && model.value.addressOptions.areaId !== null;
    }
  },
};

/* 表单实例 */
const formRef = ref(null);

/* 清空表单 */
const formDataReset = () => {
  model.value = { ...initParams };
  Add_active.value = true
  parameter.value = {}
};

/* 关闭弹窗 */
const closeModal = () => {
  formDataReset();
};

/** 获取参数 */
function getParams() {
  const { id, name, mobile, address, addressOptions, type, isActivated, remark } = model.value;
  const { province, provinceId, cityId, cityName, area, areaId } = addressOptions;

  return {
    id, 
    name, 
    mobile,
    province, 
    provinceId, 
    cityId, 
    cityName, 
    area, 
    areaId,
    address, 
    type, 
    isActivated,
    remark
  }
}

/* 确认--保存 */
const isLoading = ref(false);
const _save = (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      try {
        isLoading.value = true;
        await parameter.value.api({
            "data": getParams(),
          }).then(e=>{
          if (Add_active.value) {
            message.createMessageSuccess(`添加地址配置成功`);
          }else {
            message.createMessageSuccess(`修改地址配置成功`);
          }
          // 弹窗取消
          show.value = false;
        });
      } catch (e) {
        if (Add_active.value) {
          message.createMessageError(`新增地址配置失败： ${e}`);
        }else {
          message.createMessageError(`修改地址配置失败： ${e}`);
        }
      } finally {
        isLoading.value = false;
        // 刷新表格数据
        parameter.value.refreshTable();
      }
    }
  });
};

defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less"></style>
