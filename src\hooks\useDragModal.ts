const useDragModal = (draggableEl: HTMLElement, childrenSelectors = '.n-card-header') => {
    // 定义一个函数，使元素可拖动，接受一个HTMLElement和一个可选的子元素选择器
    const targetEl = draggableEl.querySelector(childrenSelectors) as HTMLElement;
    // 从draggableEl中查找可拖动的目标元素
    if (!targetEl) {
      return;
      // 如果没有找到目标元素，则退出函数
    }
    targetEl.style.cursor = 'move';
    // 设置目标元素的鼠标样式为可移动
    targetEl.style.userSelect = 'none';
    // 禁止目标元素的文本被选择，以避免拖动时的文本选择行为
    mousedown(targetEl as HTMLElement, draggableEl);
    // 绑定鼠标按下事件处理函数
    mouseup();
    // 绑定鼠标释放事件处理函数
  };
  
  const mousedown = (targetEl: HTMLElement, draggableEl: HTMLElement) => {
    // 定义鼠标按下时的事件处理函数
    targetEl.onmousedown = (event: MouseEvent) => {
      // 当鼠标按下时，设置处理逻辑
      const viewportWidth = window.innerWidth || document.documentElement.clientWidth;
      const viewportHeight = window.innerHeight || document.documentElement.clientHeight;
      // 获取视口的宽度和高度
      const { width, height, x: minMoveX, y: minMoveY } = draggableEl.getBoundingClientRect();
      // 获取元素的宽度、高度和相对视口的位置
      const maxMoveX = viewportWidth - width - minMoveX;
      const maxMoveY = viewportHeight - height - minMoveY;
      // 计算元素可移动的最大X和Y距离
      const { clientX: originX, clientY: originY } = event;
      // 获取鼠标按下时的位置
      const { left, top } = draggableEl.style;
      // 获取元素当前的left和top样式值
      const styleLeft = left?.replace('px', '') ?? 0;
      const styleTop = top.replace('px', '') ?? 0;
      // 将样式值转换为数字
  
      document.onmousemove = (e: MouseEvent) => {
        // 当鼠标移动时，设置处理逻辑
        const { clientX, clientY } = e;
        // 获取鼠标当前位置
        let moveX = clientX - originX;
        let moveY = clientY - originY;
        // 计算鼠标移动的距离
        if (moveX > maxMoveX) {
          moveX = maxMoveX;
        } else if (-moveX > minMoveX) {
          moveX = -minMoveX;
        }
        // 限制X方向的移动范围
        if (moveY > maxMoveY) {
          moveY = maxMoveY;
        } else if (-moveY > minMoveY) {
          moveY = -minMoveY;
        }
        // 限制Y方向的移动范围
        draggableEl.style.cssText += `;left:${+styleLeft + moveX}px;top:${+styleTop + moveY}px`;
        // 更新元素的位置
      };
    };
  };
  
  const mouseup = () => {
    // 定义鼠标释放时的事件处理函数
    document.onmouseup = () => {
      document.onmousemove = null;
      // 当鼠标释放时，取消鼠标移动的事件绑定
    };
  };

  export default useDragModal;