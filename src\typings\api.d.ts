/**
 * @description 商城功能
 */
declare namespace ApiStoreModule {
  /** 医生列表 */
  interface DoctorEntity {
    id: string;
    createTime: string;
    updateTime: string;
    img: string;
    doctorName: string;
    mobile: string;
    doctorCode: string;
    platform: string;
    isActived: 0 | 1;
  }
  /** 商品分类 */
  interface GoodsClassification {
    id: string;
    iconPath: string;
    isShow: 0 | 1;
    sort: number;
    type: 1 | 2 | 3 | 4 | 5 | 9;
    createTime: string;
    updateTime: string;
    name: string;
    parentId?: string;
    isShowList: 0 | 1;
  }
  interface StoreList {
    id: string;
    storeName: string;
    contactName: string;
  }
  /** 商品分页 */
  interface Goods {
    id: string;
    createTime: string;
    name: string;
    frontName: string;
    cateId: string;
    code: string;
    isPres: 0 | 1;
    isPublish: 0 | 1;
    isPoint: 0 | 1;
    cateName: string;
    type: 1 | 2 | 3;
    productSpecDTOList: ProductSpec[];
    productImgDTOList: ProductImg[];
  }

  interface ProductSpec {
    id: string;
    name: string;
    createTime: string;
    productId: string;
    price: number;
    upper: number;
    availStocks: number;
    lockedStocks: number;
    soldQty: number;
    initSaled: number;
    dailyCost: number;
    cycle: number;
    isCashOnDelivery: 0 | 1;
    isDownPayment: 0 | 1;
    downPayment: number;
    isDeleted: 0 | 1;
  }

  interface ProductImg {
    id: string;
    createTime: string;
    productId: string;
    img: string;
    path: string;
    isFirst: 0 | 1;
  }

  /** 药品商品信息 */
  interface DrugGoodsInfo {
    id: string;
    createTime: string;
    updateTime: string;
    name: string;
    frontName: string;
    code: string;
    cateId: string;
    isPublish: number;
    sellingScenario: 0 | 1 | 2;
    desc: string;
    type: number;
    isDistribution: number;
    distributionRatio: number;
    isAllocation: 0|1;
    dealerAllocationRatio: number;
    isHidden: 0|1;
    productImgDTOSList: Array<{
      id: string;
      createTime: string;
      productId: string;
      img: string;
      path: string;
      isFirst: number;
      type: 0 | 1;
    }>;
    productSpecDTOSList: Array<{
      id: string;
      createTime: string;
      productId: string;
      name: string;
      price: number;
      upper: number;
      availStocks: number;
      lockedStocks: number;
      soldQty: number;
      initSaled: number;
      dailyCost: number;
      cycle: number;
      isCashOnDelivery: number;
      isDownPayment: number;
      downPayment: number;
      isDeleted: number;
      sku?: string;
      costPrice: number;
    }>;
    manufacturer: string;
    upperLimit: number;
    isPres: number;
    productInsertsDTO: {
      id: string;
      createTime: string;
      updateTime: string;
      productId: string;
    };
    productPresOptionDTOSList?: Array<any>;
    pointConfigDTOSList?: Array<{
      channel: 0 | 1;
      createBy: string;
      createTime: string;
      id: string;
      costPrice:number;
      isEnabled: 0 | 1;
      points: number;
      source: number;
      sourceId: string;
      updateTime: string;
    }>;
  }

  /** 疗法商品信息 */
  interface TherapyDrugGoodsInfo {
    id: string;
    createTime: string;
    updateTime: string;
    name: string;
    frontName: string;
    code: string;
    cateId: string;
    upperLimit: number;
    isPublish: 0 | 1;
    desc: string;
    tag: string;
    type: 1 | 2;
    sellingScenario: 0 | 1 | 2;
    isAllocation: 0 | 1;
    dealerAllocationRatio: number;
    isDistribution: 0 | 1;
    distributionRatio: number;
    isHidden: 0 | 1
    productImgDTOSList: Array<{
      id: string;
      createTime: string;
      productId: string;
      img: string;
      path: string;
      isFirst: 0 | 1;
      type: 0 | 1;
    }>;
    productSpecDTOSList: Array<{
      id: string;
      createTime: string;
      productId: string;
      price: number;
      upper: number;
      availStocks: number;
      lockedStocks: number;
      soldQty: number;
      initSaled: number;
      dailyCost: number;
      cycle: number;
      isCashOnDelivery: 0 | 1;
      isDownPayment: 0 | 1;
      isDeleted: 0 | 1;
      sku?: string;
      costPrice:number;
    }>;
    therapyDrugItemDTOSList: Array<{
      id: string;
      createTime: string;
      updateTime: string;
      name: string;
      imgPath: string;
      dosage: string;
    }>;
    therapyDrugDetailDTO: {
      id: string;
      createTime: string;
      updateTime: string;
      productId: string;
      uses: string;
      dosage: string;
      appropriateCrowd: string;
      contraindications: string;
      combination: string;
      remark: string;
    };
    therapyDrugItemRelateDTOSList: Array<{
      createTime: string;
      id: string;
      itemId: string;
      productId: string;
      updateTime: string;
    }>;
    pointConfigDTOSList?: Array<{
      channel: 0 | 1;
      createBy: string;
      createTime: string;
      id: string;
      isEnabled: 0 | 1;
      points: number;
      source: number;
      sourceId: string;
      updateTime: string;
    }>;
  }

  /** 医生分类 */
  interface DoctorClassification {
    id: string;
    createTime: string;
    updateTime: string;
    name: string;
    doctorName: string;
    mobile: string;
    doctorCode: string;
    platform: string;
    isActived: string;
  }
  /** 快递公司分类 */
  interface ExpressCompanyClassification {
    id: string;
    name: string;
    code: string;
  }

  /** 商品 */
  interface GoodsClassification {
    id: string;
    frontName: string;
  }

  interface CarouselImg {
    createTime: string;
    id: number;
    imgPath: string;
    img: string;
    isEnable: number;
    position: number;
    productId: string;
    phone: number;
    redirect: number;
    remark: string;
    sortNumber: number;
  }

  interface NavigationConfig {
    id: number
    createTime: string
    updateTime: string
    orderBy: string
    isAsc: boolean
    name: string
    pointPage:number
    iconUncheckedPath: string
    iconCheckedPath: string
    sort: number
    createBy: number
  }
  interface ReturnObj {
    address: string;
    area: string;
    areaId: string;
    cityId: string;
    cityName: string;
    createBy: string;
    createTime: string;
    customerId: string;
    id: string;
    isActivated: number;
    isDefault: number;
    isSnapshot: number;
    mobile: string;
    name: string;
    province: string;
    provinceId: string;
    town: string;
    townId: string;
    type: number;
    updateTime: string;
    remark: string;
  }
}

/**
 * @description 商城功能之客户管理
 */
declare namespace ApiCustomerManagement {
  /** 客户标签分页 */
  interface CustomerTag {
    createTime: string;
    id: string;
    name: string;
    updateTime: string;
  }
}

/**
 * @description 商城功能之营销系统
 */
declare namespace ApiSalesManagement {
  /** 积分商品分类 */
  interface IntegralGoodsClass {
    id: string;
    iconPath: string;
    isShow: 0 | 1;
    sort: number;
    type: 1 | 2 | 3 | 4;
    createTime: string;
    updateTime: string;
    name: string;
  }

  /** 积分筛选 */
  interface PointSift {
    createTime: string;
    id: string;
    maxPoints: number;
    minPoints: number;
    siftName: string;
    sort: number;
    updateTime: string;
  }
}

declare namespace AddressModule {
  /** 地址列表 */
  interface AddressEntity {
    id: string;
    name:string;
    code:string
  }
}

/**
 * @description 商品号管理
 */
declare namespace ItemNumberManagement {
  interface MerchantContent {
    id: string;
    platform: 0 | 1;
    merchantId: string;
    company: string;
    serialNumber: string;
    apiV3Key: string;
    fuiouPayPubKey: string;
    fuiouPayClientPriKey: string;
    comment: string;
    state:number;
    fuiouShareProfitPubKey:string;
    fuiouShareProfitClientPriKey:string;
    wechatSerialNumber:string;
    allocationMerchantId:string;
    publicKeyId:string | number;
    signatureMode:string | number;
    identCode:string | number,
    shopId:string | number,
    appId:string | number,
    character:string | number,
    ciccPayPubKey:string | number,
    ciccShareProfitPubKey:string | number,
    ciccPayClientPriKey:string | number,
    ciccShareProfitClientPriKey:string | number
  }
}


declare namespace BankModule {
  /** 开户行 */
  interface BankEntity {
    id: string;
    name:string;
    code:string;
    no:string
  }
}

/**
 * @description 分销商管理
 */
declare namespace distributorManagement {
  interface distributorContent {
    id: string;
    structureId: string | number,
    structureName:string | number
  }
}

  /** 1.1.5选择医生 */
  interface NewChoiceDoctorClassification {
    id: string;
    doctorName: string;
    mobile: string;
    doctorCode:string;
    institutionName:string;
    isActived:number
  }

  // 药品处方项
  export interface PrescriptionItem {
    id: number;
    productName: string; // 商品通用名
    specName: string; // 规格名称
    routeOfAdministration: number; // 给药方式。0=其他；1=口服；2=外用；3=吸入；4=舌下给药；5=直肠给药；6=静脉注射；7=肌肉注射；8=皮下注射
    routeOfAdministrationOther?: string; // 自定义给药方式，即其他给药方式
    frequencyOfAdministration: number; // 用药频次。0=其他；1=每日一次；2=每日两次；3=每日三次；4=每日四次；5=隔日一次；6=每周一次；7=每周两次；8=每周三次
    frequencyOfAdministrationOther?: string; // 自定义用药频次，即其他用药频次
    dosage: number; // 剂量
    dosageUnits: number; // 剂量单位。1=粒；2=片
    count: number; // 购买数量
    chineseDosageCount?: number; //中药剂数
    medicalAdvice?: string; //药品的医嘱

  }

  // 处方详情主数据
  export interface MedicalInquiryDetailData {
    type: number; //处方类型
    id: number; // 处方ID
    status: number; // 处方状态。0=待审核；1=可使用；2=已使用；3=审核不通过；4=已失效
    diagTime: string; // 医生开处方时间
    expireTime: string; // 处方失效时间
    auditTime?: string; // 药师审核时间
    pharmacistName?: string; // 审核药师姓名
    auditFailReason?: string; // 药师审核不通过的原因
    useTime?: string; // 处方使用时间，即下单时间
    orderCode?: string; // 处方下单编号
    clinicalDiagnosis: string | string[]; // 临床诊断
    patientName: string; // 患者姓名
    patientSex: string; // 患者性别
    patientAge: string; // 患者年龄
    chiefComplaint: string; // 主诉
    period: number; // 患病时长，即本次患病多久了。1=一周内；2=一个月内；3=半年内；4=半年以上
    isLiver: number; // 肝功能。0=正常；1=异常
    isKidney: number; // 肾功能。0=正常；1=异常
    isAllergyHi: string; // 过敏史描述
    isPersonalMedicalHi: string; // 个人病史描述
    isHomeMedicalHi: string; // 家族病史描述
    isPreparePregnant: number; // 备孕、妊娠、哺乳。0=无；1=有
    doctorName: string; // 医生姓名
    departmentName: string; // 科室名称
    institutionName?: string; // 机构名称
    patientId?: string; // 患者id
    doctorId?: string; // 医生id
    presRpList?: PrescriptionItem[]; // RP取药记录
    presId?: string; // 问诊编号
    type?:number; // 处方类型。1=中药处方；2=西药处方
  }
  // 问诊单
  export interface MedicalInquiryFormDetailData {
    age: number; // 患者年龄
    chiefComplaint: string; // 主诉
    code: string; // 问诊单编号
    consultationStatus: number; // 问诊状态。0=旧版本处方记录；1=待支付；2=待接诊；3=咨询中；4=已完成；5=已取消
    createTime: string; // 创建时间
    departmentName: string; // 科室名称（固化字段）
    doctorName: string; // 医生姓名
    fee: number; // 问诊费用。0代表免费问诊
    gender: string; // 患者性别
    institutionName: string; // 机构名称（固化字段）
    isAllergyHi: string; // 过敏史
    isHomeMedicalHi: string; // 家族病史
    isKidney: number; // 肾功能
    isLiver: number; // 肝功能
    isPersonalMedicalHi: string; // 个人病史
    isPreparePregnant: number; // 备孕、妊娠、哺乳。0=无；1=有
    mchId: string; // 商户号
    merchantCompany: string; // 商户号归属主体
    merchantPlatformName: string; // 商户号服务商名
    name: string; // 患者姓名
    period: number; // 患病时长，即本次患病多久了。1=一周内；2=一个月内；3=半年内；4=半年以上
    receiveStartTime: string; // 接诊开始时间
    receiveEndTime: string; // 接诊结束时间
    successTime: string; // 支付时间
    wxPayNo: string; // 支付单号
    cancelReason: string; // 取消原因
    cancelTime: string; // 取消时间
    type:number; // 问诊类型。1=常规问诊；2=预约问诊
    preBookTime?:string; // 预约时间
    preBookUserName?:string; // 预约单创建人姓名
    physicianAssistantName?:string; // 医助姓名
  }
