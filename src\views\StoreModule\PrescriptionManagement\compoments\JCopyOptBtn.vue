<template>
    <span v-if="!props.id && !props.value">-</span>
    <NSpace v-else>
        <NEllipsis>
            {{
                isNullOrUnDef(props.value) ? '-' : props.value
            }}
        </NEllipsis>
        <CopyOptBtn v-if="props.id" :value="props.id" label="ID" />
        <span v-else>-</span>
    </NSpace>
 </template>
 
 <script lang="ts" setup>
 import { NSpace, NEllipsis } from "naive-ui";
 import { isNullOrUnDef } from "@/utils";
 import CopyOptBtn from "@/components/CopyOptBtn/index.vue";
 
 defineOptions({ name: 'JCopyOptBtn' });
 
 /** props */
 const props = defineProps<{
    id: string | undefined | null;
    value: string  | undefined | null;
 }>();
 </script>
 
 
 <style lang="less" scoped>
 
 </style>