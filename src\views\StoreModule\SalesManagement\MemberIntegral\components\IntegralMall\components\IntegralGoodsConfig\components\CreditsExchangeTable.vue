<template>
  <div class="selling-wrapper">
    <!-- 表头 -->
    <div class="header">
      <JTextButton text type="primary" :loading="isGetLoading" @click="openSelectSpecModal">
        新增规格
      </JTextButton>
      <n-popconfirm @positive-click="handleDel">
        <template #trigger>
          <JTextButton type="error" size="small" text :disabled="!checkedRowKeysRef.length">删除规格</JTextButton>
        </template>
        此操作将删除选中的商品规格，是否继续？
      </n-popconfirm>
    </div>
    <n-data-table
      :columns="columns"
      :data="sortedTableData"
      :row-key="row => row.id"
      default-expand-all
      style="height: 100%;"
      flex-height
      :single-line="false"
      size="small"
      @update:checked-row-keys="handleCheck"
    >
      <template #empty>
        <div class="infoWrapper">
          <img :src="EmptyDataSrc" alt="" />
          <div class="notice">请先新增所需要设置的规格</div>
        </div>
      </template>
    </n-data-table>
    <!-- 选择规格 -->
    <SelectSpecModal ref="selectSpecModalRef" :attributeList="props.attributeList" @update:selected-options="handleUpdateSelect" />
  </div>
</template>

<script lang="tsx" setup name="CreditsExchangeTable">
import { ref, computed, watch } from "vue";
import type { DataTableRowKey } from "naive-ui";
import { isArray } from "@/utils";
import { useLoading, useMessages } from "@/hooks";
import EmptyDataSrc from "@/assets/image/exception/emptyData.png";
import { MAXIMUMINTEGRAL, MINIMUMINTEGRAL, MAXIMUMMONEY, MINIMUMMONEY, MAXIMUMEXCHANGE, MINIMUMEXCHANGE } from "../../../settings";
import { getProductSpecByProductIds } from "@/services/api";
import useGenerateCombinations from "@/views/StoreModule/GoodsManagement/hooks/useGenerateCombinations";
const { generateCombinations } = useGenerateCombinations();
/** 相关组件 */
import SelectSpecModal from "./SelectSpecModal.vue";
import JTextButton from "@/components/JTextButton/index.vue";

/** props */
const props = defineProps<{
    type: 'add' | 'edit';
    productSpecList: Array<any>; // 商品规格
    attributeList: Array<any>; // 商品属性
}>();

const { createMessageError, createMessageSuccess } = useMessages();

/** 表格数据 */
const tableDataRef = ref([]);

/** 当前选中行 */
const checkedRowKeysRef = ref<DataTableRowKey[]>([]);

/** 定义一个函数来格式化价格 */
const formatPrice = (price) => `￥${(price / 100).toFixed(2)}`;

/** 根据规格组合排序表格数据 */
const sortedTableData = computed(() => {
    if (!props.attributeList || !tableDataRef.value.length) {
        return tableDataRef.value;
    }
    
    // 生成期望的规格组合顺序
    const expectedCombinations = generateCombinations(props.attributeList);
    
    // 创建排序映射
    const sortMap = new Map();
    expectedCombinations.forEach((combination, index) => {
        // 根据组合生成排序键
        const sortKey = combination.map(item => item.id).join('-');
        sortMap.set(sortKey, index);
    });
    
    // 对表格数据排序
    return [...tableDataRef.value].sort((a, b) => {
        // 生成当前行的排序键
        const aKey = [a.firstAttrId, a.secondAttrId, a.thirdAttrId]
            .filter(id => id)
            .join('-');
        const bKey = [b.firstAttrId, b.secondAttrId, b.thirdAttrId]
            .filter(id => id)
            .join('-');
        
        const aIndex = sortMap.get(aKey) ?? 999;
        const bIndex = sortMap.get(bKey) ?? 999;
        
        return aIndex - bIndex;
    });
});

/** 动态生成表格列 */
const columns = computed(() => {
    const dynamicColumns:{
        title: string;
        key: string;
        width?: number;
        fixed?: string;
        resizable?: boolean;
        render?: (row: any, index?: number) => any;
        rowSpan?: (row: any, index: number) => number;
        type?: string;
        minWidth?: number;
        align?: string;
    }[]= [
        {
            title:'',
            type: "selection",
            key: "selection",
            fixed: "left",
            width: 50,
            minWidth: 50,
            align: "center",
        }
    ];

    // 根据 attributeList 动态生成规格列
    props.attributeList?.forEach((attr, index) => {
        dynamicColumns.push({
            title: attr.attributeName,
            key: `spec_${index}`,
            width: 100,
            fixed: 'left',
            render: (row) => {
                // 根据索引获取对应的 attrId
                let attrId;
                if (index === 0) attrId = row.firstAttrId;
                else if (index === 1) attrId = row.secondAttrId;
                else if (index === 2) attrId = row.thirdAttrId;
                
                // 在当前属性的 specValue 中查找匹配的值
                const matchedSpec = attr.specValue.find(spec => spec.id === attrId);
                return <span>{matchedSpec?.attributeValue || '-'}</span>;
            }
        });
    });

    // 添加其他固定列
    dynamicColumns.push(
        {
            title: '售价（元）',
            key: 'price',
            width: 100,
            resizable: true,
            render: (row) => {
                let formattedPrice = formatPrice(row.price);
                return <span>{formattedPrice}</span>
            }
        },
        {
            title: "库存",
            key: 'availStocks',
            width: 100,
            resizable: true,
        },
        {
            title: '兑换积分（积分）',
            key: 'exchangePoints',
            resizable: true,
            render: (row) => {
                return <n-input-number
                    value={row?.exchangePoints}
                    onUpdateValue={(value) => handleUpdateValue(row.id, 'exchangePoints', value)}
                    placeholder="请输入兑换积分"
                    show-button={false}
                    precision={0}
                    min={MINIMUMINTEGRAL}
                    max={MAXIMUMINTEGRAL}
                    size="small"
                />
            }
        },
        {
            title: '兑换金额（元）',
            key: 'exchangePrice',
            resizable: true,
            render: (row) => {
                return <n-input-number
                    value={row?.exchangePrice}
                    onUpdateValue={(value) => handleUpdateValue(row.id, 'exchangePrice', value)}
                    placeholder="请输入兑换金额"
                    show-button={false}
                    precision={2}
                    min={MINIMUMMONEY}
                    max={MAXIMUMMONEY}
                    size="small"
                />
            }
        },
        {
            title: '每人兑换总上限',
            key: 'upper',
            resizable: true,
            render: (row) => {
                return <n-input-number
                    value={row?.upper}
                    onUpdateValue={(value) => handleUpdateValue(row.id, 'upper', value)}
                    placeholder="请输入兑换总上限"
                    show-button={false}
                    precision={0}
                    min={MINIMUMEXCHANGE}
                    max={MAXIMUMEXCHANGE}
                    size="small"
                />
            }
        },
        {
            title: '初始已兑（前端展示）',
            key: 'initSaled',
            resizable: true,
            rowSpan: (rowData, rowIndex) => tableDataRef.value.length,
            render: (row) => {
                return <n-input-number
                    value={row?.initSaled}
                    onUpdateValue={(value) => handleUpdateValue(row.id, 'initSaled', value)}
                    placeholder="请输入初始已兑"
                    show-button={false}
                    precision={0}
                    max={10000}
                    size="small"
                />
            }
        },
        {
            title: '是否包邮',
            key: 'operation',
            resizable: true,
            rowSpan: (rowData, rowIndex) => tableDataRef.value.length,
            width: 120,
            render: (row, index) => {
                return (
                    <n-select
                        value={1}
                        options={[{ label: '是', value: 1 }, { label: '否', value: 0 }]}
                        size="small"
                        disabled
                    />
                )
            },
        }
    );

    return dynamicColumns;
});

/** 已选 规格ids */
const productSpecListIds = computed(() => {
    return tableDataRef.value.map(item => {
        if (!item['specId']) {
            return item?.id;
        }
    }).filter(id => id !== undefined);
});

/** 规格更新回调 */
function handleUpdateSelect(productSpec: Array<any>) {
    if (props.type === 'edit') {
        if (isArray(productSpec)) {
            productSpec.forEach(item => {
                if (!productSpecListIds.value.includes(item.id)) {
                    tableDataRef.value.push(item);
                }
            });
        }
    } else {
        const tableDataSet = new Set(tableDataRef.value.map(item => item.id));

        for (let i = 0; i < productSpec.length; i++) {
            if (!tableDataSet.has(productSpec[i]?.id)) {
                tableDataRef.value.push(productSpec[i]);
                tableDataSet.add(productSpec[i]?.id);
            }
        }

    }

    // 统一初始已兑、是否包邮
    if (tableDataRef.value.length > 0) {
        const { initSaled, isFreeShipping } = tableDataRef.value[0];
        tableDataRef.value = tableDataRef.value.map(item => ({
            ...item,
            initSaled,
            isFreeShipping
        }));
    }
}

/** 选中行回调 */
function handleCheck(rowKeys: DataTableRowKey[]) {
    checkedRowKeysRef.value = rowKeys;
}

/** 更新 */
function handleUpdateValue(id: string, key: string, value: string | number) {
    let allModificationsList = ['initSaled'];
    if (allModificationsList.includes(key)) {
        tableDataRef.value.forEach(item => {
            item[key] = value;
        });
        return;
    }
    const item = tableDataRef.value.find(item => item.id === id);
    if (key) {
        item[key] = value;
    }
}

/** 打开该商品规格选择 */
const { loading: isGetLoading, startLoading, endLoading } = useLoading();
const selectSpecModalRef = ref<InstanceType<typeof SelectSpecModal> | null>(null);
const openSelectSpecModal = async () => {
    try {
        if (props.type === 'edit') {
            startLoading();
            const productId = props.productSpecList[0]?.productId;
            if (productId) {
                const productSpecList = await getProductSpecByProductIds(productId);
                if (isArray(productSpecList)) {
                    selectSpecModalRef.value?.acceptParams({
                        productSpecList: productSpecList,
                        productSpecListIds: productSpecListIds.value
                    });
                }
            }
        } else {
            selectSpecModalRef.value?.acceptParams({
                productSpecList: props.productSpecList,
                productSpecListIds: productSpecListIds.value
            });
        }
    } catch (error) {
        createMessageError('获取商品规格失败：' + error);
    } finally {
        endLoading();
    }
};

/** 删除规格 */
function handleDel() {
    tableDataRef.value = tableDataRef.value.filter(item => !checkedRowKeysRef.value.includes(item.id));
    checkedRowKeysRef.value = [];
}

/** 监听 */
watch(() => props.productSpecList, (newVal) => {
    if (newVal && props.type === 'edit') {
        tableDataRef.value = newVal.map(({ exchangePrice, ...rest }) => {
            return {
                ...rest,
                exchangePrice: (exchangePrice / 100),
            }
        });
    }
}, {
    immediate: true
});

/** 使用 defineExpose 显式暴露给父组件 */
defineExpose({
    tableData: tableDataRef
});
</script>

<style lang="less" scoped>
.selling-wrapper {
    width: 100%;
    height: 100%;
    position: relative;

    .header {
        width: 180px;
        height: 35px;
        display: flex;
        justify-content: end;
        align-items: center;
        position: absolute;
        top: -38px;
        right: 0;
    }

    .infoWrapper {
        width: 100%;
        text-align: center;
        box-sizing: border-box;

        img {
            height: 180px;
        }

        .notice {
            font-size: 16px;
            font-weight: 700;
        }
    }
}
</style>
