<template>
  <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumnsSource"
      :pagination="paginationRef"
      @paginationChange="paginationChange"
      :isTableSelection="true">
    <template #searchForm>
      <n-form
          ref="formRef"
          :model="formValue"
          :show-feedback="false"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          size="small"
          :style="{ width: '100%' }">
      </n-form>
    </template>
    <!-- 操作栏 -->
    <template #tableHeaderBtn="scoped">
      <n-button @click="refresh" class="store-button">刷 新</n-button>
    </template>
    <template #tableFooterBtn="scope">

      <!--  TODO 批量接诊暂时隐藏    -->
      <!-- 批量接诊 -->
      <!--      <n-popconfirm @positive-click="clickReception(scope.selectedListIds,'批量')"-->
      <!--                    :positive-button-props="{loading: isBatchLoading}">-->
      <!--        <template #trigger>-->
      <!--          <n-button type="info" ghost>批量接诊</n-button>-->
      <!--        </template>-->
      <!--        此操作将批量接诊，是否继续?-->
      <!--      </n-popconfirm>-->
    </template>
  </FormLayout>
  <DrawerPrescription ref="drawerPrescriptionShow"/>
  <ExitDiagnosis ref="exitDiagnosisShow"/>
  <AddPrescriptionVideo ref="addPrescriptionVideo"/>
  <VideoPrescriptionModal ref="prescriptionModalRef" />
  <InquiryCardModal ref="InquiryCardModalRef"/>
</template>
<script setup lang="tsx">
import {onMounted, ref, watch} from 'vue';
import FormLayout from "@/layout/FormLayout.vue";
import {useTableDefault} from "@/hooks/useTableDefault";
import {
  getPrescriptionDetail,
  presPage,
  presBatchUpdateStatus,
  presBatchDelete,
  presUpdate,
  presPrescribing
} from "@/services/api";
import {useMessages} from "@/hooks";
import {NButton, NSpace} from "naive-ui";
import DrawerPrescription from '@/views/StoreModule/PrescriptionManagement/compoments/drawerPrescription.vue';

import ExitDiagnosis from "@/views/DoctorEndModule/Reception/compoments/ExitDiagnosis.vue";
import AddPrescriptionVideo from "@/views/DoctorEndModule/Reception/compoments/AddPrescriptionVideo.vue";
import {
  doctorEndAddForVideo,
  doctorGetPreAuth,
  doctorPresCompletePres,
  receptionPresBatchConsult,
  receptionPresCancelConsult, receptionPresConsult,
  receptionPresCountToBeConsulted,
  receptionPresPage
} from "@/services/api/doctorEndApi";
import VideoPrescriptionModal from "@/views/DoctorEndModule/Reception/compoments/VideoPrescriptionModal.vue";
import moment from "moment";
import {useUserStore} from "@/stores/modules/user";
import {deepClone} from "@/utils";
import InquiryCardModal from "@/views/DoctorEndModule/Reception/compoments/InquiryCardModal.vue";
import {formatTimeToDHMS} from "@/utils/dateUtils";


enum ReceptionOperationTypeEnum {
  beginReception = '接诊',
  exitReception = '退诊',
  cancelReception = '结束问诊',
}

enum receptionFromEnum {
  Picture = 1,
  Video = 2
}

enum receptionTypeEnum {
  Normal = 1, // 常规问诊
  Order = 2, // 预约问诊
}

interface prescriptionProps {
  tabNameRef: string; // tab标签值
}

/**
 * 待接诊信息类型定义
 */
type ReceptionDataType = {
  id: string; // 问诊ID
  type: receptionTypeEnum; // 问诊类型，1=常规问诊；2=预约问诊
  form: receptionFromEnum; // 问诊形式，1=图文问诊；2=视频问诊
  remainingDuration?: number; // 剩余接诊时长（分钟）：未到预约问诊时间时，后端会返回null，前端需做兼容显示
  nickname: string; // 用户昵称
  patientName: string; // 患者姓名
  patientSex: string; // 患者性别
  patientAge: number; // 患者年龄
  chiefComplaint: string; // 主诉
  preBookTime?: string; // 预约时间：type=2时，才返回此字段
  physicianAssistantName?: string; // 医助姓名：type=2时，才返回此字段
};

/** props */
const props = withDefaults(defineProps<prescriptionProps>(), {
  tabNameRef: 'reception'
});
const emit = defineEmits(["tabQuantity"])

const userStore = useUserStore();
const {createMessageSuccess, createMessageError} = useMessages();
const isBatchLoading = ref<boolean>(false)
const totalCount = ref();
const receptionTypeMap = new Map([
  [receptionTypeEnum.Normal, '常规问诊'],
  [receptionTypeEnum.Order, '预约问诊']
])
/* 问诊形式 */
const receptionFromMap = new Map([
  [receptionFromEnum.Picture, '图文问诊'],
  [receptionFromEnum.Video, '视频问诊']
])

/* 初始化参数 */
const tabValue = ref(null);
const initParams = {
  searchValue: '', // 搜索关键字
  source: null, // 来源
  searchType: ''
};
const formValue = ref({...initParams});

/* 表格方法Hook */
const {
  isLoading,
  tableData,
  paginationRef,
  paginationChange,
  summaryRef,
  sortTableData,
  pageTableData,
} = useTableDefault({
  pageDataRequest: receptionPresPage,
});

onMounted(async () => {
  await getDoctorPreAuth()
  getTableData()
  receptionPresCountToBeConsulted({})
})

//刷新
const refresh = () => {
  getTableData()
}

/** 搜索 */
const formSearch = () => {
  getTableData()
}

/** 获取参数 */
const getParams = () => {
  const {} = formValue.value;

  return {}
};

/** 请求表格数据 */
function getTableData() {
  pageTableData(getParams(), paginationRef.value, true);
}

/* 获取医生开方权限 */
async function getDoctorPreAuth() {
  try {
    const result = await doctorGetPreAuth({})
    if (result == true || result == 'true') {
      let newUserInfo = deepClone(userStore.userInfo);
      newUserInfo.hasAddPreAuth = true
      userStore.setUserInfo(newUserInfo)
    }
  } catch (e) {
    createMessageError(`获取医生开方权限失败:${e}`)
  }
}

//触发tab数量事件
const tabClick = () => {
  emit('tabQuantity', props.tabNameRef, totalCount.value);
}

/* 表格项 */
const tableColumns = ref([]);
const tableColumnsSource = [
  {
    title: "用户昵称",
    key: "nickname",
    align: "left",
    fixed: "left",
    width: 100,
  },
  {
    title: "患者信息",
    key: "patientName",
    align: "left",
    width: 100,
    render: (row) => {
      return `${row.patientName || '-'}(${row.patientSex}  ${row.patientAge})`
    }
  },
  {
    title: "主诉",
    key: "chiefComplaint",
    align: "left",
    width: 100,
  },
  {
    title: "订单类型",
    key: "type",
    align: "left",
    width: 120,
    render: (row) => {
      return <span>{receptionTypeMap.get(row.type) ?? "-"}</span>;
    }
  },
  {
    title: "问诊形式",
    key: "from",
    align: "left",
    width: 120,
    render: (row) => {
      /* 后台字段英文写错了 */
      return <span>{receptionFromMap.get(row.form) ?? "-"}</span>;
    }
  },
  {
    title: "预约问诊时间",
    key: "preBookTime",
    align: "left",
    width: 100,
    render: (row) => {
      if (!row.preBookTime) return '-'
      return <>
        <NSpace vertical>
          <span>{row.preBookTime}</span>
          <n-ellipsis>
            <span>{row.physicianAssistantName ? `医助:${row.physicianAssistantName}` : '-'}</span>
          </n-ellipsis>
        </NSpace>
      </>
    }
  },
  {
    title: "剩余接诊时长",
    key: "remainingDuration",
    align: "left",
    width: 100,
    render: (row) => {
      if (!row.remainingDuration) {
        if (row.type == receptionTypeEnum.Normal) {
          return '-'
        }
        if (row.type == receptionTypeEnum.Order) {
          return '未到问诊时间'
        }
      }
      return formatTimeToDHMS(
          moment().valueOf(),
          moment().add({minute: row.remainingDuration}).valueOf()
      )
    }
  },
  {
    title: "操作",
    key: "action",
    width: 120,
    align: "left",
    fixed: "right",
    render: (row) => {
      return (
          <>
            {
                 <NSpace align="center" justify="center">
                  {/* 接诊 */}
                  <NButton text style={"margin-right: 8px;"} type="info" onClick={() => clickReception([row.id],row)}>
                    {ReceptionOperationTypeEnum.beginReception}
                  </NButton>
                  {/* 退诊 */}
                  <NButton style={"margin-right: 8px;"} text type="info" onClick={() => clickExitDiagnosisShow(row)}>
                    {ReceptionOperationTypeEnum.exitReception}
                  </NButton>
                </NSpace>
            }
            {
                row.form == receptionFromEnum.Video && <NSpace align="center" justify="center">
                  {/* 上传视频 */}
                  <NButton text style={"margin-right: 8px;"} type="info" onClick={
                    () => {
                      clickUploadPrescriptionVideoShow(row)
                    }}>
                    上传视频
                  </NButton>
                  {/* 开方 */}
                  {
                    (userStore.userInfo.hasAddPreAuth) ?
                        <NButton text style={"margin-right: 8px;"} type="info" onClick={() => {
                          clickAddVideoPrescription(row)
                        }}>
                          开方
                        </NButton> : <></>
                  }
                  {/* 结束问诊 */}
                  <NButton style={"margin-right: 8px;"} text type="info" onClick={() => clickCancelReception(row)}>
                    {ReceptionOperationTypeEnum.cancelReception}
                  </NButton>
                </NSpace>
            }
          </>
      );
    },
  },
];

/** 开处方 与 编辑 */
const drawerPrescriptionShow = ref(null);
const clickPrescribe = (row, type) => {
  const _params = {
    row,
    getInfoApi: getPrescriptionDetail,
    updateInfoApi: type == '编辑' ? presUpdate : presPrescribing,
    refresh: refresh,
    type: type
  };
  drawerPrescriptionShow.value?.acceptParams(_params);
};

/**
 * 接诊、批量接诊
 */
const clickReception = async (idList: Array<string>, row?:ReceptionDataType, operationType?: '批量',) => {
  if (row?.form == receptionFromEnum.Video){
    await handleSubmitNormalReception(idList, operationType)
    setTimeout(() => {
      handleSubmitVideoReception(row)
    }, 300)
  }else{
    await handleSubmitNormalReception(idList, operationType)
  }
}

/* 处理普通接诊 */
async function handleSubmitNormalReception(idList: Array<string>, operationType?: '批量') {
  try {
    isBatchLoading.value = true;
    if (operationType == '批量') {
      await receptionPresBatchConsult({
        data: {idList}
      })
    } else {
      await receptionPresConsult({
        data: {id: idList[0]}
      })
    }
    createMessageSuccess(`${operationType || ''}接诊成功`)
    refresh()
  } catch (error) {
    createMessageError(`${operationType || ''}接诊失败:${error}`)
  } finally {
    isBatchLoading.value = false;
  }
}

/* 视频接诊问诊卡句柄 */
const InquiryCardModalRef = ref<InstanceType<typeof InquiryCardModal> | null>(null);

/* 处理视频接诊 */
async function handleSubmitVideoReception(row) {
  const _params = {
    api: receptionPresCancelConsult,
    rowData: row,
    refresh
  }
  InquiryCardModalRef.value?.acceptParams(_params)
}

/** 批量取消、取消、恢复 */
const batchCancellationRecover = async (type: 'single-cancel' | 'batch-cancel' | 'single-recover' | 'batch-recover', ids: Array<string>) => {
  const params = {
    idList: ids,
  };
  const msg: Record<'single-cancel' | 'batch-cancel' | 'single-recover' | 'batch-recover', string> = {
    'single-cancel': '取消',
    'batch-cancel': '批量取消',
    'single-recover': '恢复',
    'batch-recover': '批量恢复',
  };
  try {
    await presBatchUpdateStatus(params);
    createMessageSuccess(`${msg[type]}成功`);
    refresh()
  } catch (err) {
    createMessageError(`${msg[type]}失败: ${err}`)
  }
};

/** 删除 */
const clickDelete = async (type: 'single' | 'batch', selectId: Array<string>, selectRowList: Array<any>) => {
  // customerIdList
  const createByList = selectRowList.map(item => item.createBy);

  const params = {
    idList: selectId,
    createByList: createByList,
  }
  try {
    await presBatchDelete({data: params})
    createMessageSuccess(type === 'batch' ? '批量删除成功' : '删除成功');
    refresh()
  } catch (err) {
    createMessageError(`${type === 'batch' ? '批量删除失败' : '删除失败'}: ${err}`);
  }
};

// 退诊原因
const exitDiagnosisShow = ref<InstanceType<typeof ExitDiagnosis> | null>(null);
const clickExitDiagnosisShow = (row) => {
  const _params = {
    show: true,
    api: receptionPresCancelConsult,
    rowData: row,
    refresh
  }
  exitDiagnosisShow.value?.acceptParams(_params)
}

// 预约问诊-结束问诊
const clickCancelReception = async (row: object & { id: string }) => {
  try {
    await doctorPresCompletePres({id: row.id})
    createMessageSuccess(`${ReceptionOperationTypeEnum.cancelReception}成功`)
    refresh()
  } catch (error) {
    createMessageError(`${ReceptionOperationTypeEnum.cancelReception}失败:${error}`)
  }
}

/* 开处方弹窗实例 */
const prescriptionModalRef = ref();
/* 开视频问诊处方 */
const clickAddVideoPrescription = (row) => {
  const _params = {
    show: true,
    mode: 'add',
    params: {
      id: row.id,
      api: doctorEndAddForVideo
    },
    refresh
  }
  prescriptionModalRef.value?.acceptParams(_params)
}

/* 上传视频 */
const addPrescriptionVideo = ref(null)
const clickUploadPrescriptionVideoShow = (row) => {
  const _params = {
    show: true,
    rowData: row,
    refresh
  }
  addPrescriptionVideo.value?.acceptParams(_params)
}

/** 监听 */
watch(() => props.tabNameRef, (newVal) => {
      tabValue.value = newVal;

    },
    {
      immediate: true
    });

/* 监听 分页 */
watch(paginationRef, (newVal, oldVal) => {
  totalCount.value = newVal.total;
  tabClick()
},);
</script>

<style lang="less" scoped>
@import "@/styles/default.less";

:deep .n-tag__content {
  text-overflow: ellipsis;
  overflow: hidden;
}

</style>
