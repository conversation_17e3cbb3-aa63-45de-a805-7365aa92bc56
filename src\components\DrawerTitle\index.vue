<template>
    <div class="drawer-title-wrapper" :style="{ height: isString(props.height) ? props.height : `${props.height}px` }">
        <n-card size="small" :bordered="false" :content-style="{ padding: '10px 12px' }" style="border-radius: 0;">
          <div style="height: 30px; display: flex; align-items: center;">
            <n-button text class="mr-12" @click="emits('goBack')">
              <template #icon>
                <ArrowBackOutline size="18" />
              </template>
            </n-button>
            <span style="font-weight: 600; line-height: 30px; font-size: 16px;">{{ props.title }}</span>
          </div>
        </n-card>
    </div>
  </template>
  
  <script lang="ts" setup name='drawerTitle'>
  import { ArrowBackOutline } from '@vicons/ionicons5';
  import { isString } from "@/utils/isUtils";
  
  /** props */
  const props = withDefaults(defineProps<{
    title: string; // 标题
    height?: number | string; // 高度
  }>(), {
    height: 52
  });
  
  /** emits */
  const emits = defineEmits<{
    (e: "goBack"): void; // 点击返回回调
  }>();
  
  </script>
  
  <style lang="less" scoped>
  @import "@/styles/defaultVar.less";
  .drawer-title-wrapper {
    width: 100%;
    box-sizing: border-box;
    border-bottom: 1px solid @default-border-color;
  }
  </style>