import { defHttp } from "@/services";

/** 疗法类产品清单 */
export const enum TherapyGoodsApi {
    page = "/therapyDrugItem/page",
    add = '/therapyDrugItem/add',
    update = '/therapyDrugItem/update',
    delete = '/therapyDrugItem/delete'
}

/** 疗法产品新增 */
interface AddParams {
    id?: string; // 疗法类产品清单ID
    name: string; // 产品名称
    imgPath: string; // 产品图路径
    dosage: string; // 服用方式
  }
export function addTherapyGoods(_params: AddParams) {
    return defHttp.post({
        url: TherapyGoodsApi.add,
        params: {
          data: _params
        }
    });
};

/** 疗法产品修改 */
interface UpdateParams {
    id?: string; // 疗法类产品清单ID
    name: string; // 产品名称
    imgPath: string; // 产品图路径
    dosage: string; // 服用方式
  }
export function updateTherapyGoods(_params: UpdateParams) {
    return defHttp.put({
        url: TherapyGoodsApi.update,
        params: {
          data: _params
        }
    });
};

/** 获取产品 */
export interface TherapyGoodsPageRes {
    records: Array<any>,
    total: string;
    size: string;
    current: string;
}
/** 商品分类分页 */
export function getTherapyGoodsPage(params: {
    data: {
        name: string,
    },
    pageVO: {
        current: number,
        size: number
    }
}) {
    return defHttp.post<TherapyGoodsPageRes>({
        url: TherapyGoodsApi.page,
        params,
    });
}

/** 删除产品 */
export function therapyGoodsDelete(params: { id: string }) {
  return defHttp.delete({
    url: TherapyGoodsApi.delete,
    requestConfig: {
      isQueryParams: true,
    },
    params,
  })
}