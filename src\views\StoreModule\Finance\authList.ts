import { FinanceTraceAccountBalanceAuth ,FinanceTraceInvokeHistoryAuth, FinanceAllocationAccountManagementAuth, FinanceAllocationManagementAuth ,FinanceAllocationSettlementAuth, SubAccountPayeeRuleSettingAuth, StoreCommissionRuleAuth, StoreCommissionDetailAuth, FinancePaymentReviewAuth} from "@/enums/authKeys";
import { hasAuth } from "@/utils/auth/authUtils";


/** 查看记录 */
export const hasRechargerecordAuth= function(){
    return hasAuth(FinanceTraceAccountBalanceAuth.financeTraceAccountBalanceRechargerecord.key);
}()
/** 查看记录 */
export const financeTraceAccountBalance= function(){
    return hasAuth(FinanceTraceAccountBalanceAuth.financeTraceAccountBalanceIndex.key);
}()
/** 轨迹调用 */
export const financeTraceInvokeHistory= function(){
    return hasAuth(FinanceTraceInvokeHistoryAuth.financeTraceInvokeHistoryIndex.key);
}()

/** 分账方规则设置 */
export const hasSubAccountPayeeRuleSettingAuth = function(){
    return hasAuth(SubAccountPayeeRuleSettingAuth.SubAccountPayeeRuleSetting.key);
}()

/** 分账方规则设置 -- 保存 */
export const hasSubAccountPayeeRuleSettingSaveAuth = function(){
    return hasAuth(SubAccountPayeeRuleSettingAuth.SubAccountPayeeRuleSettingSave.key);
}()

/** 分账入账方管理 */
export const hasFinanceAllocationAccountManagementAuth = function(){
    return hasAuth(FinanceAllocationAccountManagementAuth.FinanceAllocationAccountManagement.key);
}()

/** 分账入账方管理 -- 新增 */
export const hasFinanceAllocationAccountManagementAddAuth = function(){
    return hasAuth(FinanceAllocationAccountManagementAuth.FinanceAllocationAccountManagementAdd.key);
}()

/** 分账入账方管理 -- 详情 */
export const hasFinanceAllocationAccountManagementDetailAuth = function(){
    return hasAuth(FinanceAllocationAccountManagementAuth.FinanceAllocationAccountManagementDetail.key);
}()

/** 分账入账方管理 -- 重发短信 */
export const hasFinanceAllocationAccountManagementReSendMsgAuth = function(){
    return hasAuth(FinanceAllocationAccountManagementAuth.FinanceAllocationAccountManagementReSendMsg.key);
}()

/** 分账入账方管理 -- 激活 */
export const hasFinanceAllocationAccountManagementActiveAuth = function(){
    return hasAuth(FinanceAllocationAccountManagementAuth.FinanceAllocationAccountManagementActive.key);
}()

/** 分账入账方管理 -- 删除 */
export const hasFinanceAllocationAccountManagementDeleteAuth = function(){
    return hasAuth(FinanceAllocationAccountManagementAuth.FinanceAllocationAccountManagementDelete.key);
}()

/** 分账单管理 -- 详情 */
export const hasFinanceAllocationManagementDetailAuth = function(){
    return hasAuth(FinanceAllocationManagementAuth.FinanceAllocationManagementDetail.key);
}()

/** 分账单管理 -- 取消结算 */
export const hasFinanceAllocationManagementCancelSettlementAuth = function(){
    return hasAuth(FinanceAllocationManagementAuth.FinanceAllocationManagementCancelSettlement.key);
}()

/** 分账单管理 -- 导出 */
export const hasFinanceAllocationManagementExportAuth = function(){
    return hasAuth(FinanceAllocationManagementAuth.FinanceAllocationManagementExport.key);
}()

/** 分账单管理 -- 成本价 */
export const hasFinanceAllocationManagementCostDeductionAuth = function(){
    return hasAuth(FinanceAllocationManagementAuth.FinanceAllocationManagementCostDeduction.key);
}()

/** 分账单结算 */
export const hasFinanceAllocationSettlementAuth = function(){
    return hasAuth(FinanceAllocationSettlementAuth.FinanceAllocationSettlement.key);
}()


/** 分账单结算 -- 详情*/
export const hasFinanceAllocationSettlementDetailAuth = function(){
    return hasAuth(FinanceAllocationSettlementAuth.FinanceAllocationSettlementDetail.key);
}()

/** 分账单结算 -- 重新发起*/
export const hasFinanceAllocationSettlementReSendAuth = function(){
    return hasAuth(FinanceAllocationSettlementAuth.FinanceAllocationSettlementReSend.key);
}()

/** 分账单结算 -- 转线下打款*/
export const hasFinanceAllocationSettlementLocalAuth = function(){
    return hasAuth(FinanceAllocationSettlementAuth.FinanceAllocationSettlementLocal.key);
}()

/** 分账单结算 -- 已线下打款*/
export const hasFinanceAllocationSettlementLocalCompleteAuth = function(){
    return hasAuth(FinanceAllocationSettlementAuth.FinanceAllocationSettlementLocalComplete.key);
}()

/** 分账单结算 -- 导出*/
export const hasFinanceAllocationSettlementExportAuth = function(){
    return hasAuth(FinanceAllocationSettlementAuth.FinanceAllocationSettlementExport.key);
}()

/** 商户号管理*/
export const hasFinanceStoreConfigMerchantManagementAuth = function(){
    return hasAuth(FinanceAllocationSettlementAuth.FinanceStoreConfigMerchantManagement.key);
}()

/** 商户号管理 子菜单*/
export const hasFinanceStoreConfigMerchantAuth = function(){
    return hasAuth(FinanceAllocationSettlementAuth.FinanceStoreConfigMerchant.key);
}()

/** 商户号管理 编辑商户号*/
export const hasFinanceStoreConfigMerchantUpdateAuth = function(){
    return hasAuth(FinanceAllocationSettlementAuth.FinanceStoreConfigMerchantUpdate.key);
}()

/** 商户号管理 新建商户号*/
export const hasFinanceStoreConfigMerchantAddAuth = function(){
    return hasAuth(FinanceAllocationSettlementAuth.FinanceStoreConfigMerchantAdd.key);
}()
/** 商户充值*/
export const hasFinanceStoreConfigRechargeAuth = function(){
    return hasAuth(FinanceAllocationSettlementAuth.FinanceStoreConfigRecharge.key);
}()
/** 充值记录*/
export const hasFinanceStoreConfigRechargeRecordAuth = function(){
    return hasAuth(FinanceAllocationSettlementAuth.FinanceStoreConfigRechargeRecord.key);
}()
/** 支出记录*/
export const hasFinanceStoreConfigExpenseRecordAuth = function(){
    return hasAuth(FinanceAllocationSettlementAuth.FinanceStoreConfigExpenseRecord.key);
}()
/** 账户余额*/
export const hasFinanceStoreConfigAccountBalanceAuth = function(){
    return hasAuth(FinanceAllocationSettlementAuth.FinanceStoreConfigAccountBalance.key);
}()
/** 开通用户充值记录*/
export const hasFinanceStoreConfigAccountCreateAuth = function(){
    return hasAuth(FinanceAllocationSettlementAuth.FinanceStoreConfigAccountCreate.key);
}()
/** 线下充值*/
export const hasFinanceStoreConfigAccountOfflineRechargeAuth = function(){
    return hasAuth(FinanceAllocationSettlementAuth.FinanceStoreConfigAccountOfflineRecharge.key);
}()
/** 线下退款*/
export const hasFinanceStoreConfigAccountCreateOfflineRefundAuth = function(){
    return hasAuth(FinanceAllocationSettlementAuth.FinanceStoreConfigAccountCreateOfflineRefund.key);
}()
/** 分账管理*/
export const hasFinanceStoreConfigAccountCreateManagementAuth = function(){
    return hasAuth(FinanceAllocationSettlementAuth.FinanceStoreConfigAccountCreateManagement.key);
}()
/** 分账单管理*/
export const hasFinanceStoreConfigAccountCreateBillManagementAuth = function(){
    return hasAuth(FinanceAllocationSettlementAuth.FinanceStoreConfigAccountCreateBillManagement.key);
}()
/** 物流充值*/
export const hasFinanceStoreConfigAcLogisticsRechargeAuth = function(){
    return hasAuth(FinanceAllocationSettlementAuth.FinanceStoreConfigAcLogisticsRecharge.key);
}()

/** 分佣金规则修改 */
export const hasStoreCommissionRuleAuth = function(){
    return hasAuth(StoreCommissionRuleAuth.StoreCommissionRuleIndexSave.key);
}()

/** 分佣明细导出 */
export const hasStoreCommissionDetailExportAuth = function(){
    return hasAuth(StoreCommissionDetailAuth.StoreCommissionDetailIndexExport.key);
}()
/** 分佣明细详情 */
export const hasStoreCommissionDetailDetailAuth = function(){
    return hasAuth(StoreCommissionDetailAuth.StoreCommissionDetailIndexDetail.key);
}()

/** 打款审核-导出 */
export const hasFinancePaymentReviewAuth = function(){
    return hasAuth(FinancePaymentReviewAuth.FinancePaymentReviewIndexExport.key);
}()
/* 打款审核-审核通过 */
export const hasFinancePaymentReviewReviewAuth = function(){
    return hasAuth(FinancePaymentReviewAuth.FinancePaymentReviewIndexReview.key);
}()
/** 打款审核-驳回 */
export const hasFinancePaymentReviewRejectAuth = function(){
    return hasAuth(FinancePaymentReviewAuth.FinancePaymentReviewIndexReject.key);
}()
/** 打款审核-详情 */
export const hasFinancePaymentReviewDetailAuth = function(){
    return hasAuth(FinancePaymentReviewAuth.FinancePaymentReviewIndexDetail.key);
}()
/** 打款审核-打款 */
export const hasFinancePaymentReviewPayoutAuth = function(){
    return hasAuth(FinancePaymentReviewAuth.FinancePaymentReviewIndexPayout.key);
}()
