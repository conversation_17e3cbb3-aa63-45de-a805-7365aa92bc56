import { SystemRoleType, SystemStoreType } from "@/enums"
import { useSystemStoreWithoutSetup } from "@/stores/modules/system"
import { useUserStoreWithoutSetup } from "@/stores/modules/user"

export function useSkipAuthCodeList(){
    const systemStore = useSystemStoreWithoutSetup()
    const userInfoStore = useUserStoreWithoutSetup()
    const devCodeList = ["sg.store.order.storeConfig.development","sg.store.order.storeConfig.development.edit"]  
    const doctorCodeList = ['sg.store.prescription.management','sg.store.doctor.management','sg.store.doctorModule']

    /**获取角色选择表格忽略code */
    function getAuthRoleTableSkipList(){
        let skipCodeList = [...devCodeList]
        if(systemStore._globalConfig['marketplaceType'] == SystemStoreType.GENERALMALL){
            skipCodeList = [
                ...doctorCodeList
            ]
        }
        return skipCodeList
    }

    /**根据系统类型，获取菜单忽略code */
    function getMarketPlaceTypeSkipList(){
        let skipCodeList = []
        if(systemStore._globalConfig['marketplaceType'] == SystemStoreType.GENERALMALL){
            skipCodeList = skipCodeList.concat(doctorCodeList)
        }
        if(userInfoStore.userInfo.type !== SystemRoleType.SMALLPROGRAMDEVROLE){
            skipCodeList = skipCodeList.concat(devCodeList)
        }
        return skipCodeList
    }

   
    return {getMarketPlaceTypeSkipList,getAuthRoleTableSkipList}
}