import { fileURLToPath, URL } from 'node:url'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import vueJsx from "@vitejs/plugin-vue-jsx";
import { NaiveUiResolver } from 'unplugin-vue-components/resolvers'
import viteCompress from 'vite-plugin-compression'
import {createSvgIconsPlugin } from 'vite-plugin-svg-icons';
import path from 'path';
import {JComponentsResolver} from "./unplugin-vue-resolver.js"
import legacy from '@vitejs/plugin-legacy'


// https://vitejs.dev/config/
const timestamp = new Date().getTime()
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())
  const UAT_VERSION = process.env.UAT
  return {
    base:'./',
    plugins: [
      vue(),
      vueJsx(),
      AutoImport({
        imports: [
          'vue',
          {
            'naive-ui': [
              'useDialog',
              'useMessage',
              'useNotification',
              'useLoadingBar'
            ]
          }
        ]
      }),
      Components({
        resolvers: [NaiveUiResolver(),JComponentsResolver()]
      }),
      createSvgIconsPlugin({
        // 指定要缓存的图标文件夹
        iconDirs: [
          path.resolve(process.cwd(), 'src/assets/icons/svg'),
          path.resolve(process.cwd(), 'src/assets/icons/menu-svg'),
        ],
        // 执行icon name的格式
        symbolId: 'icon-[dir]-[name]',
      }),
      env.VITE_SYS_ENV === 'PROD' && legacy({
        targets: ['chrome 80'],
        additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
        renderLegacyChunks: true,
        modernPolyfills:['es.string.replace-all']
      }),
      viteCompress({
        threshold:1024
      })
    ],
    build:{
      minify:'terser',
      terserOptions:{
        compress:{
          drop_console:true,
          drop_debugger:true
        }
      },
      rollupOptions: {
        output: {
          // 入口文件名
          entryFileNames: `assets/[name]-${timestamp}${env.VITE_SYS_ENV == 'PROD'?`-${UAT_VERSION}`:''}.js`,
          // 块文件名
          chunkFileNames: `assets/[name]-[hash]-${timestamp}${env.VITE_SYS_ENV == 'PROD'?`-${UAT_VERSION}`:''}.js`,
          // 资源文件名 css 图片等等
          assetFileNames: `assets/[name]-[hash]-${timestamp}${env.VITE_SYS_ENV == 'PROD'?`-${UAT_VERSION}`:''}.[ext]`,
        },
      },
    },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    server: {
      port:Number(env.VITE_DEV_PORT),
      host:"127.0.0.1",
      proxy: {
        logLevel:'debug',
        [`${env.VITE_DEV_STORE_PREFIX}${env.VITE_DEV_BASIC_PREFIX}`]: {
          target: `${env.VITE_DEV_BASIC_BASE_URL}`,
          changeOrigin: true,
          rewrite: path => {
            const reg = new RegExp(`^\\${env.VITE_DEV_STORE_PREFIX}\\${env.VITE_DEV_BASIC_PREFIX}`)
            return path.replace(reg, '')
          }
        },
        [env.VITE_DEV_STORE_PREFIX]: {
          target: `${env.VITE_DEV_STORE_BASE_URL}`,
          changeOrigin: true,
          rewrite: path => {
            const reg =  new RegExp(`^\\${env.VITE_DEV_STORE_PREFIX}(?!\\${env.VITE_DEV_BASIC_PREFIX})`)
            return path.replace(reg, '')
          }
        }
      
      }
    }
  }
})
