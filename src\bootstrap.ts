import { guards } from "./router/guard";
import { loadGuards } from "./utils/routerUtils";
import { defHttp } from "@/services/index";
import interceptors from "@/services/interceptors";
import type { Router } from "vue-router";
import type { Pinia } from "pinia";
import { createCacheStorage } from "@/utils/cache/storageCache";
import { CacheConfig } from "@/utils/cache/config";
import { useUserStoreWithoutSetup } from "./stores/modules/user";
import { initClipboardCopyDefaultEvent } from "./utils/clipboardUtils";
export function bootstrap({ router, store }: { router: Router; store: Pinia }) {
  loadGuards(guards);
  defHttp.loadInterceptors(interceptors, { router, store });
  const routeConfigStorage = createCacheStorage(CacheConfig.RouteConfig);
  const _routeConfigCache = routeConfigStorage.get();
  const userStore = useUserStoreWithoutSetup();
  userStore.setRouteConfig(_routeConfigCache as []);
  initClipboardCopyDefaultEvent();
}

export function bootstrapAfterLogin() {}
