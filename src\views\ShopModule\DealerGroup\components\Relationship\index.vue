<template>
  <FormLayout
    :isLoading="isLoading"
    :isDisplayIndex="false"
    :tableData="tableData"
    :tableColumns="tableColumns"
    :isNeedCollapse="false"
    :pagination="paginationRef"
    :isTableSelection="false"
    :isBatchDelete="false"
    @paginationChange="paginationChange"
  >
    <template #searchForm>
      <!-- 表单 -->
      <n-form
        ref="formRef"
        :model="modal"
        :show-feedback="false"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
        size="small"
        :style="{ width: '100%' }"
      >
        <n-form-item label="大类名称">
          <j-search-input v-model:value="modal.cateName" placeholder="请输入大类名称" @search="tableSearch" />
        </n-form-item>
        <n-form-item label="分组名">
          <j-search-input v-model:value="modal.groupName" placeholder="请输入分组名称" @search="tableSearch" />
        </n-form-item>
      </n-form>
    </template>
    <template #tableHeaderBtn>
      <!-- <n-button type="primary" @click="handleModal('新增')">
          <template #icon>
            <SvgIcon name="add" style="font-size: 14px;"></SvgIcon>
          </template>
          新增分组
        </n-button> -->
    </template>
  </FormLayout>
  <AddMadal @refresh="refreshModal" v-model:show="showModal" :type="_modalType" :modalState="modalState" />
</template>

<script setup lang="tsx">
import { ref, watch, toRef, computed, onMounted } from "vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import FormLayout from "@/layout/FormLayout.vue";
import { getDealerGroupRelevancePage } from "@/services/api";
import AddMadal from "./components/addModal.vue";
import { useMessages } from "@/hooks/useMessage";
import { hasEditDealerTagsAuth } from "../../authList";
/* 表格方法Hook */
const { isLoading, tableData, paginationRef, pageTableData, refreshTableData, paginationChange } = useTableDefault({
  pageDataRequest: getDealerGroupRelevancePage,
});
const message = useMessages();
const initParams = {
  cateName: null,
  groupName: null,
};
const showModal = ref<boolean>(false);
const isOptLoading = ref<boolean>(false);
const _modalType = ref<'新增'|'编辑'>("新增");
const modal = ref({ ...initParams });
const modalState = ref({});
const tableColumns = ref<any[]>([
  {
    title: "大类名称",
    key: "cateName",
    align: "left",
    fixed: "left",
    ellipsis: true,
    width: 100,
  },
  {
    title: "分组名",
    key: "name",
    align: "left",
    width: 100,
  },
  {
    title: "关联经销商",
    key: "dealerGroupRelateDTOList",
    align: "left",
    fixed: "left",
    width: 200,
    ellipsis: false,
    render(rowData) {
      const dealerGroupRelateDTOList = rowData.dealerGroupRelateDTOList || [];
      return (
        <div style={{ display: "flex", "flex-wrap": "wrap", width: "100%" }}>
          {dealerGroupRelateDTOList.map(item => {
            return (
              <n-tag bordered={false} size="small" class="mb-5 mr-5" type="info">
                {item.dealerName}
              </n-tag>
            );
          })}
        </div>
      );
    },
  },
  {
    title: "操作",
    key: "action",
    width: 100,
    fixed: "right",
    render(rowData) {
      return (
        <n-space>
          {hasEditDealerTagsAuth ? (
            <n-button text type="primary" onClick={() => handleModal("编辑", rowData)}>
              编辑关联
            </n-button>
          ) : null}
          {/* <n-popconfirm
              v-slots={{
                trigger: () => (
                  <n-button disabled={isOptLoading.value} text size="small" type='error'>
                    删除
                  </n-button>
                )
              }}
              positiveButtonProps={{ loading: isOptLoading.value, disabled: isOptLoading.value }}
              onPositiveClick={() => handleDel(rowData.id)}
            >
              此操作将进行删除，是否继续？
            </n-popconfirm> */}
        </n-space>
      );
    },
  },
]);
//删除
// const handleDel = async (id: string | number) => {
//   try {
//     isOptLoading.value = true
//     await dealerGroupDel(id)
//     tableData.value = tableData.value.filter(item => item.id !== id)
//     message.createMessageSuccess('删除成功')
//   } catch (error) {
//     message.createMessageError(`删除失败：${error}`)
//   } finally {
//     isOptLoading.value = false
//   }
// }
//更新数据的组名
// const getUpdateName = (name: string, id: string | number) => {
//   tableData.value.forEach(item=>{
//     if(item.id === id){
//       item.name = name
//     }
//   })
// }
/* 表格搜索 */
const tableSearch = () => {
  const params = {
    cateName: modal.value.cateName,
    groupName: modal.value.groupName,
  };
  pageTableData(params, paginationRef.value);
};
const refreshModal = () => {
  tableSearch();
};
//新增|编辑
const handleModal = (type: '新增'|'编辑', row?: any) => {
  if (row) {
    modalState.value = row;
  }
  _modalType.value = type;
  showModal.value = true;
};
onMounted(() => {
  tableSearch();
});
</script>

<style scoped lang="less">
@import "@/styles/default.less";
.tag-list {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
</style>
