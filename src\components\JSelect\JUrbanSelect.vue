<!-- 地址 市区 -->
<template>
  <JSelect
    :value="props.value"
    :loading="isLoading"
    :onFocus="handlerFocus"
    :options="doctorList"
    :onClear="handleClear"
    @update:value="onChange"
    style="width: 140px;"
    placeholder="请选择市区"
    :disabled="props.disabled"
  />
</template>
<script setup lang="ts">
import { ref, watch } from "vue";
import JSelect from "@/components/JSelect/index.vue";
import { addressEntityListByCode } from "@/services/api";
import { useMessages } from "@/hooks";

/* Props */
const props = withDefaults(
  defineProps<{
    isImmediately?: boolean; // 是否立即加载
    value: Array<string | number> | string | number | null; // 选择的值
    disabled:boolean;
    parentCode:string | number
  }>(),
  {
    isImmediately: false,
    disabled:false,
    parentCode:null
  },
);

const emits = defineEmits<{
  (e: "update:value", selectValue: any): void; // 更新选择值事件
  (e: "update:label", labelValue: any): void; // 更新选择label
  (e: "update:code", codeValue: any): void; // 更新选择code
}>();

/* 提示 */
const message = useMessages();

/* 是否加载 */
const isLoading = ref(false);
const doctorList = ref([]); // 列表

/* 筛选、转化{label: '', value: ''} */
function handleData(filterData: AddressModule.AddressEntity[]) {
  let dataList = [];
  dataList = filterData.map(item => {
    return { 
        label: item.name, 
        value: item.id,
        code: item.code
      };
    });
  return dataList;
}

/* 获取省份分类列表 */
async function getDoctorList() {
  const params = {
     data: {
       parentCode: props.parentCode,
       cateType: 2
     }
  }
  try {
    isLoading.value = true;
    const data = await addressEntityListByCode(params);
    handleData(data).forEach(item => {
        doctorList.value.push(item);
    });
  } catch (error) {
    message.createMessageError("获取市区信息失败：" + error);
  } finally {
    isLoading.value = false;
  }
}

/** 选择值改变事件处理函数 */
function onChange(value,text) {
  emits("update:value", value);
  emits("update:label", text.label);
  emits("update:code", text.code);
}


/** 清空事件处理函数 */
const handleClear = () => {
  emits("update:value", null);
  emits("update:label", null);
  emits("update:code", null);
};
/** 聚焦事件处理函数 */
function handlerFocus() {
  // 如果市区列表为空
  if (!doctorList.value.length) {
    getDoctorList();
  }
}

/** 监听 */
watch(
  () => props.isImmediately,
  newVal => {
    if (newVal) {
      getDoctorList();
    }
  },
  { immediate: true },
);

/** 监听parentCode */

watch(
  () => props.parentCode,
  newVal => {
      handleClear();
      doctorList.value = []
  },
);
</script>
  
<style scoped lang="less"></style>
    