import { ref } from "vue";
import { useLoading, useMessages } from "@/hooks";
import { SystemSetting } from "@/settings/systemSetting";
import { getSupplier } from "@/services/api";

export default function useGteDoctor() {
  const messages = useMessages();

  /** 供应商列表 */
  const supplierList = ref([]);

  const searchValue = ref(null);

  const structPathList = ref(null);

  /** 供应商列表参数 */
  const _params = {
    data: {
      doctorName: "",
    },
    pageVO: {
      current: 1,
      size: 10,
    },
  };
  /** 总记录数 */
  let recordsTotal = 1;

  const { loading } = useLoading();
  const { loading: isGetLoading, startLoading: startGetLoading, endLoading: endGetLoading } = useLoading();
  /** 获取供应商列表 */
  const getDoctorList = async () => {
    try {
      startGetLoading();
      _params.data.doctorName = searchValue.value;
      const { total, current, size, records } = await getSupplier(_params);
      _params.pageVO.current = Number(current);
      _params.pageVO.size = Number(size);
      recordsTotal = Number(total);
      if (records && _params.pageVO.current === 1) {
        supplierList.value = [...records];
      } else {
        records.forEach(item => {
          supplierList.value.push(item);
        });
      }
    } catch (error) {
      messages.createMessageError("获取供应商列表失败" + error);
    } finally {
      endGetLoading();
    }
  };

  /** 滚动加载 */
  const handleScroll = e => {
    const currentTarget = e.currentTarget as HTMLElement;
    if (currentTarget.scrollTop + currentTarget.offsetHeight >= currentTarget.scrollHeight) {
      if (_params.pageVO.current * _params.pageVO.size < recordsTotal) {
        _params.pageVO.current++;
        getDoctorList();
      }
    }
  };

  return {
    loading,
    isGetLoading,
    supplierList,
    searchValue,
    _params,
    structPathList,
    getDoctorList,
    handleScroll,
  };
}
