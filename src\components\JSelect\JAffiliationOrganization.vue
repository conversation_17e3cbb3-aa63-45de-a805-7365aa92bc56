<template>
    <n-tree-select
      :value="props.value"
      :options="processedData"
      @update:value="handleUpdateValue"
      :on-load="handleLoad"
      :loading="loading"
      clearable
      :style="{ width: `${props.width}px` }"
      :disabled="disabled"
      :default-expanded-keys="props.structureName != null ? props.defaultExpandedKeys : []"
    >
      <template #empty>
        <div class="empty-wrapper">
          <img :src="EmptySrc" alt="" />
          <p>暂无数据</p>
        </div>
      </template>
    </n-tree-select>
  </template>

<script lang="ts" setup>
import EmptySrc from "@/assets/image/exception/empty.png";
import { onMounted, ref, watch } from 'vue'
import { structureListStructures } from '@/services/api';
import { useMessages } from "@/hooks";

const loading = ref(false)

const disabled = ref(false)

const defaultExpandedKeys = ref()

// 定义响应式数据
const processedData = ref([]); // 处理后的最终数据

// 归属组织响应式数据
const ascriptionData = ref([]); // 处理后的最终数据

/* 提示信息 */
const message = useMessages();

/* Props */
const props = withDefaults(
  defineProps<{
    value?: Array<string | number> | string | number | null; // 选择的值
    width?: string | number,
    structureName?:string | number,//归属名称
    defaultExpandedKeys?:Array<string | number> | string | number | null; //默认展开节点的 key
  }>(),
  {
    width:400,
    structureName:null,
  },
);

const emits = defineEmits<{
    (e: "update:value", selectValue: any): void; // 更新选择值事件
    (e: "update:defaultExpandedKeys", selectValue: any): void; // 更新选择值事件
  }>();

//回调事件
const handleUpdateValue = (value) =>{
    emits("update:value", value);
}

//异步加载
const handleLoad = (option) => {
  return new Promise<void>(async(resolve) => {
      option.children = await getChild(option); 
      resolve();
  });
};

//请求子级
const getChild = async (option) => {
  const params = {
    data: {
      parentCode: option.code,
    },
  };
  try {
    const res = await structureListStructures(params); 
    const children = []
    res.forEach(item => {
        children.push ( {
        label: item.name, // 替换 name 为 label
        key: item.id, // 替换 parentCode 为 key
        isLeaf: !item.hasChildren,
        depth: option.depth + 1,
        code:item.code
       })
    });
    return children
  } catch (err) {
    message.createMessageError("获取归属组织失败: " + err);
    return []; 
  }
};

//请求父级
const getStructureListStructures = async() =>{
    loading.value = true
    if(props.structureName != null){
       disabled.value = true
    }
    const params = {
        data:{
           parentCode:0,
        }
    }
    try{
      const res = await structureListStructures(params)
      if(props.structureName != null){
        await getAttributionRequests(res)
      }else{
        processedData.value = processData(res);
      }
    }catch(err){
        message.createMessageError('获取归属组织失败:' + err)
    }finally{
        loading.value = false
        if(props.structureName != null){
          disabled.value = false
        }
    }
}

//归属请求
const getAttributionRequests = async(value) =>{
    ascriptionData.value = processData(value);
    loading.value = true
    const params = {
        data:{
           id:props.value,
        }
    }
    try{
      const res = await structureListStructures(params)
      if(res[0].parents.length != 0){
          defaultExpandedKeys.value = ref(
            res.flatMap(item => [
              item.id, 
              ...item.parents.map(parent => parent.id)
            ])
          );
          emits("update:defaultExpandedKeys", defaultExpandedKeys.value);
          processedData.value = buildTree(ascriptionData.value, res)
      }else{
          emits("update:defaultExpandedKeys", []);
          processedData.value = ascriptionData.value
      }
    }catch(err){
        message.createMessageError('获取归属组织失败:' + err)
    }finally{
        loading.value = false
    }
}

// 转换函数
function buildTree(ParentVal, ChilVal) {
  const map = new Map();

  // 遍历 ParentVal key 映射为对应的对象
  ParentVal.forEach(item => {
    map.set(item.key, { ...item }); // 初始化时不添加 children
  });

  // 遍历 ChilVal，根据其父节点构建嵌套结构
  ChilVal.forEach(item => {
    item.parents.forEach((parent, index) => {
      // 查找或创建当前节点
      let parentNode = map.get(parent.id);
      if (!parentNode) {
        parentNode = {
          label: parent.name,
          key: parent.id,
          // isLeaf: !parent.hasChildren,
          code: parent.code,
          depth: parent.level
        };
        map.set(parent.id, parentNode);
      }

      // 添加到父节点的 children（如果需要）
      if (index > 0) {
        const grandParent = map.get(item.parents[index - 1].id);
        if (grandParent) {
          if (!grandParent.children) grandParent.children = [];
          const exists = grandParent.children.find(child => child.key === parent.id);
          if (!exists) grandParent.children.push(parentNode);
        }
      }
    });

    // 将最后一级节点（当前数据对象）添加到对应父节点
    const lastParent = map.get(item.parents[item.parents.length - 1].id);
    if (lastParent) {
      if (!lastParent.children) lastParent.children = [];
      lastParent.children.push({
        label: item.name,
        key: item.id,
        isLeaf: !item.hasChildren,
        code: item.code,
        depth: item.level
      });
    }
  });

  // 清理空的 children 属性（即没有子节点时移除 children）
  map.forEach(node => {
    if (node.children && node.children.length === 0) {
      delete node.children;
    }
  });

  // 将 map 转换为数组结构，并返回
  return Array.from(map.values()).filter(item => item.depth === 1);
}


// 数据处理函数
const processData = (data) => {
  return data.map(item => {
    const result = {
      label: item.name, // 替换 name 为 label
      key: item.id, // 替换 parentCode 为 key
      isLeaf: !item.hasChildren, //是否有子级请求
      code:item.code,
      depth: 1,
    };
    return result;
  });
};

onMounted(() => {
    getStructureListStructures()
})

</script>
<style scoped lang="less">
.empty-wrapper {
  img {
    height: 50px;
  }
  p {
    color: #666;
    font-size: 12px;
    text-align: center;
  }
}
</style>