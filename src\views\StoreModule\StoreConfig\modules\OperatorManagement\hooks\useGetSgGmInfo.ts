
import { ref } from "vue";
import { SystemSetting } from "@/settings/systemSetting";
import { useLoading, useMessages } from "@/hooks";
import { getSgGmInfo } from "@/services/api";

export default function useGetSgGmInfo() {
    const messages = useMessages();

    /** 群管列表 */
    const selectDataList = ref([]);

    const searchValue = ref('');
    
    /** 群管搜索参数 */
    const _params = {
    	data: {
            searchValue: searchValue.value,
            dealerIds: null
    	},
    	pageVO: {
    		current: 1,
    		size: SystemSetting.pagination.pageSize,
    	},
    };

    /** 总记录数 */
    let recordsTotal = 1;

    const { loading, startLoading, endLoading } = useLoading();

    /** 获取群管列表 */
    const getSgGmInfoList = async () => {
        try {
            startLoading();
            _params.data.searchValue = searchValue.value;
            const { total, current, size, records } = await getSgGmInfo(_params);
            _params.pageVO.current = Number(current);
            _params.pageVO.size = Number(size);
            recordsTotal = Number(total);
            if (records && _params.pageVO.current === 1) {
              selectDataList.value = [...records];
            } else {
              records.forEach(item => {
                selectDataList.value.push(item);
              });
            }
        } catch (error) {
            messages.createMessageError("获取群管列表失败" + error);
        } finally {
            endLoading();
        }
    };

    /** 滚动加载 */
    const handleScroll = (e) => {
    	const currentTarget = e.currentTarget as HTMLElement;
    	if (
    		currentTarget.scrollTop + currentTarget.offsetHeight >=
    		currentTarget.scrollHeight
    	) {
    		if (_params.pageVO.current * _params.pageVO.size < recordsTotal) {
    			_params.pageVO.current++;
    			getSgGmInfoList();
    		}
    	}
    };

    return {
        loading,
        _params,
        searchValue,
        selectDataList,
        getSgGmInfoList,
        handleScroll
    }
}