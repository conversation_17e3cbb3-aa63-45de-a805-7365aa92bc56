import { defHttp } from '@/services';

/** 分账入账方规则设置 */
export const enum SubAccountPayeeRuleSettingApi {
    /** 获取分账入账方规则设置 */
    subAccountPayeeRuleSettingGet = '/globalConfigs/getAllocationRuleConfig',
    /** 更新分账入账方规则设置 */
    subAccountPayeeRuleSettingUpdate = '/globalConfigs/updateAllocationRuleConfig'
}

/** 获取分账入账方规则设置 */
export function subAccountPayeeRuleSettingGet() {
    return defHttp.post({
        url: SubAccountPayeeRuleSettingApi.subAccountPayeeRuleSettingGet,
        params: {
        }
    });
}

/** 更新分账入账方规则设置 */
export function subAccountPayeeRuleSettingUpdate(params) {
    return defHttp.post({
        url: SubAccountPayeeRuleSettingApi.subAccountPayeeRuleSettingUpdate,
        params,
    });
}