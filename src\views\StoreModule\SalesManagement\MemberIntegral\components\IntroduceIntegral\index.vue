<template>
    <div class="introduce-integral-wrapper">
        <div class="left">
            <n-card title="会员积分" :bordered="false" size="small">
                <p>
                    在此模块中，包含了对积分商品首页大部分模块的设置。<br />
                    包括用户可以获得积分的数量，新建或编辑积分商品，对于一些模块的设置与启用。<br />
                    通过对这些功能的设置，可以达到对销售商品起到激励、增加用户日活跃度、滞销商品的转化等目的。<br />
                </p>
            </n-card>
            <n-card :bordered="false" size="small">
                <n-button type="primary" @click="props.toggleActive">进入使用</n-button>
            </n-card>
        </div>
        <div class="right">
            <n-scrollbar style="height: 100%;">
                <n-image :src="integral" width="390" />
            </n-scrollbar>
            <span style="margin-top: 12px; font-weight: 600;">积分首页样式</span>
        </div>
    </div>
</template>

<script lang="ts" setup name='IntroduceIntegral'>
import integral from "@/assets/image/integral/integral.png"

/** props */
const props = defineProps<{
    toggleActive: () => void; 
}>();

</script>


<style lang="less" scoped>
.introduce-integral-wrapper {
    width: 100%;
    height: 100%;
    padding: 12px 32px 12px 12px;
    background-color: #fff;
    box-sizing: border-box;
    display: flex;
    .left {
        flex: 1;
    }
    .right {
        min-width: 390px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
}
</style>