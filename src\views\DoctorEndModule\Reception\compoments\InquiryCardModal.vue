<template>
  <n-modal
      :show="show"
      preset="card"
      draggable
      :auto-focus="false"
      style="width: 1560px;min-width: 1560px;height: 912px"
      @close="()=>show = false"
  >
    <NSpin :show="isPageLoading">
      <n-grid :cols="12" style="height: 800px">
        <n-gi :span="3">
          <div class="video-container">
            <RTCLive :RTCRoomInfo="RTCRoomInfoRef"></RTCLive>
            <div v-if="countDownText" class="video-count-down-alert">
              <n-alert :show-icon="false" type="warning" closable style="background-color: #fefbea">
                <span style="color:#eaaa82;font-weight: 800">
                  {{`${countDownText}后自动结束视频`}}
                </span>
              </n-alert>
            </div>
          </div>
        </n-gi>
        <n-gi :span="9" style="display: flex;flex-direction: column">
          <div style="flex: 1">
            <div style="margin-left: 48px">
              <!-- 标题区域 -->
              <div class="card-header">
                <span class="card-title">问诊卡</span>
                <n-tag type="success" size="small" :bordered="false">{{ titleTag }}</n-tag>
              </div>

              <!-- 患者信息列 -->
              <n-grid :cols="24" y-gap="12" class="patient-info">
                <n-gi :span="24">
                  <n-text depth="3" class="info-label">患者姓名：</n-text>
                  <n-text>{{ patientName }}</n-text>
                </n-gi>
                <n-gi :span="24">
                  <n-divider/>
                </n-gi>
                <n-gi :span="24">
                  <n-text depth="3" class="info-label">主诉：</n-text>
                  <n-text>{{ chiefComplaint }}</n-text>
                </n-gi>
                <n-gi :span="24">
                  <n-text depth="3" class="info-label">患病时间：</n-text>
                  <n-text>{{ startIllnessTime }}</n-text>
                </n-gi>
                <n-gi :span="24">
                  <n-text depth="3" class="info-label">肝功能：</n-text>
                  <n-text>{{ isLiver }}</n-text>
                </n-gi>
                <n-gi :span="24">
                  <n-text depth="3" class="info-label">肾功能：</n-text>
                  <n-text>{{ isKidney }}</n-text>
                </n-gi>
                <n-gi :span="24">
                  <n-text depth="3" class="info-label">过敏史：</n-text>
                  <n-text>{{ hasAllergyHi }}</n-text>
                </n-gi>
                <n-gi :span="24">
                  <n-text depth="3" class="info-label">个人病史：</n-text>
                  <n-text>{{ isPersonalMedicalHi }}</n-text>
                </n-gi>
                <n-gi :span="24">
                  <n-text depth="3" class="info-label">家族病史：</n-text>
                  <n-text>{{ homeMedicalHi }}</n-text>
                </n-gi>
                <n-gi :span="24">
                  <n-text depth="3" class="info-label">备孕、妊娠、哺乳：</n-text>
                  <n-text>{{ isPreparePregnant }}</n-text>
                </n-gi>
                <n-gi :span="24" v-if="showPic">
                  <n-text depth="3" class="info-label">照片：</n-text>
                  <n-grid x-gap="0" y-gap="12" :cols="4" class="image-placeholders" style="width: 500px">
                    <n-gi v-for="(item,index) in medicalRecordsData" :key="index" class="image-placeholder">
                      <NImage
                          :src="item"
                          width="90"
                          height="90"
                      />
                    </n-gi>
                  </n-grid>
                </n-gi>
              </n-grid>
            </div>
          </div>
          <div>
            <n-space justify="end" style="padding-bottom: 4px;">
              <n-button
                  v-if="userStore.userInfo?.hasAddPreAuth"
                  size="small"
                  :disabled="isPageLoading"
                  type="primary"
                  @click="()=>{openVideoPrescription()}">
                开具处方
              </n-button>
              <n-button size="small" :disabled="isPageLoading" type="warning" @click="clickCancelReception">
                结束问诊
              </n-button>
            </n-space>
          </div>
        </n-gi>
      </n-grid>
    </NSpin>
  </n-modal>
  <VideoPrescriptionModal ref="videoPrescriptionModalRef"/>
</template>

<script setup lang="ts">
import {computed, nextTick, ref, watch} from "vue";
import {useMessages} from "@/hooks";
import {isNullOrUnDef, transformMinioSrc} from "@/utils";
import {doctorGetPresCardInfo, doctorPresCompletePres} from "@/services/api/doctorEndApi";
import VideoPrescriptionModal from "@/views/DoctorEndModule/Reception/compoments/VideoPrescriptionModal.vue";
import RTCLive from "@/components/RTCLive/index.vue"
import {getRTCToken} from "@/services/api/rtcApi";
import {type RTCBaseConfig} from "@/components/RTCLive/types";
import {useRTCLive} from "@/components/RTCLive/hooks/useRTCStatus";
import {useUserStore} from "@/stores/modules/user";

enum PeriodKey {
  Week = 1,
  Month,
  HalfYear,
  BeyondHalfYear,
}

// 创建映射对象
const PeriodMap: { [key in PeriodKey]: string } = {
  [PeriodKey.Week]: "一周内",
  [PeriodKey.Month]: "一个月内",
  [PeriodKey.HalfYear]: "半年内",
  [PeriodKey.BeyondHalfYear]: "半年以上",
};

enum StatusKey {
  Normal = 0,
  Abnormal = 1
}

const StatusMap: { [key in StatusKey]: string } = {
  [StatusKey.Normal]: "正常",
  [StatusKey.Abnormal]: "异常",
}

// 存在性枚举：直接表达业务含义
enum PresenceKey {
  NonExistent = 0,
  Exist = 1,
}

const isInitRTCRef = ref(false)
const PresenceMap: { [key in PresenceKey]: string } = {
  [PresenceKey.NonExistent]: "无",
  [PresenceKey.Exist]: "有",
}

const userStore = useUserStore();
/* 提示信息 */
const message = useMessages();
/* 模态框显隐状态 */
const show = ref(false);
/* 问诊卡数据 */
const receptionData = ref()
/* 页面加载 */
const isPageLoading = ref(false)
/* 开处方模态框 */
const videoPrescriptionModalRef = ref()
const RTCRoomInfoRef = ref<RTCBaseConfig>()
/* 问诊结束结束倒计时 */
let countDownTimer = null;
/* 倒计时5分钟显示文本 */
let countDownText = ref('')

const patientName = computed(() => {
  const age = receptionData.value?.patientAge ? `${receptionData.value?.patientAge}岁` : ''
  const sex = receptionData.value?.patientSex ? `${receptionData.value?.patientSex}` : ''
  const bracket = (age || sex) ? `(${age} ${sex})` : ''
  return `${receptionData.value?.patientName || '-'}${bracket}`
})

const hasAllergyHi = computed(() => {
  return `${receptionData.value?.isAllergyHi || '-'}`
})

const homeMedicalHi = computed(() => {
  return `${receptionData.value?.isHomeMedicalHi || '-'}`
})

const isPersonalMedicalHi = computed(() => {
  return `${receptionData.value?.isPersonalMedicalHi || '-'}`
})


const chiefComplaint = computed(() => {
  return `${receptionData.value?.chiefComplaint || '-'}`
})

const startIllnessTime = computed(() => {
  return !isNullOrUnDef(receptionData.value?.period) ? PeriodMap[receptionData.value.period] : '-'
})

const isPreparePregnant = computed(() => {
  return !isNullOrUnDef(receptionData.value?.isPreparePregnant) ? PresenceMap[receptionData.value.isPreparePregnant] : '-'
})

const isLiver = computed(() => {
  return !isNullOrUnDef(receptionData.value?.isLiver) ? StatusMap[receptionData.value.isLiver] : '-'
})

const isKidney = computed(() => {
  return !isNullOrUnDef(receptionData.value?.isKidney) ? StatusMap[receptionData.value.isKidney] : '-'
})

const titleTag = computed(() => {
  return receptionData.value?.type == 1 ? '常规问诊' : '预约问诊'
})

const showPic= computed(() => {
  return receptionData.value?.type !== 2
})

const medicalRecordsData = computed(() => {
  const result = [];
  if (receptionData.value) {
    // 遍历对象自身可枚举属性键
    Object.keys(receptionData.value).forEach(key => {
      // 检测键名是否包含目标字符串（不区分大小写）
      if (key.includes('medicalRecords')) {
        result.push(transformMinioSrc(receptionData.value[key]));
      }
    });
  }

  return result;
})


// 退诊api
let cancelDiagnosisApi = null
// 行数据
let rowData = null;
let refreshFunction = null
const acceptParams = (params) => {
  // receptionData.value = params.content
  cancelDiagnosisApi = params.api || null
  rowData = params.rowData || null
  refreshFunction = params.refresh

  show.value = true
  getData()
};

/* 获取问诊卡数据 */
async function getData() {
  if (!rowData?.id) {
    return
  }
  try {
    isPageLoading.value = true
    const res = await doctorGetPresCardInfo({id: rowData.id});
    receptionData.value = res
    stopCountDownTimer()
    const rtcTokenResp = await getRTCToken(rowData.id)
    RTCRoomInfoRef.value = {
      roomId: rtcTokenResp.roomId,
      appId: rtcTokenResp.appId,
      token: rtcTokenResp.token,
      userId: rtcTokenResp.userId
    }

    
    receptionData.value.receiveEndTime = rtcTokenResp?.receiveEndTime
    countDownTimer = setInterval(updateCountdown, 1000)
  } catch (error) {

  } finally {
    isPageLoading.value = false
  }
}

// 预约问诊-结束问诊
const clickCancelReception = async () => {
  if (!rowData?.id) {
    return
  }
  try {
    await doctorPresCompletePres({id: rowData.id})
    message.createMessageSuccess('结束问诊成功')
    refreshFunction && refreshFunction()
    closeModal()
  } catch (error) {
    message.createMessageError(`结束问诊失败:${error}`)
  }
}

/* 开具处方 */
async function openVideoPrescription() {
  const _params = {
    show: true,
    mode: 'add',
    params: {
      id: receptionData.value.id
    },
  }
  videoPrescriptionModalRef.value?.acceptParams(_params)
}

/* 表单实例 */
const formRef = ref(null);
const {disconnectRTC} = useRTCLive()
/* 表单参数初始化 */
/* 清空表单 */
const formDataReset = () => {
  cancelDiagnosisApi = null
  rowData = null
};
/* 关闭弹窗 */
const closeModal = () => {
  formDataReset();
  isLoading.value = false;
  // 弹窗取消
  show.value = false;
};

async function updateCountdown() {
  if (!receptionData.value?.receiveEndTime) return;

  // 将结束时间转换为时间戳
  const endTimestamp = new Date(receptionData.value.receiveEndTime).getTime();
  const nowTimestamp = Date.now();
  // 计算剩余时间(毫秒)
  const remainingMs = endTimestamp - nowTimestamp;

  if (remainingMs <= 0) {
    // 问诊已结束
    clearInterval(countDownTimer);
    countDownTimer = null;
    countDownText.value = ""; // 清空倒计时文本
    /* 挂断视频 */
    await disconnectRTC()
    setTimeout(()=>{
       clickCancelReception()
    },300)
    return;
  }

  // 计算剩余分钟数和秒数
  const remainingMinutes = Math.floor(remainingMs / (1000 * 60));
  const remainingSeconds = Math.floor((remainingMs % (60000)) / 1000);

  // 转换为天、小时、分钟、秒
  const days = Math.floor(remainingMs / (1000 * 60 * 60 * 24));
  const hours = Math.floor((remainingMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((remainingMs % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((remainingMs % (1000 * 60)) / 1000);

  const daysStr = days > 0 ? `${days}天` : '';
  const hoursStr = hours > 0 ? `${hours}小时` : '';
  const minutesStr = minutes > 0 ? `${minutes}分钟` : '';
  const secondsStr = seconds > 0 ? `${seconds}秒` : '';

  // 当剩余5分钟时显示倒计时
  if (remainingMinutes < 5 || (remainingMinutes === 5 && remainingSeconds === 0)) {
    countDownText.value = `${daysStr}${hoursStr}${minutesStr}${secondsStr}`;
  } else {
    countDownText.value = ""; // 超过5分钟不显示
  }
  console.log(`倒计时剩余${daysStr}${hoursStr}${minutesStr}${secondsStr}`)
}

const stopCountDownTimer = () => {
  if (countDownTimer) {
    clearInterval(countDownTimer)
    countDownTimer = null
  }
}

watch(show, async (newVal) => {
  stopCountDownTimer()
  if (!newVal) {
    await disconnectRTC()
  }
})

/* 确认--保存 */
const isLoading = ref(false);
defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less">
:deep(.n-divider:not(.n-divider--vertical)) {
  margin-top: 0;
  margin-bottom: 0
}

:deep(.n-card > .n-card__content, .n-card > .n-card__footer) {
  padding-bottom: 24px;
}

:deep(.n-alert .n-alert-body) {
  padding: 8px;
}

:deep(.n-alert .n-alert__close) {
  margin: 10px 8px 0 0;
}

.video-container {
  position: relative;
  border: 1px solid #ddd;
  width: 375px;
  height: 812px;
  border-radius: 15px;
  overflow: hidden;
}

.video-count-down-alert {
  z-index: 99;
  position: absolute;
  top: 20px;
  width: 80%;
  left: 50%;
  transform: translateX(-50%);
}

.consult-card {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  max-width: 680px;
  padding: 0px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.card-title {
  font-size: 20px;
  color: #1d2129;
}

.patient-info,
.medical-info {
  margin: 16px 0;
}

.info-label {
  flex: 0 0 80px;
  text-align: left;
  color: #666666;
  font-size: 16px;
}

.content-section {
  margin: 20px 0;
}

.card-divider {
  margin: 20px 0;
  background-color: #f0f0f0;
}

.image-placeholders {
  width: 500px;

}

.image-placeholder {
  height: 90px;
}

@media (max-width: 640px) {
  .consult-card {
    padding: 16px;
  }

  .info-label {
    flex-basis: 70px;
  }

  .image-placeholder {
    width: 100%;
    height: 80px;
  }
}

.image-placeholders {
  gap: 16px;
  margin-top: 16px;
}
</style>

