<template>
  <div class="wrapper inner-page-height">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :pagination="paginationRef"
      @paginationChange="paginationChange"
      :isNeedCollapse="false"
      :isDisplayIndex="false"
      :isTableSelection="false"
    >
      <!-- 表单 -->
      <template #searchForm>
        <n-form
          ref="formRef"
          :model="formValue"
          label-placement="left"
          label-width="auto"
          :show-feedback="false"
          require-mark-placement="right-hanging"
          size="small"
          :style="{ width: '100%' }"
        >
          <n-form-item label="">
            <n-input-group>
              <n-select
                v-model:value="formValue.searchType"
                placeholder="请选择"
                :options="searchTypeOptions"
                style="width: 120px"
              />
              <j-search-input
                v-model:value="formValue.searchValue"
                placeholder="请输入"
                @search="handlerSearch"
                :width="240"
              />
            </n-input-group>
          </n-form-item>
          <n-form-item label="订单号">
            <j-search-input
              v-model:value="formValue.orderNo"
              placeholder="请输入"
              @search="handlerSearch"
              :width="240"
            />
          </n-form-item>
          <n-form-item label="结算状态">
            <n-select
              v-model:value="formValue.settlementStatus"
              placeholder="请选择"
              :options="settlementStatusOptions"
              @update:value="tableSearch"
              style="width: 120px"
              clearable
            />
          </n-form-item>

          <n-form-item label="结算时间">
            <j-date-range-picker
              v-model:value="formValue.settlementTime"
              type="datetimerange"
              format="yyyy-MM-dd"
              :default-time="['00:00:00', '23:59:59']"
              clearable
            />
          </n-form-item>
          <n-form-item label="打款方式">
            <n-select
              v-model:value="formValue.paymentMethod"
              placeholder="请选择"
              :options="payoutMethodOptions"
              @update:value="tableSearch"
              style="width: 120px"
              clearable
            />
          </n-form-item>
        </n-form>
      </template>

      <template #tableHeaderBtn>
        <n-button @click="refresh" :loading="isLoading" class="store-button">刷 新</n-button>
        <n-button
          @click="exportData"
          class="store-button"
          type="primary"
          :loading="exportLoading"
          v-if="hasStoreCommissionDetailExportAuth"
        >
          导 出
        </n-button>
      </template>
    </FormLayout>
    <storeCommissionInfo v-model:show="isShow" :detailData="detailData" />
  </div>
</template>

<script lang="tsx" setup name="DoctorManagement">
import { onMounted, ref, watch } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
// import { getMedicalInquiryForm, storeCommissionExport } from "@/services/api";
import { useMessages } from "@/hooks";
import Popconfirm from "@/components/Popconfirm/index.vue";
import moment from "moment";
import { storeCommissionPage, storeCommissionExport, storeCommissionDetail, type StoreCommissionDetailDTO } from "@/services/api";
import TablePreview from "@/components/TablePreview/index.vue";
import { transformMinioSrc } from "@/utils/fileUtils"
import storeCommissionInfo from "./components/storeCommissionInfo.vue";
import { hasStoreCommissionDetailExportAuth, hasStoreCommissionDetailDetailAuth } from "@/views/StoreModule/Finance/authList";
const { createMessageSuccess, createMessageError } = useMessages();

/** 表格hook */
const { isLoading, tableData, paginationRef, pageTableData, paginationChange } = useTableDefault({
  pageDataRequest: storeCommissionPage,
});

const isShow = ref(false);

/** 详情 */
const detailData = ref<StoreCommissionDetailDTO>({});

/** 搜索类型 */
const searchTypeOptions = [
  {
    label: "门店ID",
    value: 1,
  },
  {
    label: "店长ID",
    value: 2,
  },
  {
    label: "归属用户ID",
    value: 3,
  },
  {
    label: "经销商ID",
    value: 4,
  }
];

/** 打款方式选项 */
const payoutMethodOptions = [
  {
    label: "用户提现",
    value: 1,
  },
  {
    label: "线下打款",
    value: 2,
  },
];
/** 结算状态选项 */
const settlementStatusOptions = [
  {
    label: "待结算",
    value: 1,
  },
  {
    label: "已结算",
    value: 2,
  },
  {
    label: "已取消",
    value: 3,
  },
];
/* 1=待结算；2=已结算；3=已取消;4=失败;5=结算中 */
const orderStatus = ["待结算", "已结算", "已取消", "失败", "结算中"];
/** 参数 */
const formValue = ref({
  searchValue: "",
  searchType: 1,
  /** 订单号 */
  orderNo:"",
  /** 结算时间 */
  settlementTime: null,
  /** 结算状态 */
  settlementStatus: null,
  /** 打款方式 */
  paymentMethod: null,
});
/* 表格列表项 */
const tableColumns = ref([
  {
    title: "门店ID",
    key: "storeShortId",
    width: 100,
    align: "left",
    fixed: "left",
  },
  {
    title: "门店名称",
    key: "storeName",
    width: 180,
    align: "left",
    fixed: "left",
  },
  {
    title: "订单号",
    key: "orderNo",
    align: "left",
  },
  {
    title: "归属用户ID",
    key: "clerkShortId",
    align: "left",
  },
  {
    title: "归属用户昵称",
    key: "clerkNickname",
    align: "left",
  },
  {
    title: "归属用户佣金",
    key: "clerkCommission",
    align: "left",
    render: row => {
      return <div>{ row.clerkCommission ? (row.clerkCommission / 100).toFixed(2) : '0.00'}</div>;
    },
  },
  {
    title: "店长ID",
    key: "managerShortId",
    align: "left",
    render: row => {
      return <div>{ row.managerShortId ? row.managerShortId : "-"}</div>;
    },
  },
  {
    title: "店长昵称",
    key: "managerNickname",
    align: "left",
  },
  {
    title: "店长佣金",
    key: "managerCommission",
    align: "left",
    render: row => {
      return <div>{ row.managerCommission ? (row.managerCommission / 100).toFixed(2) : '0.00'}</div>;
    },
  },
  {
    title: "经销商用户ID",
    key: "dealerShortId",
    align: "left",
  },
  {
    title: "经销商姓名",
    key: "dealerNickname",
    align: "left",
  },
  {
    title: "经销商佣金",
    key: "dealerCommission",
    align: "left",
    render: row => {
      return <div>{ row.dealerCommission ? (row.dealerCommission / 100).toFixed(2) : '0.00'}</div>;
    },
  },
  {
    title: "结算状态",
    key: "settlementStatus",
    align: "left",
    render: row => {
      return <div>{orderStatus[row.settlementStatus - 1]}</div>;
    },
  },

  {
    title: "结算时间",
    key: "settlementTime",
    align: "left",
  },
  {
    title: "打款方式",
    key: "paymentMethod",
    align: "left",
    render: row => {
      return <div>{row.paymentMethod === 1 ? "用户提现" : "线下结算"}</div>;
    },
  },
  {
    title: "创建时间",
    key: "createTime",
    align: "left",
  },
  {
    title: "操作",
    key: "action",
    width: 120,
    fixed: "right",
    align: "left",
    render: row => {
      return (
        <n-space style="padding: 5px 0;">
           {hasStoreCommissionDetailDetailAuth ? (
            <n-button text type="primary" onClick={() => handlerDetailDrawerClick(row)}>
              详情
            </n-button>
          ) : null}
        </n-space>
      );
    },
  },
]);
watch([() => formValue.value.settlementTime], () => {
  tableSearch();
});
/** 获取参数 */
const getParams = () => {
  const { searchType, searchValue, settlementTime, settlementStatus, orderNo, paymentMethod } = formValue.value;
  const settlementStartTime = settlementTime ? moment(settlementTime[0]).format(`YYYY-MM-DD HH:mm:ss`) : null;
  const settlementEndTime = settlementTime ? moment(settlementTime[1]).format(`YYYY-MM-DD HH:mm:ss`) : null;
  return {
    searchType,
    searchValue,
    settlementStartTime,
    settlementEndTime,
    settlementStatus,
    orderNo,
    paymentMethod
  };
};

/** 搜索 */
const handlerSearch = () => {
  tableSearch();
};

/** 表格刷新 */
function refresh() {
  tableSearch();
}

/* 表格搜索 */
const tableSearch = () => {
  pageTableData(getParams(), paginationRef.value);
};

// const handlerRefund = row => {
//   data.value = row;
//   isShow.value = true;
// };
/** 打开详情 */
const handlerDetailDrawerClick = row => {
  isLoading.value = true
  storeCommissionDetail({ id: row.id }).then(res => {
    detailData.value = res;
    isShow.value = true;
  }).catch(err => {
    createMessageError(`获取详情失败:${err}`);
  }).finally(() => {
    isLoading.value = false;
  });
};

const exportLoading = ref<boolean>(false);
const exportData = () => {
  exportLoading.value = true;
  storeCommissionExport({
    data: { ...getParams() },
    pageVO: {
      current: paginationRef.value.current,
      size: paginationRef.value.pageSize,
    },
  })
    .then(res => {
      createMessageSuccess("导出成功");
    })
    .catch(err => {
      createMessageError(`导出失败:${err}`);
    })
    .finally(() => {
      exportLoading.value = false;
    });
};

/** 组件挂载 */
onMounted(() => {
  tableSearch();
});
</script>

<style lang="less" scoped>
@import "@/styles/defaultVar.less";
</style>
