import type { ECOption } from "@/hooks/useEcharts";
import { getOrderReportsChart } from "@/services/api";
import { useMessages, useLoading } from "@/hooks";

export default function useOrderEcharts() {
    const { createMessageError } = useMessages();

    /** 是否加载 */
    const { loading: isGetLoading, startLoading, endLoading } = useLoading();

    const lineColor = ["#1677FF"]; // 折线图颜色

    /** 图表配置 */
    const lineOptions: ECOption = {
        title: {
            left: "center",
            text: "订单数",
            textStyle: {
                fontSize: 30,
            },
        },

        grid: {
            left: "7%",
            right: "3%",
            bottom: "25",
        },
        // 横坐标轴体配置
        xAxis: {
            type: "category",
            data: [],
            axisLabel: {
                show: true,
                interval: "auto",
                lineHeight: 18,
                width: 100,
                overflow: "truncate",
                // showMinLabel: true,
                // showMaxLabel: true
            },
            axisTick: {
                show: false, // 是否显示刻度
            },
        },
        tooltip: {
            trigger: "axis",
            formatter(params) {
                const [firstElement] = params;
                let labelTpl = ``;
                params.forEach((element, index) => {
                    return (labelTpl =
                        labelTpl +
                        `<div class="content-panel"><div class='label-wrapper'><div class='label' style='background-color:${lineColor[index]
                        }'></div><span>${element.seriesName}</span></div><span class="tooltip-value number-font">${Number(
                            element.value,
                        )}</span></div>`);
                });
                return `<div>
            <p class="tooltip-title">${firstElement.axisValueLabel}</p>
            ${labelTpl}
          </div>`;
            },
            className: "echarts-tooltip-diy",
        },
        // 纵坐标轴体配置
        yAxis: [
            {
                type: "value",
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: "#EEEEEE",
                        type: "dashed",
                    },
                },
            },
        ],
        series: [
            {
                name: '订单数',
                data: [],
                type: "line",
                itemStyle: {
                    color: lineColor[0],
                },
            },
        ],
    };

    /** 获取图表数据 */
    async function getChartData(params, callback?: (data: any) => void) {
        try {
            startLoading();
            let data = await getOrderReportsChart(params);
            callback && callback(data);
        } catch (error) {
            createMessageError("获取图表数据失败：" + error);
        } finally {
            endLoading();
        }
    }

    return {
        lineOptions,
        isGetLoading,
        getChartData
    }
}
