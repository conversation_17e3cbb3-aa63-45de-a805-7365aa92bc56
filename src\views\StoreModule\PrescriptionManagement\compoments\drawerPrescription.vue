<template>
  <JDrawer
    v-model:show="drawerVisible"
    :title="prescriptionType[drawerProps?.type]"
    to="#prescription-management"
    id="product-details"
    :isGetLoading="loadShow"
    :isShowFooter="drawerProps?.type !== '详情'"
    @after-leave="closeDrawer"
    :contents-list="[
      {
        name: '用药人信息',
        slotName: 'user_info'
      },
      {
        name: usageState ? '商品相关病症' : '产品清单',
        slotName: 'goods-info'
      }
    ]"
  >
    <!-- 用药人信息 -->
    <template #user_info>
      <n-form
        ref="formRef"
        :model="model"
        :rules="rules"
        label-width="80"
        label-placement="left"
        require-mark-placement="right-hanging"
        size="small"
        :style="{
          width: '100%',
        }"
      >
        <n-grid cols="6 m:12 l:18 xl:24" :x-gap="32" responsive="screen">
          <!-- NO.1 -->
          <n-gi :span="6">
            <!-- 姓名 -->
            <n-form-item-gi label="姓名">
              <n-input v-model:value="model.nameValue" readonly />
            </n-form-item-gi>
            <!-- 性别 -->
            <n-form-item-gi label="性别">
              <n-select v-model:value="model.genderValue" disabled :options="[{label: '男', value: '男' },{label: '女', value: '女' }]" />
            </n-form-item-gi>
            <!-- 订单ID -->
            <n-form-item-gi label="订单ID">
              <n-input-group v-if="model.orderIdValue">
                <n-input v-model:value="model.orderIdValue" readonly style="width: 86%;" />
                <n-button v-copy="model.orderIdValue">复制</n-button>
              </n-input-group>
              <span v-else>-</span>
            </n-form-item-gi>
          </n-gi>
          <!-- NO.2 -->
          <n-gi :span="6">
            <!-- 手机号 -->
            <n-form-item-gi label="手机号">
              <n-input-group v-if="model.phoneValue">
                <n-input v-model:value="model.phoneValue" readonly style="width: 86%;" />
                <n-button v-copy="model.phoneValue">复制</n-button>
              </n-input-group>
              <span v-else>-</span>
            </n-form-item-gi>
            <!-- 来源 -->
            <n-form-item-gi label="来源">
              {{model.sourceValue ? model.sourceValue : '-'}}
            </n-form-item-gi>
          </n-gi>
          <!-- No.3 -->
          <n-gi :span="6">
            <!-- 身份证 -->
            <!-- <n-form-item-gi label="身份证"  >
              <n-input-group>
                <n-input v-model:value="model.identityCardValue" readonly style="width: 86%;" />
                <n-button v-copy="model.identityCardValue">复制</n-button>
              </n-input-group>
            </n-form-item-gi> -->
            <!-- 来源课程 -->
            <n-form-item-gi label="来源课程" >
              {{model.sourceCourseValue ? model.sourceCourseValue : '-'}}
            </n-form-item-gi>
          </n-gi>
        </n-grid>
      </n-form>
    </template>
    <!-- 商品相关病症、产品清单 -->
    <template #goods-info>
      <n-grid cols="6 m:12 l:18 xl:24" :x-gap="32" responsive="screen">
        <!-- 产品信息 -->
        <n-gi v-if="!usageState" :span="24">
          <div class="goods-title">
            <div class="title">
              <span style="margin-right: 10px;">疗法名称：{{model.frontName}}</span>  
              <JTextButton
                size="small" 
                type="primary" 
                @click="handleClickProductDetails" 
                :disabled="!model.productId ? true : false"
              > 
                {{model.productId ? '查看产品详情' : '暂无产品详情'}}
              </JTextButton>
            </div> 
            <div>编号：{{model.code}}</div>
          </div>
        </n-gi>
        <!-- 表格信息 -->
        <n-gi :span="23">
          <FormLayout
            style="height: 460px;"
            :tableData="tableData" 
            :tableColumns="usageState ? tableColumns : therapyTableColumns"
            :is-table-pagination="false"
            :is-table-selection="false"
            :is-display-header="false"
          >
          </FormLayout>
        </n-gi>
        <!-- 表单信息 -->
        <n-gi :span="24">
          <n-form
            ref="formRefSymptomsPrescriptions"
            :model="model"
            :rules="rules"
            label-width="auto"
            label-placement="left"
            require-mark-placement="right-hanging"
            :disabled="prescriptionType[drawerProps.type] == PrescriptionType.detailsPrescription ?  true : false"
            size="small"
            :style="{
              width: '100%',
            }"
          >
            <n-grid :cols="24" :x-gap="24">
              <!-- 选择医生 -->
              <n-form-item-gi :span="20" label="选择医生" path="selectDoctor" style="margin-top: 10px;">
                <JSelectNewDoctor v-model:value="model.selectDoctor" isImmediately style="width: 100%;" placeholder="请选择医生"></JSelectNewDoctor>
              </n-form-item-gi>
              <!-- 诊断说明 -->
              <n-form-item-gi :span="20" label="诊断说明" path="diagnosticDescription">
                <n-input type="textarea" placeholder="请输入诊断说明" round clearable v-model:value="model.diagnosticDescription" />
              </n-form-item-gi>
              <!-- 补充说明 -->
              <n-form-item-gi :span="20" label="补充说明" path="additionalRemarks">
                <n-input type="textarea" placeholder="请输入补充说明" round clearable v-model:value="model.additionalRemarks" />
              </n-form-item-gi>
              <!-- 处方上传 -->
              <n-form-item-gi :span="20" label="处方上传" >
                <div>
                  <CustomizeUpload 
                    v-model:value="model.rechargePicture" 
                    accept="image/*"  
                    :fileListSize="1" 
                    :max="1" 
                    :disabled="prescriptionType[drawerProps.type] == PrescriptionType.detailsPrescription ?  true : false"
                    size="small"
                  />
                  <div style="text-align:center; margin-top: 10px;">
                    {{model.rechargePicture != '' ? '已上传' : '注：处方文件可以在开处方后再进行上传'}}
                  </div>
                </div>
              </n-form-item-gi>
              <!-- 创建时间 -->
              <n-form-item-gi :span="8" label="创建时间" >
                {{model.creationTime ? model.creationTime : '-'}}
              </n-form-item-gi>
              <!-- 开方时间 -->
              <n-form-item-gi :span="8" label="开方时间" >
                {{model.prescriptionTime ? model.prescriptionTime : '-'}}
              </n-form-item-gi>
              <!-- 上传文件时间 -->
              <n-form-item-gi :span="8" label="上传文件时间" >
                {{model.uploadtime ? model.uploadtime : '-'}}
              </n-form-item-gi>
            </n-grid>
          </n-form>
        </n-gi>
      </n-grid>
    </template>
    <!-- footer -->
    <template #footer>
      <div class="footer-wrapper" >
        <n-space :style="{display:prescriptionType[drawerProps.type] == PrescriptionType.detailsPrescription ? 'none' : 'flex'}">
          <n-button ghost @click="closeDrawer" class="store-button">取 消</n-button>
          <n-button type="info" :loading="isLoading" @click="_save" class="store-button">
            保 存
          </n-button>
        </n-space>
      </div>
    </template>
      <!-- 新建药品商品 -->
      <!-- <NewDrugGoods 
        ref="newDrugGoodsRef" 
        to="#product-details" 
        :refreshTable="getDetails"
       /> -->
      <!-- 新建疗法商品 -->
      <NewTherapyGoods 
        ref="newTherapyGoodsRef" 
        to="#product-details" 
        :refreshTable="getDetails"
       />
  </JDrawer>
</template>
  
<script setup lang="tsx" name="DrawerPrescription">
import { ref, watch, reactive} from "vue";
import { useMessages, useLoading } from "@/hooks";
import { deepClone, isArray } from "@/utils";
import { PrescriptionType, GoodsCategoryType } from '@/enums'
import {orderSourceLabels} from '@/constants'
/** 详情相关组件 */
// import NewDrugGoods from "@/views/StoreModule/GoodsManagement/components/NewDrugGoods.vue";
import NewTherapyGoods from "@/views/StoreModule/GoodsManagement/components/NewTherapyGoods.vue";
import FormLayout from "@/layout/FormLayout.vue";
import JImage from "@/components/JImage/index.vue";

const { createMessageSuccess, createMessageError,createMessageWarning } = useMessages();
  
/* 表单参数初始化 */
const initParams = {
    id:null,//处方id
    nameValue:null,//姓名
    genderValue:null,//性别
    phoneValue:null, // 手机号
    identityCardValue:null,// 身份证
    sourceValue:null,//来源
    sourceCourseValue:null,//来源课程
    orderIdValue:null,//订单id
    selectDoctor:null,//选择医生
    diagnosticDescription:null,//诊断说明
    additionalRemarks:null,//补充说明
    prescriptionDocument:null,//处方文件
    creationTime:null,//创建时间
    prescriptionTime:null,//开方时间
    uploadtime:null,//上传文件时间
    dosageRpValue:null,//Rp取药数量
    rechargePicture:'',
    createBy:null,//创建者ID
    frontName:null,//疗法名称
    code:null,//编号
    productType: null, // 处方商品类别 1=药品、2=疗法
    productId: null, // 商品Id
};
const model = ref(deepClone(initParams));

/** 保存 loading */
const { loading: isLoading, startLoading, endLoading } = useLoading();

type prescriptionName = '开处方'|'编辑'|'详情'
const prescriptionType:Record<string,prescriptionName> = {
  '开处方':'开处方',
  '编辑':'编辑',
  '详情':'详情'
}

/* 表单病症和开方实例 */
const formRefSymptomsPrescriptions = ref(null);

/* 表单规则 */
const rules = {
  selectDoctor: {
    type: "string",
    required: true,
    trigger: ["blur", "change"],
    message: "请选择医生",
  },
  diagnosticDescription:{
    type: "string",
    required: true,
    trigger: ["blur", "change"],
    message: "请输入诊断说",
  },
  additionalRemarks:{
    type: "string",
    required: true,
    trigger: ["blur", "change"],
    message: "请输入补充说明",
  }
};

const tableData = reactive([]);
//保存原本的病症信息
const originallyTableData = reactive([]);
const loadShow = ref(false);

/* 药品 表格项 */
const tableColumns = [
     {
      title: "序号",
      width: 80,
      fixed: "left",
      key: "index",
      align: "center",
      render: (renderData: object, index: number) => {
        return `${index + 1}`;
      },
    },
    {
      title: "商品",
      key: "productName",
      align: "left",
      fixed: "left",
      width: 100,
    },
    {
      title: "规格",
      key: "specName",
      align: "left",
      width: 100,
    },
    {
      title: "加购数量",
      key: "count",
      align: "left",
      width: 100,
    },
    {
      title: "RP取药数量",
      key: "rpCount",
      align: "left",
      width: 100,
      render: (row,index) => {
      return (
        <n-input-number  
        v-model:value={row.rpCount} 
        onChange={(value) => changeInputDosage(value,index)}  
        min="1" 
        max={row.count}  
        onBlur={(value) => blurinputDosage(value)} 
        disabled={prescriptionType[drawerProps.value.type] == PrescriptionType.beginPrescription ?  false : true}/>
       );
      },
    },
    {
      title: "病症",
      key: "options",
      align: "left",
      width: 150,
      render: (row,index) => {
        const optionData = row.options.map(item =>{
        return <div>
                 <n-ellipsis >{item.option}</n-ellipsis>
               </div>
      })
      return optionData;
      },
    },
    {
    title: "操作",
    key: "action",
    width: 80,
    align: "center",
    fixed: "right",
    render: (row,index) => {
      return (
        <n-space align="center" justify="center">
          <n-popconfirm
            onPositiveClick={() => {clickDelete(row,index)}}
            >
            {{
              trigger: () => (
                <n-button  text size="small" type="primary" style={{ color: 'red', cursor: 'pointer' }} disabled={prescriptionType[drawerProps.value.type] == PrescriptionType.beginPrescription ? false :  true }>删除</n-button>
              ),
              default: () => <span style={{width:'300px'}}>是否确定删除该数据？</span>
            }}
            </n-popconfirm>
        </n-space>
      );
    },
  },
];

/** 疗法 表格项 */
const therapyTableColumns = [
    {
      title: "序号",
      width: 80,
      fixed: "left",
      key: "index",
      align: "center",
      render: (renderData: object, index: number) => {
        return `${index + 1}`;
      },
    },
    {
      title: "产品图",
      key: "imgPath",
      align: "left",
      width: 100,
      render: rowData => {
          return <JImage imgPath={rowData.cdnPath} />;
      },
    },
    {
      title: "商品名称",
      key: "name",
      align: "left",
      width: 300,
    },
    {
      title: "服用方法",
      key: "dosage",
      align: "left",
      width: 100,
    },
];

/** 抽屉状态 */
const drawerVisible = ref(false);
const drawerProps = ref();
const orderCode = ref();
const usageState = ref(false);

/* 接收父组件传过来的参数 */
const acceptParams = (params) => {
  drawerVisible.value = true;
  drawerProps.value = params;
  orderCode.value =  params.row.id;
  // 药品类型
  if(params.row.productType == GoodsCategoryType.DRUG){
    usageState.value =  true
  }else{
    usageState.value =  false
  }
  getDetails()
};


/** 获取详情 */
const getDetails = async() =>{
  loadShow.value = true
  try{
    const data =  await drawerProps.value.getInfoApi(orderCode.value);
    const {id, name, gender, mobile, idNo, fromType, thirdGroupMgrName, thirdGroupMgrId, orderId,   createTime, diagTime, comment, diagDesc, doctorId, presProductList,thirdCourseName,  thirdCourseId, presFile, uploadTime,createBy,productTherapyDrugDTO, productType } = data
    model.value.id = id
    model.value.nameValue = name
    model.value.genderValue = gender
    model.value.phoneValue = mobile
    model.value.identityCardValue = idNo

    if(!thirdGroupMgrName && !thirdGroupMgrId){
      model.value.sourceValue = orderSourceLabels[fromType]
    }else{
      model.value.sourceValue = orderSourceLabels[fromType] +'-[' + (thirdGroupMgrName ?   thirdGroupMgrName : '-') + ']' +'-[' + (thirdGroupMgrId ? thirdGroupMgrId :'-') + ']'
    }

    model.value.orderIdValue = orderId
    model.value.creationTime = createTime
    model.value.prescriptionTime = diagTime
    model.value.uploadtime = uploadTime
    model.value.additionalRemarks = comment
    model.value.diagnosticDescription = diagDesc
    model.value.selectDoctor = doctorId
    model.value.rechargePicture = presFile ? presFile : ''
    model.value.createBy = createBy;
    // 商品类别
    model.value.productType = productType;

    if(!thirdCourseName && !thirdCourseId){
      model.value.sourceCourseValue  = null
    }else{
      model.value.sourceCourseValue = (thirdCourseName ? thirdCourseName : '-') +'-[' +   (thirdCourseId ? thirdCourseId : '-') + ']'
    }
    
    model.value.frontName = productTherapyDrugDTO?.frontName ? productTherapyDrugDTO.frontName : '-'
    model.value.code = productTherapyDrugDTO?.code ? productTherapyDrugDTO.code : '-'
    if(presProductList && presProductList.length != 0 && productType == 1){
      presProductList.forEach(item => {
        tableData.push(item)
        originallyTableData.push(item)
      });
      // 药品商品Id
      model.value.productId = presProductList?.productId ?? null;
    }
    
    if(productType == 2 && productTherapyDrugDTO?.therapyDrugItemDTOSList.length != 0 && productTherapyDrugDTO){
      productTherapyDrugDTO.therapyDrugItemDTOSList.forEach(item => {
        tableData.push(item)
      });
      // 疗法商品Id
      model.value.productId = productTherapyDrugDTO?.id ?? null;
    }
    loadShow.value = false
  }catch(err){
    createMessageError('获取详情失败' + err)
  }
  
}

/** 关闭抽屉 */
const closeDrawer = () => {
  model.value = deepClone(initParams);
  drawerVisible.value = false;
  tableData.length = 0
  specIdData.value = []
  originallyTableData.length = 0
};

/** 查看产品详情 */
const handleClickProductDetails = () =>{
  // if (model.value.productType === GoodsCategoryType.DRUG) openEditOrAddDrugGoods('view', { id: model.value.productId });
  if (model.value.productType === GoodsCategoryType.THERAPY) openEditOrAddTherapyGoods('view', { id: model.value.productId });
}

/** 打开药品商品新增或编辑 */
// const newDrugGoodsRef = ref<InstanceType<typeof NewDrugGoods> | null>(null);
// const openEditOrAddDrugGoods = (type: 'add' | 'edit' | 'view', row: Partial<ApiStoreModule.Goods> = {}) => {
//   newDrugGoodsRef.value?.acceptParams({
//     type,
//     row,
//     productTypes: model.value.productType
//   });
// };

/** 打开疗法商品新增或编辑 */
const newTherapyGoodsRef = ref<InstanceType<typeof NewTherapyGoods> | null>(null);
const openEditOrAddTherapyGoods = (type: 'add' | 'edit'  | 'view', row: Partial<ApiStoreModule.Goods> = {}) => {
  newTherapyGoodsRef.value?.acceptParams({
    type,
    row,
    productTypes: model.value.productType
  });
};

/** 保存 */
const diseaseReviseData = ref(null)
const _save = async(e) =>{
  //判断病症是否有删除的数据
  if(originallyTableData.length != tableData.length){
    diseaseReviseData.value =  originallyTableData.map(item => {
      if (specIdData.value.includes(item.specId)) {
        return { specId:item.specId, isDeleted: '1' };
      } else {
        return item;
      }
    });
  }
  
  e.preventDefault();
  formRefSymptomsPrescriptions.value?.validate(async (errors: any) => {
  if (!errors) {
    const params = {
      id:model.value.id,
      doctorId:model.value.selectDoctor,
      diagDesc:model.value.diagnosticDescription,
      comment:model.value.additionalRemarks,
      createBy: prescriptionType[drawerProps.value.type] == PrescriptionType.beginPrescription ? model.value.createBy : undefined,
      presFile: isArray(model.value.rechargePicture) ? model.value.rechargePicture?.join('') : model.value.rechargePicture,
      productType:drawerProps.value.row.productType
    }
    // 药品类型
    if(drawerProps.value.row.productType == GoodsCategoryType.DRUG){
      params['presProductList'] = prescriptionType[drawerProps.value.type] == PrescriptionType.editPrescription  ? undefined : (originallyTableData.length != tableData.length ? diseaseReviseData.value : originallyTableData)
    }
    startLoading();
    try{
      await drawerProps.value.updateInfoApi(params)
      createMessageSuccess(prescriptionType[drawerProps.value.type] == PrescriptionType.editPrescription ? '编辑处方成功' : '开处方成功')
      drawerProps.value?.refresh();
      drawerVisible.value = false
      tableData.length = 0
    }catch(err){
      createMessageError( prescriptionType[drawerProps.value.type] == PrescriptionType.editPrescription ? '编辑处方失败：' : '开处方失败：' + err)
    }finally{
      endLoading();
    }
   }
  });
}

//添加被删除的规格id
const specIdData = ref([])

//删除
const clickDelete = (row,index) =>{
  if(tableData.length == 1) return createMessageWarning('病症必须保留一条数据')
  tableData.splice(index, 1);
  specIdData.value.push(row.specId)
}

const emits = defineEmits<{
    (e: "update:value", value);
}>();

//输入的RP取药数量失焦触发
const blurinputDosage = (value) =>{
  console.log('value',value.value);
}

//输入的RP取药数量Change
const changeInputDosage = (row,index) =>{
  tableData[index].rpCount = row
}

watch(()=>model.value.rechargePicture,(newVal)=>{
  if(newVal[0] == '') model.value.rechargePicture = ''
})

defineExpose({
  acceptParams,
  closeDrawer,
  drawerVisible
});
</script>
  
<style scoped lang="less">
@import "@/styles/default.less";
@import "@/styles/defaultVar.less";

@details-inner-bg-height: calc(100vh - @main-header-height - @main-footer-height - @blank-page-padding*2);

.details-inner-bg-height{
   height: @details-inner-bg-height;
}

:deep .n-drawer-body-content-wrapper{
    padding: 0 !important;
    overflow: hidden;
}

.goods-title {
  margin-bottom: 12px;
  margin-left: 12px;
  font-size: 14px;
  .title {
    margin-bottom: 4px;
    display: flex;
    align-items: center;
  }
}
.footer-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

:deep .n-drawer-body-content-wrapper{
  overflow: hidden;
}
:deep .n-card.n-card--bordered{
  border: 0px;
}
:deep .n-input__input-el{
  text-overflow:ellipsis;
}
</style>
  