<template>
  <div class="personnel-management-page inner-page-height">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :pagination="paginationRef"
      @paginationChange="paginationChange"
      :isNeedCollapse="false"
      :isTableSelection="false"
      :isDisplayIndex="false"
    >
      <!-- 表单 -->
      <template #searchForm>
        <n-form
          ref="formRef"
          label-placement="left"
          label-width="auto"
          :show-feedback="false"
          require-mark-placement="right-hanging"
          size="small"
          :style="{ width: '100%' }"
        >
          <n-form-item :span="12" label="昵称/ID">
            <n-input
              style="width: 270px"
              v-model:value.trim="model.csSearchValue"
              placeholder="请输入店长店员昵称或ID"
              clearable
              @keyup.enter="handlerSearch"
              @clear="
                () => {
                  model.csSearchValue = '';
                  handlerSearch();
                }
              "
            />
          </n-form-item>
          <n-form-item :span="12" label="门店名称">
            <n-input
              style="width: 270px"
              v-model:value.trim="model.storeSearchValue"
              placeholder="请输入门店名称或ID"
              clearable
              @keyup.enter="handlerSearch"
              @clear="
                () => {
                  model.storeSearchValue = '';
                  handlerSearch();
                }
              "
            />
          </n-form-item>
          <n-form-item :span="12" label="经销商姓名">
            <n-input
              style="width: 270px"
              v-model:value.trim="model.dealerSearchValue"
              placeholder="请输入经销商姓名或ID"
              clearable
              @keyup.enter="handlerSearch"
              @clear="
                () => {
                  model.dealerSearchValue = '';
                  handlerSearch();
                }
              "
            />
          </n-form-item>
          <n-form-item :span="12" label="角色">
            <n-select
              style="width: 270px"
              v-model:value="model.roleType"
              :options="roleOptions"
              clearable
              @update:value="tableSearch()"
            />
          </n-form-item>
          <n-form-item label="添加时间" label-placement="left">
            <j-date-range-picker
              style="flex: 1"
              v-model:value="model.creationTime"
              type="datetimerange"
              format="yyyy-MM-dd"
              :default-time="['00:00:00', '23:59:59']"
              clearable
            />
          </n-form-item>
        </n-form>
      </template>
    </FormLayout>
    <PersonnelEditModal ref="personnelEditModalRef" />
    >
  </div>
</template>

<script lang="tsx" setup>
import { computed, onMounted, ref, watch } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { getShopPrescriptionPage } from "@/services/api";
import { useMessages } from "@/hooks";
import { transformMinioSrc } from "@/utils";
import PersonnelEditModal from "./components/PersonnelEditModal.vue";
import { hasEditRoleAuth, hasEditShopAuth, hasChangeMembershipAuth } from "./authList";
import moment from "moment";
import { useRoute, useRouter } from "vue-router";
import { RoutesName } from "@/enums/routes";

const { createMessageSuccess, createMessageError } = useMessages();
/** 表格hook */
const { isLoading, tableData, paginationRef, pageTableData, paginationChange } = useTableDefault({
  pageDataRequest: getShopPrescriptionPage,
});
const router = useRouter();
/* 表格列表项 */
const tableColumns = ref([
  {
    title: "昵称/ID",
    key: "name",
    align: "left",
    render: rowData => {
      return (
        <div>
          <div>{rowData?.nickname ? rowData.nickname : "-"}</div>
          <div>ID: {rowData?.shortId ? rowData.shortId : "-"} </div>
        </div>
      );
    },
  },
  {
    title: "角色",
    key: "role",
    align: "left",
    render: rowData => {
      return rowData.roleType === 1 ? "店长" : "店员";
    },
  },
  {
    title: "会员数",
    key: "memberCount",
    align: "left",
    render: rowData => {
      return (
        <n-button button text type="primary" onClick={() => handleToCustomerManagement(rowData)}>
          {rowData.memberCount}
        </n-button>
      );
    },
  },
  {
    title: "归属门店/ID",
    key: "shopName",
    align: "left",
    render: rowData => {
      return (
        <div>
          <div>{rowData?.storeName ? rowData?.storeName : "-"}</div>
          <div>{rowData?.storeShortId ? rowData?.storeShortId : "-"} </div>
        </div>
      );
    },
  },
  {
    title: "经销商姓名/ID",
    key: "dealerName",
    align: "left",
    render: rowData => {
      return (
        <div>
          <div>{rowData?.dealerName ? rowData?.dealerName : "-"}</div>
          <div>用户ID：{rowData?.dealerShortCsId ? rowData?.dealerShortCsId : "-"} </div>
        </div>
      );
    },
  },
  {
    title: "添加时间",
    key: "addTime",
    align: "left",
  },
  {
    title: "操作",
    key: "action",
    fixed: "right",
    align: "left",
    render: rowData => {
      return (
        <n-space align="center" justify="center">
          {hasEditRoleAuth ? (
            <n-button button text type="primary" onClick={() => handleOpenModal("EditRole", rowData)}>
              修改角色
            </n-button>
          ) : null}
          {hasEditShopAuth ? (
            <n-button text type="primary" onClick={() => handleOpenModal("EditShop", rowData)}>
              修改归属门店
            </n-button>
          ) : null}
          {hasChangeMembershipAuth ? (
            <n-button text type="primary" onClick={() => handleOpenModal("ChangeMembership", rowData)}>
              一键转会员
            </n-button>
          ) : null}
        </n-space>
      );
    },
  },
]);
const route = useRoute();
const initParams = {
  csSearchValue: "",
  storeSearchValue: route?.query?.shortId ?? "",
  creationTime: null,
  dealerSearchValue: "",
  roleType: null,
};
/** 参数 */
const model = ref({
  ...initParams,
});
const roleOptions = ref([
  {
    label: "店长",
    value: 1,
  },
  {
    label: "店员",
    value: 2,
  },
]);
/** 获取参数 */
const getParams = () => {
  const { roleType, creationTime, csSearchValue, storeSearchValue, dealerSearchValue } = model.value;
  const addStartTime = creationTime ? moment(creationTime[0]).format(`YYYY-MM-DD HH:mm:ss`) : null;
  const addEndTime = creationTime ? moment(creationTime[1]).format(`YYYY-MM-DD HH:mm:ss`) : null;
  return {
    roleType,
    addStartTime,
    addEndTime,
    csSearchValue,
    storeSearchValue,
    dealerSearchValue,
  };
};
const personnelEditModalRef = ref();
const handleOpenModal = (type, data) => {
  personnelEditModalRef.value.acceptParams({ type, data, refresh });
};

const handleToCustomerManagement = row => {
  router.push({
    name: RoutesName.CustomerManagement,
    query: {
      staffNameOrId: row?.shortId,
    },
  });
};
/** 搜索 */
const handlerSearch = () => {
  tableSearch();
};

/** 表格刷新 */
function refresh() {
  tableSearch();
}

/* 表格搜索 */
const tableSearch = () => {
  pageTableData(getParams(), paginationRef.value);
};

/** 监听 */
watch(
  () => [model.value.creationTime],
  () => {
    tableSearch();
  },
);
/** 组件挂载 */
onMounted(() => {
  tableSearch();
});
</script>

<style lang="less" scoped>
@import "@/styles/defaultVar.less";
</style>
