@import "@/styles/defaultVar.less";
.n-menu.n-menu--horizontal .n-menu-item-content.n-menu-item-content--child-active .n-menu-item-content-header {
  line-height: 22px;
  font-weight: 500;
  font-size: 14px;
  position: relative;
  &::after {
    content: "";
    border-bottom: 2px solid @primary-color;
    position: absolute;
    bottom: 0px;
    left: 0px;
    height: 2px;
    width: 100%;
  }
}

.n-menu .n-menu-item-content.n-menu-item-content--selected::before {
  border-left-color: @primary-color;
  border-radius: var(--n-border-radius);
}
.n-menu-item-content.n-menu-item-content--child-active::before {
  border-left-color: @primary-color !important;
  border-radius: var(--n-border-radius);
  background-color: var(--n-item-color-active);
}
.n-menu .n-menu-item-content::before {
  border-left: 3px solid transparent;
  border-radius: var(--n-border-radius);
}
.main-menu .n-menu.n-menu--horizontal .n-menu-item-content {
  padding: 0px 5px;
  margin: 0px 32px;
}
.main-menu .n-menu.n-menu--horizontal .n-menu-item {
  height: 32px;
  &::after {
    content: "";
    position: absolute;
    right: 0px;
    top: 8px;
    width: 1px;
    height: 20px;
    background-color: #E5E6EB;
  }
}
.main-menu .n-menu .n-menu-item-content .n-menu-item-content-header {
  font-size: 16px;
  color: #666666;
  font-weight: 500;
  line-height: 20px;
}
.n-layout-header.n-layout-header--bordered {
  border-bottom-width: 2px;
}
.n-menu.n-menu--collapsed .n-menu-item-content.n-menu-item-content--selected::before {
  border-left-color: transparent;
  background-color: transparent !important;
}
.n-menu.n-menu--horizontal .n-menu-item-content.n-menu-item-content--selected,
.n-menu.n-menu--horizontal .n-menu-item-content:not(.n-menu-item-content--disabled):hover{
  border-bottom: 2px solid var(--n-border-color-horizontal);
}