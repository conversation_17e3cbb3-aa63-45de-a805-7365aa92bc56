<template>
    <n-dropdown :options="options" @select="handleDropdown">
        <div class="userInfo-wrapper"> 
            <div class="userInfo-trigger">
              <span class="username">{{ userStore.userInfo?.name }}</span>
              <n-avatar
                round
                size="medium"
                :src="
                  (userStore.userInfo?.imgUrl || userStore.userInfo?.doctorImg)
                    ? transformMinioSrc(userStore.userInfo?.imgUrl || userStore.userInfo?.doctorImg)
                    : avatarSrc
                "
              />
            </div>
        </div>  
    </n-dropdown>
    <UserPwdEditModal
      v-model:show="pwdModalShow"
      :id="userStore.userInfo?.id"
      :closable="pwdModalClosable"
      :forceQuit="true"
      :firstLoginBoolean=firstLoginBoolean
      :easyPasswordsBoolean="easyPasswordsBoolean"
    />
</template>
  
<script lang="ts" setup name='UserAvatar'>
import { onMounted, ref, h, type Component } from "vue";
import { NIcon, type DropdownOption } from "naive-ui";
import { useUserStore } from "@/stores/modules/user";
import { transformMinioSrc } from "@/utils";
import { afterLogout } from "@/utils/accountUtils";
import avatarSrc from "@/assets/image/system/avatar.png";
import { KeyOutline as KeyOutIcon, LogOutOutline as LogoutIcon } from "@vicons/ionicons5";
import UserPwdEditModal from '@/components/UserPwdEditModal/index.vue';
import { useSystemStoreWithoutSetup } from "@/stores/modules/system"
import { FirstLogin, EasyPasswords } from "@/enums"
  
const systemStore = useSystemStoreWithoutSetup()

/* 点击key enums */
type DropdownKey = "change-password" | "logout";

/** 用户 store */
const userStore = useUserStore();

/** 渲染图标Icon */
const renderIcon = (icon: Component) => {
  return () => {
    return h(NIcon, null, {
      default: () => h(icon)
    })
  }
};

/* 下拉菜单选项 */
const options: DropdownOption[] = [
    {
      label: "修改密码",
      key: "change-password",
      icon: renderIcon(KeyOutIcon)
    },
    {
      type: "divider",
      key: "divider",
    },
    {
      label: "退出登录",
      key: "logout",
      icon: renderIcon(LogoutIcon)
    },
];

const pwdModalClosable = ref(false);
const pwdModalShow = ref(false);

/* 处理点击选项回调 */
const handleDropdown = (optionKey: string) => {
    const key = optionKey as DropdownKey;
    // 退出
    if (key === "logout") {
    accountLogout();
    }
    // 修改密码
    if (key === "change-password") {
      pwdModalShow.value = true;
      pwdModalClosable.value = true;
    }
};

/** 登出 */
const accountLogout = async () => {
  afterLogout();
};

/** 账号密码是否为首次登录 */
const firstLoginBoolean = ref(false)

/** 账号密码是否为简易登录 */
const easyPasswordsBoolean = ref(false)

onMounted(() =>{
  if(systemStore._globalConfig['isInitialPassword'] == FirstLogin.isFirstLogin){
    firstLoginBoolean.value = true
  }
  if(systemStore._globalConfig['isSimplePassword'] == EasyPasswords.isEasyPasswords){
    easyPasswordsBoolean.value = true
  }
})

</script>
  
<style lang="less" scoped>
.userInfo-wrapper {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-left: 12px;
  &:hover {
    background-color: #f6f6f6;
  }
  .userInfo-trigger {
    display: flex;
    align-items: center;
    cursor: pointer;
    .username {
      font-size: 14px;
      color: #000000;
      padding-right: 10px;
    }
  }
}


</style>
