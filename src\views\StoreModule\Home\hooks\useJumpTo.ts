import { useRouter , type LocationQueryRaw  } from "vue-router";
import type {RoutesName} from '@/enums/routes';
import { useMessages } from "@/hooks";

const message = useMessages();


export default function useJumpTo() {
    const router = useRouter();
    /**
     * @description 跳转到指定页面
     * @param {RoutesName} name 路由名称
     * @param {LocationQueryRaw} query 路由参数
     * */
    function jumpTo( isAuth:boolean, name: RoutesName, query?:LocationQueryRaw ) {
        if (!isAuth) return message.createMessageError('暂无查看权限')
        router.push({name,query})
    }
    return {
        jumpTo
    }
}