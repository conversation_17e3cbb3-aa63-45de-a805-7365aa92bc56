<template>
  <JModal
      v-model:show="show"
      width="680"
      :title="model.title"
      positiveText="确定"
      @after-leave="closeModal"
      @positive-click="_save"
      :positiveButtonProps="{
          loading: isLoading
      }"
    >
      <n-form
        v-if="model.title != '删除组织'"
        ref="formRef"
        :rules="rules"
        :model="model"
        label-width="100"
        label-placement="left"
        require-mark-placement="right-hanging"
        :style="{
            width: '100%',
            maxHeight: '80vh',
            overflowY: 'auto'
          }"
      >
        <n-grid cols="12">
          <n-form-item-gi required label="组织名称" span="12" path="name">
            <n-input
              v-model:value="model.name"
              @blur="model.name=$event.target.value.trim()"
              :show-button="false"
              maxlength="30"
              placeholder="请输入名称"
              clearable
              show-count
            />
          </n-form-item-gi>
          <n-form-item-gi label="部门类型" span="12">
            <n-select v-model:value="model.departmentType" :options="DepartmentTypeOptions" :disabled="model.title === '编辑组织'"/>
          </n-form-item-gi>
          <n-form-item-gi v-if="model.departmentType === DepartmentType.STORE" label="组织状态" span="12">
            <n-radio-group v-model:value="model.storeEntityVO.storeStatus" name="storeStatus" @click.prevent="handleRadioClick">
              <n-radio v-for="song in storeStatusRadio" :key="song.value" :value="song.value" style="padding-right: 10px">
                {{ song.label }}
              </n-radio>
            </n-radio-group>
          </n-form-item-gi>
           <n-form-item-gi v-if="model.departmentType === DepartmentType.DEALER" label="经销商姓名" span="12" path="managerName" required>
            <n-input
              v-model:value="model.managerName"
              :show-button="false"
              maxlength="15"
              show-count
              placeholder="请输入经销商姓名"
            />
          </n-form-item-gi>
           <n-form-item-gi v-if="model.departmentType === DepartmentType.DEALER" label="联系电话" span="12">
            <n-input
              v-model:value="model.managerPhone"
              :show-button="false"
               @blur="blurDealerMobilePhone"
              maxlength="11"
              placeholder="请输入手机号码"
            />
          </n-form-item-gi>
          <n-form-item-gi v-if="model.departmentType === DepartmentType.STORE" label="门店头像" span="12">
            <div style="width: 100%;">
              <CustomizeUpload v-model:value="model.storeEntityVO.storeAvatar" :imageUploadType="['image/jpg','image/jpeg','image/gif','image/png']"  enableTypeValidation accept="image/jpg,image/jpeg,image/gif,image/png" :fileListSize="1" :max="1"/>
              <div style="width: 98%; margin-top: 12px;">图片需小于1M，支持png、jpg、JPEG、GIF格式</div>
            </div>
          </n-form-item-gi>
          <n-form-item-gi v-if="model.departmentType === DepartmentType.STORE" label="门店联系人" span="12">
            <n-input
              v-model:value="model.storeEntityVO.contactName"
              @blur="model.storeEntityVO.contactName=$event.target.value.trim()"
              :show-button="false"
              maxlength="30"
              placeholder="请输门店联系人名称"
              clearable
              show-count
            />
          </n-form-item-gi>
          <n-form-item-gi v-if="model.departmentType === DepartmentType.STORE" label="联系电话" span="12">
            <n-input
              v-model:value="model.storeEntityVO.contactPhone"
              @blur="blurMobilePhone"
              :show-button="false"
              maxlength="30"
              placeholder="请输门店联系电话"
            />
          </n-form-item-gi>
          <n-form-item-gi v-if="model.departmentType === DepartmentType.STORE" label="营业时间" span="12">
            <n-input
              v-model:value="model.storeEntityVO.businessHours"
              @blur="model.storeEntityVO.businessHours=$event.target.value.trim()"
              :show-button="false"
              maxlength="50"
              placeholder="请输门店营业时间"
            />
          </n-form-item-gi>
          <n-form-item-gi v-if="model.departmentType === DepartmentType.STORE" label="地址" span="12">
            <JAreaSelect v-model:value="model.storeEntityVO.addressOptions"/>
          </n-form-item-gi>
          <n-form-item-gi v-if="model.departmentType === DepartmentType.STORE" label="详细地址" span="12">
            <n-input
              v-model:value="model.storeEntityVO.addressDetail"
              @blur="model.storeEntityVO.addressDetail=$event.target.value.trim()"
              :show-button="false"
              maxlength="30"
              placeholder="请输详细地址"
            />
          </n-form-item-gi>
          <n-form-item-gi v-if="model.departmentType === DepartmentType.STORE" label="绑定店长" span="12">
            <JSelectContact :disabled="managerId !== ''" v-model:value="model.storeEntityVO.managerId"
                            :managerId="managerId"
            ></JSelectContact>
          </n-form-item-gi>
        </n-grid>
      </n-form>
      <div v-if="model.title == '删除组织'">
        <p>1、不影响已经生成的分佣单，即从分佣单侧依然能看到订单归属原组织名称</p>
        <p>2、不影响已经生成的报表数据，即从报表侧依然能看到原组织的业绩。</p>
        <p>3、不影响已经生成的商品订单，即从商品订单单侧依然能看到订单归属原组织名称</p>
        <p>4、不影响已经生成的报表数据，即从报表侧依然能看到原组织的业绩。</p>
      </div>
    </JModal>
</template>

<script setup lang="ts" name="GroupConfigurationShow">
import { ref, watch } from "vue";
import { useMessages } from "@/hooks";
import { useDialog, type DropdownOption } from "naive-ui";
import type {organizeForm} from "@/views/Distribution/OrganizationalStructure/hooks/type";
import Organize from "@/views/Distribution/OrganizationalStructure/hooks/organize";
import { DepartmentStatus, DepartmentType, MemberType } from "@/enums";
import JAreaSelect from "@/components/JAreaSelect/index.vue";
import JSelectContact from "@/components/JSelect/JSelectContact.vue";
import { getStoreDesc } from "@/services/api";
import useSubAccountPayeeManagement
  from "@/views/StoreModule/Finance/AccountManagement/components/SubAccountPayeeManagement/hooks/useSubAccountPayeeManagement";
const {numberVerification, validateBankCardNo} = useSubAccountPayeeManagement()
const { DepartmentType_options, storeStatusRadio, organizeFormObj, assignmentObj } = Organize()
export type AddOrEdit = "add" | "edit" ;
export interface AddCompanyModalProps {
  type?: AddOrEdit; // 弹窗模式 --> 默认add
  api?: (params: any) => Promise<any>;// 新增保存Api
  updataTable?: (res,id,str) => void; // 更新表格数据
  rowData:any;
  setLoading:(btn)=>void;
}

/** 初始化参数 */
const model = ref<organizeForm>(JSON.parse(JSON.stringify(organizeFormObj)) as organizeForm);

const managerId = ref<string>('')
/* 提示信息 */
const message = useMessages();
// 店员列表
const storeStaffRelationList = ref([])
/* 模态框显隐状态 */
const show = ref(false);
/* 父组件传过来的参数 */
const parameter = ref<AddCompanyModalProps>({} as AddCompanyModalProps);
const DepartmentTypeOptions = ref([...DepartmentType_options])
const acceptParams = async (params) => {
  managerId.value = ''
  parameter.value = params
  model.value.title = parameter.value.type
  
  storeStaffRelationList.value = []
  if(model.value.title === "创建子组织") {
    switch (parameter.value.rowData.departmentType) {
      case DepartmentType.REGION:
        DepartmentTypeOptions.value = [
          {
            label: "区域",
            value: DepartmentType.AREA,
          },
        ];
        break;
      case DepartmentType.AREA:
        DepartmentTypeOptions.value = [
          {
            label: "经销商",
            value: DepartmentType.DEALER,
          }
        ];
        break;
      case DepartmentType.DEALER:
        DepartmentTypeOptions.value = [
          {
            label: "门店",
            value: DepartmentType.STORE,
          }
        ];
        break;
    }
  }
  if (model.value.title === "编辑组织"){
    model.value.name = params.rowData.name
    model.value.departmentType = parameter.value.rowData?.departmentType || 0
    model.value.managerName = parameter.value.rowData?.managerName || ""
    model.value.managerPhone = parameter.value.rowData?.managerPhone || null
    if (parameter.value.rowData.departmentType == DepartmentType.STORE){
      try {
        var data = await getStoreDesc(params.rowData.id)
        model.value = assignmentObj(data)
        managerId.value = model.value.storeEntityVO.managerId || ''
        storeStaffRelationList.value = model.value.storeEntityVO.storeStaffRelationList
      } catch (err) {
        message.createMessageError(err)
      }
    }
  }
  show.value = true;
};

/* 表单规则 */
const rules = {
  name:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入组织名称",
  },
  managerName:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入经销商姓名",
  },
};
/* 表单实例 */
const formRef = ref(null);
/* 清空表单 */
const formDataReset = () => {
  model.value = JSON.parse(JSON.stringify(organizeFormObj)) as organizeForm
  DepartmentTypeOptions.value = [...DepartmentType_options]
};

/* 关闭弹窗 */
const closeModal = () => {
  formDataReset();
};

/** 判断输入的手机号码是否正确 */
const blurMobilePhone = () =>{
  if(!numberVerification(model.value.storeEntityVO.contactPhone) && model.value.storeEntityVO.contactPhone != null){
    model.value.storeEntityVO.contactPhone = null
  }
}
  /** 判断输入的手机号码是否正确 */
const blurDealerMobilePhone = () =>{
  if(!numberVerification(model.value.managerPhone) && model.value.managerPhone != null){
    model.value.managerPhone = null
  }
}
/* 确认--保存 */
const isLoading = ref(false);
const _save = async (e: MouseEvent) => {
  if (model.value.title == '删除组织') {
    isLoading.value = true
    parameter.value.setLoading(true)
    try {
      await parameter.value.api(parameter.value.rowData.code)
      parameter.value.updataTable({}, parameter.value.rowData, model.value.title)
      show.value = false
      model.value.name = ''
    } catch (err) {
      message.createMessageError(err)
    } finally {
      isLoading.value = false
      parameter.value.setLoading(false)
    }
  } else {
    formRef.value?.validate(async (errors: any) => {
      if (!errors) {
        var params;
        if (model.value.departmentType === DepartmentType.STORE) {

          model.value.storeEntityVO = {
            ...model.value.storeEntityVO,
            province: model.value.storeEntityVO.addressOptions.province,
            provinceId: model.value.storeEntityVO.addressOptions.provinceId,
            city: model.value.storeEntityVO.addressOptions.cityName,
            cityId: model.value.storeEntityVO.addressOptions.cityId,
            area: model.value.storeEntityVO.addressOptions.area,
            districtId: model.value.storeEntityVO.addressOptions.areaId,
            storeAvatar: Array.isArray(model.value.storeEntityVO.storeAvatar) ? model.value.storeEntityVO.storeAvatar[0] : model.value.storeEntityVO.storeAvatar,
            storeName: model.value.name,
          }
          model.value.useStatus = model.value.storeEntityVO.storeStatus
          model.value.managerPhone = model.value.storeEntityVO.contactPhone
          model.value.managerId = model.value.storeEntityVO.managerId
          model.value.managerName = model.value.storeEntityVO.contactName
          if (managerId.value) {
            delete model.value.storeEntityVO.storeStaffRelationList
          } else {
            if (model.value.storeEntityVO.managerId) {
              model.value.storeEntityVO.storeStaffRelationList = [
                ...storeStaffRelationList.value,
                {
                  memberType: 1,
                  customerId: model.value.storeEntityVO.managerId,
                  memberId: model.value.storeEntityVO.managerId,
                }
              ]
            } else {
              delete model.value.storeEntityVO.storeStaffRelationList
            }
          }
        }
        if (model.value.title == "创建一级组织") {
          params = {
            data: {
              ...model.value,
              parentCode: '0',
            }
          };
        } else {
          params = {
            data: {
              parentCode: parameter.value.rowData.parentCode,
              level: parameter.value.rowData.level + 1,
              code: parameter.value.rowData.code,
              id: parameter.value.rowData.id,
              ...model.value
            }
          };
          if (model.value.title == "创建子组织") {
            params.data.parentCode = params.data.code
            delete params.data.code
            delete params.data.id
          }
        }
        isLoading.value = true
        parameter.value.setLoading(true)
        console.log(params);
        try {
          var res = await parameter.value.api(params)
          if (model.value.title == "创建子组织" || model.value.title == "创建一级组织") {
            parameter.value.updataTable(res, parameter.value.rowData, model.value.title)
          } else {
            parameter.value.updataTable(params.data, parameter.value.rowData, model.value.title)
          }
          model.value.name = ''
          show.value = false
          formDataReset()
        } catch (err) {
          message.createMessageError(err)
        } finally {
          isLoading.value = false
          parameter.value.setLoading(false)
        }
      }
    });
  }
};
/** 确认框 */
const dialog = useDialog();
/** 点击radio */
const handleRadioClick = (e) => {
  console.log(e.target.value);
  if (model.value.title === "编辑组织"){
    dialog.warning({
      title: '确定修改门店状态',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => {
        model.value.storeEntityVO.storeStatus = Number(e.target.value)
      },
    })
  }else {
    model.value.storeEntityVO.storeStatus = Number(e.target.value)
  }
};
watch(()=>model.value.storeEntityVO.storeAvatar,(newVal)=>{
  if(newVal[0] == '') model.value.storeEntityVO.storeAvatar = ''
})
defineExpose({
  acceptParams,
});
</script>
  
<style scoped lang="less"></style>
    