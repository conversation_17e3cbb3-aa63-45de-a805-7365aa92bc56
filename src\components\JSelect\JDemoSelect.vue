<template>
    <JSelect :value='_tempValueRef' @focus="getProjectList" :options="state.options" @update:value="onValueChange" :loading="isLoading" :multiple="isMultiple" :max-tag-count="maxTagCount" :display-quantity="props.isMultiple === false ? 0 : maxTagCount"></JSelect>
  </template>
  
  <script setup lang="ts">
  import JSelect from "./index.vue";
  import { reactive, watch, ref, watchEffect } from "vue";
  import { createCacheStorage } from "@/utils/cache/storageCache";
  import { CacheConfig } from "@/utils/cache/config";
  import { useMessages } from "@/hooks";
  import { getProductPresOptionPage } from "@/services/api";
  import { isArray } from "@/utils/isUtils";
  const props = withDefaults(
    defineProps<{
      isImmediately?: boolean;
      isMultiple?:boolean;
      value:Array<string | number> | string | number | null
    }>(),
    {
      isImmediately: false,
      isMultiple:false
    }
  );
  const emits = defineEmits<{
    (e: "update:name", name: string): void;
    (e: "update:value", selectValue: any): void;
  }>();
  
  const state = reactive({
    options: [],
  });
  


  const _tempValueRef = ref()
  const isLoading = ref(false);
  const projectListData = ref([]);
  const projectListRef = ref([]);
  const message = useMessages();
  const maxTagCount = ref(1)
  
  const getProjectList = async () => {
    if(!projectListData.value.length){
      try {
        isLoading.value = true;
        const proList = createCacheStorage(CacheConfig.ProjectOptions);
        const oldprojectListData = proList.get();
        if (!oldprojectListData) {
          const result = await getProductPresOptionPage({});
          if (result && result.length > 0) {
              projectListData.value = result;
              projectListRef.value = result
              proList.set(result);
          }
        } else {
          getOption(oldprojectListData);
        }
      } catch (error) {
        message.createMessageError(error || "获取项目列表失败");
      } finally {
        isLoading.value = false;
      }
    }
  };
  watch(projectListData, (newVal) => {
    if (newVal) {
      getOption(newVal);
    }
  });
  
  const getOption = (filterData) => {
    state.options = filterData.map((item) => {
      return { label: item.name, value: item.id };
    });
  };
  function onValueChange(value) {
    if(props.isMultiple){
      emits("update:value", value.join(','));
    }
    else{
      if (value) {
        emits("update:name",projectListRef.value.find((item) => item.id === value).name);
        emits("update:value", value);
      } 
      else if (value === null) {
        emits("update:name", value);
        emits("update:value", value);
      }
    }

}
  watchEffect(() => {
    if (props.isImmediately) {
        getProjectList();
    }
  });

  watch(()=>props.value,(newVal)=>{
    if(newVal) _tempValueRef.value = props.isMultiple? `${newVal}`.split(','):newVal
    else _tempValueRef.value = newVal
  },{
    immediate:true
  })

  </script>
  
  <style scoped></style>
