import { DealerGroupsAuth } from "@/enums/authKeys";
import { hasAuth } from "@/utils/auth/authUtils";

export const hasAddAuth = (function () {
  return hasAuth(DealerGroupsAuth.AddGroups.key);
})();

export const hasDeleteAuth = (function () {
  return hasAuth(DealerGroupsAuth.DeleteGroups.key);
})();

export const hasEditAuth = (function () {
  return hasAuth(DealerGroupsAuth.EditGroups.key);
})();

export const hasEditDealerTagsAuth = (function () {
  return hasAuth(DealerGroupsAuth.EditDealerTags.key);
})();
