<template>
  <div>
    <hover-container
      class="search-container"
      tooltip-content="搜索"
      @click="handleSearch"
    >
      <n-icon size="20">
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          width="20" 
          height="20" 
          viewBox="0 0 24 24"
          >
            <path 
              fill="currentColor"
              d="M21.71 20.29L18 16.61A9 9 0 1 0 16.61 18l3.68 3.68a1 1 0 0 0 1.42 0a1 1 0 0 0 0-1.39M11 18a7 7 0 1 1 7-7a7 7 0 0 1-7 7"
            />
        </svg>
      </n-icon>
    </hover-container>
    <SearchModal v-model:value="show" />
  </div>
</template>

<script lang="ts" setup>
import { useBoolean } from '@/hooks';
import { SearchModal } from './components';

defineOptions({ name: 'GlobalSearch' });

const { bool: show, toggle } = useBoolean();

function handleSearch() {
  toggle();
}
</script>

<style lang="less" scoped>
.search-container {
  width: 40px;
  height: 100%;
}
</style>
