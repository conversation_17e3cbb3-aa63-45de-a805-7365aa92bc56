<template>
  <div style="position: relative; width: 98%;">
    <n-data-table
      :columns="columns"
      :data="tableDataRef"
      :row-key="row => row.id"
      :style="{ minHeight: `${450}px` }"
      default-expand-all
      flex-height
      :single-line="false"
      size="small"
    >
      <template #empty>
        <JEmpty />
      </template>
    </n-data-table>
    <!-- 新增 -->
    <n-button type="primary" text class="add" @click="openGoodsModal">新增</n-button>
    <!-- 商品弹窗 -->
    <GoodsModal 
      ref="goodsModalRef"
      @update:selected-options="handleSelectOptions"
    />
  </div>
</template>

<script setup lang="tsx" name="ViewProductTable">
import { ref, computed } from "vue";
import type { PointCofigType } from "../../../types";
import { GoodsCategoryType } from "@/enums";
/** 相关组件 */
import GoodsModal from "@/views/business/GoodsModal/index.vue";
import JEmpty from "./JEmpty.vue";

/** emits */
const emits = defineEmits<{
  (e: 'clickCofig', type: PointCofigType): void;
}>();

/** 表格数据 */
const tableDataRef = ref([]);

/** 表单项 */
const columns = [
  {
    title: "序号",
    width: 80,
    fixed: "left",
    key: "index",
    align: "center",
    render: (renderData: object, index: number) => {
      return `${index + 1}`;
    },
  },
  {
    title: '跳转商品名称',
    key: 'name',
    resizable: true,
  },
  {
    title: "上架/下架",
    key: "isPublish",
    width: 120,
    resizable: true,
    render: row => {
      return (
        <n-tag bordered={false} size="small" type={row.isPublish === 1 ? "success" : "error"}>
          {row.isPublish === 1 ? "上架" : "下架"}
        </n-tag>
      );
    },
  },
  {
    title: "库存",
    key: "availStocks",
    width: 120,
    resizable: true,
    render: row => {
      // 普通商品
      if (row.type == GoodsCategoryType.GENERAL) {
        // 计算 availStocks 的总值
        const totalAvailStocks = row?.productSpecDTOList.reduce((total, item) => total + item.availStocks, 0);
        return <span>{totalAvailStocks}</span>;
      }
      let availStocks = row.productSpecDTOList?.[0]?.availStocks ?? 0; // 确保 price 是数字
      return <span>{availStocks}</span>;
    },
  },
  {
    title: '操作',
    key: 'operation',
    width: 160,
    fixed: "right",
    render: (row, index) => {
      return (
        <n-popconfirm onPositiveClick={() => handleDelete(row?.id)}>
          {{
            trigger: () => (<n-button type='error' text>删除</n-button>),
            default: () => <span>是否确定删除该数据？</span>
          }}
        </n-popconfirm>
      )
    },
  }
];

/** 打开新增商品 */
const goodsModalRef = ref<InstanceType<typeof GoodsModal> | null>(null);
const openGoodsModal = () => {
	goodsModalRef.value?.acceptParams();
};

/** 添加、批量添加回调 */
function handleSelectOptions(goodsList: ApiStoreModule.Goods[]) {
  console.log("添加、批量添加回调", goodsList);
  goodsList.forEach(item => {
    tableDataRef.value.push(item);
  });
}

/** 删除某项数据 */
function handleDelete(id: string) {
  tableDataRef.value = tableDataRef.value.filter(item => item?.id !== id);
}

/** 使用 defineExpose 显式暴露给父组件 */
defineExpose({
  tableData: tableDataRef
});
</script>

<style lang="less" scoped>
.add {
  position: absolute;
  top: -24px;
  right: 0;
  font-size: 16px;
}
</style>
