<template>
  <n-upload :show-file-list="false" v-model:file-list="fileList" :custom-request="customRequest" multiple
        :on-before-upload="beforeUpload"
        :accept="limitAccept"
        >
        <n-upload-dragger>
            <div style="margin-bottom: 12px">
                <n-icon size="48" :depth="3">
                    <ArchiveIcon />
                </n-icon>
            </div>
            <n-text style="font-size: 16px">
                点击或者拖动文件到该区域来上传
            </n-text>
            <n-p depth="3" style="margin: 8px 0 0 0">
                上传视频支持格式为mp4和webm格式。视频时长不可超过5分钟，大小不可超过500MB。
            </n-p>
        </n-upload-dragger>
    </n-upload>
</template>

<script setup lang="ts">
import { ref, reactive, toRefs, onMounted, onBeforeMount ,onBeforeUnmount } from 'vue';
import { ArchiveOutline as ArchiveIcon } from '@vicons/ionicons5';
import type { UploadCustomRequestOptions, UploadFileInfo } from 'naive-ui';
import { useMessages } from "@/hooks";
const { createMessageSuccess, createMessageError } = useMessages();
import { getMediaDuration, isNullOrUnDef  } from "@/utils";
import { uploadVideoFile } from "@/services/api";
import { uploadType } from "../../type"
import { getOssFileUrlPrefix } from "@/utils/http/urlUtils";
const fileList = ref<Array<UploadFileInfo>>([]);
const videoUploadList = ref<Array<UploadCustomRequestOptions>>([]);
import useBatchUploadVideo from "../../hooks/useBatchVideoUpload"
const {triggerUploadingVideoRelatedContent,updateUploadProgress,tableData,videoUploading,optionsData,batchVideoModal} = useBatchUploadVideo();
import { userGetCoverImg } from "../../hooks/userGetCoverImg"
const { getCoverImg } = userGetCoverImg()

// 上传事件
const customRequest = async (option: UploadCustomRequestOptions) => {
    
        // 添加队列等待上传
    videoUploadList.value.push(option);
    if (!videoUploading.value) {
        startUpload()
    }
}

const beforeUpload = async(options: { file: UploadFileInfo, fileList: UploadFileInfo[] })=>{
    // console.log(options,'beforeUpload');
    // 首先判断是否符合视频限制     
    return await isConditionMet(options.file)

}

// 开始上传
const startUpload = ()=>{
    const optiosn =  videoUploadList.value.shift()
    videoUploading.value = true;
    uploadVideo(optiosn)
}

// 上传方法
const uploadVideo = async(options)=>{
    const {onFinish, onError, onProgress, file } = options;
    const _formData = new FormData()
    _formData.append("file",file.file)

    const resp = await uploadVideoFile(_formData, ({ progress }) => {
          let uploadProgress = Number((progress * 100).toFixed(2)) - 10
          let ids = options.file.id
          updateUploadProgress(ids,uploadProgress)
          onProgress({ percent: Number((progress * 100).toFixed(2)) });
        });
        onFinish();
        
        returnDataContent(resp,options)
}

const returnDataContent = async(resp:string,options:UploadCustomRequestOptions)=>{
    let coverImagePath = ''
    try {
        coverImagePath = await getCoverImg(getOssFileUrlPrefix()+'/'+resp);
    } catch (error) {
        coverImagePath = ''
    }
    
    tableData.value = tableData.value.map(item => {
         if (item.id === options.file.id) {
            return { ...item,percentage: 100,videoPath: resp,uploadStatus:uploadType.completeUploading,coverImagePath};
         } else {
            return { ...item };
         }
      });
      if (videoUploadList.value.length > 0) {
            startUpload()
      }else{
        videoUploading.value = false;
      }
}

const limitDuration = 5 * 60;               // 限制视频时长
const limitFileSize = 1024 * 1024 * 500     // 限制视频大小
const limitAccept="video/mp4,video/webm"    // 限制文件格式
async function isConditionMet(option:UploadFileInfo){
    
    const duration = await getMediaDuration(option.file);
    const fileSize = option.file.size;
    const limitAcceptList = limitAccept.split(',');
    if ( !limitAcceptList.includes(option.file.type)) {
        createMessageError(`${option.name}文件格式错误`)
        return false
    }
    if (duration > limitDuration || fileSize > limitFileSize) {
        createMessageError(duration > limitDuration ? `${option.name}视频时长超过五分钟` : `${option.name}文件大小超出500MB`);
        // option.onError();
        return false;
    }else{
        triggerUploadingVideoRelatedContent(option);
        return true;
    }
}

// 删除某个等待中的视频
const removeQueue = (id:string)=>{
    const isHave = videoUploadList.value.some(item=>item.file.id == id);
    if (!isHave) return createMessageError('视频已在上传中，不可删除');
    videoUploadList.value = videoUploadList.value.filter(item=>item.file.id != id);
    tableData.value = tableData.value.filter(item=>item.id != id);
    removefileList(id);
}
// 移除文件列表中某个视频
const removefileList = (id:string)=>{
    fileList.value = fileList.value.filter(item=>item.id != id);
}
// 清除所有等待中的视频
const clearQueue = ()=>{
    videoUploadList.value = [];
    tableData.value = [];
    videoUploading.value = false;
}

onBeforeUnmount(()=>{
    clearQueue()
})
defineExpose({
    removeQueue,
    clearQueue,
    removefileList
})
</script>
<style scoped lang="less">
</style>