<template>
  <NDrawer
    v-bind="$attrs"
    v-model:show="drawerShowRef"
    width="100%"
    height="100%"
    :auto-focus="props.autoFocus"
    @close="handleClose"
    @mask-click="handleClose"
    @esc="handleClose"
    :to="props.to"
  >
    <NDrawerContent :body-content-style="{ padding: '0' }" :footer-style="footerStyle">
      <div class="wrapper">
        <!-- Title slot -->
        <slot name="custom-title">
          <DrawerTitle :title="title" @goBack="handleGoBack">
            <template #title>
              <slot name="title"></slot>
            </template>
          </DrawerTitle>
        </slot>

        <!-- Step section -->
        <div v-if="props.isShowStep" class="step">
          <slot name="step"></slot>
        </div>

        <!-- Main content -->
        <div class="publics-form" :class="formClass" :style="styleCard">
          <div class="publics-form-scroll" v-if="props.isShowScroll">
            <NSpin :show="props.isGetLoading" size="small">
              <NGrid :cols="24">
                <template v-for="item in props.contentsList" :key="item.name">
                  <NGi v-if="item.name && !item.isHide" :span="24">
                    <div class="title-wrapper">
                      <div class="title-line"></div>
                      <span>{{ item.name }}</span>
                    </div>
                  </NGi>
                  <NGi v-if="!item.isHide" :span="24">
                    <slot :name="item.slotName"></slot>
                  </NGi>
                </template>
              </NGrid>
            </NSpin>
          </div>
          <slot v-else name="noScrollContent"></slot>
        </div>
      </div>

      <!-- Footer slot -->
      <template v-if="props.isShowFooter" #footer>
        <div :class="{ 'footer-wrapper': props.enableFooterWrapper } ">
          <slot name="footer"></slot>
        </div>
      </template>

      <!-- Default slot -->
      <slot></slot>
    </NDrawerContent>
  </NDrawer>
</template>

<script lang="ts" setup>
import { computed } from "vue";
/** 相关组件 */
import DrawerTitle from "@/components/DrawerTitle/index.vue";

defineOptions({
  name: "JDrawer",
});

/** props */
const props = withDefaults(defineProps<{
    title?: string; // 主体的标题
    show: boolean; 	// 是否展示抽屉
    autoFocus?: boolean; // 是否自动聚焦 Drawer 第一个可聚焦的元素
    to?: string; // 弹窗位置
    isGetLoading?: boolean; // 是否请求数据中
    isShowFooter?: boolean; // 是否显示底部操作栏
    isShowStep?: boolean;// 是否显示步骤器
    contentsList?: Array<{
      name: string | null; // 副标题
      slotName: string; // 内容插槽名
      isHide?: boolean; // 是否显示
    }>;
    isShowScroll?: boolean; // 是否生成滚动条
    mainStyle?: Object; // 内容区域的样式
    enableFooterWrapper?:boolean // 底部区域的样式
}>(), {
    title: '抽屉标题',
    autoFocus: false,
    to: '.table-wrapper',
    isGetLoading: false,
    isShowFooter: true,
    isShowStep: false,
    contentsList: () => [{ name: null, slotName: 'content', isHide: false }],
    isShowScroll: true,
    mainStyle: undefined,
    enableFooterWrapper: false,
});

const emits = defineEmits<{
  (e: 'update:show', val: boolean): void;
  (e: "goBack"): void;
}>();

/** 抽屉显隐 */
const drawerShowRef = computed({
  get() {
    return props.show;
  },
  set(value) {
    emits('update:show', value);
  },
});

const footerStyle = computed(() => ({
  padding: '12px',
  boxShadow: 'rgba(17, 17, 26, 0.1) 0px 4px 16px, rgba(17, 17, 26, 0.05) 0px 8px 32px'
}));

const styleCard = computed(() => ({
  padding: '12px 2px 12px 24px',
  backgroundColor: '#fff',
  ...props.mainStyle
}));

const formClass = computed(() => ({
  'publics-form-h1': !props.isShowStep,
  'publics-form-h2': props.isShowStep
}));

const handleClose = () => {
  emits('update:show', false);
};

const handleGoBack = () => {
  emits('update:show', false);
  emits('goBack');
};
</script>

<style lang="less" scoped>
@import "@/styles/defaultVar.less";
@import "@/styles/scrollbar.less";

.wrapper {
  width: 100%;
  height: 100%;

  .step {
    display: flex;
    align-items: center;
    width: 100%;
    height: 64px;
    padding: 12px 24px;
    box-sizing: border-box;
    background-color: #fff;
    border-bottom: 1px solid rgba(238, 238, 238, 1);
  }

  .publics-form {
    width: 100%;
    box-sizing: border-box;

    .publics-form-scroll {
      height: 100%;
      overflow-y: auto;
      .scrollbar();
    }

    .title-wrapper {
      height: 30px;
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: 700;
      margin-bottom: 12px;

      .title-line {
        width: 4px;
        height: 60%;
        background-color: @primary-color;
        margin-right: 5px;
      }
    }

    &-h1 {
      height: calc(100% - 52px);
    }

    &-h2 {
      height: calc(100% - 116px);
    }
  }
}

.footer-wrapper {
  display: flex;
  justify-content: space-between;
  width: 100%;
}
</style>
