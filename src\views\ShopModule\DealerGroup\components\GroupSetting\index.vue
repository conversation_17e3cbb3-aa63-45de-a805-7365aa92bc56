<template>
  <div class="group-container">
    <div class="header-box">
      <!-- 表单 -->
      <n-form
        ref="formRef"
        :model="modal"
        :show-feedback="false"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
        size="small"
        :style="{ width: '100%' }"
      >
        <n-form-item label="大类名称">
          <j-search-input v-model:value="modal.categories" placeholder="请输入大类名称" @search="tableSearch" />
        </n-form-item>
      </n-form>
      <n-button v-if="hasAddAuth" type="primary" @click="handleAddCate">新增大类</n-button>
    </div>
    <n-scrollbar style="flex: 1" v-if="cardList.length">
      <div class="card-list">
        <n-card
          class="card-item"
          header-style="padding-left:0;padding-right:0;"
          content-style="padding-left:0;padding-right:0;"
          v-for="item in cardList"
          :key="item.id"
          style="border: none"
        >
          <template #header>
            <div class="card-header">
              <div class="card-title">大类名称</div>
              <LabelInput v-if="hasEditAuth" :state="{ name: item.name, id: item.id }" @refresh="refreshCate" />
              <n-popconfirm
                v-if="hasDeleteAuth"
                positive-text="删除"
                @positive-click="deleteMemberBigTag(item)"
                :positive-button-props="{
                  type: 'error',
                  loading: isDeleteLoading,
                }"
              >
                <template #trigger>
                  <n-button ghost type="error" size="tiny" :disabled="isDeleteLoading" style="margin-left: 8px">
                    删除
                  </n-button>
                </template>
                <span style="color: red">{{ `此操作将删除《${item.name}》大类` }}</span>
                ，是否继续？
              </n-popconfirm>
            </div>
          </template>
          <div class="card-wrap">
            <div class="children-title">分组名</div>
            <div class="children-list">
              <div class="children">
                <div class="label-item" v-for="child in item.subGroupList" :key="child.id">
                  <!-- 标签名 -->
                  <div class="label-name">
                    <n-ellipsis style="max-width: 160px">
                      {{ child.name }}
                    </n-ellipsis>
                    <n-button
                      v-if="hasEditAuth"
                      text
                      size="small"
                      type="primary"
                      class="mt-4 ml-4"
                      @click="openEditInfoModal(child)"
                    >
                      <SvgIcon name="edit" style="font-size: 18px"></SvgIcon>
                    </n-button>
                  </div>
                  <!-- 删除操作 -->
                  <n-popconfirm
                    v-if="hasDeleteAuth"
                    positive-text="删除"
                    @positive-click="deleteMemberTag(child.id)"
                    :positive-button-props="{
                      type: 'error',
                      loading: isDeleteLoading,
                    }"
                  >
                    <template #trigger>
                      <n-button type="error" size="large" text :disabled="isDeleteLoading">
                        <n-icon size="26"><remove-circle /></n-icon>
                      </n-button>
                    </template>
                    <span style="color: red">{{ `此操作将删除《${child.name}》分组` }}</span>
                    ，是否继续？
                  </n-popconfirm>
                </div>
                <!-- 保存分组 -->
                <n-input-group class="group-save" v-if="hasAddAuth">
                  <n-input
                    v-model:value="item.newTag"
                    style="padding-right: 0; margin-right: 2px"
                    placeholder="请输入分组名"
                  >
                    <template #prefix>
                      <n-icon :component="AddCircle" />
                    </template>
                  </n-input>
                  <n-button
                    :disabled="isAddLoading && item.id === modal.curSaveId"
                    :loading="isAddLoading && item.id === modal.curSaveId"
                    type="primary"
                    style="height: 32px"
                    @click="addMemberTag(item)"
                  >
                    保存
                  </n-button>
                </n-input-group>
              </div>
            </div>
          </div>
        </n-card>
      </div>
    </n-scrollbar>
    <!-- 空数据 -->
    <div v-else class="empty-wrapper">
      <img :src="EmptyDataSrc" alt="" />
      <p>暂无数据</p>
    </div>
    <n-spin v-show="loading" class="loading" size="small"></n-spin>
    <!-- 分页组件 -->
    <n-space justify="end">
      <j-pagination
        :pageable="pageVO"
        :handle-size-change="handleSizeChange"
        :handle-current-change="handleCurrentChange"
      />
    </n-space>
  </div>
  <EditCategory v-model:show="show" @refresh="tableSearch" :type="type" :optType="optType" :edit-data="editData" />
</template>

<script setup lang="ts">
import { ref, watch, toRef, computed, onMounted } from "vue";
import EmptyDataSrc from "@/assets/image/exception/emptyData.png";
import { RemoveCircle, AddCircle } from "@vicons/ionicons5";
import EditCategory from "./components/editCategory.vue";
import { deleteDealerGroup, addDealerGroup, getDealerGroupList } from "@/services/api";
import type { Modal, OptType, TypeN } from "./types";
import LabelInput from "../LabelInput.vue";
import { useMessages } from "@/hooks/useMessage";
import { hasEditAuth, hasDeleteAuth, hasAddAuth } from "../../authList";
import { SystemSetting } from "@/settings/systemSetting";
const initParams: Modal = {
  categories: "",
  curSaveId: null,
};
const modal = ref({ ...initParams });
const message = useMessages();
const show = ref<boolean>(false);
const isDeleteLoading = ref<boolean>(false);
const isAddLoading = ref<boolean>(false);
const editData = ref<object>({});
const type = ref<TypeN>(1);
const optType = ref<OptType>("新增");
const cardList = ref<any[]>([]);
/** 分页 */
const pageVO = ref<{ pageSize: number; pageNum: number; total: number }>({
  // 当前页数
  pageNum: 1,
  // 每页显示条数
  pageSize: SystemSetting.pagination.pageSize,
  // 总条数
  total: 0,
});
/** 每页条数改变 */
const handleSizeChange = (val: number) => {
  pageVO.value.pageNum = 1;
  pageVO.value.pageSize = val;
  tableSearch();
};

/** 当前页改变 */
const handleCurrentChange = (val: number) => {
  pageVO.value.pageNum = val;
  tableSearch();
};
const loading = ref(false);
/* 表格搜索 */
const tableSearch = async () => {
  try {
    const params = {
      data: {
        name: modal.value.categories,
      },
      pageVO: {
        size: pageVO.value.pageSize,
        current: pageVO.value.pageNum,
      },
    };
    loading.value = true;
    const { records, total } = await getDealerGroupList(params);
    cardList.value = records.map(item => {
      return {
        ...item,
        //绑定保存的标签
        newTag: "",
      };
    });
    pageVO.value.total = Number(total);
  } catch (error) {
    message.createMessageError("获取失败：" + error);
  } finally {
    loading.value = false;
  }
};
//编辑分组
const openEditInfoModal = (groupInfo: object) => {
  editData.value = groupInfo;
  type.value = 2;
  optType.value = "编辑";
  show.value = true;
};
//新增大类
const handleAddCate = () => {
  type.value = 1;
  optType.value = "新增";
  show.value = true;
};
const deleteMemberTag = async (id: string) => {
  try {
    isDeleteLoading.value = true;
    await deleteDealerGroup({
      id,
      type: 2,
    });
    tableSearch();
    message.createMessageSuccess("删除成功");
  } catch (error) {
    message.createMessageError("删除失败：" + error);
  } finally {
    isDeleteLoading.value = false;
  }
};
const deleteMemberBigTag = async (item: any) => {
  if (item?.subGroupList?.length > 0) {
    message.createMessageWarning("请先删除该大类下的所有分组");
    return;
  }
  try {
    isDeleteLoading.value = true;
    await deleteDealerGroup({
      id: item.id,
      type: 1,
    });
    tableSearch();
    message.createMessageSuccess("删除成功");
  } catch (error) {
    message.createMessageError("删除失败：" + error);
  } finally {
    isDeleteLoading.value = false;
  }
};
const addMemberTag = async (item: any) => {
  if (item.newTag.trim() === "") {
    return message.createMessageError("请输入分组名称");
  }
  //保存当前大类的id
  modal.value.curSaveId = item.id;
  try {
    isAddLoading.value = true;
    const _params = {
      data: {
        name: item.newTag,
        type: 2,
        cateId: item.id,
      },
    };
    await addDealerGroup(_params);
    tableSearch();
    message.createMessageSuccess("新增成功");
  } catch (error) {
    message.createMessageError("新增失败：" + error);
  } finally {
    isAddLoading.value = false;
    modal.value.curSaveId = null;
    item.newTag = "";
  }
};

const refreshCate = (data: any) => {
  cardList.value.forEach(item => {
    if (item.id == data.id) {
      item.name = data.name;
    }
  });
};
onMounted(() => {
  tableSearch();
});
</script>

<style scoped lang="less">
@import "@/styles/default.less";
@child-m: 0 20px 20px 0;
@input-h: 32px;

.group-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
  padding: 20px 24px;

  .header-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20px;
  }
  .card-list {
    .card-item {
      .card-header {
        display: flex;
        align-items: center;
        font-size: 16px;

        .card-title {
          font-weight: bold;
          margin-right: 10px;
        }
      }

      .card-wrap {
        display: flex;

        .children-title {
          font-size: 16px;
          font-weight: bold;
          color: #000;
          margin-right: 10px;
        }

        .children-list {
          flex: 1;

          .children {
            display: flex;
            align-items: center;
            flex-wrap: wrap;

            .label-item {
              margin: @child-m;
              width: 260px;
              height: @input-h;
              display: flex;
              justify-content: center;
              align-items: center;

              .label-name {
                width: 80%;
                height: 100%;
                line-height: 32px;
                display: flex;
                justify-content: center;
                margin-right: 4px;
                border: 1px solid @primary-color;
                border-radius: 4px;
                padding: 0 6px;
              }
            }
          }

          .group-save {
            margin: @child-m;
            height: @input-h;
            width: 260px;
          }
        }
      }
    }
  }

  .empty-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    img {
      width: 200px;
    }

    p {
      color: #666;
      font-size: 16px;
      text-align: center;
    }
  }

  .loading {
    position: absolute;
    top: 51%;
    left: 49%;
  }
}
</style>
