<template>
  <JModal v-model:show="isShowAbnormalShipmentRef" title="发货异常订单" width="1000" positiveText=""
  negativeText="" @close="closeHandle">
    <n-layout>
    <n-layout-content id="VideoManagement">
      <TabsLayout height="700px" paneClass="deliveryExceptionTab" v-model:value="tabNameRef" :tabsData="(tabsData as any)"  :onlyTabs="true" class="tabsLayout">
        <deliveryExceptionTab ref="tableDataUpdate" :tabNameRef="tabNameRef" />
      </TabsLayout>
    </n-layout-content>
  </n-layout>
  </JModal>
</template>

<script setup lang="ts">
import { ref,computed, watch } from 'vue';
import { useAbnormalShipment } from '@/hooks';
const { isShowAbnormalShipmentRef } = useAbnormalShipment();

import TabsLayout from "@/layout/TabsLayout.vue";
import deliveryExceptionTab from './components/deliveryExceptionTab.vue'
import {DeliveryExceptionType} from '@/enums'

const tabNameRef = ref<DeliveryExceptionType>(DeliveryExceptionType.ToBeProcessed);

const closeHandle = () => {
    tabNameRef.value = DeliveryExceptionType.ToBeProcessed
}

const tableDataUpdate = ref()

const tabsData = ref([
  {
    label: "待处理",
    key: DeliveryExceptionType.ToBeProcessed,
  },
  {
    label: "已处理",
    key: DeliveryExceptionType.Processed,
  },
]);


</script>

<style lang="less" scoped>
:deep(.deliveryExceptionTab){
  height: 600px;
}
</style>

