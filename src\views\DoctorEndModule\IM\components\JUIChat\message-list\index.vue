<template>
  <div class="tui-chat">
    <NSpin v-show="messageLoadingRef" style="height: 100%">
    </NSpin>
    <div
        v-show="!messageLoadingRef"
        id="tui-chat-main"
        class="tui-chat-main"
        @click="()=>{}">
      <ul
          id="messageScrollList"
          ref="messageListRef"
          class="tui-message-list"
          @click="()=>{}"
      >
        <p
            v-if="!isCompleted"
            class="message-more"
            @click="getMoreMessage"
        >
          <n-spin v-if="moreMessageLoadingRef" style="height: 12px;width: 12px"/>
          {{ `查看更多` }}
        </p>
        <p v-else class="message-more">
          {{ `没有更多数据了` }}
        </p>
        <li v-for="(item,index) in messageList"
            :id="`tui-${item.id}`"
            :key="item.id"
            ref="messageElementListRef"
            class="message-li"
        >
          <MessageTimestamp
              :currTime="item.msgTime"
              :prevTime="index > 0 ? messageList[index - 1].msgTime : 0"
          />
          <MessageCustomSystem
              v-if="isMessageCustomSysType(item)"
              :content="geCustomSysMessageContent(item)"
              :message-item="item"
          />
          <MessagePrescription
              v-else-if="isMessagePrescriptionType(item)"
              :content="getCardMessageContent(item)"
              :messageItem="item"
          />

          <div class="message-item" v-else>
            <div :class="{'message-event-bind-div': true,}">
              <MessageBubble
                  :content="item.content"
                  :blinkMessageIDList="blinkMessageIDList"
                  :isMultipleSelectMode="isMultipleSelectMode"
                  :messageItem="JSON.parse(JSON.stringify(item))"
                  :multipleSelectedMessageIDList="multipleSelectedMessageIDList"
                  @blinkMessage="blinkMessage"
                  @resendMessage="resendMessage(item)"
              >
                <template #messageElement>
                  <MessageText
                      v-if="isMessageTextType(item)"
                      :content="getTextMessageContent(item)"
                      :messageItem="item"
                  />
                  <MessageImage
                      v-else-if="isMessageImgType(item)"
                      :content="getImgMessageContent(item)"
                      :messageItem="item"
                  />
                  <MessageReception
                      v-else-if="isMessageReceptionType(item)"
                      :content="getCardMessageContent(item)"
                      :messageItem="item"
                  />
                </template>
              </MessageBubble>
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, computed, onMounted, onUnmounted, unref, watch, inject, reactive, nextTick} from 'vue'
import MessageTimestamp
  from "@/views/DoctorEndModule/IM/components/JUIChat/message-list/message-elements/message-timestamp.vue";
import {isEnabledMessageReadReceiptGlobal, safeParse,} from "@/views/DoctorEndModule/IM/utils/IMUtils";
import MessageText from "@/views/DoctorEndModule/IM/components/JUIChat/message-list/message-elements/message-text.vue";
import MessageBubble
  from '@/views/DoctorEndModule/IM/components/JUIChat/message-list/message-elements/message-bubble.vue'
import MessageImage from '@/views/DoctorEndModule/IM/components/JUIChat/message-list/message-elements/message-image.vue'
import type {IMessageModel} from "@tencentcloud/chat-uikit-engine";
import {StoreName, TUIChatEngine, TUIChatService, TUIStore} from "@tencentcloud/chat-uikit-engine";
import chatStorage from "@/views/DoctorEndModule/IM/utils/chatStorage";
import {getBoundingClientRect, getScrollInfo} from '@tencentcloud/universal-api';
import MessagePrescription
  from "@/views/DoctorEndModule/IM/components/JUIChat/message-list/message-elements/message-prescription.vue";
import MessageReception
  from "@/views/DoctorEndModule/IM/components/JUIChat/message-list/message-elements/message-reception.vue";
import MessageCustomSystem
  from "@/views/DoctorEndModule/IM/components/JUIChat/message-list/message-elements/message-custom-system.vue";
import {getConversationIMMessagePage} from "@/services/api/doctorEndApi";
import {useUserStore} from "@/stores/modules/user";
import type {JChatMessageModel} from "@/views/DoctorEndModule/IM/types";
import {
  CHAT_MSG_CUSTOM_TYPE,
  type ServiceSysStatus,
  SysStatusMap
} from "@/views/DoctorEndModule/IM/types";
import {isNullOrUnDef, transformMinioSrc} from "@/utils";
import ImEmitter from "@/views/DoctorEndModule/IM/utils/ImMitter";
import {useMessages} from "@/hooks";
import ImEMitter from "@/views/DoctorEndModule/IM/utils/ImMitter";

interface ScrollConfig {
  scrollToMessage?: IMessageModel;
  scrollToBottom?: boolean;
  scrollToOffset?: {
    top?: number;
    bottom?: number;
  };
}

interface IProps {
  isGroup: boolean;
  groupID: string;
  isNotInGroup: boolean;
  isMultipleSelectMode: boolean;
}

interface IEmits {
  (key: 'closeInputToolBar'): void;

  (key: 'toggleMultipleSelectMode'): void;

  (key: 'handleEditor', message: IMessageModel, type: string): void;
}

const emits = defineEmits<IEmits>();
const props = withDefaults(defineProps<IProps>(), {
  isGroup: false,
  groupID: '',
  isNotInGroup: false,
  isMultipleSelectMode: false,
});

let groupType: string | undefined;
let observer: IntersectionObserver | null = null;
const sentReceiptMessageIDSet = new Set<string>();

const messages = useMessages();
const messageListRef = ref<HTMLElement>();
const initMessageLoadingRef = ref<boolean>()
const moreMessageLoadingRef = ref<boolean>()
// The messageList displayed on the screen, not including messages where isDeleted is true
const messageList = ref<JChatMessageModel[]>();
// All messageList, including messages where isDeleted is true
const multipleSelectedMessageIDList = ref<string[]>([]);
const isCompleted = ref(false);
const currentConversationID = ref('');
const nextReqMessageID = ref();
const toggleID = ref('');
const TYPES = ref(TUIChatEngine.TYPES);
const isLongpressing = ref(false);
const messageElementListRef = ref<HTMLElement[] | null>();
const blinkMessageIDList = ref<string[]>([]);
const scrollButtonInstanceRef = ref<any>();
const beforeHistoryGetScrollHeight = ref<number>(0);
const isTopMessageDom = ref<boolean>(false);
const audioPlayedMapping = ref<Record<string, boolean>>({});

// image preview
const showImagePreview = ref(false);
const currentImagePreview = ref<IMessageModel>();

const {currentConversationData, changeCurrentConversation} = inject('currentConversationData');
const userStore = useUserStore();


/** 分页 */
const pageable = reactive({
  // 当前页数
  pageNum: 1,
  // 每页显示条数
  pageSize: 20,
  // 总条数
  total: 0,
});

async function getCurrentConversationMessageData(isInit: boolean = true) {
  try {
    if (isInit) {
      initMessageLoadingRef.value = true
    } else {
      moreMessageLoadingRef.value = true
    }
    const params = {
      data: {
        conversationId: currentConversationData.value.conversationId,
        doctorId: userStore.imConfig.userID,
        patientId: currentConversationData.value.contactUserId,
        msgSeq: messageList.value?.[0].msgSeq ?? undefined,
        msgTime: messageList.value?.[0].msgTime ?? undefined,
      },
      pageVO: {
        current: pageable.pageNum,
        size: pageable.pageSize,
      },
    };
    let messageListResult = await getConversationIMMessagePage(params);
    if (messageListResult && messageListResult.length > 0) {
      messageListResult = messageListResult.map(item => {
        if (item.fromImUserId !== userStore.imConfig.userID) {
          // 设置头像
          item.img = currentConversationData.value.img
        } else {
          item.img = transformMinioSrc(userStore.userInfo.doctorImg)
        }
        return item
      })
      if (pageable.pageNum == 1) {
        messageList.value = messageListResult
      } else {
        messageList.value.unshift(...messageListResult)
      }
      pageable.pageNum += 1

    } else {
      // 获取得到消息数据为空，则认为已获取所有历史数据
      isCompleted.value = true
    }
    isInit && scrollToLastMessagePosition()
  } catch (e) {
    console.log('e===>', e)
    messageList.value = []
  } finally {
    setTimeout(() => {
      initMessageLoadingRef.value = false
      moreMessageLoadingRef.value = false
    }, 500)
  }
}

function getMoreMessage() {
  getCurrentConversationMessageData(false);
}

// resend message dialog
const reSendDialogShow = ref(false);
const resendMessageData = ref();

onMounted(async () => {
  await getCurrentConversationMessageData()
  ImEMitter.on('GetSendMessageResult', async (messageResult: IMessageModel) => {
    if (messageResult) {
      // 设置医生头像
      messageResult.img = transformMinioSrc(userStore.userInfo.doctorImg)
      messageList.value.push(convertMessageType(messageResult))
      scrollToLastMessagePosition()
    }
  })
  ImEmitter.on('RefreshConversationList', async () => {
    messageList.value = []
  });
  TUIStore.watch(StoreName.CHAT, {
    // 监听 newMessageList 新消息的变化
    newMessageList: onNewMessageListUpdated
  });
});

onUnmounted(() => {
  TUIStore.unwatch(StoreName.CHAT, {
    // 监听 newMessageList 新消息的变化
    newMessageList: onNewMessageListUpdated
  });
  // ImEMitter.off('GetSendMessageResult')
  // ImEmitter.off('RefreshConversationList')

  sentReceiptMessageIDSet.clear();
  observer?.disconnect();
  observer = null;
});


async function onNewMessageListUpdated(list: IMessageModel[]) {
  console.log('list====>', list)
  if (isNullOrUnDef(currentConversationData.value)) return
  if (Array.isArray(list) && list.length > 0) {
    let newMessageItem = list[list.length - 1]
    if (newMessageItem.conversationID == `C2C${currentConversationData.value?.contactImUserId}`) {
      if (messageList.value.findIndex(item => item.msgId == newMessageItem.ID) < 0) {
        if (newMessageItem?.from !== userStore.imConfig.userID) {
          // 设置患者头像
          newMessageItem.img = currentConversationData.value.img
        } else {
          // 设置医生头像
          newMessageItem.img = transformMinioSrc(userStore.userInfo.doctorImg)
        }
        messageList.value?.push(convertMessageType(newMessageItem))
      }
    }
  }
  scrollToLastMessagePosition()
}

// 从IM消息类型转换到我们系统的消息类型
function convertMessageType(item: IMessageModel): JChatMessageModel {
  const payloadData = safeParse(item.payload.data)
  let newItem: JChatMessageModel = {
    content: '',
    conversationId: payloadData.conversationId,
    fromImUserId: item.from,
    fromUserId: payloadData.fromUserId,
    fromUserType: payloadData.fromUserType,
    id: payloadData.id,
    msgId: item.ID,
    msgKey: '',
    msgRandom: '',
    msgSeq: item?.clientSequence,
    msgTime: item?.clientTime,
    sendMsgResult: 0,
    toImUserId: payloadData.toUserId,
    toUserId: payloadData.toUserId,
    toUserType: payloadData.toUserType,
    type: item.payload.description,
    img: item?.img || ''
  }

  if (item.payload.description == CHAT_MSG_CUSTOM_TYPE.TEXT) {
    newItem.content = payloadData.content
  }
  if (item.payload.description == CHAT_MSG_CUSTOM_TYPE.SYS) {
    newItem.content = payloadData.content
    newItem.contentType = payloadData.contentType
  }
  if (item.payload.description == CHAT_MSG_CUSTOM_TYPE.IMG) {
    newItem.originCdn = payloadData.originCdn
    newItem.origin = payloadData.origin
  }
  if (item.payload.description == CHAT_MSG_CUSTOM_TYPE.PRES_CARD) {
    newItem.content = payloadData.content
  }
  if (item.payload.description == CHAT_MSG_CUSTOM_TYPE.FORMULARY_CARD) {
    newItem.content = payloadData.content
    newItem.contentType = payloadData.contentType
  }

  return newItem
}

function scrollToLastMessagePosition() {
  nextTick(() => {
    scrollToPosition({scrollToBottom: true})
  })
}

async function scrollToPosition(config: ScrollConfig = {}): Promise<void> {
  return new Promise((resolve, reject) => {
    requestAnimationFrame(() => {
      const container = messageListRef.value;
      container.scrollTo({
        top: container?.scrollHeight,  // 滚动到容器总高度
        behavior: 'smooth'            // 启用平滑动画
      });
      resolve();
    });
  });
}

const onCurrentConversationIDUpdated = (conversationID: string) => {
  currentConversationID.value = conversationID;
  if (!currentConversationID.value) {
    messageList.value = [];
  }
  if (isEnabledMessageReadReceiptGlobal()) {
    const {groupProfile} = TUIStore.getConversationModel(conversationID) || {};
    groupType = groupProfile?.type;
  }
  if (Object.keys(audioPlayedMapping.value).length > 0) {
    // Synchronize storage about whether the audio has been played when converstaion switched
    chatStorage.setChatStorage('audioPlayedMapping', audioPlayedMapping.value);
  }
};

const resendMessage = (message: JChatMessageModel) => {
  reSendDialogShow.value = true;
  resendMessageData.value = message;
};

function blinkMessage(messageID: string): Promise<void> {
  return new Promise((resolve) => {
    const index = blinkMessageIDList.value.indexOf(messageID);
    if (index < 0) {
      blinkMessageIDList.value.push(messageID);
      const timer = setTimeout(() => {
        blinkMessageIDList.value.splice(blinkMessageIDList.value.indexOf(messageID), 1);
        clearTimeout(timer);
        resolve();
      }, 3000);
    }
  });
}

async function scrollToLatestMessage() {
  const {scrollHeight} = await getScrollInfo('#messageScrollList');
  const {height} = await getBoundingClientRect('#messageScrollList');
  if (messageListRef.value) {
    messageListRef.value.scrollTop = scrollHeight - height;
  }
}

// 文本消息和自定义文本消息
const isMessageTextType = (item: JChatMessageModel): boolean => {
  return item.type === CHAT_MSG_CUSTOM_TYPE.TEXT
}

// 图片消息和自定义图片消息
const isMessageImgType = (item: JChatMessageModel): boolean => {
  return item.type === CHAT_MSG_CUSTOM_TYPE.IMG
}

// 自定义消息-系统消息
const isMessageCustomSysType = (item: JChatMessageModel): boolean => {
  return item.type == CHAT_MSG_CUSTOM_TYPE.SYS
}

// 处方卡片消息
const isMessagePrescriptionType = (item: JChatMessageModel): boolean => {
  return item.type == CHAT_MSG_CUSTOM_TYPE.FORMULARY_CARD
}

// 问诊卡片消息
const isMessageReceptionType = (item: JChatMessageModel): boolean => {
  return item.type == CHAT_MSG_CUSTOM_TYPE.PRES_CARD
}

// 设置文本content
const getTextMessageContent = (item: JChatMessageModel): any => {
  if (item.type === CHAT_MSG_CUSTOM_TYPE.TEXT) {
    return item.content
  }
  return '';
}

// 设置图片content
const getImgMessageContent = (item: JChatMessageModel): any => {
  if (item.type === CHAT_MSG_CUSTOM_TYPE.IMG) {
    return transformMinioSrc(item.origin)
  }
  return '';
}

const geCustomSysMessageContent = (item: JChatMessageModel): any => {
  if (isMessageCustomSysType(item)) {
    return SysStatusMap[item?.contentType as ServiceSysStatus]
  }
  return ''
}

// 获取处方卡片或者问诊卡片消息
const getCardMessageContent = (item: JChatMessageModel): any => {
  if (isMessagePrescriptionType(item) || isMessageReceptionType(item)) {
    return safeParse(item.content)
  }
  return {}
}


defineExpose({
  scrollToLatestMessage,
});


</script>

<style scoped lang="less">

:deep(.message-more .n-base-loading__icon) {
  width: 12px;
  height: 12px;
}

.tui-chat {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  &-main {
    min-height: 0;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;

    .tui-chat-safe-tips {
      padding: 12px 20px;
      background-color: rgba(255, 149, 0, 0.1);
      color: #ff8c39;
      line-height: 18px;
      font-family: PingFangSC-Regular;
      font-style: normal;
      font-weight: 400;
      text-align: justify;
      font-size: 12px;

      a {
        color: #006eff;
        float: right;
      }
    }

    .tui-chat-application-tips {
      text-align: center;
      width: 100%;
      background: #fce4d3;
      padding: 2px;
      font-size: 12px;
    }

    .application-tips-btn {
      color: #006eff;
      padding-left: 10px;
    }

    .tui-message-list {
      flex: 1;
      height: 100%;
      overflow: hidden auto;

      .message-more {
        color: #999;
        font-size: 12px;
        margin: 10px auto;
        text-align: center;
        cursor: pointer;
      }

      .to-bottom-tip {
        position: sticky;
        bottom: 10px;
        left: 100%;
        margin-right: 15px;
        width: 92px;
        height: 28px;
        padding: 0 5px;
        background: #fff;
        border: 1px solid #e0e0e0;
        box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.06);
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        border-radius: 3px;
        cursor: pointer;

        &-text {
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 10px;
          color: #147aff;
          letter-spacing: 0;
          text-align: center;
          padding-left: 3px;
        }
      }

      .message-li {
        &:first-child {
          margin-top: 5px;
        }

        display: flex;
        flex-direction: column;

        .message-item {
          display: flex;
          position: relative;
          flex-direction: column;

          .message-tool {
            z-index: 5;
            position: absolute;
            cursor: pointer;
            transform: translateY(-100%);
          }

          .message-tool-out {
            right: 30px;
            left: auto;
          }

          .message-tool-in {
            left: 30px;
            right: auto;
          }

          .message-tool-bottom {
            z-index: 5;
            bottom: 0;
            transform: translateY(100%);
          }
        }

        .message-label {
          max-width: 50px;
        }
      }

      .right {
        flex-direction: row-reverse;
        justify-content: flex-start;
      }
    }
  }

  .disabled {
    position: relative;

    &::before {
      content: "";
      position: absolute;
      width: 100%;
      height: 100%;
    }
  }
}

.image-dialog {
  position: fixed;
  z-index: 5;
  width: 100vw;
  height: calc(100vh - 63px);
  top: 63px;
  left: 0;

  header {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    box-sizing: border-box;
    padding: 10px;
  }
}

::-webkit-scrollbar {
  width: 6px;
  height: 140px;
  background-color: transparent;
}

::-webkit-scrollbar-track {
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: #9a999c;
}

</style>
