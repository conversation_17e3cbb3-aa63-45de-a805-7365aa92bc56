<template>
  <div
      ref="conversationListInnerDomRef"
      class="tui-conversation-list"
      style="height: 100%"
  >
    <n-spin size="small" :show="isConversationLoading" style="height: calc(100vh - 120px);">
      <n-virtual-list
          v-if="!isConversationLoading && conversationHistoryData.length > 0"
          style="max-height: calc(100vh - 120px)"
          :item-size="42"
          :items="conversationHistoryData"
          item-resizable
          @scroll="handleScroll"
      >
        <template #default="{ item:conversation}">
          <div class="tui-conversation-content" :key="conversation.key">
            <div
                style="height: 60px;box-sizing: border-box;padding: 12px;display: flex;cursor: pointer;"
                :class="['tui-conversation-item',conversationItemClass(conversation)]"
                @click.prevent="enterConversationChat(conversation)">
              <div class="left" style="width: 36px;height: 100%;">
                <n-image v-if="conversation.img" :src="conversation.img" class="avatar" width="36"/>
                <n-image v-else :src="avatarSrc" class="avatar" width="36" alt=""/>
                <span v-if="conversation.unreadCount > 0" class="num">
                  <!--       TODO 可能要改成显示未读消息数，现在先显示红点           -->
                  <!--                  {{ conversation.unreadCount > 99 ? "99+" : conversation.unreadCount }}-->
                </span>
              </div>
              <div class="content"
                   style="flex: 1;padding-left: 8px;overflow: hidden;display: flex;justify-content: space-between">
                <div class="content-header"
                     style="flex:1;height: 36px;display: flex;flex-direction: column;justify-content: space-between;width: 0">
                  <div class="content-header-label">
                    <p class="name"
                       style="width:100%;letter-spacing: 0;font-size: 14px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;box-sizing: border-box;">
                      {{ conversation.nickname || '-' }}
                    </p>
                  </div>
                  <div class="middle-box">
                    <div class="middle-box-content"
                         style="width: 100%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;font-size: 12px;line-height: 16px">
                      {{ showLastMessageContent(conversation) }}
                    </div>
                  </div>
                </div>
                <div class="content-footer"
                     style="font-size: 12px;line-height: 16px;display: inline-block;white-space: nowrap;color: #bbb;">
                  <span>{{ showLastMessageTime(conversation) }}</span>
                </div>
              </div>
            </div>
          </div>
        </template>
      </n-virtual-list>
      <div style="height: calc(100vh - 120px);display: flex;justify-content: center;align-items: center" v-else>
        <div class="empty-wrapper">
          <img :src="EmptySrc" alt=""/>
          <p>暂无数据</p>
        </div>
      </div>
    </n-spin>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive, onMounted, onUnmounted, inject, computed, onUpdated, onBeforeUpdate} from 'vue'
import type {IConversationModel, IMessageModel} from "@tencentcloud/chat-uikit-engine";
import {StoreName, TUIChatEngine, TUIConversationService, TUIStore} from "@tencentcloud/chat-uikit-engine";
import avatarSrc from "@/assets/image/system/avatar.png";
import {CHAT_MSG_CUSTOM_TYPE, type JConversationModel} from "@/views/DoctorEndModule/IM/types";
import {formatMessageLastTime, safeParse} from "@/views/DoctorEndModule/IM/utils/IMUtils";
import {useMessages} from "@/hooks";
import {getConversationPage, getPresReceiveEndTime} from "@/services/api/doctorEndApi";
import {useUserStore} from "@/stores/modules/user";
import EmptySrc from "@/assets/image/exception/empty.png";
import ImEmitter from "@/views/DoctorEndModule/IM/utils/ImMitter";
import moment from "moment";
import ImEMitter from "@/views/DoctorEndModule/IM/utils/ImMitter";
import { _throttle } from "@/utils";


interface IUserStatus {
  statusType: number;
  customStatus: string;
}

defineOptions({
  name: 'JConversationList'
})

/** 用户 store */
const userStore = useUserStore();
const {tabName} = inject('tabName'); // 当前的TabName,值为 processing | completed
const emits = defineEmits(['handleSwitchConversation', 'getPassingRef']);
const {createMessageSuccess, createMessageError} = useMessages();
const isConversationLoading = ref(false);
const conversationListInnerDomRef = ref<HTMLElement | undefined>();
// 历史消息数据，通过接口获取、当接收到im的新消息，则合并到里面去
const conversationHistoryData = ref<JConversationModel[]>([]);
// 消息会话列表
const conversationMapData = ref(new Map());
// 当前会话缓存
const {currentConversationData, changeCurrentConversation} = inject('currentConversationData');

const handleSearchConversationHistoryData = _throttle(getConversationHistoryData, 500);

onMounted(() => {
  // 监听请求刷新事件
  ImEmitter.on('RefreshConversationList', async () => {
    console.log('会话列表这里的RefreshConversationList监听触发了')
    conversationHistoryData.value = []
    changeCurrentConversation(null)
    await handleSearchConversationHistoryData(true,'位置1')
  });
  ImEMitter.on('GetSendMessageResult', async (newMessageItem: IMessageModel) => {
    // 发送消息成功，更新会话列表页数据
    if (newMessageItem) {
      conversationHistoryData.value = conversationHistoryData.value.map((item: JConversationModel) => {
        if (newMessageItem.conversationID == `C2C${item.contactImUserId}`) {
          const payloadData = safeParse(newMessageItem.payload.data)
          item.lastContent = payloadData?.content || '';
          item.lastMsgId = newMessageItem.ID
          item.lastMsgType = newMessageItem.payload.description
          item.lastContentTime = moment.unix(newMessageItem.time).format('YYYY-MM-DD HH:mm:ss')
        }
        return item
      }).sort(sortTime)
    }
  })
})

onUnmounted(() => {
  // ImEmitter.off('RefreshConversationList')
  // ImEMitter.off('GetSendMessageResult')
})


function sortTime(a, b) {
  const timeA = new Date(a.lastContentTime).getTime();
  const timeB = new Date(b.lastContentTime).getTime();
  return timeB - timeA; // 降序排列
}

// 当前的IM页面问诊状态是否为已完成
const isCompletedTab = computed(() => {
  return tabName.value == 'completed'
})

/** 分页 */
const pageVO = ref({
  // 当前页数
  current: 1,
  // 每页显示条数
  pageSize: 50,
  // 总条数
  total: 0,
});


onMounted(async () => {
  await getConversationHistoryData(true,'位置2')
  // 监听会话列表更新
  if (!isCompletedTab.value) {
    TUIStore.watch(StoreName.CHAT, {
      // 监听 newMessageList 新消息的变化
      newMessageList: onMessageListUpdated
    });
  }
});

onUnmounted(() => {
  if (!isCompletedTab.value) {
    TUIStore.unwatch(StoreName.CHAT, {
      // 监听 newMessageList 新消息的变化
      newMessageList: onMessageListUpdated
    });
  }
});


const onMessageListUpdated = (messageList: IMessageModel[]) => {
  if (Array.isArray(messageList) && messageList.length > 0) {
    let newMessageItem = messageList[messageList.length - 1]
    conversationHistoryData.value = conversationHistoryData.value.map((item: JConversationModel) => {
      if (newMessageItem.conversationID == `C2C${item.contactImUserId}`) {
        const payloadData = safeParse(newMessageItem.payload.data)
        item.lastContent = payloadData?.content || '';
        item.lastMsgId = newMessageItem.ID
        item.lastMsgType = newMessageItem.payload.description
        item.lastContentTime = moment.unix(newMessageItem.time).format('YYYY-MM-DD HH:mm:ss')
        // 不是当前聊天消息窗口的会话，则显示未读红点
        if (newMessageItem.conversationID == `C2C${item.contactImUserId}`) {
          item.unreadCount = 1
          // 当前聊天消息窗口会话，则不显示
          if (currentConversationData.value?.conversationId == payloadData?.conversationId) {
            item.unreadCount = 0
          }
        }
      }
      return item
    }).sort(sortTime)
  }
}

const enterConversationChat = async (conversation: JConversationModel) => {
  if (tabName.value == 'completed') {
    changeCurrentConversation(conversation)
    // ImEMitter.emit('ChangeConversation', conversation)
  } else {
    changeCurrentConversation(conversation)
    conversation.unreadCount = 0
    conversationHistoryData.value = conversationHistoryData.value.map(item => {
      if (item.contactImUserId == conversation.contactImUserId) {
        item.unreadCount = 0
      }
      return item
    })
    emits('handleSwitchConversation', conversation);
  }
};

// 获取会话分页。当触发监听后，获取数据
async function getConversationHistoryData(isInit: boolean = true,position:string) {
  console.log('position====>',position)
  const resetData = () => {
    conversationHistoryData.value = [];
  };
  // 更新页
  const updatePageable = ({current, size, total}) => {
    Object.assign(pageVO.value, {
      current: Number(current),
      pageSize: Number(size),
      total: Number(total),
    });
  };
  // 处理返回值
  const handleResponse = (records) => {
    if (isInit) {
      conversationMapData.value.clear()
      conversationHistoryData.value = []
      if (!records || records.length == 0) return
      //   完成的会话列表数据
      if (tabName.value == 'completed') {
        conversationHistoryData.value = records.map(item => {
          item.unreadCount = 0
          return item
        });
      } else {
        conversationHistoryData.value = records.map(item => {
          if (item?.lastMsgType == CHAT_MSG_CUSTOM_TYPE.PRES_CARD) {
            item.unreadCount = 1
          } else {
            item.unreadCount = 0
          }
          return item
        });
      }
    } else {
      if (!records || records.length == 0) return
      if (tabName.value == 'completed') {
        records.forEach(item => {
          item.unreadCount = 0
          conversationHistoryData.value.push(item)
        });
      } else {
        records.forEach(item => {
          if (item?.lastMsgType == CHAT_MSG_CUSTOM_TYPE.PRES_CARD) {
            item.unreadCount = 1
          } else {
            item.unreadCount = 0
          }
          conversationHistoryData.value.push(item)
        });
      }
    }
  };

  try {
    isConversationLoading.value = true;
    const _params = {
      data: {
        userId: userStore.imConfig?.userID,
        interviewStatus: isCompletedTab.value ? 1 : 0,
        userType: 1,
      },
      pageVO: {
        current: isInit ? 1 : pageVO.value.current,
        size: pageVO.value.pageSize,
      },
    };
    const resp = await getConversationPage(_params);
    if (resp && resp.pageDTO) {
      const {current, records, size, total} = resp.pageDTO;
      handleResponse(records);
      updatePageable({current, size, total});
    }
  } catch (error) {
    // createMessageError(`获取会话列表失败: ${error}`);
  } finally {
    setTimeout(() => {
      isConversationLoading.value = false;
    }, 100)
  }
}

// 自定义的conversation id
const conversationItemClass = (conversation) => {
  if (!currentConversationData.value) {
    return 'tui-conversation-item-no-selected'
  }
  return currentConversationData.value.contactUserId === conversation.contactUserId ?
      'tui-conversation-item-selected' : 'tui-conversation-item-no-selected'
}

function showLastMessageTime(conversation) {
  if (!conversation.lastContentTime) {
    return ''
  }
  const timestamp = moment(conversation?.lastContentTime).format(`YYYY-MM-DD HH:mm:ss`)
  return formatMessageLastTime(timestamp)
}

function showLastMessageContent(conversation) {
  let lastMessage = ''
  if (conversation.lastMsgType == CHAT_MSG_CUSTOM_TYPE.TEXT) {
    lastMessage = conversation?.lastContent || ''
  }
  if (conversation.lastMsgType == CHAT_MSG_CUSTOM_TYPE.IMG) {
    lastMessage = '[图片]'
  }
  if (conversation.lastMsgType == CHAT_MSG_CUSTOM_TYPE.PRES_CARD) {
    lastMessage = '[问诊卡]'
  }
  if (conversation.lastMsgType == CHAT_MSG_CUSTOM_TYPE.FORMULARY_CARD) {
    lastMessage = '[处方笺]'
  }
  if (conversation.lastMsgType == CHAT_MSG_CUSTOM_TYPE.SYS) {
    lastMessage = '[系统消息]'
  }
  return lastMessage
}

/** 滚动加载 */
function handleScroll(e) {
  const currentTarget = e.currentTarget as HTMLElement
  if (
      currentTarget.scrollTop + currentTarget.offsetHeight >=
      currentTarget.scrollHeight
  ) {
    if (pageVO.value.current * pageVO.value.pageSize < pageVO.value.total) {
      pageVO.value.current++;
      getConversationHistoryData(false,'位置3')
    }
  }
}

// Expose to the parent component and close actionsMenu when a sliding event is detected
defineExpose({});

</script>

<style scoped lang="less">
.tui-conversation-list {

}

.tui-conversation-item {

  .left {
    position: relative;
    width: 36px;
    height: 36px;

    .num {
      position: absolute;
      display: inline-block;
      right: -5px;
      top: -5px;
      min-width: 10px;
      width: fit-content;
      height: 10px;
      font-size: 10px;
      text-align: center;
      line-height: 15px;
      border-radius: 7.5px;
      background: red;
      color: #fff;
    }
  }
}

.tui-conversation-item-no-selected {
  &:hover {
    background-color: #e3e4e6;
  }
}

.tui-conversation-item-selected {
  background: rgba(0, 110, 255, 0.1);
}

.item {
  display: flex;
  align-items: center;


}

.avatar {
  width: 36px;
  border-radius: 5px;
}

.content {

}

.empty-wrapper {
  img {
    height: 50px;
  }

  p {
    color: #666;
    font-size: 12px;
    text-align: center;
  }
}
</style>
