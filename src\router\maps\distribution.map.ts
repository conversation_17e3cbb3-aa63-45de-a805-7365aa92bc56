import BlankLayout from '@/layout/BlankLayout.vue'
import { RoutesName } from "@/enums/routes";
import type { RouteLocation } from "vue-router";

export const Default = {
  [RoutesName.structreModule]: {
    path: "distribution",
    component: BlankLayout,
    meta: {
      title: "分销",
    },
  },
  [RoutesName.structreSettings]: {
    path: "distributionSettings",
    component: () => import("@/views/Distribution/DistributionSettings/index.vue"),
    meta: {
      title: "分销设置",
      icon: 'distribution'
    }
  },
  [RoutesName.structreMemberManagement]: {
    path: "distributorManagement",
    component: () => import("@/views/Distribution/DistributorManagement/index.vue"),
    meta: {
      title: "分销员管理",
      icon: 'distributor-user'
    }
  },
  [RoutesName.structreCommissionDeatail]: {
    path: "commissionBreakdown",
    component: () => import("@/views/Distribution/CommissionBreakdown/index.vue"),
    meta: {
      title: "佣金明细",
      icon: 'commission-breakdown'
    }
  },
  [RoutesName.structreDepartment]: {
    path: "organizationalStructure",
    component: () => import("@/views/Distribution/OrganizationalStructure/index.vue"),
    meta: {
      title: "组织架构",
      icon: 'organizational-structure'
    }
  },
  [RoutesName.structreApplication]: {
    path: "organizationalApplication",
    component: () => import("@/views/Distribution/OrganizationalApplication/index.vue"),
    meta: {
      title: "组织申请",
      icon: 'organizational-application'
    }
  },
};
