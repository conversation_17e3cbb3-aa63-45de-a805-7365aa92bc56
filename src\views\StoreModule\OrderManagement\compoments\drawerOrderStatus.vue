<template>
  <JDrawer
    v-model:show="drawerVisible"
    :title="buttonStatusValue == 'waitFeliverGoods' ? '订单发货' : '订单签收'"
    @after-leave="closeDrawer('cancel')"
    to="#OrderManagement"
  >
    <template #content>
      <n-form
        ref="formRef"
        :model="model"
        :rules="rules"
        label-width="auto"
        label-placement="left"
        require-mark-placement="right-hanging"
        size="small"
        :style="{
          width: '100%',
        }"
      >
        <n-grid :cols="12">
                <n-gi :span="4">
                  <n-form-item-gi  label="订单号" >
                    <n-input v-model:value="model.orderNumberValue" type="text"   :disabled="true" style="width: 100%;" />
                  </n-form-item-gi>
                  <n-form-item-gi  label="快递公司" v-if="buttonStatusValue == 'waitFeliverGoods' && pickupType != 2" path="expressCompanyValue">
                    <JExpressCompany v-model:value="model.expressCompanyValue" v-model:shipCompanyName="model.shipCompanyName" isImmediately style="width: 100%;" />
                  </n-form-item-gi>
                  <n-form-item-gi  label="快递单号" v-if="buttonStatusValue == 'waitFeliverGoods' && pickupType != 2" path="trackingNumberValue">
                    <n-input v-model:value="model.trackingNumberValue" type="text" style="width: 100%;" />
                  </n-form-item-gi>
                  <n-form-item-gi  label="签收类型" path="signInTypeValue" v-if="buttonStatusValue == 'waitTakeOverGoods' && pickupType != 2" >
                    <n-select v-model:value="model.signInTypeValue" :options="completeTypeOptions.filter(item => item.value)" clearable style="width: 100%;" />
                  </n-form-item-gi>
                  <n-form-item-gi label="核销码" path="verifyCode" v-if="pickupType == 2">
                    <n-input v-model:value="model.verifyCode" type="text" style="width: 100%;" maxlength="19"/>
                  </n-form-item-gi>
                </n-gi>
        </n-grid>
      </n-form>
    </template>

    <template #footer>
      <div class="footer-wrapper">
        <n-space>
          <n-button ghost @click="closeDrawer" class="store-button">取 消</n-button>
          <n-button type="info" :loading="isLoading" @click="_save" class="store-button">
            提 交
          </n-button>
        </n-space>
      </div>
    </template>
  </JDrawer>
</template>

<script setup lang="tsx" name="drawerOrderStatus">
import { ref } from "vue";
import { useMessages } from "@/hooks";
import { deepClone } from "@/utils";
import { completeTypeOptions } from "@/constants";
import { orderDetail, orderVerify } from "@/services/api";

const { createMessageSuccess, createMessageError } = useMessages();

/* 表单参数初始化 */
const initParams = {
  orderNumberValue:null,//订单号
  expressCompanyValue:null,//快递公司
  trackingNumberValue:null,//快递单号
  signInTypeValue:null,//签收类型
  shipCompanyName:null,//快递公司名称
  verifyCode:null,//核销码
};

const model = ref(deepClone(initParams));

const isLoading = ref(false)

/** 发货与签收按钮类型值 */
const buttonStatusValue = ref(null)

/* 表单规则 */
const rules = {
  signInTypeValue: {
    type: "number",
    required: true,
    trigger: ['blur', 'change'],
    message: '请选择签署类型',
  },
  expressCompanyValue:{
    required: true,
    trigger: ['blur', 'change'],
    message: '请选择快递公司',
  },
  trackingNumberValue:{
    required: true,
    trigger: ['blur', 'change'],
    message: '请输入快递单号',
  },
  verifyCode:{
    required: true,
    trigger: ['blur', 'change'],
    message: '核销码',
  },
};

/** 抽屉状态 */
const drawerVisible = ref(false);
const drawerProps = ref();
const orderCode = ref()
const pickupType = ref()
/* 接收父组件传过来的参数 */
const acceptParams = async (params) => {
  drawerProps.value = params;
  buttonStatusValue.value = params.type
  drawerVisible.value = true;
  orderCode.value =  params.row.code
  console.log(params.row);
  const data = await orderDetail(orderCode.value);
  pickupType.value = data.pickupType
  model.value.orderNumberValue = drawerProps.value.row.code
};


/** 关闭抽屉 */
const isLoadingState = ref(true)
const closeDrawer = (type) => {
  if(isLoadingState.value || type == 'cancel'){
    model.value = deepClone(initParams);
    drawerVisible.value = false;
  }
  isLoading.value = false
};

/** 保存 */
const formRef = ref()
const _save = async(e) =>{
  const _paramsHairGoods = {
    code:model.value.orderNumberValue,
    shipCompanyName:model.value.shipCompanyName,
    shipCompanyCode:model.value.expressCompanyValue,
    trackingNo:model.value.trackingNumberValue
  }
  const _paramsSign = {
    code:model.value.orderNumberValue,
    completeType:model.value.signInTypeValue
  }
  const _paramsVerify = {
    code:model.value.orderNumberValue,
    verifyCode:model.value.verifyCode
  }
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      try{
        isLoading.value = true
        if (pickupType.value == 2){
          await orderVerify({data:_paramsVerify})
          createMessageSuccess('核销发货成功')
        }else {
          await drawerProps.value.updateInfoApi({data:(buttonStatusValue.value == 'waitFeliverGoods' ? _paramsHairGoods : _paramsSign)})
          createMessageSuccess(buttonStatusValue.value == 'waitFeliverGoods' ? '提交发货成功' : '提交签收成功')
        }
        isLoadingState.value = true
        drawerProps.value.refresh();
      }catch(err){
        isLoadingState.value = false
        if (pickupType.value == 2){
          createMessageError('核销发货失败:' + err);
        }else {
          createMessageError((buttonStatusValue.value == 'waitFeliverGoods' ? "提交发货失败:" : '"提交签收失败:"') + err);
        }
      }finally{
        closeDrawer("save")
      }
    }
  });
}

const emits = defineEmits<{
  (e: "update:value", value);
}>();


defineExpose({
  acceptParams,
  closeDrawer,
  drawerVisible
});
</script>

<style scoped lang="less">
.footer-wrapper {
  display: flex;
  align-items: center;
  justify-content: end;
}
</style>
  