<template>
    <n-card size="small" :bordered="false">
        <div class="wrapper">
            <span style="font-weight: 700; color: #333;">当前冻结中的库存：{{ props.quantityInStock?.lockedStocks ?? 0 }}</span>
            <p style="color: #999;margin-bottom: 24px;">待支付的订单库存会被冻结，用户如果主动取消订单或超时未支付，库存会释放。</p>

            <span style="font-weight: 700; color: #333;">当前可下单的库存：{{ props.quantityInStock?.availStocks ?? 0 }}</span>
            <p style="color: #999;margin-bottom: 24px;">冻结中的库存如果释放，当前可下单的库存会相应增加。</p>

            <p style="color: #333;">为防止超卖情况，修改库存时建议考虑冻结中的库存数，填写“当前可下单的库存”。当前可下单的库存 = 盘点到的实际库存 - 当前冻结中的库存。</p>
        </div>
    </n-card>
</template>

<script lang="ts" setup name='InventoryDetail'>

/** props */
const props = defineProps<{
    quantityInStock: {
        lockedStocks: number, // 冻结库存
        availStocks: number; // 可下单库存
    }
}>();

</script>


<style lang="less" scoped>
.wrapper {
    width: 520px;
}
</style>