<template>
  <div :style="`width:${props.width}`">
  <n-popover
    :placement="props.placement"
    trigger="click"
    :style="{ backgroundColor: '#fff', padding: '0px' }"
    :show-arrow="false"
    :show="state.showPopover"
  >
    <template #trigger>
      <n-input
        pair
        :separator="props.separator"
        :value="state.inputValue"
        :clearable="props.clearable"
        @click="()=>{
          if(!props.disabled){
            state.showPopover = !state.showPopover
          }
        }"
        :placeholder="props.placeholder"
        :size="props.size"
        :style="props.style"
        :disabled="props.disabled"
      >
        <template #prefix v-if="props.prefix">
          <n-icon size="16" :depth="2" style="padding: 0px 2px">
            <Calendar />
          </n-icon>
        </template>
        <template #suffix v-if="props.suffix">
          <n-icon size="16" :depth="2" style="padding: 0px 2px">
            <Calendar />
          </n-icon>
        </template>
        <template #clear-icon v-if="props.clearable">
          <n-icon size="16" :depth="2" style="padding: 0px 2px" @click.stop="clear">
            <CloseCircle />
          </n-icon>
        </template>
      </n-input>
    </template>
    <div style="display: flex">
      <div class="buttonStyle" v-if="isContent && props.type !== 'monthrange'">
        <template v-for="(item, index) in DateRangeQuickMap" :key="index">
          <n-button :bordered="false" text @click="quickClick(index)">
            <p class="button-text">{{ item.name }}</p>
          </n-button>
        </template>
      </div>
      <n-date-picker
        v-bind="$attrs"
        :actions="null"
        @update:value="setChooseValue"
        panel
        :type="props.type"
        @confirm="confirm"
        :value="state.date"
        :is-date-disabled="isRangeDateDisabled"
        :default-value="defaultTimeRang"
        @keydown="handleKeyDown"
        @change="handleDateChange"
        :class=" (props.disableRightSelect && props.type === 'monthrange') ?  'disableRight' : ''"
      >
      <template #footer>
      <div class="footerBtn">
        <n-button size="small" style="margin-right:8px;" @click="state.showPopover = !state.showPopover">关闭</n-button>
        <n-button size="small" type="primary" @click="confirm(state.date)">确认</n-button>
      </div>
      </template>
        <template v-for="(item, key, index) in $slots" :key="index" v-slot:[key]>
          <slot :name="key"></slot>
        </template>
      </n-date-picker>
    </div>
  </n-popover>
  <div class="mask" v-show="state.showPopover" @click="closeMask"></div>
</div>
</template>

<script setup lang="ts">
import { DateRangeQuick, DateRangeQuickSelectEnum, getDate } from "@/utils/dateUtils";
import { reactive, watch, nextTick, toRefs, computed, ref} from "vue";
import { CalendarOutline as Calendar, CloseCircle } from "@vicons/ionicons5";
import { isArray, isFunction, isNullOrUnDef, isPromise } from "@/utils/isUtils";
import {useMessages} from "@/hooks";
import dayjs from "dayjs";
const message = useMessages();
type Tselect = number | [number, number] | null | number[];

interface Emits{
    (e: "update:value", value:[number,number] | null): void;
    (e: "update:formatted-value", value:[string,string] | null): void;
}
interface Props{
    value?: [number, number] | null | [] , //传入时间戳，优先度最高
    formattedValue?: [string, string] | null | [] , //传入格式化后的时间串
    quickSelect?: boolean, //是否显示左边快捷选择
    format?: string, //时间戳format
    type?: 'daterange' | 'datetimerange' | 'monthrange',
    valueFormatted?: string, //时间格式化format
    separator?: string, //start 选框与 end 选框之间的分隔符 default
    prefix?: boolean, //是否显示输入框前缀
    suffix?: boolean, //是否显示输入框后缀
    clearable?: boolean, //是否支持输入框清除
    placeholder?: [string, string], //输入框placeholder
    size?: "small" | "medium" | "large", //输入框size
    style?: string | object; //输入框style
    maxDays?: number; //限制选择范围，0为不限制，负数为今天往前数，正数为今天往后数
    excludeQuickSelect?:Array<DateRangeQuickSelectEnum>, //不显示某些快捷选择
    limitTimeRang?:[string,string] | DateRangeQuickSelectEnum | [] | [number, number], //设置选择范围，优先级最高，判断[0]是否小于[1]，设置时快捷选择不可选
    defaultTimeRang?:[number, number] | [], //根据默认时间来进行范围限制参数
    disabled?:boolean, //是否禁用
    width?:string,
    beforeConfirm?:(value:any)=>Promise<boolean>,
    beforeBlur?:(value:any)=>Promise<boolean>,
    closeAfterPreventConfirm?:boolean,
    disableRightSelect?:boolean,//按日 为 true 不可选中未来日期, 按月选择时 有左右的月份选择 true只能选择一个月 false可以选择多月
    placement?:'top-start' | 'top' | 'top-end' | 'right-start' | 'right' | 'right-end' | 'bottom-start' | 'bottom' | 'bottom-end' | 'left-start' | 'left' | 'left-end'
}
const emits = defineEmits<Emits>();
const props = withDefaults(
  defineProps<Props>(),
  {
    value: ()=>[],
    formattedValue: ()=>[], 
    quickSelect: true,
    format: 'YYYY-MM-DD HH:mm',
    type: 'daterange',
    valueFormatted: "YYYY-MM-DD HH:mm:ss",
    separator: '-', 
    prefix: true,
    suffix: false,
    clearable: true,
    placeholder: ()=>["开始时间", "结束时间"],
    size: "small" ,
    style: ()=>({}),
    maxDays: 0,
    excludeQuickSelect:()=>[],
    limitTimeRang:()=>[],
    defaultTimeRang:()=>[],
    disabled:false,
    width:'310px',
    closeAfterPreventConfirm:true,
    disableRightSelect:false ,
    placement:'bottom-start' 
  }
);
const state = reactive({
  inputValue: null as string[],
  date: null as Tselect,
  showPopover: false,
  isConfirm: false,
});

const {maxDays:maxDaysRef} = toRefs(props)
const d = 86400000
const h = 3600000
const m = 60000
const s = 1000
const valueFormatType = computed(()=>{
  return  props.valueFormatted == "yyyy-MM-dd" ? "YYYY-MM-DD" : props.valueFormatted;
})
const inputFormatType = computed(()=>{
  return  props.format == "yyyy-MM-dd" ? "YYYY-MM-DD" : props.format;
})



watch(
  () => props.value,
  (newValue) => {
    state.date = newValue;
    state.inputValue = dayjsFormat(newValue, inputFormatType.value);
  },
  {
    immediate: true,
  }
);
watch(()=> props.format,(newVal)=>{
  
  state.inputValue = dayjsFormat(props.value, newVal);
})

watch(
  () => [state.showPopover, state.isConfirm],
  (newVal, oldVal) => {
    const [showPopover, isConfirm] = [...newVal];
    if(showPopover){
      state.date = props.value;
      state.inputValue = dayjsFormat( props.value, inputFormatType.value);
    }
    if (isConfirm) {
      nextTick(() => {
        
        
        state.inputValue = dayjsFormat(state.date, inputFormatType.value);
      });
    } else {
      nextTick(() => {
        
        state.inputValue = dayjsFormat(props.value, inputFormatType.value);
      });
    }
  },
  { immediate: true }
);
// 判断显示
const isContent = computed(() => {
  const flag = isArray(props.limitTimeRang) ? props.limitTimeRang.length <= 0 : !(props.limitTimeRang in DateRangeQuickSelectEnum)
  return props.quickSelect  && flag
});
// 转化时间
 function dayjsFormat(arr, type){
  if (arr) {
    const newArr:[string,string] = [dayjs(arr[0]).format(type), dayjs(arr[1]).format(type)]
    return newArr;
  } else {
    return null;
  }
};
// 清空 
const clear = () => {
  state.inputValue = null;
  state.date = null;
  emits("update:value", null);
  emits("update:formatted-value", null);
};
// 快捷选择
const quickClick = (index) => {
  
  state.date = getDate(index).timestamp;
  state.inputValue = dayjsFormat(state.date, inputFormatType.value);
  emits("update:value", getDate(index).timestamp);
  emits("update:formatted-value", dayjsFormat(state.date, valueFormatType.value));
  state.showPopover = false;
  state.isConfirm = false;
};
// 加工快捷键
const DateRangeQuickMap = Object.fromEntries(
  Object.entries(DateRangeQuick)
  .filter(([key]) => !props.excludeQuickSelect.includes(Number(key)))
);
// 选择日期
const confirm = async (v) => {
  if(timeSpan.value && props.disableRightSelect && props.type == 'datetimerange') return message.createMessageError("按日选择日期之间间隔不能大于90天")
  if(props.type == 'monthrange'){
    v[0] = dayjs(v[0]).startOf('month').valueOf()
    v[1] = dayjs(props.disableRightSelect ? v[0] : v[1]).endOf('month').valueOf()
  }
  if(isFunction(props.beforeConfirm)){
    try{
      let result = await props.beforeConfirm(v)
      if(!result){
        if(props.closeAfterPreventConfirm){
          state.showPopover = false;
        }
        return
      }
    }
    catch(e){
      console.log(e);
    }
  }
  state.isConfirm = true;
  state.inputValue = dayjsFormat(v, inputFormatType.value);
  emits("update:value", v);
  emits("update:formatted-value", dayjsFormat(v, valueFormatType.value));
  state.showPopover = false;
  state.isConfirm = false;
};
const setChooseValue = (param) => {
  const timeArray  = [param[0],param[0]]
  state.inputValue = dayjsFormat(param, inputFormatType.value);
  state.date = (props.disableRightSelect && props.type == 'monthrange') ? timeArray : param;
};
// 校验时间选择
function isRangeDateDisabled(ts: number,type: 'start' | 'end',range: [number, number] | null){
  // 快捷日期范围
  if(!isArray(props.limitTimeRang) && props.limitTimeRang in DateRangeQuickSelectEnum){
    return ts < DateRangeQuick[props.limitTimeRang].startTime || ts > DateRangeQuick[props.limitTimeRang].endTime
  }
  //自定义日期范围选择
  else if(isArray(props.limitTimeRang) && props.limitTimeRang.length > 0){
    const minDate = new Date(props.limitTimeRang[0]);
    const maxDate = new Date(props.limitTimeRang[1]);
    if(minDate > maxDate){
      return ts < maxDate.setHours(0, 0, 0, 0) || ts > minDate.setHours(23, 59, 59, 0);
    }else{
      return ts < minDate.setHours(0, 0, 0, 0) || ts > maxDate.setHours(23, 59, 59, 0);
    }
  }
  //disableRight 为 true 不可选中未来事件
  else if(props.disableRightSelect) {
    return ts > Date.now()
  } 
  // 大于0时候传入的是最大天数
  else if(maxDaysRef.value > 0) {
    if (type === 'start' && range !== null) {
      return ts > range[1] || ts < range[1] - Math.abs(maxDaysRef.value) * d;
    }
    if (type === 'end' && range !== null) {
      return ts < range[0] || ts > range[0] + Math.abs(maxDaysRef.value) * d;
    }
  }
  
  //限制选择过去时间
  // else if(maxDaysRef.value < 0){
  //   // 当有默认时间就根据默认时间来定 不然就以当天算
  //   if(props.defaultTimeRang.length > 0){
  //     const dtimestamp = dayjs(props.defaultTimeRang[1]).endOf('day').valueOf();
  //     return ts > dtimestamp || ts < (dtimestamp - Math.abs(maxDaysRef.value) * d); 
  //   }else{
  //     const timestamp = dayjs().endOf('day').valueOf();
  //     return ts > timestamp || ts < (timestamp - Math.abs(maxDaysRef.value) * d); 
  //   }
  // }
  // //限制选择未来时间
  // else if(maxDaysRef.value > 0){
  //   if(props.defaultTimeRang.length > 0){
  //     const dtimestamp = dayjs(props.defaultTimeRang[1]).endOf('day').valueOf()-d;
  //     return ts < dtimestamp || ts > (dtimestamp + maxDaysRef.value * d);
  //   }else{
  //     const timestamp = dayjs().endOf('day').valueOf()-d;
  //     return ts < timestamp || ts > (timestamp + maxDaysRef.value * d);
  //   }
  // }
}
async function closeMask(){
  if(isFunction(props.beforeBlur)){
    try{
      let result = await props.beforeBlur(state.date)
        // state.showPopover = result;
        // return
    }
    catch(e){
      console.log(e);
    }
  }
  state.showPopover = !state.showPopover
}
// 有日期范围限制阻止键盘输入行为
const handleKeyDown = (event) => {
  const flag = isArray(props.limitTimeRang) ? props.limitTimeRang.length > 0 : props.limitTimeRang in DateRangeQuickSelectEnum
  if( flag || maxDaysRef.value !== 0){
    event.preventDefault(); 
  }
};

//时间之间的间隔不能大于90天
const timeSpan = ref(false)
const handleDateChange = (date) =>{
  const startDate = date[0];
  const endDate = date[1];
  const maxAllowedRange = 90 * 24 * 60 * 60 * 1000; // 90天的毫秒数
  if (endDate - startDate > maxAllowedRange) {
    timeSpan.value = true
  }else{
    timeSpan.value = false
  }
}
</script>

<style scoped>
.buttonStyle {
  width: 60px;
  box-sizing: border-box;
  border-right: 1px solid #eee;
}
.button-text {
  height: 36px;
  text-align: center;
  width: 60px;
  line-height: 36px;
  font-size: 15px;
}
.clear {
  cursor: pointer;
}
.mask {
  position: fixed;
  top: 0;
  left: 0;
  opacity: 0;
  width: 100%;
  height: 100%;
  z-index: 99;
}
.footerBtn{
  padding: 8px 12px;
  border-top: 1px solid #EEEEEE;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

:deep(.disableRight .n-date-panel-calendar--end) {
  display: none;
}
</style>
