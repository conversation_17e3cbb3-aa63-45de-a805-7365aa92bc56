<template>
  <div class="wrapper inner-page-height">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :pagination="paginationRef"
      :isTableSelection="false"
      @paginationChange="paginationChange"
      :isNeedCollapse="false"
      :isDisplayIndex="false"
    >
      <!-- 表单 -->
      <template #searchForm>
        <n-form
          ref="formRef"
          label-placement="left"
          label-width="auto"
          :show-feedback="false"
          require-mark-placement="right-hanging"
          size="small"
          :style="{ width: '100%' }"
        >
          <n-form-item :span="12" label="用户昵称" path="">
            <j-search-input v-model:value.trim="model.nickname" placeholder="请输入用户昵称" @search="handlerSearch" />
          </n-form-item>
        </n-form>
      </template>
      <template #tableHeaderBtn>
        <n-button @click="refresh" :loading="isLoading" class="store-button">刷 新</n-button>
      </template>
    </FormLayout>
  </div>
</template>

<script lang="tsx" setup name="RechargeRecord">
import { onMounted, ref } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { useMessages } from "@/hooks";
import { rechargeRecordGetPage } from "@/services/api/financialApi/userRecharge";
import { useUnitConversion } from "../hooks/unitConversion";

const { toYuanString } = useUnitConversion();

const { createMessageSuccess, createMessageError } = useMessages();

function getRechargeType(type: number): string {
  switch (type) {
    case 1:
      return "在线充值";
    case 2:
      return "线下充值";
    default:
      return "-";
  }
}

/** 表格hook */
const {
  isLoading,
  tableData,
  paginationRef,
  pageTableData,
  paginationChange,
} = useTableDefault({
  pageDataRequest: rechargeRecordGetPage,
});

/* 表格列表项 */
const tableColumns = ref([
  {
    title: "用户昵称/ID",
    key: "nickname",
    width: 150,
    align: "left",
    render: (rowData) => {
      return <TableTooltip row={rowData} nameKey="nickname" title={rowData.nickname} idKey="accountUserId" />;
    },
  },
  {
    title: "充值金额（元）",
    key: "rechargeAmount",
    width: 110,
    align: "left",
    render: rowData => {
      return toYuanString(rowData?.rechargeAmount);
    },
  },
  {
    title: "手续费（元）",
    key: "handlingFee",
    width: 100,
    align: "left",
    render: rowData => {
      return toYuanString(rowData?.handlingFee);
    },
  },
  {
    title: "实充金额（元）",
    key: "actualRechargeAmount",
    width: 100,
    align: "left",
    render: rowData => {
      return toYuanString(rowData?.actualRechargeAmount);
    },
  },
  {
    title: "充值方式",
    key: "rechargeType",
    width: 100,
    align: "left",
    render: (row) => {
      return getRechargeType(row?.rechargeType);
    },
  },
  {
    title: "商户号",
    key: "merchantId",
    width: 180,
    align: "left",
  },
  {
    title: "交易单号",
    key: "transactionId",
    width: 180,
    align: "left",
  },
  {
    title: "充值时间",
    key: "rechargeTime",
    width: 150,
    align: "left",
  },
  // {
  //   title: "操作",
  //   key: "action",
  //   width: 120,
  //   fixed: "right",
  //   align: "left",
  //   align: "center",
  //   fixed: "right",
  // },
]);

/** 参数 */
const model = ref({
  nickname: "",
});

/** 搜索 */
const handlerSearch = () => {
  tableSearch();
};

/** 表格刷新 */
function refresh() {
  tableSearch();
}

const getParams = () => {
  return model.value;
};

/* 表格搜索 */
const tableSearch = () => {
  pageTableData(getParams(), paginationRef.value);
};

/** 表格排序 */
// const tableSorterChange = (info: { sort: string, sortAsc: "ascend" | "descend" }) => {
//   const { sort, sortAsc } = info;
//   if (sort) {
//     tableData.value = [...tableData.value].sort((a, b) => {
//       const valA = a[sort];
//       const valB = b[sort];
//       if (sort.toLowerCase().includes("time")) {
//         if (sortAsc === "ascend") {
//           return valA.localeCompare(valB);
//         } else {
//           return valB.localeCompare(valA);
//         }
//       }
//       return 0;
//     });
//   }
// };

/** 组件挂载 */
onMounted(() => {
  tableSearch();
});
</script>

<style lang="less" scoped>
@import "@/styles/defaultVar.less";
</style>
