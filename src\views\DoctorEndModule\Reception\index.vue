<script setup lang="ts">
import TabsLayout from "@/layout/TabsLayout.vue";
import {ref, computed, onMounted, onUnmounted} from "vue";
import ReceotionTab from './compoments/ReceotionTab.vue';
import {DoctorEndReceptionSelection} from '@/enums';
import IMPage from "@/views/DoctorEndModule/IM/index.vue"
import CompletedIMPage from '@/views/DoctorEndModule/IM/CompletedIMPage.vue'
import WaitFinishVideoTab from "@/views/DoctorEndModule/Reception/compoments/WaitFinishVideoTab.vue";
import DoctorEndMitter from "@/views/DoctorEndModule/utils/DoctorEndMittEvent";

const tabsData = ref([
  {
    label: "待接诊",
    key: DoctorEndReceptionSelection.reception,
    number: 0,
    tabNumber: 'receptionCount',
    directive: 'if',
  },
  {
    label: "进行中",
    key: DoctorEndReceptionSelection.processing,
    number: 0,
    isShowNumber: false,
    tabNumber: 'processingCount',
    directive: 'if',
  },
  {
    label: "已完成",
    key: DoctorEndReceptionSelection.completed,
    number: 0,
    isShowNumber: false,
    tabNumber: 'completedCount',
    directive: 'if',
  },
  {
    label: "待完成视频单",
    key: DoctorEndReceptionSelection.wait_finish_video,
    number: 0,
    tabNumber: 'completedCount',
    directive: 'if',
  },
]);

const onChange = (key, value) => {
  tabsData.value.forEach(item => {
    if (item.key === key) {
      item.number = value;
    }
  });
};

onMounted(()=>{
  DoctorEndMitter.on('ChangeReceptionTab',(target:DoctorEndReceptionSelection)=>{
    handleTabChange(target)
  })
})

onUnmounted(()=>{
  DoctorEndMitter.off('ChangeReceptionTab')
})

/* 修改tab页 */
const handleTabChange = (target:DoctorEndReceptionSelection) => {
  tabNameRef.value = target
}

const tabNameRef = ref<DoctorEndReceptionSelection>(DoctorEndReceptionSelection.reception);

const pageMap = {
  [DoctorEndReceptionSelection.reception]: ReceotionTab,
  [DoctorEndReceptionSelection.processing]: IMPage,
  [DoctorEndReceptionSelection.completed]: CompletedIMPage,
  [DoctorEndReceptionSelection.wait_finish_video]: WaitFinishVideoTab,
}

const currentPage = computed(() => pageMap[tabNameRef.value])

</script>

<template>
  <n-layout>
    <n-layout-content id="prescription-management">
      <TabsLayout
          v-model:value="tabNameRef"
          :tabsData="(tabsData as any)"
          :onlyTabs="true"
          is-show-number
          class="reception-management-tabsLayout">
        <component
            :is="currentPage"
            :tabNameRef="tabNameRef"
            :tabName="tabNameRef"
            @tabQuantity="onChange"
        />
      </TabsLayout>
    </n-layout-content>
  </n-layout>
</template>

<style lang="less">
.n-tabs.reception-management-tabsLayout {
  .n-tabs-wrapper {
    height: 100%;
  }

  .n-tabs-tab-pad, .n-tabs-bar {
    display: none;
  }

  .n-tabs-tab-wrapper + .n-tabs-tab-wrapper {
    margin-left: 8px;
  }

  .n-tabs-tab {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 90px;
    padding: 8px 12px !important;
    background: #f2f3f5;
    border-radius: 4px 4px 0px 0px;
    border: 1px solid #e5e6eb;
    border-bottom: none;
    box-sizing: border-box;

    &::after {
      content: "";
      position: absolute;
      display: block;
      width: 100%;
      height: 1px;
      bottom: -1px;
      background: #e5e6eb;
    }
  }

  .n-tabs-tab.n-tabs-tab--active {
    background: #fff;

    &::after {
      background: #fff;
    }
  }
}
</style>
