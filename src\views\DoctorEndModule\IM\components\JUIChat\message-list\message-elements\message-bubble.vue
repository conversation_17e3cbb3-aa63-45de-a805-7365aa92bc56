<template>
  <div :class="containerClassNameList">
    <!-- multiple select radio -->
    <RadioSelect
        v-if="props.isMultipleSelectMode"
        class="multiple-select-radio"
        :isSelected="isMultipleSelected"
        @onChange="toggleMultipleSelect"
    />
    <div
        :class="{
        'control-reverse': messageFlow == 'out'
      }"
    >
      <!-- message-bubble-container -->
      <div class="message-bubble-content">
        <div
            class="message-bubble-main-content"
            :class="[messageFlow === 'in' ? '' : 'reverse']"
        >
          <div class="avatar-container">
            <!-- TODO 切换使用会话中的头像数据 -->
            <img
                class="avatar-image"
                :src="message?.img || avatarSrc"
                @error="avatarLoadFailed">
          </div>
          <main
              class="message-body"
              @click.stop
          >
            <div :class="['message-body-main', messageFlow === 'out' && 'message-body-main-reverse']">
              <div
                  :class="[
                  'blink',
                  'message-body-content',
                  messageFlow === 'out' ? 'content-out' : 'content-in',
                ]"
              >
                <div class="content-main">
                  <template>
                    <slot name="messageElement"/>
                  </template>
                </div>
              </div>
            </div>
          </main>
        </div>
        <!-- message extra area -->
        <div class="message-bubble-extra-content">
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {computed, inject, toRefs, ref, onMounted} from 'vue';
import {type IMessageModel} from '@tencentcloud/chat-uikit-engine';
import RadioSelect from '@/views/DoctorEndModule/IM/components/common/RadioSelect/index.vue';
import avatarSrc from "@/assets/image/system/avatar.png";
import type {JChatMessageModel} from "@/views/DoctorEndModule/IM/types";
import {useUserStore} from "@/stores/modules/user";


interface IProps {
  messageItem: JChatMessageModel;
  content?: any;
  classNameList?: string[];
  blinkMessageIDList?: string[];
  isMultipleSelectMode?: boolean;
  isAudioPlayed?: boolean | undefined;
  multipleSelectedMessageIDList?: string[];
}

interface IEmits {
  (e: 'resendMessage'): void;

  (e: 'blinkMessage', messageID: string): void;

  (e: 'setReadReceiptPanelVisible', visible: boolean, message?: IMessageModel): void;

  (e: 'changeSelectMessageIDList', options: {
    type: 'add' | 'remove' | 'clearAll';
    messageID: string
  }): void;

  // Only for uniapp
  (e: 'scrollTo', scrollHeight: number): void;
}

const emits = defineEmits<IEmits>();

const props = withDefaults(defineProps<IProps>(), {
  messageItem: () => ({} as JChatMessageModel),
  content: () => ({}),
  isAudioPlayed: false,
  blinkMessageIDList: () => [],
  classNameList: () => [],
  isMultipleSelectMode: false,
  multipleSelectedMessageIDList: () => [],
});

const userStore = useUserStore();
const {blinkMessageIDList, messageItem: message} = toRefs(props);
const {currentConversationData, changeCurrentConversation} = inject('currentConversationData');
const messageFlow = ref<'out' | 'in'>('out')

onMounted(() => {
  messageFlow.value = message.value.fromImUserId === userStore.imConfig?.userID ? 'out' : 'in'
})

const isMultipleSelected = computed<boolean>(() => {
  return props.multipleSelectedMessageIDList.includes(message.value.id);
});

const containerClassNameList = computed(() => {
  return [
    'message-bubble',
    isMultipleSelected.value ? 'multiple-selected' : '',
    ...props.classNameList,
  ];
});

const isBlink = computed(() => {
  if (message.value?.id) {
    return blinkMessageIDList?.value?.includes(message.value.id);
  }
  return false;
});

function toggleMultipleSelect(isSelected: boolean) {
  emits('changeSelectMessageIDList', {
    type: isSelected ? 'add' : 'remove',
    messageID: message.value.id,
  });
}

let reloadAvatarTime = 0;

function avatarLoadFailed(e: Event) {
  reloadAvatarTime += 1;
  if (reloadAvatarTime > 3) {
    return;
  }
  (e.currentTarget as HTMLImageElement).src = avatarSrc;
}

// const messageFlow = computed(() => {
//   return message.value.fromUserId !== currentConversationData.value.fromUserId ? 'out' : 'in'
// })

</script>

<style lang="less" scoped>
* {
  display: flex;
  flex-direction: column;
  min-width: 0;
  box-sizing: border-box;
}

.message-bubble {
  padding: 10px 15px;
  display: flex;
  flex-direction: row;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;

  &.multiple-selected {
    background-color: #f0f0f0;
  }

  .multiple-select-radio {
    margin-right: 12px;
    flex: 0 0 auto;
  }

  .control-reverse {
    flex: 1 1 auto;
    flex-direction: row-reverse;
  }

  .message-bubble-main-content {
    display: flex;
    flex-direction: row;

    .message-avatar {
      display: block;
      width: 36px;
      height: 36px;
      border-radius: 5px;
      flex: 0 0 auto;
    }

    .message-body {
      display: flex;
      flex: 0 1 auto;
      flex-direction: column;
      align-items: flex-start;
      margin: 0 8px;

      .message-body-nick-name {
        display: block;
        margin-bottom: 4px;
        font-size: 12px;
        color: #999;
        max-width: 150px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .message-body-main {
        max-width: 100%;
        display: flex;
        flex-direction: row;
        min-width: 0;
        box-sizing: border-box;

        &-reverse {
          flex-direction: row-reverse;
        }

        .audio-unplay-mark {
          flex: 0 0 auto;
          width: 5px;
          height: 5px;
          border-radius: 50%;
          background-color: #f00;
          margin: 5px;
        }

        .message-body-content {
          display: flex;
          flex-direction: column;
          min-width: 0;
          box-sizing: border-box;
          padding: 12px;
          font-size: 14px;
          color: #000;
          letter-spacing: 0;
          word-wrap: break-word;
          word-break: break-all;
          position: relative;

          .content-main {
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            flex-shrink: 0;
            align-content: flex-start;
            border: 0 solid black;
            margin: 0;
            padding: 0;
            min-width: 0;

            .message-risk-replace {
              width: 130px;
              height: 130px;
            }
          }

          .content-has-risk-tips {
            font-size: 12px;
            color: #fa5151;
            font-family: PingFangSC-Regular;
            margin-top: 5px;
            border-top: 1px solid #e5c7c7;
            padding-top: 5px;
          }
        }

        .content-in {
          background: #fbfbfb;
          border-radius: 0 10px 10px;
        }

        .content-out {
          background: #dceafd;
          border-radius: 10px 0 10px 10px;
        }

        .content-no-padding {
          padding: 0;
          background: transparent;
          border-radius: 10px;
          overflow: hidden;
        }

        .content-no-padding.content-has-risk {
          padding: 12px;
        }

        .content-has-risk {
          background: rgba(250, 81, 81, 0.16);
        }

        .blink-shadow {
          @keyframes shadow-blink {
            50% {
              box-shadow: rgba(255, 156, 25, 1) 0 0 10px 0;
            }
          }

          box-shadow: rgba(255, 156, 25, 0) 0 0 10px 0;
          animation: shadow-blink 1s linear 3;
        }

        .blink-content {
          @keyframes reference-blink {
            50% {
              background-color: #ff9c19;
            }
          }

          animation: reference-blink 1s linear 3;
        }

        .message-label {
          align-self: flex-end;
          font-family: PingFangSC-Regular;
          font-size: 12px;
          color: #b6b8ba;
          word-break: keep-all;
          flex: 0 0 auto;
          margin: 0 8px;

          &.fail {
            width: 15px;
            height: 15px;
            border-radius: 15px;
            background: red;
            color: #fff;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
          }

          &.loading-circle {
            opacity: 0;
            animation: circle-loading 2s linear 1s infinite;
          }

          @keyframes circle-loading {
            0% {
              transform: rotate(0);
              opacity: 1;
            }

            100% {
              opacity: 1;
              transform: rotate(360deg);
            }
          }
        }

        .align-self-bottom {
          align-self: flex-end;
        }
      }
    }
  }

  .reverse {
    display: flex;
    flex-direction: row-reverse;
    justify-content: flex-start;
  }

  .message-bubble-extra-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
}

.avatar-container {
  position: relative;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  flex: 0 0 auto;
  width: 36px;
  height: 36px;

  .placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #ececec;
    transition: opacity 0.3s,
    background-color 0.1s ease-out;

    &.skeleton-animation {
      animation: breath 2s linear 0.3s infinite;
    }

    &.hidden {
      opacity: 0;
    }
  }

  .avatar-image {
    width: 100%;
    height: 100%;
    border-radius: 5px
  }
}
</style>
