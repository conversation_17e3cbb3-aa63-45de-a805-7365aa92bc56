<template>
    <div class="groupMgr-wrapper">
        <!--介绍 -->
        <n-card title="客户统计" size="small" class="header" :bordered="false">
          按订单创建时间段统计每个客户的下单情况。
        </n-card>
        <!-- 表格 -->
        <div class="groupMgr-table">
            <FormLayout 
                :isLoading="isLoading"
                :tableData="tableData"
                :tableColumns="tableColumns"
                :isNeedCollapse="false"
                :pagination="paginationRef"
                @paginationChange="paginationChange"
                :isTableSelection="false" 
                :table-summary="summaryRefs"
                @table-sorter-change="tableSorterChange"
            >
              <template #searchForm>
                <!-- 表单 -->
                <n-form
                    ref="formRef"
                    :model="model"
                    :show-feedback="false"
                    label-placement="left"
                    label-width="auto"
                    require-mark-placement="right-hanging"
                    size="small"
                    :style="{ width: '100%' }"
                >
                    <n-form-item label="统计区间">
                      <JDateRangePicker v-model:value="model.rangeTime" style="flex: 1;" type="daterange"
                        format="yyyy-MM-dd" :default-time="['00:00:00', '23:59:59']" :clearable="false" :maxDays="366"
                        disableRightSelect />
                    </n-form-item>
                    <!-- 昵称 -->
                    <n-form-item label="昵称">
                      <JSearchInput
                        v-model:value="model.customerNickname"
                        placeholder="请输入昵称"
                        @search="tableSearch"
                        :isPopover="false" 
                        :is-show-search-icon="false" 
                      />
                    </n-form-item>
                    <!-- 手机号 -->
                    <n-form-item label="手机号">
                      <JSearchInput 
                        :isPopover="false" 
                        :is-show-search-icon="false" 
                        v-model:value="model.mobile" 
                        placeholder="请输入手机号" 
                        @update:value="handleUpdateValue" 
                        @search="handleMobileSearch"
                      />
                    </n-form-item>
                </n-form>
              </template>
              <!-- 操作项 -->
              <template #tableHeaderBtn>
                <n-button @click="getTableData" class="store-button">
                  刷 新
                </n-button>
                <n-button type="primary" @click="exportCustomerReports" v-if="hasReportsCustomerExportAuth" >
                  导出查询到的数据
                </n-button>
              </template>
            </FormLayout>
        </div>
    </div>
</template>

<script lang="tsx" setup name='GroupMgrTotalReport'>
import { ref, watch } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { DateRangeQuick, DateRangeQuickSelectEnum } from "@/utils/dateUtils";
import { isNumber } from "@/utils";
import { REGEXP_PHONE } from "@/config";
import { useMessages } from "@/hooks";
import { exportCustomerReportsPage , getCustomerReportsPage } from "@/services/api"
import { useTableColumns } from "./tableColumns"
import { SortType, SortTypeValue } from "../types";
const { tableColumns , summaryRefs , summaryColumn } = useTableColumns()
import dayjs from "dayjs";
import { hasReportsCustomerExportAuth  } from "@/views/StoreModule/DataReports/authList"


const { createMessageError , createMessageExportSuccess } = useMessages();

/* 表格方法Hook */
const {
  isLoading,
  tableData,
  paginationRef,
  pageTableData,
  refreshTableData,
  paginationChange,
} = useTableDefault({
  pageDataRequest: getCustomerReportsPage,
});

/* 初始化参数 */
const initParams = {
    rangeTime: [
        dayjs().startOf('day').subtract(30, 'day').valueOf(),
        dayjs().endOf("day").valueOf(),
    ],
    customerNickname: '',
    mobile: '',
    sortType: SortType.TOTALORDER,
    isAsc:false
};
const model = ref({ ...initParams });

// 排序
const tableSorterChange = (info) => {
    model.value.sortType = SortTypeValue[info.sort] ;
    model.value.isAsc = info.sortAsc === 'ascend' ? true : false;
    getTableData()
}

// 获取分页数据
function getTableData() {
    pageTableData(formatParams(), paginationRef.value, true);
}

// 导出
const exportCustomerReports = () => {
    const _params = {
        data: formatParams(),
        pageVO: paginationRef.value
    }
    exportCustomerReportsPage(_params).then(() => {
        createMessageExportSuccess("导出成功");
    }).catch((err) => {
        createMessageError(err || "导出失败");
    })
}

// 格式化参数
const formatParams = () => {
    return {
        startTime: dayjs(model.value.rangeTime[0]).format('YYYY-MM-DD 00:00:00'),
        endTime: dayjs(model.value.rangeTime[1]).format('YYYY-MM-DD 23:59:59'),
        customerNickname: model.value.customerNickname,
        sortType: model.value.sortType,
        isAsc: model.value.isAsc,
        ...(model.value.mobile ? { mobile: model.value.mobile } : {}),
    }
}

/** 手机号值改变回调 */
const handleUpdateValue = (value: string) => {
  model.value.mobile = value.replace(/ /g, '');
};

/** 手机号搜索 */
const handleMobileSearch = () => {
  if (model.value.mobile.length > 0) {
    if (!isNumber(model.value.mobile) && REGEXP_PHONE.test(model.value.mobile)) {
      tableSearch();
    } else {
      createMessageError('请输入正确的手机号');
    }
  } else {
    tableSearch();
  }
};

/** 表格搜索 */
function tableSearch() {
  getTableData();
}

watch(()=> model.value.rangeTime,()=>{
  getTableData()
},{immediate:true})
//生成合计数据
watch(
    tableData,
    (newVal) => {
        summaryColumn(newVal);
    }, { immediate: true, deep: true }
);

</script>


<style lang="less" scoped>
@import "@/styles/default.less";
.groupMgr-wrapper {
    width: 100%;
    height: 100%;
    .header {
      height: 86px;
      border-bottom: 1px solid @default-border-color;
    }
    .groupMgr-table {
      height: calc(100% - 86px);
    }
}
</style>