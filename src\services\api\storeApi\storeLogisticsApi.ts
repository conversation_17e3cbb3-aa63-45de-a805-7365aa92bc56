import { defHttp } from "@/services";
export const enum StoreLogisticsApi {
    page = "/storeLogistics/page",
    add = "/storeLogistics/createLogistics",
    update = "/storeLogistics/updateLogistics",
    batchDelete = "/storeLogistics/batchDelete",
    delete = "/storeLogistics/delete",
    logisticTraces = "/storeLogistics/getLogisticsTraces"
}

export interface StoreLogisticsPageRes {
    records: Array<any>,
    total: string;
    size: string;
    current: string;
}
/** 门店物流分页 */
export function getLogisticsPage(params: {
    data: {
        storeId: number,
        storeName: string,
        productName:string,
        sku:string
    },
    pageVO: {
        current: number,
        size: number
    }
}) {
    return defHttp.post<StoreLogisticsPageRes>({
        url: StoreLogisticsApi.page,
        params,
    });
}
/** 新增物流单 */
interface AddParams {
    storeId?: number,
    productId?:string,
    productName?: string,
    sku?: string,
    deliveryQuantity?: number,
    logisticsCompany?: string,
    logisticsNumber?: string,
    addressee?: string,
    mobile?: string
  }
export function addLogisticsOrder(_params) {
    return defHttp.post({
        url: StoreLogisticsApi.add,
        params: {
          data: _params
        }
    });
};
/** 修改物流单 */
export function updateLogisticsOrder(_params) {
    return defHttp.put({
        url: StoreLogisticsApi.update,
        params: {
          data: _params
        }
    });
};
/** 批量删除 */
export function logisticBatchDelete(_params) {
    return defHttp.delete({
      url: StoreLogisticsApi.batchDelete,
      params: _params,
      requestConfig: {
        isQueryParams: true,
    },
    });
};
export function logisticDelete(_params) {
    return defHttp.delete({
      url: StoreLogisticsApi.delete,
      params: _params,
      requestConfig: {
        isQueryParams: true,
    },
    });
}

export function getLogisticTraces(params) {
    return defHttp.get({
        url: StoreLogisticsApi.logisticTraces,
        params,
        requestConfig: {
            isQueryParams: true,
        },
    });
};