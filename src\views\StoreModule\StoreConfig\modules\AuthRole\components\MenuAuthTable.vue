<template>
    <n-data-table
        :columns="columns"
        :data="_Data"
        :row-key="(row:MenuAuthTableDataItem) => row.code"
        default-expand-all
        :style="{ height: `${400}px` }"
        flex-height
  />
</template>

<script setup lang="tsx" name="RedPacket">
import { NCheckbox, type DataTableColumns } from "naive-ui";
import { isArray } from "@/utils/isUtils";
import type { MenuAuthDataResponseItem, MenuAuthTableDataItem } from "@/utils/auth/type";
import { formatTableDataFromRawResponseData, getRecordAllParentsAndChildrenCodeListByCode } from "@/utils/auth/authUtils";
import { createCacheStorage } from "@/utils/cache/storageCache";
import { CacheConfig } from "@/utils/cache/config";
import { ref, watch } from "vue";

const authTreeStorage = createCacheStorage(CacheConfig.AuthListTree);
const authTreeCache = authTreeStorage.get('platform');
type MenuAuthTableProps = {
    value: string[],
    disabled?: boolean
}

const props = withDefaults(defineProps<MenuAuthTableProps>(),{
    disabled: false
})
 
const emits = defineEmits<{
    (e:'update:value',value:string[]):void,
}>()


const tempCodeListRef = ref([])

const data:Array<MenuAuthDataResponseItem> = authTreeCache as Array<MenuAuthDataResponseItem>
const {tableData:_Data,record:menuAuthRecord,codeMap} = formatTableDataFromRawResponseData(data)
const columns:DataTableColumns<MenuAuthTableDataItem> = [
    {
        title:'顶级导航',
        key: 'moduleType',
        width:150,
        render: row => {
            if(row.moduleType){
                return  <NCheckbox 
                            checked={tempCodeListRef.value.includes(row.code)}
                            disabled={props.disabled}
                            on-update:checked={(checked:boolean)=>handleCheckBoxCheck(checked,row.code)}
                        >
                            {row.moduleType}
                        </NCheckbox>
            }
        }
    },
    {
        title: '左侧导航',
        key: 'moduleName',
        width:180,
        render: row => {
            if(row.moduleName){
                return <NCheckbox 
                            checked={tempCodeListRef.value.includes(row.code)}
                            disabled={props.disabled}
                            on-update:checked={(checked:boolean)=>handleCheckBoxCheck(checked,row.code)}
                        >
                            {row.moduleName}
                        </NCheckbox>
            }
        }
    },
    {
        title: '菜单',
        key: 'menuName',
        width:180,
        render: row => {
            if(row.menuName){
                return  <NCheckbox
                            checked={tempCodeListRef.value.includes(row.code)}
                            disabled={props.disabled}
                            on-update:checked={(checked:boolean)=>handleCheckBoxCheck(checked,row.code)}
                        >
                            {row.menuName}
                        </NCheckbox>
            }
        }
    },
    {
        title: '功能权限',
        key: 'authList',
        render: row => {
            if(isArray(row.authList)){
                return row.authList.map(auth=> 
                    <NCheckbox 
                        checked={tempCodeListRef.value.includes(auth.code)}
                        disabled={props.disabled}
                        on-update:checked={(checked:boolean)=>handleCheckBoxCheck(checked,auth.code)}
                    >
                        {auth.authName}
                    </NCheckbox>
                );
            }
        }
    }
]

function checkSlibingExist(parentsCode:string,selectedCodeList:Array<string>){
    // if(parentsCode == AuthModuleTypeEnum.SCRM || parentsCode == AuthModuleTypeEnum.SG){
    //     return true
    // }
    const _record = menuAuthRecord[parentsCode];
    if(_record.childrenCodeList && _record.childrenCodeList.length){
        return _record.childrenCodeList.some(code=>selectedCodeList.includes(code))
    }
    return false;
}

function handleCheckBoxCheck(status:boolean,key:string){
    let _tempValue = [...tempCodeListRef.value]
    const {totalCodeList,allParentsCodeList,allChildrenCodeList} = getRecordAllParentsAndChildrenCodeListByCode(menuAuthRecord,key);
    
    function filterTempValueById(codeKey:string,idKey:string){
        const _record = menuAuthRecord[codeKey];
        _tempValue = _tempValue.filter(id=>id!==idKey)
        if(totalCodeList.length && _record.isCascade){
            const _filterParentsCodeList = allParentsCodeList.filter(code=>!checkSlibingExist(code,_tempValue))
            const _totalCodeList = [...allChildrenCodeList,..._filterParentsCodeList]
            _tempValue = _tempValue.filter(id=>!_totalCodeList.includes(id))
        }
    }
    if(!status){
        filterTempValueById(key,key)
    }
    else {
        _tempValue.push(key)
        if(totalCodeList.length){
            _tempValue = _tempValue.concat(totalCodeList)
        }
    }
    _tempValue = Array.from(new Set(_tempValue))

    const resIdList = _tempValue.map(code=>{
        return codeMap[code]
    })

    const _idValueList = []
    resIdList.forEach((resList)=>{
        resList.forEach(id=>{
            _idValueList.push(id)
        })
    })
    emits('update:value',_idValueList)
}

watch(()=>props.value,(newVal)=>{
        tempCodeListRef.value = []
        if(isArray(newVal) && newVal.length){
            newVal.forEach(resId=>{
                const _codeItem = codeMap[resId]
                if(_codeItem){
                    const _code =  _codeItem[0]
                    if(!tempCodeListRef.value.includes(_code)){
                       tempCodeListRef.value.push(_code)
                    }
                }
              
            })
    }
},{immediate:true})

</script>

<style lang="less" scoped>
@import "@/styles/default.less";
</style>
