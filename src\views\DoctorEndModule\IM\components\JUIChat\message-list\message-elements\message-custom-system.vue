<template>
  <div class="message-timestamp">
    {{ customSysContent }}
  </div>
</template>
<script setup lang="ts">
import {ref, watch} from 'vue';
import {JSONToObject, safeParse} from '@/views/DoctorEndModule/IM/utils/IMUtils';
import type {IMessageModel} from "@tencentcloud/chat-uikit-engine";
import  {type JChatMessageModel,type ServiceSysStatus} from "@/views/DoctorEndModule/IM/types";
import {SysStatusMap} from "@/views/DoctorEndModule/IM/types";

// 居中显示的系统消息,比如:服务已开始、处方已发送给药师，药师审核通过后自动发送给患者等系统消息
defineOptions({
  name: 'MessageCustomSystem'
})

interface IProps {
  content: Record<string, any>;
  messageItem: JChatMessageModel;
}

const props = withDefaults(defineProps<IProps>(), {
  content: () => ({}),
  messageItem: () => ({} as JChatMessageModel),
});

const customSysContent = ref('');

watch(
    () => [props.messageItem],
    (newVal: any, oldVal: any) => {
      if (newVal?.toString() === oldVal?.toString()) {
        return;
      } else {
        customSysContent.value = SysStatusMap[props.messageItem?.contentType as ServiceSysStatus]
      }
    },
    {
      immediate: true,
    },
);

</script>
<style lang="less" scoped>

.message-timestamp {
  margin: 10px auto;
  color: #999;
  font-size: 12px;
  overflow-wrap: anywhere;
  display: flex;
  align-items: center;
  text-align: center;
}
</style>
