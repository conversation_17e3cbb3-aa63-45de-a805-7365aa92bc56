<template>
  <div class="table_wrapper">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :isNeedCollapse="false"
      :pagination="paginationRef"
      @paginationChange="paginationChange"
      @tableSorterChange="tableSorterChange"
      :isTableSelection="false"
      table-row-key="_dummyId"
    >
      <template #searchForm>
        <!-- 表单 -->
        <n-form
          ref="formRef"
          :model="model"
          :show-feedback="false"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          size="small"
          :style="{ width: '100%' }"
        >
          <!-- 订单类型 -->
          <n-form-item label="订单类型">
            <n-select
              v-model:value="model.orderType"
              placeholder="请选择订单类型"
              :options="storeOrderTypeOptions"
              style="width: 160px;"
              clearable
            />
          </n-form-item>
          <!-- 用户ID -->
          <n-form-item label="用户ID">
            <JSearchInput
              v-model:value="model.customerId"
              placeholder="请输入用户ID"
              @search="tableSearch"
              :is-show-search-icon="false"
              :width="210"
            />
          </n-form-item>
          <!-- 统计日期 -->
          <n-form-item label="统计日期">
            <JDateRangePicker
              v-model:value="model.rangeTime"
              style="flex: 1;"
              type="daterange"
              format="yyyy-MM-dd"
              :clearable="false"
              :maxDays="366"
            />
          </n-form-item>
        </n-form>
      </template>
      <!-- 操作项 -->
      <template #tableHeaderBtn>
        <n-button @click="refresh" class="store-button">刷 新</n-button>
      </template>
    </FormLayout>
  </div>
</template>

<script lang="tsx" setup>
import { ref, watch, onMounted } from "vue";
import dayjs from "dayjs";
import { storeOrderTypeOptions } from "@/constants";
import { useTableDefault } from "@/hooks/useTableDefault";
import { useMessages } from "@/hooks";
import { StoreOrderTypeEnum } from "@/enums";
import { ProductSaleSortTypeEnum, ProductSaleSortValue, ReportType } from "../types";
import { getMemberConsumeRank } from "@/services/api";
import { hasCourseExportAuth } from "../authList";
/** 相关组件 */
import FormLayout from "@/layout/FormLayout.vue";

defineOptions({
  name: "MemberConsumptionReport",
});

const { createMessageError, createMessageSuccess } = useMessages();

/* 表格方法Hook */
const {
isLoading,
tableData,
paginationRef,
pageTableData,
refreshTableData,
paginationChange,
} = useTableDefault({
  useDummyId: true,
  pageDataRequest: getMemberConsumeRank,
});

/* 初始化参数 */
const initParams = {
  orderType: StoreOrderTypeEnum.NORMAL,
  customerId: "",
  sortType: ProductSaleSortTypeEnum.PaidOrder,
  isAsc: false,
  reportType: ReportType.CUSTOMERREPORT,
  rangeTime: null,
};
const model = ref({ ...initParams });

/* 表格项 */
const tableColumns = [
  {
    title: "用户ID",
    key: "customerShortId",
    align: "left",
    fixed: "left",
    width: 120,
  },
  {
    title: "昵称",
    key: "customerNickname",
    align: "left",
    fixed: "left",
    width: 120,
  },
  {
    title: "注册手机号",
    key: "mobile",
    align: "left",
    width: 100,
  },
  {
    title: "支付订单数",
    sorter: true,
    isSortDefault: true,
    key: "paidOrder",
    align: "left",
    width: 120,
  },
  {
    title: "实际消费金额(元)",
    sorter: true,
    key: "actualConsumeAmount",
    align: "left",
    width: 120,
    render: row => row.actualConsumeAmount ? (row.actualConsumeAmount / 100).toFixed(2) : '0.00'
  }
];

/** 排序 */
const tableSorterChange = (info: { sort: string, sortAsc: "ascend" | "descend" }) => {
  model.value.sortType = ProductSaleSortValue[info.sort];
  if (info.sortAsc === 'ascend') {
    model.value.isAsc = true;
  } else {
    model.value.isAsc = false;
  }
  tableSearch();
};

/** 获取参数 */
function getParams() {
  const { orderType, sortType, isAsc, customerId, reportType, rangeTime } = model.value;
  const [startTime, endTime] = rangeTime ?? [null, null];

  let _params = {
    orderType,
    sortType,
    isAsc,
    reportType,
    customerId,
    startTime: startTime ? dayjs(startTime).format('YYYY-MM-DD 00:00:00') : null,
    endTime: endTime ? dayjs(endTime).format('YYYY-MM-DD 23:59:59') : null,
  };

  return _params;
}

/** 表格搜索 */
function tableSearch() {
  pageTableData(getParams(), paginationRef.value);
}

/** 表格刷新 */
function refresh(){
  tableSearch();
};

/* 组件挂载 */
onMounted(() => {
  tableSearch();
});

/** 监听 */
watch(() => [model.value.orderType, model.value.rangeTime], (newVal) => {
  if (newVal) {
    tableSearch();
  }
});
</script>

<style lang="less" scoped>
@import "@/styles/default.less";
.table_wrapper {
  width: 100%;
  height: 100%;
}
</style>
