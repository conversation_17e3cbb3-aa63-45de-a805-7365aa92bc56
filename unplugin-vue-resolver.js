
const JComponentsMap={
    'JSelect':'@/components/JSelect',
    'JSearchInput':'@/components/JSearchInput',
    "JDateRangePicker":'@/components/JDateRangePicker',
    "JRoleSelect":'@/components/JSelect/JRoleSelect',
    "JPagination":'@/components/JPagination',
}

export function JComponentsResolver() {
    return {
      type: "component",
      resolve: (name) => {
        if (name.match(/^(N[A-Z]|n-[a-z])/) && JComponentsMap[name])
          return { name, from: JComponentsMap[name] };
      }
    };
  }