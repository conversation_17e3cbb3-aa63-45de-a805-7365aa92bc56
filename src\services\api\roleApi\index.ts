import { defHttp } from "@/services";

export const enum RoleApi{
  page = "/authRole/pageSearch",
  add = "/authRole/addRole",
  update = "/authRole/updateRole",
  delete = "/authRole/delete/roleId",
  authDeatilByRole = "/authRole/get/roleList",
  getAuthListTree = "/authRole/getAllResList",    // 获取全部角色权限树 于1.1.1版本更改为/authRes/getAllResList
  getAuthResList = "/authRes/getAllResList",      // 获取全部角色权限树
  copy ="/authRole/addCopyRole",
}

export interface RolePageResponse{
    createTime:string,
    id:string,
    name:string,
    type:number,
    updateTime:string,
    isSystem:0 | 1,
    isUse:number
}

/**分页获取角色 */
export function rolePage(params) {
  return defHttp.post<Array<RolePageResponse>>({
    url: RoleApi.page,
    params,
  });
}



interface RoleAddParams {
    name:string,
    authResGrantVOList:Array<{
        resId:string
    }>
}

/**添加角色 */
export function roleAdd(params:RoleAddParams) {
  return defHttp.post({
    url: RoleApi.add,
    params: { 
        data: {
            type:10,
            ...params
      } },
  });
}

interface RoleEditParams{
    id:string,
    name:string,
    authResGrantVOList:Array<{
      resId:string,
      roleId:string,
      id?:string
  }>,
}
/**编辑角色 */
export function roleUpdate(params:RoleEditParams) {
  return defHttp.put({
    url: RoleApi.update,
    params: { 
      data: {
        type:10,
        ...params
    } 
  },
  });
}

interface AuthDetailResponse{
  code:string,
  id:string,
  authResDTOList:Array<{
    id:string,
    code:string
  }>
}
/**根据角色id获取角色详情 */
export function getAuthDetailByRoleId(roleId:string) {
  return defHttp.get<AuthDetailResponse>({
    url: RoleApi.authDeatilByRole,
    params: { 
        roleId,
        isReturnTree:false
    },
  });
}

export interface AuthListTreeResponseItem{
  "id": string,
  "createTime": string,
  "updateTime": string,
  "type": 1 | 2 | 3,
  "name": string,
  "code": string,
  "childrenList":Array<AuthListTreeResponseItem>
}

/**获取全部角色权限树 */
export function getAuthListTreeV2() {
  return defHttp.get<{
    head:Array<AuthListTreeResponseItem>
  }>({
    url: RoleApi.getAuthResList,
    params: {
        isTree:true
    },
  });
}

/**根据角色id删除角色 */
export function deleteAuthById(roleId:string) {
  return defHttp.delete({
    url: RoleApi.delete,
    params: {
      roleId
    },
    requestConfig: {
      isQueryParams: true,
    },
  });
}

/** 复制角色*/
export function copyAuthById(roleId:string) {
  return defHttp.post({
    url: `${RoleApi.copy}?roleId=${roleId}`,
  });
}
