<template>
  <n-popover style="text-align: center;" trigger="click" placement="bottom-start" :disabled="!props.isPopover">
      <template #trigger>
        <n-input
          v-bind="$attrs"
          @keyup.enter.native="handleSearch"
          :clearable="false"
          :value="valueRef"
          @update:value="handlerInput"
          :allow-input="!inputNumber ?  undefined :  onlyAllowNumber"
          :placeholder="props.placeholder"
          :style="{width: isNumber(props.width) ? `${props.width}px` : `${props.width}`}"
          :maxlength="props.maxlength"
        >
          <template #prefix v-if="!inputNumber && isShowSearchIcon">
            <n-button text @click="handleSearch">
              <n-icon size="14" :component="Search" color="rgba(194, 194, 194, 1)"></n-icon>
            </n-button>
          </template>
          <template #suffix>
            <n-button text @click="clearValue" v-if="isShowCloseIcon">
              <n-icon size="14" :component="CloseCircleSharp" color="rgba(194, 194, 194, 1)"></n-icon>
            </n-button>
          </template>
        </n-input>
      </template>
      <span> {{ props.placeholder }} </span>
    </n-popover>
</template>
<script lang="ts" setup>
import { isNUllString, isNumber } from "@/utils/isUtils";
import { Search, CloseCircleSharp } from "@vicons/ionicons5";
import { computed, ref, toRefs, watch } from "vue";

const props = withDefaults(defineProps<{
  value: string | null;
  inputNumber?: boolean | null;
  placeholder?:string | null;
  width?: string | number; // 搜索输入框长度
  isPopover?: boolean; // 是否弹出信息
  isShowSearchIcon?: boolean;
  maxlength?: string | number; // 最大字符
}>(), {
  width: 170,
  isPopover: true,
  isShowSearchIcon: true,
});

const { value: originValueRef } = toRefs(props);

const valueRef = ref<string | null>("");

const onlyAllowNumber = (value: string) => !value || /^\d+$/.test(value)

watch(originValueRef, (newVal, oldVal) => {
  valueRef.value = newVal;
},{
  immediate:true
});

const isShowCloseIcon = computed(() => {
  return !isNUllString(valueRef.value);
});

const emits = defineEmits<{
  (e: "search"): void;
  (e: "update:value", value: string): void;
  (e: "emptyEvent",value:string)
}>();
function handleSearch() {
  emits("search");
}
function clearValue() {
  valueRef.value = "";
  emits("update:value", "");
  emits('emptyEvent',"")
}
function handlerInput(value) {
  emits("update:value", value);
}

</script>
<style scoped lang="less">

:deep .n-input__placeholder>span{
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

</style>
