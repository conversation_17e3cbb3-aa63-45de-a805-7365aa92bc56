import { hasAuth } from "@/utils/auth/authUtils";
import { DepartmentConfigAuth } from "@/enums/authKeys";

/** 新增一级科室 */
export const hasDepartmentConfigAddFirstLevelAuth = function(){
    return hasAuth(DepartmentConfigAuth.DepartmentConfigAddFirstLevel.key);
}()

/** 新增二级科室 */
export const hasDepartmentConfigAddSecondLevelAuth = function(){
    return hasAuth(DepartmentConfigAuth.DepartmentConfigAddSecondLevel.key);
}()

/** 删除 */
export const hasDepartmentConfigDeleteAuth = function(){
    return hasAuth(DepartmentConfigAuth.DepartmentConfigDelete.key);
}()

/** 编辑 */
export const hasDepartmentConfigEditAuth = function(){
    return hasAuth(DepartmentConfigAuth.DepartmentConfigEdit.key);
}()

