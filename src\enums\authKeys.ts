
export type AuthKeyType = {
    key: string | Array<string>,
    title: string,
    desc?: string,
}
export const HomeAuth = {

    /** 首页 */
    homeIndex:{
        key:"sg.store.home.index",
        title:"首页",
    },

}

export const GoodsManagementAuth = {

    /** 商品管理 */
    goodsManagementIndex:{
        key:"sg.store.goods.management.index",
        title:"商品管理",
    },

    /** 新建商品 */
    goodsManagementIndexAdd:{
        key:"sg.store.goods.management.index.add",
        title:"新建商品",
    },

    /** 编辑商品 */
    goodsManagementIndexEdit:{
        key:"sg.store.goods.management.index.edit",
        title:"编辑商品",
    },

    /** 复制创建 */
    goodsManagementIndexCopy:{
        key:"sg.store.goods.management.index.copy",
        title:"复制创建",
    },

    /** 批量下架/批量上架 */
    goodsManagementIndexBulk:{
        key:"sg.store.goods.management.index.bulk",
        title:"批量下架/批量上架",
    },

    /** 新建分类 */
    goodsManagementIndexNewcategory:{
        key:"sg.store.goods.management.index.newCategory",
        title:"新建分类",
    },

    /** 编辑分类 */
    goodsManagementIndexEditcategory:{
        key:"sg.store.goods.management.index.editCategory",
        title:"编辑分类",
    },

    /** 删除分类 */
    goodsManagementIndexDeletecategory:{
        key:"sg.store.goods.management.index.deleteCategory",
        title:"删除分类",
    },

}

/** 供应商商品 */
export const SupplierProductManagementAuth = {
    /** 供应商商品 */
    supplierProductManagement:{
        key:"sg.store.supplier.product.management",
        title:"供应商商品",
    },
    /** 供应商商品-子项 */
    supplierProductManagementIndex:{
        key:"sg.store.supplier.product.management.index",
        title:"供应商商品",
    },
    /** 新增供应商商品 */
    supplierProductManagementIndexAdd:{
        key:"sg.store.supplier.product.management.index.add",
        title:"新增商品",
    },
    /** 编辑供应商商品 */
    supplierProductManagementIndexEdit:{
        key:"sg.store.supplier.product.management.index.edit",
        title:"编辑商品",
    },
    /** 提交申请 */
    supplierProductManagementIndexApply:{
        key:"sg.store.supplier.product.management.index.apply",
        title:"提交申请",
    },
    /** 详情 */
    supplierProductManagementIndexDetails:{
        key:"sg.store.supplier.product.management.index.details",
        title:"详情",
    },
}

export const CustomerManagementAuth = {

    /** 会员管理 */
    customerManagementIndex:{
        key:"sg.store.customer.management.index",
        title:"会员管理",
    },

    /** 编辑客户 */
    customerManagementIndexEditcustomer:{
        key:"sg.store.customer.management.index.editCustomer",
        title:"编辑客户",
    },

    /** 新建标签 */
    customerManagementIndexNewlabel:{
        key:"sg.store.customer.management.index.newLabel",
        title:"新建标签",
    },

    /** 编辑标签 */
    customerManagementIndexEditlabel:{
        key:"sg.store.customer.management.index.editLabel",
        title:"编辑标签",
    },

    /** 删除标签 */
    customerManagementIndexDeletelabel:{
        key:"sg.store.customer.management.index.deleteLabel",
        title:"删除标签",
    },
    
    /** 新建虚拟号 */
    customerManagementAddVirtualAccount:{
        key:"sg.store.customer.management.add.virtualAccount",
        title:"新建虚拟号",
    },

    /** 禁止评论/解除禁止评论 */
    customerManagementIndexEnableOrDisableComment:{
        key:"sg.store.customer.management.index.enableOrDisableComment",
        title:"禁止评论/解除禁止评论",
    },

    /** 增减积分 */
    customerManagementPointsIncreaseAndDecrease:{
        key:"sg.store.customer.management.points.increaseAndDecrease",
        title:"增减积分",
    },
    /** 导出会员列表 */
    customerManagementExportAccountList:{
        key:"sg.store.customer.management.export.accountList",
        title:"导出会员列表",
    },
    /** 黑名单 */
    customerManagementDisableOrEnableAccount:{
        key:"sg.store.customer.management.disableOrEnable.account",
        title:"拉黑/取消拉黑",
    },
    /** 修改归属店员 */
    customerManagementChangeAccountOwner:{
        key:"sg.store.customer.management.change.account.owner",
        title:"修改归属店员",
    }
}

export const PrescriptionManagementAuth = {

    /** 处方管理 */
    prescriptionManagementIndex:{
        key:"sg.store.prescription.management.index",
        title:"处方管理",
    },

    /** 开处方 */
    prescriptionManagementIndexAdd:{
        key:"sg.store.prescription.management.index.add",
        title:"开处方",
    },

    /** 恢复/取消 */
    prescriptionManagementIndexRecoverorcancel:{
        key:"sg.store.prescription.management.index.cancel",
        title:"恢复/取消",
    },

    /** 编辑 */
    prescriptionManagementIndexEdit:{
        key:"sg.store.prescription.management.index.edit",
        title:"编辑",
    },

    /** 详情 */
    prescriptionManagementIndexDetails:{
        key:"sg.store.prescription.management.index.details",
        title:"详情",
    },

    /** 删除 */
    prescriptionManagementIndexDelete:{
        key:"sg.store.prescription.management.index.delete",
        title:"删除",
    },

}

export const DoctorManagementAuth = {

    /** 医生管理 */
    doctorManagementIndex:{
        key:"sg.store.doctor.management.index",
        title:"医生管理",
    },

    /** 新建 */
    doctorManagementIndexNew:{
        key:"sg.store.doctor.management.index.new",
        title:"新建",
    },

    /** 编辑 */
    doctorManagementIndexEdit:{
        key:"sg.store.doctor.management.index.edit",
        title:"编辑",
    },

    /** 启用/禁用 */
    doctorManagementIndexEnablechange:{
        key:"sg.store.doctor.management.index.enableChange",
        title:"启用/禁用",
    },

}

export const OrderManagementAuth = {

    /** 订单管理 */
    orderManagementIndex:{
        key:"sg.store.order.management.index",
        title:"订单管理",
    },

    /** 删除 */
    orderManagementIndexDelete:{
        key:"sg.store.order.management.index.delete",
        title:"删除",
    },

    /** 详情 */
    orderManagementIndexDetails:{
        key:"sg.store.order.management.index.details",
        title:"详情",
    },

    /** 订单导出 */
    orderManagementIndexExport:{
        key:"sg.store.order.management.index.export",
        title:"订单导出",
    },

    /** 发货 */
    orderManagementIndexShip:{
        key:"sg.store.order.management.index.ship",
        title:"发货",
    },

    /** 签收 */
    orderManagementIndexSign:{
        key:"sg.store.order.management.index.sign",
        title:"签收",
    },

    /** 锁单 */
    orderManagementIndexLock:{
        key:"sg.store.order.management.index.lock",
        title:"锁单",
    },
    /** 退款 */
    orderManagementIndexRefund: {
        key: "sg.store.order.management.index.refund",
        title: "退款",
    },

}

export const SalesManagementAuth = {

    /** 会员积分 */
    salesManagementPoints:{
        key:"sg.store.sales.management.points",
        title:"会员积分",
    },

    /** 积分商城启用 */
    salesManagementPointsMallenable:{
        key:"sg.store.sales.management.points.mallEnable",
        title:"积分商城启用",
    },

    /** 积分兑换商品配置 */
    salesManagementPointsProductconfig:{
        key:"sg.store.sales.management.points.productConfig",
        title:"积分兑换商品配置",
    },

    /** 积分规则配置 */
    salesManagementPointsRuleconfig:{
        key:"sg.store.sales.management.points.ruleConfig",
        title:"积分规则配置",
    },

    /** 会员等级配置 */
    salesManagementPointsLevelconfig:{
        key:"sg.store.sales.management.points.levelConfig",
        title:"会员等级配置",
    },
    /** 更多配置 */
    salesManagementMoreConfigurations:{
        key:"sg.store.sales.management.points.more.configurations",
        title:"更多配置",
    },
    /** 福利券分类 */
    salesManagementWelfareClassify:{
        key:"sg.store.sales.management.welfareClassify",
        title:"福利券分类",
    },
    /** 福利券分类 - 新增 */
    salesManagementWelfareClassifyAdd:{
        key:"sg.store.sales.management.welfareClassify.add",
        title:"新增福利券分类",
    },
    /** 福利券分类 - 编辑 */
    salesManagementWelfareClassifyEdit:{
        key:"sg.store.sales.management.welfareClassify.edit",
        title:"编辑福利券分类",
    },
    /** 福利券分类 - 删除 */
    salesManagementWelfareClassifyDelete:{
        key:"sg.store.sales.management.welfareClassify.delete",
        title:"删除福利券分类",
    },
    /** 福利券管理 */
    salesWelfareManagement:{
        key:"sg.store.sales.management.welfareManagement",
        title:"福利券分类",
    },
    /** 福利券管理 - 领取记录 */
    selectRecord:{
        key:"sg.store.sales.management.welfareManagement.selectRecord",
        title:"领取记录",
    },
    /** 福利券管理 - 编辑 */
    salesWelfareManagementSend:{
        key:"sg.store.sales.management.welfareManagement.send",
        title:"发放/停止发放",
    },
    /** 福利券管理 - 删除 */
    updateValidDate:{
        key:"sg.store.sales.management.welfareManagement.updateValidDate",
        title:"修改有效期",
    },

    /** 福利品管理 */
    salesWelfareGoods:{
        key:"sg.store.sales.management.welfareGoods",
        title:"福利品管理",
    },
    /** 福利品管理 - 新建分类*/
    salesWelfareGoodsClassifyAdd:{
        key:"sg.store.sales.management.welfareGoods.classifyAdd",
        title:"新建福利品分类",
    },
    /** 福利品管理 - 编辑分类*/
    salesWelfareGoodsClassifyEdit:{
        key:"sg.store.sales.management.welfareGoods.classifyEdit",
        title:"编辑福利品分类",
    },
    /** 福利品管理 - 删除分类*/
    salesWelfareGoodsClassifyDelete:{
        key:"sg.store.sales.management.welfareGoods.classifyDelete",
        title:"删除福利品分类",
    },
    /** 福利品管理 - 新建*/
    salesWelfareGoodsAdd:{
        key:"sg.store.sales.management.welfareGoods.add",
        title:"新建福利品",
    },
    /** 福利品管理 - 编辑*/
    salesWelfareGoodsEdit:{
        key:"sg.store.sales.management.welfareGoods.edit",
        title:"编辑福利品",
    },
    /** 福利品管理 - 上下架*/
    salesWelfareGoodsPublish:{
        key:"sg.store.sales.management.welfareGoods.publish",
        title:"上架/下架",
    },
}

export const AfterServiceManagementAuth = {

    /** 售后管理 */
    afterServiceManagementIndex:{
        key:"sg.store.after.service.management.index",
        title:"售后管理",
    },

    /** 同意/拒绝/收货并退款/再次发起退款/已线下退款 */
    afterServiceManagementIndexOperation:{
        key:"sg.store.after.service.management.index.operation",
        title:"同意/拒绝/收货并退款/再次发起退款/已线下退款",
    },

    /** 详情 */
    afterServiceManagementIndexDetails:{
        key:"sg.store.after.service.management.index.details",
        title:"详情",
    },

}

export const DataReportsAuth = {

    /** 订单统计 */
    dataReportsOrder:{
        key:"sg.store.data.reports.order",
        title:"订单统计",
    },

    /** 导出 */
    dataReportsOrderExport:{
        key:"sg.store.data.reports.order.export",
        title:"导出",
    },

    /** 客户统计 */
    dataReportsCustomer:{
        key:"sg.store.data.reports.customer",
        title:"客户统计",
    },

    /** 导出 */
    dataReportsCustomerExport:{
        key:"sg.store.data.reports.customer.export",
        title:"导出",
    },

    /** 群管统计 */
    dataReportsGroup:{
        key:"sg.store.data.reports.group",
        title:"群管统计",
    },

    /** 导出 */
    dataReportsGroupExport:{
        key:"sg.store.data.reports.group.export",
        title:"导出",
    },

    /** 经销商统计 */
    dataReportsDealer:{
        key:"sg.store.data.reports.dealer",
        title:"经销商统计",
    },

    /** 导出 */
    dataReportsDealerExport:{
        key:"sg.store.data.reports.dealer.export",
        title:"导出",
    },

    /** 商品统计 */
    dataReportsProduct:{
        key:"sg.store.data.reports.product",
        title:"商品统计",
    },

    /** 导出 */
    dataReportsProductExport:{
        key:"sg.store.data.reports.product.export",
        title:"导出",
    },

    /** 课程统计 */
    dataReportsCourse:{
        key:"sg.store.data.reports.course",
        title:"课程统计",
    },

    /** 导出 */
    dataReportsCourseExport:{
        key:"sg.store.data.reports.course.export",
        title:"导出",
    },

    /** 门店数据统计 */
    dataReportsStoreDataReport: {
        key: "sg.store.data.reports.storeData.report",
        title: "门店数据统计",
    },
    /** 门店数据统计-导出 */
    dataReportsStoreDataReportExport: {
        key: "sg.store.data.reports.storeData.report.export",
        title: "导出",
    },

    /** 经销商数据统计 */
    dataReportsDistributorDataReport: {
        key: "sg.store.data.reports.distributor.report",
        title: "经销商数据统计",
    },
    /** 经销商数据统计-导出 */
    dataReportsDistributorDataReportExport: {
        key: "sg.store.data.reports.distributor.report.export",
        title: "导出",
    },

}

export const StoreBasicConfigAuth = {

    /** 角色权限 */
    storeBasicConfigRoles:{
        key:"sg.store.store.basic.config.roles",
        title:"角色权限",
    },

    /** 新建角色 */
    storeBasicConfigRolesNewrole:{
        key:"sg.store.store.basic.config.roles.newRole",
        title:"新建角色",
    },

    /** 编辑 */
    storeBasicConfigRolesEditrole:{
        key:"sg.store.store.basic.config.roles.editRole",
        title:"编辑",
    },

    /** 复制 */
    storeBasicConfigRolesCopyrole:{
        key:"sg.store.store.basic.config.roles.copyRole",
        title:"复制",
    },

    /** 删除 */
    storeBasicConfigRolesDeleterole:{
        key:"sg.store.store.basic.config.roles.deleteRole",
        title:"删除",
    },

    /** 操作员管理 */
    storeBasicConfigOperators:{
        key:"sg.store.store.basic.config.operators",
        title:"操作员管理",
    },

    /** 新建操作员 */
    storeBasicConfigOperatorsNewoperator:{
        key:"sg.store.store.basic.config.operators.newOperator",
        title:"新建操作员",
    },

    /** 编辑 */
    storeBasicConfigOperatorsEditoperator:{
        key:"sg.store.store.basic.config.operators.editOperator",
        title:"编辑",
    },

    /** 重置密码 */
    storeBasicConfigOperatorsResetpassword:{
        key:"sg.store.store.basic.config.operators.resetPassword",
        title:"重置密码",
    },

    /** 删除 */
    storeBasicConfigOperatorsDeleteoperator:{
        key:"sg.store.store.basic.config.operators.deleteOperator",
        title:"删除",
    },

}

export const OrderStoreconfigAuth = {

    /** 轮播图 */
    orderStoreconfigCarousel:{
        key:"sg.store.order.storeConfig.carousel",
        title:"轮播图",
    },

    /** 新建 */
    orderStoreconfigCarouselNew:{
        key:"sg.store.order.storeConfig.carousel.new",
        title:"新建",
    },

    /** 编辑 */
    orderStoreconfigCarouselEdit:{
        key:"sg.store.order.storeConfig.carousel.edit",
        title:"编辑",
    },

    /** 删除 */
    orderStoreconfigCarouselDelete:{
        key:"sg.store.order.storeConfig.carousel.delete",
        title:"删除",
    },

    /** 地址配置 */
    orderStoreconfigAddress:{
        key:"sg.store.order.storeConfig.address",
        title:"地址配置",
    },

    /** 设置默认地址 */
    orderStoreconfigAddressSetDefault:{
        key:"sg.store.order.storeConfig.address.setDefault",
        title:"设置默认地址",
    },

    /** 新建 */
    orderStoreconfigAddressNew:{
        key:"sg.store.order.storeConfig.address.new",
        title:"新建",
    },

    /** 编辑 */
    orderStoreconfigAddressEdit:{
        key:"sg.store.order.storeConfig.address.edit",
        title:"编辑",
    },

    /** 删除 */
    orderStoreconfigAddressDelete:{
        key:"sg.store.order.storeConfig.address.delete",
        title:"删除",
    },

    /** 发货工具 */
    orderStoreconfigDelivery:{
        key:"sg.store.order.storeConfig.delivery",
        title:"发货工具",
    },

    /** 编辑参数 */
    orderStoreconfigDeliveryEdit:{
        key:"sg.store.order.storeConfig.delivery.edit",
        title:"编辑参数",
    },

    /** 开启/关闭 */
    orderStoreconfigDeliveryEnable:{
        key:"sg.store.order.storeConfig.delivery.enable",
        title:"开启/关闭",
    },

    /** 交易相关 */
    orderStoreconfigTransaction:{
        key:"sg.store.order.storeConfig.transaction",
        title:"交易相关",
    },

    /** 编辑配置值 */
    orderStoreconfigTransactionEdit:{
        key:"sg.store.order.storeConfig.transaction.edit",
        title:"编辑配置值",
    },

    /** 开发配置 */
    orderStoreconfigDevelopment:{
        key:"sg.store.order.storeConfig.development",
        title:"开发配置",
    },

    /** 编辑配置值 */
    orderStoreconfigDevelopmentEdit:{
        key:"sg.store.order.storeConfig.development.edit",
        title:"编辑配置值",
    },
    /** 清除缓存 */
    orderStoreconfigDevelopmentClearRedisCache:{
        key:"sg.store.order.storeConfig.development.clearRedisCache",
        title:"清除缓存",
    },
    /** 首页Logo */
    orderStoreconfigHomelogo:{
        key:"sg.store.order.storeConfig.homeLogo",
        title:"首页Logo",
    },

    /** 小程序导航 */
    orderStoreconfigMiniprogramnavigation:{
        key:"sg.store.order.storeConfig.miniProgramNavigation",
        title:"小程序导航",
    },

    /** 新建 */
    orderStoreconfigMiniprogramnavigationNew:{
        key:"sg.store.order.storeConfig.miniProgramNavigation.new",
        title:"新建",
    },

    /** 编辑 */
    orderStoreconfigMiniprogramnavigationEdit:{
        key:"sg.store.order.storeConfig.miniProgramNavigation.edit",
        title:"编辑",
    },

    /** 删除 */
    orderStoreconfigMiniprogramnavigationDelete:{
        key:"sg.store.order.storeConfig.miniProgramNavigation.delete",
        title:"删除",
    },


}

export const StoreOperationFlowConfigAuth = {

  /** 业务流程 */
  storeConfigOperationFlowPage:{
    key:"sg.store.config.operationFlow.page",
    title:"业务流程",
  },

  /** 业务流程-新建 */
  storeConfigOperationFlowAdd: {
    key: "sg.store.config.operationFlow.add",
    title: "新建流程",
  },

  /** 业务流程-编辑 */
  storeConfigOperationFlowEdit: {
    key: "sg.store.config.operationFlow.edit",
    title: "编辑",
  },

  /** 业务流程-删除 */
  storeConfigOperationFlowDelete: {
    key: "sg.store.config.operationFlow.delete",
    title: "删除",
  },

}

export const FinanceTraceAccountBalanceAuth = {

    /** 轨迹查询余额 */
    financeTraceAccountBalanceIndex:{
        key:"sg.store.finance.trace.account.balance",
        title:"轨迹查询余额",
    },

    /** 充值记录 */
    financeTraceAccountBalanceRechargerecord:{
        key:"sg.store.finance.trace.account.balance.rechargeRecord",
        title:"充值记录",
    },

}

export const FinanceTraceInvokeHistoryAuth = {

    /** 轨迹调用 */
    financeTraceInvokeHistoryIndex:{
        key:"sg.store.finance.trace.invoke.history",
        title:"轨迹查询余额",
    },

}

/** 门店分佣规则 */
export const StoreCommissionRuleAuth = {
    /** 门店分佣规则 */
    StoreCommissionRuleIndex:{
        key:"sg.store.finance.commission.rule",
        title:"门店分佣规则",
    },
    /** 门店分佣规则-子项 */
    StoreCommissionRuleIndexItem:{
        key:"sg.store.finance.commission.rule.index",
        title:"子项",
    },
    /** 门店分佣规则-保存 */
    StoreCommissionRuleIndexSave:{
        key:"sg.store.finance.commission.rule.save",
        title:"保存",
    },
}

/** 门店分佣明细 */
export const StoreCommissionDetailAuth = {
    /** 门店分佣明细 */
    StoreCommissionDetailIndex:{
        key:"sg.store.finance.commission.detail",
        title:"门店分佣明细",
    },
    /** 门店分佣明细-子项 */
    StoreCommissionDetailIndexItem:{
        key:"sg.store.finance.commission.detail.index",
        title:"门店分佣明细",
    },
    /** 门店分佣明细-导出 */
    StoreCommissionDetailIndexExport:{
        key:"sg.store.finance.commission.detail.export",
        title:"导出",
    },
    /** 门店分佣明细-详情 */
    StoreCommissionDetailIndexDetail:{
        key:"sg.store.finance.commission.detail.info",
        title:"详情",
    },
}

/** 打款审核 */
export const FinancePaymentReviewAuth = {
    /** 打款审核 */
    FinancePaymentReviewIndex:{
        key:"sg.store.finance.payment.review",
        title:"打款审核",
    },
    /** 打款审核-子项 */
    FinancePaymentReviewIndexItem:{
        key:"sg.store.finance.payment.review.index",
        title:"打款审核",
    },
    /** 打款审核-导出 */
    FinancePaymentReviewIndexExport:{
        key:"sg.store.finance.payment.review.export",
        title:"导出",
    },
    /** 打款审核-审核通过 */
    FinancePaymentReviewIndexReview:{
        key:"sg.store.finance.payment.review.review",
        title:"审核通过",
    },
    /** 打款审核-驳回 */
    FinancePaymentReviewIndexReject:{
        key:"sg.store.finance.payment.review.reject",
        title:"驳回",
    },
    /** 详情 */
    FinancePaymentReviewIndexDetail:{
        key:"sg.store.finance.payment.review.detail",
        title:"详情",
    },
    /** 打款 */
    FinancePaymentReviewIndexPayout:{
        key:"sg.store.finance.payment.review.payout",
        title:"打款",
    },
}

export const LexiconConfigAuth = {

    /** 敏感词库 */
    LexiconConfigIndex:{
        key:"sg.store.lexicon.config.index",
        title:"敏感词库",
    },
    /** 添加敏感词 */
    LexiconConfigIndexAdd:{
        key:"sg.store.lexicon.config.index.add",
        title:"添加敏感词",
    },
    /** 删除 */
    LexiconConfigIndexDelete:{
        key:"sg.store.lexicon.config.index.delete",
        title:"删除",
    },

}

export const ContentConfigAuth = {

    /** 视频配置 */
    ContentConfigVideoConfig:{
        key:"sg.store.content.config.videoConfig",
        title:"视频配置",
    },
    /** 编辑配置值 */
    ContentConfigVideoConfigEdit:{
        key:"sg.store.content.config.videoConfig.edit",
        title:"编辑配置值",
    },

}

/** 视频管理 */
export const VideoManagementAuth={
    /**发布视频 */
    VideoManagementAdd:{
        key:"sg.store.video.management.index.add",
        title:"发布视频"
    },
    /** 查看详情 */
    VideoManagementDetail:{
        key:"sg.store.video.management.index.detail",
        title:"查看详情"
    },
    /** 删除 */
    VideoManagementDelete:{
        key:"sg.store.video.management.index.delete",
        title:"删除"
    },
    /** 发布/取消发布 */
    VideoManagementHandleStatus:{
        key:"sg.store.video.management.index.publishOrUnpublished",
        title:"发布/取消发布"
    }
}
/** 评论管理 */
export const CommentManagementAuth = {
    /** 删除 */
    CommentManagementIndexDelete:{
        key:"sg.store.comment.management.index.delete",
        title:"删除"
    },
    /** 审核通过/不通过 */
    CommentManagementIndexHandleStatus:{
        key:"sg.store.comment.management.index.audit",
        title:"审核通过/不通过"
    },
}

/** 分账方规则设置 */
export const SubAccountPayeeRuleSettingAuth = {
    /** 分账方规则设置 */
    SubAccountPayeeRuleSetting:{
        key:"sg.store.finance.subAccountPayeeRuleSetting",
        title:"分账方规则设置"
    },
    /** 保存 */
    SubAccountPayeeRuleSettingSave:{
        key:"sg.store.finance.subAccountPayeeRuleSetting.save",
        title:"保存"
    },
}
/** 分账入账方管理 */
export const  FinanceAllocationAccountManagementAuth = {
    /** 分账入账方管理 */
    FinanceAllocationAccountManagement:{
        key:"sg.store.finance.allocationAccount.management",
        title:"分账入账方管理"
    },
    /** 新增 */
    FinanceAllocationAccountManagementAdd:{
        key:"sg.store.finance.allocationAccount.management.add",
        title:"新增"
    },
    /** 详情 */
    FinanceAllocationAccountManagementDetail:{
        key:"sg.store.finance.allocationAccount.management.detail",
        title:"详情"
    },
    /** 重发短信 */
    FinanceAllocationAccountManagementReSendMsg:{
        key:"sg.store.finance.allocationAccount.management.reSendMsg",
        title:"重发短信"
    },
    /** 激活 */
    FinanceAllocationAccountManagementActive:{
        key:"sg.store.finance.allocationAccount.management.active",
        title:"激活"
    },
    /** 删除 */
    FinanceAllocationAccountManagementDelete:{
        key:"sg.store.finance.allocationAccount.management.delete",
        title:"删除"
    }
}

/** 分账单管理 */
export const FinanceAllocationManagementAuth = {
    /** 详情 */
    FinanceAllocationManagementDetail:{
        key:"sg.store.finance.allocation.management.detail",
        title:"详情"
    },
    /** 导出 */
    FinanceAllocationManagementExport:{
        key:"sg.store.finance.allocation.management.export",
        title:"导出"
    },
    /** 取消结算 */
    FinanceAllocationManagementCancelSettlement:{
        key:"sg.store.finance.allocation.management.cancelSettlement",
        title:"取消结算"
    },
    /** 成本价 */
    FinanceAllocationManagementCostDeduction:{
        key:"sg.store.finance.allocation.management.costDeduction",
        title:"成本价"
    }
}

/** 分账单结算 */
export const FinanceAllocationSettlementAuth = {
    /** 分账单结算 */
    FinanceAllocationSettlement:{
        key:"sg.store.finance.allocation.settlement",
        title:"分账单结算"
    },
    /** 详情 */
    FinanceAllocationSettlementDetail:{
        key:"sg.store.finance.allocation.settlement.detail",
        title:"详情"
    },
    /** 重新发起 */
    FinanceAllocationSettlementReSend:{
        key:"sg.store.finance.allocation.settlement.reSend",
        title:"重新发起"
    },
    /** 转线下打款 */
    FinanceAllocationSettlementLocal:{
        key:"sg.store.finance.allocation.settlement.local",
        title:"转线下打款"
    },
    /** 已线下打款 */
    FinanceAllocationSettlementLocalComplete:{
        key:"sg.store.finance.allocation.settlement.localComplete",
        title:"已线下打款"
    },
    /** 导出 */
    FinanceAllocationSettlementExport:{
        key:"sg.store.finance.allocation.settlement.export",
        title:"导出"
    },
    /** 商户号管理 */
    FinanceStoreConfigMerchantManagement:{
        key:"sg.store.finance.merchant.management",
        title:"商户号管理",
    },
    /** 商户号管理 子菜单*/
    FinanceStoreConfigMerchant:{
        key:"sg.store.finance.merchant",
        title:"商户号管理",
    },
    /** 商户号管理 编辑商户号 */
    FinanceStoreConfigMerchantUpdate:{
        key:"sg.store.finance.merchant.update",
        title:"编辑商户号",
    },
    /** 商户号管理 新建商户号 */
    FinanceStoreConfigMerchantAdd:{
        key:"sg.store.finance.merchant.add",
        title:"新建商户号",
    },
    /** 商户充值 */
    FinanceStoreConfigRecharge:{
        key:"sg.store.finance.recharge",
        title:"商户充值",
    },
    /** 充值记录 */
    FinanceStoreConfigRechargeRecord:{
        key:"sg.store.finance.recharge.record",
        title:"充值记录",
    },
    /** 支出记录 */
    FinanceStoreConfigExpenseRecord:{
        key:"sg.store.finance.expense.record",
        title:"支出记录",
    },
    /** 账户余额 */
    FinanceStoreConfigAccountBalance:{
        key:"sg.store.finance.account.balance",
        title:"账户余额",
    },
    /** 开通用户充值记录 */
    FinanceStoreConfigAccountCreate:{
        key:"sg.store.finance.account.create",
        title:"开通用户充值记录",
    },
    /** 线下充值  */
    FinanceStoreConfigAccountOfflineRecharge:{
        key:"sg.store.finance.account.offlineRecharge",
        title:"线下充值",
    },
    /** 线下退款  */
    FinanceStoreConfigAccountCreateOfflineRefund:{
        key:"sg.store.finance.account.offlineRefund",
        title:"线下退款",
    },
    /** 分账管理  */
    FinanceStoreConfigAccountCreateManagement:{
        key:"sg.store.finance.allocation.management",
        title:"分账管理",
    },
    /** 分账单管理 */
    FinanceStoreConfigAccountCreateBillManagement:{
        key:"sg.store.finance.allocation.bill.management",
        title:"分账单管理",
    },
    /** 物流充值  */
    FinanceStoreConfigAcLogisticsRecharge:{
        key:"sg.store.finance.logistics.recharge",
        title:"物流充值",
    },
}

/** 分销设置 */
export const  StructreSettingsAuth  = { 
    /** 保存 */
    StructreSettingsIndexSave:{
        key:"sg.store.structre.settings.index.save",
        title:"分账单结算"
    },
}

/** 分销员管理 */
export const  StructreMemberManagementAuth  = { 
    /** 添加分销员 */
    StructreMemberManagementIndexAdd:{
        key:"sg.store.structre.memberManagement.index.add",
        title:"添加分销员"
    },
    /** 冻结/解冻 */
    StructreMemberManagementIndexEnable:{
        key:"sg.store.structre.memberManagement.index.enable",
        title:"冻结/解冻"
    },
    /** 删除 */
    StructreMemberManagementIndexDelete:{
        key:"sg.store.structre.memberManagement.index.delete",
        title:"删除"
    },
    /** 归属组织 */
    StructreMemberManagementIndexBelong:{
        key:"sg.store.structre.memberManagement.index.belong",
        title:"归属组织"
    },
    /** 分销商品 */
    StructreMemberManagementIndexProduct:{
        key:"sg.store.structre.memberManagement.index.product",
        title:"分销商品"
    },
    /** 个人码 */
    StructreMemberManagementIndexPersonalCode:{
        key:"sg.store.structre.memberManagement.index.personalCode",
        title:"个人码"
    },
}

/** 佣金明细 */
export const  StructreCommissionDeatailAuth  = { 
    /** 导出 */
    StructreCommissionDeatailIndexExport:{
        key:"sg.store.structre.commissionDeatail.index.export",
        title:"导出"
    },
    /** 详情 */
    StructreCommissionDeatailIndexDetail:{
        key:"sg.store.structre.commissionDeatail.index.detail",
        title:"详情"
    },
}

/** 组织架构 */
export const  StructreDepartmentDeatailAuth  = { 
    /** 创建一级组织 */
    StructreDepartmentIndexCreateMain:{
        key:"sg.store.structre.department.index.createMain",
        title:"创建一级组织"
    },
    /** 删除 */
    StructreDepartmentIndexDelete:{
        key:"sg.store.structre.department.index.delete",
        title:"删除"
    },
    /** 编辑 */
    StructreDepartmentIndexEdit:{
        key:"sg.store.structre.department.index.edit",
        title:"编辑"
    },
    /** 创建子组织 */
    StructreDepartmentIndexCreateSub:{
        key:"sg.store.structre.department.index.createSub",
        title:"创建子组织"
    },
    /** 区域经理注册码 */
    RegionalManagerRegistrationCode:{
        key:"sg.store.structre.department.index.manager.code",
        title:"区域经理注册码"
    },
    /** 经销商注册码 */
    DealerCode:{
        key:"sg.store.structre.department.index.dealer.code",
        title:"经销商注册码"
    },
}
/** 组织申请 */
export const  OrganizationalApplicationAuth  = { 
    /** 审核 */
    Audit:{
        key:"sg.store.structre.application.index.audit",
        title:"审核"
    },
}
/** 药师管理 */
export const  PharmacistManagementAuth  = { 
    /** 新增 */
    PharmacistManagementAdd:{
        key:"sg.store.pharmacists.management.index.add",
        title:"新增药师"
    },
    /** 编辑 */
    PharmacistManagementEdit:{
        key:"sg.store.pharmacists.management.index.edit",
        title:"编辑"
    },
}

/** 机构管理 */
export const  OrganizationManagementAuth  = { 
    /** 新增 */
    OrganizationManagementAdd:{
        key:"sg.store.organization.management.index.add",
        title:"新增机构"
    },
    /** 编辑 */
    OrganizationManagementEdit:{
        key:"sg.store.organization.management.index.edit",
        title:"编辑"
    },  
    /** 删除 */
    OrganizationManagementDelete:{
        key:"sg.store.organization.management.index.delete",
        title:"删除"
    },
}

/** 科室配置 */
export const  DepartmentConfigAuth  = { 
    /** 新增一级科室 */
    DepartmentConfigAddFirstLevel:{
        key:"sg.store.department.config.index.addFirstLevel",
        title:"新增一级科室"
    },
    /** 新增二级科室 */
    DepartmentConfigAddSecondLevel:{
        key:"sg.store.department.config.index.addSecondLevel",
        title:"新增二级科室"
    },
    /** 删除 */
    DepartmentConfigDelete:{
        key:"sg.store.department.config.index.delete",
        title:"删除"
    },  
    /** 编辑 */
    DepartmentConfigEdit:{
        key:"sg.store.department.config.index.edit",
        title:"编辑"
    },
}

/* 病种管理 */
export const  DiseaseManagementAuth  = { 
    /** 新增 */
    DiseaseManagementAdd:{
        key:"sg.store.disease.management.index.add",
        title:"新增"
    },
    /** 编辑 */
    DiseaseManagementEdit:{
        key:"sg.store.disease.management.index.edit",
        title:"编辑"
    },  
    /** 删除 */
    DiseaseManagementDelete:{
        key:"sg.store.disease.management.index.delete",
        title:"删除"
    },
}

/** 问诊基础设置 */
export const  ConsultationBasicSettingsAuth  = { 
    /** 保存 */
    ConsultationBasicSettingsSave:{
        key:"sg.store.basic.setting.index.save",
        title:"保存"
    },
}
/** 问诊单 */
export const  MedicalInquiryFormManagementAuth  = {
    Index:{
        key:"sg.store.medical.inquiry.form.index",
        title:"问诊单",
    },
    /** 详情 */
    Details:{
        key:"sg.store.medical.inquiry.form.index.details",
        title:"详情"
    },
    /** 退款 */
    Refund:{
        key:"sg.store.medical.inquiry.form.index.refund",
        title:"退款"
    },
    /** 导出 */
    Export:{
        key:"sg.store.medical.inquiry.form.index.export",
        title:"导出"
    },
    /** 查看视频 */
    Video:{
        key:"sg.store.medical.inquiry.form.index.video",
        title:"查看视频"
    },
    /** 创建预约单 */
    CreateAppointment:{
        key:"sg.store.medical.inquiry.form.index.create.appointment",
        title:"创建预约单"
    },
    /** 取消预约单 */
    CancelAppointment:{
        key:"sg.store.medical.inquiry.form.index.cancel.appointment",
        title:"取消预约"
    },        
}
/** 问诊处方 */
export const  MedicalInquiryPrescriptionAuth  = {
    Index:{
        key:"sg.store.medical.inquiry.prescription.index",
        title:"问诊处方",
    },
    /** 详情 */
    Details:{
        key:"sg.store.medical.inquiry.prescription.index.details",
        title:"详情"
    },
    /** 处方笺 */
    Form:{
        key:"sg.store.medical.inquiry.prescription.index.form",
        title:"处方笺"
    }
}
/** 药师处方 */
export const  PharmacistPrescriptionAuth  = {
    Index:{
        key:"sg.store.medical.inquiry.prescription.index---",
        title:"问诊处方",
    },
    /** 详情 */
    Details:{
        key:"sg.store.medical.inquiry.prescription.index.details---",
        title:"详情"
    },
    /** 处方笺 */
    Form:{
        key:"sg.store.medical.inquiry.prescription.index.form---",
        title:"处方笺"
    }
}
/** 医助管理 */
export const  AssistantDoctorAuth  = {
    Add:{
        key:"sg.store.assistant.doctor.management.index.add",
        title:"新增",
    },
    Edit:{
        key:"sg.store.assistant.doctor.management.index.edit",
        title:"编辑"
    },
    Delete:{
        key:"sg.store.assistant.doctor.management.index.delete",
        title:"删除"
    }
}
/* 直播列表 */
export const  LiveListAuth  = { 
    /** 直播列表 */
    LiveList:{
        key:"sg.store.live.list.index",
        title:"直播列表"
    },
    /** 创建直播 */
    Add:{
        key:"sg.store.live.list.index.add",
        title:"创建直播"
    },
    /** 编辑 */
    Edit:{
        key:"sg.store.live.list.index.edit",
        title:"编辑"
    },
    /** 推流地址 */
    Stream:{
        key:"sg.store.live.list.index.stream",
        title:"推流地址"
    },
    /** 直播控制台 */
    Control:{
        key:"sg.store.live.list.index.control",
        title:"直播控制台"
    },
    /** 分享 */
    Share:{
        key:"sg.store.live.list.index.share",
        title:"分享"
    }, 
    /** 商品管理 */
    Commodity:{
        key:"sg.store.live.list.index.commodity",
        title:"商品管理"
    }, 
    /** 福利券管理 */
    Voucher:{
        key:"sg.store.live.list.index.voucher",
        title:"福利券管理"
    }, 
    /** 销售数据大屏 */
    SalesData:{
        key:"sg.store.live.list.index.sales.data",
        title:"销售数据大屏"
    }, 
    /** 查询上播率 */
    BroadcastRate:{
        key:"sg.store.live.list.index.broadcast.rate",
        title:"查询上播率"
    }, 
}
/* 观看数据统计 */
export const ViewDataStatisticsAuth  = { 
    /** 详情 */
    Details:{
        key:"sg.store.view.data.statistics.index.details",
        title:"详情"
    },
    /** 导出 */
    Export:{
        key:"sg.store.view.data.statistics.index.export",
        title:"导出"
    },
}
/**供应商管理 */
export const SupplierManagementAuth = {
    /** 供应商 - 新增 */
    supplierManagementAdd:{
        key:"sg.store.supplier.management.index.add",
        title:"新增供应商",
    },
    /** 供应商 - 编辑 */
    supplierManagementEdit:{
        key:"sg.store.supplier.management.index.edit",
        title:"编辑供应商",
    },
    /** 供应商 - 删除 */
    supplierManagementDelete:{
        key:"sg.store.supplier.management.index.delete",
        title:"删除供应商",
    },
}
/**门店管理 */
export const ShopManagementAuth = {
    WelfareVoucher:{
        key:"sg.store.shop.management.index.welfare.voucher",
        title:"发放福利券",
    },
    BatchWelfareVoucher:{
        key:"sg.store.shop.management.index.batch.welfare.voucher",
        title:"批量发放福利券",
    },
    Edit:{
        key:"sg.store.shop.management.index.edit",
        title:"编辑",
    },
    Check :{
        key:"sg.store.shop.management.index.check",
        title:"查看订单",
    },
}
/**店员店长 */
export const PersonnelManagementAuth = {
    EditRole:{
        key:"sg.store.personnel.management.index.edit.role",
        title:"修改角色",
    },
    EditShop:{
        key:"sg.store.personnel.management.index.edit.shop",
        title:"修改归属门店",
    },
    ChangeMembership:{
        key:"sg.store.shop.management.index.change.membership",
        title:"一键转会员",
    },
}
/**经销商管理 */
export const DealerManagementAuth = {
    DealerCode:{
        key:"sg.store.dealer.management.index.dealer.code",
        title:"经销商注册码",
    },
    ShopCode:{
        key:"sg.store.dealer.management.index.shop.code",
        title:"门店注册码",
    },
}
/**经销商分组 */
export const DealerGroupsAuth = {
    AddGroups:{
        key: "sg.store.dealer.groups.add",
        title: "新增分组",
    },
    DeleteGroups:{
        key: "sg.store.dealer.groups.delete",
        title: "删除分组",
    },
    EditGroups:{
        key: "sg.store.dealer.groups.edit",
        title: "编辑分组",
    },
    EditDealerTags:{
        key: "sg.store.dealer.groups.edit.relevance",
        title: "编辑关联",
    },
}

/**门店退货物流 */
export const ShopReturnGoodsLogisticsAuth = {
  export:{
    key:"sg.store.shop.return.goods.logistics.index.export",
    title:"导出",
  },
  details:{
    key:"sg.store.shop.return.goods.logistics.index.details",
    title:"详情",
  },
  logistics:{
    key:"sg.store.shop.return.goods.logistics.index.logistics",
    title:"查看物流",
  },
  review:{
    key:"sg.store.shop.return.goods.logistics.index.review",
    title:"审核",
  }
}
/**门店物流 */
export const StoreLogisticsAuth = {
    creat:{
        key:"sg.store.shop.storeLogistics.index.create",
        title:"创建物流单",
    },
    logistics:{
        key:"sg.store.shop.storeLogistics.index.getLogisticsTraces",
        title:"查看物流",
    },
    update:{
        key:"sg.store.shop.storeLogistics.index.update",
        title:"编辑",
    },
    delete:{
        key:"sg.store.shop.storeLogistics.index.delete",
        title:"删除",
    },
    batchDelete:{
        key:"sg.store.shop.storeLogistics.index.batchDelete",
        title:"批量删除",
    },
  }