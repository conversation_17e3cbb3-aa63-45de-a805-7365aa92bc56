<template>
    <div class="selling-wrapper">
        <!-- 表头 -->
        <div v-if="props.type !== 'view'" class="header">
            <JPointsTip v-if="!props.isPointEnable" />
            <JTextButton text type="primary" @click="handleAddSpec">新增规格</JTextButton>
            <JTextButton text type="error" @click="handleDeleteSpec" :disabled="!checkedRowKeysRef.length">删除规格</JTextButton>
        </div>
        <n-data-table 
            :columns="columns" 
            :data="tableDataRef" 
            :row-key="row => row.id" 
            default-expand-all
            :style="{ height: `${400}px` }" 
            flex-height
            :single-line="false"
            size="small"
            @update:checked-row-keys="handleCheck"
        >
        </n-data-table>
        <!-- 付款方式设置 -->
        <PaymentMethod 
            v-model:show="show" 
            v-model:value="model" 
            :price="price"  
            @save-successfully="handleSave"
        />
        <!-- 商品活动价设置 -->
        <JActivePrice ref="activePriceRef" @update:activityPrice="handleActivityPrice" />
    </div>
</template>

<script setup lang="tsx" name="GeneralSellingPriceInventory">
import { ref, watch } from "vue";
import type { DataTableRowKey } from "naive-ui";
import { createDummyId } from "@/utils";
import { useBoolean, useMessages } from '@/hooks';
import { UPPERDEFAULTVALUE, MAXPOINT, MINPOINT } from "../types";
import { isArray } from "@/utils";
/** 相关组件 */
import PaymentMethod from "./PaymentMethod.vue";
import InventoryDetail from "./InventoryDetail.vue";
import JPointsTip from "./JPointsTip.vue";
import JActivePrice from "./JActivePrice.vue";
import type { ActivityPriceVO } from "./JActivePrice.vue";

interface GeneralSelling {
    id: string,
    name: string, // 规格名称
    price: number, // 售价（元）
    availStocks: number, // 可用库存
    lockedStocks: number, // 冻结库存
    upper: number, // 每订单上限
    initSaled: number, // 初始已售（前端展示）
    isDownPayment: 0 | 1, // 是否支持定金支付。0=否；1=是
    isCashOnDelivery: 0 | 1, // 是否支持物流代收。0=否；1=是
    downPayment: number, // 定金单价，单位分
    points?: null, // 可获得积分
    activityPriceVO?: ActivityPriceVO, // 活动价设置
    sku: string, // 商品编码
}

type GoodsSpecificationTable = {
    value: Array<GeneralSelling>,
    type: 'add' | 'edit' | 'view';
    isPointEnable?: boolean; // 是否启用积分功能
    isVirtual?: boolean; // 是否虚拟商品
}

const { createMessageError } = useMessages();

/** Props */
const props = withDefaults(defineProps<GoodsSpecificationTable>(), {
    isPointEnable: false
});

/** emits */
const emits = defineEmits<{
    (e: 'update:value', value: Array<any>): void;
    (e: 'productSpecVOList', value: Array<any>): void;
}>();

/** 付款方式展示 */
const show = ref(false);
/** 售价 */
const price = ref(null);
/** 当前选中行 */
const checkedRowKeysRef = ref<DataTableRowKey[]>([]);

const { bool: showPopover, setFalse, setTrue } = useBoolean();
const currentFocusId = ref(null);

/** 表单参数 */
const model = ref<{
    id: string;
    isDownPayment: 0 | 1; // 是否支持定金支付。0=否；1=是
    isCashOnDelivery: 0 | 1; // 是否支持物流代收。0=否；1=是
    downPayment: number; // 定金单价，单位分
}>({
    id: null,
    isDownPayment: 0,
    isCashOnDelivery: 0,
    downPayment: null,
});

/** 表格数据 */
const tableDataRef = ref<Array<Partial<GeneralSelling>>>([]);

/** 新增规格 */
function handleAddSpec() {
    tableDataRef.value.push({
        id: createDummyId(),
        name: null,
        price: null,
        availStocks: null,
        lockedStocks: 0,
        upper: UPPERDEFAULTVALUE,
        initSaled: tableDataRef.value[0]?.initSaled ?? 0,
        isDownPayment: tableDataRef.value[0]?.isDownPayment ?? 0,
        isCashOnDelivery: tableDataRef.value[0]?.isCashOnDelivery ?? 0,
        downPayment: tableDataRef.value[0]?.downPayment ?? null,
        points: null,
        sku: null,
    });
}

/** 删除规格 */
function handleDeleteSpec() {
    if (checkedRowKeysRef.value.length > 0) {
        tableDataRef.value = tableDataRef.value.filter(item => !checkedRowKeysRef.value.includes(item.id));
        if (!tableDataRef.value.length) {
            tableDataRef.value.push({
                id: createDummyId(),
                name: null,
                price: null,
                availStocks: null,
                lockedStocks: 0, 
                upper: UPPERDEFAULTVALUE, 
                initSaled: 0,
                isDownPayment: 0 as 0 | 1,
                isCashOnDelivery: 0 as 0 | 1,
                downPayment: null,
                points: null,
                sku: null,
            });
        }
        checkedRowKeysRef.value = [];
        emits('update:value', tableDataRef.value);
    }
}

/** 选中行回调 */
function handleCheck (rowKeys: DataTableRowKey[]) {
  checkedRowKeysRef.value = rowKeys;
}

/** 表单项 */
const columns = [
    {
      type: "selection",
      key: "selection",
      fixed: "left",
      width: 50,
      minWidth: 50,
      align: "center",
    },
    {
        title: '规格',
        key: 'name',
        resizable: true,
        render: (row) => {
            return <n-input
                value={row?.name}
                onUpdateValue={(value) => handleUpdateValue(row.id, 'name', value)}
                placeholder="请输入规格"
                maxlength="30" 
                show-count
            />
        }
    },
    {
        title: '售价（元）',
        key: 'price',
        resizable: true,
        render: (row) => {
            return <n-input-number
                precision={2}
                value={row?.price}
                onUpdateValue={(value) => handleUpdateValue(row.id, 'price', value)}
                placeholder="请输入售价（元）"
                show-button={false}
                min={0}
                max={99999}
            />
        }
    },
    {
        title: "库存",
        key: 'availStocks',
        resizable: true,
        width: 120,
        render: (row) => {
            // 压测后改回max={99999}
            return (
            <div style="width: 100%; display: flex;">
                <n-popover trigger="hover" raw show={showPopover.value && currentFocusId.value === row.id}>
                    {{
                        default: () => <InventoryDetail quantityInStock={{lockedStocks: row?.lockedStocks ?? 0, availStocks: row?.availStocks ?? 0 }} />,
                        trigger: () => (
                            <n-input-number
                                value={row?.availStocks}
                                onUpdateValue={(value) => handleUpdateValue(row.id, 'availStocks', value)}
                                onFocus={() => handleFocus(row.id)}
                                onBlur={handleBlur}
                                placeholder="请输入库存"
                                min={0}
                                max={9999999}
                                show-button={false}
                                precision={0}
                                style="width: 100%;"
                            />
                        )
                    }}
                </n-popover>
            </div>
            );
        }
    },
    {
        title: '采购单价（元）',
        key: 'costPrice',
        resizable: true,
        render: (row) => {
            return <n-input-number
                precision={2}
                value={row?.costPrice}
                onUpdateValue={(value) => handleUpdateValue(row.id, 'costPrice', value)}
                placeholder="请输入采购单价（元）"
                show-button={false}
                min={0}
                max={999999}
            />
        }
    },
    {
        title: '活动价（元）',
        key: 'activePrice',
        width: 100,
        resizable: true,
        render: (row) => {
            let activityPrice = null;
            if (isArray(row['activityPriceVOList']) && row['activityPriceVOList'].length > 0) {
                activityPrice = (row['activityPriceVOList'][0]?.activityPrice / 100).toFixed(2);
            }
            return (
                <n-flex>
                    {activityPrice ? <span>{ activityPrice }</span> : null }
                    <n-button text type="primary" onClick={() => openJActivePrice(row)}>设置</n-button>
                </n-flex>
          );
        }
    },
    {
        title: '每订单上限',
        key: 'upper',
        width: 120,
        resizable: true,
        render: (row) => {
            return <n-input-number
                value={row?.upper}
                onUpdateValue={(value) => handleUpdateValue(row.id, 'upper', value)}
                placeholder="请输入每订单上限"
                show-button={false}
                precision={0}
                min={0}
                max={99999}
            />
        }
    },
    {
        title: '初始已售（前端显示）',
        key: 'initSaled',
        resizable: true,
        rowSpan: (rowData, rowIndex) => tableDataRef.value.length,
        render: (row) => {
            return <n-input-number
                value={row?.initSaled}
                onUpdateValue={(value) => handleUpdateValue(row.id, 'initSaled', value)}
                placeholder="请输入初始已售"
                show-button={false}
                precision={0}
                max={10000}
            />
        }
    },
    {
        title: '可获得积分',
        key: 'points',
        resizable: true,
        render: (row) => {
            return <n-input-number
                value={row?.points}
                onUpdateValue={(value) => handleUpdateValue(row.id, 'points', value)}
                placeholder="请输入可获得积分"
                show-button={false}
                precision={0}
                min={MINPOINT}
                max={MAXPOINT}
            />
        }
    },
    {
        title: '编码',
        key: 'sku',
        resizable: true,
        render: (row) => {
            return <n-input
                value={row?.sku}
                onUpdateValue={(value) => handleUpdateValue(row.id, 'sku', value)}
                placeholder="商家内部自定义编码"
            />
        }
    },
    {
        title: '操作',
        key: 'operation',
        rowSpan: (rowData, rowIndex) => tableDataRef.value.length,
        width: 100,
        fixed: "right",
        render: (row, index) => {
            return (
                <n-button text type="primary" onClick={() => handleSetPaymentMethod(row)}>付款方式设置</n-button>
            )
        },
    }
];

function handleFocus(id: string) {
    currentFocusId.value = id;
    setTrue();
}

function handleBlur() {
    currentFocusId.value = null;
    setFalse();
}

/** 打开活动价设置 */
const activePriceRef = ref<InstanceType<typeof JActivePrice> | null>(null);
const openJActivePrice = (row: Partial<GeneralSelling>) => {
    if (!row.price) {
        createMessageError("请先输入售价！");
        return;
    }
    activePriceRef.value?.acceptParams({
        row
    });
};

/** 设置付款方式 */
const handleSetPaymentMethod = (row: Partial<{
    id: string;
    price: number; // 售价
    isDownPayment: 0 | 1; // 是否支持定金支付。0=否；1=是
    isCashOnDelivery: 0 | 1; // 是否支持物流代收。0=否；1=是
    downPayment: number; // 定金单价，单位分
}>) => {
    if (!row?.price) {
        createMessageError("请先输入售价！");
        return;
    }
    // 判断是否是虚拟商品
    if (props.isVirtual) {
        createMessageError("虚拟商品只能线上付款，不支持设置付款方式!");
        return;
    }
    price.value = Number(row.price);
    model.value.id = row.id;
    model.value.isCashOnDelivery = row.isCashOnDelivery;
    model.value.isDownPayment = row.isDownPayment;
    model.value.downPayment = row.downPayment;
    show.value = true;
};

/** 更新值 */
const handleUpdateValue = (id: string, key: string, value: string | number) => {
    tableDataRef.value.forEach((item) => {
        // 初始已售
        if (key == 'initSaled') {
            item[key] = value as number;
        }
        if (item.id == id) {
          item[key] = value;
          // 库存填写时
          // if (key == 'availStocks') {
          //   // 判断上限是否超过库存
          //   if (Number(item['upper']) > Number(value)) {
          //       item['upper'] = null;
          //       createMessageError("每订单上限不能超过当前库存量！");
          //   }
          // }
        }
    });
    emits('productSpecVOList', tableDataRef.value);
    emits('update:value', tableDataRef.value);
}

/** 付款方式设置成功回调 */
const handleSave = (formData: {
    id: string;
    isDownPayment: 0 | 1; // 是否支持定金支付。0=否；1=是
    isCashOnDelivery: 0 | 1; // 是否支持物流代收。0=否；1=是
    downPayment: number; // 定金单价，单位分
}) => {
    tableDataRef.value.forEach(item => {
        item['isCashOnDelivery'] = formData.isCashOnDelivery;
        item['isDownPayment'] = formData.isDownPayment;
        item['downPayment'] = formData.downPayment;
    });
    emits('update:value', tableDataRef.value);
};

/** 活动价设置回调 */
function handleActivityPrice(id: string, activityPriceList: ActivityPriceVO[]) {
    const item = tableDataRef.value.find(item => item.id === id);
    if (item) {
        let activityPriceVOList = [];
        for (let i = 0; i < activityPriceList.length; i++) {
            activityPriceVOList.push({ ...activityPriceList[i] });
        }
        item['activityPriceVOList'] = activityPriceVOList;
        
    }
    emits('update:value', tableDataRef.value);
}

/** 监听 */
watch(() => props.value, (newVal) => {
    tableDataRef.value = newVal;
}, { immediate: true });
</script>

<style lang="less" scoped>
.selling-wrapper {
    position: relative;
    .header {
        display: flex;
        align-items: center;
        position: absolute;
        top: -38px;
        right: 0;
    }
}
:deep(.n-checkbox .n-checkbox-box) {
    width: 18px;
    height: 18px;
}
</style>