/** 验证身份证号码的格式和校验码 */
export const validateIDNumber = idNumber => {
  const idPattern = /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2]\d|3[0-1])\d{3}[\dXx]$/;
  if (!idPattern.test(idNumber)) {
    return false; // 格式不符合
  }

  // 校验码验证（仅针对 18 位身份证号）
  if (idNumber.length === 18) {
    const factors = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    const checksum = ["1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"];
    const nums = idNumber
      .slice(0, 17)
      .split("")
      .map(num => parseInt(num, 10));
    const sum = nums.reduce((acc, num, idx) => acc + num * factors[idx], 0);
    const expectedCheckCode = checksum[sum % 11];

    return expectedCheckCode === idNumber[17].toUpperCase(); // 校验码匹配
  }

  return true; // 如果是 15 位身份证号（旧式身份证），直接返回 true
};
