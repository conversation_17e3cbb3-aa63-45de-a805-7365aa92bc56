import Quill from "quill";

export function createVideoElement(){
    let BlockEmbed = Quill.import('blots/block/embed');

    class VideoBlot extends BlockEmbed {
      static create(url) {
        let node = super.create();
        node.setAttribute('src', url);
        node.setAttribute('controls', true); 
        node.setAttribute('width', '320'); 
        node.setAttribute('height', 'auto'); 
        return node;
      }
    
      static value(node) {
        return node.getAttribute('src');
      }
    }

    VideoBlot.blotName = 'video';
    VideoBlot.tagName = 'video';
    Quill.register(VideoBlot);
}