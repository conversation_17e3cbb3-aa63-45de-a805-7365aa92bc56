export const enum ReceiveRecordEnum {
    /** 全部 */
    ALL = -1,
    /** 未使用 */
    UNUSED = 0,
    /** 已使用 */
    USE = 1,
    /** 已失效 */
    FAILURE = 2,
}

export const enum WelfareModalTypeEnum {
    /** 添加福利券 */
    ADD = 0,
    /** 修改有效期 */
    VALIDITY = 1,
    /** 编辑福利券 */
    EDIT = 2
}

export const enum WelfarePageTypeEnum {
    /**商城 页面形式 */
    PAFGE = 0,
    /**直播 弹框形式 */
    LIVEROOM = 1,
}

export const enum WelfareTypeEnum {
    /** 福利券 */
    WELFARE = 1,
    /** 时长券 */
    TIME = 2,
}

export const enum WelfareStatusEnum {
    /** 未发放 */
    UNISSUED = 0,
    /** 已发放 */
    ISSUING = 1,
    /** 已结束 停止发放 */
    ENDED = 2,
}

export const enum LiveStatusEnum {
    /** 直播中 */
    LIVEING = 1,
    /** 未开始 */
    NOTSTART = 2,
    /** 已结束 */
    ENDED = 3,
}