import { defHttp } from "@/services";

/** 供应商管理 */
export const enum supplierMessageApi {
    addSupplier = "/supplier/addSupplier",
    updateSupplier = "/supplier/updateSupplier",
    getSupplierById = "/supplier/getSupplierById",
    batchDeleteSupplier = "/supplier/batchDeleteSupplier",
    pageSupplier = "/supplier/pageSupplier",
    enableSupplier = "/supplier/enableSupplier",
  
}

/**
 * @description 新增供应商
 */
export function addSupplier(params) {
    return defHttp.post({
        url: supplierMessageApi.addSupplier,
        params,
    });
}

/**
 * @description 修改供应商
 */
export function updateSupplier(params) {
    return defHttp.put({
        url: supplierMessageApi.updateSupplier,
        params,
    });
}

/**
 * @description 根据Id获取供应商
 */
export function getSupplierById(id: string) {
    return defHttp.get({
        url: supplierMessageApi.getSupplierById + `?id=${id}`
    });
}

/**
 * @description 根据ID批量删除供应商
 */
export function batchDeleteSupplier(params) {
    return defHttp.delete({
        url: supplierMessageApi.batchDeleteSupplier,
        params,
        requestConfig: {
            isQueryParams: true,
        },
    });
}

/**
 * @description 分页查询供应商列表
 */
export function pageSupplier(params) {
    return defHttp.post({
        url: supplierMessageApi.pageSupplier,
        params,
    });
}

/**
 * @description 启用/禁用供应商
 * @param supplierId 供应商id
 * @param enable 启用状态 0=禁用,1=启用
 */
interface EnableSupplierRes {
    data:{
        supplierId:string,
        enable:0|1
    }
}
export function enableSupplier(params:EnableSupplierRes) {
    return defHttp.put({
        url: supplierMessageApi.enableSupplier,
        params,
    });
}