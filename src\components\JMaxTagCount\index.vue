<template>
  <div style="display: flex">
    <n-space>
      <template v-for="(item, index) in props.data" :key="item">
        <n-tag v-if="index < props.maxTagCount" :bordered="false" size="small">
          <n-ellipsis style="max-width: 80px">
            {{ item["name"] }}
          </n-ellipsis>
        </n-tag>
      </template>
      <n-popover trigger="hover" v-if="props.data.length > props.maxTagCount">
        <template #trigger>
          <n-tag :bordered="false" size="small">+{{ props.data.length - props.maxTagCount }}</n-tag>
        </template>
        <n-space style="width: 200px">
          <template v-for="(item, index) in props.data" :key="item">
            <n-tag :bordered="false" size="small">
              {{ item["name"] }}
            </n-tag>
          </template>
        </n-space>
      </n-popover>
    </n-space>
  </div>
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    data?: Array<{}>;
    maxTagCount?: number;
  }>(),
  {
    data: () => [],
    maxTagCount: 3,
  }
);
</script>

<style scoped></style>
