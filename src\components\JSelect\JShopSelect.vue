<template>
  <JSelect
    :options="userDataList"
    :render-label="renderLabel"
    :render-tag="renderSingleSelectTag"
    clearable
    filterable
    v-bind="$attrs"
    @focus="handlerFocus"
    value-field="id"
    :isShowTooltip="false"
    @search="handlerSearch"
    @clear="handlerSearch"
    :loading="isLoading"
    @scroll="handleScroll"
    remote
  />
</template>

<script setup lang="ts">
import { ref, reactive, toRefs, onMounted, computed } from "vue";
import { defineComponent, h } from "vue";
import type { SelectRenderLabel, SelectRenderTag } from "naive-ui";
import { NAvatar, NTag, NText, NTooltip } from "naive-ui";
import { getShopPageCommon } from "@/services/api";
import { _debounce } from "@/utils";
// 获取焦点
const handlerFocus = () => {
  pageVO.current = 1;
  getShopList(true); 
};
const _searchValue = ref(""); // 缓存搜索数据
const handlerSearch = (value: string) => {
  _searchValue.value = value;
  debunceGetlist(true, value);
};

const pageVO = reactive({
  current: 1,
  size: 30,
});
const recordsTotal = ref(0);
// 加载数据
const isLoading = ref<boolean>(false);
/**
 * @param isClear 是否清空现有数据(筛选时传入)
 */
const getShopList = (isClear: boolean = false, searchValue: string = "") => {
  isLoading.value = true;
  const params = {
    data: {
      nameOrShortId: searchValue,
    },
    pageVO,
  };
  getShopPageCommon(params)
    .then(res => {
      console.log(res, "res");
      if (isClear) {
        userDataList.value = res.records;
      } else {
        userDataList.value.push(...res.records);
      }
      recordsTotal.value = res.total;
    })
    .catch(err => {
      console.log(err, "err");
    })
    .finally(() => {
      isLoading.value = false;
    });
};

const debunceGetlist = _debounce(getShopList, 500);

const userDataList = ref([]);

const renderSingleSelectTag = ({ option }) => {
  return option.storeName;
};

function handleScroll(e) {
  const currentTarget = e.currentTarget as HTMLElement;
  if (currentTarget.scrollTop + currentTarget.offsetHeight >= currentTarget.scrollHeight - 20) {
    if (pageVO.current * pageVO.size < recordsTotal.value) {
      pageVO.current++;
      debunceGetlist();
    }
  }
}

const renderLabel: SelectRenderLabel = option => {
  const renderSelectItem = () =>
    h(
      "div",
      {
        style: {
          display: "flex",
          alignItems: "center",
        },
      },
      [
        h(
          "div",
          {
            style: {
              marginLeft: "12px",
              padding: "4px 0",
              overflow: "hidden",
            },
          },
          [
            h("div", null, `门店名称：${option?.storeName ? option?.storeName : "-"}`),
            h(
              NText,
              {
                depth: 3,
                tag: "div",
                style: {
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                },
              },
              {
                default: () => `门店地址：${option?.addressDetail ? option?.addressDetail : "-"}`,
              },
            ),
            h("div", { style: { color: "#999999" } }, { default: () => `门店ID：${option?.shortId ? option?.shortId : '-'}` }),
            h("div", { style: { color: "#999999" } }, { default: () => `店长姓名：${option?.contactName ? option?.contactName : '-'}` }),
          ],

        ),
      ],
    );
  return h(
    NTooltip,
    { duration: 0 },
    {
      trigger: renderSelectItem,
      default: renderSelectItem,
    },
  );
};
</script>
<style scoped lang="less"></style>
