<template>
  <JModal
    v-model:show="isShow"
    width="680"
    title="创建预约单（仅限视频问诊）"
    positiveText="确定"
    @after-leave="closeModal"
    @positive-click="_save"
    :positiveButtonProps="{
      loading: isLoading,
    }"
  >
    <n-form
      ref="formRef"
      :rules="rules"
      :model="model"
      label-width="auto"
      label-placement="left"
      require-mark-placement="right-hanging"
      :style="{
        width: '100%',
      }"
    >
      <n-grid :cols="8" :x-gap="24">
        <n-form-item-gi :span="24" label="预约医生" path="doctorId" required>
          <JSelectNewDoctor v-model:value="model.doctorId" label-format="{doctorName} - {institutionName}" isImmediately placeholder="输入医生姓名搜索" />
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="预约时间" path="preBookTime" required>
          <n-date-picker
            style="width: 100%"
            :is-date-disabled="isDateDisabled"
            :is-time-disabled="isTimeDisabled"
            v-model:value="model.preBookTime"
            type="datetime"
            clearable
          />
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="问诊用户" path="customerId" required>
          <JUserSelect
            v-model:value="model.customerId"
            placeholder="输入用户昵称搜索"
            @update:value="
              () => {
                model.customerDrugUserId = null;
              }
            "
          />
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="选择患者" path=" customerDrugUserId " required>
          <JSelectPatient v-model:value="model.customerDrugUserId" :customerId="model.customerId" />
        </n-form-item-gi>
        <!-- 提示 -->
        <n-gi :span="24" style="margin-left: 65px; margin-bottom: 12px">
          <n-button text type="primary" @click="handlerCreatePatient">新增患者资料</n-button>
        </n-gi>
        <n-form-item-gi :span="24" label="病情描述" path="    chiefComplaint" required>
          <n-input v-model:value="model.chiefComplaint" maxlength="100" show-count type="textarea" />
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="患病时长" path="afterSalelmgVOList" required>
          <n-radio-group v-model:value="model.period" name="radiogroup">
            <n-space>
              <n-radio v-for="song in periodOp" :key="song.value" :value="song.value">
                {{ song.label }}
              </n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="医助" path="physicianAssistantId ">
          <JSelectAssistantDoctor v-model:value="model.physicianAssistantId" />
        </n-form-item-gi>
        <!-- 提示 -->
        <n-gi :span="24" style="margin-left: 65px; margin-bottom: 12px">
          <span>医助可以加入视频</span>
        </n-gi>
      </n-grid>
    </n-form>
  </JModal>
  <PatientInformation
    v-model:show="showPatientInformation"
    :customerId="model.customerId"
    @id="id => (model.customerDrugUserId = id)"
    ref="patientInformationRef"
  />
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { useMessages } from "@/hooks";
import JSelectNewDoctor from "@/components/JSelect/JSelectNewDoctor.vue";
import JUserSelect from "@/components/JSelect/JUserSelect.vue";
import JSelectPatient from "@/components/JSelect/JSelectPatient.vue";
import JSelectAssistantDoctor from "@/components/JSelect/JSelectAssistantDoctor.vue";
import PatientInformation from "./PatientInformation.vue";
import { addReservationForm } from "@/services/api";
import moment from "moment";
const { createMessageSuccess, createMessageError, createMessageWarning } = useMessages();
const props = withDefaults(
  defineProps<{
    show: boolean;
  }>(),
  {
    show: false,
  },
);
const emits = defineEmits<{
  (e: "update:show", value: boolean): void;
  (e: "refresh"): void;
}>();

const isShow = computed({
  get: () => props.show,
  set: (value: boolean) => {
    emits("update:show", value);
  },
});
const periodOp = [
  {
    label: "一周内",
    value: 1,
  },
  {
    label: "一个月内",
    value: 2,
  },
  {
    label: "半年内",
    value: 3,
  },
  {
    label: "半年以上",
    value: 4,
  },
];
const initialData = {
  doctorId: null,
  preBookTime: null,
  customerId: null,
  customerDrugUserId: null,
  period: 1,
  assistantDoctorId: null,
  chiefComplaint: "",
  physicianAssistantId: null,
};
const model = ref({ ...initialData });
const showPatientInformation = ref(false);
/* 表单规则 */
const rules = {
  doctorId: {
    required: true,
    trigger: ["blur", "change"],
    message: "请选择预约医生",
    validator: () => {
      return model.value.doctorId !== null;
    },
  },
  preBookTime: {
    required: true,
    trigger: ["blur", "change"],
    message: "请选择预约时间",
    validator: () => {
      return model.value.preBookTime !== null;
    },
  },
  customerId: {
    required: true,
    trigger: ["blur", "change"],
    message: "请选择问诊用户",
    validator: () => {
      return model.value.customerId !== null;
    },
  },
  customerDrugUserId: {
    required: true,
    trigger: ["blur", "change"],
    message: "请选择患者",
    validator: () => {
      return model.value.customerDrugUserId !== null;
    },
  },
  chiefComplaint: {
    required: true,
    trigger: ["blur", "change"],
    message: "请输入病情描述",
    validator: () => {
      return model.value.chiefComplaint !== "";
    },
  },
};
const getParams = () => {};
const isLoading = ref(false);

// 关闭按钮
const closeModal = () => {
  isShow.value = false;
  model.value = {
    ...initialData,
  };
};
const handlerCreatePatient = () => {
  if (!model.value.customerId) {
    return createMessageWarning("请选择问诊用户");
  }
  showPatientInformation.value = true;
};
// 确认按钮
const formRef = ref(null);
const isDateDisabled = (ts: number) => {
  // 获取当前日期（不含时间）
  const today = moment().startOf("day");
  // 获取目标日期
  const targetDate = moment(ts).startOf("day");

  // 若目标日期早于今天，则禁用
  return targetDate.isBefore(today);
};
const isTimeDisabled = (ts: number) => {
  const now = moment(); // 当前时间
  const target = moment(ts); // 目标时间
  const today = moment().startOf("day"); // 今天零点
  const targetDate = moment(ts).startOf("day"); // 目标日期零点

  // 如果目标日期不是今天，不禁用时间
  if (!targetDate.isSame(today, "day")) {
    return {
      isHourDisabled: () => false,
      isMinuteDisabled: () => false,
      isSecondDisabled: () => false,
    };
  }

  // 如果是今天，禁用当前时间之前的时间
  return {
    isHourDisabled: (hour: number) => hour < now.hour(),
    isMinuteDisabled: (minute: number) => {
      if (target.hour() < now.hour()) return true; // 小时已过，分钟全禁用
      return target.hour() === now.hour() && minute < now.minute(); // 同一小时，禁用当前分钟前的
    },
    isSecondDisabled: (second: number) => {
      if (target.hour() < now.hour()) return true; // 小时已过，秒全禁用
      if (target.hour() === now.hour() && target.minute() < now.minute()) return true; // 分钟已过，秒全禁用
      return target.hour() === now.hour() && target.minute() === now.minute() && second < now.second(); // 同分禁用当前秒前的
    },
  };
};

const _save = () => {
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      isLoading.value = true;
      const _params = {
        data: {
          doctorId: model.value.doctorId,
          preBookTime: model.value.preBookTime ? moment(model.value.preBookTime).format("YYYY-MM-DD HH:mm:ss") : null,
          customerId: model.value.customerId,
          customerDrugUserId: model.value.customerDrugUserId,
          chiefComplaint: model.value.chiefComplaint,
          period: model.value.period,
          physicianAssistantId: model.value.physicianAssistantId,
        },
      };
      try {
        await addReservationForm(_params);
        createMessageSuccess("操作成功");
        emits("refresh");
        closeModal();
      } catch (error) {
        createMessageError(`${error}`);
      } finally {
        isLoading.value = false;
      }
    }
  });
};
</script>
