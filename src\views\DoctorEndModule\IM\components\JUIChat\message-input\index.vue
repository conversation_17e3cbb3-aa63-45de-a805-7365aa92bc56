<template>
  <div class="message-input-wrapper">
    <div :class="['message-input-container']">
      <MessageInputEditor
          ref="editor"
          :placeholder="props.placeholder"
          :isMuted="props.isMuted"
          :muteText="props.muteText"
          :enableInput="props.enableInput && tabName != 'completed' && currentConversationData?.status !== 'finish'"
          :enableAt="props.enableAt"
          :enableTyping="props.enableTyping"
          :enableDragUpload="props.enableDragUpload"
          @sendMessage="sendMessage"
          @onTyping="onTyping"
          @onAt="onAt"
      />
      <!--      <NInput-->
      <!--          v-model:value="inputValue"-->
      <!--          type="textarea"-->
      <!--          placeholder="基本的 Textarea"-->
      <!--      />-->
      <MessageInputButton
          :enable-send="tabName != 'completed' && currentConversationData?.status !== 'finish'"
          v-if="!props.isMuted"
          @sendMessage="sendMessage"
      />
    </div>
    <MessageInputQuote/>
  </div>
</template>

<script setup lang="ts">
import {inject, ref} from 'vue';
import MessageInputEditor from '@/views/DoctorEndModule/IM/components/JUIChat/message-input/message-input-editor.vue';
import MessageInputButton from '@/views/DoctorEndModule/IM/components/JUIChat/message-input/message-input-button.vue';
import MessageInputQuote
  from '@/views/DoctorEndModule/IM/components/JUIChat/message-input/message-input-quote/index.vue';
import {sendMessages, sendTyping} from "@/views/DoctorEndModule/IM/utils/sendMessage";
// import {sendMessages, sendTyping} from '../utils/sendMessage';


const props = defineProps({
  placeholder: {
    type: String,
    default: 'this is placeholder',
  },
  isMuted: {
    type: Boolean,
    default: true,
  },
  muteText: {
    type: String,
    default: '',
  },
  enableInput: {
    type: Boolean,
    default: true,
  },
  enableAt: {
    type: Boolean,
    default: true,
  },
  enableDragUpload: {
    type: Boolean,
    default: true,
  },
  enableTyping: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(['sendMessage', 'resetReplyOrReference', 'onTyping']);
const editor = ref<InstanceType<typeof MessageInputEditor>>();
// const messageInputAtRef = ref<InstanceType<typeof MessageInputAt>>();
// 当前会话缓存
const {currentConversationData, changeCurrentConversation} = inject('currentConversationData');

const {tabName} = inject('tabName'); // 当前的TabName,值为 processing | completed

const onTyping = (inputContentEmpty: boolean, inputBlur: boolean) => {
  sendTyping(inputContentEmpty, inputBlur);
};

const onAt = (show: boolean) => {
  // messageInputAtRef.value?.toggleAtList(show);
};

const sendMessage = async () => {
  const _editorContentList = editor.value?.getEditorContent();
  if (!_editorContentList || !currentConversationData.value) return;
  const editorContentList = _editorContentList.map((editor: any) => {
    if (editor.type === 'text') {
      // editor.payload.text = transformTextWithEmojiNamesToKeys(editor.payload.text);
    }
    return editor;
  });
  await sendMessages(
      editorContentList,
      currentConversationData.value,
  );
  emit('sendMessage');
  editor.value?.resetEditor();
};

const insertEmoji = (emoji: any) => {
  editor.value?.addEmoji(emoji);
};

const onAtListOpen = () => {
};

const insertAt = (atInfo: any) => {
  editor?.value?.insertAt && editor?.value?.insertAt(atInfo);
};

const reEdit = (content: any) => {
  editor.value?.resetEditor();
  editor.value?.setEditorContent(content);
};

defineExpose({
  insertEmoji,
  reEdit,
});
</script>

<style scoped lang="less">

.message-input-wrapper {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.message-input-container {
  box-sizing: border-box;
  flex: 1;
  display: flex;
  flex-direction: column;
  border: none;
  overflow: hidden;
}

.message-input-container-h5 {
  display: flex;
  flex-flow: row nowrap;
  align-items: flex-end;
}
</style>
