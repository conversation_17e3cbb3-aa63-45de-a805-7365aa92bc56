import { ref } from "vue";
import { useMessages } from "@/hooks";
import dayjs from "dayjs";
import { addCouponBatch, 
         updateCouponBatch,
         updateValidDate, 
         updateValidDateForPc,
         deleteCouponBatch, 
         sendHandle,
         sendHandleForPc         
        } from "@/services/api";
const { createMessageSuccess, createMessageError } = useMessages();
import { WelfareModalTypeEnum,WelfarePageTypeEnum } from "../types"

const isPopconfirmLoadingRef = ref(false);
const isAddLoadingRef = ref<boolean>(false)
const liveRoomIdRef = ref<string>('')
export function useWelfareTicketManage() {
    /** 新增直播福利券 */
    async function onCouponBatchFn( welfareModalType,liveRoomId,data, refresh:()=>void) {
        try {
            isAddLoadingRef.value = true
            const endOfDay = dayjs(data.validUntil).endOf('day').format('YYYY-MM-DD HH:mm:ss');
            const watchDurationParams = data.receiveConditionType == 2 ? {watchDuration:data.minute} : {}
            const params = {
                data:{
                    name: data.name,
                    totalQuantity:data.isInfinite ? -1 : data.totalQuantity,
                    validUntil: endOfDay,
                    categoryId: data.categoryId,
                    receiveConditionType:data.receiveConditionType,
                    ...watchDurationParams,
                    liveRoomId
                }
            }
            if(data.id){
                params.data['id'] = data.id
            }
            const api = welfareModalType == WelfareModalTypeEnum.ADD ? addCouponBatch:updateCouponBatch
            await api(params)
            refresh()
            createMessageSuccess("操作成功")
        } catch (error) {
            createMessageError("操作异常"+error)
        } finally {
            isAddLoadingRef.value = false
        }
    }
    /** 修改福利券有效期 */ 
    async function updateCouponValidityFn(welfarePageType,data, refresh:()=>void) {
        try {
            isAddLoadingRef.value = true
            const endOfDay = dayjs(data.validUntil).endOf('day').format('YYYY-MM-DD HH:mm:ss');
            const params = {
                id: data.id,
                validUntil: endOfDay
            }
            const api = welfarePageType == WelfarePageTypeEnum.LIVEROOM ? updateValidDate : updateValidDateForPc
            await api(params)
            refresh()
            createMessageSuccess("操作成功")
        } catch (error) {
            createMessageError("操作异常"+error)
        } finally {
            isAddLoadingRef.value = false
        }
    }

    /** 删除福利券 */
    async function deleteCouponBatchFn(id:string, refresh:()=>void) {
        try {
            isPopconfirmLoadingRef.value = true
            await deleteCouponBatch(id)
            refresh()
            createMessageSuccess("操作成功")
        } catch (error) {
            createMessageError("操作异常"+error)
        } finally {
            isPopconfirmLoadingRef.value = false
        }
    }
    /** 批次发放开关 1-开启发放 0-停止发放 */
    async function sendHandleFn(welfarePageType,sendEnable:0 | 1, row:any, refresh:()=>void) {
        try {
            isPopconfirmLoadingRef.value = true
            const api = welfarePageType == WelfarePageTypeEnum.LIVEROOM ? sendHandle : sendHandleForPc
            await api({id:row.id,sendEnable})
            refresh()
            createMessageSuccess("操作成功")
        } catch (error) {
            createMessageError("操作异常"+error)
        } finally {
            isPopconfirmLoadingRef.value = false
        }
    }
    return {
        isAddLoadingRef,
        isPopconfirmLoadingRef,
        onCouponBatchFn,
        updateCouponValidityFn,
        deleteCouponBatchFn,
        sendHandleFn,
        liveRoomIdRef
    }
}