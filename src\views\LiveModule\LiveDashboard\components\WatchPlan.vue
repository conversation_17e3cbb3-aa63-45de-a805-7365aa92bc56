<template>
    <div class="watch-plan-wrapper">
        <div class="plan" v-for="item in watchPlanDataRef.list" :key="item.name">
            <div class="plan-top">
                <span>{{item.name}}</span>
                <div class="plan-top-right">
                    <span class="sum">{{item.num}}人</span>
                    <span>{{watchPlanDataRef.sum?((item.num/watchPlanDataRef.sum)*100).toFixed(2):0}}%</span>
                </div>
            </div>
            <div class="plan-bottom">
                <n-progress
                    style="margin: 0 8px 12px 0"
                    type="line"
                    :show-indicator="false"
                    :percentage="(item.num/watchPlanDataRef.sum)*100 || 0"
                    :color="item.color"
                    rail-color="#0a1752"
                />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import useLiveDashboard from "../hook/useLiveDashboard";
const { watchPlanDataRef } = useLiveDashboard()

</script>

<style lang="less" scoped>
.watch-plan-wrapper{
    .plan{
        width: 100%;
        height: 100%;
        .plan-top{
            display: flex;
            justify-content: space-between;
            margin: 20px 0 12px;
            .plan-top-right{
                display: flex;
                align-items: center;
                .sum{
                    margin-right: 20px;
                }
            }
        }
    }
}
</style>
