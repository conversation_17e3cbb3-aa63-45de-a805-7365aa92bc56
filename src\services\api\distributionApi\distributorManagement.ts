import { defHttp } from '@/services';

/** 分销员管理 */
export const enum distributors{
    structureListStructures="/structure/listForOthers",
    distributorPageCsInfo = "/distributor/pageCsInfo",
    distributorPage = '/distributor/page',
    distributorAdd = '/distributor/add',
    distributorSuspend = '/distributor/suspend',
    distributorDelete = '/distributor/delete',
    distributorUpdateStruct = '/distributor/updateStruct',
    distributeProduct = "/distributeProduct/page/distributeProduct",
    distributeProductAdd = "/distributeProduct/add",
    distributeProductDelete = "/distributeProduct/delete",
    distributePage = "/distributeProduct/get/cateList",
    updateProductRange = "/distributor/updateProductRange",
    getPersonalCode= "/distributor/getPersonalCode",
}

/** 开发配置 */
export const enum distributionSettings{
    globalConfigsGetDistributionConfig = "/globalConfigs/getDistributionConfig",
    globalConfigsUpdateDistributionConfig = "/globalConfigs/updateDistributionConfig"
}

export function structureListStructures(params) {
    return defHttp.post({
        url: distributors.structureListStructures,
        params
    });
}

/** 分页检索客户 */
export function distributorPageCsInfo(params) {
    return defHttp.post({
        url: distributors.distributorPageCsInfo,
        params
    });
}

/** 分页检索分销员 */
export function distributorPage(params) {
    return defHttp.post({
        url: distributors.distributorPage,
        params
    });
}

/** 添加分销员 */
export function distributorAdd(params) {
    return defHttp.post({
        url: distributors.distributorAdd,
        params
    });
}

/** 冻结或解冻分销员 */
export function distributorSuspend(params) {
    return defHttp.put({
        url: distributors.distributorSuspend,
        params
    });
}

/** 删除分销员 */
export function distributorDelete(id) {
    return defHttp.delete({
        url: distributors.distributorDelete +'?id=' + id,
        requestConfig:{
          isQueryParams:false
        }
    });
}

/** 更改归属组织 */
export function distributorUpdateStruct(params) {
    return defHttp.put({
        url: distributors.distributorUpdateStruct,
        params
    });
}

/** 分页查询分销商品列表 */
export function distributeProduct(params) {
    return defHttp.post({
        url: distributors.distributeProduct,
        params
    });
}

/** 添加分销商品 */
export function distributeProductAdd(params) {
    return defHttp.post({
        url: distributors.distributeProductAdd,
        params
    });
}

/** 移除分销商品 */
export function distributeProductDelete(params) {
    return defHttp.delete({
        url: distributors.distributeProductDelete + '?id=' + params,
        requestConfig:{
            isQueryParams:false
        }
    });
}

/** 分页查询分销商品分类列表 */
export function distributePage(params) {
    return defHttp.get({
        url: distributors.distributePage,
        params
    });
}

/** 更改分销商品范围 */
export function updateProductRange(params) {
    return defHttp.put({
        url: distributors.updateProductRange,
        params
    });
}

/** 获取个人分销码 */
export function getPersonalCode(params) {
    return defHttp.get({
        url: distributors.getPersonalCode + "?id=" + params,
    });
}

/** 获取分销(分佣)开发配置 */
export function globalConfigsGetDistributionConfig(params) {
    return defHttp.post({
        url: distributionSettings.globalConfigsGetDistributionConfig,
        params
    });
}

/** 修改后台分销设置 */
export function globalConfigsUpdateDistributionConfig(params) {
    return defHttp.post({
        url: distributionSettings.globalConfigsUpdateDistributionConfig,
        params
    });
}


/** 佣金明细 */
export const enum distribution{
    distributionPage =  "/distribution/page",
    distributionGet = '/distribution/get',
    distributionDataExport = '/distribution/select/distributionDataExport',
}

/** 分页查询分销(分佣)列表 */
export function distributionPage(params) {
    return defHttp.post({
        url: distribution.distributionPage,
        params
    });
}

/** 分销记录导出 */
export function distributionDataExport(params) {
    return defHttp.post({
        url: distribution.distributionDataExport,
        requestConfig:{
            responeseType:'stream'
        },
        params
    });
}

/** 分销记录详情 */
export function distributionGet(params) {
    return defHttp.get({
        url: distribution.distributionGet + '?distributionId=' + params,
    });
}

/** 组织架构 */
export const enum structure{
    listStructures =  '/structure/listStructures',
    structureDelete = '/structure/delete',
    structureUpdate = '/structure/update',
    addSubOrganization = '/structure/addSubOrganization',
    addHeadOrganization = '/structure/addHeadOrganization',
    // storeCustomer="/customerEntity/page/storeCustomer",
    storeCustomer="/customerEntity/page/common",
    getStoreMain="/structure/getStoreMain",
    getStoreDesc="/structure/getStoreDesc",
    getSubStructQrCode="/structure/getSubStructQrCode",
    bindThirdDealer = "/storeEntity/bindThirdDealer",
    unbindThirdDealer = "/storeEntity/unbindThirdDealer",
}

/** 组织列表 */
export function listStructures(params) {
    return defHttp.post({
        url: structure.listStructures,
        params
    });
}

/** 删除一行记录 */
export function structureDelete(params) {
    return defHttp.delete({
        url: structure.structureDelete + '?code=' + params,
        requestConfig:{
            isQueryParams:false
        }
    });
}

/** 修改组织 */
export function structureUpdate(params) {
    return defHttp.put({
        url: structure.structureUpdate,
        params
    });
}

/** 添加子组织 */
export function addSubOrganization(params) {
    return defHttp.post({
        url: structure.addSubOrganization,
        params
    });
}

/** 添加一级组织 */
export function addHeadOrganization(params) {
    return defHttp.post({
        url: structure.addHeadOrganization,
        params
    });
}

/** 分页 门店查询客户列表 */
export function getStoreCustomer(params) {
    return defHttp.post({
        url: structure.storeCustomer,
        params
    });
}

/** 获取门店二维码 */
export function getStoreMain(params) {
    return defHttp.get({
        url: structure.getStoreMain + '?structureId=' + params,
    });
}

/** 获取门店详情 */
export function getStoreDesc(params) {
    return defHttp.get({
        url: structure.getStoreDesc + '?structureId=' + params,
    });
}
// /** 获取区域二维码 */
export function getSubStructQrCode(structureId) {
    return defHttp.get({
        url: structure.getSubStructQrCode + '?structureId=' + structureId,
    });
}

/** 组织申请 */
export const enum application{
    structureApplyPage =  '/structureApply/page', // 组织申请分页
    structureApplyAudit = '/structureApply/audit', // 组织申请审核
}
/** 组织申请分页列表 */
export function structureApplyPage(params) {
    return defHttp.post({
        url: application.structureApplyPage,
        params
    });
}
/** 组织申请-审核 */
export function structureApplyAudit(params) {
    return defHttp.put({
        url: application.structureApplyAudit,
        params
    });
}
/** 绑定经销商 */
export function bindThirdDealer(params) {
    return defHttp.post({
        url: structure.bindThirdDealer,
        params
    });
}
/** 解绑经销商 */
export function unbindThirdDealer(params) {
    return defHttp.post({
        url: structure.unbindThirdDealer,
        params
    });
}

