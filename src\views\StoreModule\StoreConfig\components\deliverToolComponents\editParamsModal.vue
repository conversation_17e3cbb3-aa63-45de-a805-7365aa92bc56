<template>
    <JModal
    v-model:show="isShow"
    width="680"
    title="编辑参数"
    @after-leave="closeModal"
		@positive-click="_save"
		:positiveButtonProps="{
			loading: isLoading
		}"
  >
  <n-spin :show="isLoading">
    <n-form
      ref="formRef"
      :rules="rules"
      :model="model"
      label-width="auto"
      label-placement="left"
      require-mark-placement="right-hanging"
      :style="{
          width: '100%',
        }"
    >
      <n-grid :cols="8" :x-gap="24">
        <n-form-item-gi :span="24" label="应用名称" path="appName" required>
          <n-input v-model:value="model[ZtoParams.appName]" placeholder="请输入应用名称" :maxlength="30" clearable show-count/>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="AppKey" path="appKey" required>
          <n-input v-model:value="model[ZtoParams.appKey]" placeholder="请输入AppKey" :maxlength="255" clearable show-count/>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="AppSecret" path="appSecret" required>
          <n-input v-model:value="model[ZtoParams.appSecret]" placeholder="请输入AppSecret" :maxlength="255" clearable show-count/>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="shopKey" path="shopKey" required>
          <n-input v-model:value="model[ZtoParams.shopKey]" placeholder="请输入shopkey" :maxlength="255" clearable show-count/>
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </n-spin>
  </JModal>
</template>

<script setup lang="ts">
import { ref,computed,watch } from 'vue';
import { updateZtoParams, getZtoParams } from '@/services/api';
import { ZtoParams } from '@/views/StoreModule/StoreConfig/type';
import { useMessages } from "@/hooks";
const message = useMessages();

const props = withDefaults(defineProps<{
    show: boolean;
}>(), {
    show: false,
});
const emits = defineEmits<{
    (e: 'update:show', value: boolean): void;
    (e: 'refresh'): void;
}>();

const isShow = computed({
    get: () => props.show,
    set: (value: boolean) => {
        emits('update:show', value);
    }
});

const initParams = {
  [ZtoParams.appName]: '',
  [ZtoParams.appKey]: '',
  [ZtoParams.appSecret]: '',
  [ZtoParams.shopKey]: ''
};
const model = ref({ ...initParams });

/* 表单规则 */
const rules = {
  appName:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入应用名称",
    validator: ()=>{
      return model.value[ZtoParams.appName] != '';
    }
  },
  appKey:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入AppKey",
    validator: ()=>{
      return model.value[ZtoParams.appKey] != '';
    }
  },
  appSecret:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入AppSecret",
    validator: ()=>{
      return model.value[ZtoParams.appSecret] != '';
    }
  },
  shopKey:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入shopkey",
    validator: ()=>{
      return model.value[ZtoParams.shopKey] != '';
    }
  }
};

const isLoading = ref(false);

/** 获取参数 */
const getParams = () => {
  isLoading.value = true;
  getZtoParams().then(res=>{
    console.log(res);
    Object.keys(res).forEach(key => {
        model.value[ZtoParams[key]] = res[key];
    })
  }).finally(()=>{
    isLoading.value = false;
  })
}

// 关闭按钮
const closeModal = () => {
    isShow.value = false;
    model.value = { ...initParams };
}

watch(()=>props.show, ()=>{
  if(props.show){
    getParams();
  }
})

// 确认按钮
const formRef = ref(null);
const _save = () => {
    formRef.value?.validate((errors: any) => {
        if (!errors) {
            console.log('errors', errors);
            // TODO:调用接口
            const params = Object.keys(model.value).map(key => ({
              key,
              value: model.value[key],
              groupName:'shippingToolConfig'
            }));
            
            isLoading.value = true;
            updateZtoParams({data:params}).then((res: any) => {
              message.createMessageSuccess('操作成功');
              emits('refresh');
              closeModal();
            }).catch(err=>{
              message.createMessageError(`${err}`);
            }).finally(()=>{
              isLoading.value = false;
            })
        }
    });
}

</script>