import { ref, watch, type VNode } from "vue";
import { h } from "vue";
import "@/styles/mainMenu.less";
import { RouterLink, useRoute, useRouter } from "vue-router";
import { isArray } from "@/utils/isUtils";
import { useActiveRoute } from "@/hooks/useActiveRoute";
import { RoutesName } from "@/enums/routes";
import { useUserStoreWithoutSetup } from "@/stores/modules/user";
import { getRoutesMapByConfig } from "@/utils/routerUtils";
import { routesMap } from "@/router/maps";
import type { MenuOption } from "naive-ui";
import { useSvgIcon } from "@/hooks";

type JMenuOption = MenuOption & {
  parentKey?: string;
};

let isRouteInit = false;
const activeKey = ref<string | null>(null);
const activeMainKey = ref<string | null>(null);
const menu = ref([]);
const sideMenu: any = ref([]);
const menuParentKeysMap = {};
const subMenuMap = {};
export function useMenu(props: undefined | { isMainMenu: boolean } = { isMainMenu: false }) {
  const { SvgIconVNode } = useSvgIcon();
  const { activeRouteName } = useActiveRoute();
  const userStore = useUserStoreWithoutSetup();
  const router = useRouter();
  const route = useRoute()
  function renderRouterLink(route): () => VNode {
    return (): VNode =>
      h(
        RouterLink,
        {
          to: {
            name: route.name,
          },
        },
        { default: () => route.meta.title },
      );
  }

  function handleMainMeunClick(key: string) {
    const temp = subMenuMap[key];
    const pathArray = route.fullPath.split('/')
    if(`/${pathArray[1]}`.indexOf(routesMap[key].path) != -1 ){
      return
    }
    if (temp) {
      if (subMenuMap[temp[0].key] && !subMenuMap[temp[0].key]?.isNest) {
        handleMainMeunClick(temp[0].key);
      } else {
        
        router.push(routesMap[temp[0].key]);
      }
    } else{
      
      router.push(routesMap[key]);
    } 
  }


  function getMenuTreeNodeByKey(key,menuTree){
    let node = null;
    for(let i =0;i<menuTree.length;i++){
      if(menuTree[i].key === key){
        node = menuTree[i];
        break;
      }else if(menuTree[i].children && menuTree[i].children.length){
        node = getMenuTreeNodeByKey(key,menuTree[i].children);
        if(node){
          break;
        }
      }
    }
    return node;
  }


  function handleSideMenuClick(key: string) {
    const _sideMenu = getMenuTreeNodeByKey(key,sideMenu.value) 
    // const _sideMenu = sideMenu.value.find(item => item.key == key);
    if (_sideMenu.children && _sideMenu.children.length) {
      const displaySubMenu = _sideMenu.children.find(item => item.show !== false);
      if (routesMap[key]?.meta.isShow !== false && !displaySubMenu) {
        
        router.push(routesMap[_sideMenu.children[0].key]);
      }
    } else {
      if (routesMap[key]?.meta.isShow !== false) {
        
        router.push(routesMap[key]);
      }
    }
  }

  function renderMainMenusItem(route): () => VNode {
    return (): VNode =>
      h(
        "span",
        {
          onClick: () => {
            handleMainMeunClick(route.name);
          },
        },
        route.meta.title,
      );
  }

  function renderMenuItem(route, isSideMenu = false): () => VNode {
    return (): VNode =>
      h(
        "p",
        {
          class:'j-menu-item',
          style: "user-select:none;",
          onClick: isSideMenu
            ? () => {
                handleSideMenuClick(route.name);
              }
            : undefined,
        },
        route.meta.title,
      );
  }
  function renderSubMenuIcon(Icon: VNode, route): () => VNode {
    return (): VNode =>
      h(Icon, {
        onClick: () => {
          handleSideMenuClick(route.name);
        },
      });
  }
  function createMenuByRoutesConfig(routesConfig, parentsKey?: string): Array<JMenuOption> {
    const _menu: Array<JMenuOption> = [];

    routesConfig.forEach(route => {
      if (route.meta.isMenu || route.name === RoutesName.Root) {
        const _menuItem: JMenuOption = {
          key: route.name,
        };
        if (parentsKey) {
          _menuItem["parentKey"] = parentsKey;
          menuParentKeysMap[route.name] = parentsKey;
        }
        if (route.meta.hasOwnProperty("isShow")) {
          _menuItem.show = route.meta.isShow;
        }
        if (isArray(route.children) && route.children.length) {
          if (parentsKey === RoutesName.Root) {
            // _menuItem.label = renderMainMenusItem(route)
            _menuItem.label = renderMenuItem(route);
            createMenuByRoutesConfig(route.children, route.name);
          } else {
            _menuItem.label = renderMenuItem(route, true);
            // route.meta.title
            if (route.meta.icon) _menuItem.icon = SvgIconVNode({ localIcon: route.meta.icon, fontSize: 22 });
            _menuItem.children = createMenuByRoutesConfig(route.children, route.name);
          }
        } else {
          if (route.meta.icon) _menuItem.icon = SvgIconVNode({ localIcon: route.meta.icon, fontSize: 22 });
          // _menuItem.label = renderRouterLink(route)
          _menuItem.label = renderMenuItem(route);
        }
        if (!route.meta.isMenu && isArray(_menuItem.children) && _menuItem.children.length) {
          _menuItem.children.forEach(item => _menu.push(item));
        } else _menu.push(_menuItem);
      }
    });
    if (parentsKey) {
      subMenuMap[parentsKey] = _menu;
    }
    return _menu;
  }
  function findParentsKey(key: string) {
    let topKey = "";
    if (menuParentKeysMap[key] !== RoutesName.Root) {
      topKey = findParentsKey(menuParentKeysMap[key]);
    } else topKey = key;
    return topKey;
  }

  function filterMainMenuConfigByKey(activeKey: string) {
    const topKey = findParentsKey(activeKey);
    const _sideMenu = subMenuMap[topKey] ? subMenuMap[topKey].filter(item => item.show !== false) : [];
    if (_sideMenu.length && _sideMenu.children && _sideMenu.children.filter(item => item.show).length) {
      _sideMenu.children = _sideMenu.children.filter(item => item.show);
    } else {
      delete _sideMenu["children"];
    }
    sideMenu.value = _sideMenu;
    activeMainKey.value = topKey;
  }

  watch(
    activeRouteName,
    (newVal, oldVal) => {
      activeKey.value = newVal;
      if (isRouteInit) {
        filterMainMenuConfigByKey(newVal);
      }
    },
    {
      immediate: true,
    },
  );

  watch(
    userStore.routeConfig,
    (newVal, oldVal) => {
      const _routes = getRoutesMapByConfig(newVal, routesMap);
      menu.value = createMenuByRoutesConfig(_routes);
      isRouteInit = true;
      filterMainMenuConfigByKey(activeKey.value);
    },
    {
      immediate: true,
    },
  );
  if (props.isMainMenu) {
    watch(activeMainKey, newVal => {
      handleMainMeunClick(newVal);
    });
  } else {
    watch(activeKey, (newVal,oldval) => {
      if (routesMap[newVal]?.meta.isShow !== false && route.name != newVal) {
        
        router.push(routesMap[newVal]);
      }
    });
  }
  return {
    menu,
    sideMenu,
    activeMainKey,
    activeKey,
  };
}
