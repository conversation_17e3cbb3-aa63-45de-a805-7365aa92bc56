<template>
    <div class="wrapper">
        <transition appear name="fade-slide" mode="out-in">
          <component 
            :is="currentPage" 
            :toggleActive="toggleActive" 
            :isPointEnable="isPointEnable"
            @update:checked="handleChecked"
          />  
        </transition>
    </div>
</template>

<script lang="ts" setup name='MemberIntegral'>
import { ref, computed, watch } from "vue";
import { MemberPointsEnum } from "./types";
import { useTriggerPointsShop } from "./hooks";
/** 相关组件 */
import IntroduceIntegral from "./components/IntroduceIntegral/index.vue";
import IntegralMall from "./components/IntegralMall/index.vue";

const { getPointsShop, isPointEnable, setPointsSystem } = useTriggerPointsShop();

const activeTypeRef = ref(null);

/** 相关组件 */
const pageMap = {
  [MemberPointsEnum.INTRODUCTION]: IntroduceIntegral,
  [MemberPointsEnum.CONFIGURATION]: IntegralMall,
}

/** 切换page */
function toggleActive() {
  // 切换 activeTypeRef 的值
  activeTypeRef.value = activeTypeRef.value === MemberPointsEnum.INTRODUCTION
    ? MemberPointsEnum.CONFIGURATION
    : MemberPointsEnum.INTRODUCTION;
}

/** 当前页 */
const currentPage = computed(() => pageMap[activeTypeRef.value]);

/** 切换积分商城使用  */
async function handleChecked(checked: boolean) {
  await setPointsSystem(checked);
}

/** 监听 */
watch(() => isPointEnable.value, (newVal) => {
  activeTypeRef.value = newVal ? MemberPointsEnum.CONFIGURATION : MemberPointsEnum.INTRODUCTION;
}, {
  immediate: true
});

/** 初始化 */
function init() {
  getPointsShop();
}

init();
</script>


<style lang="less" scoped>
.wrapper {
    width: 100%;
    height: 100%;
}
</style>