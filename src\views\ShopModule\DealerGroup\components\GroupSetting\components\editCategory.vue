<template>
  <n-modal
    @after-leave="closeModal"
    v-model:show="isShow"
    style="width: 30%"
    :auto-focus="false"
    class="custom-card"
    preset="card"
    size="small"
    :bordered="false"
    :title="`${optType}${typeMap[type]}`"
  >
    <n-form
      ref="formRef"
      :model="modal"
      :rules="rules"
      label-width="auto"
      label-placement="left"
      require-mark-placement="right-hanging"
      size="small"
      :style="{
        width: '100%',
      }"
    >
      <n-form-item :label="`${typeMap[type]}名称`" path="name">
        <n-input style="width: 380px" v-model:value="modal.name" placeholder="请输入" clearable />
      </n-form-item>
    </n-form>
    <template #footer>
      <n-space justify="end">
        <n-button size="small" @click="isShow = false">取消</n-button>
        <n-button size="small" type="primary" :disabled="isLoading" :loading="isLoading" @click="_save">保存</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script lang="ts" setup name="editInfoModal">
import { ref, computed, watch } from "vue";
import { useMessages } from "@/hooks/useMessage";
import type { OptType, TypeN } from "../types";
import {  addDealerGroup, updateDealerGroup } from "@/services/api";
interface EditInfoModalProps {
  optType: OptType;
  type: TypeN;
  show: boolean;
  editData?: any; // 编辑数据
}

const message = useMessages();

const props = withDefaults(defineProps<EditInfoModalProps>(), {
  show: false,
});
const emits = defineEmits<{
  "update:show": [value: boolean];
  refresh;
}>();

const isShow = computed({
  get: () => props.show,
  set: value => emits("update:show", value),
});

const typeMap: Record<TypeN, string> = {
  1: "大类",
  2: "分组",
};
/* 表单参数初始化 */
const initParams = {
  name: null,
};
/* 表单规则 */
const rules = {
  name: {
    required: true,
    trigger: ["blur", "input"],
    message: `请输入名称`,
  },
};

const modal = ref({ ...initParams });

/* 清空表单 */
const formDataReset = () => {
  modal.value = { ...initParams };
};

/* 关闭弹窗之后 */
const closeModal = () => {
  formDataReset();
};

const formRef = ref(null);

/** 保存 */
const isLoading = ref(false);
const _save = async e => {
  const api = props.optType == "编辑" ? updateDealerGroup : addDealerGroup;
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      try {
        isLoading.value = true;
        const _params: any = {
          name: modal.value.name,
          type: props.type,
        };

        if (props.optType === "编辑") {
          _params.id = props.editData?.id;
        }
        if (props.optType === "新增" && props.type === 2) {
          _params.cateId = props.editData?.cateId;
        }
        await api({ data: _params });
        message.createMessageSuccess(`${props.optType}成功！`);
        emits("refresh");
        isShow.value = false;
      } catch (error) {
        message.createMessageError(`${props.optType}失败！:${error}`);
      } finally {
        isLoading.value = false;
      }
    }
  });
};
watch(
  () => props.show,
  newVal => {
    if (newVal && props.optType === "编辑") {
      modal.value.name = props.editData.name;
    }
  },
);
</script>

<style lang="less" scoped></style>
