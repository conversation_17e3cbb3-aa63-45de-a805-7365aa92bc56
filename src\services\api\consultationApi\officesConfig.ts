import {defHttp} from '@/services';
/** 科室配置 */
export const enum OfficesConfigApi {
    /** 科室列表 */
  departmentList = '/departmentConfig/listDepartments',
  /** 新增一级科室 */
  addDepartment = '/departmentConfig/addHeadDepartment',
  /** 编辑科室 */
  editDepartment = '/departmentConfig/updateDepartment',
  /** 删除科室 */
  deleteDepartment = '/departmentConfig/deleteDepartment',
  /** 新增二级科室 */
  addSecondDepartment = '/departmentConfig/addSubDepartment',
}

/** 科室列表 */
export function departmentList() {
  return defHttp.post({
    url: OfficesConfigApi.departmentList
  })
}

/** 新增一级科室 */
export function addDepartment(params) {
  return defHttp.post({
    url: OfficesConfigApi.addDepartment,
    params
  })
}

/** 编辑科室 */
export function editDepartment(params) {
  return defHttp.put({
    url: OfficesConfigApi.editDepartment,
    params
  })
}

/** 删除科室 */
export function deleteDepartment(params) {
  return defHttp.delete({
    url: OfficesConfigApi.deleteDepartment,
    requestConfig:{
      isQueryParams:true
    },
    params
  })
}

/** 新增二级科室 */
export function addSecondDepartment(params) {
  return defHttp.post({
    url: OfficesConfigApi.addSecondDepartment,
    params
  })
}
