import { ref } from "vue";
import { useMessages } from "@/hooks";
import { couponProductCatePage } from "@/services/api";
import { deepClone } from "@/utils";
import { WELFARE_GOOD_CLASSIFY_ALL } from "../type";

const message = useMessages();

export default function useGoodsClassify() {
    const isGetLoading = ref(false);
    const searchValue = ref('');
    /** 当前SelectedKeys */
    const selectedKeys = ref<Array<string>>([WELFARE_GOOD_CLASSIFY_ALL]);

    /** 分页总数 */
    let recordsTotal = 1;
    
    /** 商品分类搜索参数 */
    const _params = {
    	data: {
    		name: searchValue.value,
    	},
    	pageVO: {
    		current: 1,
    		size: 300,
    	},
    };

    /** 标签数据初始化 */
    const initgoodsData: Array<{ 
        key: string; // 标签标识
        label: string; // 标签名
        isMenu: boolean;
    }> = [
        {
            key: WELFARE_GOOD_CLASSIFY_ALL,
            label: '全部',
            isMenu: false,
        }
    ];

    /** 标签数据 */
    const goodsOptions = ref(deepClone(initgoodsData));

    /** 处理数据 */
    const handleData = (dataList) => {
        let tempDataList = [];
        dataList.forEach(item => {
            tempDataList.push({
                key: item.id,
                label: item.name,
                imageUrl: item.imageUrl,
                sort: item.sort,
                isMenu: true,
            });
        });
        return tempDataList;
    };

    /** 获取商品分类数据 */
    const getClassifyData = async (callBack?: () => void) => {
        try {
    		isGetLoading.value = true;
            _params.data.name = searchValue.value;
            const { total, current, size, records } = await couponProductCatePage(_params);
		    _params.pageVO.current = Number(current);
		    _params.pageVO.size = Number(size);
		    recordsTotal = Number(total);
            if (_params.pageVO.current == 1) {
                goodsOptions.value = handleData(records);
                initgoodsData.forEach(item => {
                    goodsOptions.value.unshift(item);
                });
                // 初始化回调
                callBack && callBack();
            } else {
                // 分页处理
                let tempList = handleData(records);
                tempList.forEach(item => {
                    goodsOptions.value.push(item);
                });
            }
    	} catch (err) {
    		message.createMessageError("分类列表失败: " + err);
    	} finally {
    		isGetLoading.value = false;
    	}
    };

    /** 滚动加载 */
    const handleScroll = (e) => {
    	const currentTarget = e.currentTarget as HTMLElement;
    	if (currentTarget.scrollTop + currentTarget.offsetHeight >= currentTarget.scrollHeight) {
    		if (_params.pageVO.current * _params.pageVO.size < recordsTotal) {
    			_params.pageVO.current++;
    			getClassifyData();
    		}
    	}
    };

    return {
        isGetLoading,
        searchValue,
        getClassifyData,
        handleScroll,
        goodsOptions,
        selectedKeys,
        _params
    }
};