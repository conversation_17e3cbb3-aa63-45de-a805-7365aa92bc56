<template>
  <n-select
    v-bind="$attrs"
    clearable
    :render-option="renderOption"
    :render-tag="props.displayQuantity !== 0 ? renderTag : undefined"
    filterable
  >
    <template #empty>
      <div class="empty-wrapper">
        <img :src="EmptySrc" alt="" />
        <p>暂无数据</p>
      </div>
    </template>
    <template #header>
      <slot name="header"></slot>
    </template>
    <template #action>
      <slot name="action"></slot>
    </template>
  </n-select>
</template>

<script lang="tsx" setup>
import { toRefs, h, type VNode,getCurrentInstance } from "vue";
import { NTag } from "naive-ui";
import type { SelectOption } from "naive-ui/es/select";
import { NTooltip } from "naive-ui/es/tooltip";
/** 静态资源 */
import EmptySrc from "@/assets/image/exception/empty.png";

defineOptions({
  name: "JSelect",
});

/** props */
const props = withDefaults(
  defineProps<{
    clearable?: boolean;
    displayQuantity?:number;
    closable?: boolean;
    isShowTooltip?: boolean;
  }>(),
  {
    clearable: true,
    closable: true,
    displayQuantity:0,
    isShowTooltip: true,
  }
);

const { clearable: clearableRef, closable: closableRef  } = toRefs(props);
const renderOption = ({ node, option }: { node: VNode; option: SelectOption }): VNode => {
  return props.isShowTooltip
    ? h(NTooltip, {
        delay: 10,
        duration: 10,
        placement: 'right',
        style: { wordBreak: 'break-all' }
      }, {
        trigger: () => node,
        default: () => option.label
      })
    : node;
};

const renderTag = ({ option, handleClose }) => {
  const { vnode } = getCurrentInstance(); // 获取当前组件实例
  const parent = vnode.el; // 获取父元素
  const itemGiEl = parent?.querySelector(".n-base-selection-tags");
  const itemGiWidth = itemGiEl ? itemGiEl.offsetWidth  : 0; //获取输入框宽度
    return h(
    NTag,
    {
      size: "small",
      style: { maxWidth: `${(itemGiWidth / (props.displayQuantity + 1)) }px` },
      closable: closableRef.value,
      onClose: (e) => {
            e.stopPropagation();
            handleClose();
      }
    },
    { default: () => option.label }
   );
 
};
</script>

<style scoped lang="less">
.empty-wrapper {
  img {
    height: 50px;
  }
  p {
    color: #666;
    font-size: 12px;
    text-align: center;
  }
}
</style>
