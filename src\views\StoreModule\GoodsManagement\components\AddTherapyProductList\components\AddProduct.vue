<template>
	<JModal
		v-model:show="show"
    :title="title"
		width="680"
    @after-leave="handleAfterLeave"
    @close-modal="handleClose"
		@positive-click="_save"
		:positiveButtonProps="{
			loading: isLoading
		}"
	>
    <n-form
      ref="formRef"
      :model="model"
      :rules="rules"
      label-placement="left"
      label-width="auto"
      require-mark-placement="right-hanging"
      :style="{ width: '100%' }"
    >
        <!-- 产品名称 -->
        <n-form-item label="产品名称" path="name">
            <n-input 
              v-model:value="model.name" 
              placeholder="请输入产品名称" 
              :maxlength="60"
              clearable
             />
        </n-form-item>
        <!-- 服用方式 -->
        <n-form-item label="服用方式" path="dosage">
            <n-input 
              v-model:value="model.dosage" 
              placeholder="请输入服用方式" 
              :maxlength="60"
              clearable
             />
        </n-form-item>
        <!-- 产品图片 -->
        <n-form-item label="产品图片" path="imgPath">
          <div style="display: flex;flex-direction: column;margin-bottom: 8px;">
            <UploadProductImg 
               v-model:value="model.imgPath" 
               :maxFileSize="0.5"
               accept="image/*" 
               :fileListSize="1" 
               :max="1"
            />
            <span>注：图片需小于500K，支持png、jpg、JPEG、GIF格式。<br /> 建议尺寸：</span>
          </div>
        </n-form-item>
    </n-form>
	</JModal>
</template>

<script setup lang="tsx" name="AddProduct">
import { ref, computed } from "vue";
import { isObject, deepClone } from "@/utils";
import type { FormRules } from 'naive-ui';
import { useMessages } from '@/hooks';
/** Api */
import { addTherapyGoods, updateTherapyGoods } from "@/services/api";

interface ModalProps {
  type: 'add' | 'edit';
  row?: any;
  refresh?: () => void; // 刷新表格
}

const { createMessageSuccess, createMessageError } = useMessages();

/** 标题 */
const title = computed(() => {
  const titleMap: Record<'add' | 'edit', string> = {
    add: '产品新增',
    edit: '产品编辑',
  };
  return titleMap[type.value];
});

/** emits */
const emits = defineEmits<{
	(e: 'update:value', value: Array<any>): void;
	(e: 'checkAdd', value: object): void;
  (e: 'addSucceed', row: Partial<{
    dosage: string;
    id: string;
    imgPath: string;
    name: string;
  }>): void;
}>();

const show = ref(false);
const modalProps = ref<ModalProps>({
    type: 'add',
});

/** 初始化 */
const initParams = {
    id: null,
    name: null,
    dosage: null,
    imgPath: []
};

/** 参数 */
const model = ref(deepClone(initParams));

const type = ref<'add' | 'edit'>('add');

/* 接收父组件传过来的参数 */
const acceptParams = (params: ModalProps) => {
	modalProps.value = { ...params };
  type.value = params.type;
    // 处理行数据
	if (isObject(params.row) && Object.keys(params.row).length !== 0) {
      let row = params.row;
      model.value.id = row?.id ?? null;
      model.value.name = row?.name ?? null;
      model.value.dosage = row?.dosage ?? null;
      model.value.imgPath = [{ path: row?.imgPath }] ?? [];
    }
	show.value = true;
};

/** 表单实例 */
const formRef = ref();

/* 表单规则 */
const rules: FormRules = {
    name: {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入产品名称',
    },
    dosage: {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入服用方式',
    },
    imgPath: {
      type: 'array',
      required: true,
      trigger: ['blur', 'change'],
      message: '请选择产品图片',
    },
};

/** 弹窗关闭回调 */
const handleAfterLeave = () => {
    model.value = deepClone(initParams);
};

/** 关闭 */
function handleClose() {
	show.value = false;
}

/** 获取参数 */
const getParams = (): {
  name: string;
  imgPath: string;
  dosage: string;
} => {
  const { name, imgPath, dosage } = model.value;
  return {
    name,
    imgPath: imgPath[0]?.path ?? '',
    dosage
  }
};

/** 保存 */
const isLoading = ref(false);
const _save = (e: MouseEvent) => {
    e.preventDefault();
    formRef.value?.validate(async (errors: any) => {
        if (!errors) {
          try {
            isLoading.value = true;
            // 新增
            if (modalProps.value.type === 'add') {
              const data = await addTherapyGoods(getParams());
              if (isObject(data)) {
                emits("addSucceed", data);
                createMessageSuccess("产品新建成功");
              }
            }
            // 修改
            if (modalProps.value.type === 'edit') {
              await updateTherapyGoods({
                id: model.value.id,
                ...getParams(),
              });
              createMessageSuccess("产品修改成功");
            }
            // 刷新
            show.value = false;
            modalProps.value?.refresh();
          } catch (error) {
            createMessageError("失败：" + error);
          } finally {
            isLoading.value = false;
          }
        }
    });
};

defineExpose({
	acceptParams,
});
</script>

<style lang="less" scoped></style>
