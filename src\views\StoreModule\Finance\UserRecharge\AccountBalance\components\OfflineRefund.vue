<template>
  <JModal
    v-model:show="show"
    width="580"
    :title="title"
    @after-leave="closeModal"
    @positive-click="_save"
    :positiveButtonProps="{
      loading: isLoading,
    }"
  >
    <n-form
      ref="formRef"
      :rules="rules"
      :model="model"
      label-width="150"
      label-placement="left"
      require-mark-placement="right-hanging"
      :style="{
          width: '100%',
        }"
    >
      <n-grid :cols="8" :x-gap="24">
        <n-form-item-gi :span="24" label="退款金额（元）" path="refundAmount">
          <n-input-number
            style="width: 100%"
            v-model:value="model.refundAmount"
            placeholder="请输入退款金额"
            :min="0"
            :max="10000000"
            :show-button="false"
          />
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="备注" path="remarks">
          <n-input v-model:value="model.remarks" type="text" placeholder="请输入备注" maxlength="30" />
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </JModal>
</template>

<script setup lang="ts" name="OfflineRefund">
import { ref } from "vue";
import { useMessages } from "@/hooks";
import { offlineRefund } from "@/services/api/financialApi/userRecharge";
import { FinanceBusinessType } from "@/enums";
import { useUnitConversion } from "../../hooks/unitConversion";

const { toFen, toYuan, verifyDecimalPoint } = useUnitConversion();

const initParams = {
  accountUserId: "",
  remarks: "",
  refundAmount: null,
  businessType: FinanceBusinessType.OfflineRefund,
};
const title = ref("线下退款（扣减用户账户余额）");
const model = ref({ ...initParams });
const props = ref<any>({});
const accountBalance = ref(0);
/* 提示信息 */
const { createMessageSuccess, createMessageError } = useMessages();
/* 模态框显隐状态 */
const show = ref(false);
/* 表单规则 */
const rules = {
  refundAmount: {
    required: true,
    trigger: ["blur", "change"],
    validator(rule, value) {
      if (!value) {
        return new Error("请输入退款金额");
      }
      if (Number(value) > accountBalance.value) {
        return new Error("退款金额不能大于账户余额");
      } else if (!verifyDecimalPoint(value)) {
        return new Error("只支持小数点后两位");
      }
      return true;
    },
  },
  remarks: {
    required: false,
    trigger: ["blur", "change"],
    message: "请输入备注",
  },
};
const acceptParams = ({ row, refreshTable }) => {
  show.value = true;
  model.value.accountUserId = row?.accountUserId ?? "";
  //分转换为元
  accountBalance.value = toYuan(row?.accountBalance ?? "0");
  props.value.refresh = refreshTable;
};
/* 表单实例 */
const formRef = ref(null);
/* 表单参数初始化 */
/* 清空表单 */
const formDataReset = () => {
  model.value = { ...initParams };
};
/* 关闭弹窗 */
const closeModal = () => {
  formDataReset();
  isLoading.value = false;
  // 弹窗取消
  show.value = false;
};
/* 确认--保存 */
const isLoading = ref(false);
const _save = (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors && !isLoading.value) {
      try {
        isLoading.value = true;
        const params = {
          data: {
            ...model.value,
            refundAmount: toFen(model.value?.refundAmount),
          },
        };
        await offlineRefund(params);
        createMessageSuccess(`退款成功`);
        // 刷新表格数据
        props.value.refresh();
        closeModal();
      } catch (e) {
        createMessageError(`退款失败：${e}`);
        isLoading.value = false;
      }
    }
  });
};
defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less"></style>
