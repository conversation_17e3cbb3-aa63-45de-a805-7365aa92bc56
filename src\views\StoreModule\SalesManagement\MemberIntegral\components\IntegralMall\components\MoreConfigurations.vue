<template>
  <JDrawer
    v-model:show="show"
    title="更多配置"
    :isGetLoading="isGetLoading"
    @after-leave="handleAfterLeave"
    @after-enter="handleAfterEnter"
    :to="props.to"
  >
    <!-- 基本信息 -->
    <template #content>
      <n-form
        ref="formRef"
        :model="model"
        :rules="rules"
        label-placement="left"
        label-width="200"
        require-mark-placement="right-hanging"
        size="small"
        :style="{ width: '100%' }"
      >
        <!-- 可兑换日期 -->
        <n-form-item-gi :span="10" label="小程序端积分商城首页展示：" path="isPres">
          <div style="display: flex;flex-direction: column;">
            <n-checkbox-group v-model:value="model.pointProductConfig" @update:value="_update('checkbox','',$event)">
              <n-flex>
                <n-checkbox value="sto_point_rule_status" label="积分规则" />
                <n-checkbox value="sto_point_detail_status" label="积分明细" />
                <n-checkbox value="sto_point_exchange_record_status" label="兑换记录" />
              </n-flex>
            </n-checkbox-group>
          </div>
        </n-form-item-gi>
        <!-- 可兑换日期 -->
        <n-form-item-gi :span="10" label="可兑换日期：" path="isPres">
          <div style="display: flex;flex-direction: column;margin-bottom: 8px;">
            <n-radio-group v-model:value="model.sto_point_exchange_date_mode" name="redemptionDate" @update:value="_update('str','sto_point_exchange_date_mode')">
              <n-space>
                <n-radio v-for="item in redemptionDateOptions" :key="item.value" :value="item.value">
                  {{ item.label }}
                </n-radio>
              </n-space>
            </n-radio-group>
            <span style="margin-top: 4px;font-size: 14px;">会员兑换日期目前仅在小程序端积分商城和社群端小黄车下单生效，选择扣除后，如果账户余额不足，会员会兑换失败</span>
          </div>
        </n-form-item-gi>
        <!-- 限定时间段 -->
        <n-form-item-gi :span="12" label="限定时间段">
          <n-flex>
            <JDateRangePicker
              v-model:value="model.rangeTime"
              :width="320"
              type="datetimerange"
              format="YYYY-MM-DD HH:mm:ss"
              :default-time="['00:00:00', '23:59:59']"
              :clearable="false"
            />
            <JTextButton v-if="model.activityTimes.length < 10" type="primary" text size="small" @click="_update('add_time')">添加</JTextButton>
          </n-flex>
        </n-form-item-gi>
        <n-form-item-gi :span="12" :show-label="false">
          <n-flex vertical>
            <template v-for="(item,index) in model.activityTimes" :key="index">
              <div v-if="!item?.isDeleted" style="display: flex;align-items: center;margin-left: 200px;font-size: 14px;">
                <span style="margin-right: 6px;">
                  {{ dayjs(item['startTime']).format('YYYY年MM月DD HH:mm:ss') }} ~
                  {{ dayjs(item['endTime']).format('YYYY年MM月DD HH:mm:ss') }}
                </span>
                <JTextButton type="error" text size="small" @click="_update('del_time','',item)">删除</JTextButton>
              </div>
            </template>
          </n-flex>
        </n-form-item-gi>
        <!-- 可兑换日期 -->
        <n-form-item-gi :span="10" label="小程序端积分商城用户可见：" path="isPres">
          <div style="display: flex;flex-direction: column;">
            <n-radio-group v-model:value="model.sto_point_visible_scope" name="pointsMallVisible" @update:value="_update('str','sto_point_visible_scope')">
              <n-space>
                <n-radio v-for="item in pointsMallVisibleTypeOptions" :key="item.value" :value="item.value">
                  {{ item.label }}
                </n-radio>
              </n-space>
            </n-radio-group>
            <span style="margin-top: 4px;font-size: 14px;">选择社群经销商商品需用到门店相关功能：</span>
            <span style="margin-top: 4px;font-size: 14px;">1.商品编辑界面设置门店可见</span>
            <span style="margin-top: 4px;font-size: 14px;">2.组织架构界面设置门店绑定社群经销商</span>
          </div>
        </n-form-item-gi>
        <!-- 可兑换日期 -->
        <n-form-item-gi :span="10" label="商品成本扣除：" path="isPres">
          <div style="display: flex;flex-direction: column;">
            <n-radio-group v-model:value="model.sto_point_cost_deduct_mode" name="deductionOfCommodityCost"  @update:value="_update('str','sto_point_cost_deduct_mode')">
              <n-space>
                <n-radio v-for="item in deductionOfCommodityCostTypeOptions" :key="item.value" :value="item.value">
                  {{ item.label }}
                </n-radio>
              </n-space>
            </n-radio-group>
            <span style="margin-top: 4px;font-size: 14px;">1.选择扣除后，如果账户余额不足，会员会兑换失败</span>
            <span style="margin-top: 4px;font-size: 14px;">2.此项配置对小程序端和社群端下的订单有效</span>
          </div>
        </n-form-item-gi>
      </n-form>
    </template>
  </JDrawer>
</template>

<script lang="tsx" setup name="MoreConfigurations">
import { ref} from "vue";
import {
  updatePointProductConfig,
  addTime,
  deleteTime, getPointProductConfig,
} from "@/services/api";
import { useMessages, useLoading } from '@/hooks';
import { deepClone, isArray} from "@/utils";
/** 相关组件 */
import JDrawer from "@/components/JDrawer/index.vue";
import JTextButton from "@/components/JTextButton/index.vue";
import { redemptionDateOptions,
  pointsMallVisibleTypeOptions,
  deductionOfCommodityCostTypeOptions } from "@/constants";
import dayjs from "dayjs";
import { hasOverlap } from "@/views/StoreModule/GoodsManagement/utils";

const { createMessageSuccess, createMessageError } = useMessages();

/** props */
const props = withDefaults(defineProps<{
  to?: string; // 弹窗位置
  refreshTable?: () => void; // 刷新表格数据
}>(), {
  to: '#integral-mall'
});

/** 显隐 */
const show = ref(false);
/** 接收父组件参数 */
const acceptParams = async () => {
  show.value = true;
  try {
    startLoading();
    const data = await getPointProductConfig({});
    console.log(data);
    data?.pageDTO?.records.forEach(e=>{
      switch (e.key) {
        case 'sto_point_rule_status':
          if (e.value == '0'){
            let index = model.value.pointProductConfig.indexOf('sto_point_rule_status')
            if (index != -1) {
              model.value.pointProductConfig.splice(index, 1);
            }
          }
          break
        case 'sto_point_detail_status':
          if (e.value == '0'){
            let index = model.value.pointProductConfig.indexOf('sto_point_detail_status')
            if (index != -1) {
              model.value.pointProductConfig.splice(index, 1);
            }
          }
          break
        case 'sto_point_exchange_record_status':
          if (e.value == '0'){
            let index = model.value.pointProductConfig.indexOf('sto_point_exchange_record_status')
            if (index != -1) {
              model.value.pointProductConfig.splice(index, 1);
            }
          }
          break
        default:
          model.value[e.key] = Number(e.value)
      }
    })
    model.value.activityTimes = data?.activityTimes.map(e=>{
      return {
        id:e.id,
        startTime:dayjs(e.startTime),
        endTime:dayjs(e.endTime),
      }
    })
    model.value.activityTimes.sort((a,b)=>{
      return dayjs(a.startTime) - dayjs(b.startTime)
    })
  } catch (error) {
    createMessageError("获取会员等级失败：" + error);
  } finally {
    endLoading();
  }
};
const initParams = {
  activityTimes:[],
  pointProductConfig:[
    'sto_point_rule_status',
    'sto_point_detail_status',
    'sto_point_exchange_record_status',
  ],
  sto_point_exchange_date_mode:0,
  sto_point_visible_scope:0,
  sto_point_cost_deduct_mode:0,
  rangeTime: null
}
const model = ref(deepClone(initParams))
const pointProductConfig = ref([
  'sto_point_rule_status',
  'sto_point_detail_status',
  'sto_point_exchange_record_status',
])
/** 抽屉出现后的回调 */
const { loading: isGetLoading, startLoading, endLoading } = useLoading(false);
/* 确认--保存 */
const { loading: isLoading, startLoading: startSaveLoading, endLoading: endSaveLoading } = useLoading(false);
const _update = async (type?:string,str?:string,obj?:any) => {
  const { rangeTime, activityTimes } = model.value;
  const temp = model.value
  try {
    startSaveLoading();
    switch (type) {
      case 'add_time':
        if (isArray(rangeTime)) {
          const [startTime, endTime] = rangeTime;
          let timeObj = await addTime({
            startTime:dayjs(startTime).format('YYYY-MM-DD HH:mm:ss'),
            endTime:dayjs(endTime).format('YYYY-MM-DD HH:mm:ss'),
          });
          model.value.activityTimes.push(timeObj);
          model.value.activityTimes.sort((a,b)=>{
            return dayjs(a.startTime) - dayjs(b.startTime)
          })
        } else {
          createMessageError("请选择时间段！");
        }
        break
      case 'del_time':
        await deleteTime({
          id:obj.id
        });
        model.value.activityTimes = model.value.activityTimes.filter(item => item?.id !== obj.id);
        model.value.activityTimes.sort((a,b)=>{
          return a.startTime - b.startTime
        })
        break
      case 'checkbox':
        if (pointProductConfig.value.length > obj.length){
          await updatePointProductConfig({
            groupName:'pointProductConfig',
            key:arrayDifference(pointProductConfig.value,obj)[0],
            value:0
          });
        }else {
          await updatePointProductConfig({
            groupName:'pointProductConfig',
            key:arrayDifference(obj,pointProductConfig.value)[0],
            value:1
          });
        }
        pointProductConfig.value = obj
        break
      default:
        await updatePointProductConfig({
          groupName:'pointProductConfig',
          key:str,
          value:model.value[str]
        });
    }
    createMessageSuccess("更新成功！");
  } catch (error) {
    createMessageError("更新失败：" + error);
    model.value = temp
  } finally {
    endSaveLoading();
  }
};
function arrayDifference(arr1, arr2) {
  const set2 = new Set(arr2);
  return arr1.filter(item => !set2.has(item));
}
defineExpose({
  acceptParams,
});
</script>

<style lang="less" scoped>
@import "../styles/index.less";

:deep(.n-scrollbar-rail) {
  bottom: 8px !important;
}

.footer-wrapper {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: end;
}

.default-rule-wrapper {
  width: 98%;
}

.custom-rule-wrapper {
  width: 98%;
  position: relative;
  .add {
    position: absolute;
    top: -38px;
    right: 0;
    font-size: 16px;
  }
}
</style>
