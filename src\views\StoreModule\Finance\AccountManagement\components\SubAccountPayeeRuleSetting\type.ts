/** 分账规则 */
export const enum SubAccountPayeeRule {
  /** 不分账 */
  NO_SUB_ACCOUNT = 0,
  /** 按商品分账 */
  SUB_ACCOUNT_BY_GOODS = 1,
  /** 按订单分账 */
  SUB_ACCOUNT_BY_ORDER = 2,
}

/** 分账规则设置 */
export const enum SubAccountPayeeRuleSetting {
  /** 分账规则 */
  subAccountPayeeRule = 'sto_allocation_calculation_rule',
  /** 分账比例 */
  subAccountPayeeRatio = 'sto_allocation_ratio',
  /** 分账结算时间点 */
  subAccountPayeeSettlementTime = 'sto_allocation_action_time',
}
