<template>
  <div
      class="radio-select"
      @click="toggleSelect"
  >
    <div
        v-if="!props.isSelected"
        class="radio-no-select"
    />
    <!--    <NIcon-->
    <!--        v-else-->
    <!--        :size="20">-->
    <!--      <RadioIcon/>-->
    <!--    </NIcon>-->
    <NIcon size="20">
      <SvgIcon localIcon="IMRadio"/>
    </NIcon>
  </div>
</template>

<script lang="ts" setup>
import SvgIcon from "@/components/SvgIcon/index.vue";

interface IProps {
  isSelected: boolean;
}

interface IEmits {
  (e: 'onChange', value: boolean): void;
}

const emits = defineEmits<IEmits>();
const props = withDefaults(defineProps<IProps>(),
    {},
);

function toggleSelect() {
  emits('onChange', !props.isSelected);
}
</script>
<style lang="less" scoped>
:not(not) {
  display: flex;
  flex-direction: column;
  min-width: 0;
  box-sizing: border-box
}

.radio-select {
  flex: 1;
  flex-direction: column;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
  justify-content: center;

  .radio-no-select {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    border: 2px solid #ddd;
  }
}
</style>
