<template>
  <n-cascader
    v-model:value="regionValue"
    placeholder="请选择所在地区"
    :options="regionOptions"
    check-strategy="child"
    remote
    clearable
    :onFocus="handleFocus"
    :on-load="handleLoad"
    @update:value="handleUpdateValue"
  >
    <template #empty>
      <div class="empty-wrapper">
        <img :src="EmptySrc" alt="" />
        <p>暂无数据</p>
      </div>
    </template>
  </n-cascader>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import type { CascaderOption } from "naive-ui";
import EmptySrc from "@/assets/image/exception/empty.png";
import { addressData } from "@/services/api";

defineOptions({ name: 'JAreaSelect' });

/* Props */
const props = withDefaults(
	defineProps<{
		value?: {
      province: string;
      provinceId: string;
      cityId: string;
      cityName: string;
      area: string;
      areaId: string;
    };
	}>(),{},
);

/** emits */
const emits = defineEmits<{
  (e: 'update:value', value: {
    province: string;
    provinceId: string;
    cityId: string;
    cityName: string;
    area: string;
    areaId: string;
  }): void;
}>();

/** 选中的值 */
const regionValue = ref(null);

/** 地址 */
const regionOptions = ref<any>([]);

/** 获取省份列表 */
const getProvinceList = async (_params: {
  code: number | string,
  cateType: number
}) => {
  const res = await addressData(_params);
  if (res) {
    let list = res.map(item => {
      return {
        label: item.name,
        value: item.id,
        id: item.id,
        code: item.code,
        cateType: item.cateType,
        depth: 1,
        isLeaf: false
      }
    });
    return list;
  }
};

/** 获取焦点回调 */
async function handleFocus() {
  if (!regionOptions.value.length) {
    regionOptions.value = await getProvinceList({
      code: 0,
      cateType: 1
    });
  }
}

/** 获取城市列表 */
const getCityList = async (id: string | number, _params: {
  code: number | string,
  cateType: number
}) => {
  const res = await addressData(_params);
  const list: CascaderOption[] = [];
  for (let i = 0; i < res.length; i++) {
    list.push({
      label: res[i].name,
      value: `${id}-${res[i].id}`,
      id: res[i].id,
      code: res[i].code,
      cateType: res[i].cateType,
      depth: 2,
      isLeaf: false
    })
  }
  return list;
};

/** 获取区县列表 */
async function getCountyList(id: string | number, _params: {
  code: number | string,
  cateType: number
}) {
  const res = await addressData(_params);
  const list: CascaderOption[] = [];
  for (let i = 0; i < res.length; i++) {
    list.push({
      label: res[i].name,
      value: `${id}-${res[i].id}`,
      id: res[i].id,
      code: res[i].code,
      cateType: res[i].cateType,
      depth: 3,
      isLeaf: true
    })
  }
  return list;
}

/** 在点击未加载完成节点时的回调，在返回的 promise 中设定 option.children，在返回的 promise resolve 或 reject 之后完成加载 */
const handleLoad = (option: CascaderOption & { code: number, cateType: number }) => {
  return new Promise<void>(async (resolve, reject) => {
    let list = [];
    switch (option.depth) {
      case 1:
        list = await getCityList(option.value, {
          code: option?.code,
          cateType: 2,
        });
        break;
      case 2:
        list = await getCountyList(option.value, {
          code: option?.code,
          cateType: 3,
        });
        break;
      default:
        break;
    }
    if (list.length > 0) {
      option.children = list;
      resolve()
    } else {
      reject()
    }
  });
};

/** 值更新回调 */
function handleUpdateValue(
  value: string | number | Array<string | number> | null,
  option: CascaderOption | Array<CascaderOption | null> | null,
  pathValues: Array<CascaderOption | null> | Array<CascaderOption[] | null> | null
) {
  let params = {
    province: null,
    provinceId: null,
    cityId: null,
    cityName: null,
    area: null,
    areaId: null,
  };

  if (Array.isArray(pathValues)) {
    pathValues.forEach(item => {
      switch (item.depth) {
        case 1:
          params['province'] = item.label;
          params['provinceId'] = item.id;
          break;
        case 2:
          params['cityName'] = item.label;
          params['cityId'] = item.id;
          break;
        case 3:
          params['area'] = item.label;
          params['areaId'] = item.id;
          break;
      }
    });
  }

  emits('update:value', params);
}

/** 监听 */
watch(
  () => props.value,
  async (newVal) => {
    if (newVal.provinceId && newVal.cityId  && newVal.areaId  && regionOptions.value?.length === 0) {
      const { provinceId, cityId, areaId } = newVal;
      regionValue.value = `${provinceId}-${cityId}-${areaId}`;
      try {
        const provinceData = await getProvinceList({
          code: 0,
          cateType: 1
        });
        regionOptions.value = [...provinceData];

        if (cityId) {
          for (const item of regionOptions.value) {
            if (item.id === provinceId) {
              await handleLoad(item);
              if (areaId) {
                for (const child of item['children']) {
                  if (child.id === cityId) {
                    await handleLoad(child);
                  }
                }
              }
            }
          }
        }
      } catch (error) {
        console.error('Error fetching region data:', error);
      }
    }
  },
  { immediate: true },
);
</script>

<style scoped lang="less">
.empty-wrapper {
  img {
    height: 50px;
  }

  p {
    color: #666;
    font-size: 12px;
    text-align: center;
  }
}
</style>
