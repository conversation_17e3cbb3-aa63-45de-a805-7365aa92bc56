import { getPoolLink } from "@/services/api/pool";

export async function createPoolLink(link) {
    const urlPattern = /state=([^&]+)/;
    const match = link.match(urlPattern);
    if (match && match[1]) {
        const state = match[1];
        const params = {
            scene:6,
            dealerId:0,
            groupId:0,
            state
        }

        const resp = await getPoolLink(params,link)
        return resp

    } 
    else {
        throw new Error('State parameter not found in the URL');
    }
}
