<template>
  <JDrawer
    v-model:show="drawerVisible"
    title="编辑客户"
    to=".table-wrapper"
    :isGetLoading="loadShow"
    @after-leave="closeDrawer"
    :contents-list="[
      {
        name: '基本信息',
        slotName: 'basic_info'
      },
      {
        name: '社群会员关联',
        slotName: 'relevancy'
      }
    ]"
  >
    <!-- 基本信息 -->
    <template #basic_info>
      <n-form
        ref="formRef"
        :model="model"
        :rules="rules"
        label-width="90"
        label-placement="left"
        require-mark-placement="right-hanging"
        size="small"
        :style="{
          width: '100%'
        }"
      >
        <n-grid cols="8 m:16 l:16 xl:24" :x-gap="32" responsive="screen">
          <!-- 第一列 -->
          <n-gi :span="8">
            <!-- 用户头像 -->
            <n-form-item-gi :span="8" label="用户头像">
              <JImage :img-path="model.img" />
            </n-form-item-gi>
            <!-- 昵称 -->
            <n-form-item-gi :span="8" label="昵称">
                {{model?.nickname ? model.nickname : '-'}}
            </n-form-item-gi>
            <!-- 手机号 -->
            <n-form-item-gi :span="8" label="手机号">
              <n-input-group v-if="model.mobile">
                <n-input v-model:value="model.mobile" readonly style="width: 86%;" />
                <n-button v-copy="model.mobile">复制</n-button>
              </n-input-group>
              <span v-else>-</span>
            </n-form-item-gi>
            <!-- 客户编号 -->
            <n-form-item-gi :span="8" label="客户编号">
              <n-input-group>
                <n-input v-model:value="model.code" placeholder="暂无客户编号" style="width: 86%;" />
                <n-button v-copy="model.code">复制</n-button>
              </n-input-group>
            </n-form-item-gi>
            <!-- 积分总额 -->
            <n-form-item-gi :span="8" label="积分总额">
              <n-input-group>
                <n-input v-model:value="model.totalPoints" readonly placeholder="暂无积分总额"/>
              </n-input-group>
            </n-form-item-gi>
            <!-- 积分余额 -->
            <n-form-item-gi :span="8" label="积分余额">
              <n-input-group>
                <n-input v-model:value="model.availPoints" readonly placeholder="暂无积分余额"/>
              </n-input-group>
            </n-form-item-gi>
            <!-- 积分明细 -->
            <n-form-item-gi :span="8" label="积分明细">
              <JTextButton size="small" type="primary" @click="handleViewDetails()">
                查看详情
              </JTextButton>
            </n-form-item-gi>
          </n-gi>
          <!-- 第二列 -->
          <n-gi :span="8">
            <n-form-item-gi :span="8" label="UNION ID" >
              <n-input-group v-if="model.unionId">
                <n-input v-model:value="model.unionId" readonly style="width: 86%;" />
                <n-button v-copy="model.unionId">复制</n-button>
              </n-input-group>
              <span v-else>-</span>
            </n-form-item-gi>
            <n-form-item-gi :span="8"  label="OPEN ID"  >
              <n-input-group v-if="model.openId">
                <n-input v-model:value="model.openId" readonly style="width: 86%;" />
                <n-button v-copy="model.openId">复制</n-button>
              </n-input-group>
              <span v-else>-</span>
            </n-form-item-gi>
          </n-gi>
          <!-- 客户标签 -->
          <n-gi :span="24">
            <n-form-item-gi :span="24"  label="标签"  class="checkbox_group">
              <n-checkbox-group 
                v-model:value="labelCheckbox"
                v-for="(item,index) in labelCheckboxList" 
                :key="index"
                size="medium"
                style="width: 184px;box-sizing: border-box;"
              >
                <div 
                  style="
                    width: 168px;
                    box-sizing: border-box;
                    margin-bottom: 12px;
                    box-shadow: rgba(0, 0, 0, 0.05) 0px 0px 0px 1px;
                  "
                >
                  <n-checkbox 
                    :value="item.tagId" 
                    :checked="true"
                    @click="clickCheckbox(item.customerId,item.tagId,item.id)"
                    style="width: 82%;padding: 4px 12px;"
                  >
                    <n-ellipsis style="max-width: 112px">{{ item.tagName }}</n-ellipsis>
                  </n-checkbox>
                </div>
              </n-checkbox-group>
            </n-form-item-gi>
          </n-gi>
        </n-grid>
      </n-form>
    </template>
    <!-- 社群会员关联 -->
    <template #relevancy>
      <n-grid :cols="24" :x-gap="24" responsive="self">
        <n-gi :span="24" style="margin-right: 5px;">
          <FormLayout
            class="inner-page-height"
            :tableData="tableData"
            :tableColumns="tableColumns"
            :isNeedCollapse="false"
	          :is-table-selection="false"
            :is-table-pagination="false"
            :is-display-header="false"
	          style="height: 520px;"
          ></FormLayout>
        </n-gi>
      </n-grid>
    </template>
    <!-- footer -->
    <template #footer>
      <div class="footer-wrapper">
        <n-space>
          <n-button ghost @click="closeDrawer" class="store-button">取 消</n-button>
          <n-button type="info" :loading="isLoading" @click="_save" class="store-button">
            保 存
          </n-button>
        </n-space>
      </div>
    </template>
  </JDrawer>  
  <IntegralDetailModel ref='IntegralDetailModelShow' :Operator='true'/>
</template>
  
<script setup lang="ts" name="drawerEditCustomer">
import { ref } from "vue";
import { useMessages } from "@/hooks";
import {customerEntityUpdate , pointRecordPagePointsRecord} from '@/services/api';
import FormLayout from "@/layout/FormLayout.vue";
import JImage from "@/components/JImage/index.vue";
import IntegralDetailModel from './IntegralDetailModel.vue'

interface DrawerEditCustomer {
  show?:boolean,
  related?:boolean,//是否已关联
};

const isLoading = ref(false);

/* 表单参数初始化 */
const initParams = {
  createTime:null,
  id:null,
  img:null,
  lastThirdCourseName:null,
  mobile:null,
  nickname:null,
  openId:null,
  status:null,
  tagList:null,
  unionId:null,
  updateTime:null,
  code:null,
  totalPoints:null,
  availPoints:null
};

const model = ref<{
  createTime:string,
  id:string,
  img:string,
  lastThirdCourseName:string,
  mobile:string,
  nickname:string,
  openId:string,
  status:string,
  unionId:string,
  updateTime:string,
  code:string,
  totalPoints:string
  availPoints:string
}>({ ...initParams });
const addLabelCheckbox = ref([])
const deleteLabelCheckbox = ref([])
const clickCheckbox = (customerId,tagId,id) =>{

    labelCheckbox.value.filter(item => item !== tagId);
    if(deleteLabelCheckbox.value.includes(id)){
      deleteLabelCheckbox.value.splice(deleteLabelCheckbox.value.indexOf(id), 1)
      return deleteLabelCheckbox.value
    }

    if(addLabelCheckbox.value.includes(tagId)){
      addLabelCheckbox.value.splice(addLabelCheckbox.value.indexOf(tagId), 1)
      return addLabelCheckbox.value
    }

    //存储删除的标签
    if(!storageNotSelected.value.includes(tagId)){
      deleteLabelCheckbox.value.push(id)
    }
     //新增标签
    if(!storageSelection.value.includes(tagId)){
      
      addLabelCheckbox.value.push(tagId)
    }
}

/** 标签 */
const labelCheckbox = ref([])
const labelCheckboxList = ref([])
const loadShow = ref(false)

/* 表单规则 */
const rules = {};

/* 表格方法数据 */
const tableData = ref([]) 

/** Props */
const props = withDefaults(defineProps<DrawerEditCustomer>(), {
    related:false
});

  /* 表格项 */
  const tableColumns = [
    {
      title: "序号",
      width: 80,
      fixed: "left",
      key: "index",
      align: "center",
      render: (renderData: object, index: number) => {
        return `${index + 1}`;
      },
    },
    {
      title: "会员昵称",
      key: "thirdMemberNickname",
      align: "left",
      fixed: "left",
      width: 100,
      render: rowData => {
        return rowData.thirdMemberNickname ? rowData.thirdMemberNickname : '-'
      }
    },
    {
      title: "所属经销商",
      key: "gmDealerName",
      align: "left",
      fixed: "left",
      width: 100,
      render: rowData => {
        return rowData.gmDealerName ? rowData.gmDealerName : '-'
      }
    },
    {
      title: "所属群管",
      key: "gmMgrName",
      align: "left",
      width: 100,
      render: rowData => {
        return rowData.gmMgrName ? rowData.gmMgrName : '-'
      }
    },
    {
      title: "训练营",
      key: "campEntityName",
      align: "left",
      fixed: "left",
      width: 100,
      render: rowData => {
        return rowData.campEntityName ? rowData.campEntityName : '-'
      }
    },
    {
      title: "公众号",
      key: "wxappEntityName",
      align: "left",
      width: 100,
      render: rowData => {
        return rowData.wxappEntityName ? rowData.wxappEntityName : '-'
      }
    },
];
  
  const { createMessageSuccess, createMessageError } = useMessages();

  /** 抽屉状态 */
  const drawerVisible = ref(false);
  const drawerProps = ref();
  
  /* 接收父组件传过来的参数 */
  const acceptParams = (params) => {
    drawerProps.value = params;
    drawerVisible.value = true;
    customerEntityGet()
    customerEntityPage()
  };

  /** 存储选中的标签 */
  const storageSelection = ref([])
  /** 存储未选择的标签 */
  const storageNotSelected = ref([])
  const customerEntityGet = async() =>{
    loadShow.value = true
    try{
      const customerEntity = await drawerProps.value.getInfoApi({ csId:drawerProps.value.row.id });
      const  {id,img,mobile,nickname,openId,unionId,tagList,code,totalPoints,availPoints} = customerEntity
      model.value.img = img
      model.value.mobile = mobile
      model.value.nickname = nickname
      model.value.openId = openId
      model.value.unionId = unionId
      model.value.id = id
      model.value.code = code
      model.value.totalPoints = totalPoints
      model.value.availPoints = availPoints
      tagList?.map((item)=>{
        if(item.customerId != undefined){
          labelCheckbox.value.push(item.tagId)
          storageSelection.value.push(item.tagId)
        }else{
          storageNotSelected.value.push(item.tagId)
        }
      })
      
      labelCheckboxList.value = tagList
      loadShow.value = false
    }catch(err){
      createMessageError(err || '获取详情数据失败') 
    }
  }

  /** 社群关联 */
  const customerEntityPage = async()=>{
    try{
      const customerEntity = await drawerProps.value.socialCommunityApi({ csId:drawerProps.value.row.id });
      tableData.value = customerEntity
    }catch(err){
      createMessageError(err || '获取社群关联失败') 
    }
  }
  
  /** 关闭抽屉 */
  const closeDrawer = () => {
    drawerVisible.value = false;
    labelCheckbox.value = []
    storageSelection.value = []
    storageNotSelected.value = []
    addLabelCheckbox.value = []
    deleteLabelCheckbox.value = []
    tableData.value = []
  };
  
  /** 保存 */
  const _save = async() =>{
    const params = {
      csId: drawerProps.value.row.id || null,
      code: model.value.code || null,
      addTags:addLabelCheckbox.value.join(',') || null,
      delCsTags:deleteLabelCheckbox.value.join(',') || null,
    }
    isLoading.value = true
    try{
      await customerEntityUpdate(params)
      createMessageSuccess("修改成功")
      drawerProps.value?.refresh()
      drawerVisible.value = false;
    }catch(err){
      createMessageError(err || '修改失败')
    }finally{
      isLoading.value = false
    }
  }

  /** 查看详情 */
  const IntegralDetailModelShow = ref()
  const handleViewDetails = () =>{
    const _params = {
      unionId:model.value.unionId,
      customerId:drawerProps.value.row.id,
      getInfoApi: pointRecordPagePointsRecord,
    };
    IntegralDetailModelShow.value.acceptParams(_params)
  }
  
  
  defineExpose({
    acceptParams,
    closeDrawer,
    drawerVisible
  });
  </script>
  
<style scoped lang="less">
@import "@/styles/default.less";
@import "@/styles/defaultVar.less";

:deep(.n-drawer-body-content-wrapper){
  padding: 0 !important;
  overflow: hidden;
}
:deep(.checkbox_group .n-form-item-blank){
  flex-flow:row wrap
}
.footer-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
  