<template>
  <JModal
    v-model:show="show"
    title="规则预览"
    width="680"
    @after-leave="closeModal"
    :is-scale="false"
    :positive-text="null" 
    :negative-text="null"
  >
    <div class="wrapper">
      <n-scrollbar style="height: 500px;">
        <div v-for="(item, index) in props.previewContent" :key="item.id" class="content">
          <span class="title">{{ numberToChinese(index + 1) }}、{{ item.title }}</span>
          <template v-if="isObject(item.content)">
            <n-ol align-text>
              <n-li v-for="(value, key) in item.content">{{ key }}、{{ value }}</n-li>
            </n-ol>
          </template>
          <template v-else>
            <p>{{ item.content }}</p>
          </template>
        </div>
      </n-scrollbar>
    </div>
  </JModal>
</template>

<script setup lang="tsx" name="JRulePreview">
import { ref } from "vue";
import { isObject, numberToChinese } from "@/utils";
/** 相关组件 */
import JModal from "@/components/JModal/index.vue";

/** props */
const props = defineProps<{
  previewContent: Array<{
    id: string;
    title: string;
    content?: Record<number, string> | string;
  }>;
}>();

/** 弹窗显隐 */
const show = ref(false);

/* 接收父组件传过来的参数 */
const acceptParams = () => {
  show.value = true;
};

/* 关闭弹窗之后 */
const closeModal = () => {

};

defineExpose({
    acceptParams,
});
</script>

<style scoped lang="less">
.wrapper {
  width: 100%;
  height: 100%;
  .title {
    font-size: 16px;
    font-weight: 600;
  }
}
:deep(.n-scrollbar > .n-scrollbar-container > .n-scrollbar-content) {
	padding-right: 18px;
}
</style>
