import { defHttp } from "@/services";

/** 福利券分类 */
export const enum welfareTicketClassApi {
  addCouponCate = "/couponCategory/addCouponCate",
  updateCouponCate = "/couponCategory/updateCouponCate",
  getCouponCateById = "/couponCategory/getCouponCateById",
  batchDeleteCouponCate ="/couponCategory/batchDeleteCouponCate",
  list ="/couponCategory/list",
  pageCouponCate ="/couponCategory/pageCouponCate",
  
}

/**
 * @description 新建福利券分类
 */
export function addCouponCate(params) {
    return defHttp.post({
        url: welfareTicketClassApi.addCouponCate,
        params,
    });
}

/**
 * @description 修改福利券分类
 */
export function updateCouponCate(params) {
    return defHttp.put({
        url: welfareTicketClassApi.updateCouponCate,
        params,
    });
}

/**
 * @description 根据Id查询福利券分类
 */
export function getCouponCateById(id: string) {
    return defHttp.get({
        url: welfareTicketClassApi.getCouponCateById + `?id=${id}`
    });
}

/**
 * @description 根据ID批量删除福利券分类
 */
export function batchDeleteCouponCate(params) {
    return defHttp.delete({
        url: welfareTicketClassApi.batchDeleteCouponCate,
        params,
        requestConfig: {
            isQueryParams: true,
        },
    });
}

/**
 * @description 查询福利券分类
 */
export function pageCouponCate(params) {
    return defHttp.post({
        url: welfareTicketClassApi.pageCouponCate,
        params,
    });
}

/** 福利券管理 */
export const enum welfareTicketManageApi {
    addCouponBatch = "/couponBatch/add",
    updateCouponBatch = "/couponBatch/update",
    updateValidDate = "/couponBatch/updateValidDate",
    pageCouponBatch = "/couponBatch/page",
    sendHandle = "/couponBatch/sendHandle",
    deleteCouponBatch = "/couponBatch/delete",
    pageByCouponBatchId = "/couponReceiveRecord/pageByCouponBatchId",
    syncCouponBatchStatus = "/liveActivity/syncCouponBatchStatus",
    
}

/**
 * @description 添加直播福利券
 */
export function addCouponBatch(params) {
    return defHttp.post({
        url: welfareTicketManageApi.addCouponBatch,
        params,
    });
}

/**
 * @description 更新直播福利券
 */
export function updateCouponBatch(params) {
    return defHttp.put({
        url: welfareTicketManageApi.updateCouponBatch,
        params,
    });
}

/**
 * @description 修改福利券有效期
 */
export function updateValidDate(data:{id:string,validUntil:string}) {
    return defHttp.put({
        url: welfareTicketManageApi.updateValidDate,
        params:{
            data
        },
    });
}

/**
 * @description 福利券发放/停止发放
 * @param sendEnable  1-开启发放 0-停止发放
 */
export function sendHandle(data:{id:string,sendEnable:0|1}) {
    return defHttp.post({
        url: welfareTicketManageApi.sendHandle,
        params:{
            data
        },
    });
}

/**
 * @description 分页查询福利券管理
 */
export function pageCouponBatch(params) {
    return defHttp.post({
        url: welfareTicketManageApi.pageCouponBatch,
        params,
    });
}

/**
 * @description 删除福利券
 */
export function deleteCouponBatch(id:string) {
    return defHttp.delete({
        url: welfareTicketManageApi.deleteCouponBatch + `?id=${id}`,
        requestConfig: {
            isQueryParams: true,
        },
    }); 
}

/**
 * @description 领取记录
 */
export function pageByCouponBatchId(params) {
    return defHttp.post({
        url: welfareTicketManageApi.pageByCouponBatchId,
        params
    }); 
}

/**
 * @description 同步卡券工具状态
 */
export function syncCouponBatchStatus(id:string) {
    return defHttp.get({
        url: welfareTicketManageApi.syncCouponBatchStatus + `?id=${id}`,
    }); 
}

/** 商城-福利券管理 */
export const enum storeWelfareTicketManageApi {
    updateValidDateForPc = "/couponBatch/updateValidDateForPc",
    pagePCCouponBatch = "/couponBatch/pageForPc",
    pagePCByCouponBatchId = "/couponBatch/pageByCouponBatchId",
    sendHandleForPc = "/couponBatch/sendHandleForPc",
}

/**
 * @description 修改福利券有效期
 */
export function updateValidDateForPc(data:{id:string,validUntil:string}) {
    return defHttp.put({
        url: storeWelfareTicketManageApi.updateValidDateForPc,
        params:{
            data
        },
    });
}

/**
 * @description 商城-领取记录
 */
export function pagePCByCouponBatchId(params) {
    return defHttp.post({
        url: storeWelfareTicketManageApi.pagePCByCouponBatchId,
        params
    }); 
}

/**
 * @description 商城-分页查询福利券管理
 */
export function pagePCCouponBatch(params) {
    return defHttp.post({
        url: storeWelfareTicketManageApi.pagePCCouponBatch,
        params,
    });
}

/**
 * @description 福利券发放/停止发放
 * @param sendEnable  1-开启发放 0-停止发放
 */
export function sendHandleForPc(data:{id:string,sendEnable:0|1}) {
    return defHttp.post({
        url: storeWelfareTicketManageApi.sendHandleForPc,
        params:{
            data
        },
    });
}