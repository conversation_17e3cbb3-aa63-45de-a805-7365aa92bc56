@font-face {
  font-family: "BebasNeue";
  src: url("@/assets/fonts/BebasNeue.otf");
  font-weight: normal;
  font-style: normal;
}
// @font-face {
//   font-family: "SourceHanSansCN";
//   src: url("@/assets/fonts/SourceHanSansCN-Medium.otf");
//   font-weight: 500;
//   font-style: normal;
// }
// @font-face {
//   font-family: "SourceHanSansCN";
//   src: url("@/assets/fonts/SourceHanSansCN-Medium.otf");
//   font-weight: bold;
//   font-style: normal;
// }
// @font-face {
//   font-family: "SourceHanSansCN";
//   src: url("@/assets/fonts/SourceHanSansCN-Regular.otf");
//   font-weight: normal;
//   font-style: normal;
// }
@font-face {
  font-family: "SourceHanSansCN";
  font-display: swap;
  src:
      url("@/assets/fonts/SourceHanSansCN-Medium.woff2") format('woff2'),
      url("@/assets/fonts/SourceHanSansCN-Medium.otf") format('opentype');
  font-weight: 500;
  font-style: normal;
}
@font-face {
  font-family: "SourceHanSansCN";
  font-display: swap;
  src: 
      url("@/assets/fonts/SourceHanSansCN-Medium.woff2") format('woff2'),
      url("@/assets/fonts/SourceHanSansCN-Medium.otf") format('opentype');
  font-weight: bold;
  font-style: normal;
}
@font-face {
  font-family: "SourceHanSansCN";
  font-display: swap;
  src: 
      url("@/assets/fonts/SourceHanSansCN-Regular.woff2") format('woff2'),
      url("@/assets/fonts/SourceHanSansCN-Regular.otf") format('opentype');
  font-weight: normal;
  font-style: normal;
}


