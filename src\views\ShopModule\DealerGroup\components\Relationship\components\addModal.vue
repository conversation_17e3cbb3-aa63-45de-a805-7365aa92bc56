<template>
  <n-modal
    preset="card"
    :show="modalVisible"
    :title="`分组关联经销商`"
    :style="{ width: '30%' }"
    :bordered="false"
    :auto-focus="false"
    size="small"
    :closable="true"
    @after-leave="closeModal"
    @close="modalVisible = false"
    @mask-click="modalVisible = false"
  >
    <n-form
      ref="formRef"
      :model="modal"
      :rules="rules"
      label-width="auto"
      label-placement="left"
      require-mark-placement="right-hanging"
      size="small"
      :style="{
        width: '100%',
      }"
    >
      <n-form-item label="大类名称">
        {{ modalState.cateName }}
      </n-form-item>
      <n-form-item label="分组名">
        {{ modalState.name }}
      </n-form-item>
      <n-form-item label="经销商" path="dealerIds">
        <JDealerSelect
          isUseSearch
          width="100%"
          :proportion="[0, '100%']"
          :groupCascade="false"
          :allOptionValue="1"
          v-model:value="modal.dealerIds"
          :isMultiple="true"
          placeholder="请选择经销商"
          filterable
          clearable
        ></JDealerSelect>
      </n-form-item>
    </n-form>
    <template #footer>
      <n-space justify="end">
        <n-button size="small" @click="modalVisible = false">取消</n-button>
        <n-button size="small" type="primary" :loading="isLoading" @click="handleSave">确定</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";
import { useMessages } from "@/hooks/useMessage";
import { affiliatedDealers } from "@/services/api";
import JDealerSelect from "./JDealerSelect.vue"
const message = useMessages();
const props = withDefaults(
  defineProps<{
    show: boolean;
    type: "新增" | "编辑";
    modalState: any;
  }>(),
  {
    show: false,
    type: "新增",
    modalState: () => {},
  },
);
const emits = defineEmits<{
  "update:show": [show: boolean];
  refresh: [];
}>();
const initParams = {
  dealerIds: [],
};
/* 表单规则 */
const rules = {
  dealerIds: {
    type: "array",
    required: true,
    trigger: ["blur", "change"],
    message: "请选择经销商",
  },
};
const modal = ref({ ...initParams });
/* 表单实例 */
const formRef = ref();
const modalVisible = computed({
  get: () => props.show,
  set: value => emits("update:show", value),
});
const isLoading = ref<boolean>(false);
/* 清空表单 */
const formDataReset = () => {
  modal.value = { ...initParams };
};

/* 关闭弹窗之后 */
const closeModal = () => {
  formDataReset();
};

const handleSave = (e: MouseEvent) => {
  console.log(props.modalState);

  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      try {
        const arr = modal.value.dealerIds.map(id => {
          return {
            groupId: props.modalState.id,
            dealerId: id,
          };
        });
        const params: any = {
          data: arr,
        };
        isLoading.value = true;
        await affiliatedDealers(params);
        message.createMessageSuccess(`关联成功`);
        emits("refresh");
        modalVisible.value = false;
      } catch (error) {
        message.createMessageError(`关联失败: ${error}`);
      } finally {
        isLoading.value = false;
      }
    }
  });
};

watch(
  () => props.show,
  (newVal, oldVal) => {
    if (newVal) {
      modal.value.dealerIds = props.modalState.dealerGroupRelateDTOList ? props.modalState.dealerGroupRelateDTOList.map(item => item.dealerId) : [];
    }
  },
);
</script>

<style scoped lang="less"></style>
