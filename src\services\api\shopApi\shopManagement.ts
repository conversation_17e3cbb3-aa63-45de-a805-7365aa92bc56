import { defHttp } from "@/services";

/** 门店管理 */
export const enum ShopManagementApi {
  page = "/storeEntity/pageListStoreInfo",
  list = "/structure/listStoreInfo",
  batchAdd = "/couponBatch/batchAdd",
  info = "/storeEntity/getStoreInfo",
  pageCommon = "/storeEntity/pageCommon",
}

/**
 * @description 门店信息分页
 */
export function getShopInfoPage(params) {
  return defHttp.post({
    url: ShopManagementApi.page,
    params,
  });
}
/**
 * @description 门店信息列表
 */
export function getShopInfoList(nameOrId) {
  return defHttp.get({
    url: ShopManagementApi.list + `?nameOrId=${nameOrId}`,
  });
}
/**
 * @description 门店信息-公共分页列表
 */
export function getShopPageCommon(params) {
  return defHttp.post({
    url: ShopManagementApi.pageCommon,
    params,
  });
}
/**
 * @description 批量添加福利券
 */
export function batchAddWelfareVoucher(params) {
  return defHttp.post({
    url: ShopManagementApi.batchAdd,
    params,
  });
}
/**
 * @description 获取门店信息
 */
export function getShopInfo(storeId) {
  return defHttp.get({
    url: ShopManagementApi.info + `?storeId=${storeId}`,
  });
}
