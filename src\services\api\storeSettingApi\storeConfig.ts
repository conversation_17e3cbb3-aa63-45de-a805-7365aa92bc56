import {defHttp} from '@/services';
import { basicPlatformUrl } from "@/utils/http/urlUtils";
// 商城配置
export const enum systemConfigApi {
  getParamsByGroupName = '/globalConfigs/pageParamsByGroupName', //根据分组名获取商城配置
  getDealConfig = '/globalConfigs/getDealConfig', //获取交易相关配置
  getDevConfig = '/globalConfigs/getDevConfig', //获取开发配置
  batchUpdateSysConfig = '/globalConfigs/updateSysConfig', //修改商城配置
  batchUpdateDealConfig = '/globalConfigs/updateDealConfig', //修改交易相关系统配置
  batchUpdateDevConfig = '/globalConfigs/updateDevConfig', //修改开发配置
  deleteStoRedis = '/globalConfigs/deleteStoRedis', // 开发配置-清理商城相关缓存（只提供给开发者账号使用）
  pageVideoConfigs = '/globalConfigs/pageVideoConfigs', // 获取视频配置
  updateVideoConfig = '/globalConfigs/updateVideoConfig', // 修改视频配置
  systemConfigOptionGetOptions = '/systemConfigOption/getOptions',//查询参数选项

}

// 首页Logo配置
export const enum homeLogoConfigApi {
  homeLogoGetLogo = '/homeLogo/getLogo', //查询首页logo配置
  homeLogoUpdate = '/homeLogo/update' //添加或修改首页logo
}

// 地址配置配置
export const enum returnAddressConfigApi {
  returnAddressList = '/customerAddress/returnAddress/list', //地址配置-退货地址列表
  returnAddressAdd = '/customerAddress/returnAddress/add', //地址配置-新增退货地址
  returnAddressUpdate = '/customerAddress/returnAddress/update', //地址配置-编辑退货地址
  returnAddressGet = '/customerAddress/returnAddress/get', //地址配置-退货地址详情
  returnAddressDelete = '/customerAddress/returnAddress/delete', //地址配置-删除退货地址
  addressData = "/addressEntity/listByCode"  //获取地址数据
}

/** 地址配置 (1.1.5版本更换接口) */ 
export const enum addressConfigApi {
  addressList = '/customerAddress/addressConfig/list', //地址配置-地址列表
  addressAdd = '/customerAddress/addressConfig/add', //地址配置-新增地址
  addressUpdate = '/customerAddress/addressConfig/update', //地址配置-编辑地址
  addressGet = '/customerAddress/addressConfig/get', //地址配置-地址详情
  addressDelete = '/customerAddress/addressConfig/delete', //地址配置-删除地址
  setDefaultAddress = '/customerAddress/businessDefaultAddr/update', //地址配置-设置默认地址
  getSupplierAddress = '/customerAddress/addressConfig/supplier',// 地址配置 - 获取供应商地址
}

// 轮播图配置
export const enum carouselImgApi {
  addCarouseParams = '/carouselImg/addCarouseParams',
  updateCarouseParams = '/carouselImg/updateCarouseParams',
  getCarouseParams = '/carouselImg/getCarouseParams',
  deleteCarouseParams = '/carouselImg/deleteCarouseParams',
  pageCarouseParams = '/carouselImg/pageCarouseParams',
  getViewCarouseParams = '/carouselImg/getViewCarouseParams'
}

// 导航栏设置
export const enum navigationConfigApi{
  addNavigationConfig ='/navigationConfig/addNavigationConfig', // 添加导航栏设置
  updateNavigationConfig = '/navigationConfig/updateNavigationConfig', // 修改导航栏设置
  getNavigationConfig = '/navigationConfig/getNavigationConfig', // 查询导航栏设置图片
  deleteNavigationConfig = '/navigationConfig/deleteNavigationConfig', // 删除导航栏设置
  listNavigationConfig = '/navigationConfig/listNavigationConfig', // 查询导航栏设置
}

// T9相关配置
export const enum JTConfigApi{
  startConfig ='/productJTIntegration/start/Config', // 开启t9对接配置项
  stopConfig = '/productJTIntegration/stop/Config', // 关闭t9对接配置项
}

//商品管理
export const enum JTMerchantApi{
  merchantAdd ='/merchant/add', // 新增商户号信息
  merchantUpdate = '/merchant/update', // 编辑商户号信息
  merchantPage = '/merchant/page', //分页查询列表
  merchantListFuiouEnabledMers = '/merchant/listFuiouEnabledMers' //获取富友已启用的商户
}

//业务流程
export const enum OperationFlowApi{
  getOperationFlowList ='/processDef/listByBizCode',
  addOperationFlow = '/processDef/add',
  updateOperationFlow = '/processDef/update',
  deleteOperationFlow = '/processDef/delete',
  operationFlowDetail = '/processDef/detail'
}


export function getParamsByGroupName(params) {
  return defHttp.post({
    url: systemConfigApi.getParamsByGroupName,
    params
  })
}

export function getDealConfig(params) {
  return defHttp.post({
    url: systemConfigApi.getDealConfig,
    params
  })
}

export function getDevConfig(params) {
  return defHttp.post({
    url: systemConfigApi.getDevConfig,
    params
  })
}

export function pageVideoConfigs(params) {
  return defHttp.post({
    url: systemConfigApi.pageVideoConfigs,
    params
  })
}

export function updateVideoConfig(params) {
  return defHttp.post({
    url: systemConfigApi.updateVideoConfig,
    params
  })
}

export function batchUpdateSysConfig(params) {
  return defHttp.post({
    url: systemConfigApi.batchUpdateSysConfig,
    params
  })
}

export function batchUpdateDealConfig(params) {
  return defHttp.post({
    url: systemConfigApi.batchUpdateDealConfig,
    params
  })
}

export function batchUpdateDevConfig(params) {
  return defHttp.post({
    url: systemConfigApi.batchUpdateDevConfig,
    params
  })
}

export function deleteStoRedis() {
  return defHttp.post({
    url: systemConfigApi.deleteStoRedis
  })
}

export function addCarouseParams(params) {
  return defHttp.post({
    url: carouselImgApi.addCarouseParams,
    params
  })
}

export function updateCarouseParams(params) {
  return defHttp.put({
    url: carouselImgApi.updateCarouseParams,
    params
  })
}

export function getCarouseParams(params) {
  return defHttp.get({
    url: carouselImgApi.getCarouseParams,
    params
  })
}

export function deleteCarouseParams(params: {
  id: number
}) {
  return defHttp.delete({
    url: carouselImgApi.deleteCarouseParams,
    requestConfig: {
      isQueryParams: true,
    },
    params
  })
}

export function pageCarouseParams(params) {
  return defHttp.post({
    url: carouselImgApi.pageCarouseParams,
    params
  })
}

export function getViewCarouseParams(params) {
  return defHttp.get({
    url: carouselImgApi.getViewCarouseParams,
    params
  })
}
export function homeLogoGetLogoParams(params) {
  return defHttp.get({
    url: homeLogoConfigApi.homeLogoGetLogo,
    params
  })
}
export function homeLogoUpdateParams(params) {
  return defHttp.put({
    url: homeLogoConfigApi.homeLogoUpdate,
    params
  })
}

export function returnAddressList(params) {
  return defHttp.post({
    url: `${returnAddressConfigApi.returnAddressList}?isActivated=${typeof params == 'string' ? params : ''}`,
  })
}

export function returnAddressAdd(params) {
  return defHttp.post({
    url: returnAddressConfigApi.returnAddressAdd,
    params
  })
}

export function returnAddressUpdate(params) {
  return defHttp.put({
    url: returnAddressConfigApi.returnAddressUpdate,
    params
  })
}

export function returnAddressGet(params) {
  return defHttp.get({
    url: `${returnAddressConfigApi.returnAddressGet}?id=${params}`,
  })
}

export function returnAddressDelete(params) {
  return defHttp.delete({
    url: returnAddressConfigApi.returnAddressDelete,
    requestConfig: {
      isQueryParams: true,
    },
    params
  })
}

/** 地址配置列表 */
export function addressList(params) {
  return defHttp.get({
    url: `${addressConfigApi.addressList}?type=${params.data.type}`,
  })
}

/** 获取供应商地址 */
export function getSupplierAddress(params) {
  return defHttp.get({
    url:`${ addressConfigApi.getSupplierAddress}?id=${params}`,
  })
}

/** 新增地址 */
export function addressAdd(params) {
  return defHttp.post({
    url:addressConfigApi.addressAdd,
    params
  })
}

/** 编辑地址 */
export function addressUpdate(params) {
  return defHttp.put({
    url:addressConfigApi.addressUpdate,
    params
  })
}

/** 删除地址 */
export function addressDelete(params) {
  return defHttp.delete({
    url:addressConfigApi.addressDelete,
    requestConfig: {
      isQueryParams: true,
    },
    params
  })
}

/** 设置默认地址 */
export function setDefaultAddress(params) {
  return defHttp.post({
    url:addressConfigApi.setDefaultAddress,
    params
  })
}

export function addNavigationConfig(params) {
  return defHttp.post({
    url:navigationConfigApi.addNavigationConfig,
    params
  })
}

export function updateNavigationConfig(params) {
  return defHttp.put({
    url:navigationConfigApi.updateNavigationConfig,
    params
  })
}

export function getNavigationConfig(params) {
  return defHttp.get({
    url:`${navigationConfigApi.getNavigationConfig}?id=${params}`
  })
}

export function deleteNavigationConfig(params) {
  return defHttp.delete({
    url:navigationConfigApi.deleteNavigationConfig,
    requestConfig: {
      isQueryParams: true,
    },
    params
  })
}

export function listNavigationConfig(params) {
  return defHttp.post({
    url:navigationConfigApi.listNavigationConfig,
    params
  })
}

export function startConfig() {
  return defHttp.get({
    url:JTConfigApi.startConfig,
  })
}

export function stopConfig() {
  return defHttp.get({
    url:JTConfigApi.stopConfig,
  })
}

export function merchantAdd(params) {
  return defHttp.post({
    url: JTMerchantApi.merchantAdd,
    params
  })
}

export function merchantUpdate(params) {
  return defHttp.put({
    url: JTMerchantApi.merchantUpdate,
    params
  })
}

export function merchantPage(params) {
  return defHttp.post({
    url: JTMerchantApi.merchantPage,
    params
  })
}

export function systemConfigOptionGetOptions(params){
  return defHttp.get({
    url:`${systemConfigApi.systemConfigOptionGetOptions}?configId=${params}`
  })
}

export function merchantListFuiouEnabledMers(payPlatForm){
  return defHttp.get({
    url:`${JTMerchantApi.merchantListFuiouEnabledMers}?payPlatForm=${payPlatForm}`
  })
}


// 获取地址数据
interface AddressDataParams {
  code: number | string,
  cateType: number
}
interface AddressData {
  cateType: number;
  code: string;
  createTime: string;
  id: string;
  name: string;
  parentCode: string;
  pinYin: string;
  updateTime: string;
  zipCode: string;
}
export async function addressData(params: AddressDataParams) {
  return defHttp.get<AddressData[]>({
    url: `${returnAddressConfigApi.addressData}?parentCode=${params.code}&cateType=${params.cateType}`,
    requestConfig: {
      withToken: false
    }
  })
}

export const enum DeliverToolApi{
  /** 获取中通快递管家功能启用状态 */
  getZtoStatus = '/globalConfigs/getZTOFunctionEnable',
  /** 更新中通快递管家功能启用状态 */
  updateZtoStatus = '/globalConfigs/updateZTOFunctionEnable',
  /** 获取中通快递管家配置参数 */
  getZtoParams = '/globalConfigs/getZTOSystemConfigs',
  /** 编辑中通快递管家配置参数 */
  updateZtoParams = '/globalConfigs/updateShippingToolConfig',
}


export interface GlobalConfigData {
  /** 配置名 */
  configName: string;
  /** 创建时间 */
  createTime: string;
  /** 描述 */
  desc: string;
  /** 分组名 */
  groupName: string;
  /** 主键 */
  id: string;
  /** 配置编码 */
  key: string;
  /** 数据类型。1=字符串；2=整型；3=整数范围；4=是否值；5=单选项；6=多选项；7=图片 */
  type: number;
  /** 更新者 */
  updateBy?: string;
  /** 修改时间 */
  updateTime?: string;
  /** 配置值：true=开启、false=关闭 */
  value?: boolean | string;
}

/* 获取中通快递管家功能启用状态 */
export function getZtoStatus():Promise<GlobalConfigData>{
  return defHttp.get({
    url:DeliverToolApi.getZtoStatus
  })
}
/* 更新中通快递管家功能启用状态 */
export function updateZtoStatus(params:GlobalConfigData){
  return defHttp.put({
    url:DeliverToolApi.updateZtoStatus,
    params:{data:params}
  })
}

export interface ZtoParams {
  /** 中通快递管家：应用名称 */
  appName?: string;
  /** 中通快递管家：AppKey */
  appKey?: string;
  /** 中通快递管家：AppSecret */
  appSecret?: string;
  /** 中通快递管家：ShopKey */
  shopKey?: string;
}
/* 获取中通快递管家配置参数 */
export function getZtoParams():Promise<ZtoParams>{
  return defHttp.get({
    url:DeliverToolApi.getZtoParams
  })
}
/* 编辑中通快递管家配置参数 */
export function updateZtoParams(params){
  return defHttp.put({
    url:DeliverToolApi.updateZtoParams,
    params
  })
}

export const enum DeliverToolExceptionApi{
  /** 获取发货异常记录 */
  getExceptionList = '/orderPushRecords/page',
  /** 标注为已处理 */
  batchMarkAsProcessed = '/orderPushRecords/markAsHandled',
  /** 重新推送 */
  batchResend = '/orderPushRecords/rePush',
  /** 导出订单 */
  exportOrder = '/orderPushRecords/exportRecords',
}

/* 获取发货异常记录 */
export function getExceptionList(params){
  return defHttp.post({
    url:DeliverToolExceptionApi.getExceptionList,
    params
  })
}

/* 标注为已处理 */
export function batchMarkAsProcessed(params:string[]){
  return defHttp.put({
    url:DeliverToolExceptionApi.batchMarkAsProcessed,
    params:{data:params}
  })
}

/* 重新推送 */
export function batchResend(params:string[]){
  return defHttp.post({
    url:DeliverToolExceptionApi.batchResend,
    params:{data:params}
  })
}

/* 导出订单 */
export function exportOrder(params){
  return defHttp.post({
    url:DeliverToolExceptionApi.exportOrder,
    requestConfig:{
      responeseType:'stream'
    },
    params,
  })
}

/* 获取业务流程列表 */
export function getOperationFlowList(params){
  return defHttp.get({
    url:OperationFlowApi.getOperationFlowList,
    params:{
      ...params?.data
    }
  })
}

/* 获取业务流程详情 */
export function getOperationFlowDetail(params){
  return defHttp.get({
    url:OperationFlowApi.operationFlowDetail,
    params
  })
}

/* 添加业务流程详情 */
export function addOperationFlow(params){
  return defHttp.post({
    url:OperationFlowApi.addOperationFlow,
    params:{data:params}
  })
}

/* 修改业务流程详情 */
export function updateOperationFlow(params){
  return defHttp.post({
    url:OperationFlowApi.updateOperationFlow,
    params:{data:params}
  })
}

/* 删除业务流程 */
export function deleteOperationFlow(params){
  return defHttp.delete({
    url:`${OperationFlowApi.deleteOperationFlow}`,
    requestConfig:{
      isQueryParams: true,
    },
    params
  })
}

