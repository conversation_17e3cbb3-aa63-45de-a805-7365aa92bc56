<template>
  <div class="logo-wrapper" v-if="!isLoading">
    <div v-if="hasCustomLogo" class="logo-head">
      <img v-if="model.logo" :src="model.logo" :alt="systemTitle" />
      <span v-if="model.title">{{ model.title }}</span>
    </div>
    <img v-else :src="logoSrc" :alt="systemTitle" />
  </div>
  
  <!-- 可选的加载状态占位 -->
  <div v-else class="logo-placeholder"></div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from "vue";
import logoSrc from "@/assets/image/system/logoSmall.png";
import { SystemSetting } from "@/settings/systemSetting";
import { homeLogoGetLogoParams } from "@/services/api";
import { transformMinioSrc } from "@/utils";

// 响应式数据
const isLoading = ref(true);
const model = ref({
  logo: '',
  title: null,
});

// 计算属性
const systemTitle = computed(() => SystemSetting.title);
const hasCustomLogo = computed(() => model.value.logo || model.value.title);

// 方法
const isHttp = (url: string): boolean => url?.startsWith("http");

const fetchLogoData = async () => {
  try {
    const response = await homeLogoGetLogoParams({});
    
    if (!response.isEnableName) {
      model.value.title = response.name;
    }
    
    if (!response.isEnableImg && response.imgPath) {
      model.value.logo = isHttp(response.imgPath) 
        ? response.imgPath 
        : transformMinioSrc(response.imgPath);
    }
  } catch (err) {
    console.error("获取开发配置失败：", err);
    // 出错时使用默认logo
    model.value.logo = '';
    model.value.title = null;
  } finally {
    isLoading.value = false;
  }
};

// 生命周期
onMounted(fetchLogoData);

defineOptions({ name: "SystemLogo" });
</script>

<style lang="less" scoped>
.logo-wrapper {
  display: flex;
  align-items: center;
  justify-content: start;
  transition: all 0.3s;
  
  img {
    height: 26px;
  }
  
  .logo-head {
    display: flex;
    align-items: center;
    
    img {
      height: 44px;
      margin-right: 8px;
    }
    
    span {
      color: black;
      font-weight: bolder;
      white-space: nowrap;
      line-height: 44px;
    }
  }
}

/* 可选的加载占位样式 */
.logo-placeholder {
  width: 120px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: start;
  border-radius: 4px;
}
</style>