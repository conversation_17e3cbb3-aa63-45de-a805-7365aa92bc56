import type {IMessageModel} from "@tencentcloud/chat-uikit-engine";
import {StoreName, TUIChatEngine, TUIStore} from "@tencentcloud/chat-uikit-engine";
import {getImSysConfig, getImUserSig, updateOnlineStatus} from "@/services/api/doctorEndApi";
import {useMessages} from "@/hooks";
import {useUserStore} from "@/stores/modules/user";
import JSONbig from 'json-bigint';
import {isDevEnv} from "@/utils/envUtils";

/** 用户 store */
const userStore = useUserStore();

interface IMConfig {
    SDKAppID: number | null,
    userID: string,
    userSig: string
}

//轮询5秒调用一次激活在线
let doctorOnlineStatusTimer;
const startUpdateDoctorOnlineStatus = (id: string) => {
    if (!id) {
        return
    }
    stopStreamRecordsDataPolling()
    doctorOnlineStatusTimer = setInterval(() => {
        updateOnlineStatus({id, onlineStatus: 1})
    }, 1000 * 3)
}
const stopStreamRecordsDataPolling = () => {
    if (doctorOnlineStatusTimer) {
        clearInterval(doctorOnlineStatusTimer)
        doctorOnlineStatusTimer = null
    }
}

// 页面刷新后重新登录
if (userStore.userInfo?.id && userStore.imConfig && userStore.imConfig.userSig) {
    TencentIMLogin(userStore.imConfig?.userID).then(() => {
        // 页面刷新后重新轮询更新登录状态
        // TODO 后期再考虑轮询
        // startUpdateDoctorOnlineStatus(userStore.userInfo?.id)
    })
}


/**
 *
 * @param imUserId // 账号昵称，IM登录的id
 * @param id // 账号id
 * @constructor
 */
export async function TencentIMLogin(id: string | null) {
    try {
        if (!id) {
            throw new Error('id为空')
        }
        const IMSysConfig = await getImSysConfig({});
        const IMUserSig = await getImUserSig({imUserId: id})
        userStore.setImConfig({
            SDKAppID: IMSysConfig.sdkAppId ?? userStore.imConfig?.SDKAppID ?? '',
            userID: id,
            userSig: IMUserSig ?? '',
        } as IMConfig)
        await doctorOnlineStatusChange(1)
    } catch (e) {
        const {createMessageError} = useMessages();
        createMessageError(`获取IM信息失败:${e}`)
        return
    }

    let TUIChatLoginPromise = TUIChatEngine.login({
        ...userStore.imConfig,
        useUploadPlugin: false, // 使用文件上传插件
    });
    TUIChatEngine.setLogLevel(isDevEnv() ? 0 : 4)
    TUIChatLoginPromise.then(() => {
        // TODO 后期再考虑轮询
        // startUpdateDoctorOnlineStatus(id)
    }).catch((e) => {
        const {createMessageError} = useMessages();
        createMessageError(`登录IM失败:${e},请退出登录后重试`)
    })
}

export async function TencentIMLogout(callback?: Function) {
    let TUIChatLogoutPromise = TUIChatEngine.destroy();
    TUIChatLogoutPromise.then((res) => {
        callback && callback()
    }).catch(e => {

    })
}

// 修改医生在线状态,退出登录 0:退出登录 1:登录 
export async function doctorOnlineStatusChange(onlineStatus: 0 | 1 = 0) {
    try {
        await updateOnlineStatus({id: userStore.imConfig?.userID ?? undefined, onlineStatus})
    } catch (e) {

    }
}

export async function handleDoctorLogout() {
    await doctorOnlineStatusChange(0)
    TencentIMLogout()
}

export function shallowCopyMessage(message: IMessageModel) {
    return Object.assign({}, message);
}

export const isUrl = (url: string) => {
    return /^(https?:\/\/(([a-zA-Z0-9]+-?)+[a-zA-Z0-9]+\.)+[a-zA-Z]+)(:\d+)?(\/.*)?(\?.*)?(#.*)?$/.test(
        url,
    );
};

// Determine if it is a JSON string
export const isJSON = (str: string) => {
    // eslint-disable-next-line no-useless-escape
    if (typeof str === 'string') {
        try {
            const data = JSON.parse(str);
            if (data) {
                return true;
            }
            return false;
        } catch (error: any) {
            return false;
        }
    }
    return false;
};

// Determine if it is a JSON string
export const JSONToObject = (str: string) => {
    if (!str || !isJSON(str)) {
        return str;
    }
    // 解决 Long 类型数据转换精度丢失问题
    str = str.replace(/:s*([0-9]{15,})s*(,?)/g, ': "$1" $2')
    return JSON.parse(str);
};

export const safeParse = (str) => {
    const parser = JSONbig({storeAsString: true});
    return parser.parse(str);
};

// calculate timestamp
export function calculateTimestamp(timestamp: number): string {
    const todayZero = new Date().setHours(0, 0, 0, 0);
    const thisYear = new Date(
        new Date().getFullYear(),
        0,
        1,
        0,
        0,
        0,
        0,
    ).getTime();
    const target = new Date(timestamp);

    const oneDay = 24 * 60 * 60 * 1000;
    const oneWeek = 7 * oneDay;

    const diff = todayZero - target.getTime();

    function formatNum(num: number): string {
        return num < 10 ? '0' + num : num.toString();
    }

    if (diff <= 0) {
        // today, only display hour:minute
        return `${formatNum(target.getHours())}:${formatNum(target.getMinutes())}`;
    } else if (diff <= oneDay) {
        // yesterday, display yesterday:hour:minute
        return `${'昨天'} ${formatNum(
            target.getHours(),
        )}:${formatNum(target.getMinutes())}`;
    } else if (diff <= oneWeek - oneDay) {
        // Within a week, display weekday hour:minute
        const weekdays = [
            '星期日',
            '星期一',
            '星期二',
            '星期三',
            '星期四',
            '星期五',
            '星期六',
        ];
        const weekday = weekdays[target.getDay()];
        return `${weekday} ${formatNum(
            target.getHours(),
        )}:${formatNum(target.getMinutes())}`;
    } else if (target.getTime() >= thisYear) {
        // Over a week, within this year, display mouth/day hour:minute
        return `${target.getMonth() + 1}/${target.getDate()} ${formatNum(
            target.getHours(),
        )}:${formatNum(target.getMinutes())}`;
    } else {
        // Not within this year, display year/mouth/day hour:minute
        return `${target.getFullYear()}/${
            target.getMonth() + 1
        }/${target.getDate()} ${formatNum(target.getHours())}:${formatNum(
            target.getMinutes(),
        )}`;
    }
}

/**
 * displayMessageReadReceipt: User-level control to display message read status
 * After turning off, the messages you send and receive will not have message read status
 * You will not be able to see whether the other party has read their messages, and they will also not be able to see whether you have read the messages they sent
 *
 * enabledMessageReadReceipt: App-level setting to enable read receipts
 * @return {boolean} - Returns a boolean value indicating if the message read receipt is enabled globally.
 */
export function isEnabledMessageReadReceiptGlobal(): boolean {
    return TUIStore.getData(StoreName.USER, 'displayMessageReadReceipt')
        && TUIStore.getData(StoreName.APP, 'enabledMessageReadReceipt');
}


export const parseTextToRenderArray = (text: string): Array<{
    type: 'text' | 'image';
    content: string;
    emojiKey?: string
}> => {
    const emojiRegex = /\[([^\]]+)\]/g;
    const result: any[] = [];

    let match: RegExpExecArray | null;
    let lastIndex = 0;

    while ((match = emojiRegex.exec(text)) !== null) {
        const startIndex = match.index;
        const endIndex = emojiRegex.lastIndex;
        const emojiKey = match[0];

        if (startIndex > lastIndex) {
            result.push({type: 'text', content: text.substring(lastIndex, startIndex)});
        }

        const emojiUrl = '' + {}[emojiKey];
        if (emojiUrl) {
            result.push({type: 'image', content: emojiUrl, emojiKey});
        } else {
            result.push({type: 'text', content: emojiKey});
        }

        lastIndex = endIndex;
        emojiRegex.lastIndex = lastIndex;
    }

    if (lastIndex < text.length) {
        result.push({type: 'text', content: text.substring(lastIndex)});
    }

    return result;
};

export function formatMessageLastTime(
    input: string | number,
    showFullDate: boolean = false
): string {
    // 统一转换为毫秒时间戳
    let timestamp: number;
    if (typeof input === 'string') {
        timestamp = new Date(input).getTime();
    } else {
        timestamp = input < 1e12 ? input * 1000 : input;
    }

    if (!timestamp || timestamp <= 0) return '';

    const now = Date.now();
    const diff = now - timestamp;

    // 时间单位计算
    const minute = 60 * 1000;
    const hour = 60 * minute;
    const day = 24 * hour;
    const week = 7 * day;

    // 相对时间判断
    if (diff < 0) return '刚刚';
    if (diff < minute) return '刚刚';
    if (diff < hour) return `${Math.floor(diff / minute)}分钟前`;
    if (diff < day) return `${Math.floor(diff / hour)}小时前`;
    if (diff < week) return `${Math.floor(diff / day)}天前`;

    // 显示完整日期或周数
    if (showFullDate || diff >= week) {
        const date = new Date(timestamp);
        return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
    }

    return `${Math.floor(diff / week)}周前`;
}

/**
 * 格式化日期为 YYYY-MM-DD
 * @param {Date} date
 * @returns {string}
 */
function formatDate(date) {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
}
