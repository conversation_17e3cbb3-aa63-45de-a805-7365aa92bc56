import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
import { isString } from "./isUtils";
import {isDoctorEnv} from "@/utils/envUtils";

export function downloadFileFromStream({ stream, type = "application/octet-stream", filename = "文件下载" }) {
  let blob = new Blob([stream], { type: type });
  let downloadElement = document.createElement("a");
  let href = window.URL.createObjectURL(blob);
  downloadElement.href = href;
  downloadElement.download = filename;
  document.body.appendChild(downloadElement);
  downloadElement.click();
  document.body.removeChild(downloadElement);
  window.URL.revokeObjectURL(href);
}

export function transformMinioSrc(fileSrc: string): string {
  const systemStore = useSystemStoreWithoutSetup();
  if (isString(fileSrc)) {
    // 医生端img的minio路径修改
    if (isDoctorEnv()){
      let tempImgPrefix = systemStore.imgPrefix.replace('-doctor', '')
      return `${tempImgPrefix}/common/downloadFile?filePath=/${fileSrc}`;
    }
    return `${systemStore.imgPrefix}/common/downloadFile?filePath=/${fileSrc}`;
  } else return "";
}

export function revertToMinioSrc(fileSrc: string): string {
  if (!fileSrc) return;
  const systemStore = useSystemStoreWithoutSetup();
  return `${fileSrc.replace(`${systemStore.imgPrefix}/common/downloadFile?filePath=/`, "")}`;
}

export function asyncFileToBlob(file: File) {
  return new Promise((resolve, reject) => {
    const { type } = file;
    const reader = new FileReader();
    reader.onload = function (evt) {
      const blob = new Blob([evt.target.result], { type });
      resolve(blob);
    };
    reader.onerror = function (err) {
      reject(err);
    };
    reader.readAsDataURL(file);
  });
}
export function asyncFileToBase64(file: File) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = function (evt) {
      resolve(evt.target.result);
    };
    reader.onerror = function (err) {
      reject(err);
    };
    reader.readAsDataURL(file);
  });
}

export function blobToFile(blob: Blob) {
  return new File([blob], "avatar.jpg", { type: blob.type });
}


export function getMediaDuration(file:File):Promise<number>{
  return new Promise((resolved,rejected)=>{
    const binaryData = []
    binaryData.push(file)
    const audioElement = new Audio()
    audioElement.src = URL.createObjectURL(file)
    audioElement.onloadedmetadata = () => {
      const duration = audioElement.duration
      resolved(duration)
    }
    audioElement.onerror = (err) => {
      rejected(err)
    }
  })
} 

/** 下载本地文件 */
export function downloadLocalFile(fileUrl: string, filename: string) {
  const a = document.createElement("a");
  a.href = fileUrl;
  a.download = filename;
  a.click();
}

// 下载文件
export const downloadFile = async (url: string, filename: string) => {
  try {
    const response = await fetch(url)
    const blob = await response.blob() // 获取二进制数据
    
    // 创建下载链接
    const downloadUrl = window.URL.createObjectURL(blob)
    
    // 创建 <a> 标签并触发下载
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename // 自定义文件名（含扩展名）
    document.body.appendChild(link)
    link.click()
    
    // 清理资源
    window.URL.revokeObjectURL(downloadUrl)
    document.body.removeChild(link)
  } catch (error) {
    // console.error('文件下载失败:', error)
  }

}

