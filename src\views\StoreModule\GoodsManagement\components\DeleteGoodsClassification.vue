<template>
  <JModal
    v-model:show="show"
    :title="`删除《${model.name}》商品分类`"
    width="600"
    @after-leave="closeModal"
		@positive-click="_save"
		:positiveButtonProps="{
			loading: isLoading
		}"
    :isScale="false"
  >
    <n-card content-style="padding: 12px" size="small" :bordered="false" aria-modal="true">
      <span>确认删除当前分类后，对所属该分类的商品后续处理</span>
      <n-form
        ref="formRef"
        :model="model"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
        :style="{ width: '100%' }"
        size="small"
        class="mt-12"
      >
        <n-form-item label="更改为新分类">
          <!-- <JGoodsClassification
            :type="model.type"
            v-model:value="model.updateId"
            :forbiddenId="[model.id]"
            style="width: 100%;"
          /> -->
          <!-- 1.0.8版本 -->
          <JProductTreeSelector 
            :type="model.type"
            v-model:value="model.updateId"
            :disableId="model.id"
          />
        </n-form-item>
      </n-form>
    </n-card>
  </JModal>
</template>

<script lang="ts" setup name="editInfoModal">
import { ref } from "vue";
import type { TreeOption } from 'naive-ui';
import { useDialog } from 'naive-ui';
import { goodsClassificationDelete } from "@/services/api";
import { useMessages, useBoolean, useLoading } from '@/hooks';
import { isObject } from "@/utils";
import type { GoodsType } from "@/enums";

interface ModalProps {
  row: TreeOption & { isMenu?: boolean } & Partial<ApiStoreModule.GoodsClassification>; // 分类
}

const { createMessageSuccess, createMessageError } = useMessages();

/** 确认框 */
const dialog = useDialog();

/** emits */
const emits = defineEmits<{
  (e: "afterSuccess", value: string, type: GoodsType): void;
}>();

const { bool: show, setFalse, setTrue } = useBoolean(false);
const modalProps = ref<ModalProps>({
  row: {},
});
/* 接收父组件传过来的参数 */
const acceptParams = (params: ModalProps) => {
  modalProps.value = params;
  // 处理行数据
  if (isObject(params.row) && Object.keys(params.row).length !== 0) {
    model.value.id = params.row?.id;
    model.value.name = params.row?.name;
    model.value.type = params.row?.type;
  }
  show.value = true;
};

/* 表单参数初始化 */
const initParams = {
  name: null,
  id: null, // 需删除商品分类id
  updateId: null, // 更换的商品分类Id
  type: 1 as GoodsType, // 商品分类类型
};

const model = ref({ ...initParams });

/* 清空表单 */
const formDataReset = () => {
  model.value = { ...initParams };
};

/* 关闭弹窗之后 */
const closeModal = () => {
  formDataReset();
};

/** 表单实例 */
const formRef = ref();

/** 保存 */
const { loading: isLoading, startLoading, endLoading } = useLoading(false);
const _save = async e => {
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      try {
        startLoading();
        const _params = {
          id: model.value.id,
          updateId: model.value?.updateId
        };
        if (!_params.updateId) {
          const d = dialog.warning({
            title: '警告',
            content: '未选择新分类，确认所属该分类商品做无分类处理？',
            positiveText: '确认',
            negativeText: '取消',
            onPositiveClick: async () => {
              d.loading = true;
              await goodsClassificationDelete(_params);
              createMessageSuccess('删除成功');
              setFalse();
              // 更新的商品分类Id
              emits('afterSuccess', model.value?.updateId, model.value.type);
            },
            onNegativeClick: () => {
              return;
            }
          });
        } else {
          await goodsClassificationDelete(_params);
          createMessageSuccess('删除成功');
          setFalse();
          // 更新的商品分类Id
          emits('afterSuccess', model.value?.updateId, model.value.type);
        }
      } catch (error) {
        createMessageError('删除失败：' + error);
      } finally {
        endLoading();
      }
    }
  });
};

defineExpose({
  acceptParams,
});
</script>

<style lang="less" scoped></style>
