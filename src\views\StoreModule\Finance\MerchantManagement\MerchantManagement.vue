<template>
  <div class="inner-page-height">
    <div class="table-wrapper">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :pagination="paginationRef"
      :isNeedCollapse="false"
      :isTableSelection="false"
      :isTablePagination="false"
      :isDisplayIndex="false"
    >
      <!-- 操作项 -->
      <template #tableHeaderBtn>
        <n-button @click="tableSearch" class="store-button">刷 新</n-button>
        <JAddButton v-if="hasFinanceStoreConfigMerchantAddAuth" @click="rechargeRecords('add')" type="primary">新增</JAddButton>
      </template>
    </FormLayout>
    <!-- 新建商户号或编辑商户号 -->
    <MerchantModal ref="MerchantModalShow" @success="tableSearch" />
    </div>
  </div>
</template>

<script setup lang="tsx" name="MerchantManagement">
import { onMounted, ref } from "vue";
import { useMessages } from "@/hooks";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import MerchantModal from './components/MerchantModal.vue'
import {
  merchantAdd,
  merchantUpdate,
  merchantPage
} from "@/services/api";
import { paymentMethodsOptions,paymentMethodsLabels } from "@/constants";
import {
   hasFinanceStoreConfigMerchantAddAuth, hasFinanceStoreConfigMerchantUpdateAuth,
} from "../authList";

const { createMessageSuccess, createMessageError } = useMessages();

/* 表格方法Hook */
const {
  isLoading,
  tableData,
  paginationRef,
  pageTableData,
} = useTableDefault({
  pageDataRequest: merchantPage,
});

/* 表格项 */
const tableColumns = [
  {
    title: "支付服务商",
    key: "imgPath",
    width: 150,
    align: 'center',
    resizable:true,
    render: rowData => {
      return paymentMethodsLabels[rowData.platform]
    }
  },
  {
    title: "商户号",
    key: "merchantId",
    align: "center",
    summaryTitle:"",
    width: 100,
  },
  {
    title: "归属主体",
    key: "company",
    align: "center",
    summaryTitle:"",
    width: 200,
  },
  {
    title: "备注",
    key: "comment",
    align: "center",
    summaryTitle:"",
    width: 300,
  },
  {
    title: "更新者",
    key: "userName",
    align: "center",
    summaryTitle:"",
    width: 100,
  },
  {
    title: "更新时间",
    key: "updateTime",
    align: "center",
    summaryTitle:"",
    width: 200,
  },
  {
    title: "操作",
    key: "action",
    width: 120,
    align: "center",
    fixed: "right",
    render: rowData => {
      return (
        <n-space align="center" justify="center">
          { hasFinanceStoreConfigMerchantUpdateAuth ?
            <n-button  text size="small" onClick={()=>rechargeRecords('edit',rowData)} type="primary">
             编辑
            </n-button> 
            : null
          }
        </n-space>
      );
    },
  },
];


/* 刷新列表 */
const tableSearch = () => {
  pageTableData({}, paginationRef.value);
};

/** 新增与编辑 */
const MerchantModalShow = ref()
const rechargeRecords = (type,rowData:any) =>{
    let params = {
     type,
     tableData: tableData.value,   
     row: rowData,
    }
    MerchantModalShow.value.acceptParams(params)
}

/* 组件挂载 */
onMounted(() => {
  tableSearch()
});

</script>

<style lang="less" scoped>
@import "@/styles/default.less";
@import "@/styles/defaultVar.less";

.wrapper {
  width: 100%;
  height: 100%;
}

.table-wrapper {
  height: 100%;
  background: #ffffff;
  width: 100%;
  box-sizing: border-box;
  position: relative;
  .overview-wrapper{

  }
  .header-wrapper{
    padding: 12px 12px 0 12px;
  }
  .header-wrapper,
  .breadcrumb-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    height: auto;
    // height: 34px;
  }
  .header {
    line-height: 22px;
    color: @primary-color;
    font-size: @font-size-base;
    line-height: 34px;
    flex:1;
    .label {
      color: @secondary-text-color;
    }
  }
  .btn {
    flex-shrink: 0;
  }
  .infoWrapper {
    width: 100%;
    text-align: center;
    padding: 70px;
    box-sizing: border-box;
  }
  .notice {
    color: #333333;
    line-height: 29px;
    font-size: 16px;
  }
  img {
    height: 210px;
  }
  .footer-slot-wrapper {
    position: absolute;
    bottom: 19px;
    height: 44px;
    width: calc(100% - 24px);
    background: rgba(255, 255, 255, 0.6);
    display: flex;
    align-items: center;
    padding-right: 17px;
    box-sizing: border-box;
    border-top: 1px solid #eee;
    background: #fff;
    left:12px;
    .footer-selected-count {
      font-weight: 600;
      color: @primary-color;
    }
  }
}
</style>
  
  