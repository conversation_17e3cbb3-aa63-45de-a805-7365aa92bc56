<template>
  <JModal
    v-model:show="showModal"
    width="680"
    :title="isAddMode ? '新增商户号' : '编辑商户号'"
    @after-leave="closeModal"
    @positive-click="handleSave"
    :positiveButtonProps="{
      loading: isLoading
    }"
  >
    <NForm
      ref="formRef"
      :rules="rules"
      :model="model"
      label-width="120"
      label-placement="left"
      require-mark-placement="right-hanging"
      :style="{
        width: '100%',
      }"
    >
      <NGrid :cols="8" :x-gap="24">
        <NFormItemGi :span="24" label="支付服务商" path="platform">
          <NSelect v-model:value="model.platform" :options="paymentMethodsOptions" :disabled="!isAddMode" />
        </NFormItemGi>

        <NFormItemGi :span="24" label="支付商户号" path="merchantId">
          <NInput
            v-model:value="model.merchantId"
            @blur="model.merchantId = $event.target.value.trim()"
            style="width: 100%"
            :show-button="false"
            maxlength="30"
            placeholder="请填写商户号"
            clearable
            show-count
            :disabled="!isAddMode"
          />
        </NFormItemGi>

        <NFormItemGi :span="24" label="归属主体" path="company">
          <NInput
            v-model:value="model.company"
            @blur="model.company = $event.target.value.trim()"
            style="width: 100%"
            :show-button="false"
            maxlength="30"
            placeholder="请填写归属主体"
            clearable
            show-count
          />
        </NFormItemGi>

        <!-- 微信支付特有字段 -->
        <NFormItemGi v-if="isWeChatPay" :span="24" label="商户证书序列号" path="serialNumber">
          <NInput
            v-model:value="model.serialNumber"
            @blur="model.serialNumber = $event.target.value.trim()"
            style="width: 100%"
            :show-button="false"
            maxlength="255"
            placeholder="请填写商户证书序列号"
            clearable
            show-count
          />
        </NFormItemGi>

        <NFormItemGi v-if="isWeChatPay" :span="24" label="商户APIV3密钥" path="apiV3Key">
          <NInput
            v-model:value="model.apiV3Key"
            @blur="model.apiV3Key = $event.target.value.trim()"
            style="width: 100%"
            :show-button="false"
            maxlength="255"
            placeholder="请填写商户APIV3密钥"
            clearable
            show-count
          />
        </NFormItemGi>

        <NFormItemGi v-if="isWeChatPay" :span="24" label="验签模式" path="signatureMode">
          <NSelect v-model:value="model.signatureMode" :options="configureValueOptions" />
        </NFormItemGi>

        <NFormItemGi v-if="isPublicKeyMode && isWeChatPay" :span="24" label="微信支付公钥" path="publicKeyId">
          <NInput
            v-model:value="model.publicKeyId"
            @blur="model.publicKeyId = $event.target.value.trim()"
            style="width: 100%"
            :show-button="false"
            maxlength="255"
            placeholder="请填写微信支付公钥"
            clearable
            show-count
          />
        </NFormItemGi>

        <NFormItemGi
          v-if="isPlatformCertMode && isWeChatPay"
          :span="24"
          label="微信支付平台证书序列号"
          path="wechatSerialNumber"
          required
        >
          <NInput
            v-model:value="model.wechatSerialNumber"
            @blur="model.wechatSerialNumber = $event.target.value.trim()"
            style="width: 100%"
            :show-button="false"
            maxlength="255"
            placeholder="请填写微信支付平台证书序列号"
            clearable
            show-count
          />
        </NFormItemGi>

        <!-- 富友支付特有字段 -->
        <NFormItemGi v-if="isFuiouPay" :span="24" label="富友支付公钥" path="fuiouPayPubKey">
          <NInput
            v-model:value="model.fuiouPayPubKey"
            @blur="model.fuiouPayPubKey = $event.target.value.trim()"
            style="width: 100%"
            :show-button="false"
            maxlength="2048"
            placeholder="请填写富友支付公钥"
            clearable
            show-count
          />
        </NFormItemGi>

        <NFormItemGi v-if="isFuiouPay" :span="24" label="商户支付私钥" path="fuiouPayClientPriKey">
          <NInput
            v-model:value="model.fuiouPayClientPriKey"
            @blur="model.fuiouPayClientPriKey = $event.target.value.trim()"
            style="width: 100%"
            :show-button="false"
            maxlength="2048"
            placeholder="请填写商户支付私钥"
            clearable
            show-count
          />
        </NFormItemGi>

        <NFormItemGi v-if="isFuiouPay" :span="24" label="分账商户号">
          <NInput
            v-model:value="model.allocationMerchantId"
            @blur="model.allocationMerchantId = $event.target.value.trim()"
            style="width: 100%"
            :show-button="false"
            maxlength="30"
            placeholder="请填写分账商户号"
            clearable
            show-count
          />
        </NFormItemGi>

        <NFormItemGi v-if="isFuiouPay" :span="24" label="富友分账公钥">
          <NInput
            v-model:value="model.fuiouShareProfitPubKey"
            @blur="model.fuiouShareProfitPubKey = $event.target.value.trim()"
            style="width: 100%"
            :show-button="false"
            maxlength="2048"
            placeholder="请填写富友分账公钥"
            clearable
            show-count
          />
        </NFormItemGi>

        <NFormItemGi v-if="isFuiouPay" :span="24" label="商户分账私钥">
          <NInput
            v-model:value="model.fuiouShareProfitClientPriKey"
            @blur="model.fuiouShareProfitClientPriKey = $event.target.value.trim()"
            style="width: 100%"
            :show-button="false"
            maxlength="2048"
            placeholder="请填写商户分账私钥"
            clearable
            show-count
          />
        </NFormItemGi>

        <!-- 中金支付特有字段 -->
        <NFormItemGi v-if="isZhongJinPay" :span="24" label="平台编码" path="identCode" required>
          <NInput
            v-model:value="model.identCode"
            @blur="model.identCode = $event.target.value.trim()"
            style="width: 100%"
            :show-button="false"
            maxlength="30"
            placeholder="请填写服务商给的平台编码"
            clearable
            show-count
          />
        </NFormItemGi>

        <NFormItemGi v-if="isZhongJinPay" :span="24" label="店铺号" path="shopId" required>
          <NInput
            v-model:value="model.shopId"
            @blur="model.shopId = $event.target.value.trim()"
            style="width: 100%"
            :show-button="false"
            maxlength="30"
            placeholder="请填写服务商分配的店铺号"
            clearable
            show-count
          />
        </NFormItemGi>

        <NFormItemGi v-if="isZhongJinPay" :span="24" label="appId(appCode)" path="appId" required>
          <NInput
            v-model:value="model.appId"
            @blur="model.appId = $event.target.value.trim()"
            style="width: 100%"
            :show-button="false"
            maxlength="30"
            placeholder="请填写服务商给的Appid"
            clearable
            show-count
          />
        </NFormItemGi>

        <NFormItemGi v-if="isZhongJinPay" :span="24" label="随机字符(character)" path="character" required>
          <NInput
            v-model:value="model.character"
            @blur="model.character = $event.target.value.trim()"
            style="width: 100%"
            :show-button="false"
            maxlength="2048"
            placeholder="请填写服务商给的字符串"
            clearable
            show-count
          />
        </NFormItemGi>

        <NFormItemGi v-if="isZhongJinPay" :span="24" label="中金支付公钥" path="ciccPayPubKey" required>
          <NInput
            v-model:value="model.ciccPayPubKey"
            @blur="model.ciccPayPubKey = $event.target.value.trim()"
            style="width: 100%"
            :show-button="false"
            maxlength="2048"
            placeholder="请填写中金支付公钥"
            clearable
            show-count
          />
        </NFormItemGi>

        <NFormItemGi v-if="isZhongJinPay" :span="24" label="商户支付私钥" path="ciccPayClientPriKey" required>
          <NInput
            v-model:value="model.ciccPayClientPriKey"
            @blur="model.ciccPayClientPriKey = $event.target.value.trim()"
            style="width: 100%"
            :show-button="false"
            maxlength="2048"
            placeholder="请填写中金商户支付私钥"
            clearable
            show-count
          />
        </NFormItemGi>

        <NFormItemGi v-if="isZhongJinPay" :span="24" label="中金分账公钥">
          <NInput
            v-model:value="model.ciccShareProfitPubKey"
            @blur="model.ciccShareProfitPubKey = $event.target.value.trim()"
            style="width: 100%"
            :show-button="false"
            maxlength="2048"
            placeholder="请填写中金分账公钥"
            clearable
            show-count
          />
        </NFormItemGi>

        <NFormItemGi v-if="isZhongJinPay" :span="24" label="商户分账私钥">
          <NInput
            v-model:value="model.ciccShareProfitClientPriKey"
            @blur="model.ciccShareProfitClientPriKey = $event.target.value.trim()"
            style="width: 100%"
            :show-button="false"
            maxlength="2048"
            placeholder="请填写中金商户分账私钥"
            clearable
            show-count
          />
        </NFormItemGi>

        <!-- 汇聚支付特有字段 -->
        <NFormItemGi v-if="isHuiJuPay" :span="24" label="汇聚key" path="fuiouPayClientPriKey">
          <NInput
            v-model:value="model.fuiouPayClientPriKey"
            @blur="model.fuiouPayClientPriKey = $event.target.value.trim()"
            style="width: 100%"
            :show-button="false"
            maxlength="2048"
            placeholder="请填写汇聚key"
            clearable
            show-count
          />
        </NFormItemGi>

        <NFormItemGi v-if="isHuiJuPay" :span="24" label="报备商户号" path="tradeMerchantNo">
          <NInput
            v-model:value="model.tradeMerchantNo"
            @blur="model.tradeMerchantNo = $event.target.value.trim()"
            style="width: 100%"
            :show-button="false"
            maxlength="30"
            placeholder="请填写报备商户号"
            clearable
            show-count
          />
        </NFormItemGi>

        <NFormItemGi :span="24" label="备注">
          <NInput
            v-model:value="model.comment"
            @blur="model.comment = $event.target.value.trim()"
            style="width: 100%"
            maxlength="30"
            show-count
            clearable
            placeholder="请填写备注"
          />
        </NFormItemGi>

        <NFormItemGi :span="24" label="启用状态">
          <n-radio-group v-model:value="model.state">
            <n-space>
              <n-radio :value="1">启用</n-radio>
              <n-radio :value="0">停用</n-radio>
            </n-space>
          </n-radio-group>
        </NFormItemGi>
      </NGrid>
    </NForm>
  </JModal>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { useMessages } from "@/hooks";
import { PayServiceEnum, SignatureModeEnum } from "@/enums";
import { paymentMethodsOptions, configureValueOptions } from "@/constants";
import {
  merchantAdd,
  merchantUpdate,
} from "@/services/api";
import { error } from "naive-ui/es/_utils/naive/warn";

defineOptions({
  name: "MerchantModal",
});

type AddOrEdit = "add" | "edit";

interface MerchantModalProps {
  type?: AddOrEdit;
  row?: MerchantContent;
  refreshTable?: () => void;
}

interface MerchantContent {
  id?: number;
  platform: number;
  merchantId?: string;
  company?: string;
  serialNumber?: string;
  apiV3Key?: string;
  fuiouPayPubKey?: string;
  fuiouPayClientPriKey?: string;
  fuiouShareProfitPubKey?: string;
  fuiouShareProfitClientPriKey?: string;
  comment?: string;
  state: 0 | 1;
  wechatSerialNumber?: string;
  allocationMerchantId?: string;
  signatureMode?: number;
  publicKeyId?: string;
  identCode?: string;
  shopId?: string;
  appId?: string;
  character?: string;
  ciccPayPubKey?: string;
  ciccPayClientPriKey?: string;
  ciccShareProfitPubKey?: string;
  ciccShareProfitClientPriKey?: string;
  /** 报备商户号 */
  tradeMerchantNo?: string;
}

/** emits */
const emit = defineEmits<{
  (e: "success"): void;
}>();

const INIT_MODEL: MerchantContent = {
  platform: PayServiceEnum.WeChatPay,
  state: 1,
  signatureMode: SignatureModeEnum.PublicKey
};

const model = ref<MerchantContent>({ ...INIT_MODEL });
const showModal = ref(false);
const formRef = ref();
const isLoading = ref(false);
const isAddMode = ref(true);
const existingMerchantIds = ref<string[]>([]);
const editMerchantId = ref<string | undefined>();
const message = useMessages();

const props = withDefaults(defineProps<MerchantModalProps>(), {
  type: "add"
});

/** 支付服务商 */
const isWeChatPay = computed(() => model.value.platform === PayServiceEnum.WeChatPay);
const isFuiouPay = computed(() => model.value.platform === PayServiceEnum.FuiouPay);
const isZhongJinPay = computed(() => model.value.platform === PayServiceEnum.ZhongJinPay);
const isHuiJuPay = computed(() => model.value.platform === PayServiceEnum.HuiJuPay);

/** 验签模式 */
const isPublicKeyMode = computed(() => model.value.signatureMode === SignatureModeEnum.PublicKey);
const isPlatformCertMode = computed(() => model.value.signatureMode === SignatureModeEnum.PlatformCertificate);

/** 表单规则 */
const rules = computed(() => {
  const baseRules: Record<string, any> = {
    platform: { type: 'number', required: true, message: "请选择支付服务商", trigger: ["blur", "change"] },
    merchantId: { required: true, message: "请填写商户号", trigger: ["blur", "change"] },
    company: { required: true, message: "请填写归属主体", trigger: ["blur", "change"] },
  };

  if (isWeChatPay.value) {
    baseRules.serialNumber = { required: true, message: "请填写商户证书序列号", trigger: ["blur", "change"] };
    baseRules.apiV3Key = { required: true, message: "请填写商户APIV3密钥", trigger: ["blur", "change"] };
    baseRules.signatureMode = { type: 'number', required: true, message: "请选择验签模式", trigger: ["blur", "change"] };

    if (isPublicKeyMode.value) {
      baseRules.publicKeyId = { required: true, message: "请填写微信支付公钥", trigger: ["blur", "change"] };
    } else if (isPlatformCertMode.value) {
      baseRules.wechatSerialNumber = { required: true, message: "请填写微信支付平台证书序列号", trigger: ["blur", "change"] };
    }
  }

  if (isFuiouPay.value) {
    baseRules.fuiouPayPubKey = { required: true, message: "请填写富友支付公钥", trigger: ["blur", "change"] };
    baseRules.fuiouPayClientPriKey = { required: true, message: "请填写商户支付私钥", trigger: ["blur", "change"] };
  }

  if (isZhongJinPay.value) {
    baseRules.identCode = { required: true, message: "请填写平台编码", trigger: ["blur", "change"] };
    baseRules.shopId = { required: true, message: "请填写店铺号", trigger: ["blur", "change"] };
    baseRules.appId = { required: true, message: "请填写Appid", trigger: ["blur", "change"] };
    baseRules.character = { required: true, message: "请填写随机字符", trigger: ["blur", "change"] };
    baseRules.ciccPayPubKey = { required: true, message: "请填写中金支付公钥", trigger: ["blur", "change"] };
    baseRules.ciccPayClientPriKey = { required: true, message: "请填写中金商户支付私钥", trigger: ["blur", "change"] };
  }

  if (isHuiJuPay.value) {
    baseRules.fuiouPayClientPriKey = { required: true, message: "请填写汇聚key", trigger: ["blur", "change"] };
    baseRules.tradeMerchantNo = { required: true, message: "请填写报备商户号", trigger: ["blur", "change"] };
  }

  return baseRules;
});

/**
 * 接收参数并初始化表单
 * @param params 传入的参数，包含行数据和表格数据
 */
const acceptParams = (params: MerchantModalProps & { tableData?: Array<{ merchantId: string }> }) => {
  resetForm();

  if (params.row?.id) {
    const {
      platform,
      merchantId,
      company,
      serialNumber,
      apiV3Key,
      fuiouPayPubKey,
      fuiouPayClientPriKey,
      comment,
      state,
      fuiouShareProfitPubKey,
      fuiouShareProfitClientPriKey,
      id,
      wechatSerialNumber,
      allocationMerchantId,
      publicKeyId,
      signatureMode,
      identCode,
      shopId,
      appId,
      character,
      ciccPayPubKey,
      ciccShareProfitPubKey,
      ciccPayClientPriKey,
      ciccShareProfitClientPriKey,
      tradeMerchantNo,
    } = params.row || {};

    Object.assign(model.value, {
      platform,
      merchantId,
      company,
      serialNumber,
      apiV3Key,
      fuiouPayPubKey,
      fuiouPayClientPriKey,
      comment,
      state,
      fuiouShareProfitPubKey,
      fuiouShareProfitClientPriKey,
      id,
      wechatSerialNumber,
      allocationMerchantId,
      publicKeyId,
      signatureMode,
      identCode,
      shopId,
      appId,
      character,
      ciccPayPubKey,
      ciccShareProfitPubKey,
      ciccPayClientPriKey,
      ciccShareProfitClientPriKey,
      tradeMerchantNo
    });

    isAddMode.value = false;
    editMerchantId.value = params.row.merchantId;
  }

  if (params.tableData) {
    existingMerchantIds.value = params.tableData.map(item => item.merchantId);
  }

  showModal.value = true;
};

/**
 * 重置表单数据
 */
const resetForm = () => {
  model.value = { ...INIT_MODEL };
  isAddMode.value = true;
  existingMerchantIds.value = [];
  editMerchantId.value = undefined;
};

/**
 * 关闭模态框
 */
const closeModal = () => {
  showModal.value = false;
  resetForm();
};

/**
 * 处理保存操作
 * @param e 鼠标事件
 */
const handleSave = async (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async (error) => {
    if (!error) {
      try {
        // 检查商户号是否重复
        if (isDuplicateMerchantId()) {
          message.createMessageWarning('商户号已存在，不能重复录入');
          return;
        }

        isLoading.value = true;
        let _params = {
          data:{ ...model.value }
        };
        if (isAddMode.value) {
          await merchantAdd(_params);
        } else {
          await merchantUpdate(_params);
        }
        message.createMessageSuccess(`${isAddMode.value ? '新增' : '编辑'}商户号成功`);
        emit("success");
        closeModal();
      } catch (error) {
        message.createMessageError(`${isAddMode.value ? '新增' : '编辑'}商户号失败: ${error}`);
      } finally {
        isLoading.value = false;
      }
    }
  });
};

/**
 * 检查商户号是否重复
 * @returns 是否重复
 */
const isDuplicateMerchantId = () => {
  if (!model.value.merchantId) return false;

  return existingMerchantIds.value.some(id => {
    if (isAddMode.value) {
      return id === model.value.merchantId;
    }
    return id === model.value.merchantId && id !== editMerchantId.value;
  });
};

/**
 * 监听支付平台变化
 * 切换平台时重置相关字段
 */
watch(() => model.value.platform, (newPlatform) => {
  if (isAddMode.value) {
    // 重置平台特有字段
    const resetFields = {
      serialNumber: undefined,
      apiV3Key: undefined,
      fuiouPayPubKey: undefined,
      fuiouPayClientPriKey: undefined,
      tradeMerchantNo: undefined,
      fuiouShareProfitPubKey: undefined,
      fuiouShareProfitClientPriKey: undefined,
      wechatSerialNumber: undefined,
      publicKeyId: undefined,
      identCode: undefined,
      shopId: undefined,
      appId: undefined,
      character: undefined,
      ciccPayPubKey: undefined,
      ciccPayClientPriKey: undefined,
      ciccShareProfitPubKey: undefined,
      ciccShareProfitClientPriKey: undefined
    };

    Object.assign(model.value, resetFields);

    // 微信支付默认使用公钥模式
    if (newPlatform === PayServiceEnum.WeChatPay) {
      model.value.signatureMode = SignatureModeEnum.PublicKey;
    }
  }
});

/**
 * 监听签名模式变化
 * 切换模式时重置相关字段
 */
watch(() => model.value.signatureMode, (newMode) => {
  if (isAddMode.value) {
    if (newMode === SignatureModeEnum.PublicKey) {
      model.value.wechatSerialNumber = undefined;
    } else {
      model.value.publicKeyId = undefined;
    }
  }
});

defineExpose({
  acceptParams
});
</script>
