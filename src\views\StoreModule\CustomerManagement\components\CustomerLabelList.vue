<template>
    <n-spin 
        size="small"
        :show="props.loading" 
        style="height: 100%;"
    >
        <n-scrollbar 
            v-if="props.options.length > 1" 
            style="height: 100%;" 
            @scroll="($event) => emits('scroll', $event)"
        >
          <ul>
            <li 
                v-for="item in props.options" 
                :key="item.key" 
                class="label-wrapper"
                :class="{'active': props.selectedKeys.includes(item.key)}"
                @contextmenu.prevent="($event) => handleRightMenu($event, item)"
                @click="emits('clickMenu', [item.key], item)"
            >
                <n-ellipsis>{{item.label}}</n-ellipsis>
            </li>
          </ul>
        </n-scrollbar>
        <div v-else class="empty-wrapper">
          <img :src="EmptySrc" alt="" />
          <p>暂无数据</p>
        </div>
    </n-spin>
</template>

<script lang="ts" setup name='CustomerLabelList'>
import { ref } from "vue";
import EmptySrc from "@/assets/image/exception/empty.png";
import type { CustomerTag } from "../type";

/** props */
const props = defineProps<{
    options: Array<CustomerTag> | null;
    loading?: boolean;
    selectedKeys: Array<string>; // 当前选中的keys
}>();

/** emits */
const emits = defineEmits<{
    (e: 'scroll', val: MouseEvent): void;
    (e: "rightClick", event: MouseEvent, val: CustomerTag): void; 
    (e: "clickMenu", val: Array<string>, option: CustomerTag): void; 
}>();

/** 右键菜单 */
const handleRightMenu = (e: MouseEvent, val: CustomerTag) => {
    // 判断是否支持右键菜单
    if (val?.isMenu) {
        emits('rightClick', e, val);
    }
};
</script>

<style lang="less" scoped>
:deep(.n-spin-content) {
    height: 100%;
}
:deep(.n-scrollbar > .n-scrollbar-rail.n-scrollbar-rail--vertical) {
	bottom: 8px;
}
.active{
    background-color: rgb(231, 241, 255);
    color: rgb(42, 119, 255);
}
.label-wrapper {
    height: 31px;
    box-sizing: border-box;
    padding: 8px 12px;
    margin: 4px 16px;
    font-size: 15px;
    border-radius: 2px;
    cursor:pointer;
    &:hover {
        background-color: rgb(231, 241, 255);
        color: rgb(42, 119, 255);
    }
}
.empty-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
      	img {
      		height: 160px;
      	
      	p {
      		font-size: 16px;
      		text-align: center;
        font-weight: 700;
      	}
    }
}
</style>