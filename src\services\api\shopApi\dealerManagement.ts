import { defHttp } from "@/services";

/** 经销商管理 */
export const enum DealerManagementApi {
  page = "/structure/listForDealer",
  getDealerQrCode = "/structure/getDealerQrCode",
}
/**
 * @description 经销商管理分页
 */
export function getShopDealerPage(params) {
  return defHttp.post({
    url: DealerManagementApi.page,
    params,
  });
}
/**
 * @description 经销商注册码（不传组织机构id）
 */
export function getDealerQrCode() {
  return defHttp.get({
    url: DealerManagementApi.getDealerQrCode,
  });
}
