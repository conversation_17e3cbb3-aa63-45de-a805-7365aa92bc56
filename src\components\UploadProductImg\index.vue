<template>
    <n-upload
      v-bind="$attrs"
      :custom-request="customRequest"
      :list-type="listTypeRef"
      :accept="acceptRef"
      :multiple="props.isMultiple"
      :max="fileListSizeRef"
      v-model:file-list="fileList"
      @remove="fileRemoveHandler"
      @before-upload="beforeUpload"
    />
</template>

<script setup lang="ts" name="UploadProductImg">
import { ref, toRef, toRefs, watch } from "vue";
import type { UploadCustomRequestOptions, UploadFileInfo } from "naive-ui";
// import type { FileInfo } from "naive-ui/es/upload/src/interface";
import { useMessages } from "@/hooks";
import { revertToMinioSrc, transformMinioSrc } from "@/utils/fileUtils";
import { isArray } from "@/utils/isUtils";
// 上传文件Api
import { UploadProductImg } from "@/services/api";

const { createMessageWarning, createMessageError } = useMessages();

/** props */
const props = withDefaults(
  defineProps<{
    value: Array<{
        img: string,
        path: string,
    }>;
    listType?: "text" | "image" | "image-card"; // 文件列表的内建样式
    maxFileSize?: number; // 接受文件大小, 默认值为0
    fileListSize?: number;
    accept?: string; // 接受的文件类型
    isMultiple?: boolean; // 是否多文件上传
  }>(),
  {
    listType: "image-card",
    maxFileSize: 0,
    fileListSize: 1,
    isMultiple: false
  }
);

/** emits */
const emits = defineEmits<{
    (e: "update:value", urlList: Array<{
        img: string,
        path: string,
    }>): void;
}>();

/** value */
const valueRef = toRef(props, "value");
/** 接受的文件类型 */
const acceptRef = toRef(props, "accept");
/** 临时值 */
let tempValue = ref([]);
/** 解构props */
const {
  listType: listTypeRef,
  maxFileSize: maxFileSizeRef,
  fileListSize: fileListSizeRef,
} = toRefs(props);

const fileList = ref<Array<UploadFileInfo>>([]);
/** 上传内容 */
const uploadFileList = ref<Array<UploadCustomRequestOptions>>([]);
/** 是否文件正在上传中 */
const isUploadLoading = ref(false);
/** 多文件上传时，已成功上传的文件Id */
const uploadSuccessFileId = ref([]);

/**
 * @description 自定义上传方法
 * @param options upload 所有配置项
 * */
async function customRequest(options: UploadCustomRequestOptions) {
  // 去重，避免重复
  if (!uploadFileList.value.some(item => item.file.id === options.file.id)) {
    // 将选项推入记录
    uploadFileList.value.push(options);
  }
  // 处理上传队列
  if (!isUploadLoading.value) {
    handleUploadQueue();
  }
};

/**
 * @description 处理文件上传队列
 */
function handleUploadQueue() {
  // 获取并移除第一个文件
  const options: UploadCustomRequestOptions = uploadFileList.value.shift();
  isUploadLoading.value = true;
  // 进行文件上传
  handleUploadFile(options);
}

/**
 * @description 进行文件上传
 */
const handleUploadFile = async (options: UploadCustomRequestOptions) => {
  const { file, onFinish, onError, onProgress } = options;
  const _formData = new FormData();
  _formData.append("files", file.file);
  try {
    const resp = await UploadProductImg(_formData, ({ progress }) => {
      onProgress({ percent: Number((progress * 100).toFixed(2)) }); // 上传进度
    });
    /** 完成回调 */
    onFinish();
    // 成功记录当前成功上传的文件Id
    uploadSuccessFileId.value.push(file.id);
    if (isArray(resp)){
      // 单文件与多文件处理
      if (!props.isMultiple) {
        if (tempValue.value.length >= fileListSizeRef.value) {
          tempValue.value.splice(0, 1);
        }
        tempValue.value.push(resp[0]);
        emits("update:value", [...tempValue.value]);
        isUploadLoading.value = false;
      } else {
        // 多文件
        if (tempValue.value.length >= fileListSizeRef.value) {
          tempValue.value.splice(0, 1);
        }
        tempValue.value.push(resp[0]);
        // 队列是否存在未完成文件上传，继续进行文件上传
        if (uploadFileList.value.length !== 0 && props.isMultiple) {
          handleUploadQueue();
        } else {
          // 多文件上传完成
          handleUploadCompleteCallBack();
        }
      }
    }
  } catch (e) {
    createMessageError("上传错误：" + e);
    onError();
    emits("update:value", []);
    if (!props.isMultiple) {
      isUploadLoading.value = false;
    }
  }
};

/**
 * @description 文件上传完成回调
 */
const handleUploadCompleteCallBack = () => {
  emits("update:value", [...tempValue.value]);
  isUploadLoading.value = false;
  uploadSuccessFileId.value = [];
};

/**
 * @description 文件删除回调
 */
function fileRemoveHandler(options: {
  file: UploadFileInfo;
  fileList: Array<UploadFileInfo>;
}) {
  const { url, status, id } = options.file;
  // 如果文件正在上传，提示用户不要删除
  if (status === "uploading") {
    createMessageWarning("正在上传中，请勿删除");
    return false;
  }
  // 多文件上传, 检查是否上传中
  if (uploadSuccessFileId.value.indexOf(id) !== -1) {
    createMessageWarning("文件已上传，需等待未上传内容，请勿删除");
    return false;
  }
  // 未进行文件上传，可进行删除操作
  uploadFileList.value = uploadFileList.value.filter(item => item.file.id !== id);
  // 更新临时值
  tempValue.value = tempValue.value.filter((item) => {
    if (item.path !== revertToMinioSrc(url)) {
        return item;
    }
  });
  // 单文件上传、上传状态
  if (!props.isMultiple || !isUploadLoading.value) {
    emits("update:value", tempValue.value);
    return false;
  }
};

/**
 * @description 文件上传之前回调
 */
function beforeUpload(data: {
  file: UploadFileInfo;
  fileList: UploadFileInfo[];
}) {
  if (maxFileSizeRef.value) {
    const {
      file: { size },
    } = data.file;
    if (size > maxFileSizeRef.value * 1024 * 1024) {
      createMessageError(`该文件超出大小，大小限制为 ${maxFileSizeRef.value} MB`);
      return false;
    }
  }
  return true;
}

/** 监听 */
watch(
  valueRef,
  (newVal) => {
    tempValue.value = newVal;
    const fileListTemp: Array<UploadFileInfo> = [];
    newVal.forEach((item, index) => {
      if (item) {
        const _id = `${Math.random() * 1000}-${index}`;
        fileListTemp.push({
          id: _id,
          name: `file-${_id}`,
          status: "finished",
          url: transformMinioSrc(item.path),
          thumbnailUrl: null,
        });
      }
    });
    fileList.value = [...fileListTemp];
  },
  { immediate: true }
);

defineExpose({
  isUploadLoading
});
</script>


