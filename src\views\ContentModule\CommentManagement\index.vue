<script setup lang="ts">
import TabsLayout from "@/layout/TabsLayout.vue";
import {ref, watch , toRefs } from "vue";
import CommentManagementTab from './components/CommentManagementTab.vue'
import {CommentTypeEnum} from '@/enums'

const tabNameRef = ref<CommentTypeEnum>(CommentTypeEnum.AuditPending);

// const props = defineProps<{
//   tabValue: CommentTypeEnum,
// }>();

// watch(()=>props.tabValue,(val)=>{
//   if (val) {
//     tabNameRef.value = val
//   }
// },{immediate:true})

const tableDataUpdate = ref()

const tabsData = ref([
  {
    label: "待审核",
    key: CommentTypeEnum.AuditPending,
  },
  {
    label: "审核通过",
    key: CommentTypeEnum.AuditResolve,
  },
  {
    label: "审核不通过",
    key: CommentTypeEnum.AuditReject,
  },
]);


</script>

<template >
  <n-layout>
    <n-layout-content id="VideoManagement">
      <TabsLayout  v-model:value="tabNameRef" :tabsData="(tabsData as any)"  :onlyTabs="true" class="tabsLayout">
        <CommentManagementTab ref="tableDataUpdate" :tabNameRef="tabNameRef" />
      </TabsLayout>
    </n-layout-content>
  </n-layout>
</template>

<style lang="less" scoped>
// @import "@/styles/default.less";
</style>
