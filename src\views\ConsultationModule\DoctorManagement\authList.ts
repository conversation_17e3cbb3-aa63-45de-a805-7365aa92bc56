import { DoctorManagementAuth } from "@/enums/authKeys";
import { hasAuth } from "@/utils/auth/authUtils";


/** 医生管理之新建 */
export const hasAddDoctorAuth= function(){
    return hasAuth(DoctorManagementAuth.doctorManagementIndexNew.key);
}()

/** 编辑操作员 */
export const hasEditDoctorAuth = function(){
    return hasAuth(DoctorManagementAuth.doctorManagementIndexEdit.key);
}()

/** 启用/禁用 */
export const hasEnableChangeDoctorAuth = function(){
    return hasAuth(DoctorManagementAuth.doctorManagementIndexEnablechange.key);
}()
