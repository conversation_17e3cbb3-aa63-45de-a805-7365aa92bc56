<template>
  <n-popover trigger="hover" style="max-width:480px;">
    <template #trigger>
        <span v-if="!isNullOrUnDef(props.text)">{{ props.text }}</span>
        <n-icon v-else :size="props.size ? size : 14" :depth="2" style="padding: 0px 2px"><HelpIcon /></n-icon>
    </template>
    <span style="white-space: pre-wrap;" >{{ helpTextComputed }}</span>
  </n-popover>
</template>
<script setup lang="ts">
import { HelpCircleOutline as HelpIcon } from "@vicons/ionicons5";
import { computed, toRef } from "vue";
import { HELP_MAP } from "./helpMap";
import { isNullOrUnDef } from "@/utils/isUtils";
const props = defineProps<{
  helpEntry: string;
  text?: string;
  size?: number | string;
}>();

const helpEntryRef = toRef(props, "helpEntry");
const helpTextComputed = computed(() => {
  return HELP_MAP.hasOwnProperty(helpEntryRef.value)
    ? HELP_MAP[helpEntryRef.value]
    : "暂时没找到该词条的详细信息";
});
</script>
