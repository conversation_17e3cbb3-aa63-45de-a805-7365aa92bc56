import { hasAuth } from "@/utils/auth/authUtils";
import { MedicalInquiryFormManagementAuth } from "@/enums/authKeys";

/** 详情 */
export const hasManagementDetails = function(){
    return hasAuth(MedicalInquiryFormManagementAuth.Details.key);
}()

/** 退款 */
export const hasManagementRefund = function(){
    return hasAuth(MedicalInquiryFormManagementAuth.Refund.key);
}()

/** 导出 */
export const hasManagementExport = function(){
    return hasAuth(MedicalInquiryFormManagementAuth.Export.key);
}()

/** 查看视频 */
export const hasManagementVideo = function(){
    return hasAuth(MedicalInquiryFormManagementAuth.Video.key);
}()

/** 创建预约单 */
export const hasManagementCreateAppointment = function(){
    return hasAuth(MedicalInquiryFormManagementAuth.CreateAppointment.key);
}()

/** 取消预约单 */
export const hasManagementCancelAppointment = function(){
    return hasAuth(MedicalInquiryFormManagementAuth.CancelAppointment.key);
}()