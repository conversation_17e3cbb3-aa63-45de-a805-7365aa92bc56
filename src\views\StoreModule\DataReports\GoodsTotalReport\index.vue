<template>
  <div class="goods-wrapper">
    <!--介绍 -->
    <n-card title="商品统计" size="small" class="header" :bordered="false">按时间段统计每个商品的订单情况。</n-card>
    <!-- 表格 -->
    <div class="goods-table">
      <FormLayout
        :isLoading="isLoading"
        :tableData="tableData"
        :tableColumns="tableColumns"
        :isNeedCollapse="false"
        :pagination="paginationRef"
        @paginationChange="paginationChange"
        :table-summary="summaryRefs"
        @tableSorterChange="tableSorterChange"
        :isTableSelection="false"
        table-row-key="_dummyId"
      >
        <template #searchForm>
          <!-- 表单 -->
          <n-form
            ref="formRef"
            :model="model"
            :show-feedback="false"
            label-placement="left"
            label-width="auto"
            require-mark-placement="right-hanging"
            size="small"
            :style="{ width: '100%' }"
          >
            <n-form-item label="统计区间">
              <JDateRangePicker 
                v-model:value="model.rangeTime"
                :limitTimeRang="model.limitTimeRang"
                style="flex: 1;" 
                type="daterange" 
                format="yyyy-MM-dd"
                :clearable="false"
                :maxDays="366"
              />
            </n-form-item>
            <!-- 商品名称 -->
            <n-form-item label="商品名称">
              <n-input-group>
                <n-select
                  v-model:value="model.searchType"
                  placeholder="请选择"
                  :options="searchTypeOptions"
                  style="width: 100px;"
                />
                <JSearchInput
                  v-model:value="model.name"
                  placeholder="请输入商品名称或ID"
                  @search="tableSearch"
                  :is-show-search-icon="false"
                  :width="210"
                />
              </n-input-group>
            </n-form-item>
            <!-- 商品类型 -->
            <n-form-item label="商品类型">
              <n-select
                v-model:value="model.type"
                placeholder="请选择商品类型"
                :options="merchandiseTypeOptions"
                style="width: 160px;"
                clearable
              />
            </n-form-item>
          </n-form>
        </template>
        <!-- 操作项 -->
        <template #tableHeaderBtn>
          <n-button @click="refresh" class="store-button">刷 新</n-button>
          <n-button v-if="hasProductExportAuth" type="primary" :loading="exportLoading" @click="handeReport" :disabled="!tableData.length">导出查询到的数据</n-button>
        </template>
      </FormLayout>
    </div>
  </div>
</template>

<script lang="tsx" setup name="GoodsTotalReport">
import { ref, onMounted, watch } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import dayjs from "dayjs";
import { useLoading, useMessages } from "@/hooks";
import { merchandiseTypeOptions, merchandiseTypeLabels } from "@/constants";
import { getProduceStatData, produceStatDataExport } from "@/services/api";
import { GoodsCategoryType } from "@/enums";
import { SortType, GoodsSearchType, SortTypeValue } from "../types";
import { hasProductExportAuth } from "../authList";

const { createMessageError, createMessageSuccess } = useMessages();

/* 表格方法Hook */
const {
  isLoading,
  tableData,
  paginationRef,
  pageTableData,
  refreshTableData,
  paginationChange,
} = useTableDefault({
  useDummyId: true,
  pageDataRequest: getProduceStatData,
});

/* 初始化参数 */
const initParams = {
  rangeTime: [
    dayjs().subtract(29, 'day').valueOf(),
    dayjs().valueOf()
  ],
  limitTimeRang: [dayjs().valueOf(), 0],
  name: '',
  sortType: SortType.TOTALORDER,
  isAsc: false,
  searchType: GoodsSearchType.NAME,
  type: null,
};
const model = ref({ ...initParams });

/** 搜索类型 */
const searchTypeOptions = [
  {
    label: "商品名称",
    value: GoodsSearchType.NAME,
  },
  {
    label: "商品ID",
    value: GoodsSearchType.ID,
  },
];

/* 表格项 */
const tableColumns = [
  {
    title: "商品名称",
    summaryTitle: "",
    key: "nickName",
    align: "left",
    fixed: "left",
    width: 160,
    render: (row) => {
      // 普通商品 与 疗法类商品
      if (row.type === GoodsCategoryType.GENERAL || row?.type === GoodsCategoryType.THERAPY) {
        return <table-tooltip row={row} nameKey="productName" idKey="productId" />;
      }
      let title = `[${row.productName ?? ''}] ${row.productFrontName ?? ''} ${row?.specName ?? ''}`;
      return <table-tooltip row={row} nameKey="productName" title={title} idKey="productId" />;
    }
  },
  {
    title: "商品类型",
    summaryTitle: "",
    key: "type",
    align: "left",
    width: 80,
    render: (row) => {
      return <span>{merchandiseTypeLabels[row?.type]}</span>
    }
  },
  {
    title: "总订单数",
    sorter: true,
    isSortDefault: true,
    summaryTitle: "总订单数",
    key: "totalOrder",
    align: "left",
    width: 120,
  },
  {
    title: "已支付单数",
    sorter: true,
    summaryTitle: "总已支付单数",
    key: "paidOrder",
    align: "left",
    width: 120,
  },
  {
    title: "待支付单数",
    sorter: true,
    summaryTitle: "总待支付单数",
    key: "unpaidOrder",
    align: "left",
    width: 120,
  },
  {
    title: "在线支付单数",
    key: "onlinePaymentSingular",
    align: "left",
    width: 120,
  },
  {
    title: "定金支付单数",
    key: "downPaymentSingular",
    align: "left",
    width: 120,
  },
  {
    title: "物流代收单数",
    key: "logisticsCollectionSingular",
    align: "left",
    width: 120,
  },
  {
    title: "已支付订单总额（元）",
    sorter: true,
    summaryTitle: "总已支付订单总额（元）",
    key: "totalAmount",
    align: "left",
    width: 140,
    render: row => {
      return (
        <span>{row.totalAmount ? (row.totalAmount / 100).toFixed(2) : '0.00'}</span>
      );
    }
  },
];

/** 排序 */
const tableSorterChange = (info: { sort: string, sortAsc: "ascend" | "descend" }) => {
  model.value.sortType = SortTypeValue[info.sort];
  if (info.sortAsc === 'ascend') {
    model.value.isAsc = true;
  } else {
    model.value.isAsc = false;
  }
  tableSearch();
};

/** 导出 */
const { loading: exportLoading, startLoading, endLoading } = useLoading();
async function handeReport() {
  try {
    startLoading();
    await produceStatDataExport({
      data: { ...getParams() },
      pageVO:{
        current: paginationRef.value.current,
        size: paginationRef.value.pageSize
      }
    });
    createMessageSuccess('导出成功！');
  } catch (error) {
    createMessageError('导出失败：' + error);
  } finally {
    endLoading();
  }
}

/** 当前页数据总计 */
const summaryRefs = ref(null);
watch(tableData, (newVal) => {
 summaryRefs.value = summaryColumn(newVal);
});
const summaryColumn = (rowData) => {
  const _sum = {
    "totalOrder":0,
    "paidOrder": 0,
    "unpaidOrder": 0,
    "onlinePaymentSingular": 0,
    "downPaymentSingular": 0,
    "logisticsCollectionSingular": 0,
    "totalAmount": 0,
  };
  rowData.forEach(row=>{
    for(let key in row){
      if(Object.keys(_sum).includes(key)){
        _sum[key] = _sum[key] + row[key]
      }
    }
  });
  _sum['totalAmount'] = Number((_sum['totalAmount'] / 100).toFixed(2));
  return {
    ..._sum,
  }
};

/** 获取参数 */
function getParams() {
  const { rangeTime, sortType, isAsc, searchType, type, name } = model.value;
  const [startTime, endTime] = rangeTime;
  let _params = {
    sortType,
    isAsc,
    type,
    startTime: dayjs(startTime).format('YYYY-MM-DD 00:00:00'),
    endTime: dayjs(endTime).format('YYYY-MM-DD 23:59:59'),
  };
  if (searchType === GoodsSearchType.NAME) {
    _params['productName'] = name;
  } else {
    _params['productId'] = name;
  }
  return _params;
}

/** 表格搜索 */
function tableSearch() {
  pageTableData(getParams(), paginationRef.value);
}

/** 表格刷新 */
function refresh(){
  tableSearch();
};

/* 组件挂载 */
onMounted(() => {
  tableSearch();
});

/** 监听 */
watch(() => [model.value.rangeTime, model.value.type], (newVal) => {
  if (newVal) {
    tableSearch();
  }
});
</script>

<style lang="less" scoped>
@import "@/styles/default.less";
.goods-wrapper {
    width: 100%;
    height: 100%;
    .header {
      height: 86px;
      border-bottom: 1px solid @default-border-color;
    }
    .goods-table {
      height: calc(100% - 86px);
    }
}
</style>
