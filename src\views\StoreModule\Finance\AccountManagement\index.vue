<template>
  <MenuLayout v-model:activeKey="activeTypeRef" :menuOptions="menuList">
    <component :is="currentPage" />
  </MenuLayout>
</template>

<script setup lang="tsx">
import { ref, watch, computed } from "vue";
import { AccountManagementTypeEnum } from "@/enums";
/** 相关组件 */
import MenuLayout from "@/components/MenuLayout/index.vue";

import SubAccountPayeeManagement from "@/views/StoreModule/Finance/AccountManagement/components/SubAccountPayeeManagement/index.vue";
import SubBillManagement from "@/views/StoreModule/Finance/AccountManagement/components/SubBillManagement/index.vue";
import SplitBillSettlement from "@/views/StoreModule/Finance/AccountManagement/components/SplitBillSettlement/index.vue";
import SubAccountPayeeRuleSetting from "@/views/StoreModule/Finance/AccountManagement/components/SubAccountPayeeRuleSetting/index.vue";
import {
  hasFinanceAllocationAccountManagementAuth,
  hasFinanceAllocationSettlementAuth,
  hasFinanceStoreConfigAccountCreateBillManagementAuth,
  hasSubAccountPayeeRuleSettingAuth,
} from "@/views/StoreModule/Finance/authList";
/** props */
const props = defineProps<{
  configurationAddressStatus?: number, // 配置地址跳转状态
}>();


/** 获取到的标签数据 */
const menuList = ref([
  {
    label: '分账方规则设置',
    key: AccountManagementTypeEnum.SubAccountPayeeRuleSetting,
    show:hasSubAccountPayeeRuleSettingAuth
  },
  {
    label: '分账入账方管理',
    key: AccountManagementTypeEnum.SubAccountPayeeManagement,
    show:hasFinanceAllocationAccountManagementAuth
  },
  {
    label: '分账单管理',
    key: AccountManagementTypeEnum.SubBillManagement,
    show:hasFinanceStoreConfigAccountCreateBillManagementAuth
  },
  {
    label: '分账单结算',
    key: AccountManagementTypeEnum.SplitBillSettlement,
    show:hasFinanceAllocationSettlementAuth
  },
]);

const activeTypeRef = ref(menuList.value.filter(item => item.show).map(item => item.key)[0]);

/** 相关组件 */
const pageMap = {
  [AccountManagementTypeEnum.SubAccountPayeeManagement]: SubAccountPayeeManagement,
  [AccountManagementTypeEnum.SubBillManagement]: SubBillManagement,
  [AccountManagementTypeEnum.SplitBillSettlement]: SplitBillSettlement,
  [AccountManagementTypeEnum.SubAccountPayeeRuleSetting]: SubAccountPayeeRuleSetting,
}

/** 当前页 */
const currentPage = computed(() => pageMap[activeTypeRef.value])

</script>

<style lang="less" scoped></style>
