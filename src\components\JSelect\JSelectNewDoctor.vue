<template>
  <JSelect
    :value="props.value"
    :loading="isLoading"
    :onFocus="handlerFocus"
    :onBlur="handlerBlur"
    :options="shipCompanyList"
    :onClear="handleClear"
    :multiple="isMultiple"
    :max-tag-count="maxTagCount"
    :display-quantity="props.isMultiple === false ? 0 : maxTagCount"
    @update:value="onChange"
    :style="{ width }"
    placeholder="请选择医生"
    :filter="customFilter"
    @scroll="handleScroll"
    @keydown.enter="handleSearch"
    :reset-menu-on-options-change="isResetSelectStatusRef"
    
  />
</template>
<script setup lang="ts">
import { ref, watch, } from "vue";
import JSelect from "@/components/JSelect/index.vue";
import { organizationList, doctorEntityPageCommon } from "@/services/api";
import { useMessages } from "@/hooks";
import { isArray } from "@/utils/isUtils";
/* Props */
const props = withDefaults(
  defineProps<{
    isImmediately?: boolean; // 是否立即加载
    value: Array<string | number> | string | number | null; // 选择的值
    isMultiple?: boolean; // 是否多选 --> 非必传，默认值为false
    forbiddenId?: Array<string> | null; // 禁用的id
    width?: string | number ; // 宽度
    isSearch?:boolean; //是否用于搜索
    // 动态标签格式字符串（支持字段：doctorName, mobile, doctorCode, institutionName）
    labelFormat?: string; // 如：'姓名：{doctorName} 手机：{mobile}'
  }>(),
  {
    isImmediately: false,
    isMultiple: false,
    forbiddenId: null,
    width: 460,
    isSearch: false,
    labelFormat: undefined
  },
);

const emits = defineEmits<{
  (e: "update:value", selectValue: any): void; // 更新选择值事件
}>();

/* 提示 */
const message = useMessages();

/* 是否加载 */
const isLoading = ref(false);
const isResetSelectStatusRef = ref(false); // 重置选择状态
const shipCompanyList = ref([]); // 列表
const maxTagCount = ref(2);
const selectedValue = ref([]) //已经被选中的selectedValue

/* 执行搜索返回的内容*/
let recordsTotal = 1; // 总记录数

const params: { data:{doctorName:string|number, requestScene:number},pageVO: { current: number; size: number } } = {
  data:{
      doctorName:'',
      requestScene:1
  },
  pageVO: {
    current: 1, // 当前页
    size: 100, // 每页大小
  },
};

/* 筛选、转化{label: '', value: ''} */
function handleData(filterData) {
  return filterData.map(item => {
    if (isArray(props.forbiddenId) && props.forbiddenId.includes(item.id)) {
      return { 
        label: item.doctorName, 
        value: item.id, 
        disabled: !item.isActived
      };
    }

    // 动态格式处理
    let label = '';
    
    if (props.labelFormat) {
      label = props.labelFormat
        .replace(/\{doctorName\}/g, item.doctorName || '')
        .replace(/\{mobile\}/g, item.mobile || '')
        .replace(/\{doctorCode\}/g, item.doctorCode || '')
        .replace(/\{institutionName\}/g, item.institutionName || '');
      
      // 移除连续分隔符和首尾符号
      label = label
        .replace(/-+$|]+$|^\-+|^\]+/g, '')  // 清理多余符号
        .replace(/\s+/g, ' ');              // 规范空格
    } else {
      // 默认格式保持兼容
      let parts = [];
      if (item.doctorName) parts.push(`[${item.doctorName}]`);
      if (item.mobile) parts.push(`-[${item.mobile}]`);
      if (item.doctorCode) parts.push(`-[${item.doctorCode}]`);
      if (item.institutionName) parts.push(`-[${item.institutionName}]`);
      label = parts.join('');
    }

    return {
      label: label || '未知医生',
      value: item.id,
      disabled: !item.isActived
    };
  });
}
/* 获取商品分类列表 */
async function getMemberTagList() {
  try {
    isLoading.value = true;
    const { total, current, size, records } = await doctorEntityPageCommon(params);
    params.pageVO.current = Number(current);
    params.pageVO.size = Number(size);
    recordsTotal = Number(total);
    // 如果是第一页
    if (params.pageVO.current == 1) {
      isResetSelectStatusRef.value = true; // 重置选择状态为true
      shipCompanyList.value = handleData(records);
    } else {
      isResetSelectStatusRef.value = false; // 重置选择状态为false
      handleData(records).forEach(item => {
        // 如果列表中不存在该项
        if (!shipCompanyList.value.find(temp => temp.value == item.value)) {
          // 添加到列表中
          shipCompanyList.value.push(item);
        }
      });
    }

    // 判断是否有已选中内容
    if(selectedValue.value.length && props.value){
      selectedValue.value.forEach(item => {
        if (!shipCompanyList.value.some(existingItem => existingItem.value === item.value)) {
          shipCompanyList.value.push(item);
          selectedValue.value = []
        }
      });
    }
  } catch (error) {
    message.createMessageError("获取医生信息失败：" + error);
  } finally {
    isLoading.value = false;
  }
}

/** 自定义过滤函数 */
function customFilter(keyword, options) {
  const labelMatch = options.label.toLowerCase().includes(keyword.toLowerCase());
  const valueMatch = options.value.toLowerCase().includes(keyword.toLowerCase());
  return labelMatch || valueMatch;
}

/** 选择值改变事件处理函数 */
function onChange(value,label) {
  // 额外的全选逻辑
  if (props.isMultiple) {
    if (value?.includes("all")) {
      let newVal = shipCompanyList.value.filter(item => item.value !== "all").map(item => item.value);
      emits("update:value", newVal);
      return;
    }
  }
  selectedValue.value = [label]
  emits("update:value", value);
  handleClearName()
}

/** 清空事件处理函数 */
const handleClear = () => {
  emits("update:value", null);
  handleClearName()
};

/** 滚动事件处理函数 */
function handleScroll(e) {
  const currentTarget = e.currentTarget as HTMLElement;
  if (currentTarget.scrollTop + currentTarget.offsetHeight >= currentTarget.scrollHeight) {
    // 如果当前页乘以每页大小小于总记录数
    if (params.pageVO.current * params.pageVO.size < recordsTotal) {
      params.pageVO.current++; // 当前页加1
      getMemberTagList();
    }
  }
}

/** 聚焦事件处理函数 */
function handlerFocus() {
  // 如果会员标签列表为空
  if (!shipCompanyList.value.length) {
    getMemberTagList();
  }
}

/** 失焦事件处理函数 */
function handlerBlur(){
  handleClearName()
}

/** 搜索事件处理函数 */
function handleSearch(event) {
  params.pageVO.current = 1;
  params.data.doctorName = event.target.value;
  getMemberTagList();
}

/** 清除搜索名称函数 */
const handleClearName = () =>{
  if(params.data.doctorName != ''){
    params.data.doctorName = ''
    getMemberTagList();
  }
}

/** 监听 */
watch(
  () => props.isImmediately,
  newVal => {
    if (newVal) {
      getMemberTagList();
    }
  },
  { immediate: true },
);

</script>

<style scoped lang="less"></style>
