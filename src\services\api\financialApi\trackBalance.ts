import { defHttp } from '@/services';

/** 财务管理之轨迹查询余额 */
export const enum TrackBalanceApi {
    page = "/traceRechargeRecord/pageCount"
}

export interface TrackBalance {
    businessId: string;
    platformName: string;
    remainTimes: number;
    totalMoney: number;
    totalTimes: number;
}
interface TrackBalanceResponse {
    total: string,
    current: string,
    size: string,
    records: Array<TrackBalance>
}
export function getTrackBalance(params = {}) {
    return defHttp.post<TrackBalanceResponse>({
        url: TrackBalanceApi.page,
        params,
    });
}