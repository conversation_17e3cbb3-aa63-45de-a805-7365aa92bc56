import { defHttp } from "@/services";

/** 积分商品 */
export const enum IntegralGoodsApi {
    page = "/pointSpec/page",
    getSpec = "/productSpec/queryNonPointProduct",
    add = "/pointSpec/add",
    get = "/pointSpec/get",
    update = "/pointSpec/update",
    batch = "/pointSpec/batch/publish",
    deletePointCate = "/product/cate/delete/point",
    delete = "/pointSpec/delete"
}

/**
 * @description 积分商品删除
 */
export function deletePointGoodsById(_params: {
    pointProductIds: string;
}) {
    return defHttp.delete({
        url: IntegralGoodsApi.delete,
        requestConfig: {
            isQueryParams: true,
        },
        params: _params,
    });
}

/**
 * @description 积分商品删除分类
 */
export function deletePointCateById(_params: {
    id: string;
    updateId?: string;
}) {
    return defHttp.delete({
        url: IntegralGoodsApi.deletePointCate,
        requestConfig: {
            isQueryParams: true,
        },
        params: _params,
    });
}

/**
 * @description 分页查询积分商品
 */
export function getPointSpecPage(params) {
    return defHttp.post({
        url: IntegralGoodsApi.page,
        params,
    });
}

/**
 * @description 根据商品ID查询未添加商品规格
 */
export function getProductSpecByProductIds(id: string) {
    return defHttp.get({
        url: IntegralGoodsApi.getSpec,
        requestConfig: {
            isQueryParams: true,
        },
        params: {
            productId: id
        },
    });
}

/**
 * @description 新增积分商品规格
 */
export function addProductSpec(_params) {
    return defHttp.post({
        url: IntegralGoodsApi.add,
        params: {
            data: _params,
        },
    });
}

/**
 * @description 根据积分商品ID查询详情
 */
export function getProductSpec(id: string) {
    return defHttp.get({
        url: IntegralGoodsApi.get + "?pointProductId=" + id,
    });
}

/**
 * @description 修改积分商品
 */
export function updateProductSpec(_params) {
    return defHttp.put({
        url: IntegralGoodsApi.update,
        params: {
            data: _params,
        },
    });
}

/**
 * @description 积分商品批量上下架
 */
export function batchUnmountProduct(_params: {
    productIds: string;
    isPublish: 0 | 1;
    cateIds: string;
}) {
    return defHttp.put({
        url: IntegralGoodsApi.batch + `?productIds=${_params.productIds}&isPublish=${_params.isPublish}&cateIds=${_params.cateIds}`
    });
}