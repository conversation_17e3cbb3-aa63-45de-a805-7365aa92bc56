import { defHttp } from "@/services";

/** 观看时长 */
export const enum ViewDataApi {
  page = "/shareRecord/page",
  export = "/shareRecord/export",
  getViewDataDetails = "/shareRecord/enterExitDetail",
  storePage = "/storeEntity/page",
}

/**
 * @description 观看数据列表分页查询
 */
export function getViewDataPage(params) {
  return defHttp.post({
    url: ViewDataApi.page,
    params,
  });
}
/**
 * @description 观看数据统计导出
 */
export function viewDataExport(params) {
  return defHttp.post({
    url: ViewDataApi.export,
    requestConfig: {
      responeseType: "stream",
    },
    params,
  });
}
/**
 * @description 观看进出详情
 */
export function getViewDataDetails(params) {
  return defHttp.get({
    url: ViewDataApi.getViewDataDetails,
    params,
  });
}
/**
 * @description 分页查询门店列表
 */
export function getStorePage(params) {
  return defHttp.post({
    url: ViewDataApi.storePage,
    params,
  });
}
