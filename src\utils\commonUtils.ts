//深拷贝对象
export function duplicateNewCopy(data:object|[]){
    if(typeof data !== 'object' || data === null){
        return data
    }
    const result = Array.isArray(data) ? [] : {}
    for(let key in data){
        if(data.hasOwnProperty(key)){
            result[key] = duplicateNewCopy(data[key])
        }
    }
    return result
}

/**
 * @description 动态引入图片
 * @param {string} image 
 */

export const getAssetUrl = (image: string) => {
    return new URL(`../assets/image/system/home/<USER>
};

export const getAnalyseAssetUrl = (image: string) => {
    return new URL(`../assets/image/overview/${image}.png`, import.meta.url).href;
};


export function createDummyId(){
    return Math.random().toString(16).slice(2)
}

/**
 * @description 深度克隆
 */
export function deepClone<T>(value: T): T {
  /** 空 */
  if (!value) return value;
  /** 数组 */
  if (Array.isArray(value)) return value.map((item) => deepClone(item)) as unknown as T;
  /** 日期 */
  if (value instanceof Date) return new Date(value) as unknown as T;
  /** 普通对象 */
  if (typeof value === 'object') {
    return Object.fromEntries(
      Object.entries(value).map(([k, v]: [string, any]) => {
        return [k, deepClone(v)];
      })
    ) as unknown as T;
  }
  /** 基本类型 */
  return value;
}

/**
 *
 * @param length `uuid` 长度
 * @param radix `uuid` 基数
 * @returns `uuid`
 */
export const uuid = (length = 16, radix = 62) => {
	// 定义可用的字符集，即 0-9, A-Z, a-z
	const availableChars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split("");
	// 定义存储随机字符串的数组
	const arr: string[] = [];
	// 获取加密对象，兼容 IE11
	const cryptoObj = window.crypto;
	let i = 0;

	// 循环 length 次，生成随机字符，并添加到数组中
	for (i = 0; i < length; i++) {
		// 生成一个随机数
		const randomValues = new Uint32Array(1);

		cryptoObj.getRandomValues(randomValues);

		// 根据随机数生成对应的字符，并添加到数组中
		const index = randomValues[0] % radix;

		arr.push(availableChars[index]);
	}

	// 将数组中的字符连接起来，返回最终的字符串
	return arr.join("");
};
  
export function formatTime(time: number): string {
    if (time === 0) {
      return "00:00:00";
    }
    const h = Math.floor(time / 3600);
    const minute = Math.floor((time / 60) % 60);
    const second = Math.ceil(time % 60);

    const hours = h < 10 ? "0" + h : h;
    const formatSecond = second > 59 ? 59 : second;

    return `${Number(hours) > 0 ? `${hours}:` : "00:"}${minute < 10 ? "0" + minute : minute}:${
      formatSecond < 10 ? "0" + formatSecond : formatSecond
    }`;
}