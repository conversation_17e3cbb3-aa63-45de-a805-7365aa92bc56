export const _syncRouteConfig = [
    "sg.store.login",
    "sg.store.check",
    "sg.store.exception.403",
    "sg.store.exception.404",
    "sg.store",
    "sg.store.doctorend.login",
    "sg.store.live.dashboard",
    {
        "name": "sg.store",
        "children": [
            {
                "name": "sg.store.store.module",
                "children": [
                    {
                        "name": "sg.store.store.index",
                    },
                    {
                        "name": "sg.store.goods.management",
                    },
                    
                    {
                        "name": "sg.store.supplierProduct.management",
                    },
                    {
                        "name": "sg.store.customer.management",
                    },
                    {
                        "name": "sg.store.prescription.management",
                    },
                    {
                        "name": "sg.store.doctor.management",
                    },
                    {
                        "name": "sg.store.order.management",
                    },
                    {
                        "name": "sg.store.sales.management",
                    },
                    {
                        "name": "sg.store.after.service.management",
                    },
                    {
                        "name": "sg.store.data.reports",
                    },
                    {
                        "name": "sg.store.finance",
                    },
                    {
                        "name": "sg.store.order.storeConfig",
                    },
                    {
                        "name": "sg.store.store.basic.config",
                    },
                    {
                        "name": "sg.store.supplier.management",
                    },
                ]
            },
            {
                "name": "sg.store.shop.module",
                "children": [
                    {
                        "name": "sg.store.shop.index",
                    },
                    {
                        "name": "sg.store.shop.management",
                    },
                    {
                        "name": "sg.store.personnel.management",
                    },
                    {
                        "name": "sg.store.shop.storeLogistics",
                    },
                    {
                        "name": "sg.store.shop.return.logistics",
                    },
                    {
                        "name": "sg.store.dealer.management",
                    },
                    {
                        "name": "sg.store.dealer.group",
                    }
                ]
            },
            {
                "name": "sg.store.content.module",
                "children": [
                    {
                        "name": "sg.store.video.management",
                    },
                    {
                        "name": "sg.store.comment.management",
                    },
                    {
                        "name": "sg.store.lexicon.config",
                    },
                    {
                        "name": "sg.store.content.config",
                    },
                ]
            },
            {
                "name": "sg.store.structre.module",
                "children": [
                    {
                        "name":"sg.store.structre.settings",
                    },
                    {
                        "name":"sg.store.structre.memberManagement",
                    },
                    {
                        "name":"sg.store.structre.commissionDeatail"
                    },
                    {
                        "name":"sg.store.structre.department",
                    },
                    {
                        "name":"sg.store.structre.application",
                    }
                ]
            },
            {
                "name": "sg.store.finance",
                "children": [
                    {
                        "name": "sg.store.finance.merchant.management",
                    },
                    {
                        "name": "sg.store.finance.recharge",
                    },
                    {
                        "name": "sg.store.finance.allocation.management",
                    },
                    {
                        "name": "sg.store.finance.logistics.recharge",
                    },
                    {
                        "name": "sg.store.finance.commission.rule",
                    },
                    {
                        "name": "sg.store.finance.commission.detail",
                    },
                    {
                        "name": "sg.store.finance.payment.review",
                    }
                ]
            },
            {
                "name": "sg.store.doctorModule",
                "children": [
                    {
                        "name": "sg.store.prescription.management",
                    },
                    {
                        "name": "sg.store.doctor.management",
                    },
                    {
                        "name": "sg.store.pharmacists.management",
                    },
                    {
                        "name": "sg.store.organization.management",
                    },
                    {
                        "name": "sg.store.department.config",
                    },
                    {
                        "name": "sg.store.disease.management",
                    },
                    {
                        "name": "sg.store.basic.setting",
                    },
                    {
                        "name": "sg.store.medical.inquiry.form",
                    },
                    {
                        "name": "sg.store.medical.inquiry.prescription",
                    },
                    {
                        "name": "sg.store.assistant.doctor.management",
                    }
                ]
            },
            {
                "name": "sg.store.pharmacist.prescription.module",
                "children": [
                    {
                        "name": "sg.store.pharmacist.prescription"
                    }
                ]
            },
            {          
                "name": "sg.store.liveModule",
                "children": [
                    {
                        "name": "sg.store.live.list",
                    },
                    {
                        "name": "sg.store.live.sensitive.words",
                    },
                    {
                        "name": "sg.store.view.data.statistics",
                    },
                    {
                        "name": "sg.store.watch.seconds.page",
                    }
                ]
            }      
        ]
    }
]