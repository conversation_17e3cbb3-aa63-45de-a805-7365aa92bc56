import type { organizeForm } from "@/views/Distribution/OrganizationalStructure/hooks/type";
import { MemberType } from "@/enums";

export default function Organize() {

  const DepartmentType_options = [
    {
      label: '经销商',
      value: 0,
    },
    {
      label: '门店',
      value: 1
    },
    // {
    //   label: '公司',
    //   value: 2
    // },
    // {
    //   label: '直播间',
    //   value: 3
    // },
    // {
    //   label: '市场',
    //   value: 4
    // },
    // {
    //   label: '运营',
    //   value: 5
    // },
    {
      label: '大区',
      value: 6
    },
    {
      label: '区域',
      value: 7
    },
    // {
    //   label: '财务',
    //   value: 8
    // },
  ]
  const storeStatusRadio = [
    {
      value:0,
      label:"停用"
    },
    {
      value:1,
      label:"启用"
    }
  ]
  const organizeFormObj = {
    name: "",
    title:"",
    departmentType: null,
    managerName: "",
    managerPhone: null,
    managerId: null,
    storeEntityVO: {
      storeName: "",
      storeStatus: 1,
      storeAvatar: "",
      contactName: "",
      contactPhone: "",
      businessHours: "",
      managerId:"",
      addressOptions: {
        provinceId: null,
        province: null,
        cityId: null,
        cityName: null,
        areaId: null,
        area: null,
      },
      storeStaffRelationList:[]
    }
  }
  function assignmentObj(data:organizeForm){
    return {
      title:"编辑组织",
      id:data.id,
      createTime:data.createTime,
      updateTime:data.updateTime,
      code:data.code,
      name:data.name,
      parentCode:data.parentCode,
      level:data.level,
      path:data.path,
      isDeleted:data.isDeleted,
      createBy:data.createBy,
      updateBy:data.updateBy,
      departmentType:data.departmentType,
      useStatus:data.useStatus,
      hasChildren:data.hasChildren,
      storeEntityVO:{
        id: data.storeEntityDTO.id,
        storeName: data.storeEntityDTO.storeName,
        storeStatus: data.storeEntityDTO.storeStatus,
        storeAvatar: data.storeEntityDTO.storeAvatar || '',
        contactName: data.storeEntityDTO.contactName,
        contactPhone: data.storeEntityDTO.contactPhone,
        businessHours: data.storeEntityDTO.businessHours,
        managerId:data.storeEntityDTO.managerId,
        addressDetail:data.storeEntityDTO.addressDetail,
        addressOptions: {
          provinceId: data.storeEntityDTO.provinceId,
          province: data.storeEntityDTO.province,
          cityId: data.storeEntityDTO.cityId,
          cityName: data.storeEntityDTO.city,
          areaId: data.storeEntityDTO.districtId,
          area: data.storeEntityDTO.area,
          remark:''
        },
        storeStaffRelationList:data.storeEntityDTO.storeStaffRelationList || []
      }
    }
  }
  return {
    DepartmentType_options,
    storeStatusRadio,
    organizeFormObj,
    assignmentObj
  }
}
