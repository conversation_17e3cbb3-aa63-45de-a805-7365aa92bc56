<template>
    <JModal
        v-model:show="isShow"
        width="530"
        :title="modalType == 'add' ? '创建物流单' : '编辑物流单'"
        @after-leave="closeModal"
        @positive-click="_submit"
        :positiveButtonProps="{
            loading: props.isAddLoading
        }"
    >
        <n-form
            ref="formRef"
            :rules="rules"
            :model="model"
            label-width="auto"
            label-placement="left"
            require-mark-placement="right-hanging"
            :style="{
                width: '100%',
                }"
            >
            <n-grid :cols="8" :x-gap="24">
              <n-form-item-gi :span="24" label="店铺名称" label-placement="left" path="storeId" required>
                  <JStoreSelect width="100%" v-model:value="model.storeId" placeholder="请选择店铺"></JStoreSelect>
              </n-form-item-gi>
              <n-form-item-gi :span="24" label="商品名称"  path="productName" required>
                <n-input v-model:value="model.productName" width="100%" maxlength="40" placeholder="请输入商品名称" clearable/>
              </n-form-item-gi>
              <n-form-item-gi :span="24" label="商品ID"  path="productId" required>
                <n-input v-model:value="model.productId" width="100%" maxlength="19" placeholder="请输入商品ID" clearable />
              </n-form-item-gi>
              <n-form-item-gi :span="24" label="SKU名称">
                <n-input v-model:value="model.sku" width="100%" maxlength="40" placeholder="请输入SKU" clearable/>
              </n-form-item-gi>
              <n-form-item-gi :span="24" label="发货数量"  path="deliveryQuantity" required>
                <n-input-number v-model:value="model.deliveryQuantity" width="100%" :show-button="false" precision="0" placeholder="请输入发货数量" min="1" max="10000" clearable/>
              </n-form-item-gi>
              <n-form-item-gi :span="24" label="物流公司" label-placement="left" path="logisticsCompany" required>
                <JExpressCompany v-model:value="model.logisticsCode" v-model:shipCompanyName="model.logisticsCompany" placeholder="请选择物流公司"  isImmediately style="width: 100%;" />
              </n-form-item-gi>
              <n-form-item-gi :span="24" label="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;收件人" path="addressee" required v-if="model.logisticsCode == 'SF' || model.logisticsCode == 'ZTO'">
              <n-input v-model:value="model.addressee" width="100%" maxlength="20"  placeholder="请输入收件人" clearable/>
              </n-form-item-gi>
              <n-form-item-gi :span="24" label="收件人手机号" path="mobile" required v-if="model.logisticsCode == 'SF' || model.logisticsCode == 'ZTO'">
                <n-input v-model:value="model.mobile" width="100%" maxlength="11" placeholder="请输入收件人手机号" clearable :disabled="isMobile"/>
              </n-form-item-gi>
              <n-form-item-gi :span="24" label="物流单号" path="logisticsNumber" required >
                <n-input v-model:value="model.logisticsNumber" type="textarea" placeholder="不同物流单号用;隔开,最多支持20个"/>
              </n-form-item-gi>
            </n-grid>
        </n-form>
    </JModal>
</template>

<script setup lang="ts">
import { ref,computed, watch } from 'vue';
import { useMessages } from '@/hooks';
import JWelfareClassify from '@/components/JSelect/JWelfareClassify.vue';
import JStoreSelect from '@/components/JSelect/JStoreSelect.vue';
import { logisticsOrder } from '../hooks/logisticsOrder'
import { REGEXP_PHONE } from "@/config";
import { isNumber } from "@/utils/isUtils";
const { createMessageError } = useMessages();
const { 
  isMobile
} = logisticsOrder(); 
import dayjs from 'dayjs';
const message = useMessages();
const props = withDefaults(defineProps<{
    show?: boolean;
    isAddLoading?: boolean;
    modalType?:string;
    row?:object

}>(), {
    show: false,
    isAddLoading: false,
    modalType:'',
  });
const emits = defineEmits<{
    (e: 'update:show', value: boolean): void;
    (e: 'refresh'): void;
    (e: 'onSubmit',value:any): void;
}>();
const showMinute = ref(false)
const isShow = computed({
    get: () => props.show,
    set: (value: boolean) => {
      emits('update:show', value);
    }
});

const initParams = {
  id:null,
  storeId:null,
  storeName: "",
  productName:null,
  productId:null,
  sku:null,
  deliveryQuantity:null,
  logisticsCode:null,
  logisticsCompany:'',
  logisticsNumber:'',
  addressee:'',
  mobile:null
};
const model = ref({ ...initParams });

/* 表单规则 */
const rules = {
  storeId:{
    required: true,
    trigger: ["blur", "change"],
    message: "请选择店铺",
  },
  productName:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入商品名称",
  },
  productId:{
    required: true,
     trigger: ["blur", "change"],
     validator(rule, value) {
       if (!value) {
         return new Error('请填写商品ID')
       }
       else if (!/^\d+$/.test(value)) {
         return new Error('商品ID必须为整数')
       }
       return true
     }
  },
  deliveryQuantity:{
    type:"number",
    required: true,
    trigger: ["blur", "change"],
    message: "请填写发货数量",
  },
  logisticsCompany:{
    required: true,
    trigger: ["blur", "change"],
    message: "请选择物流公司",
  },
  addressee:{
    required: true,
    trigger: ["blur", "change"],
    message: "请填写收件人",
  },
  mobile:{
    required: true,
    trigger: ["blur", "change"],
    message: "请填写手机号",
  },
  logisticsNumber:{
    type: "string",
    required: true,
    trigger: ["blur", "change"],
    message: "请填写物流单号",
  },

};
const watchMinuteDis = ref(false)
// 关闭按钮
const closeModal = () => {
  isShow.value = false;
  showMinute.value = false
  model.value = { ...initParams };
}

// 确认按钮
const formRef = ref(null); 
const _submit = async () => {
    formRef.value?.validate(async (errors: any) => {
        if (!errors) {
          // 校验手机号
          if (props.modalType == 'add' && model.value.mobile && !isNumber(model.value.mobile) && !REGEXP_PHONE.test(model.value.mobile)) {
            createMessageError("请输入正确的手机号");
            return;
          }
          const max = 20;
          const codes = model.value.logisticsNumber.split(';')
          if (codes.length > max) {
            createMessageError("不同物流单号用;隔开，最多支持20个");
            return;
          }
          emits("onSubmit",{type:props.modalType,data:model.value})
        }
    });
}
function isMaskedPhone(phone) {
  const regex = /^1[3-9]\d\*{4}\d{4}$/;
  return regex.test(phone);
}
watch(()=>props.show, (newVal, oldVal) => {
  isMobile.value = false
  if(newVal && props.modalType === 'edit' ){
    const rowData = {
      id: props.row.id,
      storeId: props.row.storeId,
      productId:props.row.productId,
      productName:props.row.productName,
      sku:props.row.sku,
      deliveryQuantity:Number(props.row.deliveryQuantity),
      logisticsCode:props.row?.logisticsCode || null,
      logisticsCompany:props.row.logisticsCompany,
      logisticsNumber:props.row.logisticsNumber,
      addressee:props.row.addressee,
      mobile:props.row.mobile,
    }
    Object.assign(model.value, rowData);
    // if(model.value.logisticsCode == 'SF' || model.value.logisticsCode == 'ZTO'){
      const isMasked = isMaskedPhone(model.value.mobile)
      if(isMasked){
        isMobile.value = true
      }
      
    // }
    
  }else{
  }
});

// watch(()=>model.value.totalQuantity, (newVal, oldVal) => {
//   if(model.value.totalQuantity == -1){
//       model.value.isInfinite = model.value.totalQuantity == -1 ? true : false
//   }
// });

</script>