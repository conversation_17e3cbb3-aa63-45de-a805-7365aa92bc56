<template>
	<JModal
		v-model:show="show"
		:auto-focus="false"
		:bordered="false"
		:closable="true"
		width="800"
		height="580"
		@after-enter="handleAfterEnter"
		:positive-text="null" 
        :negative-text="null"
	>
	    <!-- 标题 -->
	    <template #header>
          <div style="display: flex;">
			<span style="margin-right: 12px;">新增产品</span>
			<n-button text type="primary" @click="openAddOrEditProduct('add')">找不到产品？新创建</n-button>
		  </div>
        </template>
		<!-- 内容 -->
		<FormLayout
          class="inner-page-height"
          :isLoading="isLoading"
          :tableData="tableData"
          :tableColumns="tableColumns"
          :pagination="paginationRef"
          @paginationChange="paginationChange"
          :isNeedCollapse="false"
		  :is-table-selection="false"
		  style="height: 100%;"
        >
          <!-- 表单 -->
          <template #searchForm>
            <n-form
              ref="formRef"
              label-placement="left"
              label-width="auto"
              :show-feedback="false"
              require-mark-placement="right-hanging"
              size="small"
              :style="{ width: '100%' }"
            >
              <n-form-item :span="12" :show-label="false" path="">
                <j-search-input
                  v-model:value="model.name"
                  placeholder="请输入产品名称"
                  @search="handlerSearch"
                />
              </n-form-item>
            </n-form>
          </template>
      
          <template #tableHeaderBtn>
            <n-button @click="refresh" class="store-button">刷 新</n-button>
          </template>
        </FormLayout>
        <!-- 产品添加 -->
        <AddProduct ref="addProductRef" @add-succeed="(row) => handleAdd(row)" />
	</JModal>
</template>

<script setup lang="tsx" name="AddTherapyProductList">
import { ref } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { useMessages } from '@/hooks';
import { getTherapyGoodsPage, therapyGoodsDelete } from "@/services/api";
/** 相关组件 */
import AddProduct from "./components/AddProduct.vue";
import JImage from "@/components/JImage/index.vue";

interface ModalProps {
  refresh?: () => void; // 刷新表格
}

const { createMessageSuccess, createMessageError } = useMessages();

/** props */
const props = withDefaults(defineProps<{
	checkedKeys: Array<string | number>;
}>(),{
	checkedKeys: () => []
});

/** emits */
const emits = defineEmits<{
	(e: 'checkAddProduct', value: Partial<{
		dosage: string;
        id: string;
        imgPath: string;
        name: string;
	}>): void;
}>();

/** 表格hook */
const {
  isAddLoading,
  isEditLoading,
  isLoading,
  tableData,
  paginationRef,
  pageTableData,
  deleteTableData,
  editTableData,
  addTableData,
  refreshTableData,
  paginationChange,
} = useTableDefault({
  pageDataRequest: getTherapyGoodsPage,
});

const show = ref(false);
const modalProps = ref<ModalProps>({});

/** 参数 */
const model = ref({
  name: '',
});

/** 表格列表项 */
const tableColumns = [
	{
		title: "产品图",
		key: "memberName",
		width: 80,
		align: "left",
		render: row => {
          return <JImage imgPath={row.imgPath} />;;
        },
	},
	{
		title: "产品名称",
		key: "name",
		align: "left",
	},
	{
		title: "服用方式",
		key: "dosage",
		width: 120,
		align: "left",
	},
	{
        title: "操作",
        key: "action",
        width: 120,
        align: "left",
		fixed: "right",
        render: row => {
          return (
            <n-space>
				{/* 添加 */}
                <n-button 
				    text 
				    type="primary" 
				    onClick={() => handleAdd(row)}
					disabled={props.checkedKeys.includes(row?.id)}
				>
					{ !props.checkedKeys.includes(row?.id) ? '添加' : '已添加' }
				</n-button>
				{/* 修改 */}
				<n-button text type="primary" onClick={() => openAddOrEditProduct('edit', row)}>修改</n-button>
				{/* 删除 */}
                <n-popconfirm onPositiveClick={() => handleDelete(row?.id)}>
	    	    	{{
	    	    		default: () => {
	    	    			return (
	    	    				<span>{`确认删除《${row.name}》产品吗？`}</span>
	    	    			);
	    	    		},
	    	    		trigger: () => {
	    	    			return (
	    	    				<n-button text type="error">
	    	    					删除
	    	    				</n-button>
	    	    			);
	    	    		},
	    	    	}}
	    	    </n-popconfirm>
            </n-space>
          );
        },
    }
];

/** 添加 */
const handleAdd = (row: Partial<{
	dosage: string;
    id: string;
    imgPath: string;
    name: string;
}>) => {
	emits('checkAddProduct', row);
};

/** Modal 出现后的回调 */
const handleAfterEnter = () => {
	paginationRef.value.pageSize = 10;
	tableSearch();
};

/** 添加产品与编辑 */
const addProductRef = ref<InstanceType<typeof AddProduct> | null>(null);
const openAddOrEditProduct = (type: 'add' | 'edit', row: Partial<{ label: string, key: string }> = {}) => {
    const params = {
        type,
        row,
		refresh: refresh,
    };
    addProductRef.value?.acceptParams(params);
};

/** 删除商品 */
const handleDelete = async (id: string) => {
	try {
		// 判断当前是否添加
		if (props.checkedKeys.includes(id)) {
			createMessageError("该产品已被疗法引用，不支持删除!");
			return;
		}
		await therapyGoodsDelete({id});
		createMessageSuccess("删除产品成功");
		refresh();
	} catch (error) {
		createMessageError("删除产品失败：" + error);
	}
};

/** 搜索 */
const handlerSearch = () => {
  tableSearch();
};

/** 表格刷新 */
function refresh(){
  tableSearch();
};

/** 获取参数 */
const getParams = () => {
	const { name  } = model.value;
	return {
		name
	}
};

/* 表格搜索 */
const tableSearch = async () => {
  await pageTableData(getParams(), paginationRef.value);
};

/* 接收父组件传过来的参数 */
const acceptParams = (params: ModalProps) => {
	modalProps.value = params;
	show.value = true;
};

defineExpose({
	acceptParams,
});
</script>

<style lang="less" scoped>

.infoWrapper {
	width: 210px;
	text-align: center;
	box-sizing: border-box;
}

.notice {
	color: #333333;
	line-height: 29px;
	font-size: 16px;
}

.title {
	font-size: 16px;
	margin-bottom: 20px;

	.count {
		font-size: 20px;
		font-weight: bold;
	}
}
</style>
