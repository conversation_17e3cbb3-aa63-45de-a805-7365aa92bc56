<template>
  <n-modal
    v-model:show="showModal"
    :auto-focus="false"
    style="width: 600px"
    :bordered="false"
    size="small"
    @after-leave="closeModal"
    preset="card"
    :closable="false"
  >

  </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, toRefs, onMounted, computed} from 'vue';
import { paymentAuditReject } from '@/services/api';
import { useMessages } from "@/hooks";
const { createMessageSuccess, createMessageError } = useMessages();

const isLoading = ref(false);

const props = withDefaults(defineProps<{
  show: boolean;
  auditReasonList: Array<any>;
    AuditfrontName:string;
}>(), {
  show: false,
  auditReasonList: () => [],
  AuditfrontName: '-',
});
const emits = defineEmits<{
    (e: 'update:show', value: boolean): void;
    (e: 'success'): void;
}>();


const showModal = computed({
    get() {
    return props.show;
    },
    set(value) {
    emits("update:show", value);
    },
})

const closeModal = () => {
    showModal.value = false;
}
</script>
<style scoped lang="less"></style>
