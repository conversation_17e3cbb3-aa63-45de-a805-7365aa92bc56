<template>
  <n-modal
    v-model:show="showModal"
    :auto-focus="false"
    style="width: 600px"
    :bordered="false"
    size="small"
    @after-leave="closeModal"
    preset="card"
    :closable="false"
    :title="`[${AuditfrontName}商品]审核说明`"
  >
    <template #header-extra>
      <n-button quaternary circle @click="closeModal">
        <template #icon>
          <n-icon size="18">
            <Close />
          </n-icon>
        </template>
      </n-button>
    </template>

    <div class="audit-timeline">
      <n-timeline>
        <n-timeline-item
          v-for="(item, index) in sortedAuditList"
          :key="item.id || index"
          :type="getTimelineType(item.operationType)"
          :title="getOperationTitle(item.operationType)"
          :time="formatTime(item.operationTime)"
        >
          <div v-if="item.reason" class="audit-reason">
            原因：{{ item.reason }}
          </div>
        </n-timeline-item>
      </n-timeline>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { computed} from 'vue';
import { Close } from '@vicons/ionicons5';

// 审核记录类型定义
interface AuditRecord {
  auditorId?: number | null;
  createTime?: null | string;
  id?: number | null;
  operationTime?: null | string;
  operationType?: number | null;
  productId?: number | null;
  reason?: null | string;
}



const props = withDefaults(defineProps<{
  show: boolean;
  auditReasonList: Array<AuditRecord>;
  AuditfrontName: string;
}>(), {
  show: false,
  auditReasonList: () => [],
  AuditfrontName: '-',
});

const emits = defineEmits<{
  (e: 'update:show', value: boolean): void;
  (e: 'success'): void;
}>();

const showModal = computed({
  get() {
    return props.show;
  },
  set(value) {
    emits("update:show", value);
  },
});

// 按时间排序审核记录（最新的在上面）
const sortedAuditList = computed(() => {
  return [...props.auditReasonList].sort((a, b) => {
    const timeA = new Date(a.operationTime || 0).getTime();
    const timeB = new Date(b.operationTime || 0).getTime();
    return timeB - timeA; // 降序排列，最新的在前
  });
});

// 获取操作类型对应的标题
const getOperationTitle = (operationType?: number | null): string => {
  const typeMap: Record<number, string> = {
    1: '提交申请',
    2: '申核不通过',
    3: '再次申请',
    4: '申核通过'
  };
  return typeMap[operationType || 0] || '未知操作';
};

// 获取时间线类型（用于显示不同颜色）
const getTimelineType = (operationType?: number | null): 'default' | 'success' | 'error' | 'warning' | 'info' => {
  const typeMap: Record<number, 'default' | 'success' | 'error' | 'warning' | 'info'> = {
    1: 'info',      // 提交申请 - 蓝色
    2: 'error',     // 申核不通过 - 红色
    3: 'warning',   // 再次申请 - 橙色
    4: 'success'    // 申核通过 - 绿色
  };
  return typeMap[operationType || 0] || 'default';
};

// 格式化时间
const formatTime = (time?: string | null): string => {
  if (!time) return '';
  try {
    const date = new Date(time);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  } catch (error) {
    return time;
  }
};

const closeModal = () => {
  showModal.value = false;
};
</script>
<style scoped lang="less">
.audit-timeline {
  padding: 20px 0;

  :deep(.n-timeline) {
    .n-timeline-item {
      .n-timeline-item-content {
        .n-timeline-item-content__title {
          font-weight: 600;
          font-size: 16px;
          margin-bottom: 4px;
        }

        .n-timeline-item-content__meta {
          color: #999;
          font-size: 14px;
          margin-bottom: 8px;
        }
      }
    }
  }

  .audit-reason {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    margin-top: 4px;
  }
}
</style>
