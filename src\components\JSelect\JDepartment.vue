<template>
  <n-tree-select
    :value="props.value"
    :options="processedData"
    @update:value="handleUpdateValue"
    :loading="loading"
    clearable
    cascade
    :style="{ width: `${props.width}px` }"
    :default-expand-all="true"
    :disabled="!props.isSearch ? loading : false"
  >
    <template #empty>
      <div class="empty-wrapper">
        <img :src="EmptySrc" alt="" />
        <p>暂无数据</p>
      </div>
    </template>
  </n-tree-select>
</template>

<script lang="ts" setup>
import EmptySrc from "@/assets/image/exception/empty.png";
import { onMounted, ref, watch } from 'vue'
import { departmentConfigListDepartmentsForDoctor } from '@/services/api';
import { useMessages } from "@/hooks";

const loading = ref(false)

// 定义响应式数据
const processedData = ref([]); // 处理后的最终数据

/* 提示信息 */
const message = useMessages();

/* Props */
const props = withDefaults(
defineProps<{
  value?: Array<string | number> | string | number | null; // 选择的值
  width?: string | number,
  isSearch?:boolean //是否用于搜索
}>(),
{
  width:400,
  isSearch:false
},
);

const emits = defineEmits<{
  (e: "update:value", selectValue: any): void; // 更新选择值事件
}>();

//回调事件
const handleUpdateValue = (value) =>{
  emits("update:value", value);
}

//请求父级
const getStructureListStructures = async() =>{
  loading.value = true
  try{
    const res = await departmentConfigListDepartmentsForDoctor()
      processedData.value = processData(res);
  }catch(err){
      message.createMessageError('获取科室组织失败:' + err)
  }finally{
      loading.value = false
  }
}

const processData = (data) => {
  return data.map(item => {
    const { id, name, children } = item;
    const transformedItem = {
      key: id,
      label: name,
      children:children ? processData(children) : null 
    };
    return transformedItem;
  });
};

onMounted(() => {
  getStructureListStructures()
})

</script>
<style scoped lang="less">
.empty-wrapper {
img {
  height: 50px;
}
p {
  color: #666;
  font-size: 12px;
  text-align: center;
}
}
</style>
