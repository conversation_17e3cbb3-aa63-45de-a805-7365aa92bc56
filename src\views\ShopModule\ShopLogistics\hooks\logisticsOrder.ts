import { ref } from "vue";
import { useMessages } from "@/hooks";
const { createMessageSuccess, createMessageError } = useMessages();
import { addLogisticsOrder,updateLogisticsOrder,logisticBatchDelete,logisticDelete,getLogisticTraces } from "@/services/api";
const isAddLoadingRef = ref<boolean>(false)
const isDeleteLoading = ref<boolean>(false)
const isMobile = ref(false)
export function logisticsOrder() {
    async function onCouponBatchFn(type,data,refresh) {
        if(data.logisticsCode !='SF' && data.logisticsCode !='ZTO'){
            isMobile.value  = true
        }
        try {
            isAddLoadingRef.value = true
            const mobileParams =  !isMobile.value ? {mobile: data.mobile} : {}
            // const addresseeParams =  !isMobile.value ? {addressee: data.addressee} : {}
            const params = {
                    storeId: data.storeId,
                    productId:data.productId,
                    productName: data.productName,
                    sku: data.sku,
                    deliveryQuantity: data.deliveryQuantity,
                    logisticsCompany: data.logisticsCompany,
                    logisticsCode: data.logisticsCode,
                    logisticsNumber: data.logisticsNumber,
                    addressee: data.addressee,
                    ...mobileParams
            }
            if(data.id){
                params['id'] = data.id
            }
            const api = type == 'add' ? addLogisticsOrder:updateLogisticsOrder
            await api(params)
            refresh()
            createMessageSuccess("操作成功")
        } catch (error) {
            createMessageError("操作异常"+error)
        } finally {
            isAddLoadingRef.value = false
        }
    }
    const handleBatchDelete = async(ids,refresh) => {
        isDeleteLoading.value = true
        let _params = {
            ids: ids.toString(),
        };
        try {
           await logisticBatchDelete(_params)
            createMessageSuccess('批量删除成功')
            refresh()
        } catch (error) {
            createMessageError(error)
        }finally{
            isDeleteLoading.value = false
        }
    };
    const handleDelete =async(id,refresh) =>{
        isDeleteLoading.value = true
        console.log('删除===>');
        let _params = {
            id: id,
        };
        try {
           await logisticDelete(_params)
            createMessageSuccess('删除成功')
            refresh()
        } catch (error) {
            createMessageError(error)
        }finally{
            isDeleteLoading.value = false
        }
    }
    return {
        onCouponBatchFn,
        isAddLoadingRef,
        handleBatchDelete,
        handleDelete,
        isDeleteLoading,
        isMobile
    }

}