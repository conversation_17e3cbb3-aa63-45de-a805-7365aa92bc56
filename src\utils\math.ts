/**
 * 秒转化为时分秒
 * @param time 秒
 * @returns 时分秒
 */
export function formateTime(time: number): string {
    const h = Math.floor(time / 3600)
    const minute = Math.floor((time / 60) % 60)
    const second = Math.ceil(time % 60)
    const hours = h < 10 ? '0' + h : h
    const formatSecond = second > 59 ? 59 : second
    return `${Number(hours) > 0 ? `${hours}时:` : ''}${minute < 10 ? '0' + minute : minute}分:${formatSecond < 10 ? '0' + formatSecond : formatSecond}秒`
}


/**
 * 秒转化为分秒和时分秒
 * @param time 秒
 */
export function formateTimeInfo(time: number): { ms: string, hms: string } {
    let hours = zeroFill(Math.floor(time / 3600));
    let minutes = zeroFill(Math.floor(time / 60));
    let _minute = zeroFill(Math.floor((time / 60) % 60))
    let seconds = zeroFill(Math.ceil(time % 60));
    const formatSecond = seconds > 59 ? 59 : seconds
    function zeroFill(n) {
        return n < 10 ? "0" + n : n;
    }
    return {
        hms: `${hours}小时${_minute}分钟${formatSecond}秒`,
        ms: `${minutes}分钟${formatSecond}秒`
    }
}
/**
 * 将金额（元）转换为分（整数）
 * @param yuan 输入的金额字符串（如 "5.00", "10.50"）
 * @returns 分单位的整数值
 */
export function yuanToCent(yuan: string): number {
  // 去除非法字符（保留数字和小数点）
  const sanitized = yuan.replace(/[^\d.]/g, '');
  
  // 拆分整数和小数部分
  const parts = sanitized.split('.');
  
  // 处理整数部分（默认0）
  const integerPart = parts[0] ? parseInt(parts[0], 10) : 0;
  
  // 处理小数部分（补零至两位）
  const decimalPart = parts[1] 
    ? parts[1].padEnd(2, '0').slice(0, 2) 
    : '00';
  
  // 拼接为分单位（整数）
  return integerPart * 100 + parseInt(decimalPart, 10);
}
/** 计算当前页总计 */
export function calculateTotal(data) {
    return data.reduce((total, current) => {
        for (let key in current) {
            if (typeof current[key] === 'number') {
                total[key] = (total[key] || 0) + current[key];
            }
        }
        return total;
    }, {});
}

// 计算当前页总计  isJudge为是否字段
export function computeTotal(data:any,isJudge) {
    return data.reduce((total, current) => {
        for (let key in current) {
            if (typeof current[key] === 'number') {
                total[key] = (total[key] || 0) + current[key];
            }else if(isJudge?.includes(key) && current[key] == 1){
                total[key] = (total[key] || 0) + 1
            }
        }
        return total;
    }, {});
}

/** 计算百分比 */
export function calculatePercentage(numerator, denominator) {
    if (denominator === 0) {
      return '0.0%';
    }
    const result = (numerator / denominator) * 100;
    return result.toFixed(1) + '%';
  }

/** 遍历赋值key */
export function assignOptionValues(data: { [key: string]: number }, reactiveObj: any){
    const newReactiveObj = { ...reactiveObj };
    for (const key in newReactiveObj) {
      if (newReactiveObj.hasOwnProperty(key)) {
        const options = newReactiveObj[key].options;
        options.forEach(option => {
          option.value = data[option.key];
        });
      }
    }
    return newReactiveObj;
  }

/** 防抖 */
export function _debounce<T extends (...args: any[]) => any>(func: T, delay: number): T {
  let timeoutId: ReturnType<typeof setTimeout>;
  return function(this: any, ...args: Parameters<T>) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      func.apply(this, args);
    }, delay);
  } as T;
};

/**
 * 节流函数
 * @param fn 要实现节流的函数
 * @param time 节流时长（间隔多久执行一次） 默认1000毫秒
 * @returns 返回一个函数 以节流逻辑执行传入fn
 */
export function _throttle <T extends (...args:any[]) => void> ( fn:T, time:number = 1000) :(...args:Parameters<T>)=> void {
  let flag:boolean = true;
  return function (this: any,...args:Parameters<T>) {
      if (!flag) return;
      flag = false;
      setTimeout(() => {
          fn.apply(this, args);
          flag = true;
      }, time);
  }
}



/** 
 * @description 数字转文字
 */
export function numberToChinese(number) {
  const MAPSTRING = {
      1: '一',
      2: '二',
      3: '三',
      4: '四',
      5: '五',
      6: '六',
      7: '七',
      8: '八',
      9: '九',
      10: '十',
      11: '十一',
      12: '十二',
      13: '十三',
      14: '十四',
      15: '十五',
      16: '十六',
      17: '十七',
      18: '十八',
      19: '十九',
      20: '二十',
      21: '二十一',
      22: '二十二',
      23: '二十三',
      24: '二十四',
      25: '二十五',
      26: '二十六',
      27: '二十七',
      28: '二十八',
      29: '二十九',
      30: '三十',
  };
  return MAPSTRING[number] || '未知数字';
}
// 身份证信息解析工具函数
export function parseIdNumber(idNumber: string): { gender: string; birthday: string } | null {
  // 格式校验正则（15位或18位）
  const idRegex = /^(\d{6}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx])$|^(\d{6}\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3})$/;
  
  if (!idRegex.test(idNumber)) return null;

  // 处理15位身份证
  if (idNumber.length === 15) {
    const birthYear = `19${idNumber.substring(6, 8)}`;
    const birthMonth = idNumber.substring(8, 10);
    const birthDay = idNumber.substring(10, 12);
    const genderCode = parseInt(idNumber.substring(14, 15));
    
    return {
      birthday: `${birthYear}-${birthMonth}-${birthDay}`,
      gender: genderCode % 2 === 1 ? '男' : '女'
    };
  }
  
  // 处理18位身份证
  const birthYear = idNumber.substring(6, 10);
  const birthMonth = idNumber.substring(10, 12);
  const birthDay = idNumber.substring(12, 14);
  const genderCode = parseInt(idNumber.substring(16, 17));
  
  return {
    birthday: `${birthYear}-${birthMonth}-${birthDay}`,
    gender: genderCode % 2 === 1 ? '男' : '女'
  };
}