import dayjs from "dayjs";
import weekday from "dayjs/plugin/weekday";
import "dayjs/locale/zh-cn";

dayjs.extend(weekday);
dayjs.locale('zh-cn')
type DateType = "day" | "week" | "month" | "year";
type DateValue = {
  timestamp: [number, number];
  formatValue: [string, string];
};
export const enum TimeFormat {
  DEFAULT = "YYYY-MM-DD HH:mm:ss",
  DATE = "YYYY-MM-DD",
  MONTH = "YYYY-MM",
}
export enum DateRangeQuickSelectEnum{  
  today = 0,
  yesterday,
  this_week,
  last_week,
  this_month,
  last_month,
  this_year,
  last_seven_days,
}
type DateRangeQuickSelectMap = Record<DateRangeQuickSelectEnum,{
  name:string,
  startTime:number,
  endTime:number
}>
export const DateRangeQuick:DateRangeQuickSelectMap =  {
  [DateRangeQuickSelectEnum.today]: {
    name: "今天",
    startTime: dayjs().startOf("day").add(0, "day").valueOf(),
    endTime: dayjs().endOf("day").add(0, "day").valueOf(),
  },
  [DateRangeQuickSelectEnum.yesterday]: {
    name: "昨天",
    startTime: dayjs().startOf("day").add(-1, "day").valueOf(),
    endTime: dayjs().endOf("day").add(-1, "day").valueOf(),
  },
  [DateRangeQuickSelectEnum.this_week]: {
    name: "本周",
    startTime: dayjs().startOf("week").valueOf(),
    endTime: dayjs().endOf("day").valueOf(),
  },
  [DateRangeQuickSelectEnum.last_week]: {
    name: "上周",
    startTime: dayjs().add(-1, "week").startOf("week").valueOf(),
    endTime: dayjs().add(-1, "week").endOf("week").valueOf(),
  },
  [DateRangeQuickSelectEnum.this_month]: {
    name: "本月",
    startTime: dayjs().startOf("month").valueOf(),
    endTime: dayjs().endOf("day").add(0, "day").valueOf(),
  },
  [DateRangeQuickSelectEnum.last_month]: {
    name: "上月",
    startTime: dayjs().add(-1, "month").startOf("month").valueOf(),
    endTime: dayjs().add(-1, "month").endOf("month").valueOf(),
  },
  [DateRangeQuickSelectEnum.this_year]: {
    name: "本年",
    startTime: dayjs().startOf("year").valueOf(),
    endTime: dayjs().endOf("day").add(0, "day").valueOf(),
  },
  //近七天
  [DateRangeQuickSelectEnum.last_seven_days]: {
    name: "近七天",
    startTime: dayjs().startOf("day").add(-6, "day").valueOf(),
    endTime: dayjs().endOf("day").add(0, "day").valueOf(),
  }
};

export function getTimeRangeByType(
  type: DateType,
  date?: Date | undefined,
  format: TimeFormat = TimeFormat.DEFAULT,
): Array<string> {
  dayjs.extend(weekday);
  dayjs.locale("zh-cn");
  const dateInstance = !date ? dayjs() : dayjs(date);
  const formatTimeRange = [];
  formatTimeRange.push(dateInstance.startOf(type).format(format));
  formatTimeRange.push(dateInstance.endOf(type).format(format));
  return formatTimeRange;
}

export function getDate(
  type: string | number,
  format: TimeFormat = TimeFormat.DEFAULT,
): DateValue {
  return {
    timestamp: [DateRangeQuick[type].startTime, DateRangeQuick[type].endTime],
    formatValue: [
      dayjs(DateRangeQuick[type].startTime).format(format),
      dayjs(DateRangeQuick[type].endTime).format(format),
    ],
  };
}

//基于baseDate获取number天的时间范围（不包括baseDate当天）
export function getDateRangeByNumber(
  number: number,
  format: TimeFormat = TimeFormat.DEFAULT,
  baseDate?: string | number | undefined,
): DateValue {
  if(number == 0) throw new Error("number can not be 0");
  let baseDateInstance = baseDate?dayjs(baseDate):dayjs();
  const targerDateObj = {
    start_time: number<0?baseDateInstance.startOf("day").add(number, "day").valueOf():baseDateInstance.startOf("day").add(2, "day").valueOf(),
    end_time: number<0?baseDateInstance.endOf("day").add(-1, "day").valueOf():baseDateInstance.endOf("day").add(number, "day").valueOf(),
  }
  return {
    timestamp: [targerDateObj.start_time, targerDateObj.end_time],
    formatValue: [
      dayjs(targerDateObj.start_time).format(format),
      dayjs(targerDateObj.end_time).format(format),
    ],
  };
}

//填充月份时间
export function getMonthRangeTime(start_time:string | number,end_time?:string | number):DateValue {
  if(!start_time) throw new Error("time can not be empty");
  const defaultTime = ['00:00:00', '23:59:59']
  //开始时间
  const startPre = dayjs(start_time).format(TimeFormat.MONTH)
  //结束时间
  let endPre = startPre
  let last_day = dayjs(start_time).daysInMonth()
  if(end_time){
    endPre = dayjs(end_time).format(TimeFormat.MONTH)
    last_day = dayjs(end_time).daysInMonth()
  }
  const startTime = `${startPre}-01 ${defaultTime[0]}`
  const endTime = `${endPre}-${last_day} ${defaultTime[1]}`
  return {
    formatValue:[startTime,endTime],
    timestamp:[new Date(startTime).getTime(),new Date(endTime).getTime()]
  }
}

//填充日期时间
export function getDayRangeTime(start_time:string | number,end_time?:string | number):DateValue {
  if(!start_time) throw new Error("time can not be empty");
  const defaultTime = ['00:00:00', '23:59:59']
  //开始时间
  const startPre = dayjs(start_time).format(TimeFormat.DATE)
  //结束时间
  let endPre = startPre
  if(end_time){
    endPre = dayjs(end_time).format(TimeFormat.DATE)
  }
  const startTime = `${startPre} ${defaultTime[0]}`
  const endTime = `${endPre} ${defaultTime[1]}`
  return {
    formatValue:[startTime,endTime],
    timestamp:[new Date(startTime).getTime(),new Date(endTime).getTime()]
  }
}

export function formatTimeToDHMS(start, end = new Date().getTime()) {
  // 处理输入参数，支持时间戳和YYYY-MM-DD HH:mm:ss格式的字符串
  const parseTime = (time) => {
    if (typeof time === 'number') {
      return new Date(time);
    } else if (typeof time === 'string') {
      // 处理YYYY-MM-DD HH:mm:ss格式
      const [datePart, timePart] = time.split(' ');
      if (datePart && timePart) {
        const [year, month, day] = datePart.split('-').map(Number);
        const [hours, minutes, seconds] = timePart.split(':').map(Number);
        return new Date(year, month - 1, day, hours || 0, minutes || 0, seconds || 0);
      }
    }
    // 默认返回当前时间
    return new Date();
  };

  const startTime = parseTime(start);
  const endTime = parseTime(end);

  // 计算时间差（毫秒）
  let diffMs = endTime.getTime() - startTime.getTime();
  // 如果差值为负，取绝对值
  if (diffMs < 0) diffMs = -diffMs;

  // 转换为秒、分钟、小时、天
  const totalSeconds = Math.floor(diffMs / 1000);
  const seconds = totalSeconds % 60;
  const totalMinutes = Math.floor(totalSeconds / 60);
  const minutes = totalMinutes % 60;
  const totalHours = Math.floor(totalMinutes / 60);
  const hours = totalHours % 24;
  const days = Math.floor(totalHours / 24);

  // 构建时间字符串
  const daysStr = days > 0 ? `${days}天` : '';
  const hoursStr = hours > 0 ? `${hours}小时` : '';
  const minutesStr = minutes > 0 ? `${minutes}分钟` : '';
  // const secondsStr = seconds > 0 ? `${seconds}秒` : '';

  // 至少显示一个单位（当所有值为0时，显示"0秒"）
  if (!daysStr && !hoursStr && !minutesStr) {
    return '0分';
  }

  return `${daysStr}${hoursStr}${minutesStr}`;
}
