<template>
    <JDrawer
      v-model:show="drawerVisible"
      title="基础信息"
      :isGetLoading = isGetLoading
      :enableFooterWrapper="true"
      @after-leave="closeDrawer"
      to=".table-wrapper"
    >
      <!-- 表单内容 -->
      <template #content>
        <n-form
          ref="formRef"
          :model="model"
          :rules="rules"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          size="small"
          :style="{width: '60%'}"
        >
           <!-- 姓名 -->
           <n-form-item-gi :span="8" label="姓名" path="pharmacistName">
             <n-input v-model:value="model.pharmacistName" clearable placeholder="请输入姓名" />
           </n-form-item-gi>
           <!-- 手机号 -->
           <n-form-item-gi :span="8" label="手机号">
             <n-input v-model:value="model.mobile" clearable placeholder="请输入手机号" @blur="handleMobileVerify"/>
           </n-form-item-gi>
           <!-- 职务 -->
           <n-form-item-gi :span="8" label="职务" path="title">
              <n-checkbox-group v-model:value="model.title">
                <n-space>
                  <n-checkbox :value="1">
                    平台审核药师
                  </n-checkbox>
              </n-space>
           </n-checkbox-group>
           </n-form-item-gi>
           <!-- 签名照片 -->
            <n-form-item-gi :span="8" label="签名照片" :path="model.signImg.length || model.signImg.length ? '' : 'signImg'">
             <div >
                <CustomizeUpload v-model:value="model.signImg" :fileListSize="1" :max="1" :isSignatureImg="true" accept=".png" :maxFileSize="1" :isCrop="true" :title="'裁剪签名图片'" :fixedNumber="[3,1]" :imageUploadType="['image/png']" :enableTypeValidation="true"/>
                <div style="display: flex; font-size: 14px;color: #c2c2c2;margin-top: 10px;">
                    <span>请上传背景透明的png格式的签名图片</span> 
                    <n-popover trigger="hover" :show-arrow="false">
                      <template #trigger>
                        <span style="color: #169bd5; cursor: pointer">【查看示例】</span>
                      </template>
                      <img 
                        style="width: 120px;
                        height: 120px;"
                        :src="ImageExample" 
                        alt="" />
                    </n-popover>
                    <span>，不能大于1M，平台审核药师必传</span>
                </div>
             </div>
           </n-form-item-gi>
           <!-- 医师资格证书编号 -->
           <n-form-item-gi :span="8" label="医师资格证书编号">
             <n-input v-model:value="model.pharmacistCode" clearable placeholder="请输入医师资格证书编号" maxlength="64" show-count/>
           </n-form-item-gi>
           <!-- 第三方医师编码 -->
           <n-form-item-gi :span="8" label="第三方医师编码">
             <n-input v-model:value="model.thirdPharmacistCode" clearable placeholder="请输入医师在第三方系统的唯一编码，可用于数据推送等事务" maxlength="64" show-count/>
           </n-form-item-gi>
        </n-form>
      </template>
      <!-- footer -->
      <template #footer>
        <n-space style="align-content:center;">
          启用状态
          <n-radio-group v-model:value="model.isActived" style="margin-left: 10px;">
            <n-space>
              <n-radio :value="1">
                是
              </n-radio>
              <n-radio :value="0">
                否
              </n-radio>
            </n-space>
          </n-radio-group>
        </n-space>
        <n-space justify="end">
          <n-button @click="closeDrawer" class="store-button">取 消</n-button>
          <n-button type="primary" :loading="isLoading" @click="_save" class="store-button">保 存</n-button>
        </n-space>
      </template>
    </JDrawer>
  </template>
  
<script lang="ts" setup name="NewDoctor">
import { ref, watch } from "vue";
import { deepClone } from "@/utils";
import type { FormRules } from "naive-ui";
import { useMessages } from '@/hooks';
import ImageExample from "@/assets/image/system/ImageExample.png";
import { pharmacistEntityAdd, pharmacistEntityUpdate, pharmacistEntityGet } from "@/services/api";
const { createMessageSuccess, createMessageError, createMessageWarning } = useMessages();

/* 表单实例 */
const formRef = ref();

/* 表单参数初始化 */
const initParams = {
  /** 药师姓名 */
  pharmacistName:null,
  /** 手机号码 */
  mobile:null,
  /** 职务 */
  title:[1],
  /** 药师签名照片 */
  signImg:'',
  /** 药师执业证书编号 */
  pharmacistCode:null,
  /** 第三方药师编码 */
  thirdPharmacistCode:null,
  /** 是否启用 */
  isActived:1,
  id:null
};
const model = ref(deepClone(initParams));

/* 表单规则 */
const rules: FormRules = {
  pharmacistName: {
    type: "string",
    required: true,
    trigger: ["blur", "input"],
    message: "请输入姓名",
  },
  title: {
    type: "array",
    required: true,
    trigger: ["blur", "input"],
    message: "请选择职务",
  },
  signImg:{
    type: "array",
    required: true,
    trigger: ["blur", "change"],
    message: "请先上传签名照片",
  }
};

/** 获取参数 */
const isGetLoading = ref(false)
const getParams = () => {
  let title = model.value.title[0]
  let signImg = model.value.signImg[0]
  const { 
    pharmacistName,
    mobile,
    pharmacistCode,
    thirdPharmacistCode,
    isActived,
    id
  } = model.value;
  return {
    pharmacistName,
    mobile,
    signImg,
    title,
    pharmacistCode,
    thirdPharmacistCode,
    isActived,
    id
  };
};

/** 抽屉状态 */
const drawerVisible = ref(false);

const drawerProps = ref()
const acceptParams = async(param) => {
  drawerVisible.value = true;
  drawerProps.value = param
  if(drawerProps.value.type == 'edit'){
    getPharmacistsData(drawerProps.value.row.id)
  }
};

/** 关闭抽屉 */
const closeDrawer = () => {
    model.value = deepClone(initParams);
    drawerVisible.value = false;
};

/** 获取药师数据 */
const getPharmacistsData = async(ids) =>{
  isGetLoading.value = true
  try{
    const res = await pharmacistEntityGet(ids)
    const title = res.title ? [res.title] : [];
    const signImg = res.signImg ? [res.signImg] : '';
    const { 
      pharmacistName,
      mobile,
      pharmacistCode,
      thirdPharmacistCode,
      isActived,
      id
    } = res;
    Object.assign(model.value, {
      pharmacistName,
      mobile,
      signImg,
      title,
      pharmacistCode,
      thirdPharmacistCode,
      isActived,
      id
    });
  }catch(err){
    createMessageError('获取药师数据失败:' + err)
  }finally{
    isGetLoading.value = false
  }
}

/* 确认--保存 */
const isLoading = ref(false);
const _save = (e: MouseEvent) => {
    e.preventDefault();
    formRef.value?.validate(async (errors: any) => {
      if (!errors) {
        isLoading.value = true
        try{
          drawerProps.value.type == 'add' ? await pharmacistEntityAdd({data:getParams()}) : await pharmacistEntityUpdate  ({data:getParams()})
          drawerProps.value.refresh()
          drawerVisible.value = false
          createMessageSuccess((drawerProps.value.type == 'add' ? '新增' : '编辑') + '成功' )
        }catch(err){
          createMessageError((drawerProps.value.type == 'add' ? '新增' : '编辑') + '失败:' + err)
        }finally{
          isLoading.value = false
        }
      }
    })
};

/** 手机号码校验 */
const handleMobileVerify = () =>{
  const phoneRegex = /^[1]([3-9])[0-9]{9}$/; // 手机号正则
    if(!phoneRegex.test(model.value.mobile) && model.value.mobile != null){
        model.value.mobile = null
        createMessageWarning('请输入有效的手机号码');
    }
}

watch(()=>model.value.signImg,(newVal)=>{
  if(newVal[0] == '') model.value.signImg = ''
})

defineExpose({
  acceptParams,
  closeDrawer,
  drawerVisible
});
  

</script>

<style lang="less" scoped></style>
  