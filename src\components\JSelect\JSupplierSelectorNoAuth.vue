<!-- 此组件和JSupplierSelector的区别是不过滤禁用状态的 并且接口不受权限影响 -->
<template>
  <JSelect
    v-bind="$attrs"
    :value="props.value"
    :loading="isLoading"
    :onFocus="handlerFocus"
    :options="selectListData"
    :onClear="handleClear"
    @update:value="onChange"
    placeholder="请选择供应商"
    :filter="customFilter"
    @scroll="handleScroll"
    @keydown.enter="handleSearch"
    :max-tag-count="maxTagCount"
    :display-quantity="props.isMultiple === false ? 0 : maxTagCount"
    :reset-menu-on-options-change="isResetSelectStatusRef"
    :disabled="props.isDisabled"
    :clearable="props.isClearable"
    :filterable="props.filterable"
    :multiple="props.multiple"
  />
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import { useMessages } from "@/hooks";
import { getSupplierNoAuth } from "@/services/api";
import { isArray } from "@/utils";
/** 相关组件 */
import JSelect from "@/components/JSelect/index.vue";

defineOptions({
 name: "JSupplierSelector",
});

/* Props */
const props = withDefaults(
 defineProps<{
  value: Array<string | number> | string | number | null; // 选择的值
  isImmediately?: boolean; // 是否立即加载
  isMultiple?: boolean; // 是否多选 --> 非必传，默认值为false
  isClearable?: boolean;// 是否可清除选项
  isDisabled?: boolean; // 是否禁用
  filterable?: boolean; // 是否可过滤
  multiple?: boolean; // 是否多选
  extraId?: Array<string | number> | string | number | null; // 额外id,编辑时使用
 }>(),
 {
  isImmediately: false,
  isMultiple: false,
  isClearable: true,
  isDisabled: false,
  filterable: true,
  multiple: false,
  extraId: null,
 },
);

/** emits */
const emits = defineEmits<{
 (e: "update:value", value: Array<string | number> | string | number | null): void; // 更新选择值事件
}>();

const maxTagCount = ref(1);
/* 提示 */
const message = useMessages();
/** 是否加载 */
const isLoading = ref(false);
/** 重置选择状态 */
const isResetSelectStatusRef = ref(false);
/** 数据列表 */
const selectListData = ref([]);
const _resultTempList = [];
/* 执行搜索返回的内容*/
let recordsTotal = 1; // 总记录数

/** 分页参数 */
const _params = {
 data: {
  supplierName: null,
 },
 pageVO: {
  current: 1,
  size: 500,
 },
};

/** 筛选并转化 */
const handleData = (filterData: {
 supplierName?: string;
 id?: number;
 status?: 0 | 1;
}[]) => {
 const mappedData = filterData.map(item => {
  const label = `${item.supplierName}`;
  return { 
   label, 
   value: item.id, 
   disabled: false
  };
 });

 // 判断extraId是否匹配当前项value
 const isExtraIdMatch = (value: number) => {
  if (props.extraId === null || props.extraId === undefined) {
   return false;
  }
  if (isArray(props.extraId)) {
   return props.extraId.includes(value);
  }
  return props.extraId === value;
 };

 // 过滤数据：保留启用项或与extraId匹配的项
 return mappedData.filter(item => {
  return !item.disabled || isExtraIdMatch(item.value);
 });
};

/** 聚焦事件处理函数 */
function handlerFocus() {
 // 如果直播列表为空
 if (!selectListData.value.length) {
  getSelectListData();
 }
};

/** 清空事件处理函数 */
function handleClear() {
 _params.data.supplierName = null;
 emits("update:value", null);
};

/** 选择值改变事件处理函数 */
function onChange(value: Array<string | number> | string | number | null) {
 _params.data.supplierName = null;

 if (props.multiple && isArray(value)) {
  maxTagCount.value = value.length > 1 ? 1 : 0; // 多选时，最多显示一个tag
 }
  emits("update:value", value);
};

/** 自定义过滤函数 */
function customFilter(keyword, options) {
 const labelMatch = options.label
  .toLowerCase()
  .includes(keyword.toLowerCase());
 const valueMatch = options.value
  .toLowerCase()
  .includes(keyword.toLowerCase());
 return labelMatch || valueMatch;
};

/** 滚动事件处理函数 */
function handleScroll(e) {
 const currentTarget = e.currentTarget as HTMLElement;
 if (
  currentTarget.scrollTop + currentTarget.offsetHeight >=
  currentTarget.scrollHeight
 ) {
  // 如果当前页乘以每页大小小于总记录数
  if (_params.pageVO.current * _params.pageVO.size < recordsTotal) {
	  _params.pageVO.current++; // 当前页加1
	  getSelectListData();
  }
 }
};

/** 搜索事件处理函数 */
function handleSearch(event) {
 _params.data.supplierName = event.target.value ?? null;
 _params.pageVO.current = 1;
 getSelectListData();
};

/** 获取下拉列表数据 */
const getSelectListData = async () => {
 try {
  isLoading.value = true;
  const data = await getSupplierNoAuth(_params);
  const { total, current, size, records } = data;
  _params.pageVO.current = Number(current);
  _params.pageVO.size = Number(size);
  recordsTotal = Number(total);
  // 如果是第一页
  if (_params.pageVO.current == 1) {
	  isResetSelectStatusRef.value = true; // 重置选择状态为true
	  selectListData.value = handleData(records);
  } else {
	  isResetSelectStatusRef.value = false; // 重置选择状态为false
	  handleData(records).forEach((item) => {
		  // 如果列表中不存在该项
		  if (!selectListData.value.find((temp) => temp.value == item.value)) {
			  // 添加到列表中
			  selectListData.value.push(item);
		  }
	  });
  }
  // 如果有搜索值
  if (_params.data.supplierName) {
	  // 遍历直播列表
	  selectListData.value.forEach((item) => {
		  // 如果临时结果列表中不存在该项
		  if (!_resultTempList.find((temp) => temp.value == item.value)) {
			  // 添加到临时结果列表中
			  _resultTempList.push(item);
		  }
	  });
  } else {
	  // 遍历临时结果列表
	  _resultTempList.forEach((item) => {
		  // 如果直播列表中不存在该项
		  if (!selectListData.value.find((temp) => temp.value == item.value)) {
			  // 添加到直播列表中
			  selectListData.value.push(item);
		  }
	  });
  }
 } catch (error) {
  message.createMessageError("获取供应商列表失败：" + error);
 } finally {
  isLoading.value = false;
 }
};

/** 监听 */
watch(
 () => props.value,
 (newVal) => {
  if (newVal && selectListData.value.length === 0) {
	getSelectListData();
  }
 },
 { immediate: true },
);
</script>

<style lang="less" scoped></style>
