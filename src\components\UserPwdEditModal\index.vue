<template>
  <n-modal
    :show="show"
    :auto-focus="false"
    preset="card"
    :style="{
      width: '600px',
    }"
    :title="modalTitle()"
    size="small"
    :bordered="false"
    :segmented="{
      content: 'soft',
      footer: 'soft',
    }"
    @update:show="toggleShow"
    @mask-click=" (!props.firstLoginBoolean && !props.easyPasswordsBoolean) && closable && toggleShow(false)"
    @close="close"
    @after-enter="handleAfterEnter($event)"
    :close-on-esc="false"
    class="updatePassword"
    :mask-closable=" (props.firstLoginBoolean || props.easyPasswordsBoolean) ? false : true"
  >
    <n-form ref="formRef" :model="modelRef" :rules="rules">
      <!--<n-form-item v-show="closable" path="oldPwd" label="旧密码">-->
      <!--  <n-input v-model:value="modelRef.oldPwd" type="password" show-password-on="click" />-->
      <!--</n-form-item>-->
      <n-form-item path="pwd" label="密码">
        <PwdInput 
            v-model:value="modelRef.pwd"
            notice='必须为5位数以上，包含字母和数字且不能含有4位及以上的连续相同数字或递增递减字符'
        />
        <!-- <n-input
          v-model:value="modelRef.pwd"
          type="password"
          show-password-on="click"
          @input="handlePasswordInput"
          @keydown.enter.prevent
        /> -->
      </n-form-item>
      <n-form-item path="repeatPwd" ref="repeatPwdInputRef" first label="确认密码">
        <n-input 
          v-model:value="modelRef.repeatPwd" 
          type="password" 
          show-password-on="click" 
          @keydown.enter.prevent 
          @update:value="handleInputChange"/>
      </n-form-item>
    </n-form>
    <template #footer>
      <n-space justify="end">
        <n-button v-if="closable" @click="toggleShow(false)" class="store-button">取 消</n-button>
        <n-button type="primary" @click="onConfirm" :loading="confirmLoading" class="store-button">确 认</n-button>
      </n-space>
    </template>
    <div :class="(props.firstLoginBoolean || props.easyPasswordsBoolean) ? 'maskLayer' : 'notMaskLayer'"></div>
  </n-modal>
</template>

<script setup lang="ts" name="UserPwdEditModal">
import { ref, toRefs, watch } from 'vue';
import type { FormInst, FormItemRule, FormRules } from 'naive-ui';
import { storeToRefs } from 'pinia';
import { isNullOrUnDef, isNullStringOrNullOrUnDef } from '@/utils/isUtils';
import { useMessages, useDragModal } from '@/hooks';
import type { ChangePwdProps } from './type';
import { userModifyPassword, userUpdatePassword } from '@/services/api';
import { afterLogout } from '@/utils/accountUtils';
import { createCacheStorage } from '@/utils/cache/storageCache';
import { CacheConfig } from '@/utils/cache/config';
import { useUserStore } from '@/stores/modules/user';
import PwdInput from "@/components/PwdInput/index.vue"
import { checkPwdStrength } from '../PwdInput/pwdUtils';
import { PwdLevelEnum } from '../PwdInput/types';
/** props */
const props = withDefaults(
  defineProps<{
    show: boolean;
    id: string | number;
    closable?: boolean;
    forceQuit?: boolean;
    firstLoginBoolean?:boolean;
    easyPasswordsBoolean?:boolean
  }>(),
  {
    closable: false,
    forceQuit: false,
    firstLoginBoolean:false,
    easyPasswordsBoolean:false
  },
);

/** emits */
const emits = defineEmits<{
  (e: 'update:show', value: boolean): void;
}>();

const { id, show, closable, forceQuit } = toRefs(props);

/** 表单实例 */
const formRef = ref<FormInst | null>(null);
const confirmLoading = ref(false);

const userOldPassword = createCacheStorage(CacheConfig.UserInfo);
const userStore = useUserStore();
const { _userInfo } = storeToRefs(userStore);
// const userIsForce = ref(_userInfo.value.isForce);
// let passwordTimer = null;
// const getOldPassword = () => {
//   const oldPassword = userOldPassword.get();
//   if (userIsForce.value) {
//     if (oldPassword) {
//       // modelRef.value.oldPwd = oldPassword;
//     } else {
//       createMessageError('旧密码读取异常，请重新登录');
//       if (passwordTimer) clearTimeout(passwordTimer);
//       passwordTimer = setTimeout(async () => {
//         try {
//           // await userLogout();
//         } catch (e) {
//           createMessageError(e);
//         }
//         afterLogout();
//       }, 3000);
//     }
//   }
// };
// onMounted(() => {
//   getOldPassword();
// });
const initParams = {
  id: id.value,
  pwd: '',
  // oldPwd: '',
  repeatPwd: '',
};
const modelRef = ref<ChangePwdProps & { repeatPwd: string }>({ ...initParams });
const { createMessageSuccess, createMessageError } = useMessages();
const repeatPwdInputRef = ref();
const handlePasswordInput = () => {
  if (modelRef.value.repeatPwd) {
    repeatPwdInputRef.value?.validate({ trigger: 'password-input' });
  }
};

function validatePasswordStartWith(rule: FormItemRule, value: string): boolean {
  return (
    !!modelRef.value.pwd &&
    modelRef.value.pwd.startsWith(value) &&
    modelRef.value.pwd.length >= value.length
  );
}

function validatePasswordSame(rule: FormItemRule, value: string): boolean {
  return value === modelRef.value.pwd;
}

function validatePasswordStrength(rule: FormItemRule, value: string): boolean {
  if(isNullStringOrNullOrUnDef(value)){
    return true
  }
  return checkPwdStrength(value) >= PwdLevelEnum.MID
}

/** 表单规则 */
const rules: FormRules = {
  oldPwd: [
    {
      required: true,
      message: '请输入旧密码',
    },
  ],
  pwd: [
    {
      required: true,
      message: '请输入新密码',
    },
    {
      validator: validatePasswordStrength,
      message: '当前密码强度过低',
      trigger: 'input',
    },
  ],
  repeatPwd: [
    {
      required: true,
      message: '请再次输入密码',
      trigger: ['input', 'blur'],
    },
    {
      validator: validatePasswordStartWith,
      message: '两次密码输入不一致',
      trigger: 'input',
    },
    {
      validator: validatePasswordSame,
      message: '两次密码输入不一致',
      trigger: ['blur', 'password-input'],
    },
  ],
};
const toggleShow = (value?: boolean) => {
  if (isNullOrUnDef(value)) emits('update:show', !show.value);
  else emits('update:show', value);
};
const accountLogout = async () => {
  try {
    // TODO 添加退出登录接口
    new Promise((resolve, reject) => {
      setTimeout(() => {
        resolve('')
      }, 2000)
    }).then(() => {
      afterLogout();
    })
    // await userLogout();
  } catch (e) {
    createMessageError('退出登录失败');
  }
  // afterLogout();
};
const onConfirm = (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async (errors) => {
    if (!errors) {
      confirmLoading.value = true;
      try {
        const {
          id,
          pwd,
          repeatPwd
        } = modelRef.value;
        // await changeUserPwd({ id, pwd, oldPwd });
        await userUpdatePassword({ 
          userId: id,
          firstPassword: pwd,
          secondPassword: repeatPwd
        });
        userOldPassword.remove();
        toggleShow(false);
        createMessageSuccess(
          `修改密码成功${forceQuit.value ? ',即将跳转到登录页面' : ''}`,
        );

        if (forceQuit.value) {
          await accountLogout();
        }
      } catch (e) {
        createMessageError(`修改密码失败${e}`);
      }
      confirmLoading.value = false;
    }
  });
};
const close = () => {
  let newUserInfo = { ..._userInfo.value };
  newUserInfo.isForce = 0;
  userStore.setUserInfo(newUserInfo);
  // return closable.value && toggleShow(false);
  return toggleShow(false);
};
/** 出现动画完成执行的回调 */
const handleAfterEnter = (e) => {
  useDragModal(e);
}

/** 模态框提示语 */
const modalTitle = () =>{
  if(props.firstLoginBoolean){
    return '首次登录或密码已重置，请修改密码'
  }else if(props.easyPasswordsBoolean){
    return '密码过于简单，请修改后重新登录'
  }else{
    return '修改密码'
  }
}

const handleInputChange = (value:string) => {
    modelRef.value.repeatPwd = value.trim()
}

watch(show, (newVal) => {
  if (!newVal) modelRef.value = { ...initParams };
});

//首次登录监听 或 密码简易
watch(()=>[
props.firstLoginBoolean,
props.easyPasswordsBoolean
],
 (newVal)=>{
  newVal.map((item)=>{
    if(item == true){
      toggleShow(item);
    }
  })
})
</script>
<style scoped lang="less">
@import "@/styles/default.less";
@import "@/styles/defaultVar.less";
.maskLayer{
  display:block;
  position: absolute;
  right: 10px;
  top: 10px;
  width: 25px;
  height: 25px;
  background: #fff;
}
.notMaskLayer{
  display:none
}
</style>