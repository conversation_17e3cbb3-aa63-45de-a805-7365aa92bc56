<template>
    <JModal
    v-model:show="isShow"
    width="680"
    :title="Add_active?'新增机构':'编辑机构'"
    @after-leave="closeModal"
		@positive-click="_save"
		:positiveButtonProps="{
			loading: isLoading
		}"
  >
  <n-form
      ref="formRef"
      :rules="rules"
      :model="model"
      label-width="auto"
      label-placement="left"
      require-mark-placement="right-hanging"
      :style="{
          width: '100%',
        }"
    >
      <n-grid :cols="8" :x-gap="24">
        <n-form-item-gi :span="24" label="机构名称" path="name" required>
          <n-input v-model:value="model.name" placeholder="请输入机构名称" :maxlength="64" clearable/>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="所在区域" path="addressOptions" required>
            <JAreaSelect v-model:value="model.addressOptions" />
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </JModal>
</template>

<script setup lang="ts">
import { ref,computed, watch } from 'vue';
import { useMessages } from '@/hooks';
import { addOrganization, editOrganization } from '@/services/api';
const message = useMessages();

const props = withDefaults(defineProps<{
    Add_active: boolean;
    row?: any;
    show: boolean;
}>(), {
    Add_active: true,
    row: null,
    show: false,
});
const emits = defineEmits<{
    (e: 'update:show', value: boolean): void;
    (e: 'refresh'): void;
}>();

const isShow = computed({
    get: () => props.show,
    set: (value: boolean) => {
      emits('update:show', value);
    }
});

watch(()=>props.show, (newVal, oldVal) => {
  if(newVal && !props.Add_active){
    const rowData = {
      id: props.row.id,
      name: props.row.name,
      addressOptions: {
        provinceId: props.row.provinceId,
        province: props.row.province,
        cityId: props.row.cityId,
        cityName: props.row.cityName,
        areaId: props.row.areaId,
        area: props.row.areaName,
      }
    }
    Object.assign(model.value, rowData);
  }
});

const initParams = {
  id:null,
  name: '',
  addressOptions: {
    provinceId: null,
    province: null,
    cityId: null,
    cityName: null,
    areaId: null,
    area: null,
    remark:null,
  }
};
const model = ref({ ...initParams });

/* 表单规则 */
const rules = {
   name:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入机构名称",
    validator: ()=>{
      return model.value.name != '';
    }
  },
  addressOptions:{
    required: true,
    trigger: ["blur", "change"],
    message: "请选择所在区域",
    validator: ()=>{
      return model.value.addressOptions.provinceId !== null && model.value.addressOptions.cityId !== null  && model.value.addressOptions.areaId !== null;
    }
  },
};

const isLoading = ref(false);

// 关闭按钮
const closeModal = () => {
    isShow.value = false;
    model.value = { ...initParams };
}

// 确认按钮
const formRef = ref(null);
const _save = () => {
    console.log('_save');
    formRef.value?.validate((errors: any) => {
        if (!errors) {
            console.log('errors', errors);
            // TODO:调用接口
            const params = {
              data:{
              ...(props.Add_active ? {} : {id: model.value.id}),
              name: model.value.name,
              ...model.value.addressOptions,}
            }
            isLoading.value = true;
            const api = props.Add_active ? addOrganization : editOrganization;
            api(params).then((res: any) => {
              message.createMessageSuccess('操作成功');
              emits('refresh');
              closeModal();
            }).catch(err=>{
              message.createMessageError(`${err}`);
            }).finally(()=>{
              isLoading.value = false;
            })
        }
    });
}

</script>