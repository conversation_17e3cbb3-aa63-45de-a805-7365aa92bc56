<template>
    <div class="wrapper">
        <n-spin :show="loading || isGetLoading" size="small" style="width: 46%;height: 100%;">
            <div class="left">
                <n-tree 
                    multiple 
                    block-line 
                    cascade 
                    checkable 
                    check-on-click 
                    :data="treeData" 
                    :default-checked-keys="checkedKeysRef"
                    @update:checked-keys="handleCheckedKeysFn"
                >
                  <template #empty>
                    <JEmpty title="暂无经销商分组信息" />
                  </template>
                </n-tree>
            </div> 
        </n-spin>
        <div class="right">
            <div class="header">
                <JSearchInput 
                    v-model:value="searchValue" 
                    placeholder="请输入经销商id、姓名或昵称" 
                    @search="handleSearch"
                    size="small" 
                    width="100%"
                />
            </div>
            <!-- 经销商列表 -->
            <div class="dealer-list-wrapper">
                <transition appear name="fade" mode="out-in">
                    <template v-if="dealerList.length > 0">
                        <div class="dealer-selected-wrapper">
                            <n-checkbox 
                                class="check-all" 
                                v-model:checked="model.checkedAllRef" 
                                label="全选"
                                :disabled="selectAll"
                                @update:checked="handleCheckedAllChange" 
                            />
                            <n-spin :show="isGetLoading" size="small" style="height: calc(100% - 36px);">
                                <div class="dealer-selected-list" @scroll="handleScroll">
                                    <n-checkbox-group v-model:value="checkDealerKeysRef" @update:value="handleUpdateValue">
                                        <n-space item-style="display: flex;" vertical>
                                            <n-checkbox class="dealer-list-item" v-for="item in dealerList" :value="item.id"
                                                :key="item.id"
                                                :disabled="item.disabled">
                                                <template #default>
                                                    <CustomOptions :infoItem="item" />
                                                </template>
                                            </n-checkbox>
                                        </n-space>
                                    </n-checkbox-group>
                                </div>
                            </n-spin>
                        </div>
                    </template>
                    <template v-else>
                        <JEmpty />
                    </template>
                </transition>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, toRaw, effectScope, watch, onScopeDispose } from "vue";
import { useGetSgDealer } from "../hooks";
import { SystemSetting } from "@/settings/systemSetting";
/** 相关组件 */
import CustomOptions from "./CustomOptions.vue";
import JEmpty from "./JEmpty.vue";

defineOptions({ name: 'JDealerTransfer' });

/** props */
const  props = withDefaults(defineProps<{
    dealerIds: Array<string | number>;
    communityDealersState:boolean //是否为分账入账方管理--社群经销商账号新增
    selectedId:any //分账入账方管理--社群经销商账号已被选中的经销商
}>(), {
    dealerIds: () => [],
    communityDealersState:false
});

/** emits */
const emits = defineEmits<{
    (e: "update:checked-keys", meta: { action: 'check' | 'uncheck'  | 'checkAll' | 'uncheckAll', currentId: string | number | Array<string | number>, currentItem: Object | Array<Object> }): void;
}>();

/** 经销商数据 */
const {
    loading,
    isGetLoading,
    treeData, 
    dealerList,
    checkedKeysRef,
    searchValue,
    _params,
    getDealerGroupsSelectList, 
    getSgDealerInfoList,
    handleCheckedKeysFn,
    handleScroll
} = useGetSgDealer();

/** 初始化 */
const model = ref({
    checkedAllRef: false,
});

/** 选中的经销商id */
const checkDealerKeysRef = ref<(string | number)[]>([]);

/** 搜索 */
const handleSearch = () => {
    // 清空当前页码
    _params.pageVO.current = 1;
    _params.pageVO.size = SystemSetting.pagination.pageSize;
    getSgDealerInfoList();
};

/** 全选 */
function handleCheckedAllChange(checked: boolean) {
    const currentId = dealerList.value.map(item => item.id);
    if (checked) {
        // 触发事件
        emits("update:checked-keys", { action: 'checkAll', currentId, currentItem: toRaw(dealerList.value) });
    } else {
        // 触发事件
        emits("update:checked-keys", { action: 'uncheckAll', currentId, currentItem: toRaw(dealerList.value) });
    }
}

/** 选项组的值改变时的回调 */
function handleUpdateValue(value: Array<string | number>, meta: { actionType: 'check' | 'uncheck', value: string | number }) {
    const { actionType, value: selectId } = meta;

    // 查找当前选中的项
    const currentItem = toRaw(dealerList.value.find(dealer => dealer.id === selectId));
    // 触发事件
    emits("update:checked-keys", { action: actionType, currentId: selectId, currentItem });
}

/** 初始化 */
function init() {
    getDealerGroupsSelectList();
    getSgDealerInfoList();
}
init();

/** 创建effectScope */
const scope = effectScope();

/** 全选按钮是否禁用 */
const selectAll = ref(false)

/** 作用域内运行一组副作用 */
scope.run(() => {
    // 监听
    watch(() => props.dealerIds, (newVal) => {
        if (newVal) {
            checkDealerKeysRef.value = newVal;
        }
    }, { immediate: true, deep: true });

    // 监听
    watch(() => [checkDealerKeysRef.value, dealerList.value], (newVal) => {
        if (dealerList.value.every(item => checkDealerKeysRef.value.includes(item.id))) {
            model.value.checkedAllRef = true;
        } else {
            model.value.checkedAllRef = false;
        }
    });
  });


watch(
  () => dealerList.value,
  (newVal) => {
    if(props.communityDealersState){
       /** 已选中的经销商改为禁用 id赋值为空 */
        const updatedList = newVal.map((item) => {
          if (props.selectedId.includes(item.id)) {
              return { ...item, disabled: true,id:null,accountUserId:item.id};
          }
         return item;
        });
      /** 当前有经销商被选中 禁用全选按钮 */
      if (JSON.stringify(updatedList) !== JSON.stringify(dealerList.value)) {
        dealerList.value = updatedList;
        dealerList.value.map((item)=>{
          if(item.disabled){
              return selectAll.value = true
          }
        })
      }
    }
  },
  { deep: true }
);

/** 分组按钮事件监听 */
watch(
  () => checkedKeysRef.value,
  (newVal) => {
    selectAll.value = false
  }
);

/** 作用域销毁时，停止作用域 */
onScopeDispose(() => {
  scope.stop();
});
</script>

<style lang="less" scoped>
@import "@/styles/scrollbar.less";

.wrapper {
    width: 100%;
    height: 288px;
    display: flex;
    border: 1px solid #eee;
    :deep(.n-spin-content) {
        width: 100%;
        height: 100%;
    }

    .left {
        height: 100%;
        border-right: 1px solid #EEEEEE;
        overflow-y: auto;
        box-sizing: border-box;
        padding: 8px 12px;
        .scrollbar();
    }

    .right {
        width: 54%;
        height: 100%;
        box-sizing: border-box;

        .header {
            height: 28px;
            padding: 8px 12px;
        }

        .dealer-list-wrapper {
            height: calc(100% - 52px);
            padding-left: 12px;
            padding-bottom: 8px;
            box-sizing: border-box;

            .dealer-selected-wrapper {
                width: 100%;
                height: 100%;

                .check-all {
                    width: 100%;
                    height: 44px;
                    display: flex;
                    align-items: center;
                    padding: 2px 8px;
                    box-sizing: border-box;
                }

                :deep(.n-spin-content) {
                    height: 100%;
                }

                .dealer-selected-list {
                    height: 100%;
                    overflow-y: auto;
                    .scrollbar();

                    .dealer-list-item {
                        width: 100%;
                        display: flex;
                        align-items: center;
                        padding-left: 8px;
                        border-radius: 2px;

                        &:hover {
                            background-color: rgb(231, 241, 255);
                        }
                    }
                }
            }
        }
    }
}

:deep(.n-tree.n-tree--block-line .n-tree-node:not(.n-tree-node--disabled).n-tree-node--selected) {
    background-color: #fff;
}
</style>
