<template>
  <FormLayout
      :isLoading="isLoading"
      :tableData="tableData" 
      :tableColumns="tableColumns"
      :pagination="paginationRef"
      @paginationChange="paginationChange"
      :isTableSelection="true"
    >
    <template #searchForm>
      <n-form
        ref="formRef"
        :model="formValue"
        :show-feedback="false"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
        size="small"
        :style="{ width: '100%' }"
      >
        <!-- 客户关键字-->
        <n-form-item label="客户关键字" path="range">
          <n-input-group>
            <n-select
              v-model:value="formValue.searchType"
              placeholder="请选择"
              :options="searchTypeOptions"
              style="width: 120px;"
            />
            <JSearchInput
              v-model:value="formValue.searchValue"
              placeholder="请输入昵称、姓名、手机号"
              @search="formSearch"
              :width="240"
            />
          </n-input-group>
        </n-form-item>
        <!-- 来源 -->
        <n-form-item label="来源">
          <n-select 
            v-model:value="formValue.source" 
            :options="orderSourceOptions" 
            style="width: 170px;" 
            clearable
          />
        </n-form-item>
        <!-- 商品 -->
        <n-form-item
          v-if="props.tabNameRef == PrescriptionSelection.alreadyOpen || props.tabNameRef == PrescriptionSelection.placeAnOrder"
          label="商品" 
        >
          <JGoodsSelect v-model:value="formValue.goodsValue" />
        </n-form-item>
        <!-- 订单ID -->
        <n-form-item
          v-if="props.tabNameRef == PrescriptionSelection.placeAnOrder"
          label="订单ID" 
        >
          <n-input v-model:value="formValue.orderId" placeholder="请输入订单ID"  style="width: 170px !important;" @keyup.enter.native="formSearch" clearable/>
        </n-form-item>
      </n-form>
    </template>
    <!-- 操作栏 -->
    <template #tableHeaderBtn="scoped">
      <n-button @click="refresh" class="store-button">刷 新</n-button>
      <!-- 批量取消 -->
      <n-button 
        ghost
        type="primary"  
        @click="batchCancellationRecover('batch-cancel', scoped.selectedListIds)" 
        v-if="props.tabNameRef == PrescriptionSelection.unopened && hasPrescriptionRecoverOrCancel" 
        :disabled="!scoped.isSelected"
       >
        批量取消
      </n-button>
      <!-- 批量恢复 -->
<!--      <n-button-->
<!--        v-if="props.tabNameRef == PrescriptionSelection.cancelled && hasPrescriptionRecoverOrCancel"-->
<!--        ghost-->
<!--        type="primary"-->
<!--        @click="batchCancellationRecover('batch-recover', scoped.selectedListIds)"-->
<!--        :disabled="!scoped.isSelected"-->
<!--      >-->
<!--        批量恢复-->
<!--      </n-button>-->
      <!-- 批量删除 -->
      <n-popconfirm
          v-if="props.tabNameRef == PrescriptionSelection.cancelled && hasPrescriptionDelete"
          @positive-click="clickDelete('batch', scoped.selectedListIds, scoped.selectedList)"
        >
          <template #trigger>
            <n-button 
              type="error" 
              ghost 
              :disabled="!scoped.isSelected"
            >
              批量删除
            </n-button>
          </template>
          确实删除此数据?
        </n-popconfirm>
    </template>
   </FormLayout>
   <DrawerPrescription ref="drawerPrescriptionShow"/>
</template>
<script setup lang="tsx">
import { ref, watch } from 'vue';
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { getPrescriptionDetail, presPage, presBatchUpdateStatus, presBatchDelete, presUpdate, presPrescribing } from "@/services/api";
import { useMessages } from "@/hooks";
import { PrescriptionSelection, PrescriptionType, GoodsCategoryType } from '@/enums';
import JCopyOptBtn from "./JCopyOptBtn.vue";
import JButton from "./JButton.vue";
import { deepClone } from "@/utils";
import DrawerPrescription from './drawerPrescription.vue';
import { orderSourceOptions, orderSourceLabels } from "@/constants";
import JImage from "@/components/JImage/index.vue";
/** 权限 */
import {
  hasAddPrescription, 
  hasPrescriptionRecoverOrCancel, 
  hasPrescriptionEdit, 
  hasPrescriptionDetails,
  hasPrescriptionDelete
} from '../authList';

interface prescriptionProps {
  tabNameRef: string; // tab标签值
}

/** props */
const props = withDefaults(defineProps<prescriptionProps>(), {
  tabNameRef: 'unopened'
});

const { createMessageSuccess, createMessageError } = useMessages();

/* 初始化参数 */
const tabValue = ref(null);
const initParams = {
  searchValue: '', // 搜索关键字
  source: null, // 来源
  orderId: null, // 订单ID
  goodsValue: null, // 商品
  searchType: 'nickname'
};
const formValue = ref({ ...initParams });

/* 表格方法Hook */
const {
  isLoading,
  tableData,
  paginationRef,
  paginationChange,
  summaryRef,
  sortTableData,
  pageTableData,
} = useTableDefault({
  pageDataRequest: presPage,
});

//刷新
const refresh = () =>{
  getTableData()
}

/** 搜索 */
const formSearch = () =>{
  getTableData()
}

/** 搜索类型 */
const searchTypeOptions = [
  {
    label: "客户昵称",
    value: "nickname",
  },
  {
    label: "用药人姓名",
    value: "name",
  },
  {
    label: "手机号码",
    value: "mobile",
  },
];

/** 获取参数 */
const getParams = () => {
  const { source, goodsValue, orderId, searchType, searchValue } = formValue.value;

  return {
    status: tabValue.value,
    fromType: source,
    productId: goodsValue,
    orderId,
    searchType,
    searchValue
  }
};

/** 请求表格数据 */
function getTableData() {
  pageTableData(getParams(), paginationRef.value, true);
}

/** 弹窗类型 */
const getDrawerType = (type) => {
  switch (type) {
    case PrescriptionSelection.unopened:
      return hasAddPrescription ?  PrescriptionType.beginPrescription : null;
    case PrescriptionSelection.alreadyOpen:
      return hasPrescriptionEdit ? PrescriptionType.editPrescription : null;
    default:
      return hasPrescriptionDetails ? PrescriptionType.detailsPrescription : null;
  }
};

/* 表格项 */
const tableColumns = ref([]);
const tableColumnsSource = [
  {
    title: "头像",
    key: "img",
    align: "left",
    fixed: "left",
    width: 100,
    render: rowData => {
      return <JImage imgPath={rowData.img} />;
    },
  },
  {
    title: "用户昵称",
    key: "nickname",
    align: "left",
    width: 100,
  },
  {
    title: "用药人",
    key: "name",
    align: "left",
    width: 100,
  },
  {
    title: "手机号",
    key: "mobile",
    align: "left",
    width: 120,
  },
  {
    title: "来源",
    key: "fromType",
    align: "left",
    width: 100,
    render: rowData => {
      return <span>{orderSourceLabels[rowData.fromType] ?? '-'}</span>;
    },
  },
  {
    title: "社群管理",
    key: "thirdGroupMgrName",
    align: "left",
    width: 120,
    render: rowData => {
      return <JCopyOptBtn value={rowData.thirdGroupMgrName} id={rowData.thirdGroupMgrId} />;
    },
  },
  {
    title: "社群课程入口",
    key: "thirdCourseName",
    align: "left",
    width: 160,
    render: rowData => {
      return  <JCopyOptBtn value={rowData.thirdCourseName} id={rowData.thirdCourseId} />;
    },
  },
  {
    title: "商品",
    key: "productList",
    align: "left",
    width: 160,
    render: (row) => {
      if (!row.productList) {
        return '-';
      }
      const formattedItems = row.productList.map(item => {
        switch (Number(row.productType)) {
          case GoodsCategoryType.DRUG:
            return '药品-' + item;
          case GoodsCategoryType.THERAPY:
            return '疗法-' + item;
          default:
            return item;
        }
      });
      return (
        <div style={{display:'flex','flex-wrap':'wrap',width:"100%"}}>
          {formattedItems.map(item => (
            <n-tag style="width: 100%;">
              <n-ellipsis trigger="hover" style="padding: 2px 0px;">{item}</n-ellipsis>
            </n-tag>
          ))}
        </div>
      );
    }
  },
  {
    title: "订单",
    key: "orderId",
    align: "left",
    width: 160,
  },
  {
    title: "创建时间",
    key: "createTime",
    align: "left",
  },
  {
    title: "操作",
    key: "action",
    width: 80,
    align: "left",
    fixed: "right",
    render: (row) => {
      return (
        <div class="action-wrapper">
          {/* 开处方、编辑、详情 */}
          <JButton 
            isShow={tabValue.value != PrescriptionSelection.cancelled}
            onClick={() => clickPrescribe(row, getDrawerType(tabValue.value))}
          >
            {getDrawerType(tabValue.value)}
          </JButton>
          {/* 取消 */}
          {hasPrescriptionRecoverOrCancel ? 
            <JButton 
            isShow={tabValue.value == PrescriptionSelection.unopened}
            onClick={() => batchCancellationRecover('single-cancel', [row.id])}
          >
            取消
          </JButton> : null}
          {/* 删除 */}
          
          {tabValue.value == PrescriptionSelection.cancelled ? <n-popconfirm
            onPositiveClick={() => clickDelete('single', [row.id], [row])}
            >
            {{
              trigger: () => (
                hasPrescriptionDelete ? <n-button text size="small" type="error">删除</n-button> : null
              ),
              default: () => <span>{`是否确定删除用药人《${row?.name ?? ''}》的数据？`}</span>
            }}
            </n-popconfirm> : null}
        </div>
      );
    },
  },
];

/** 开处方 与 编辑 */
const drawerPrescriptionShow = ref<InstanceType<typeof DrawerPrescription> | null>(null);
const clickPrescribe = (row,type) =>{
  const _params = {
    row,
    getInfoApi: getPrescriptionDetail,
    updateInfoApi:type == '编辑' ? presUpdate : presPrescribing,
    refresh: refresh,
    type:type
  };
  drawerPrescriptionShow.value?.acceptParams(_params);
};

/** 批量取消、取消、恢复 */
const batchCancellationRecover = async(type: 'single-cancel' | 'batch-cancel' | 'single-recover'| 'batch-recover', ids: Array<string>) =>{
  const params = {
    idList: ids,
    // status: tabValue.value == 0 ? PrescriptionSelection.cancelled : PrescriptionSelection.unopened,
  };
  const msg: Record<'single-cancel' | 'batch-cancel' | 'single-recover'| 'batch-recover', string> = {
    'single-cancel': '取消', 
    'batch-cancel': '批量取消', 
    'single-recover': '恢复',
    'batch-recover': '批量恢复',
  };
  try{
    await presBatchUpdateStatus(params);
    createMessageSuccess(`${msg[type]}成功`);
    refresh()
  }catch(err){
    createMessageError(`${msg[type]}失败: ${err}` )
  }
};

/** 删除 */
const clickDelete = async(type: 'single' | 'batch', selectId: Array<string>, selectRowList: Array<any>) =>{
  // customerIdList
  const createByList = selectRowList.map(item => item.createBy);

  const params = {
    idList: selectId,
    createByList: createByList,
  }
  try{
    await presBatchDelete({data:params})
    createMessageSuccess(type === 'batch' ? '批量删除成功' : '删除成功');
    refresh()
  }
  catch(err){
    createMessageError(`${type === 'batch' ? '批量删除失败' :'删除失败'}: ${err}`);
  }
};

/** 监听 */
watch(() => [
    formValue.value.source,
    formValue.value.goodsValue, 
  ], 
  () => {
    getTableData();
  }
);

/** 监听 */
watch(() => props.tabNameRef, (newVal) => {
  tableColumns.value = deepClone(tableColumnsSource);
  tabValue.value = newVal;
  getTableData();

  // 处方管理 -- PrescriptionSelection.placeAnOrder 已下单
  if(tabValue.value !== PrescriptionSelection.placeAnOrder) {
    tableColumns.value = tableColumns.value.filter(column => {
      return column.key !== 'orderId';
    });
  }
  let targetColumn = tableColumns.value.find(column => column.key === "createTime" || column.key === "diagTime");
  // 处方管理 -- 待开方 || 已取消
  if(tabValue.value == PrescriptionSelection.unopened || tabValue.value == PrescriptionSelection.cancelled) {
    targetColumn.title = "创建时间";
    targetColumn.key = "createTime";
  }
  // 处方管理 -- 已开方 || 已下单
  if(tabValue.value == PrescriptionSelection.alreadyOpen || tabValue.value == PrescriptionSelection.placeAnOrder) {
    targetColumn.title = "开处方时间";
    targetColumn.key = "diagTime";
  }
},
{
  immediate: true
});
</script>

<style lang="less" scoped>
@import "@/styles/default.less";
:deep .n-tag__content {
  text-overflow:ellipsis;
  overflow: hidden;
}
.action-wrapper {
  display: flex;
  align-items: center;
}
</style>