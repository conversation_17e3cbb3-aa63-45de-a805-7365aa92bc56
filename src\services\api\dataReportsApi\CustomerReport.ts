import { defHttp } from '@/services';

export const enum CustomerReportsEnum{
    page="/report/customerStatData",
    export = "/report/customerStatDataExport",
    // 会员消费榜
    memberConsumeRank="/report/customerConsumeStatData",
}

/** 会员消费榜 */
export function getMemberConsumeRank(params) {
    return defHttp.post({
        url: CustomerReportsEnum.memberConsumeRank,
        params,
    });
}

export function getCustomerReportsPage(params) {
    return defHttp.post({
        url: CustomerReportsEnum.page,
        params,
    });
}
export function exportCustomerReportsPage(params) {
    return defHttp.post({
        url: CustomerReportsEnum.export,
        requestConfig:{
            responeseType:'stream'
          },
        params,
    });
}