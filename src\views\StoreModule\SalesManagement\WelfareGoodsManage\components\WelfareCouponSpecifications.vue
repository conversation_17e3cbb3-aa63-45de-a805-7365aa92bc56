<template>
  <div class="selling-wrapper">
    <!-- Header 头部操作区 -->
    <div class="header-actions">
      <!-- 操作按钮组 -->
      <div class="action-buttons">
        <!-- 新增规格按钮 -->
        <JTextButton text type="primary" @click="handleAddSpec">新增规格</JTextButton>
        <!-- 删除规格按钮（当有选中项时可点击） -->
        <JTextButton text type="error" @click="handleDeleteSpec" :disabled="!hasSelectedSpecs">删除规格</JTextButton>
      </div>
    </div>

    <!-- 数据表格 -->
    <NDataTable
      :columns="columns"
      :data="specifications"
      :row-key="getRowKey"
      default-expand-all
      :style="{ height: `${400}px` }" 
      flex-height
      :single-line="false"
      size="small"
      @update:checked-row-keys="handleSelectionChange"
    />
  </div>
</template>

<script setup lang="tsx">
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue';
import type { DataTableRowKey } from 'naive-ui';
import { createDummyId } from '@/utils';
import { useGetWelfareList } from '@/hooks/business';
/** 相关组件 */
import JWelfareList from '@/components/JSelect/JWelfareList.vue';

/**
 * 福利券规格接口定义
 */
interface WelfareCouponSpecification {
  id: string;                   // 唯一标识
  couponProductId?: string;      // 关联商品ID
  specName?: string;            // 规格名称
  availStock?: number;         // 可用库存
  lockedStock?: number;        // 锁定库存
  limitPerPurchase?: number;    // 每单限购数量
  soldCount?: number;           // 已售数量
  skuCode?: string;             // SKU编码
  couponCateId?: string;        // 福利券分类ID
  exchangeCount?: number;       // 兑换所需数量
  purchasePrice?: number;       // 采购价格
}

// 组件属性定义
const props = defineProps<{
  value: WelfareCouponSpecification[];  // 规格数据
  type: 'add' | 'edit';                 // 规格类型
}>();

// 组件事件定义
const emit = defineEmits<{
  (e: 'update:value', value: WelfareCouponSpecification[]): void;        // 数据更新事件
  (e: 'productSpecVOList', value: WelfareCouponSpecification[]): void;  // 产品规格列表事件
}>();

// ============= 状态管理 =============
const selectedSpecIds = ref<DataTableRowKey[]>([]);            // 选中的规格ID集合
const specifications = ref<WelfareCouponSpecification[]>([]);  // 规格数据
const { selectListData, isLoading, getSelectList, clearCache } = useGetWelfareList();

// ============= 计算属性 =============
/**
 * 是否有选中的规格
 */
const hasSelectedSpecs = computed(() => selectedSpecIds.value.length > 0);
/**
 * 是否编辑
 */
 const isEdit = computed(() => props.type === 'edit');

// ============= 方法定义 =============
/**
 * 获取行唯一标识
 */
const getRowKey = (row: WelfareCouponSpecification) => row.id;

/**
 * 新增规格
 */
const handleAddSpec = () => {
  // 创建新规格对象，使用第一个规格的soldCount作为默认值（如果存在）
  const newSpec: WelfareCouponSpecification = {
    id: createDummyId(),
    specName: undefined,
    purchasePrice: undefined,
    availStock: undefined,
    lockedStock: null,
    limitPerPurchase: null,
    soldCount: specifications.value[0]?.soldCount ?? 0,
    skuCode: undefined,
    couponCateId: undefined,
    exchangeCount: undefined,
  };

  // 添加新规格并触发更新
  specifications.value = [...specifications.value, newSpec];
  emitChanges();
};

/**
 * 删除选中规格
 */
const handleDeleteSpec = () => {
  // 没有选中项时直接返回
  if (!hasSelectedSpecs.value) return;

  // 过滤掉选中的规格
  specifications.value = specifications.value.filter(
    spec => !selectedSpecIds.value.includes(spec.id)
  );

  // 确保至少保留一个规格（如果没有则新增一个）
  if (specifications.value.length === 0) {
    handleAddSpec();
  }

  // 清空选中状态并触发更新
  selectedSpecIds.value = [];
  emitChanges();
};

/**
 * 处理选中项变化
 */
const handleSelectionChange = (keys: DataTableRowKey[]) => {
  selectedSpecIds.value = keys;
};

/**
 * 更新规格字段值
 * @param id 规格ID
 * @param field 字段名
 * @param value 新值
 */
const handleUpdateSpecField = <K extends keyof WelfareCouponSpecification>(
  id: string,
  field: K,
  value: WelfareCouponSpecification[K]
) => {
  // 更新指定规格的字段值
  specifications.value = specifications.value.map(spec =>
    spec.id === id ? { ...spec, [field]: value } : spec
  );
  emitChanges();
};

/**
 * 触发数据变更事件
 */
const emitChanges = () => {
  emit('update:value', specifications.value);
  emit('productSpecVOList', specifications.value);
};

// ============= 表格列定义 =============
const columns = [
  // 选择列
  {
    type: 'selection',
    key: 'selection',
    fixed: 'left',
    width: 50,
    align: 'center',
  },
  // 规格名称列
  {
    title: '规格名称',
    key: 'specName',
    render: row => (
      <n-input
        value={row.specName}
        onUpdateValue={value => handleUpdateSpecField(row.id, 'specName', value)}
        placeholder="请输入规格名称"
        maxlength={30}
        show-count
      />
    ),
  },
  // 可用库存列
  {
    title: '可用库存',
    key: 'availStock',
    width: 120,
    render: row => (
      <n-input-number
        value={row.availStock}
        onUpdateValue={value => handleUpdateSpecField(row.id, 'availStock', value)}
        placeholder="请输入库存"
        min={0}
        max={9999}
        show-button={false}
        precision={0}
        style={{ width: '100%' }}
      />
    ),
  },
  // 采购价格列
  {
    title: '成本价',
    key: 'purchasePrice',
    render: row => (
      <n-input-number
        value={row.purchasePrice}
        onUpdateValue={value => handleUpdateSpecField(row.id, 'purchasePrice', value)}
        placeholder="请输入成本价"
        min={0}
        max={999999}
        precision={2}
        show-button={false}
      />
    ),
  },
  // 限购数量列
  {
    title: '每订单上限',
    key: 'limitPerPurchase',
    width: 120,
    render: row => (
      <n-input-number
        value={row.limitPerPurchase}
        onUpdateValue={value => handleUpdateSpecField(row.id, 'limitPerPurchase', value)}
        placeholder="请输入上限"
        min={0}
        max={99999}
        precision={0}
        show-button={false}
      />
    ),
  },
  // 已售数量列
  {
    title: '初始已兑',
    key: 'soldCount',
    resizable: true,
    rowSpan: (rowData, rowIndex) => specifications.value.length,
    render: row => (
      <n-input-number
        value={row.soldCount}
        onUpdateValue={value => handleUpdateSpecField(row.id, 'soldCount', value)}
        placeholder="请输入初始已兑"
        min={0}
        max={10000}
        precision={0}
        show-button={false}
      />
    ),
  },
  // 福利券分类列
  {
    title: '福利券兑换种类',
    key: 'couponCateId',
    render: row => (
      <JWelfareList
        value={row.couponCateId}
        selectListData={selectListData.value}
        onFocus={getSelectList}
        loading={isLoading.value}
        onUpdate:value={value => handleUpdateSpecField(row.id, 'couponCateId', value)}
        style={{ width: '100%' }}
      />
    ),
  },
  // 兑换数量列
  {
    title: '兑换所需福利券数量',
    key: 'exchangeCount',
    render: row => (
      <n-input-number
        value={row.exchangeCount}
        onUpdateValue={value => handleUpdateSpecField(row.id, 'exchangeCount', value)}
        placeholder="请输入兑换所需数量"
        min={0}
        max={10000}
        precision={0}
      />
    ),
  },
  // 商品编码列
  {
    title: '商品编码',
    key: 'skuCode',
    render: row => (
      <n-input
        value={row.skuCode}
        onUpdateValue={value => handleUpdateSpecField(row.id, 'skuCode', value)}
        placeholder="请输入商品编码"
      />
    ),
  },
];

onMounted(() => {
  getSelectList();
});

onBeforeUnmount(() => {
  clearCache();
});
// ============= 数据监听 =============
/**
 * 监听props.value变化，更新本地数据
 */
watch(
  () => props.value,
  (newValue) => {
    if (newValue) {
      specifications.value = [...newValue];
    }
  },
  { immediate: true, deep: true }
);
</script>

<style lang="less" scoped>
.selling-wrapper {
  position: relative;

  /* 头部操作区样式 */
  .header-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    position: absolute;
    top: -38px;
    right: 0;

    /* 操作按钮组样式 */
    .action-buttons {
      display: flex;
      gap: 8px;  /* 按钮间距 */
    }
  }

  /* 表格样式 */
  .spec-table {
    border: 1px solid #eee;
    border-radius: 4px;
    overflow: hidden;
  }
}

/* 复选框样式覆盖 */
:deep(.n-checkbox .n-checkbox-box) {
  width: 18px;
  height: 18px;
}
</style>
