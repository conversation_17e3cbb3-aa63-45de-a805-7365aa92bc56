<template>
  <JModal
    v-model:show="show"
    width="1000"
    :title="Add_active?'新建业务':'编辑业务'"
    @after-leave="closeModal"
    @positive-click="_save"
    :positiveButtonProps="{
			loading: isLoading
		}"
  >
    <n-form
      ref="formRef"
      :rules="dynamicRules"
      :model="model"
      label-width="auto"
      label-placement="left"
      require-mark-placement="right-hanging"
      :style="{
          width: '100%',
        }"
    >
      <n-grid :cols="8" :x-gap="24">
        <n-form-item-gi :span="24" label="流程名称" path="name">
          <n-input
            v-model:value="model.name"
            type="text"
            show-count
            style="width: 100%"
            maxlength="30"
            placeholder="请输入流程名称"
            clearable
          />
        </n-form-item-gi>
        <template v-for="(reviewerItem,index) in model.reviewerList" :key="reviewerItem.uuid">
          <n-form-item-gi :span="24" :label="getLabelName(index)" :path="`reviewerList[${index}].stepDefAuditorVOList`">
            <n-flex vertical>
              <n-flex>
                <n-tag
                  size="large"
                  v-for="(subItem,subIndex) in reviewerItem?.stepDefAuditorVOList"
                  :key="subItem.auditorId"
                  closable
                  @close="handleCloseTag(index,subIndex)"
                >
                  {{ subItem.auditorName }}
                </n-tag>
                <JSelectOperators
                  v-model:value="operationSelectValue"
                  @update:selected-options="(selectOptions)=>handeOperators(selectOptions,index)"
                />
                <n-button
                  v-show="model.reviewerList.length>1 && index!=0  && Add_active"
                  type="error"
                  @click="deleteReviewerItem(index)"
                >
                  删除环节
                </n-button>
              </n-flex>
            </n-flex>
          </n-form-item-gi>
        </template>
        <n-form-item-gi v-if="Add_active && model.reviewerList.length<maxAddReviewerList">
            <n-button type="info" @click="addReviewerItem" style="margin-left: 70px">新增环节</n-button>
        </n-form-item-gi>

        <!--        <n-form-item-gi :span="24" label="审批人员" path="reviewerList" required>-->
        <!--          <n-flex vertical>-->
        <!--            <n-space v-for="(reviewerItem,index) in model.reviewerList" :key="reviewerItem.uuid">-->
        <!--              <n-flex :wrap="false">-->
        <!--                <span style="flex-shrink: 0;height: 34px;line-height: 34px">-->
        <!--                  {{ numberToChinese(index + 1) }}审人员-->
        <!--                </span>-->
        <!--                <n-flex>-->
        <!--                  <n-tag-->
        <!--                    size="large"-->
        <!--                    v-for="(subItem,subIndex) in reviewerItem?.stepDefAuditorVOList"-->
        <!--                    :key="subItem.auditorId"-->
        <!--                    closable-->
        <!--                    @close="handleCloseTag(index,subIndex)"-->
        <!--                  >-->
        <!--                    {{ subItem.auditorName }}-->
        <!--                  </n-tag>-->
        <!--                  <JSelectOperators-->
        <!--                    v-model:value="operationSelectValue"-->
        <!--                    @update:selected-options="(selectOptions)=>handeOperators(selectOptions,index)"-->
        <!--                  />-->
        <!--                  <n-button-->
        <!--                    v-show="model.reviewerList.length>1 && index!=0  && Add_active"-->
        <!--                    type="error"-->
        <!--                    @click="deleteReviewerItem(index)"-->
        <!--                  >-->
        <!--                    删除环节-->
        <!--                  </n-button>-->
        <!--                </n-flex>-->
        <!--              </n-flex>-->
        <!--            </n-space>-->
        <!--            <n-space v-if="Add_active && model.reviewerList.length<maxAddReviewerList">-->
        <!--              <n-button type="info" style="margin-left: 70px" @click="addReviewerItem">新增环节</n-button>-->
        <!--            </n-space>-->
        <!--          </n-flex>-->
        <!--        </n-form-item-gi>-->
        <n-form-item-gi :span="24" label="启用" path="enabled">
          <n-radio-group v-model:value="model.enabled" name="active">
            <n-space>
              <n-radio v-for="song in radio" :key="song.value" :value="song.value">
                {{ song.label }}
              </n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </JModal>
</template>

<script setup lang="ts" name="AddorEditVideo">
import { ref,computed } from "vue";
import { useMessages } from "@/hooks";
import JSelectOperators from "@/components/JSelect/JSelectOperators.vue";
import { getOperationFlowDetail } from "@/services/api";
import { uuid } from "@/utils";

/* 提示信息 */
const message = useMessages();

export interface AddCompanyModalProps {
  api?: (params: any) => Promise<any>;// 新增保存Api
  refreshTable?: () => void; // 刷新表格数据
  row?: Partial<ApiStoreModule.CarouselImg>;
  bizCode: string;
}

const Add_active = ref<boolean>(true);
type ReviewerItem = {
  id?: string,
  auditorId: string,
  auditorName: string
}
type ProcessStepDefVOItem = {
  id?: string,
  uuid?: string,
  stepDefAuditorVOList: ReviewerItem[]
}
type ReviewerList = ProcessStepDefVOItem[]
type FormDataType = {
  id?: string,
  name: string,
  reviewerList: ReviewerList,
  auditorName: number,
}
const initReviewerItem = () => {
  return { uuid: uuid(), stepDefAuditorVOList: [] };
};
const initParams = {
  id: null,
  name: null,
  reviewerList: [initReviewerItem()],
  enabled: 1,
};
const model = ref<FormDataType>({ ...initParams });
const operationSelectValue = ref(null);
const maxAddReviewerList = ref(3);

const addReviewerItem = () => {
  if (model.value.reviewerList.length >= maxAddReviewerList) {
    return message.createMessageError(`"最多添加${maxAddReviewerList}个"`);
  }
  operationSelectValue.value = null;
  model.value.reviewerList.push(initReviewerItem());
};
const deleteReviewerItem = (index) => {
  if (model.value.reviewerList.length <= 1) {
    return message.createMessageError("请至少保留一个环节");
  } else {
    model.value.reviewerList.splice(index, 1);
  }
};
const handleCloseTag = (fIndex, sIndex) => {
  model.value.reviewerList[fIndex]?.stepDefAuditorVOList.splice(sIndex, 1);
};


//获取选中的人员，并判断选中的人员是否已存在
function handeOperators(selectOptions, index) {
  if (selectOptions?.length > 0) {
    const currentItem = selectOptions[0];
    //判断选中的人员是否已存在
    if (model.value.reviewerList[index]?.stepDefAuditorVOList.some(item => item.auditorId === currentItem.value)) {
      message.createMessageError("该人员已选择");
    } else {
      model.value.reviewerList[index]?.stepDefAuditorVOList.push({
        auditorId: currentItem.value,
        auditorName: currentItem.label,
      });
    }
  }
  operationSelectValue.value = null;
}

// 获取数字对应的中文
function numberToChinese(num) {
  const map = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
  return map[num] || "";
}

function getLabelName(num) {
  return numberToChinese(num+1) + "审人员";
}

/* 模态框显隐状态 */
const show = ref(false);
/* 父组件传过来的参数 */
const parameter = ref<AddCompanyModalProps>({});

/* 处理数据结构 */
const handeStepDefDTOList = (stepDefDTOList) => {
  return stepDefDTOList.map(item => {
    return {
      id: item.id,
      stepDefAuditorVOList: item.stepDefAuditorDTOList.map(subItem => {
        return {
          id: subItem.id,
          auditorId: subItem.auditorId,
          auditorName: subItem.auditorName,
        };
      }),
    };
  });
};
/* 接收父组件参数 */
const acceptParams = (params: AddCompanyModalProps) => {
  parameter.value = params;
  if (params.row.id) {
    Add_active.value = false;
    getOperationFlowDetail({ id: params.row.id }).then(data => {
      const { id, name, stepDefDTOList, enabled } = data;
      model.value.id = id;
      model.value.name = name;
      model.value.enabled = enabled;
      if (stepDefDTOList.length > 0) {
        model.value.reviewerList = handeStepDefDTOList(stepDefDTOList);
      }
    }).catch(err => {
      message.createMessageError(`获取流程配置失败： ${err}`);
    });
  }
  show.value = true;
};
const radio = [
  {
    value: 1,
    label: "是",
  },
  {
    value: 0,
    label: "否",
  },
];
/* 动态表单规则 */
const dynamicRules = computed(() => {
  const baseRules = {
    name: {
      required: true,
      trigger: ["blur", "change"],
      message: "请输入流程名称",
    },
    enabled: {
      required: true,
      type: 'number',
      message: '请选择是否启用',
      trigger: 'change'
    }
  };

  model.value.reviewerList.forEach((_, index) => {
    baseRules[`reviewerList[${index}].stepDefAuditorVOList`] = {
      validator: (_, value) => {
        if (!Array.isArray(value) || value.length === 0) {
          return new Error("每个环节至少有一个审核员");
        }
        return true;
      },
      trigger: ["blur", "change"],
    };
  });

  return baseRules;
});
/* 表单实例 */
const formRef = ref(null);
/* 清空表单 */
const formDataReset = () => {
  model.value = { ...initParams, reviewerList: [initReviewerItem()] };
  Add_active.value = true;
  parameter.value = {};
};
/* 关闭弹窗 */
const closeModal = () => {
  formDataReset();
};

const getParams = () => {
  const { id, name, reviewerList, enabled } = model.value;
  const params = { name, bizCode: parameter.value?.bizCode, enabled };
  //修改
  if (!Add_active.value) {
    params.id = id;
  }
  //处理审核人员数据格式
  params.processStepDefVOList = reviewerList.map((item, index) => {
    return {
      stepName: `${numberToChinese(index + 1)}审`,
      id: item.id,
      stepDefAuditorVOList: item?.stepDefAuditorVOList.map(subItem => {
        return {
          id: subItem?.id,
          auditorId: subItem?.auditorId,
          auditorName: subItem?.auditorName,
        };
      }),
    };
  });
  return params;
};
/* 确认--保存 */
const isLoading = ref(false);
const _save = (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      try {
        isLoading.value = true;
        const params = getParams();
        await parameter.value.api(params).then(e => {
          if (Add_active.value) {
            message.createMessageSuccess(`添加业务流程成功`);
          } else {
            message.createMessageSuccess(`修改业务流程成功`);
          }
        });
        // 弹窗取消
        show.value = false;
        // 刷新表格数据
        parameter.value.refreshTable();
      } catch (e) {
        if (Add_active.value) {
          message.createMessageError(`新增业务流程失败： ${e}`);
        } else {
          message.createMessageError(`修改业务流程失败： ${e}`);
        }
      } finally {
        isLoading.value = false;
      }
    }
  });
};
defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less">
::v-deep .n-form-item-label {
  width: 70px !important;
}
</style>
