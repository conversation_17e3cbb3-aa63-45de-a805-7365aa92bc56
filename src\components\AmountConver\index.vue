<template>
  <span v-if="!isAnimation" class="amount-conver">{{ DataNumber }}</span>
  <n-number-animation v-else :to="Number(DataNumber)" :precision="2" />
</template>
<script setup lang="ts">
import { computed, toRefs } from "vue";
import { isNumber } from "@/utils/isUtils";
/* 接口 */
interface AmountConverProps {
  value: number | null; // 数值 --> 必传
  isAnimation?: boolean; // 数值是否动画 --> 不必传，默认为false
}
/* Props */
const props = withDefaults(defineProps<AmountConverProps>(),{
  isAnimation: false
});

const { value: valueRef, isAnimation } = toRefs(props);
const DataNumber = computed(() => {
  return isNumber(Number(valueRef.value))
    ? Number(valueRef.value / 100).toFixed(2)
    : "-";
});
</script>

<style lang="less" scoped>
@import "@/styles/default.less";
</style>
