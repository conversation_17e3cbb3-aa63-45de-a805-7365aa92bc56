<template>
  <div class="videoContainer" :style="{height,width}" @click="showVideoModal" >
    <div class="videoWrapperMask">
      <n-icon :component="CaretForwardCircleSharp" class="sharpIcon" size="30"></n-icon>
    </div>
    <video :src="videoPath" class="video"></video>
  </div>

  <n-modal
      v-model:show="isShow"
      preset="card"
      style="width: 400px; height: 600px"
      :auto-focus="false"
      title="视频预览"
      :content-style="{
        padding: '5px 10px',
      }"
      :header-style="{
        padding: '5px 10px',
        fontSize: '15px',
      }"
    >
      <video :src="videoPath" controls style="height: 550px; width: 100%"></video>
    </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, toRefs, onMounted, computed } from 'vue';
import { getOssFileUrlPrefix } from "@/utils/http/urlUtils";
import { CaretForwardCircleSharp } from '@vicons/ionicons5';

import { transformMinioSrc } from "@/utils/fileUtils"
const props = withDefaults(defineProps<{
  src: File | string ;
  height?:string | number;
  width?:string | number
}>(), {
  src: '',
  height:'150px',
  width:'150px'
})

const isShow = ref(false)
const showVideoModal = ()=>{
  isShow.value = true
}

const videoPath = computed(()=>{
  if ( typeof props.src == 'string' ) return transformMinioSrc(props.src)
  return URL.createObjectURL(props.src as File )
})

</script>
<style scoped lang="less">
.videoContainer {
  flex-shrink: 0;
  position: relative;

  .videoWrapperMask {
    position: absolute;
    top: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.3);
    width: 100%;
    height: 100%;
    z-index: 3;
  }

  .sharpIcon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
  }

  .video {
    height: 100%;
    width: 100%;
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
  }
}
</style>