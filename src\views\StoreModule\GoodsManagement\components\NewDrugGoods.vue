<template>
    <JDrawer 
      v-model:show="show" 
      :title="title" 
      :isGetLoading="isGetLoading"
      @after-leave="handleAfterLeave"
      @after-enter="handleAfterEnter"
    >
      <!-- 基本信息 -->
      <template #content>
        <n-form 
          ref="formRef"
          :model="model" 
          :rules="rules" 
          label-placement="left"
		      label-width="100"
		      require-mark-placement="right-hanging"
          size="small" 
          :style="{ width: '100%' }"
        >
          <n-grid :cols="12" :x-gap="12">
            <!-- 基本信息 -->
            <n-gi :span="10">
              <div class="title-wrapper">
                <div class="title-line"></div>
                <span>基本信息</span>
              </div>
            </n-gi>
            <!-- 药品通用名 -->
            <n-form-item-gi :span="10" label="药品通用名" path="name">
                <n-input 
                  v-model:value="model.name" 
                  placeholder="请输入药品通用名" 
                  :maxlength="60" 
                  clearable
                 />
            </n-form-item-gi>
            <!-- 商品名 -->
            <n-form-item-gi :span="10" label="商品名" path="frontName">
                <n-input 
                  v-model:value="model.frontName" 
                  placeholder="请输入商品名" 
                  :maxlength="60" 
                  clearable
                 />
            </n-form-item-gi>
            <!-- 规格 -->
            <n-form-item-gi :span="10" label="规格" path="specName">
              <n-grid x-gap="12" :cols="1">
                <n-gi>
                  <n-input
                    v-model:value="model.specName"
                    placeholder="请输入规格"
                    :maxlength="100"
                    clearable
                  />
                </n-gi>
                <n-gi>
                  <span style="font-size: 14px;">如果是中草药，规格请输入单位，譬如g或条</span>
                </n-gi>
              </n-grid>
            </n-form-item-gi>
            <!-- 厂家 -->
            <n-form-item-gi :span="10" label="厂家" path="manufacturer">
                <n-input 
                  v-model:value="model.manufacturer" 
                  placeholder="请输入厂家" 
                  :maxlength="60" 
                  clearable
                 />
            </n-form-item-gi>
            <!-- 所属分类 -->
            <n-form-item-gi :span="10" label="所属分类" path="cateId">
              <JProductTreeSelector 
                style="width: 100%;" 
                v-model:value="model.cateId" 
                :type="GoodsCategoryType.DRUG" 
                isImmediately
               />
            </n-form-item-gi>
            <!-- 药物类型 -->
            <n-form-item-gi :span="10" label="药物类型" path="isPres">
              <div style="display: flex;flex-direction: column;margin-bottom: 8px;">
                  <n-radio-group v-model:value="model.isPres" name="radiogroup" @update:value="handleRadioChange">
                    <n-space>
                      <n-radio v-for="item in drugTypeOptions" :key="item.value" :value="item.value">
                          {{ item.label }}
                      </n-radio>
                    </n-space>
                  </n-radio-group>
                  <span style="margin-top: 4px;font-size: 14px;">注：处方药需要客户填写姓名、身份证和病症等，提交处方，后台开处方后才能继续下订单</span>
              </div>
            </n-form-item-gi>
            <!-- 处方病症选项 处方药需要显示 -->
            <template v-if="model.isPres === DrugTypeEnum.Prescription">
              <n-form-item-gi 
                v-for="(item, index) in model.productPresOptionVOList" 
                :key="index" 
                :span="8"
                label="处方病症选项" 
                :path="`productPresOptionVOList[${index}].option`" 
                :rule="{
                  required: true,
                  message: '请输入处方病症',
                  trigger: ['input', 'blur']
                }"
              >
                <div style="width: 100%; display: flex; justify-content: center;align-items: center;">
                  <n-input 
                    v-model:value="item.option" 
                    type="text" 
                    width="100%" 
                    size="small" 
                    placeholder="请输入病症"
                    clearable
                    style="margin-right: 12px;"
                   />
                  <JTextButton
                    v-if="(index === model.productPresOptionVOList.length - 1)" 
                    size="small" 
                    text
                    type="primary"
                    @click="addItem"
                  >
                    新 增
                  </JTextButton>
                  <JTextButton 
                    v-if="model.productPresOptionVOList.length !== 1" 
                    size="small" 
                    text 
                    type="error"
                    @click="removeItem(index)"
                  >
                    删 除
                  </JTextButton>
                </div>
              </n-form-item-gi>
            </template>
            <!-- 商品图片 -->
            <n-gi :span="12" v-if="model.isPres!=DrugTypeEnum.ChineseHerbalMedicine">
                <n-grid :cols="24" :x-gap="12">
                    <n-form-item-gi :span="2" label="商品图片"></n-form-item-gi>
                    <n-form-item-gi 
                      :span="6" 
                      label="首图"
                      :path="model.productFirstImg.length || model.productMoreImg.length ? '' : 'productFirstImg'"
                    >
                      <UploadProductImg v-model:value="model.productFirstImg" accept="image/*" :fileListSize="1" />
                    </n-form-item-gi>
                    <n-form-item-gi 
                      :span="16" 
                      label="更多图片"
                      :path="model.productFirstImg.length || model.productMoreImg.length ? '' : 'productMoreImg'"
                     >
                      <UploadProductImg ref="uploadProductImgRef" v-model:value="model.productMoreImg" accept="image/*" :fileListSize="8" is-multiple />
                    </n-form-item-gi>
                </n-grid>
            </n-gi>
            <!-- 商品视频 *******版本新增 -->
            <n-form-item-gi v-if="model.isPres!=DrugTypeEnum.ChineseHerbalMedicine" :span="10" label="商品视频">
              <n-flex vertical :size="4">
                <UploadProductVideo v-model:value="model.productVideoPath" />
                <span style="font-size: 14px;">注：只支持mp4格式，视频时长不超过60秒，视频大小不超过200M，视频将默认展示在商品轮播图之前</span>
              </n-flex>
            </n-form-item-gi>
            <!-- 商品编码 -->
            <n-form-item-gi :span="10" label="商品编码" :path="props.isT9Sync ? 'sku' : ''">
                <n-input 
                  v-model:value="model.sku" 
                  placeholder="用于商家内部管理所使用的自定义编码" 
                  :maxlength="60" 
                  clearable
                 />
            </n-form-item-gi>
            <!-- 每人购买上限 1.0.8版本增加 -->
            <n-form-item-gi v-if="model.isPres!=DrugTypeEnum.ChineseHerbalMedicine" :span="10" label="每人购买上限" path="upperLimit">
              <n-input-number 
                v-model:value="model.upperLimit" 
                placeholder="请输入购买上限" 
                :min="0" 
                :max="99999" 
                :precision="0"
                :show-button="false"
              />
              <span style="margin-left: 12px;font-size: 14px;">注：0表示不设限</span>
            </n-form-item-gi>
            <!-- 销售场景 *******版本增加 -->
            <n-form-item-gi v-if="model.isPres!=DrugTypeEnum.ChineseHerbalMedicine" :span="10" label="销售场景" path="sellingScenario">
              <n-checkbox-group v-model:value="model.sellingScenario" @update:value="handleSellingScenarioChange" >
                <n-flex>
                  <n-checkbox :value="ProductSalesSceneEnum.Store" label="商城" />
                  <n-checkbox :value="ProductSalesSceneEnum.Social" :disabled="model.isPres === DrugTypeEnum.Prescription" >
                    <span>社群 <HelpPopover helpEntry="勾选社群" /> </span>
                  </n-checkbox>
                </n-flex>
                <n-checkbox :value="ProductSalesSceneEnum.IsHideInStore" :disabled="!model.sellingScenario.includes(ProductSalesSceneEnum.Store)" >
                  <span>在商城中隐藏 <HelpPopover helpEntry="在商城中隐藏" /> </span>
                </n-checkbox>
              </n-checkbox-group>
              
              <!-- <span style="margin-left: 12px;font-size: 14px;">注：勾选社群后，如开通群管易社群系统，商品可以在直播间挂车销售，处方药不支持</span> -->
            </n-form-item-gi>
            <!-- 售价与库存 -->
            <n-gi :span="12">
              <div class="title-wrapper">
                <div class="title-line"></div>
                <span>售价与库存</span>
                <span style="color: #FF4D4F;">*</span>
              </div>
            </n-gi>
            <n-form-item-gi :span="12" :show-label="false" path="productSpecVOList">
              <DrugSellingPriceInventory 
                v-model:value="model.productSpecVOList" 
                :type="pattern" 
                style="width: 98%;"
              />
            </n-form-item-gi>
            <!-- 图文描述 -->
            <n-gi :span="12">
              <div class="title-wrapper">
                <div class="title-line"></div>
                <span>图文描述</span>
              </div>
            </n-gi>
            <n-gi :span="12">
              <div style="height:520px; width: 98%; margin-bottom: 42px;">
                <div id="desc-richtext-container" ref="descRichTextDomRef" style="height:100%;width:100%;"></div>
              </div>
            </n-gi>
            <!-- 药品说明书 -->
			      <n-gi :span="12">
              <div class="title-wrapper" style="margin-top: 24px;">
                <div class="title-line"></div>
                <span>药品说明书</span>
                <n-button text size="small" type="primary" icon-placement="right" class="unpack" @click="isShowExpand = !isShowExpand">
                  {{ isShowExpand ? `收起` : `展开填写` }}
                  <template #icon>
                    <ChevronUpOutline v-if="isShowExpand" />
                    <ChevronDownSharp v-else />
                  </template>
                </n-button>
              </div>
            </n-gi>
            <!-- 药品说明书表单 -->
            <n-gi v-if="isShowExpand" :span="10">
              <n-grid :cols="12" :x-gap="12">
                <!-- 品牌 -->
                <n-form-item-gi :span="12" label="品牌">
                  <n-input 
                    v-model:value="model.productInsertsVO.brandName" 
                    placeholder="请输入品牌" 
                    :maxlength="60" 
                    clearable
                   />
                </n-form-item-gi>
                <!-- 功能主治 -->
                <n-form-item-gi :span="12" label="功能主治">
                  <n-input 
                    v-model:value="model.productInsertsVO.uses" 
                    type="textarea" 
                    maxlength="500" 
                    show-count
                    placeholder="请输入功能主治"
                    clearable
                  />
                </n-form-item-gi>
                <!-- 用法用量 -->
                <n-form-item-gi :span="12" label="用法用量">
                  <n-input 
                    v-model:value="model.productInsertsVO.dosage" 
                    placeholder="请输入用法用量" 
                    :maxlength="100"
                    clearable
                   />
                </n-form-item-gi>
                <!-- 不良反应 -->
                <n-form-item-gi :span="12" label="不良反应">
                  <n-input 
                    v-model:value="model.productInsertsVO.adverseReaction" 
                    type="textarea" 
                    maxlength="500" 
                    show-count
                    placeholder="请输入不良反应"
                    clearable
                  />
                </n-form-item-gi>
                <!-- 禁忌 -->
                <n-form-item-gi :span="12" label="禁忌">
                  <n-input 
                    v-model:value="model.productInsertsVO.contraindications" 
                    placeholder="请输入禁忌" 
                    :maxlength="100"
                    clearable
                   />
                </n-form-item-gi>
                <!-- 注意事项 -->
                <n-form-item-gi :span="12" label="注意事项">
                  <n-input 
                    v-model:value="model.productInsertsVO.precautions" 
                    type="textarea" 
                    maxlength="500" 
                    show-count
                    placeholder="请输入注意事项"
                    clearable
                  />
                </n-form-item-gi>
                <!-- 药物相互作用 -->
                <n-form-item-gi :span="12" label="药物相互作用">
                  <n-input 
                    v-model:value="model.productInsertsVO.drugInteraction" 
                    type="textarea" 
                    maxlength="500" 
                    show-count
                    placeholder="请输入药物相互作用"
                    clearable
                  />
                </n-form-item-gi>
                <!-- 贮藏 -->
                <n-form-item-gi :span="12" label="贮藏">
                  <n-input 
                    v-model:value="model.productInsertsVO.storage" 
                    placeholder="请输入贮藏" 
                    :maxlength="60"
                    clearable
                   />
                </n-form-item-gi>
                <!-- 是否外用 -->
                <n-form-item-gi :span="12" label="是否外用">
                  <n-select v-model:value="model.productInsertsVO.topical" :options="externalStatusOptions" clearable />
                </n-form-item-gi>
                <!-- 成份 -->
                <n-form-item-gi :span="12" label="成份">
                  <n-input 
                    v-model:value="model.productInsertsVO.composition" 
                    placeholder="请输入成份" 
                    :maxlength="60"
                    clearable
                   />
                </n-form-item-gi>
                <!-- 性状 -->
                <n-form-item-gi :span="12" label="性状">
                  <n-input 
                    v-model:value="model.productInsertsVO.description" 
                    placeholder="请输入性状" 
                    :maxlength="60"
                    clearable
                   />
                </n-form-item-gi>
                <!-- 剂型 -->
                <n-form-item-gi :span="12" label="剂型">
                  <JDosageForm v-model:value="model.productInsertsVO.unitDose" clearable />
                </n-form-item-gi>
                <!-- 包装 -->
                <n-form-item-gi :span="12" label="包装">
                  <n-input 
                    v-model:value="model.productInsertsVO.drugPackage" 
                    placeholder="请输入包装" 
                    :maxlength="60"
                    clearable
                   />
                </n-form-item-gi>
                <!-- 有效期 -->
                <n-form-item-gi :span="12" label="有效期">
                  <n-input 
                    v-model:value="model.productInsertsVO.validity" 
                    placeholder="请输入有效期" 
                    :maxlength="60"
                    clearable
                   />
                </n-form-item-gi>
                <!-- 产地类型 -->
                <n-form-item-gi :span="12" label="产地类型">
                  <n-select v-model:value="model.productInsertsVO.origin" :options="drugOriginOptions" clearable />
                </n-form-item-gi>
                <!-- 批准文号 -->
                <n-form-item-gi :span="12" label="批准文号">
                  <n-input 
                    v-model:value="model.productInsertsVO.approvalNumber" 
                    placeholder="请输入批准文号" 
                    :maxlength="60"
                    clearable
                   />
                </n-form-item-gi>
                <!-- 执行标准 -->
                <n-form-item-gi :span="12" label="执行标准">
                  <n-input 
                    v-model:value="model.productInsertsVO.executiveStandard" 
                    placeholder="请输入执行标准" 
                    :maxlength="60"
                    clearable
                   />
                </n-form-item-gi>
                <!-- 温馨提示 -->
                <n-form-item-gi :span="12" label="温馨提示">
                  <n-input 
                    v-model:value="model.productInsertsVO.reminder" 
                    type="textarea" 
                    maxlength="500" 
                    show-count
                    placeholder="请输入温馨提示"
                    clearable
                  />
                </n-form-item-gi>
              </n-grid>
            </n-gi>
            <!-- 更多设置 -->
            <template v-if="model.isPres!=DrugTypeEnum.ChineseHerbalMedicine">
            <n-gi :span="12">
              <div class="title-wrapper">
                <div class="title-line"></div>
                <span>更多设置</span>
              </div>
            </n-gi>
            <!-- 积分设置 -->
            <n-form-item-gi :span="12"   >
              <JCheckbox 
                v-model:checked="model.isEnabled" 
                style="margin-left: 24px;align-items: flex-start !important;"
                size="medium"
              >
              <n-form-item path="points" style="height: 32px;" show-require-mark >
                <span style="font-size: 16px; display: flex; align-items: center;">
                  订单完成后可获得积分，用户获得
                  <n-input-number 
                    v-model:value="model.points"
                    :min="MINPOINT" 
                    :max="MAXPOINT"
                    :precision="0"
                    @click.stop=""
                    :show-button="false"
                    placeholder="请输入积分"
                    :disabled="!model.isEnabled"
                    style="width: 120px;"
                    size="small"
                  />
                  积分（只能录入大于0小于10万的数值，不支持小数，录入后勾选用户购买该商品则获得积分）
                </span>
              </n-form-item>
              </JCheckbox>
            </n-form-item-gi>
            <!-- 经销商分账 -->
            <n-form-item-gi :span="12">
              <JCheckbox
                v-model:checked="model.isAllocation"
                @update:checked="handleCheckboxChange"
                style="margin-left:24px;"
                size="medium"
              >
                <n-form-item path="dealerAllocationRatio" style="height: 32px;" >
                <span style="font-size: 16px; display: flex; align-items: center;">
                    给社群端经销商分账，分账比例
                    <n-input-number
                      v-model:value="model.dealerAllocationRatio"
                      :min="0"
                      :max="80"
                      @click.stop=""
                      :precision="0"
                      :show-button="false"
                      placeholder="可输入0-80"
                      style="width: 120px;"
                      :disabled="!model.isAllocation"
                      size="small"
                    />
                    %
                  </span>
                </n-form-item>
              </JCheckbox>
            </n-form-item-gi>

            <!-- 备注 -->
            <n-form-item-gi :span="12">
              <div style="font-size: 12px;margin-left: 30px;">
                <p>备注:</p>
                <p>开展分账业务需在“财务管理”》“分账规则设置”界面设置分账规则</p>
              </div>
            </n-form-item-gi>
            <!-- 分销员分佣 -->
             <n-form-item-gi :span="12">
                <JCheckbox 
                  v-model:checked="model.distributorsCheck" 
                  style="margin-left:24px;"
                  size="medium"
                >
              <n-form-item path="distributorAccount" style="height: 32px;" >
                <span style="font-size: 16px; display: flex; align-items: center;">
                    给分销员分佣，佣金比例
                    <n-input-number 
                      v-model:value="model.distributorAccount"
                      :min="0" 
                      :max="80"
                      @click.stop=""
                      :precision="0"
                      :show-button="false"
                      placeholder="可输入0-80"
                      style="width: 120px;"
                     :disabled="!model.distributorsCheck"
                      size="small"
                    />
                    %
                  </span>
              </n-form-item>
                </JCheckbox>
              </n-form-item-gi>
          
              <!-- 备注 -->
              <n-form-item-gi :span="12">
                <div style="font-size: 12px;margin-left: 30px;">
                  <p>备注:</p>
                  <p>1、开展分销业务需在【分销】》【分销设置】界面开启分销功能</p>
                  <p>2、按商品销售额计算佣金，佣金金额 = 商品销售总额 * 佣金比例</p>
                  <p>3、如果社群端经销商也参与分账，分账分佣总额占订单总额的比例不能大于80%</p>
                </div>
              </n-form-item-gi>

              <n-gi :span="12">
                <div v-if="isDistributorChecked" style="font-size: 12px;margin-left: 30px;">
                  <p style="color: #FF4D4F;font-size: 14px;margin-top: 10px;">当前设置经销商分账和分销员佣金总额可能占订单总额的{{total}}%或以上</p>
                  <p>计算公式：分账分佣总额占订单总额的比例 = （商品总售价-总成本价）*经销商分账比例 / 商品总售价 + 分销员分佣比例</p>
                </div>
              </n-gi>
            </template>

            <!-- 提示 -->
            <n-form-item-gi v-if="!props.isPointEnable" :span="12">
              <JPointsTip />
            </n-form-item-gi>
          </n-grid>
        </n-form>
      </template>
      <!-- Footer -->
      <template #footer v-if="pattern !== 'view'">
        <div class="footer-wrapper">
          <!-- 商品是否上架 -->
          <JCheckbox 
            v-model:checked="model.isPublish" 
            style="margin-left: 24px;"
          >
            <span style="font-size: 16px;">上架</span>
          </JCheckbox>
          <n-space>
            <n-button @click="show = false" style="width: 72px;">取 消</n-button>
            <n-button type="primary" :loading="isLoading" @click="_save" style="width: 72px;">保 存</n-button>
          </n-space>
        </div>
      </template>
    </JDrawer>
  </template>
  
  <script lang="ts" setup name="NewDrugGoods">
  import { ref, computed, nextTick, watch } from "vue";
  import { createDummyId, deepClone, isNullOrUnDef, isArray } from "@/utils";
  import type { FormRules, FormItemRule } from "naive-ui";
  import { ChevronUpOutline, ChevronDownSharp } from "@vicons/ionicons5";
  import { isObject, _debounce } from "@/utils";
  import { addDrugGoods, getDrugGoodsById, updateDrugGoods, uploadRichTextResouce, addPointConfig } from "@/services/api";
  import { useMessages } from '@/hooks';
  import Quill from 'quill';
  import "@/assets/quill/quill.snow.css";
  // import quillDropdownIcon from 'quill/assets/icons/dropdown.svg';
  import JDrawer from "@/components/JDrawer/index.vue";
  import UploadProductImg from "@/components/UploadProductImg/index.vue";
  import { getOssUrlPrefix } from "@/utils/http/urlUtils";
  import { createVideoElement } from "@/utils/quill/createVideoElement";
  import { drugTypeOptions, externalStatusOptions, drugOriginOptions } from "@/constants";
  import type { GoodsType } from "@/enums";
  import { GoodsCategoryType, ProductSalesSceneEnum, DrugTypeEnum, IsHideInStoreEnum } from "@/enums";
  import {
    CHINESE_HERBAL_MEDICINE_PARAMS,
    UPPERDEFAULTVALUE,
  } from "../types";
  import { MAXPOINT, MINPOINT } from "@/views/StoreModule/SalesManagement/MemberIntegral/types";
  import type { ActivityPriceVO } from "./JActivePrice.vue";
  /** 相关组件 */
  import DrugSellingPriceInventory from "./DrugSellingPriceInventory.vue";
  import JPointsTip from "./JPointsTip.vue";
  const prices = ref(0) //全部售价
  const costPrices = ref(0) //全部成本加
  const total = ref<string | number>(0)//订单总额比率
  const { createMessageSuccess, createMessageError } = useMessages();
  const descRichTextDomRef = ref(null);

  /** props */
  const props = withDefaults(defineProps<{
    to?: string; // 弹窗位置
    isPointEnable?: boolean; // 是否启用积分功能
    isT9Sync?: boolean; // 是否同步到T9
    refreshTable?: () => void; // 刷新表格数据
  }>(), {
    to: '.table-wrapper',
    isPointEnable: false
  });

  /** 是否分销员分佣比例校验 */
  const distributorsComputed = ref()
  
  /** 显隐 */
  const show = ref(false);
  
  /* 表单实例 */
  const formRef = ref();

  /** 药品说明书展开 */
  const isShowExpand = ref(false);

  /** 多文件上传实例 */
  const uploadProductImgRef = ref<InstanceType<typeof UploadProductImg> | null>(null);

  /* 表单参数初始化 */
  const initParams = {
    
    distributorsCheck:false,//分销员选框值
    distributorAccount:null,//佣金比例
    id: null, // 编辑时，商品id
    type: GoodsCategoryType.DRUG, // 商品类别

    name: null, // 药品通用名
    frontName: null, // 商品名
    manufacturer: null, // 厂家
    cateId: null, // 所属分类
    isPres: DrugTypeEnum.Prescription, // 药物类型
    productPresOptionVOList: [
      {
        option: null
      }
    ], // 处方病症
    productFirstImg: [], // 首图
    productMoreImg: [], // 更多图片
    sku: null, // 商品编码
    upperLimit: 0, // 每订单上限(1.0.8版本)
    sellingScenario: [ProductSalesSceneEnum.Store], // 销售场景(*******版本)
    specName: null, // 商品规格Name
    productSpecVOList: [{
      id: createDummyId(), // 标识
      price: null, // 售价（元）
      costPrice: null, // 成本价(元)
      availStocks: null, // 可用库存
      lockedStocks: 0, // 冻结库存
      upper: UPPERDEFAULTVALUE, // 每订单上限
      initSaled: 0, // 初始已售（前端展示）
      isDownPayment: 0 as 0 | 1, // 是否支持定金支付。0=否；1=是
      isCashOnDelivery: 0 as 0 | 1, // 是否支持物流代收。0=否；1=是
      downPayment: null, // 定金单价，单位分
      activityPriceVOList: [], // 活动价设置
    }], // 售价与库存

    desc: null, // 图文描述

    productInsertsVO: {
      brandName: null, // 品牌
      uses: null, // 功能主治
      dosage: null, // 用法用量
      adverseReaction: null, // 不良反应
      contraindications: null, // 禁忌
      precautions: null, // 注意事项
      drugInteraction: null, // 药物相互作用
      storage: null, // 贮藏
      topical: null, // 是否外用
      composition: null, // 成份
      description: null, // 性状
      unitDose: null, // 剂型
      drugPackage: null, // 包装
      validity: null, // 有效期
      origin: null, // 产地类型
      approvalNumber: null, // 批准文号
      executiveStandard: null, // 执行标准
      reminder: null, // 温馨提示
    },

    isPublish: false, // 商品是否上架
    // 药品更多设置
    pointConfigId: null, // 编辑时,积分配置Id
    sourceId: null, // // 编辑时,来源关联ID
    isEnabled: false, // 是否获取积分
    points: null, // 积分
    isAllocation: false, // 是否分账
    dealerAllocationRatio: null, // 分账比例
    pointIsDeleted: 0, // 是否删除配置：0=否、1=是

    productVideoPath: null, // *******版本 商品视频
  };
  const model = ref(deepClone(initParams));

  let quill: Quill = null;
  /* 接收父组件传过来的参数 */
  const pattern = ref<'add' | 'edit' | 'view'>('add');
  const acceptParams = (params: {
    cateId?: string | null; // 商品分类Id
    row: Partial<ApiStoreModule.Goods>;
    type: 'add' | 'edit' | 'view';
    productTypes: GoodsType;
  }) => {
    pattern.value = params.type;
    // 当前新增商品分类id
    if (params.productTypes == GoodsCategoryType.DRUG) {
      model.value.cateId = params.cateId ?? null;
    }
    if (isObject(params.row) && Object.keys(params.row).length !== 0) {
      if (params.type === 'edit' || params.type === 'view') {
        model.value.id = params.row?.id;
      }
    }
    show.value = true;
  };
  /** 设置经销商分账和分销员佣金总额 */
  const handleTotal = () =>{
    prices.value = model.value.productSpecVOList.reduce((total, item) => total + (item.price || 0), 0);
    costPrices.value = model.value.productSpecVOList.reduce((total, item) => total + (item.costPrice || 0), 0);
    const splitAccountValue =  model.value.dealerAllocationRatio ? (model.value.dealerAllocationRatio / 100) : 0
    const distributorAccountValue =  model.value.distributorAccount ? (model.value.distributorAccount / 100) : 0
    const distributorCommission = isNaN((prices.value - costPrices.value) * splitAccountValue / prices.value) ?  0 : ((prices.value - costPrices.value) * splitAccountValue / prices.value);
    const proportionTotal = (distributorCommission + distributorAccountValue) * 100
    total.value = !isFinite(proportionTotal) ? 0 : (proportionTotal >= 0 ? proportionTotal.toFixed() : 0)
  }
  /** 标题 */
  const title = computed(() => {
    const titleMap: Record<'add' | 'edit' | 'view', string> = {
      add: '新建药品商品',
      edit: '编辑药品商品',
      view: '药品商品详情',
    };
    return titleMap[pattern.value];
  });
  
  /** 添加病症选项 */
  const addItem = () => {
    model.value.productPresOptionVOList.push({ option: null });
  };

  /**  */
  const isDistributorChecked  = computed (() =>{
    return model.value.distributorsCheck && model.value.isAllocation
  })
  
  /** 移除病症选项 */
  const removeItem = (index: number) => {
    model.value.productPresOptionVOList.splice(index, 1);
  };
  
  /* 表单规则 */
  const rules: FormRules = {
    sku: {
      type: "string",
      required: true,
      trigger: ["blur", "input"],
      message: "请输入商品编码",
    },
    upperLimit: {
      type: "number",
      required: true,
      trigger: ["blur", "input"],
      message: "请输入每人购买上限",
    },
    name: {
      type: "string",
      required: true,
      trigger: ["blur", "input"],
      message: "请输入管理名称",
    },
    frontName: {
      type: "string",
      required: true,
      trigger: ["blur", "input"],
      message: "请输入商品名",
    },
    specName: {
      type: "string",
      required: true,
      trigger: ["blur", "input"],
      message: "请输入规格",
    },
    manufacturer: {
      type: "string",
      required: true,
      trigger: ["blur", "input"],
      message: "请输入厂家",
    },
    cateId: {
      type: "string",
      required: true,
      trigger: ["blur", "input"],
      message: "请选择所属分类",
    },
    isPres: {
      type: "number",
      required: true,
      trigger: ["change"],
      message: "请选择药物类型",
    },
    productFirstImg: {
      type: "array",
      required: true,
      trigger: ["blur", "input"],
      message: "请选择商品图片",
    },
    productMoreImg: {
      type: "array",
      required: true,
      trigger: ["blur", "input"],
      message: "请选择商品图片",
    },
    productSpecVOList: {
      type: "array",
      required: true,
      validator: (rule: FormItemRule, value: Array<{
        id: string,
        name: string,
        price: number,
        costPrice: number
        availStocks: number,
        upper: number,
        initSaled: number,
      }>) => {
        if (value?.length > 0) {
          let check = value?.every(product => {
              return (
                product.price !== null &&
                (product.costPrice !== null || !model.value.isAllocation) &&
                product.availStocks !== null &&
                product.upper !== null &&
                product.initSaled !== null
              );
          });
          return check;
        }
      },
      trigger: ['blur', 'change'],
      message: "请输入商品售价、库存、每订单上限、初始已售",
    },
    points: {
      type: "number",
      required: true,
      trigger: ["blur", "change"],
      message: "请输入正确的积分",
      validator:(rule,value)=>{
        if (!model.value.isEnabled) return true
        if ( value < 1 || value > 100000 ) {
          return false
        }
      }
    },
    dealerAllocationRatio: {
      type: "number",
      required: true,
      trigger: ["blur", "change"],
      message: "请输入正确的分账比例",
      validator:(rule,value)=>{
        if (!model.value.isAllocation) return true
        if ( value < 1 || value > 1000000 ) {
          return false
        }
      }
    },
    distributorAccount: {
      type: "number",
      required: true,
      trigger: ["blur", "change"],
      message: "请输入正确的分销比例",
      validator:(rule,value)=>{
        if (!model.value.distributorsCheck) return true
        if ( value == null || value > 80 ) {
          return false
        }
      }
    },
    sellingScenario: {
      type: "array",
      required: true,
      validator: (rule: FormItemRule, value) => {
        // 校验至少选择一个
        if (!value || value.length === 0) {
          return false;
        }
        return true;
      },
      trigger: ['change'],
      message: "请选择销售场景，至少选择一个",
    },
  };

  const handleCheckboxChange = (checked) => {
    model.value.isAllocation = checked;
    // 手动触发整个表单的验证
    formRef.value?.validate(['productSpecVOList']);
  };

  /** 关闭抽屉回调 */
  const handleAfterLeave = () => {
    // 初始化参数
    model.value = deepClone(initParams);
  };

  /** 药物类型更新回调 */
  function handleRadioChange(value) {
    if (value === DrugTypeEnum.OTC) {
      model.value.sellingScenario = [ProductSalesSceneEnum.Store, ProductSalesSceneEnum.Social];
    } else if (value === DrugTypeEnum.Prescription) {
      model.value.sellingScenario = [ProductSalesSceneEnum.Store];
    }
    else if(value === DrugTypeEnum.ChineseHerbalMedicine){
      model.value.sellingScenario = [ProductSalesSceneEnum.Store];
      model.value.upperLimit = 0;
    }
  }

  /**判断是否中草药类型，处理映射 */
  const handleIsPres = (resp:any)=>{
    if (resp?.medicineType===CHINESE_HERBAL_MEDICINE_PARAMS){
      return DrugTypeEnum.ChineseHerbalMedicine
    }
    else {
      return  resp?.isPres
    }
  }

  /** 抽屉出现后的回调 */
  const isGetLoading = ref(false);
  const handleAfterEnter = async () => {
    try {
      if (show.value && (pattern.value === 'edit' || pattern.value === 'view')) {
        isGetLoading.value = true;
        const resp = await getDrugGoodsById(model.value.id);
        // 解构
        const { 
          id, 
          name, 
          desc, 
          cateId, 
          frontName,
          isPublish, 
          manufacturer,
          upperLimit,
          sellingScenario,
          productImgDTOSList, 
          productInsertsDTO, 
          productSpecDTOSList, 
          productPresOptionDTOSList,
          pointConfigDTOSList,
          isDistribution,
          distributionRatio,
          isAllocation,
          dealerAllocationRatio,
          isHidden
        } = resp;
        // 商品积分配置
        const { points, isEnabled, id: pointConfigId, sourceId } = pointConfigDTOSList[0] ?? {
          points: null,
          isEnabled: 0,
          pointConfigId: null,
          sourceId: null
        }; // 药品单个规格

        // 过滤图片与视频
        const productFirstImg = productImgDTOSList.filter(img => img.isFirst === 1 && img.type === 0);
        const productMoreImg = productImgDTOSList.filter(img => img.isFirst !== 1 && img.type === 0);
        const productVideoPath = productImgDTOSList.filter(img => img.type === 1);

        Object.assign(model.value, {
          id,
          name,
          cateId,
          frontName,
          isPres:handleIsPres(resp),
          isPublish: isPublish ? true : false,
          manufacturer,
          upperLimit,
          distributorAccount:distributionRatio,
          dealerAllocationRatio,
          isAllocation: isAllocation == 1,
          distributorsCheck:isDistribution == 1,
          sellingScenario: sellingScenario === ProductSalesSceneEnum.ALL ? [ProductSalesSceneEnum.Store, ProductSalesSceneEnum.Social,
            ...(isHidden === IsHideInStoreEnum.Hide ? [ProductSalesSceneEnum.IsHideInStore] : [])
          ] : [sellingScenario,
          ...(isHidden === IsHideInStoreEnum.Hide ? [ProductSalesSceneEnum.IsHideInStore] : [])
          ],
          productSpecVOList: productSpecDTOSList?.length > 0 ? productSpecDTOSList.map(({ price,costPrice, ...rest}) => ({...rest, price: price / 100 ,costPrice: (costPrice !==undefined ? (costPrice  / 100 ) : null)})) : [
            {
              id: createDummyId(),
              price: null,
              costPrice: null,
              availStocks: null,
              lockedStocks: 0,
              upper: UPPERDEFAULTVALUE, // 每订单上限
              initSaled: 0,
              isDownPayment: 0,
              isCashOnDelivery: 0,
              downPayment: null,
            }
          ],
          productPresOptionVOList: productPresOptionDTOSList?.length > 0 ? productPresOptionDTOSList : [{ option: null }],
          productFirstImg,
          productMoreImg,
          productVideoPath: productVideoPath.length > 0 ? productVideoPath : null,
          // 药品说明书
          productInsertsVO: {
            ...productInsertsDTO
          },
          // 药品规格
          specName:  productSpecDTOSList?.length > 0 ? productSpecDTOSList[0]?.name : null,

          // 药品积分配置
          pointConfigId, // 编辑时, 积分配置Id
          sourceId,
          isEnabled: isEnabled ? true : false, // 是否获取积分
          points, // 积分

          sku: productSpecDTOSList?.length > 0 ? productSpecDTOSList[0].sku : null,
        });
        quill.root.innerHTML = desc;
      }
    } catch (error) {
      createMessageError('查询商品失败: ' + error);
    } finally {
      isGetLoading.value = false;
    }
  };
  
  /** 获取新增药品商品参数 */
  const getAddParams = () => {
    const { 
      name, 
      type, 
      specName, 
      cateId, 
      sku, 
      isPres, 
      manufacturer,
      upperLimit,
      sellingScenario,
      isPublish, 
      frontName, 
      productFirstImg, 
      productMoreImg,
      productVideoPath,
      productSpecVOList, 
      productPresOptionVOList, 
      productInsertsVO,
      isEnabled,
      points,
      pointIsDeleted,
      distributorsCheck,
      distributorAccount,
      isAllocation,
      dealerAllocationRatio
    } = model.value;
    // 商品图片
    const imgList = deepClone(productMoreImg).map(item => ({ ...item, type: 0 }));
    if (productFirstImg.length > 0) {
      imgList.unshift({ ...productFirstImg[0], type: 0 });
    }
    // 商品视频
    if (productVideoPath !== null) {
      imgList.push(...productVideoPath);
    }
    return {
      name, 
      type, 
      cateId, 
      isPres, 
      manufacturer,
      upperLimit,
      sellingScenario: [ProductSalesSceneEnum.Store, ProductSalesSceneEnum.Social].every(scene => sellingScenario.includes(scene)) ? 0 : sellingScenario[0],
      isHidden: model.value.sellingScenario.includes(ProductSalesSceneEnum.IsHideInStore) ? 1 : 0,
      isPublish: isPublish ? 1 : 0, 
      frontName, 
      desc: quill.root.innerHTML,
      productImgVOList: imgList,
      
      isDistribution: distributorsCheck ? 1 : 0,
      isAllocation:isAllocation ? 1 :0,
      distributionRatio: distributorsCheck ? distributorAccount : null,
      dealerAllocationRatio: isAllocation? dealerAllocationRatio : null,
      productSpecVOList: productSpecVOList.map(({ id, price,costPrice, ...rest }) => ({
        ...rest, 
        price: parseFloat((price * 100).toFixed(2)),
        costPrice: costPrice !== null ? parseFloat((costPrice * 100).toFixed(2)) : null,
        name: specName,
        dailyCost: 0,
        cycle: 0,
        lockedStocks: 0,
        soldQty: 0,
        isDeleted: 0,
        sku,
        ..._getPointsAddParams(),
      })),
      // 处方病症选项
      productPresOptionVOList: isPres === DrugTypeEnum.Prescription ? productPresOptionVOList : [],
      productInsertsVO,
      isEnabled: isEnabled ? 1 : 0,
      points,
      pointIsDeleted,
      isUseCashCoupon:0
    }
  };
  
  /** 获取编辑药品商品参数 */
  const getEditParams = () => {
    const {
      id, // 商品Id
      name, 
      type, 
      specName, 
      cateId, 
      sku, 
      isPres, 
      manufacturer,
      upperLimit,
      sellingScenario,
      isPublish, 
      frontName, 
      productFirstImg, 
      productMoreImg, 
      productVideoPath,
      productSpecVOList, 
      productPresOptionVOList, 
      productInsertsVO,
      distributorAccount,
      distributorsCheck,
      isAllocation,
      dealerAllocationRatio
    } = model.value;
    // 药品图片
    const imgList = productMoreImg.map(item => ({ ...item, productId: model.value.id, isFirst: 0, type: 0 }));
    if (productFirstImg.length > 0) {
      imgList.unshift({ ...productFirstImg[0], productId: model.value.id, isFirst: 1, type: 0 });
    } else if (imgList.length > 0) {
      // 首图为空，设置更多图片第一张为首图
      imgList[0].isFirst = 1;
    }
    // 商品视频
    if (productVideoPath !== null && isArray(productVideoPath)) {
      const videoList = productVideoPath.map(item => ({ ...item, productId: model.value.id, isFirst: 0, type: 1 }));
      imgList.push(...videoList);
    }

    return {
      id,
      name,
      type,
      cateId,
      isPres,
      manufacturer,
      upperLimit,
      isAllocation:isAllocation?1:0,
      isDistribution: distributorsCheck ? 1 : 0,
      distributionRatio: distributorsCheck ? distributorAccount : null,
      dealerAllocationRatio: isAllocation ? dealerAllocationRatio : null,
      sellingScenario: [ProductSalesSceneEnum.Store, ProductSalesSceneEnum.Social].every(scene => sellingScenario.includes(scene)) ? 0 : sellingScenario[0],
      isHidden: model.value.sellingScenario.includes(ProductSalesSceneEnum.IsHideInStore) ? 1 : 0,
      isPublish: isPublish ? 1 : 0,
      frontName,
      desc: quill.root.innerHTML,
      productImgVOList: imgList,
      // 商品规格、售价与库存
      productSpecVOList: productSpecVOList.map(({ price,costPrice, ...rest }) => ({
        ...rest, 
        price: parseFloat((price * 100).toFixed(2)),
        costPrice: costPrice !== null ? parseFloat((costPrice * 100).toFixed(2)) : null,
        name: specName,
        dailyCost: 0,
        cycle: 0,
        lockedStocks: 0,
        soldQty: 0,
        isDeleted: 0,
        isUpdate: 1,
        sku,
        ..._getPointsEditParams(),
      })),
      // 处方病症选项
      productPresOptionVOList: isPres === DrugTypeEnum.Prescription ? productPresOptionVOList.map(item => ({ ...item, productId: model.value.id })) : [],
      productInsertsVO,
    }
  };

  /** 药品类型字段转换
   *  isPres==中草药，删除isPres,传递medicineType=1
   * */
  const handleIsPresParam = (obj:any)=>{
    if(obj?.isPres && obj?.isPres===DrugTypeEnum.ChineseHerbalMedicine){
      delete obj['isPres']
      return {...obj,medicineType:CHINESE_HERBAL_MEDICINE_PARAMS}
    }
    return obj
  }

  /** 获取商城积分配置 */
  function _getPointsAddParams() {
    const { points, isEnabled, pointIsDeleted } = model.value;

    return {
      points,
      isEnabled: isEnabled ? 1 : 0,
      pointIsDeleted
    }
  }

  /** 获取商城编辑积分配置 */
  function _getPointsEditParams() {
    const { points, isEnabled, pointConfigId, sourceId} = model.value;

    return {
      // 药品编辑积分配置
      pointConfigId,
      sourceId,
      isEnabled: isEnabled ? 1 : 0,
      points,
      pointIsDeleted: !isEnabled && !points ? 1 : 0,
    }
  }

  /** 校验函数 */
  function handleVerifyParams(_params): boolean {
    const { isEnabled, points, distributorsCheck, distributorAccount } = _params;
    if (isEnabled) {
      if (isNullOrUnDef(points)) {
        createMessageError("请输入用户可获取积分！");
        return false;
      }
    }
    if (distributorsCheck) {
      if (isNullOrUnDef(distributorAccount)) {
        createMessageError("请输入佣金比例！");
        return false;
      }
    }
    return true;
  }

  /* 确认--保存 */
  const isLoading = ref(false);
  const _save = async (e: MouseEvent) => {
    e.preventDefault();
    formRef.value?.validate(async (errors: any) => {
      if (!errors) {
        try {
          isLoading.value = true;
          // 是否正在进行文件上传
          if (uploadProductImgRef.value?.isUploadLoading) {
            createMessageError('文件正在上传中，请稍等！');
            return;
          }
          // 新增
          if (pattern.value === 'add') {
            let _params = getAddParams();
            _params = handleIsPresParam(_params)
            // console.log("药品新增参数", _params);
            // 积分配置启用时，校验数据
            let checked = handleVerifyParams(_getPointsAddParams());
            if (!checked) {
              return;
            }
            await addDrugGoods(_params);
          }
          // 编辑
          if (pattern.value === 'edit') {
            let _params = getEditParams();
            _params = handleIsPresParam(_params)
            console.log("药品编辑参数", getEditParams());
            // 积分配置启用时，校验数据
            let checked = handleVerifyParams(_getPointsEditParams());
            if (!checked) {
              return;
            }
            await updateDrugGoods(_params);
          }
          createMessageSuccess(pattern.value === 'add' ? '新增商品成功' : '编辑商品成功');
          // 关闭并刷新
          show.value = false;
          props?.refreshTable();
        } catch (error) {
          createMessageError(pattern.value === 'add' ? `新增商品失败: ${error}` : `编辑商品失败: ${error}`);
        } finally {
          isLoading.value = false;
        }
      }
    });
  }
  /** 监听分账比例 */
  watch(() => model.value.dealerAllocationRatio,(newVal) =>{
    handleTotal()
  })

  /** 佣金比例比例 */
  watch(() => model.value.distributorAccount,(newVal) =>{
    handleTotal()
  })
  /** 监听 */
  watch(show, (newVal) => {
    if (newVal) {
      nextTick(() => {
        createVideoElement()
        quill = new Quill(descRichTextDomRef.value, {
          theme: 'snow', // 使用 snow 主题
          modules: {
            toolbar: {
              container: [
                // Include image button in the toolbar
                [{ 'size': ['small', false, 'large', 'huge'] }],  // 自定义字体大小
                ['bold', 'italic', 'underline', 'strike'],        // 加粗、斜体、下划线和删除线
                [{ 'color': [] }, { 'background': [] }],          // 字体颜色和背景颜色 
                [{ 'header': '1' }, { 'header': '2' }, 'blockquote',],  // 标题、引用和代码块
                [{ 'list': 'ordered' }, { 'list': 'bullet' }, { 'indent': '-1' }, { 'indent': '+1' }], // 列表和缩进
                [{ 'direction': 'rtl' }, { 'align': [] }],        // 文本方向和对齐方式
                ['image', 'video'],            // 链接、图片、视频和公式         
              ],
              // Handle image uploads
              handlers: {
                image: function () {
                  const input = document.createElement('input');
                  input.setAttribute('type', 'file');
                  input.setAttribute('accept', 'image/*');
                  input.setAttribute('multiple', 'true');
                  input.click();
  
                  input.onchange = () => {
                    const file = input.files[0];
                    if (file) {
                      const _formData = new FormData()
                      for (let key in input.files) {
                        if (input.files.hasOwnProperty(key)) {
                          console.log(key, input.files[key]);
                          _formData.append('files', input.files[key])
                        }
                      }
                      uploadRichTextResouce(_formData).then(res => {
                        res.forEach(e=>{
                          const range = quill.getSelection();
                          quill.insertEmbed(range.index, 'image', `${getOssUrlPrefix()}/${e}`);
                        })
                      }).catch((error) => {
                        console.error('Image upload failed:', error);
                      });
                    }
                  };
                },
                video: function () {
                  const input = document.createElement('input');
                  input.setAttribute('type', 'file');
                  input.setAttribute('accept', 'video/mp4');
                  input.click();
  
                  input.onchange = () => {
                    const file = input.files[0];
                    if (file) {
                      console.log(file);
                      const _formData = new FormData()
                      _formData.append('files', file)
                      uploadRichTextResouce(_formData).then(res => {
                        const videoUrl = res[0]
                        const range = quill.getSelection();
                        quill.insertEmbed(range.index, 'video', `${getOssUrlPrefix()}/${videoUrl}`);
                      }).catch((error) => {
                        console.error('Video upload failed:', error);
                      });
                    }
                  };
                }
              }
            }
          }
        });
      })
    }
  })
  
 /* 处理销售场景变化 */
const handleSellingScenarioChange = (newVal: number[]) => {
  // 检查是否包含商城选项
  const hasStore = newVal.includes(ProductSalesSceneEnum.Store);
  if (!hasStore) {
    // 如果不包含商城,则移除"在商城中隐藏"选项
    model.value.sellingScenario = newVal.filter(
      item => item !== ProductSalesSceneEnum.IsHideInStore
    );
  } else {
    // 如果包含商城,直接更新值
    model.value.sellingScenario = newVal;
  }
};

  /** 关闭 */
  const close = () => {
    show.value = false;
  }
  
  defineExpose({
    acceptParams,
    close
  });
  </script>
  
  <style lang="less" scoped>
  @import "@/styles/defaultVar.less";
  :deep(.n-scrollbar-rail) {
    bottom: 8px !important;
  }
  
  .title-wrapper {
	  height: 30px;
	  display: flex;
	  align-items: center;
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 12px;
	  .title-line {
	  	width: 4px;
	  	height: 60%;
	  	background-color: @primary-color;
	  	margin-right: 5px;
	  }
    .unpack {
      margin-left: 12px;
    }
    :deep(.n-button__icon) {
      margin-left: 2px;
    }
  }
  .footer-wrapper {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
</style>
  