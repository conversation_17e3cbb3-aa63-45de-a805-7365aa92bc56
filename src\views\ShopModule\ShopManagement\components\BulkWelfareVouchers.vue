<template>
  <JModal
    v-model:show="show"
    width="680"
    title="批量发放福利券"
    @after-leave="closeModal"
    @positive-click="_save"
    :positiveButtonProps="{
      loading: isLoading,
    }"
  >
    <n-form
      ref="formRef"
      :rules="rules"
      :model="model"
      label-width="150"
      label-placement="left"
      require-mark-placement="right-hanging"
    >
      <n-grid :cols="8" :x-gap="24">
        <n-form-item-gi :span="8" label="发放店铺" path="storeIdList" required>
          <JStoreSelect width="100%" placeholder="请选择发放店铺" multiple v-model:value="model.storeIdList" />
        </n-form-item-gi>
        <n-form-item-gi :span="8" label="福利券名称" path="name" required>
          <n-input v-model:value="model.name" placeholder="请输入名称" maxlength="150" />
        </n-form-item-gi>
        <n-form-item-gi :span="8" label="福利券分类" path="categoryId" required>
          <JWelfareClassify
            v-model:value="model.categoryId"
            isImmediately
            style="width: 100%"
            placeholder="请选择分类"
          ></JWelfareClassify>
        </n-form-item-gi>
        <n-form-item-gi :span="8" label="发行数量" path="totalQuantity" required>
          <n-input-number v-model:value="model.totalQuantity" :min="1" :max="999999" />
        </n-form-item-gi>

        <n-form-item-gi :span="8" label="每人最高领取数" path="maxPerUser" required>
          <n-input-number v-model:value="model.maxPerUser" :min="1" :max="999999" />
        </n-form-item-gi>
        <n-form-item-gi :span="8" label="券有效日期" path="effectiveDate" required>
          <n-date-picker
            style="width: 100%"
            v-model:value="model.effectiveDate"
            type="date"
            :is-date-disabled="dateDisabled"
            :input-readonly="true"
          />
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </JModal>
</template>

<script setup lang="ts" name="BulkWelfareVouchers">
import { ref, watch } from "vue";
import { useMessages } from "@/hooks";
import { batchAddWelfareVoucher } from "@/services/api";
import JWelfareClassify from "@/components/JSelect/JWelfareClassify.vue";
import moment from "moment";
const initParams = {
  storeIdList: null,
  name: null,
  categoryId: null,
  totalQuantity: null,
  maxPerUser: null,
  effectiveDate: null,
};
const model = ref({ ...initParams });
const props = ref<any>({});
/* 提示信息 */
const { createMessageSuccess, createMessageError } = useMessages();
/* 模态框显隐状态 */
const show = ref(false);
/* 表单规则 */
const rules = {
  storeIdList: {
    required: true,
    trigger: ["blur", "change"],
    message: "请选择发放店铺",
    validator: () => {
      return model.value.storeIdList !== null && model.value.storeIdList?.length > 0;
    },
  },
  name: {
    required: true,
    trigger: ["blur", "change"],
    message: "请输入福利券名称",
    validator: () => {
      return model.value.name !== null;
    },
  },
  categoryId: {
    required: true,
    trigger: ["blur", "change"],
    message: "请选择福利券分类",
    validator: () => {
      return model.value.categoryId !== null;
    },
  },
  totalQuantity: {
    required: true,
    trigger: ["blur", "change"],
    message: "请输入发行数量",
    validator: () => {
      return model.value.totalQuantity !== null;
    },
  },
  maxPerUser: {
    required: true,
    trigger: ["blur", "change"],
    message: "请输入每人最高领取数",
    validator: () => {
      return model.value.maxPerUser !== null;
    },
  },
  effectiveDate: {
    required: true,
    trigger: ["blur", "change"],
    message: "请选择券有效期",
    validator: () => {
      return model.value.effectiveDate !== null;
    },
  },
};
const acceptParams = params => {
  show.value = true;
  props.value = params;
};
/* 表单实例 */
const formRef = ref(null);
/* 表单参数初始化 */
/* 清空表单 */
const formDataReset = () => {
  model.value = { ...initParams };
};
/* 关闭弹窗 */
const closeModal = () => {
  formDataReset();

  isLoading.value = false;
  // 弹窗取消
  show.value = false;
};
/* 确认--保存 */
const isLoading = ref(false);
const dateDisabled = (ts: number) => {
  return moment(ts).isBefore(moment().startOf("day"));
};
/** 获取参数 */
const getParams = () => {
  const { storeIdList, name, categoryId, totalQuantity, maxPerUser, effectiveDate } = model.value;
  const validUntil = effectiveDate ? moment(effectiveDate).format("YYYY-MM-DD 23:59:59") : "";
  const issueToAllStores = storeIdList[0] === "0" ? 1 : 0;
  return {
    storeIdList: issueToAllStores ? undefined : storeIdList,
    name,
    categoryId,
    totalQuantity,
    maxPerUser,
    validUntil,
    issueToAllStores,
  };
};
const _save = (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors && !isLoading.value) {
      try {
        isLoading.value = true;
        await batchAddWelfareVoucher({ data: getParams() });
        createMessageSuccess(`批量发放福利券成功`);
        // 刷新表格数据
        props.value.refresh();
        closeModal();
      } catch (e) {
        createMessageError(e);
        isLoading.value = false;
      }
    }
  });
};

defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less"></style>
