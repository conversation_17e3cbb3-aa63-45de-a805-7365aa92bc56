<template>
    <JDrawer
      v-model:show="drawerVisible"
      title="分账单结算详情"
      :isGetLoading = isGetLoading
      :isShowFooter="false"
      @after-leave="closeDrawer"
      to="#splitBillSettlement"
      :contents-list="[
        {
          name: '',
          slotName: 'order_info'
        },
      ]"
    >
    <template #order_info>
        <n-form
          :model="model"
          label-width="100"
          label-placement="left"
          require-mark-placement="right-hanging"
          size="small"
          :style="{
            width: '100%',
          }"
          id="orderInfo"
        >
          <n-grid cols="6 m:18 l:18 xl:24" :x-gap="24" responsive="screen">
            <!-- 第一列 -->
            <n-gi :span="12">
              <n-card title="">
               <!-- 结算单号 -->
               <n-form-item-gi :span="6" label="结算单号" placeholder='无'>
                 <n-input-group>
                   <n-input v-model:value="model.settlementNo" readonly placeholder='无'/>
                 </n-input-group>
               </n-form-item-gi>

               <!-- 打款状态 -->
               <n-form-item-gi :span="12" label="打款状态">
                 <n-select 
                  v-model:value="model.status" 
                  :options="payoutStatusOptions" 
                  placeholder='无'
                  style="width: 100%;"
                  clearable
                  disabled
                 />
               </n-form-item-gi>

               <!-- 经销商姓名 -->
               <n-form-item-gi :span="6" label="经销商姓名" placeholder='无'>
                 <n-input-group>
                   <n-input v-model:value="model.dealerName" readonly placeholder='无'/>
                 </n-input-group>
               </n-form-item-gi>

               <!-- 经销商id -->
               <n-form-item-gi :span="6" label="经销商id" placeholder='无'>
                 <n-input-group>
                   <n-input v-model:value="model.accountUserId" readonly placeholder='无'/>
                 </n-input-group>
               </n-form-item-gi>
              </n-card>

              <n-card title="">
                <!-- 分账金额 -->
               <n-form-item-gi :span="6" label="分账金额" placeholder='无'>
                {{model.allocationTotalAmount}}
               </n-form-item-gi>

               <!-- 分账手续费 -->
               <n-form-item-gi :span="6" label="分账手续费" placeholder='无'>
                {{model.allocationTotalFee}}
               </n-form-item-gi>

               <!-- 到账金额 -->
               <n-form-item-gi :span="6" label="到账金额" placeholder='无'>
                {{model.actualTotalAmount}}
               </n-form-item-gi>
              </n-card>

              <n-card title="">
                  <!-- 分账渠道 -->
                <n-form-item-gi :span="12" label="分账渠道">
                  <n-select 
                   v-model:value="model.accountPlatform" 
                   :options="paymentMethodsOptions" 
                   placeholder='无'
                   style="width: 100%;"
                   clearable
                   disabled
                  />
                </n-form-item-gi>

                <!-- 分账商户号 -->
                <n-form-item-gi :span="6" label="分账商户号" placeholder='无'>
                 <n-input-group>
                   <n-input v-model:value="model.allocationMerchantId" readonly placeholder='无'/>
                 </n-input-group>
                </n-form-item-gi>

                <!-- 分账交易第三方批次号 -->
                <n-form-item-gi :span="6" label="分账交易第三方批次号" placeholder='无'>
                 <n-input-group>
                   <n-input v-model:value="model.batchNo" readonly placeholder='无'/>
                 </n-input-group>
                </n-form-item-gi>

                <!-- 渠道端分账状态 -->
                <n-form-item-gi :span="12" label="渠道端分账状态">
                  <n-select 
                   v-model:value="model.allocationStatus" 
                   :options="splitStatusOptions" 
                   placeholder='无'
                   style="width: 100%;"
                   clearable
                   disabled
                  />
                </n-form-item-gi>

                <!-- 渠道端结算状态 -->
                <n-form-item-gi :span="12" label="渠道端结算状态">
                  <n-select 
                   v-model:value="model.settlementStatus" 
                   :options="settlementStatusOptions" 
                   placeholder='无'
                   style="width: 100%;"
                   clearable
                   disabled
                  />
                </n-form-item-gi>

                <!-- 失败原因 -->
                <n-form-item-gi :span="6" label="失败原因" placeholder='无'>
                  <n-input-group>
                   <n-input v-model:value="model.failureReason" readonly placeholder='无'/>
                 </n-input-group>
                </n-form-item-gi>
              </n-card>

              <n-card title="">
                <!-- 入账方名称 -->
                <n-form-item-gi :span="6" label="入账方名称" placeholder='无'>
                   <n-input-group>
                    <n-input v-model:value="model.alias" readonly placeholder='无'/>
                  </n-input-group>
                 </n-form-item-gi>

                 <!-- 入账方id -->
                 <n-form-item-gi :span="6" label="入账方id" placeholder='无'>
                   <n-input-group>
                    <n-input v-model:value="model.accountCode" readonly placeholder='无'/>
                  </n-input-group>
                 </n-form-item-gi>

                 <!-- 银行卡号 -->
                 <n-form-item-gi :span="6" label="银行卡号" placeholder='无'>
                   <n-input-group>
                    <n-input v-model:value="model.bankNo" readonly placeholder='无'/>
                  </n-input-group>
                 </n-form-item-gi>
              </n-card>
              
              <n-card title="">
                 <!-- 结算单创建时间 -->
                 <n-form-item-gi :span="12" label="结算单创建时间">
                  {{model.createTime}}
                 </n-form-item-gi>

                 <!-- 转线下打款时间 -->
                 <n-form-item-gi :span="12" label="转线下打款时间">
                  {{model.offlineTime}}
                 </n-form-item-gi>

                 <!-- 打款完成时间 -->
                 <n-form-item-gi :span="12" label="打款完成时间">
                  {{model.completeTime}}
                 </n-form-item-gi>
              </n-card>
            </n-gi>
          </n-grid>
        </n-form>
    </template>
    </JDrawer>
</template>
  
<script setup lang="tsx" name="breakoutDetails">
import { ref, reactive } from "vue";
import { useMessages } from "@/hooks";
import { deepClone } from "@/utils";
import { splitStatusOptions ,settlementStatusOptions,payoutStatusOptions,paymentMethodsOptions } from "@/constants";
import { allocationSettlementGetDetails } from "@/services/api";

/* 表单参数初始化 */
const initParams = {
  settlementNo:null,
  status:null,
  dealerName:null,
  accountUserId:null,

  allocationTotalAmount:null,
  allocationTotalFee:null,
  actualTotalAmount:null,

  accountPlatform:null,
  allocationMerchantId:null,
  batchNo:null,
  allocationStatus:null,
  settlementStatus:null,
  failureReason:null,

  alias:null,
  accountCode:null,
  bankNo:null,
  createTime:null,
  offlineTime:null,
  completeTime:null,
}

const model = ref(deepClone(initParams));

const {createMessageError } = useMessages();

/** 定义一个函数来格式化价格 */
const formatPrice = (price) => `${(price / 100).toFixed(2)}`;

/** 抽屉状态 */
const drawerVisible = ref(false);

/* 接收父组件传过来的参数 */
const drawerProps = ref()
const acceptParams = async(param) => {
  drawerVisible.value = true;
  drawerProps.value = param
  allocationGetDetailData(drawerProps.value.row?.settlementNo)
};

/** 关闭抽屉 */
const closeDrawer = () => {
    model.value = deepClone(initParams);
    drawerVisible.value = false;
};

/** 获取详情 */
const isGetLoading = ref(false)
const allocationGetDetailData = async(id) =>{
  isGetLoading.value = true
  try{
   const res =  await allocationSettlementGetDetails(id)
   const {
    settlementNo,
    status,
    dealerName,
    accountUserId,
    allocationTotalAmount,
    allocationTotalFee,
    actualTotalAmount,
    accountPlatform,
    allocationMerchantId,
    batchNo,
    allocationStatus,
    settlementStatus,
    failureReason,
    alias,
    accountCode,
    bankNo,
    createTime,
    offlineTime,
    completeTime
   } = res
    // 直接赋值的字段
    Object.assign(model.value, {
      settlementNo,
      status,
      dealerName,
      accountUserId,
      accountPlatform,
      allocationMerchantId,
      batchNo,
      allocationStatus,
      settlementStatus,
      failureReason,
      alias,
      accountCode,
      bankNo
    });

    // 条件处理的字段
    const fieldsWithFallback = {
      createTime: createTime || '-',
      offlineTime: offlineTime || '-',
      completeTime: completeTime || '-'
    };
    
    const priceFields = {
      allocationTotalAmount: allocationTotalAmount ? `￥${formatPrice(allocationTotalAmount)}` : '￥0.00' ,
      allocationTotalFee:allocationTotalFee ? `￥${formatPrice(allocationTotalFee)}` : '￥0.00',
      actualTotalAmount: actualTotalAmount ? `￥${formatPrice(actualTotalAmount)}` : '￥0.00'
    };

    // 合并所有字段到 model.value
    Object.assign(model.value, fieldsWithFallback, priceFields);
  }catch(err){
    createMessageError('获取详情失败:' + err)
  }finally{
    isGetLoading.value = false
  }
}

defineExpose({
  acceptParams,
  closeDrawer,
  drawerVisible
});
</script>
  
<style scoped lang="less">
@import "@/styles/default.less";
@import "@/styles/defaultVar.less";
.wrapper {
  width: 100%;
  height: 100%;
  background-color: #f2f3f5;

  .publics-form {
    width: 100%;
    height: calc(100% - 52px);
    box-sizing: border-box;
    background-color: #fff;
    .order-info,
    .user-info,
    .order-info,
    .order-item {
      padding: 12px 24px;
    }
    .title-wrapper {
      height: 30px;
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: 700;
      margin-bottom: 12px;
      .title-line {
      	width: 4px;
      	height: 60%;
      	background-color: @primary-color;
      	margin-right: 5px;
      }
    }
  }
}

:deep(.n-form-item-label){
    width: 150px !important;
}
:deep(.n-card__content){
    padding-bottom:0px
}
:deep(.n-card--bordered){
    margin-bottom: 10px;
}
</style>
  