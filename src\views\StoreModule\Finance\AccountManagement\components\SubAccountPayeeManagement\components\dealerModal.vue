<template>
    <JModal
      v-model:show="show"
      width="800"
      title="选择经销商"
      @after-leave="closeModal"
          @positive-click="_save"
          :positiveButtonProps="{
          loading: isLoading
      }"
    >
     <JDealerTransfer :communityDealersState="callAddState" :dealerIds="model.dealerIds" @update:checked-keys="handleCheckedKeys" :selectedId="selectedId"/>
    </JModal>
  </template>
  
<script setup lang="ts" name="AddorEditVideo">
import { ref } from "vue";
import { useMessages } from "@/hooks";
import { isArray } from "@/utils";
import JDealerTransfer from "@/views/StoreModule/StoreConfig/modules/OperatorManagement/components/JDealerTransfer.vue";
import { allocationAccountUserAdd,allocationAccountUserGetIsUsed } from "@/services/api";
const initParams = {
    dealerIds:[],
    dealerList: [],
};
const model = ref({ ...initParams });
export interface CreditChangesProps { 
    ids?: any;
    list?:any
}

/** props */
const  props = withDefaults(defineProps<{
    accountId: string | number;
    accountCode:string | number;
    callAddState:boolean //新增经销商
    detailsAddState:boolean //是否为详情新增经销商
}>(), {
    accountId:null,
    accountCode:null,
    callAddState:false,
    detailsAddState:false
});


const emits = defineEmits<{
  (e: "dealerIds", dealerIdsValue: any): void; // 更新经销商ids
  (e: "dealerList", dealerListValue: any): void; // 更新经销商List
}>();

/* 提示信息 */
const message = useMessages();

/* 模态框显隐状态 */
const show = ref(false);

const acceptParams = (params) => {
  
  parameter.value = params
  if(parameter.value.ids && parameter.value.ids.length != 0){
    parameter.value.ids.map((item)=>{
      model.value.dealerIds.push(item)
    })
  }
  if(parameter.value.list && parameter.value.list.length != 0){
    parameter.value.list.map((item)=>{
      model.value.dealerList.push(item)
    })
  }
  
  show.value = true
  getSelectedDistributorId()
};

/* 表单参数初始化 */
const formDataReset = () => {
  model.value = { ...initParams };
};

const parameter = ref<CreditChangesProps>({
});

/* 关闭弹窗 */
const closeModal = () => {
  formDataReset();
  // 弹窗取消
  show.value = false;
  model.value.dealerIds.length = 0
  model.value.dealerList.length = 0
};

/* 确认--保存 */
const isLoading = ref(false);
const _save = async() => {
  const dealerIds = model.value.dealerIds.filter((value, index, self) => self.indexOf(value) === index);
  const dealerList = model.value.dealerList.filter((item, index, self) => 
       index === self.findIndex(obj => obj.id === item.id)
  );
  
  if(props.callAddState && props.detailsAddState){
    const resultDealerIds = dealerIds.filter(item => !parameter.value.ids.includes(item));
    const updatedArrDealerList = dealerList.map(item => {
       const { id, name, ...rest } = item;
       return {
         id: id,
         accountUserId: id, // 修改 id 为 accountUserId
         dealerName: name,  // 修改 name 为 dealerName
         ...rest            // 保留其他键值对
       };
     });
    const param = {
     data:{
       accountUserIds:resultDealerIds,
       accountId:props.accountId,
       accountCode:props.accountCode
     }
    }
    
    try{
      await allocationAccountUserAdd(param)
      message.createMessageExportSuccess('新增社群端经销商账号成功')
      emits("dealerIds", dealerIds);
      emits("dealerList", updatedArrDealerList);
      closeModal()
    }catch(err){
      message.createMessageError('新增社群端经销商账号失败:' + err)
    }
  }else{
    emits("dealerIds", dealerIds);
    emits("dealerList", dealerList);
    closeModal()
  }
};

/** 经销商值更新回调 */
function handleCheckedKeys(
  meta: { 
    action: 'check' | 'uncheck'  | 'checkAll' | 'uncheckAll', 
    currentId: string | number | Array<string | number>, 
    currentItem: Object | Array<Object>
}) {
  
  const { action, currentId, currentItem } = meta;

  if (action === 'check') {
    // 如果是 check，添加 currentItem 至 dealerList
    model.value.dealerList.push(currentItem);
    model.value.dealerIds.push(currentId);
  } else if (action === 'uncheck') {
    // 如果是 uncheck，移除 dealerList 中对应的 currentId 项
    model.value.dealerList = model.value.dealerList.filter(dealer => dealer.id !== currentId);
    model.value.dealerIds = model.value.dealerIds.filter(id => id !== currentId);
  }

  // 全选或全不选
  if (isArray(currentItem)) {
    const dealerIdsSet = new Set(model.value.dealerIds);
  
    if (action === 'checkAll') {
      currentItem.forEach(item => {
        if (!dealerIdsSet.has(item.id)) {
          model.value.dealerList.push(item);
          model.value.dealerIds.push(item.id);
        }
      });
    } else if (action === 'uncheckAll') {
      const currentItemIds = new Set(currentItem.map(item => item.id));
      model.value.dealerList = model.value.dealerList.filter(dealer => !currentItemIds.has(dealer.id));
      model.value.dealerIds = model.value.dealerIds.filter(id => !currentItemIds.has(id));
    }
  }
};

/** 获取已被绑定的经销商 */
const selectedId = ref()
const getSelectedDistributorId = async() =>{
  try{
    const res = await allocationAccountUserGetIsUsed()
    selectedId.value = res.map(item => item.accountUserId);
  }catch(err){
    message.createMessageError('获取已被绑定的经销商失败:' + err)
  }
}


defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less">
</style>
  