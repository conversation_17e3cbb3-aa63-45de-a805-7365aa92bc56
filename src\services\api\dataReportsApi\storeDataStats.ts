import { defHttp } from "@/services";

/** 门店数据统计 */
export const enum StoreDataStatsApi {
    /** 分页查询门店数据统计 */
    page = "/storeDailyReport/pageList",
    /** 导出门店数据统计 */
    export = "/storeDailyReport/storeExport",
    /** 获取门店/经销商报表最新更新时间 */
    getReportLastUpdate = "/storeDailyReport/getLastUpdateTime",
}

/** 获取门店数据统计 */
export function getStoreDataStatsPage(params) {
    return defHttp.post({
        url: StoreDataStatsApi.page,
        params,
    });
}

/** 门店数据统计导出 */ 
export function storeDataStatsExport(params) {
    return defHttp.post({
        url: StoreDataStatsApi.export,
        requestConfig: {
            responeseType: "stream",
        },
        params,
    });
}

/** 获取门店/经销商报表最新更新时间 */ 
export function getReportLastUpdate() {
    return defHttp.get({
        url: StoreDataStatsApi.getReportLastUpdate,
    });
}