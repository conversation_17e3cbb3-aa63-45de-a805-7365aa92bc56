<template>
  <div class="inner-page-height">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :pagination="paginationRef"
      :isTableSelection="false"
      @paginationChange="paginationChange"
      :isDisplayIndex="false"
      id="distributorManagement"
    >
      <!-- 表单 -->
      <template #searchForm>
        <n-form
          ref="formRef"
          :model="formValue"
          :show-feedback="false"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          size="small"
          :style="{ width: '100%' }"
        >
          <n-form-item label="申请类型">
            <n-select
              v-model:value="formValue.applyType"
              :options="ApplicationTypeOption"
              placeholder="请选择"
              style="width: 170px;"
              clearable
              @update:value="tableSearch"
            />
          </n-form-item>
          <n-form-item label="审核状态">
            <n-select
              v-model:value="formValue.auditStatus"
              :options="AuditStatusOption"
              placeholder="请选择"
              style="width: 170px;"
              clearable
              @update:value="tableSearch"
            />
          </n-form-item>
        </n-form>
      </template>
    </FormLayout>
  </div>
</template>
<script setup lang="tsx" name="OrganizationalStructure">
import { ref, onMounted, onUnmounted } from 'vue';
import { OrganizationalStructure, DepartmentType } from "@/enums";
import { structureApplyPage, structureApplyAudit } from "@/services/api";
import { hasOrganizationalApplicationAuditAuth } from '../authList'
import { useTableDefault } from "@/hooks/useTableDefault";
import { ApplicationTypeOption, AuditStatusOption, ApplicationType, AuditStatus } from "./type"
import { NButton, NSpace, NTag, useDialog } from "naive-ui";
import FormLayout from "@/layout/FormLayout.vue";
import Popconfirm from '@/components/Popconfirm/index.vue'
import { useMessages } from "@/hooks";
const { createMessageSuccess, createMessageError } = useMessages();

const initParams = {
 applyType: null,
 auditStatus: null,
};

const formValue = ref({ ...initParams });

/** 获取参数 */
const getParams = () => {
  const { applyType, auditStatus } = formValue.value;
  return {
      applyType,
      auditStatus
    };
};
const {
  isLoading,
  tableData,
  pageTableData,
  paginationRef,
  paginationChange,
} = useTableDefault({
  pageDataRequest: structureApplyPage,
});

/* 表格搜索 */
const tableSearch = () => {
  pageTableData(getParams(), paginationRef.value);
};
const auditLoading = ref(false)
const tableColumns = [
    {
      title: '用户昵称/ID',
      key: 'nickname',
      render: (row) => {
        return <table-tooltip row={row} nameKey="nickname" title={row.nickname} idKey="shortCsId" />;
      }
    },
    {
      title: '申请类型',
      key: 'applyType',
      render: (row) => {
        return [ "", "区域", "经销商", "门店" ][ row.applyType ];
      }
    },
    {
      title: '申请内容',
      key: 'applyContent',
      render: (row) => {
        return (
          <div class="apply-content">
            <div class="apply-cs-name">申请人：{row.applyCsName ? row.applyCsName : '-'}</div>
            <div class="apply-cs-phone">联系电话：{row.applyCsPhone ? row.applyCsPhone : '-'}</div>
            <div class="apply-structure-name">公司名称：{row.applyStructureName ? row.applyStructureName : "-"}</div>
            <div class="apply-structure-area">公司区域：{row.province} - {row.city} - {row.district}</div>
            <div class="apply-structure-address">详细地址：{row.addressDetail ? row.addressDetail : "-"}</div>
          </div>
        );
      }
    },
    {
      title: '备注',
      key: 'applyRemark',
    },
    {
      title: '上级组织名称',
      key: 'higherStructureName',
    },
    {
      title: "审核状态",
      key: "auditStatus",
      render: (row) => {
        return (
          <NSpace>
            <NTag
              bordered={false}
              size="small"
              type={["primary", "success", "error"][row.auditStatus]}
            >
              {["待审核", "审核通过", "审核不通过"][row.auditStatus]}
            </NTag>
          </NSpace>
        );
      }
    },
    {
      title: "审核时间",
      key: "auditTime",
    },
    {
      title: "申请时间",
      key: "applyTime",
    },
    {
      title: "操作",
      key: "action",
      width: 180,
      fixed: "right",
      render: rowData => {
          return (
              <n-space align="center" justify="center">
                {
                    hasOrganizationalApplicationAuditAuth && rowData.auditStatus === AuditStatus.WAIT ?
                    <Popconfirm
                      onHandleClick={()=>handleAudit(rowData.id, 1)}
                      loading={auditLoading.value}
                      buttonContent ={'审核通过'}
                      type={'primary'}
                      promptContent={'是否审核通过?'}/> : null
                  }
                  {
                    hasOrganizationalApplicationAuditAuth && rowData.auditStatus === AuditStatus.WAIT ?
                    <Popconfirm
                      onHandleClick={()=>handleAudit(rowData.id, 2)}
                      loading={auditLoading.value}
                      buttonContent ={'审核不通过'}
                      type={'primary'}
                      promptContent={'是否审核不通过?'}/> : null
                  }
              </n-space>
          );
       },
    }
]

const handleAudit = async (id,val) =>{
  auditLoading.value = true;
  try {
    await structureApplyAudit({
      data: {
        id,
        auditStatus: val
      }
    });
    createMessageSuccess("审核成功")
    tableSearch()
  } catch (error) {
    createMessageError(error)
  } finally {
    auditLoading.value = false;
  }
}
/* 组件挂载 */
onMounted(() => {
  tableSearch()
});
/* 组件挂载 */
</script>
<style scoped lang="less">
.apply-content {

}
</style>
