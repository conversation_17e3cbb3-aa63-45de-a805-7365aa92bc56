<template>
  <JDrawer
    v-model:show="show"
    title="签到配置"
    :isGetLoading="isGetLoading"
    @after-leave="handleAfterLeave"
    @after-enter="handleAfterEnter"
    :to="props.to"
  >
    <!-- 基本信息 -->
    <template #content>
      <n-form
        ref="formRef"
        :model="model"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
        size="small"
        :style="{ width: '100%' }"
      >
        <n-grid :cols="12" :x-gap="12">
          <!-- 基本配置 -->
          <n-gi :span="12">
            <div class="title-wrapper">
              <div class="title-line"></div>
              <span>基本配置</span>
            </div>
          </n-gi>
          <n-form-item-gi :span="12" label="任务内容">
            <span class="explain">点击签到，即可获取奖励配置中积分</span>
          </n-form-item-gi>
          <n-form-item-gi :span="12" label="规则说明">
            <span class="explain">
              1、点击签到获得积分奖励，每天只能获取一次；
              <br />
              2、获取天数7天为一个周期，以第一次签到的时间为周期第一天；
              <br />
              3、出现断签情况，重新开始计算获取天数；
              <br />
            </span>
          </n-form-item-gi>
          <!-- 配置获取积分 -->
          <n-gi :span="12">
            <div class="title-wrapper">
              <div class="title-line"></div>
              <span>配置获取积分（都必填，需输入大于0小于10万的数值，不支持小数）</span>
            </div>
          </n-gi>
          <n-gi :span="12">
            <div class="config-wrapper">
              <div v-for="item, index in model.integral" :key="index" class="integral-item">
                <span style="margin-right: 8px;">{{ `第${index + 1}天` }}</span>
                <n-input-number
                  v-model:value="item[`day${index + 1}`]"
                  :min="MINPOINT"
                  :max="MAXPOINT"
                  :precision="0"
                  :show-button="false"
                  placeholder="请输入积分"
                  size="small"
                  style="width: 240px;"
                  clearable
                />
              </div>
            </div>
          </n-gi>
        </n-grid>
      </n-form>
    </template>
    <!-- Footer -->
    <template #footer>
      <div class="footer-wrapper">
        <!-- 商品是否上架 -->
        <JCheckbox v-model:checked="model.isUse" style="margin-left: 24px;">
          <span style="font-size: 16px;">启用签到</span>
        </JCheckbox>
        <n-space>
          <n-button @click="show = false">取消</n-button>
          <n-button type="primary" :loading="isLoading" @click="_save">保存</n-button>
        </n-space>
      </div>
    </template>
  </JDrawer>
</template>

<script lang="ts" setup name="SignInConfig">
import { ref } from "vue";
import { } from "@/services/api";
import { useMessages } from '@/hooks';
import { MAXPOINT, MINPOINT } from "../../../types";
import JDrawer from "@/components/JDrawer/index.vue";

interface Integral {
  [key: string]: number | null;
}

const { createMessageSuccess, createMessageError } = useMessages();

/** props */
const props = withDefaults(defineProps<{
  to?: string; // 弹窗位置
  refreshTable?: () => void; // 刷新表格数据
}>(), {
  to: '#integral-mall'
});

/** 显隐 */
const show = ref(false);

/* 表单实例 */
const formRef = ref();


/* 表单参数初始化 */
const initParams = {
  integral: [
    { day1: null },
    { day2: null },
    { day3: null },
    { day4: null },
    { day5: null },
    { day6: null },
    { day7: null },
  ] as Integral[], // 积分
  isUse: false, // 是否启用配置
};
const model = ref({ ...initParams });

const acceptParams = () => {
  show.value = true;
};

/** 校验数据录入 */
function validateIntegral(integral: Integral[]): boolean {
  let lastValidPoint: number | null = null; // 允许null值
  for (let i = 0; i < integral.length; i++) {
    const currentDayIntegral = Object.values(integral[i])[0];
    if (currentDayIntegral === null) {
      createMessageError(`请录入第${i + 1}天的积分!`);
      return false; // 如果需要继续检查其他天，可以不返回false，而是继续循环
    }
    if (lastValidPoint !== null && currentDayIntegral < lastValidPoint) {
      createMessageError(`录入有误：第${i + 1}天的积分: ${currentDayIntegral} 小于前一天的积分: ${lastValidPoint} `);
      return false;
    }
    lastValidPoint = currentDayIntegral; // 更新最后一个有效的积分值
  }
  return true;
}

/** 关闭抽屉回调 */
const handleAfterLeave = () => {
  // 初始化参数
  model.value = { ...initParams };
};

/** 抽屉出现后的回调 */
const isGetLoading = ref(false);
const handleAfterEnter = async () => {

};

/* 确认--保存 */
const isLoading = ref(false);
const _save = async (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      const { isUse, integral } = model.value;
      let checked  = false;
      // 启用时，校验数据
      if (isUse) {
        checked = validateIntegral(integral);
        if (checked) {
          return;
        }
      }

    }
  });
};

defineExpose({
  acceptParams,
});
</script>

<style lang="less" scoped>
@import "../styles/index.less";
:deep(.n-scrollbar-rail) {
  bottom: 8px !important;
}

.config-wrapper {
  width: 100%;
  display: flex;
  flex-wrap: wrap;

  .integral-item {
    display: flex;
    align-items: center;
    margin-left: 32px;
    margin-bottom: 24px;
  }
}
</style>
