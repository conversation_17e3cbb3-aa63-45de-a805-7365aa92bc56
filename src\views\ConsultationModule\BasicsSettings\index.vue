<template>
  <div class="wrapper inner-page-height">
      <n-scrollbar style="height: calc(100% - 50px);position: relative;padding: 24px;box-sizing: border-box;" >
        <n-spin :show="loadShow">
        <n-form 
          ref="formRef"
        :model="model" 
        :rules="rules" 
        label-placement="left"
        min-label-width="auto"
        require-mark-placement="right-hanging"
        size="small" 
        :style="{ width: '100%' }"
      >
  
      <n-grid :cols="12" :x-gap="12">
       <!-- 医生药师工作台 -->
<!--       <n-gi :span="10">-->
<!--         <div class="title-wrapper">-->
<!--           <div class="title-line"></div>-->
<!--           <span>医生药师工作台</span>-->
<!--          </div>-->
<!--        </n-gi>-->
<!--        <n-form-item-gi :span="6" label="电脑端登录地址" >-->
<!--          <div>{{ model[BaseSettingType.doctorPharmacistLoginUrl] }} <CopyOptBtn :value="model[BaseSettingType.doctorPharmacistLoginUrl]" label=""></CopyOptBtn></div>-->
<!--        </n-form-item-gi>-->
        <!-- 互联网医院机构信息 -->
        <n-gi :span="10">
          <div class="title-wrapper">
            <div class="title-line"></div>
            <span>互联网医院机构信息</span>
          </div>
        </n-gi>

        <n-form-item-gi :span="6" label="医疗机构名称" path="medicalInstitutionName">
          <n-input v-model:value="model[BaseSettingType.medicalInstitutionName]" placeholder="请输入医疗机构名称" :maxlength="64" clearable />
        </n-form-item-gi>
        <n-form-item-gi :span="7" label="医疗机构执业许可证登记号" path="licenseNo">
          <n-input v-model:value="model[BaseSettingType.licenseNo]" placeholder="请输入医疗机构执业许可证登记号" :maxlength="64" clearable />
        </n-form-item-gi>
        <n-form-item-gi 
          :span="7" 
          label="互联网医院签章样式"
          label-placement="top"
          path="signStyle"
        >
          <div>
            <span class="hint" >注：请上传背景透明的png格式的签章图片
              <n-popover trigger="hover" raw :show-arrow="false">
              <template #trigger>
                <span style="color: #0070f3;cursor: pointer;">【查看示例】</span>
              </template>
              <img :src="sealExample" width="150" height="150"  alt="">
              </n-popover>
              ，不能大于1M</span>
            <CustomizeUpload v-model:value="model[BaseSettingType.signStyle]" accept=".png" :fileListSize="1" :max="1" :maxFileSize="1" sizeTitle="图片大小不能超过1M，请重新上传" :isCrop="true" :title="'裁剪签章图片'" :fixedNumber="[1,1]" :imageUploadType="['image/png']" :enableTypeValidation="true"/>
          </div>
        </n-form-item-gi>
      <n-gi :span="12" >
        <n-divider />
      </n-gi>
      <!-- 1对1图文问诊 -->
      <n-gi :span="10">
          <div class="title-wrapper">
            <div class="title-line"></div>
            <span>1对1图文问诊</span>
          </div>
        </n-gi>
        <n-form-item-gi :span="7" label="问诊时长（分钟）" required path="licenseNo" :show-feedback="false" >
          <n-input-number v-model:value="model[BaseSettingType.consultationTime]" placeholder="请输入问诊时长" :style="{width: '120px'}" :min="10" :max="527040" :show-button="false" />
        </n-form-item-gi>
        <n-grid-item :span="12" >
          <p class="hintL" >用户每次问诊时长，从接诊时间点开始计算，超时自动结束，最短10分钟，最长24小时。</p>
        </n-grid-item>
        <n-form-item-gi :span="7" label="超时未接诊取消订单时长（分钟）" :show-feedback="false" required path="cancelOrderTime">
          <n-input-number v-model:value="model[BaseSettingType.cancelOrderTime]" placeholder="请输入时长" :style="{width: '120px'}" :min="1" :max="10080" :show-button="false" />
        </n-form-item-gi>
        <n-grid-item :span="12">
          <p class="hintL" >用户发起问诊请求后，医生需要在这个时限内接诊，超时系统自动取消订单，最短1分钟，最长7天。</p>
        </n-grid-item>
        <n-gi :span="12" >
        <n-divider />
      </n-gi>
         <!-- 视频问诊 -->
      <n-gi :span="10">
          <div class="title-wrapper">
            <div class="title-line"></div>
            <span>视频问诊</span>
          </div>
        </n-gi>
        <n-form-item-gi :span="7" label="问诊时长（分钟）" required path="licenseNo" :show-feedback="false" >
          <n-input-number v-model:value="model[BaseSettingType.videoInterrogationTime]" placeholder="请输入问诊时长" :style="{width: '120px'}" :min="10" :max="527040" :show-button="false" />
        </n-form-item-gi>
        <n-grid-item :span="12" >
          <p class="hintL" >医生接诊后需在问诊时长内和患者视频沟通完成，超时自动结束，最短10分钟，最长24小时。1、问诊时长：必填，默认30分钟，最短可设10分钟，最长24小时。</p>
        </n-grid-item>
        <n-form-item-gi :span="7" label="超时未接诊取消订单时长（分钟）" :show-feedback="false" required path="cancelOrderTime">
          <n-input-number v-model:value="model[BaseSettingType.videoCancelOrderTime]" placeholder="请输入时长" :style="{width: '120px'}" :min="1" :max="10080" :show-button="false" />
        </n-form-item-gi>
        <n-grid-item :span="12">
          <p class="hintL" >用户发起问诊请求后，医生需要在这个时限内接诊，超时系统自动取消订单，最短1分钟，最长7天。</p>
        </n-grid-item>
        <n-gi :span="12" >
        <n-divider />
      </n-gi>
        <!-- 处方相关 -->
        <n-gi :span="10">
          <div class="title-wrapper">
            <div class="title-line"></div>
            <span>处方相关</span>
          </div>
        </n-gi>
        <n-form-item-gi :span="7" label="处方有效期（天）" required path="licenseNo" :show-feedback="false" >
          <n-input-number v-model:value="model[BaseSettingType.prescriptionValidity]" placeholder="请输入问诊时长" :precision="0" :style="{width: '120px'}" :min="1" :max="3" :show-button="false" />
        </n-form-item-gi>
        <n-grid-item :span="12">
          <p class="hintL" >从医生开方时间点开始计算，药师超时未审核或用户超时未下单，处方自动作废，最长3天</p>
        </n-grid-item>
        <n-gi :span="12" >
          <n-divider />
        </n-gi>
        <!-- 处方药下单开方系统 -->
        <n-gi :span="10">
          <div class="title-wrapper">
            <div class="title-line"></div>
            <span>处方药下单开方系统</span>
          </div>
        </n-gi>
        <n-form-item-gi :span="7" label="" required :show-feedback="false" >
          <n-radio-group v-model:value="model[BaseSettingType.stoPresSystem]" style="margin-left: 10px;">
            <n-space>
              <n-radio :value="1">
                自研系统
              </n-radio>
              <n-radio :value="2">
                第三方互联网医院（请确认已技术对接后再选择）
              </n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item-gi>
      </n-grid>
    </n-form>
    </n-spin>
    
    </n-scrollbar>
    <div class="footer-wrapper">
      <n-space>
        <n-button type="info" @click="_confirm" :loading="setLoading" v-if="hasConsultationBasicSettingsSaveAuth" :disabled="!isAllUploadEnd" > 确认发布 </n-button>
      </n-space>
    </div>
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref, watch,computed } from 'vue';
import sealExample from "@/assets/image/system/sealExample.png"
import { getBaseSetting,editBaseSetting } from '@/services/api';
import { BaseSettingType } from './type';
import { hasConsultationBasicSettingsSaveAuth } from './authList';
import { useMessages } from "@/hooks";
import CopyOptBtn from "@/components/CopyOptBtn/index.vue";
const message = useMessages();

onMounted(()=>{
  getBaseSettingData()
})

// 获取设置数据
const getBaseSettingData = () => {
  loadShow.value = true
  getBaseSetting({
    pageVO:{
      current:1,
      size:30
    }
  }).then(res=>{
    settingData.value = res.records
    res.records.forEach(item=>{
      if(item.key === BaseSettingType.signStyle){
        model.value[item.key] = [item.value]
      }else if(item.key === BaseSettingType.medicalInstitutionName || item.key === BaseSettingType.licenseNo || item.key === BaseSettingType.doctorPharmacistLoginUrl){
        model.value[item.key] = item.value
      }else{
        model.value[item.key] = Number(item.value)
      }
    })
  }).catch(err=>{
    message.createMessageError('获取设置数据失败')
  }).finally(()=>{
    loadShow.value = false
  })
}

/** 设置数据 */
const settingData = ref([])
const loadShow = ref(false)
const isAllUploadEnd = ref(false);
const setLoading = ref(false);

const _confirm = () => {
  setLoading.value = true
  settingData.value.forEach(item=>{
    if(item.key === BaseSettingType.signStyle){
      item.value = model.value[item.key][0] || ""
    }else{
      item.value = model.value[item.key]
    }
  })
  editBaseSetting({data:settingData.value}).then(res=>{
    message.createMessageSuccess('编辑成功')
  }).catch(err=>{
    message.createMessageError(`编辑失败：${err}`);
    getBaseSettingData()
  }).finally(()=>{
    setLoading.value = false
  })
  
}

const initParams = {
  [BaseSettingType.medicalInstitutionName]: null,
  [BaseSettingType.licenseNo]: null,
  [BaseSettingType.signStyle]: "",
  [BaseSettingType.consultationTime]: null,
  [BaseSettingType.cancelOrderTime]: null,
  [BaseSettingType.prescriptionValidity]: null,
  [BaseSettingType.stoPresSystem]: 1,
  [BaseSettingType.doctorPharmacistLoginUrl]: null,
  [BaseSettingType.videoInterrogationTime]: null,
  [BaseSettingType.videoCancelOrderTime]: null,
}
const model = ref({...initParams});
const rules = ref({});

watch(()=>model.value[BaseSettingType.signStyle],(newVal)=>{
  if(newVal[0] == '') model.value[BaseSettingType.signStyle] = ''
})

watch(model,()=>{
  isAllUploadEnd.value = !settingData.value.every(item=>{
    if(item.key === BaseSettingType.signStyle){
      return model.value[item.key][0] == item.value 
    }else{
      return model.value[item.key] == item.value
    }
  })
}, {deep: true})

</script>
<style lang="less" scoped>
  @import "@/styles/defaultVar.less";
  // 混入
  .form-item-gi-hint(){
    color: #999;
  }
  .wrapper{
    background-color: #fff;
    // padding: 24px;
    // box-sizing: border-box;
    .title-wrapper {
      height: 30px;
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: 700;
      margin-bottom: 12px;
      .title-line {
        width: 4px;
        height: 60%;
        background-color: @primary-color;
        margin-right: 5px;
      }
      .unpack {
        margin-left: 12px;
      }
      :deep(.n-button__icon) {
        margin-left: 2px;
      }
    }
    .hint{
      .form-item-gi-hint();
      font-size: 12px;
      margin-bottom: 12px;
    }
    .hintL{
      .form-item-gi-hint();
      margin-top: 4px;
      margin-bottom: 12px;
    }
    .divider{
      width: 100%;
      height: 1px;
      background-color: #010101;
      margin: 12px 0;
    }
    .footer-wrapper {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      position: absolute;
      bottom: 0;
      width: calc(100% - 48px);
      background-color: #fff;
      padding: 24px;
      box-sizing: border-box;
    }
    :deep(.n-form-item-label__text){
      font-size: 14px;
    }
  }
</style>

