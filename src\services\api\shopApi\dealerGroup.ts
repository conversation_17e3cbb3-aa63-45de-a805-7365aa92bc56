import { defHttp } from "@/services";

/** 经销商分组管理 */
export const enum DealerGroupApi {
  dealerGroupList = "/dealerGroup/list",
  dealerGroupRelevancePage = "/dealerGroup/page",
  add = "/dealerGroup/add",
  update = "/dealerGroup/update",
  delete = "/dealerGroup/delete",
  affiliatedDealers = "/dealerGroupRelate/affiliatedDealers",
  dealerList = "/structure/pageDealer",
}

/**
 * @description 经销商分组列表
 */
export function getDealerGroupList(params) {
  return defHttp.post({
    url: DealerGroupApi.dealerGroupList,
    params,
  });
}
/**
 * @description 分组关联经销商分页
 */
export function getDealerGroupRelevancePage(params) {
  return defHttp.post({
    url: DealerGroupApi.dealerGroupRelevancePage,
    params,
  });
}
/**
 * @description 经销商列表分页
 */
export function getDealerList(params) {
  return defHttp.post({
    url: DealerGroupApi.dealerList,
    params,
  });
}
/**
 * @description 新增经销商分组
 */
export function addDealerGroup(params) {
  return defHttp.post({
    url: DealerGroupApi.add,
    params,
  });
}
/**
 * @description 编辑分组关联经销商
 */
export function affiliatedDealers(params) {
  return defHttp.post({
    url: DealerGroupApi.affiliatedDealers,
    params,
  });
}
/**
 * @description 修改经销商分组
 */
export function updateDealerGroup(params) {
  return defHttp.put({
    url: DealerGroupApi.update,
    params,
  });
}
/**
 * @description 删除经销商分组
 */
export function deleteDealerGroup(params) {
  return defHttp.delete({
    url: DealerGroupApi.delete,
    requestConfig: {
      isQueryParams: true,
    },
    params,
  });
}
