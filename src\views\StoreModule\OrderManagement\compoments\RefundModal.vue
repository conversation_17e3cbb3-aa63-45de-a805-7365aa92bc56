<template>
    <JModal
      v-model:show="show"
      width="680"
      title="退款"
      @after-leave="closeModal"
          @positive-click="_save"
          :positiveButtonProps="{
          loading: isLoading
      }"
      :positiveText="'提交'"
    >
      <n-form
        ref="formRef"
        :rules="rules"
        :model="model"
        label-width="auto"
        label-placement="left"
        require-mark-placement="right-hanging"
        :style="{
            width: '100%',
          }"
      >
        <n-grid :cols="8" :x-gap="24">
          <n-form-item-gi :span="8" label="售后类型" path="type">
             <n-select 
             v-model:value="model.type" 
             :options="ordersAfterSalesOptionsValue"
             placeholder="请选择售后类型"
             :disabled="model.disabledType"
             />
          </n-form-item-gi>
         
          <n-form-item-gi :span="8"  label="退款金额" path="refundAmount">
            <n-input-number 
              v-model:value="model.refundAmount" 
              :show-button="false"  
              style="width: 100%;" 
              :readonly="!model.isModifyAmount"
              :precision="2"
              />
          </n-form-item-gi>

          <n-form-item-gi :span="8"  label="联系电话">
           <n-input-number 
             v-model:value="model.phone" 
             :show-button="false"  
             @blur="handleMobilePhone" 
             style="width: 100%;" 
             placeholder="请输入联系电话"/>
          </n-form-item-gi>

          <n-form-item-gi :span="8" label="退款原因" path="reason">
             <n-select 
             v-model:value="model.reason" 
             :options="aReasonForRefundOptions"
             placeholder="请选择退款原因"
             />
          </n-form-item-gi>

          <n-form-item-gi :span="8" label="具体原因" >
            <n-input 
             maxlength="200" 
             v-model:value="model.reasonDescription" 
             @blur="model.reasonDescription = ($event.target as HTMLInputElement)?.value.trim ()" 
             show-count 
             clearable 
             placeholder="请输入具体原因"
             type="textarea" />
          </n-form-item-gi>

          <n-form-item-gi  :span="8" label="上传图片" >
            <UploadProductImg
              ref="uploadProductImgRef" 
              v-model:value="model.afterSaleImgVOList"
              accept="image/*"
              :fileListSize="9"
              is-multiple
            />
          </n-form-item-gi>
        </n-grid>
      </n-form>
    </JModal>
  </template>
  
<script setup lang="ts" name="AddorEditVideo">
import { computed, ref, watch } from "vue";
import { useMessages } from "@/hooks";
import {
    ordersAfterSalesOptions,
    aReasonForRefundOptions,
    ordersAfterSalesLabels
} from "@/constants";
import {afterSaleRecordRefund} from '@/services/api';
import UploadProductImg from "@/components/UploadProductImg/index.vue";
import { orderStatusLabels, } from "@/constants";
const initParams = {
  orderCode:null,
  type:null,
  reason:null,
  reasonDescription:null,
  refundAmount:null,
  phone:null,
  afterSaleImgVOList:[],
  isModifyAmount:false,
  disabledType:false,
  initAmount:null
};
const model = ref({ ...initParams });
export interface CreditChangesProps { 
    row?: any;
    payType: string | number,//支付类型
    status:string | number,//订单状态
    isVirtual?:number /** 1 就不能退货退款 0 就可以退货退款 */
    refresh?: () => void; // 刷新表格
    closeDrawer?: () => void; //关闭抽屉
}
/* 提示信息 */
const message = useMessages();
/* 模态框显隐状态 */
const show = ref(false);

/* 表单规则 */
const rules = {
  type:{
    required: true,
    trigger: ["blur", "change"],
    message: "请选择售后类型",
    validator: ()=>{
      return model.value.type != null;
    }
  },
  refundAmount:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入退款金额",
    validator: ()=>{
      return model.value.refundAmount != null;
    }
  },
  reason:{
    required: true,
    trigger: ["blur", "change"],
    message: "请选择退款原因",
    validator: ()=>{
      return model.value.reason != null;
    }
  },
};

/** 是否可修改金额 */
const acceptParams = (params) => {
  parameter.value = params
  /** 支付方式:在线支付, 或者是支付方式:支付定金、订单状态: 待发货 */
  if(parameter.value.payType == '在线支付' || (parameter.value.payType == '支付定金' && parameter.value.status == '待发货') || parameter.value.payType == '积分 + 现金'){
    /** 支付方式:待发货 售后类型仅可选 “仅退款” 类型*/
    if(parameter.value.status == '待发货'){
      model.value.type = 1
      model.value.disabledType = true
    }
    model.value.refundAmount = parameter.value.row?.onlinePayment ? (parameter.value.row?.onlinePayment / 100) : 0
    model.value.initAmount = parameter.value.row?.onlinePayment ? (parameter.value.row?.onlinePayment / 100) : 0
  }
   /** 支付方式:物流支付*/
  if(parameter.value.payType == '物流支付'){
    model.value.refundAmount = parameter.value.row?.cashOnDelivery ? (parameter.value.row?.cashOnDelivery / 100) : 0
    model.value.initAmount = parameter.value.row?.cashOnDelivery ? (parameter.value.row?.cashOnDelivery / 100) : 0
  }
    /** 订单状态:已完成*/
  if(parameter.value.status == '已完成' ){
    model.value.refundAmount = parameter.value.row?.money ? (parameter.value.row?.money / 100) : 0
    model.value.initAmount = parameter.value.row?.money ? (parameter.value.row?.money / 100) : 0
  }
   /** 支付方式:支付定金、订单状态:待收货*/
  if(parameter.value.payType == '支付定金' && parameter.value.status == '待收货'){
     model.value.type = 1
  }

  /** 订单状态: 待收货 或者 已完成 退款金额则为可编辑*/
  if(orderStatusLabels[params.row.status] == '待收货' || orderStatusLabels[params.row.status] == '已完成'){
    model.value.isModifyAmount = true
  }

  show.value = true
};
/* 表单实例 */
const formRef = ref(null);

/* 表单参数初始化 */
/* 清空表单 */
const formDataReset = () => {
  model.value = { ...initParams };
  model.value.afterSaleImgVOList = []
};

const parameter = ref<CreditChangesProps>({
});

/* 关闭弹窗 */
const closeModal = () => {
  formDataReset();
  // 弹窗取消
  show.value = false;
};

/** 多文件上传实例 */
const uploadProductImgRef = ref<InstanceType<typeof UploadProductImg> | null>(null);

/* 确认--保存 */
const isLoading = ref(false);
const _save = (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors && !isLoading.value) {
      // 是否正在进行文件上传
      if (uploadProductImgRef.value?.isUploadLoading) {
            message.createMessageWarning('文件正在上传中，请稍等！');
            return;
      }
      // 退款金额不能大于订单总金额
      if (model.value.refundAmount > model.value.initAmount) {
            message.createMessageError('退款金额不能大于订单总金额');
            return;
      }
      isLoading.value = true;
      const _params = {
        data:{
          orderCode:parameter.value?.row.code,
          type:model.value.type,
          reason:model.value.reason,
          reasonDescription:model.value.reasonDescription,
          refundAmount:(Number(model.value.refundAmount) * 100).toFixed(),
          phone:model.value.phone,
          afterSaleImgVOList:model.value.afterSaleImgVOList
        }
      };
      try {
        await afterSaleRecordRefund(_params)
        closeModal()
        message.createMessageExportSuccess("退款成功");
        // 刷新表格数据
        parameter.value.refresh();
        // 关闭抽屉
        if(parameter.value.closeDrawer){
          parameter.value.closeDrawer();
        }
      } catch (e) {
        message.createMessageError(`退款失败： ${e}`);
      }finally{
        isLoading.value = false;
      }
    }
  });
};

/** 判断输入的手机号码是否正确 */
const handleMobilePhone = () =>{
    const phoneRegex = /^[1]([3-9])[0-9]{9}$/; // 手机号正则
    if(!phoneRegex.test(model.value.phone) && model.value.phone != null){
        model.value.phone = null
        message.createMessageWarning('请输入有效的手机号码');
    }
}

/** 控制售后类型  */
const ordersAfterSalesOptionsValue = computed(() => {
  return  ordersAfterSalesOptions.map((option) => ({
    ...option,
    disabled: option.value === 2 ? ((parameter.value.isVirtual && parameter.value.row.fromType == 7) ? true : false) : null, // 动态禁用条件
  }));
})

/** 目前为定金支付 使用 */
watch(()=>model.value.type,
 (newVal)=>{
  /** 支付方式:支付定金  订单状态:待收货  售后类型:仅退款 */
  if(parameter.value.payType == '支付定金' && parameter.value.status == '待收货' && ordersAfterSalesLabels[newVal] == '仅退款'){
     model.value.refundAmount = parameter.value.row?.onlinePayment ? (parameter.value.row?.onlinePayment / 100) : 0
     model.value.initAmount = parameter.value.row?.onlinePayment ? (parameter.value.row?.onlinePayment / 100) : 0
  }
  /** 支付方式:支付定金  订单状态:待收货  售后类型:退货退款*/
  if(parameter.value.payType == '支付定金' && parameter.value.status == '待收货' && ordersAfterSalesLabels[newVal] == '退货退款'){
    model.value.refundAmount = parameter.value.row?.money ? (parameter.value.row?.money / 100) : 0
    model.value.initAmount = parameter.value.row?.money ? (parameter.value.row?.money / 100) : 0
  }
})

defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less">
::v-deep .n-form-item-label{
  width: 70px !important;
}
</style>
  