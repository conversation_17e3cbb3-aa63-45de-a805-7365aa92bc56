<template>
  <n-card class="consult-card">
    <!-- 标题区域 -->
    <div class="card-header">
      <n-text tag="h2" strong class="card-title">问诊卡</n-text>
      <n-tag type="info" size="small">常规问诊</n-tag>
    </div>

    <!-- 患者信息列 -->
    <n-grid :cols="24" class="patient-info">
      <n-gi :span="24">
        <n-text depth="3" class="info-label">患者姓名：</n-text>
        <n-text>{{ patientName }}</n-text>
      </n-gi>
      <n-gi :span="24">
        <n-text depth="3" class="info-label">主诉：</n-text>
        <n-text>{{ chiefComplaint }}</n-text>
      </n-gi>
      <n-gi :span="24">
        <n-text depth="3" class="info-label">患病时间：</n-text>
        <n-text>{{ startIllnessTime }}</n-text>
      </n-gi>
      <n-gi :span="24">
        <n-text depth="3" class="info-label">肝功能：</n-text>
        <n-text>{{ isLiver }}</n-text>
      </n-gi>
      <n-gi :span="24">
        <n-text depth="3" class="info-label">肾功能：</n-text>
        <n-text>{{ isKidney }}</n-text>
      </n-gi>
      <n-gi :span="24">
        <n-text depth="3" class="info-label">过敏史：</n-text>
        <n-text>{{ hasAllergyHi }}</n-text>
      </n-gi>
      <n-gi :span="24">
        <n-text depth="3" class="info-label">个人病史：</n-text>
        <n-text>{{ isPersonalMedicalHi }}</n-text>
      </n-gi>
      <n-gi :span="24">
        <n-text depth="3" class="info-label">家族病史：</n-text>
        <n-text>{{ homeMedicalHi }}</n-text>
      </n-gi>
      <n-gi :span="24">
        <n-text depth="3" class="info-label">备孕、妊娠、哺乳：</n-text>
        <n-text>{{ isPreparePregnant }}</n-text>
      </n-gi>


    </n-grid>
    <n-divider class="card-divider" v-if="medicalRecordsData.length > 0"/>
    <n-grid x-gap="12" y-gap="12" :cols="4" class="image-placeholders">
      <n-gi v-for="(item,index) in medicalRecordsData" :key="index" class="image-placeholder">
        <NImage
            :src="item"
            width="90"
            height="90"
        />
      </n-gi>
    </n-grid>
  </n-card>
</template>

<script setup lang="ts">
import {computed, ref, watch} from 'vue'
import {isNullOrUnDef, transformMinioSrc} from "@/utils";
import type {JChatMessageModel} from "@/views/DoctorEndModule/IM/types";

interface IProps {
  content: object;
  messageItem: JChatMessageModel;
}

enum PeriodKey {
  Week = 1,
  Month,
  HalfYear,
  BeyondHalfYear,
}

// 创建映射对象
const PeriodMap: { [key in PeriodKey]: string } = {
  [PeriodKey.Week]: "一周内",
  [PeriodKey.Month]: "一个月内",
  [PeriodKey.HalfYear]: "半年内",
  [PeriodKey.BeyondHalfYear]: "半年以上",
};

enum StatusKey {
  Normal = 0,
  Abnormal = 1
}

const StatusMap: { [key in StatusKey]: string } = {
  [StatusKey.Normal]: "正常",
  [StatusKey.Abnormal]: "异常",
}

// 存在性枚举：直接表达业务含义
enum PresenceKey {
  NonExistent = 0,
  Exist = 1,
}

const PresenceMap: { [key in PresenceKey]: string } = {
  [PresenceKey.NonExistent]: "无",
  [PresenceKey.Exist]: "有",
}


const props = withDefaults(defineProps<IProps>(), {
  content: () => ({}),
  messageItem: () => ({} as JChatMessageModel),
});

const receptionData = ref()

function changeReceptionData() {
  receptionData.value = props.content
}

changeReceptionData()


const patientName = computed(() => {
  const age = receptionData.value?.patientAge ? `${receptionData.value?.patientAge}岁` : ''
  const sex = receptionData.value?.patientSex ? `${receptionData.value?.patientSex}` : ''
  const bracket = (age || sex) ? `(${age} ${sex})` : ''
  return `${receptionData.value?.patientName || '-'}${bracket}`
})

const hasAllergyHi = computed(() => {
  return `${receptionData.value?.isAllergyHi || '-'}`
})

const homeMedicalHi = computed(() => {
  return `${receptionData.value?.isHomeMedicalHi || '-'}`
})

const isPersonalMedicalHi = computed(() => {
  return `${receptionData.value?.isPersonalMedicalHi || '-'}`
})


const chiefComplaint = computed(() => {
  return `${receptionData.value?.chiefComplaint || '-'}`
})

const startIllnessTime = computed(() => {
  return !isNullOrUnDef(receptionData.value.period) ? PeriodMap[receptionData.value.period] : '-'
})

const isPreparePregnant = computed(() => {
  return !isNullOrUnDef(receptionData.value.isPreparePregnant) ? PresenceMap[receptionData.value.isPreparePregnant] : '-'
})

const isLiver = computed(() => {
  return !isNullOrUnDef(receptionData.value?.isLiver) ? StatusMap[receptionData.value.isLiver] : '-'
})

const isKidney = computed(() => {
  return !isNullOrUnDef(receptionData.value?.isKidney) ? StatusMap[receptionData.value.isKidney] : '-'
})

const medicalRecordsData = computed(() => {
  const result = [];

  // 遍历对象自身可枚举属性键
  Object.keys(receptionData.value).forEach(key => {
    // 检测键名是否包含目标字符串（不区分大小写）
    if (key.includes('medicalRecords')) {
      result.push(transformMinioSrc(receptionData.value[key]));
    }
  });

  return result;
})

watch(() => props.messageItem, (newValue) => {

  // changeReceptionData()
})

</script>

<style scoped>
.consult-card {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  max-width: 680px;
  padding: 0px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.card-title {
  font-size: 1.5rem;
  color: #1d2129;
}

.patient-info,
.medical-info {
  margin: 16px 0;
}

.info-label {
  flex: 0 0 80px;
  text-align: left;
  color: #8a8f8d;
}

.content-section {
  margin: 20px 0;
}

.card-divider {
  margin: 20px 0;
  background-color: #f0f0f0;
}

.image-placeholders {
  gap: 16px;
  margin-top: 16px;
}

.image-placeholder {
  height: 90px;
//background: linear-gradient(145deg, #89c4f4, #6b89c4); //border-radius: 6px; //box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

@media (max-width: 640px) {
  .consult-card {
    padding: 16px;
  }

  .info-label {
    flex-basis: 70px;
  }

  .image-placeholder {
    width: 100%;
    height: 80px;
  }
}

</style>
