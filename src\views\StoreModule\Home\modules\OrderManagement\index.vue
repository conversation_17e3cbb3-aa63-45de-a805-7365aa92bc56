<template>
    <NCard :bordered="false" style="border-radius: 16px;">
        <NSpin :show="loading" size="small">
            <NSpace vertical :size="20">
                <NFlex align="center" :size="2">
                    <span class="title">订单管理</span>
                    <!-- <span class="subhead">(不含积分兑换订单)</span> -->
                </NFlex>
                <NGrid cols="s:2 m:4 l:5 xl:6" responsive="screen" :x-gap="28" :y-gap="16">
                    <NGi v-for="item in dataPanel" :key="item.title">
                        <NCard 
                            :bordered="false"
                            :style="{ height: '165px', backgroundImage: `url(${cardBg})`, backgroundSize: '100% 100%' }"
                        >
                            <div class="data-content">
                                <span class="data-title">{{ item.title }}</span>
                                <div class="data-num" :style="{ color: 'black' }">
                                    <n-number-animation :from="0" :to="Number(item.num)" />
                                </div>
                            </div>
                        </NCard>
                    </NGi>
                </NGrid>
            </NSpace>
        </NSpin>
    </NCard>
</template>

<script lang="ts" setup>
import { reactive, computed } from "vue";
import { getOrderCount } from "@/services/api";
import { useLoading, useMessages } from "@/hooks";
import cardBg from "@/assets/image/system/home/<USER>/cardBg.png";

const message = useMessages();

/** 数据源 */
const orderCount = reactive({
    todayOrderCount: 0,
    weekOrderCount: 0,
    monthOrderCount:0
});
/** 数据面板 */
const dataPanel = computed(() => {
    let originData = [
    {
            title: '今日订单',
            num: orderCount.todayOrderCount,
        },
        {
            title: '本周订单',
            num: orderCount.weekOrderCount,
        },
        {
            title: '本月订单',
            num: orderCount.monthOrderCount,
        }
    ];
    return originData;
});
/** 获取订单管理信息 */
const { loading, startLoading, endLoading } = useLoading();
function getPendingMangement() {
    startLoading();
    getOrderCount().then(res => {
        Object.assign(orderCount, res);
    }).catch(err => {
        message.createMessageError("获取订单管理信息失败：" + err);
    }).finally(() => {
        endLoading();
    });
};

function init() {
    getPendingMangement();
}
init();
</script>

<style lang="less" scoped>
@import "@/styles/default.less";

.title {
    font-size: 20px;
    font-weight: bold;
    color: #333333;

    &::before {
        content: '';
        display: inline-block;
        width: 4px;
        height: 16px;
        background: #4DA4FF;
        border-radius: 12px;
        margin-right: 4px;
    }
}

.subhead {
    font-size: 16px;
    color: #999999;
}

.data-content {
    display: flex;
    flex-direction: column;

    .data-title {
        font-size: 16px;
        color: #666666;
        display: flex;
        align-items: center;
        margin-top: 12px;
    }

    .data-num {
        font-size: 56px;
        font-family: Bebas Neue, Bebas Neue;
        // 字间距
        letter-spacing: -3px;
    }
}
</style>