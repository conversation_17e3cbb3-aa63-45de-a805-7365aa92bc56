import TUIChatEngine, {
    TUIChatService,
    TUIStore,
    StoreName,
    TUITranslateService,
    type SendMessageParams,
    type IConversationModel,
} from '@tencentcloud/chat-uikit-engine';
// import {Toast, TOAST_TYPE} from '../../common/Toast/index';
import {isEnabledMessageReadReceiptGlobal} from "@/views/DoctorEndModule/IM/utils/IMUtils";
import {enableSampleTaskStatus} from "@/views/DoctorEndModule/IM/utils/enableSampleTaskStatus";
import type {ITipTapEditorContent, JConversationModel} from "@/views/DoctorEndModule/IM/types";
import {useMessages} from "@/hooks";
import {useUserStore} from "@/stores/modules/user";
import ImEMitter from "@/views/DoctorEndModule/IM/utils/ImMitter";

const userStore = useUserStore();
const {createMessageError} = useMessages()

export const sendMessageErrorCodeMap: Map<number, string> = new Map([
    [3123, '文本包含本地审核拦截词'],
    [4004, '图片消息失败,无效的图片格式'],
    [4005, '文件消息失败,禁止发送违规封禁的文件'],
    [7004, '文件不存在,请检查文件路径是否正确'],
    [7005, '文件大小超出了限制,如果上传文件,最大限制是100MB'],
    [8001, '消息长度超出限制,消息长度不要超过12K'],
    [80001, '消息或者资料中文本存在敏感内容,发送失败'],
    [80004, '消息中图片存在敏感内容,发送失败'],
    [10017, '您已被禁止聊天'],
]);
/**
 * This function only processes five message types: Text/TextAt/Image/Video/File
 * @param messageList
 * @param currentConversation
 */
export const sendMessages = async (
    messageList: ITipTapEditorContent[],
    currentConversation: JConversationModel,
) => {
    for (const content of messageList) {
        try {
            const sendMessageOptions = {
                offlinePushInfo: {},
            };
            let sendMessageResult = null
            let options: SendMessageParams = {
                to: currentConversation?.contactImUserId,
                conversationType: TUIChatEngine.TYPES.CONV_C2C,
                payload: {},
                needReadReceipt: isEnabledMessageReadReceiptGlobal(),
            };
            // handle message typing
            let textMessageContent;
            // 自定义的消息格式
            let customPayloadData = null
            if (content.type == 'custom') {
                textMessageContent = JSON.parse(JSON.stringify(content.payload?.text));
                customPayloadData = {
                    "fromUserType": 1,
                    "fromUserId": userStore.imConfig?.userID,
                    "toUserType": 0,
                    "toUserId": currentConversation?.contactUserId,
                    "conversationId": currentConversation.conversationId,
                    "content": textMessageContent
                }
            }
            switch (content?.type) {
                case 'text':
                    textMessageContent = JSON.parse(JSON.stringify(content.payload?.text));
                    // Do not send empty messages
                    if (!textMessageContent) {
                        break;
                    }
                    options.payload = {
                        text: textMessageContent,
                    };
                    if (content.payload?.atUserList) {
                        options.payload.atUserList = content.payload.atUserList;
                        sendMessageResult = await TUIChatService.sendTextAtMessage(options);
                    } else {
                        sendMessageResult = await TUIChatService.sendTextMessage(options);
                    }
                    if (sendMessageResult.data) {
                        sendMessageResult = sendMessageResult.data.message
                        ImEMitter.emit('GetSendMessageResult', sendMessageResult)
                    }
                    break;
                // 自定义消息
                case 'custom':
                    textMessageContent = JSON.parse(JSON.stringify(content.payload?.text));
                    if (!textMessageContent) {
                        break;
                    }
                    options.payload = {
                        data: JSON.stringify(customPayloadData),
                        description: 'TEXT',
                        extension: '',
                    }
                    sendMessageResult = await TUIChatService.sendCustomMessage(options);
                    if (sendMessageResult?.data?.message) {
                        sendMessageResult = sendMessageResult.data.message
                        ImEMitter.emit('GetSendMessageResult', sendMessageResult)
                    }
                    break;
                default:
                    break;
            }
            enableSampleTaskStatus('sendMessage');
        } catch (error: any) {
            createMessageError({
                content: sendMessageErrorCodeMap.get(error?.code)
                    ? `${sendMessageErrorCodeMap.get(error.code)}`
                    : error?.message,
            })
        }
    }
};

export const sendTyping = (inputContentEmpty: boolean, inputBlur: boolean) => {
    if (!inputContentEmpty && !inputBlur) {
        TUIChatService.enterTypingState();
    } else {
        TUIChatService.leaveTypingState();
    }
};
