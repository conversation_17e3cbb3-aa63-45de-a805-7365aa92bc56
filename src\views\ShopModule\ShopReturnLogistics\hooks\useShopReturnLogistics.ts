import { ShopReturnLogisticsEnum } from "@/enums";

export default function useShopReturnLogistics() {
  /**
   * @description 单位分转元
   * @param {RoutesName} name 路由名称
   *
   * */
  function toYuanString(value: number | string) {
    return (parseFloat(String(value ?? "0")) / 100).toFixed(2);
  }

  /**
   * @description 获取状态对应的label
   * @param {RoutesName} status 状态
   * */
  function getTableListStatusLabel(status: string) {
    switch (status) {
      case ShopReturnLogisticsEnum.InProgress:
        return "待审核";
      case ShopReturnLogisticsEnum.Completed:
        return "已完成";
      case ShopReturnLogisticsEnum.NotPassed:
        return "审核不通过";
      default:
        return "-";
    }
  }

  return {
    toYuanString,
    getTableListStatusLabel
  };
}
