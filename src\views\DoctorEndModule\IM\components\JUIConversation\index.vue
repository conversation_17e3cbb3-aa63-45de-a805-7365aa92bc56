<template>
  <div class="tui-conversation" @click="handleClickConv">
    <JConversationList @handleSwitchConversation="handleSwitchConversation"/>
  </div>
</template>

<script setup lang="ts">
import JConversationList from '@/views/DoctorEndModule/IM/components/JUIConversation/conversation-list/index.vue'

const emits = defineEmits(['handleSwitchConversation']);

const handleSwitchConversation = (conversationID: string) => {
  emits('handleSwitchConversation', conversationID);
};


</script>

<style scoped lang="less">

</style>
