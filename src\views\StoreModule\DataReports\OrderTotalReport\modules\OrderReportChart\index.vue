<template>
  <div class="chart-container">
    <div ref="orderLineRef" class="echarts"></div>
    <!-- loading -->
    <JLoading v-if="isGetLoading" />
    <n-flex justify="center" style="margin-top: 8px;">
      <n-radio-group v-model:value="checked" @update:value="handleRadioChange" shape="dot" direction="horizontal">
        <n-radio :value="ChartTypeEnum.TOTALORDER">订单数</n-radio>
        <n-radio :value="ChartTypeEnum.TOTALAMOUNT">订单总额</n-radio>
      </n-radio-group>
    </n-flex>
  </div>
</template>

<script lang="ts" setup name="OrderReportChart">
import { ref, watch } from "vue";
import dayjs from "dayjs";
import { isArray } from "@/utils";
import { ChartTypeEnum} from "../../type";
import { useEcharts } from "@/hooks";
import { useOrderEcharts } from "../../hooks";
/** 相关组件 */
import JLoading from "@/components/JLoading/index.vue";

/** props */
const props = defineProps<{
    // 搜索参数
    model: {
        rangeTime: Array<number>;
        type: number;
        orderBy: string;
        isAsc: boolean;
    };
}>();

const checked = ref(ChartTypeEnum.TOTALORDER);

const { lineOptions, isGetLoading, getChartData } = useOrderEcharts();
const { domRef: orderLineRef, updateOptions } = useEcharts(() => lineOptions, { onRender() {} });

/** 获取图表参数 */
function getChartParams() {
    const { rangeTime, type } = props.model;
    return {
        data:{
            startTime: dayjs(rangeTime[0]).format('YYYY-MM-DD 00:00:00'),
            endTime: dayjs(rangeTime[1]).format('YYYY-MM-DD 23:59:59'),
            type
        }
    }
}

/** 更新图表 */
async function _refresh() {
    await getChartData(getChartParams(), (res) => {
        if (isArray(res)) {
            updateOptions(opt => {
                opt.xAxis['data'] = res.map(item => item.reportDate);
                if (checked.value === ChartTypeEnum.TOTALORDER) {
                    opt.series[0].name = "订单数",
                    opt.title['text'] = "订单数",
                    opt.series[0]['data'] = res.map(item => item.totalOrder);
                } else {
                    opt.series[0].name = "订单数额",
                    opt.title['text'] = "订单数额",
                    opt.series[0]['data'] = res.map(item=>(item.totalAmount / 100).toFixed(2));
                }
                return opt;
            })
        }
    });
}

/** 选项组更新回调 */
async function handleRadioChange(value: ChartTypeEnum) {
    _refresh();
}

/** 暴露给父组件 */
defineExpose({
    _refresh
});

/** 监听 */
watch(() => props.model.rangeTime, (newVal) => {
    if (newVal) {
        _refresh();
    }
}, { immediate: true });
</script>

<style lang="less" scoped>
.chart-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    .echarts {
        flex: 1;
    }
}
</style>
