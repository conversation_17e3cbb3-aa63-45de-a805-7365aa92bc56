<template>
  <div class="wrapper">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :pagination="paginationRef"
      @paginationChange="paginationChange"
      :isTableSelection="true"
      @selectedKeysChange="selectedKeysChange"
      :isDisplayIndex="false"
    >
      <template #searchForm>
        <n-form-item label="创建时间" label-placement="left">
          <j-date-range-picker
            style="flex: 1;"
            v-model:value="formValue.creationTime"
            type="datetimerange"
            format="yyyy-MM-dd"
            :default-time="['00:00:00', '23:59:59']"
            clearable
          />
        </n-form-item>
      </template>
      <template #tableHeaderBtn>
        <n-button @click="refresh" class="store-button" :loading="isLoading">刷 新</n-button>
        <n-button
          @click="exportData"
          v-if="hasDeliveryExceptionExportAuth"
          class="store-button"
          type="primary"
          :loading="exportLoading"
        >
          导 出
        </n-button>
      </template>
      <template #tableFooterBtn="scope" v-if="props.tabNameRef === DeliveryExceptionType.ToBeProcessed">
        <!-- 批量上架 -->
        <n-popconfirm
          @positive-click="handleBatch(scope.selectedListIds, scope.selectedList, BatchType.MarkedAsProcessed)"
          :positive-button-props="{
                    loading: isBatchLoading
                }"
        >
          <template #trigger>
            <n-button ghost type="primary" size="small">批量标注为已处理</n-button>
          </template>
          此操作将把所有选中的订单标注为已处理，是否继续？
        </n-popconfirm>
        <!-- 批量下架 -->
        <n-popconfirm
          @positive-click="handleBatch(scope.selectedListIds, scope.selectedList, BatchType.Resend)"
          :positive-button-props="{
                    loading: isBatchLoading
                }"
          v-if="isSelectedCreateFailed"
        >
          <template #trigger>
            <n-button ghost type="primary" size="small">批量重新推送</n-button>
          </template>
          此操作将重新推送所有选中的订单，是否继续？
        </n-popconfirm>
      </template>
    </FormLayout>
  </div>
</template>
<script setup lang="tsx">
import { ref, computed, watch,reactive } from 'vue';
import { DeliveryExceptionType, DeliveryExceptionStateEnum, DeliveryChannelEnum} from '@/enums';
import { useTableDefault } from "@/hooks/useTableDefault";
import { getExceptionList, batchMarkAsProcessed, batchResend, exportOrder } from '@/services/api';
import FormLayout from "@/layout/FormLayout.vue";
import dayjs from 'dayjs';
import { deliveryChannelMap } from '@/components/DeliveryExceptionModal/type';
import { hasDeliveryExceptionExportAuth, hasDeliveryExceptionMarkedAsProcessedAuth, hasDeliveryExceptionResendAuth } from '@/components/DeliveryExceptionModal/authList';
import { useMessages } from "@/hooks";
const { createMessageSuccess, createMessageError } = useMessages();

const props = defineProps<{
  tabNameRef: DeliveryExceptionType,
}>();

/* 表格项 */
const tableColumns = computed(() => [
    {
        title: "订单编号",
        key: "orderCode",
        align: "left",
        width: 170,
        fixed: "left",
    },
    {
        title: "快递公司名称",
        key: "shipName",
        align: "left",
        width: 100,
    },
    {
        title: "快递单号",
        key: "trackingNo",
        align: "left",
        width: 100,
    },
    {
        title: "发货渠道",
        key: "deliveryChannel",
        align: "left",
        width: 100,
        render: (row) => {
            return deliveryChannelMap[row['deliveryChannel']]
        }
    },
    {
        title: "失败原因",
        key: "failureReason",
        align: "left",
        width: 200,
    },
    {
        title: "创建时间",
        key: "createTime",
        align: "left",
        width: 100
    },
    ...(props.tabNameRef === DeliveryExceptionType.ToBeProcessed ? [{
        title: "操作",
        key: "action",
        width: 100,
        align: "left",
        fixed: "right",
        render: (row) => {
            return (
                <n-space align="center" justify="center">
                    {hasDeliveryExceptionResendAuth && row['status'] == DeliveryExceptionStateEnum.CreateFailed ? <n-button text size="small" type="primary" class="mt-4 mr-4 edit-icon" onClick={() => handleChange([row.orderCode],BatchType.Resend)}>
                        重新推送
                    </n-button> : null}

                    {hasDeliveryExceptionMarkedAsProcessedAuth ? <n-popconfirm
                        onPositiveClick={() => handleChange([row.orderCode],BatchType.MarkedAsProcessed)}
                    >
                    
                        {{
                            trigger: () => (
                                <n-button text size="small" type="primary" >标记为已处理</n-button>
                            ),
                            default: () => <span style={{ width: '300px' }}>是否确定标记为已处理？</span>
                        }}
                    </n-popconfirm> : null}

                </n-space>
            );
        }
    }] : [])
])

/* 初始化参数 */
const initParams = {
    creationTime: null
};
const formValue = reactive({ ...initParams });
/* 表格方法Hook */
const {
    isLoading,
    tableData,
    paginationRef,
    paginationChange,
    pageTableData,
} = useTableDefault({
    pageDataRequest: getExceptionList,
});

function getTableData() {
  const { creationTime} = formValue;

    const _params = {
        isProcessed: props.tabNameRef,
        startTime: creationTime ? dayjs(creationTime?.[0]).format('YYYY-MM-DD HH:mm:ss') : null,
        endTime: creationTime ? dayjs(creationTime?.[1]).format('YYYY-MM-DD HH:mm:ss') : null
    }

    pageTableData(_params, paginationRef.value, true);
}

const refresh = () => {
    getTableData()
}
const isBatchLoading = ref<boolean>(false)

/** 批量处理 */
const handleBatch = (selectedListIds: string[], selectedList, type: BatchType) => {
    const paramRow = selectedList
        .filter(item => {
            // 如果是标记为已处理，返回所有订单
            if (type === BatchType.MarkedAsProcessed) {
                return true;
            }
            // 如果是重新推送，只返回创建失败的订单
            return item.status === DeliveryExceptionStateEnum.CreateFailed;
        })
        .map(item => item.orderCode);

    handleChange(paramRow, type);
}

const handleChange = (row, type:BatchType) => {
    isLoading.value = true
    const api = type == BatchType.MarkedAsProcessed ? batchMarkAsProcessed : batchResend
    api(row).then(res=>{
        createMessageSuccess('操作成功')
        refresh()
    }).catch(err=>{
        createMessageError(`${type == BatchType.MarkedAsProcessed ? '标记为已处理' : '重新推送'}失败:${err}`)
        isLoading.value = false
    })
}

const enum BatchType {
    /** 标记为已处理 */
    MarkedAsProcessed = '1',
    /** 重新推送 */
    Resend = '2'
}

const selectedArr = ref<string[]>([]);
/** 选中列表 */
const selectedKeysChange = (selectedKeys:string[], selectedRows:any[]) => {
    selectedArr.value = selectedRows
}

/** 是否选中创建失败的订单 */
const isSelectedCreateFailed = computed(() => {
    return selectedArr.value.some(item => item['status'] == DeliveryExceptionStateEnum.CreateFailed)
})

const exportLoading = ref<boolean>(false);
/** 导出订单 */
const exportData = () => {
    exportLoading.value = true;
    const _params = selectedArr.value.length > 0 ? selectedArr.value : tableData.value;
    
    exportOrder({data:_params}).then(res=>{
        createMessageSuccess('导出成功')
    }).catch(err=>{
        createMessageError(`导出失败:${err}`)
    }).finally(()=>{
        exportLoading.value = false
    })
}

watch(()=>[formValue.creationTime,props.tabNameRef],(val)=>{
    getTableData()
},{
    immediate: true
})
</script>
<style lang="less" scoped>
.wrapper{
    height: 100%;
}
</style>
