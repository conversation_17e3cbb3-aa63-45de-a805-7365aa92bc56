<template>
    <JDrawer v-bind="$attrs" title="发布视频" :isGetLoading="loadShow" @after-leave="closeDrawer" to="#VideoManagement"
        :contents-list="[
            {
                name: '发布账号',
                slotName: 'authorInfo'
            }, {
                name: '视频信息',
                slotName: 'videoInfo'
            }
        ]"  >
        <template #custom-title>
            <DrawerTitle title="发布视频" @goBack="customBack">
                      <template #title>
                        <slot name="title"></slot>
                      </template>
                   </DrawerTitle>
        </template>

        <template #authorInfo>
            <n-form ref="formRef" :show-feedback="false" label-placement="left" label-width="auto"
                require-mark-placement="right-hanging" size="small" :style="{ width: '60%' }" inline>
                <n-form-item label="昵称">
                    <JUserSelect v-model:value="select" placeholder="请选择发布账号"></JUserSelect>
                </n-form-item>

            </n-form>
        </template>
        <template #videoInfo>
            <div class="updateVideo">
                <div class="fileInfoList">
                    <n-tabs v-for="(item, index) in tableData" v-model:value="item.id" type="card" closable
                        tab-style="min-width: 80px;" @close="handleTabsClose" :key="item.id">
                        <n-tab-pane :key="item.id" :tab="`视频${index + 1}`" :name="item.id">
                            <div class="fileInfo">
                                <div class="preview" >
                                    <VideoPreviewModal :src="item.file"></VideoPreviewModal>
                                    <n-progress
                                    type="line"
                                    :percentage="item.percentage"
                                    :show-indicator="false"
                                    :height="3"
                                    :status="item.uploadStatus == uploadType.completeUploading?'success':'default'"
                                    class="uploadProgress"
                                    />
                                </div>
                                <n-input v-model:value="item.description" type="textarea" autosize 
                                    placeholder="添加该视频作品描述（非必填，最多录入300字）" maxlength="300" show-count />
                                <div class="videoGoods">
                                    <div class="addGoodsBox" @click="selectGoods(item, index)">
                                        <div>
                                            {{ item.productName || '点击添加商品' }}
                                        </div>
                                        <n-icon :component="ChevronForward" class="sharpIcon" size="20"
                                            color="#1677FF"></n-icon>
                                    </div>
                                </div>
                            </div>
                        </n-tab-pane>
                    </n-tabs>
                </div>
                <BatchVideoUpload ref="videoUpLoad" ></BatchVideoUpload>
                <!-- {{ tableData }} -->
            </div>


        </template>
        <template #footer>
            <div class="footer-wrapper">
                <n-space>
                    <n-button type="info" @click="_confirm" :loading="loadShow" :disabled="!isAllUploadEnd" > 确认发布 </n-button>
                </n-space>
            </div>
        </template>
    </JDrawer>
    <GoodsModal v-model:show="showGoodsModal" ref="goodsModalRef" :type="GoodsCategoryType.ALL"
        :showCategoryType="[GoodsCategoryType.DRUG, GoodsCategoryType.GENERAL]"
        @update:selected-options="handleSelectOptions" :selectProductId="selectProductId" 
        :goodsModalType="goodsModalType"
        ></GoodsModal>
</template>

<script setup lang="ts">
import JUserSelect from '@/components/JSelect/JUserSelect.vue';
import { ref, reactive, toRefs, onMounted, computed, watch , onBeforeUnmount } from 'vue';
const loadShow = ref<boolean>(false);
import type { UploadCustomRequestOptions, UploadFileInfo } from 'naive-ui';
import { ArchiveOutline as ArchiveIcon, CaretForwardCircleSharp, ChevronForward } from '@vicons/ionicons5';
import { useMessages } from "@/hooks";
const { createMessageSuccess, createMessageError } = useMessages();
import { getMediaDuration, isNullOrUnDef , isEmpty  } from "@/utils";
import GoodsModal from "./GoodsModal/index.vue";
import { GoodsCategoryType } from "@/enums";
import VideoPreviewModal from "./VideoPreviewModal/index.vue"
import { uploadVideoFile, deleteFile, publishVideo } from "@/services/api"
import { getOssFileUrlPrefix } from "@/utils/http/urlUtils";
import { transformMinioSrc } from "@/utils/fileUtils";
import BatchVideoUpload from "../components/BatchVideoUpload/index.vue"
import useBatchUploadVideo from "../hooks/useBatchVideoUpload";
import { uploadType , GoodsModalType } from "../type"
const {tableData,videoUploading} = useBatchUploadVideo();
import { userGetCoverImg } from "../hooks/userGetCoverImg"
const { getCoverImg } = userGetCoverImg()
const testPath = "https://sg-test-api.jiuwei.cloud/upload/downloadFile?filePath=video/20240925/83dd76e649549da7393e03a81d5d953d.mp4";
// const paramsFileList = ref([]);
import DrawerTitle from "@/components/DrawerTitle/index.vue";

const emits = defineEmits<{
    (e: 'closeVideo'): void;
    (e: 'update:show',value:boolean): void;
}>()

const videoUpLoad = ref(null)

// 视频删除回调  
const handleTabsClose = (name: string | number) => {
    // console.log(name, '关闭视频回调',tableData);
    const targetVideo = tableData.value.find(item=>item.id == name);
    if (targetVideo.uploadStatus == uploadType.completeUploading) {
        loadShow.value = true
        deleteFile(targetVideo.videoPath as string).then(res => {
            tableData.value = tableData.value.filter(item=>item.id != name)
            videoUpLoad.value.removefileList(name)
        }).catch(err => {
            console.log(err, '视频删除失败');
            createMessageError(`视频删除失败:${err}`)
        }).finally(() => {
            loadShow.value = false
        })
    }else if(targetVideo.uploadStatus == uploadType.goOnUploading){
         createMessageError('视频上传中不可删除')   
    }else{
        videoUpLoad.value.removeQueue(name)
    }
}

const isAllUploadEnd = computed(()=>{
    return tableData.value.every(item=>item.uploadStatus == uploadType.completeUploading);
})

const customBack = ()=>{
    // if (!isAllUploadEnd.value) return createMessageError("正在上传中 禁止操作");
    emits('update:show',false)
}

const select = ref(null)

/** 确认发布 */
const _confirm = () => {
    if (isNullOrUnDef(select.value)) return createMessageError('请选择发布账号');
    if (tableData.value.length == 0) return createMessageError('请先上传视频');
    loadShow.value = true;
    const params = [];
    tableData.value.forEach((item) => {
        // item.publisherId = select.value
        const videoRow = {
            publisherId:select.value,
            path:item.videoPath,
            description:item.description,
            coverImagePath:item.coverImagePath,
            productId:item.productId
        }
        params.push(videoRow)
    })
    publishVideo(params).then(res => {
        emits('closeVideo')
    }).catch(err => {
    createMessageError(`发布失败：${err}`)
        console.log(err);
    }).finally(()=>{
        loadShow.value = false;
    })
}
const goodsModalRef = ref(null)
const showGoodsModal = ref(false);                                  // 展示商品选择框
const goodsModalType = ref<GoodsModalType>(GoodsModalType.add);     // 弹窗状态
const selectProductId = ref<string>('')                             // 选中的商品id
const videoIndex = ref<null | number>(null)                         // 缓存要修改商品的索引（修改对应的商品时使用）
const selectGoods = (row, index) => {
    goodsModalType.value = isEmpty(row.productId) ? GoodsModalType.add : GoodsModalType.edit ;
    videoIndex.value = index
    showGoodsModal.value = true
    // goodsModalRef.value?.acceptParams();
    selectProductId.value = row.productId;

}

// 商品弹窗添加
const handleSelectOptions = (goodsList) => {

    if (goodsList[0].id == tableData.value[videoIndex.value].productId) {
        tableData.value[videoIndex.value].productId = ''
        tableData.value[videoIndex.value].productName = ''
    } else {
        tableData.value[videoIndex.value].productId = goodsList[0].id
        tableData.value[videoIndex.value].productName = goodsList[0].type == 
        GoodsCategoryType.DRUG 
        ? `[${goodsList[0].frontName}]${goodsList[0].name}${ goodsList[0]?.productSpecDTOList?.[0]?.name ?? ""}` 
        : goodsList[0].frontName
    }

    showGoodsModal.value = false;
}

/** 关闭抽屉 */
const closeDrawer = () => {
    select.value = null
    tableData.value = [];
    videoUpLoad.value.clearQueue()
};


</script>
<style lang="less" scoped>
@import "@/styles/default.less";

.updateVideo {
    width: 80%;
    padding: 0px 20px;
    box-sizing: border-box;

    .fileInfoList {
        .fileInfo {
            display: flex;
            gap: 50px;
            border: 1px solid #e8e8e8;
            margin-bottom: 20px;
            padding: 20px 10px;
            .preview{
                width: 150px;
                height: 150px;
                position: relative;
                .uploadProgress{
                    position: absolute;
                    bottom: 10px;
                    width: 90%;
                    left: 5%;
                    z-index: 99;
                }
            }


            .videoGoods {
                width: 40%;
                display: flex;
                align-items: center;

                .addGoodsBox {
                    border: 1px solid @primary-color;
                    display: flex;
                    align-items: center;
                    font-size: 14px;
                    color: @primary-color;
                    padding: 5px;
                    border-radius: 5px;
                    cursor: pointer;
                }
            }
        }
    }
}

.footer-wrapper {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}
</style>