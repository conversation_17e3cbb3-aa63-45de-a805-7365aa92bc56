<script lang="ts" setup>
import { ref, onBeforeUnmount } from "vue";
import { NLayout, NLayoutSider, NLayoutContent, NTooltip } from "naive-ui";
import type { VNode } from "vue";
import { useBoolean } from "@/hooks";
/** 相关组件 */
import Menu from "./components/Menu.vue";
import SvgIcon from "@/components/SvgIcon/index.vue";
import { isBoolean, _debounce, isUnDef } from "@/utils";

defineOptions({
  name: 'MenuLayout'
});

type MenuOption = {
  key: string | number;
  label: string;
  // icon?: () => VNode;
  /** 为false时不渲染该菜单 */
  show?:boolean
}

/** props */
const props = defineProps<{
  menuOptions: Array<MenuOption>; // 菜单项
  activeKey: string | number; // 选中menu
}>();
/** emits */
const emits = defineEmits<{
  ( e: 'update:activeKey', value: string | number ): void;
}>()

/** 是否展开 */
const { bool: collapsed, toggle, setBool } = useBoolean(false);

/** 监听窗口大小变化，折叠侧边栏 */
// const screenWidth = ref(0);
// const listeningWindow = _debounce(() => {
//   screenWidth.value = document.body.clientWidth;
//   if (!collapsed.value && screenWidth.value < 1200) setBool(true);
//   if (collapsed.value && screenWidth.value > 1200) setBool(false);
// }, 100);
// window.addEventListener("resize", listeningWindow, false);
// onBeforeUnmount(() => {
//   window.removeEventListener("resize", listeningWindow);
// });
</script>

<template>
  <NLayout has-sider style="border-radius: 4px;">
    <NLayoutSider collapse-mode="width" :collapsed-width="66" :collapsed="collapsed" :width="140" bordered>
      <div class="menu-wrapper">
        <div class="left">
          <NTooltip
            v-for="item in props.menuOptions.filter(option=>(isUnDef(option.show)) || (isBoolean(option.show) && option.show))"
            :key="item.key"
            placement="right"
            :disabled="!collapsed"
            :content-style="{
              padding: '2px',
            }"
          >
            <template #trigger>
              <div
                class="menu-list"
                :class="{ 'active': item.key === props.activeKey }"
                :style="{ justifyContent: !collapsed ? 'flex-start' : 'center'}"
                @click="emits('update:activeKey', item.key)"
              >
                <!-- 去除图标 -->
                <!-- <component :is="item.icon" style="font-size: 18px;" /> -->
                <div v-if="!collapsed" class="menu-label">{{ item.label }}</div>
              </div>
            </template>
            {{ item.label }}
          </NTooltip>
        </div>
        <!-- 切换 -->
        <!-- <div class="collapsed">
          <Menu :tooltip-content="collapsed ? '展开' : null" tooltip-placement="right" @click="toggle">
            <SvgIcon :localIcon="collapsed ? 'MenuFoldRight' : 'MenuFoldLeft'" style="font-size: 20px;" />
            <span class="title">{{ collapsed ? null : '收起导航' }}</span>
          </Menu>
        </div> -->
      </div>
    </NLayoutSider>
    <NLayoutContent>
      <div class="inner-page-height">
        <transition appear name="fade-slide" mode="out-in">
          <slot></slot>
        </transition>
      </div>
    </NLayoutContent>
  </NLayout>
</template>

<style lang="less" scoped>
@import "@/styles/default.less";
:deep(.n-layout) {
  background-color: @blank-background-color;
}
.menu-wrapper {
  width: 100%;
  height: 100%;
  padding: 6px 8px;
  box-sizing: border-box;
  overflow: hidden;
  .left {
    width: 100%;
    height: 100%;
    // height: calc(100% - 46px);
    .menu-list {
      width: 100%;
      height: 32px;
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      margin-bottom: 6px;
      box-sizing: border-box;
      border-radius: 4px;
      padding: 0 11px;
      overflow: hidden;
      transition: all .3s;
      &:hover {
        background-color: rgb(231, 241, 255);
        color: rgb(42, 119, 255);
      }
      .menu-label {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        max-width: 120px;
        font-size: 14px;
      }
    }
  }
  .collapsed {
    height: 40px;
    position: relative;
    &::before {
      background: rgba(31, 35, 41, .15);
      content: "";
      height: 0;
      height: 1px;
      left: 8px;
      pointer-events: none;
      position: absolute;
      right: 8px;
      top: 0;
      transform: translateY(-6px);
    }
    .title {
      font-size: 16px;
    }
  }
}

.active{
  background: rgb(231, 241, 255);
  color: rgb(42, 119, 255);
}
</style>
