<template>
    <n-space vertical style="width: 100%;">
        <p v-if="props.notice" class="pwd-notice">{{ props.notice }}</p>
        <n-input
          :value="props.value"
          type="password"
          show-password-on="click"
          @input="handlePasswordInput"
          @keydown.enter.prevent
          @update:value="handleInputChange"
        />
        <div 
            v-if="props.enableStrengthCheck" 
            :class="['strengthLevelWrapper']"
        >
            <div class="progress">
                <div  
                    class="progress-fill"
                    :style="{
                        background:progressInfoComputed.color,
                        width:progressInfoComputed.width
                    }"
                ></div>
            </div>
            <div class="progress-outside-wrapper">
                <div 
                    v-for="level in levelItem" 
                    :key="level" 
                    class="strengthLevelItemWrapper" 
                >
                    <div class="strengthLevelItem"></div>
                </div>
            </div>
        </div>
    </n-space>
</template>
<script setup lang="ts">
import { computed, ref } from 'vue';
import { PwdLevelEnum } from './types';
import { checkPwdStrength } from './pwdUtils';

    interface PwdInputProps{
        value:string,
        enableStrengthCheck:boolean,
        notice?:string
    }
    interface PwdInputEmits{
        (e: "update:value", value: string): void;
        (e:'')
    }

    const props = withDefaults(defineProps<PwdInputProps>(),{
        value:'',
        enableStrengthCheck:true,
        notice:''
    })
    const emits = defineEmits<PwdInputEmits>()


    const levelItem = 3
    const nowLevelRef = ref<PwdLevelEnum>(PwdLevelEnum.IDLE)
    const progressInfoComputed = computed(()=>{
        const progressInfo = {
            color:'#eee',
            width:'0%'
        }
        if(nowLevelRef.value == PwdLevelEnum.LOW){
            progressInfo.color = '#FF4D4F'
            progressInfo.width = '33%'
        }
        else if(nowLevelRef.value == PwdLevelEnum.MID){
            progressInfo.color = '#f0a020'
            progressInfo.width = '66%'
        }
        else if(nowLevelRef.value == PwdLevelEnum.HIGH){
            progressInfo.color = '#00B42A'
            progressInfo.width = '100%'
        }
        return progressInfo
    })
    function handlePasswordInput(value:string){
        nowLevelRef.value = checkPwdStrength(value.trim())
    }

    function handleInputChange(value:string){
        emits("update:value",value.trim())
    }

</script>
<style lang="less" scoped>
    .pwd-notice{
        color:#999;
        padding: 2px;
    }
    .strengthLevelWrapper{
        display: flex;
        width: 100%;
        justify-content: space-between;
        margin:5px 0px;
        position: relative;
        .progress{
            width: 100%;
            height: 8px;
            background: #eee;
            .progress-fill{
                height: 100%;
                width:0%;
                transition: all 0.1s ease-in-out;
            }
        }
        .progress-outside-wrapper{
            width: 100%;
            position: absolute;
            top: 0px;
            left: 0px;
            display: flex;
            .strengthLevelItemWrapper{
                border:2px solid white;
                border-width: 2px 2px;
                flex:1;
                height: 4px;
                .strengthLevelItem{
                    background: transparent;
                }
            }
        }
    }
</style>