<template>
  <MenuLayout v-model:activeKey="activeTypeRef" :menuOptions="salesList">
    <component :is="currentPage" />
  </MenuLayout>
</template>

<script lang="ts" setup name="SalesManagement">
import { ref, computed } from "vue";
// import { useSvgIcon } from "@/hooks";
import { SalesManagementType, type SalesType } from "@/enums";
import { hasMemberPointsPageAuth,hasWelfareClassifyPageAuth,hasWelfareManagementPageAuth,hasWelfareGoodsManagementPageAuth } from "./authList";
/** 相关组件 */
import MenuLayout from "@/components/MenuLayout/index.vue";
import MemberIntegral from "./MemberIntegral/index.vue";
import WelfareTicketClass from "./WelfareTicketClass/index.vue";
import WelfareTicketManage from "./WelfareTicketManage/index.vue";
import WelfareGoodsManage from "./WelfareGoodsManage/index.vue";
// const { SvgIconVNode } = useSvgIcon();

const activeTypeRef = ref<SalesType>(SalesManagementType.MEMBERPOINTS);

/** 营销管理 tab */
const salesList = ref([
  {
    label: '会员积分',
    key: SalesManagementType.MEMBERPOINTS,
    // icon: SvgIconVNode({ localIcon: 'Crown', fontSize: 18 }),
    show: hasMemberPointsPageAuth
  },
  {
    label: '福利商品管理',
    key: SalesManagementType.WELFARE_GOODS_MANAGE,
    show: hasWelfareGoodsManagementPageAuth
  },
  {
    label: '福利券分类',
    key: SalesManagementType.WELFARE_TICKET_CLASS,
    show: hasWelfareClassifyPageAuth
  },
  {
    label: '福利券管理',
    key: SalesManagementType.WELFARE_TICKET_MANAGE,
    show: hasWelfareManagementPageAuth
  },
]);

/** 相关组件 */
const pageMap = {
  [SalesManagementType.MEMBERPOINTS]: MemberIntegral,
  [SalesManagementType.WELFARE_TICKET_CLASS]: WelfareTicketClass,
  [SalesManagementType.WELFARE_TICKET_MANAGE]: WelfareTicketManage,
  [SalesManagementType.WELFARE_GOODS_MANAGE]: WelfareGoodsManage,
}

/** 当前页 */
const currentPage = computed(() => pageMap[activeTypeRef.value])
</script>

<style lang="less" scoped></style>
