<template>
    <n-space vertical size="large" >
      <input type="file" class="input-file" style="display: none" accept=".xlsx,.xls" @change="handleFileUpload">
      <JAddButton width="100px" type="primary" secondary @click="openFileExplorer" :loading="props.isLoadingShow" >上  传</JAddButton>
      <n-button type="primary" text @click="downloadFile" :loading="props.isLoadingShow" >下载模板</n-button>
    </n-space>
</template>
  
<script setup lang="tsx">
import {useMessages} from "@/hooks";
const { createMessageSuccess, createMessageError } = useMessages();
import { downloadLocalFile } from "@/utils/fileUtils";
const props = withDefaults(
  defineProps<{
    isLoadingShow:boolean
  }>(),
  {
    isLoadingShow:false
  }
);
const emits = defineEmits<{
    (e: "importedData", importedData); //导入的数据返回 
  }>();
const openFileExplorer = () => {
      const fileInput: HTMLInputElement | null =  document.querySelector(".input-file")
      if (fileInput) fileInput.click();
};
const handleFileUpload = (event) => {
    const file = event.target.files[0];
    const fileName = file.name;
    const fileType = file.type;
    if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls') || fileType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || fileType === 'application/vnd.ms-excel') {
    const reader = new FileReader();
    reader.onload = (e) => {
        const file = new File([e.target.result], fileName, { type: fileType });
        const formData = new FormData();
        formData.append('file', file); // 将二进制数据附加到表单数据中
        emits("importedData", formData);
        
    };
        reader.readAsArrayBuffer(file);
    }else{
        createMessageError('请上传.xlsx或.xls格式的文件');
    }
};

/** 下载模板 */
const downloadFile = () => {
    downloadLocalFile('/static/costDeductionTemplate.xlsx','成本扣除模板.xlsx')
}
</script>