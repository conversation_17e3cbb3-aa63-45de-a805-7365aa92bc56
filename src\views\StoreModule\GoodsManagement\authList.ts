import { GoodsManagementAuth } from "@/enums/authKeys";
import { hasAuth } from "@/utils/auth/authUtils";


/** 新建商品 */
export const hasAddGoodsAuth= function(){
    return hasAuth(GoodsManagementAuth.goodsManagementIndexAdd.key);
}()

/** 编辑商品 */
export const hasEditGoodsAuth = function(){
    return hasAuth(GoodsManagementAuth.goodsManagementIndexEdit.key);
}()

/** 复制创建 */
export const hasCopyGoodsAuth = function(){
    return hasAuth(GoodsManagementAuth.goodsManagementIndexCopy.key);
}()

/** 批量下架/批量上架 */
export const hasBulkGoodsAuth = function(){
    return hasAuth(GoodsManagementAuth.goodsManagementIndexBulk.key);
}()

/** 新建分类 */
export const hasAddGoodsClassAuth = function(){
    return hasAuth(GoodsManagementAuth.goodsManagementIndexNewcategory.key);
}()

/** 编辑分类 */
export const hasEditGoodsClassAuth = function(){
    return hasAuth(GoodsManagementAuth.goodsManagementIndexEditcategory.key);
}()

/** 删除分类 */
export const hasDeleteGoodsClassAuth = function(){
    return hasAuth(GoodsManagementAuth.goodsManagementIndexDeletecategory.key);
}()

/** 审核商品 */
export const hasAuditGoodsAuth = function(){
    return hasAuth(GoodsManagementAuth.goodsManagementIndexAudit.key);
}()


