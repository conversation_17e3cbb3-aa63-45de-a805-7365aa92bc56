<template>
  <JDrawer
      v-model:show="drawerVisible"
      :title="prescriptionType[drawerProps?.type]"
      to="#prescription-management"
      id="product-details"
      :isGetLoading="loadShow"
      :isShowFooter="drawerProps?.type !== '详情'"
      @after-leave="closeDrawer"
      :contents-list="[
      {
        name: '处方状态',
        slotName: 'prescript-info'
      },
      {
        name: '临床诊断',
        slotName: 'clinical-diagnosis'
      },
      {
        name: '用药',
        slotName: 'prescription'
      },
      {
        name: '病例',
        slotName: 'patient-case'
      }
    ]"
  >
    <template #prescript-info>
      <div style="margin-bottom: 12px">
        <n-descriptions label-placement="left" :column="1">
          <n-descriptions-item label="医生开方时间">2025-05-10 25：24：12</n-descriptions-item>
        </n-descriptions>
      </div>
    </template>

    <!-- 临床诊断 -->
    <template #clinical-diagnosis>
      <div style="margin-bottom: 12px">
        <n-descriptions label-placement="left" :column="1">
          <n-descriptions-item label="诊断结果">高血压 高血脂</n-descriptions-item>
        </n-descriptions>
      </div>
    </template>

    <template #prescription>
      <n-grid cols="6 m:12 l:18 xl:24" :x-gap="32" responsive="screen">
        <!-- 表格信息 -->
        <n-gi :span="23">
          <div class="rp-container">
            <h2>Rp</h2>
            <div v-for="(drug, index) in model.drugs" :key="index" class="drug-item">
              <div class="drug-info">
                <span class="drug-name">{{ drug.name }}</span>
                <span class="drug-spec">{{ drug.spec }} x{{ drug.num }}</span>
              </div>
              <div class="usage-info">
                <span>用法用量：{{ drug.way }} {{ drug.frequency }} 每次{{ drug.dosage }} {{ drug.unit }}</span>
              </div>
            </div>
          </div>
        </n-gi>
      </n-grid>
    </template>

    <template #patient-case>
      <n-grid x-gap="20" :cols="3" responsive="screen" :collapsed="false">
        <!-- 左侧基本信息 -->
        <n-gi>
          <n-card title="基本信息" :bordered="false" class="light-green-box"
                  :header-style="{ 'padding-top': '0px','padding-bottom':'0px' }">
            <n-descriptions label-placement="left" :column="1">
              <n-descriptions-item label="患者姓名">张一</n-descriptions-item>
              <n-descriptions-item label="性别">男</n-descriptions-item>
              <n-descriptions-item label="年龄">88岁</n-descriptions-item>
              <n-descriptions-item label="主诉">
                感冒发烧3天，未用药，偶尔咳嗽
              </n-descriptions-item>
              <n-descriptions-item label="患病时长">一周内</n-descriptions-item>
            </n-descriptions>
          </n-card>
        </n-gi>

        <!-- 中间健康状况 -->
        <n-gi>
          <n-card title="健康状况" :bordered="false" class="green-box"
                  :header-style="{ 'padding-top': '0px','padding-bottom':'0px' }">
            <n-descriptions label-placement="left" :column="1">
              <n-descriptions-item label="肝功能异常" :value="'否认'"/>
              <n-descriptions-item label="肾功能异常" :value="'是'"/>
              <n-descriptions-item label="过敏史" :value="'无'"/>
              <n-descriptions-item label="个人病史">
                高血压，糖尿病
              </n-descriptions-item>
              <n-descriptions-item label="家族病史">无</n-descriptions-item>
              <n-descriptions-item label="特殊状态">无</n-descriptions-item>
            </n-descriptions>
          </n-card>
        </n-gi>

        <!-- 右侧医生信息 -->
        <n-gi>
          <n-card title="医疗信息" :bordered="false" class="light-green-box"
                  :header-style="{ 'padding-top': '0px','padding-bottom':'0px' }">
            <n-descriptions label-placement="left" :column="1">
              <n-descriptions-item label="主治医生">刘梓妍</n-descriptions-item>
              <n-descriptions-item label="科室">内科</n-descriptions-item>
              <n-descriptions-item label="机构">
                九天医疗门诊部
              </n-descriptions-item>
              <n-descriptions-item label="建档时间">
                2024-03-20
              </n-descriptions-item>
            </n-descriptions>
          </n-card>
        </n-gi>
      </n-grid>
    </template>
  </JDrawer>
</template>

<script setup lang="tsx" name="DrawerPrescription">
import {ref, watch, reactive} from "vue";
import {useMessages, useLoading} from "@/hooks";
import {deepClone, isArray} from "@/utils";
import {GoodsCategoryType} from '@/enums'
/** 详情相关组件 */
// import NewDrugGoods from "@/views/StoreModule/GoodsManagement/components/NewDrugGoods.vue";
import JImage from "@/components/JImage/index.vue";

const {createMessageSuccess, createMessageError, createMessageWarning} = useMessages();

/* 表单参数初始化 */
const initParams = {
  id: null,//处方id
  nameValue: null,//姓名
  genderValue: null,//性别
  phoneValue: null, // 手机号
  identityCardValue: null,// 身份证
  sourceValue: null,//来源
  sourceCourseValue: null,//来源课程
  orderIdValue: null,//订单id
  selectDoctor: null,//选择医生
  diagnosticDescription: null,//诊断说明
  additionalRemarks: null,//补充说明
  prescriptionDocument: null,//处方文件
  creationTime: null,//创建时间
  prescriptionTime: null,//开方时间
  uploadtime: null,//上传文件时间
  dosageRpValue: null,//Rp取药数量
  rechargePicture: '',
  createBy: null,//创建者ID
  frontName: null,//疗法名称
  code: null,//编号
  productType: null, // 处方商品类别 1=药品、2=疗法
  productId: null, // 商品Id,
  drugs: [
    {
      name: '',
    }
  ]
};
const model = ref(deepClone(initParams));

/** 保存 loading */
const {loading: isLoading, startLoading, endLoading} = useLoading();

type prescriptionName = '处方筏' | '详情'
const prescriptionType: Record<string, prescriptionName> = {
  '处方筏': '处方筏',
  '详情': '详情'
}

const tableData = reactive([]);
//保存原本的病症信息
const originallyTableData = reactive([]);
const loadShow = ref(false);

/** 抽屉状态 */
const drawerVisible = ref(false);
const drawerProps = ref();
const orderCode = ref();
const usageState = ref(false);

/* 接收父组件传过来的参数 */
const acceptParams = (params) => {
  drawerVisible.value = true;
  drawerProps.value = params;
  orderCode.value = params.row.id;
  // 药品类型
  if (params.row.productType == GoodsCategoryType.DRUG) {
    usageState.value = true
  } else {
    usageState.value = false
  }
  getDetails()
};


/** 获取详情 */
const getDetails = async () => {
  loadShow.value = true
  try {
    const data = await drawerProps.value.getInfoApi(orderCode.value);
    model.value = data
  } catch (err) {
    createMessageError('获取详情失败' + err)
  } finally {
    loadShow.value = false
  }
}

/** 关闭抽屉 */
const closeDrawer = () => {
  model.value = deepClone(initParams);
  drawerVisible.value = false;
  tableData.length = 0
  originallyTableData.length = 0
};


const emits = defineEmits<{
  (e: "update:value", value);
}>();

watch(() => model.value.rechargePicture, (newVal) => {
  if (newVal[0] == '') model.value.rechargePicture = ''
})

defineExpose({
  acceptParams,
  closeDrawer,
  drawerVisible
});
</script>

<style scoped lang="less">
@import "@/styles/default.less";
@import "@/styles/defaultVar.less";

@details-inner-bg-height: calc(100vh - @main-header-height - @main-footer-height - @blank-page-padding * 2);

.details-inner-bg-height {
  height: @details-inner-bg-height;
}

:deep .n-drawer-body-content-wrapper {
  padding: 0 !important;
  overflow: hidden;
}

.goods-title {
  margin-bottom: 12px;
  margin-left: 12px;
  font-size: 14px;

  .title {
    margin-bottom: 4px;
    display: flex;
    align-items: center;
  }
}

.footer-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

:deep .n-drawer-body-content-wrapper {
  overflow: hidden;
}

:deep .n-card.n-card--bordered {
  border: 0px;
}

:deep .n-input__input-el {
  text-overflow: ellipsis;
}

/* 颜色方案 */
.light-green-box {
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 0px;
}

.green-box {
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 16px;
}

/* 文字样式增强 */
:deep(.n-card-header) {
  font-size: 18px;
  color: #1a7d3c;
  margin-bottom: 12px;
}

.n-statistic {
  margin-bottom: 12px;
}

.rp-container {
  padding: 20px;
}

.drug-item {
  margin-bottom: 20px;
}

.drug-info {
  margin-bottom: 10px;
}

.drug-name {
  font-weight: bold;
  margin-right: 10px;
}

.drug-spec {
  color: #666;
}

.usage-info {
  color: #333;
}
</style>
  
