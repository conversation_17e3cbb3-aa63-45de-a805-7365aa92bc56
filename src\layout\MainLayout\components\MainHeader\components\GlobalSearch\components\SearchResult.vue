<template>
  <n-scrollbar>
    <div class="content-wrapper">
      <template v-for="item in options" :key="item.name">
        <div
          class="item-container"
          :class="{ 'active-item': item.name === active, 'default-item':  item.name !== active}"
          @click="handleTo"
          @mouseenter="handleMouse(item)"
        >
          <span class="title">
            {{ item.meta?.title }}
          </span>
          <n-icon size="20">
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              width="20" 
              height="20" 
              viewBox="0 0 1024 1024"
            >
              <path 
                fill="currentColor" 
                d="M864 170h-60c-4.4 0-8 3.6-8 8v518H310v-73c0-6.7-7.8-10.5-13-6.3l-141.9 112a8 8 0 0 0 0 12.6l141.9 112c5.3 4.2 13 .4 13-6.3v-75h498c35.3 0 64-28.7 64-64V178c0-4.4-3.6-8-8-8"
              />
            </svg>
          </n-icon>
        </div>
      </template>
    </div>
  </n-scrollbar>
</template>

<script lang="ts" setup>
import { computed } from 'vue';

defineOptions({ name: 'SearchResult' });

interface Props {
  value: string;
  options: Array<any>;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'update:value', val: string): void;
  (e: 'enter'): void;
}

const emit = defineEmits<Emits>();

const active = computed({
  get() {
    return props.value;
  },
  set(val: string) {
    emit('update:value', val);
  }
});

/** 鼠标移入 */
async function handleMouse(item) {
  active.value = item.name;
}

function handleTo() {
  emit('enter');
}
</script>

<style lang="less" scoped>
@import "@/styles/defaultVar.less";
.content-wrapper {
  padding-bottom: 12px;
  .item-container {
    height: 56px;
    margin-top: 8px;
    padding-left: 14px;
    padding-right: 14px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title {
      flex: 1;
    }
  }
}
.active-item {
  background-color: @primary-color;
  color: #fff;
}
.default-item {
  background-color: #e5e7ed;
}

</style>
