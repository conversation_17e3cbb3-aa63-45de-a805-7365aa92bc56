<template>
  <JModal
    v-model:show="show"
    width="680"
    title="新建虚拟账号"
    @after-leave="closeModal"
		@positive-click="_save"
		:positiveButtonProps="{
			loading: isLoading
		}"
  >
    <n-form
      ref="formRef"
      :rules="rules"
      :model="model"
      label-width="auto"
      label-placement="left"
      require-mark-placement="right-hanging"
      :style="{
          width: '100%',
        }"
    >
      <n-grid :cols="8" :x-gap="24">
        <n-form-item-gi :span="8" label="昵称">
          <n-input v-model:value="model.name" placeholder="最多10个字" maxlength="10"/>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="头像" :path="model.headPortrait.length || model.headPortrait.length ? '' : 'headPortrait'" required>
          <div style="width: 100%;">
            <CustomizeUpload v-model:value="model.headPortrait" accept="image/*" :fileListSize="1" :max="1"/>
          </div>
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </JModal>
</template>

<script setup lang="ts" name="AddorEditVideo">
import { ref, watch } from "vue";
import { useMessages } from "@/hooks";
import {customerEntityAddVirAccount} from "@/services/api"
const initParams = {
  name:'',
  headPortrait:''
};
const model = ref({ ...initParams });
export interface AddCompanyModalProps {
  refreshTable?: () => void; // 刷新表格数据
}
/* 提示信息 */
const message = useMessages();
/* 模态框显隐状态 */
const show = ref(false);

/* 表单规则 */
const rules = {
  headPortrait:{
    required: true,
    trigger: ["blur", "change"],
    message: "请先上传头像",
    validator: ()=>{
      return model.value.headPortrait != '';
    }
  },
  name:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入昵称",
    validator: ()=>{
      return model.value.name != '';
    }
  },
};
const acceptParams = (params) => {
  parameter.value.refreshTable = params.refreshTable
  show.value = true

};
/* 表单实例 */
const formRef = ref(null);
/* 表单参数初始化 */
/* 清空表单 */
const formDataReset = () => {
  model.value = { ...initParams };
};
const parameter = ref<AddCompanyModalProps>({
});
/* 关闭弹窗 */
const closeModal = () => {
  formDataReset();
  
  isLoading.value = false;
  // 弹窗取消
  show.value = false;
};
/* 确认--保存 */
const isLoading = ref(false);
const _save = (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors && !isLoading.value) {
      try {
        isLoading.value = true;
        await customerEntityAddVirAccount({
            "data": {
              "nickname":model.value.name,
              "img": Array.isArray(model.value.headPortrait)?model.value.headPortrait[0]:model.value.headPortrait,
            }
          }).then(e=>{
          message.createMessageSuccess(`添加虚拟账号成功`);
          // 刷新表格数据
          parameter.value.refreshTable();
          closeModal()
        });
      } catch (e) {
        message.createMessageError(`添加虚拟账号失败： ${e}`);
        isLoading.value = false;
      }
    }
  });
};
watch(()=>model.value.headPortrait,(newVal)=>{
  if(newVal[0] == '') model.value.headPortrait = ''
})
defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less">
::v-deep .n-form-item-label{
  width: 70px !important;
}
</style>
