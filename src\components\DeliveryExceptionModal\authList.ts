import { hasAuth } from "@/utils/auth/authUtils";

/** 发货异常订单导出 */
export const hasDeliveryExceptionExportAuth = function(){
    // return hasAuth(DeliveryExceptionAuth.DeliveryExceptionExport.key);
    return true
}()

/** 发货异常订单标记已处理 */
export const hasDeliveryExceptionMarkedAsProcessedAuth = function(){
    // return hasAuth(DeliveryExceptionAuth.DeliveryExceptionMarkedAsProcessed.key);
    return true
}()

/** 发货异常订单重新推送 */
export const hasDeliveryExceptionResendAuth = function(){
    // return hasAuth(DeliveryExceptionAuth.DeliveryExceptionResend.key);
    return true
}()
