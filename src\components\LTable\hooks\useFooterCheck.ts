import {computed, nextTick, ref, unref, watch, type ComputedRef, type Ref} from "vue";

type useFooterCheckProps={
    checkedRowsKeysRef: Ref<Array<string>>;
    allIdsListRef: ComputedRef<string[]>;
    checkedHandle:(keys:Array<string | number>)=>void
}
type useFooterReturnProps={
    isShowFooterWrapperRef: ComputedRef<boolean>;
    footerSelectorWrapperWidthRef: Ref<number>;
    isFooterCheckBoxIndeterminateRef: ComputedRef<boolean>;
    footerCheckBoxValueRef: Ref<boolean>;
    handlerFooterCheckboxChecked: (checked: boolean) => void;
}

export function useFooterCheck(props: useFooterCheckProps):useFooterReturnProps {
    const { checkedRowsKeysRef,allIdsListRef,checkedHandle } = props;
    const isShowFooterWrapperRef = computed(() => {
        return checkedRowsKeysRef.value.length ? true : false;
    });

    const footerCheckBoxValueRef = ref(false);
    const isFooterCheckBoxIndeterminateRef = computed(() => {
        footerCheckBoxValueRef.value = false;
        if (checkedRowsKeysRef.value.length == 0){
            return false;
        }
        else if (checkedRowsKeysRef.value.length != allIdsListRef.value.length){
          return true;
        }
        else {
          let flag = false;
          for (let i = 0; i < allIdsListRef.value.length; i++) {
            if (!checkedRowsKeysRef.value.includes(allIdsListRef.value[i])) {
              flag = true;
              break;
            }
          }
          if (!flag) footerCheckBoxValueRef.value = true;
          return flag;
        }
    });

    const footerSelectorWrapperWidthRef = ref(55);   
    watch(isShowFooterWrapperRef, async(newVal) => {
        if (newVal) {
          let width = 55;
          await nextTick();
          const tableSelection = document.querySelectorAll(".n-data-table-th--selection");
          if (tableSelection.length) {
            for (let i = 0; i < tableSelection.length; ++i) {
              const _width = tableSelection[i].clientWidth;
              if (_width) {
                width = _width;
                break;
              }
            }
          }
          footerSelectorWrapperWidthRef.value = width;
        }
    });
    function handlerFooterCheckboxChecked(checked:boolean) {
      if (checked) {
        checkedRowsKeysRef.value = [...unref(allIdsListRef)];
        
      } else {
        checkedRowsKeysRef.value = [];
      }
      checkedHandle(checkedRowsKeysRef.value)
      footerCheckBoxValueRef.value = checked;
    }
    return {
      isShowFooterWrapperRef,
      footerSelectorWrapperWidthRef,
      isFooterCheckBoxIndeterminateRef,
      footerCheckBoxValueRef,
      handlerFooterCheckboxChecked
    }
}