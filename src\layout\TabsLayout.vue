<script setup lang="ts">
import { toRef } from "vue";
export type TabsDataOption = {
  label: string;
  key: string | number;
  directive?: "if" | "show";
  number?: number | string; // tab 动态数量,
  isShowNumber?: boolean // 是否该tab不显示
}
interface TabsLayoutProps {
  tabsData: Array<TabsDataOption>;
  height?: string;
  paneClass?: string;
  onlyTabs?: boolean;
  isShowNumber?: boolean;
}
const props = defineProps<TabsLayoutProps>();
const tabsDataRef = toRef(props, "tabsData");
const heightRef = toRef(props, "height");
const paneClassRef = toRef(props, "paneClass");
const onlyTabsRef = toRef(props, "onlyTabs");
const isShowNumberRef = toRef(props, "isShowNumber");
</script>

<template>
  <div :class="heightRef ? 'default-bg' : 'default-bg inner-page-height'" :style="`height:${heightRef};`">
    <n-tabs
      size="small"
      v-bind="$attrs"
      type="line"
      animated
      tab-style="padding:8px 0px;"
      :tabs-padding="12"
      :pane-class="`tab-pane-default ${paneClassRef}`"
    >
      <template v-if="onlyTabsRef">
        <n-tab v-for="tab in tabsDataRef" :key="tab.key" :name="tab.key">
          {{ tab.label }} {{ isShowNumberRef && (tab.isShowNumber !== false) ? `(${tab.number})` : ''}}
        </n-tab>
      </template>
      <n-tab-pane
        v-else
        v-for="tab in tabsDataRef"
        :name="tab.key"
        :key="tab.key"
        :tab="`${tab.label}${isShowNumberRef && (tab.isShowNumber !== false) ? `(${tab.number})` : ''}`"
        :display-directive="tab.directive ? tab.directive : 'show'"
      >
        <slot :name="tab.key"></slot>
      </n-tab-pane>
    </n-tabs>
    <div v-if="onlyTabsRef" :class="`tab-pane-default ${paneClassRef}`">
      <slot></slot>
    </div>
  </div>
</template>

<style lang="less" scoped>
@import "@/styles/default.less";
@import "@/styles/defaultVar.less";
// @import "@/views/Marketing/Fission/markingStyle.less";
.default-bg {
  background-color: #ffffff;
  width: 100%;
  padding-top: 6px;
  box-sizing: border-box;
}

.tab-pane-default {
  padding-top: 0px;
  height: @tab-pane-inner-bg-height;
  // height: calc( @inner-bg-height - 42);
  // height:815px;
}
</style>
