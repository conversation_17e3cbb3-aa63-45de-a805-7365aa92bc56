<script setup lang="ts">
import TabsLayout from "@/layout/TabsLayout.vue";
import { ref } from "vue";
import ShopReturnLogisticsTab  from './components/ShopReturnLogisticsTab.vue';
import { ShopReturnLogisticsEnum } from '@/enums';

const tabNameRef = ref<string>(ShopReturnLogisticsEnum.WaitingForReview);

const tableDataUpdate = ref()

const tabsData = ref([
  {
    label: "待我审核",
    key: ShopReturnLogisticsEnum.WaitingForReview,
  },
  {
    label: "进行中",
    key: ShopReturnLogisticsEnum.InProgress,
  },
  {
    label: "已完成",
    key: ShopReturnLogisticsEnum.Completed,
  },
  {
    label: "审核不通过",
    key: ShopReturnLogisticsEnum.NotPassed,
  },
]);

</script>

<template >
  <n-layout>
    <n-layout-content id="ShopReturnLogisticsTab">
      <TabsLayout  v-model:value="tabNameRef" :tabsData="(tabsData as any)"  :onlyTabs="true" class="tabsLayout">
        <ShopReturnLogisticsTab ref="tableDataUpdate" :tabNameRef="tabNameRef" />
      </TabsLayout>
    </n-layout-content>
  </n-layout>
</template>

<style lang="less" scoped>
// @import "@/styles/default.less";
</style>
