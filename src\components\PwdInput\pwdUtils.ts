import { isString } from '@/utils';
import { PwdLevelEnum } from './types';

function judgeContinuous(str:string = '',count:number=5)  {
    const isIncreasing = (a: string, b: string) => a.charCodeAt(0) - b.charCodeAt(0) == -1;
    const isDecreasing = (a: string, b: string) => a.charCodeAt(0) - b.charCodeAt(0) == 1 ;
    let incCount = 1;
    let decCount = 1; 
    for (let i = 1; i < str.length; i++) {
      if (isIncreasing(str[i - 1], str[i])) {
        incCount++;
        decCount = 0; // 重置递减计数
      } 
      else if (isDecreasing(str[i - 1], str[i])) {
        decCount++;
        incCount = 1; // 重置递增计数
      } 
      else {
        incCount = 1;
        decCount = 1;
      }
      if (incCount >= (count) || decCount >= (count)) {
        return true;
      }
    }
    return false;
}

function judgeSame(str:string = '',count:number=5): boolean {
    let x = 1; // 用于计数连续相同字符的数量
    // 遍历字符串从第二个字符开始
    for (let i = 1; i < str.length; i++) {
      if (str[i] === str[i - 1]) {
        x++;
      } else {
        x = 1;
      }
      if (x >= (count)) {
        return true;
      }
    }
    return false;
  }

/**检查密码强度 */
export function checkPwdStrength(pwd:string):PwdLevelEnum{
    const lowestCount = 5
    if(isString(pwd)){
        if(pwd.length == 0){
            return PwdLevelEnum.IDLE
        }
        const judgeCharReg = /^([a-zA-Z]+[0-9]+)|([a-zA-Z]+[0-9]+)|([0-9]+[a-zA-Z]+)|([0-9]+[a-zA-Z]+)|([a-zA-Z]+[0-9]+)|([0-9]+[a-zA-Z]+)$/
        const speicalCharacterReg = /[^a-zA-Z0-9]/;
        if(pwd.length <= lowestCount || judgeContinuous(pwd,4) || judgeSame(pwd,4) || !judgeCharReg.test(pwd)){
            // debugger
            return PwdLevelEnum.LOW
        }
        else if(pwd.length >lowestCount && pwd.length < 8 && !speicalCharacterReg.test(pwd)){
            return PwdLevelEnum.MID
        }
        else if(pwd.length >= 8 || speicalCharacterReg.test(pwd)){
            return PwdLevelEnum.HIGH
        }
    }
    else{
        return PwdLevelEnum.IDLE
    }
}

/**获取随机密码串 */
export function getRandomPwd(count:number=8):string{
    // 定义数字、字母、特殊字符的字符集
    const numbers = '0123456789';
    const lowerCase = 'abcdefghijklmnopqrstuvwxyz';
    const upperCase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const specialChars = '!@#$%^&*()_+[]{}|;:,.<>?';
    // 创建一个包含所有字符的字符池
    const allChars = numbers + lowerCase + upperCase + specialChars;
    // 保证每种类型的字符至少出现一次
    let result = '';
    result += numbers.charAt(Math.floor(Math.random() * numbers.length));  
    result += lowerCase.charAt(Math.floor(Math.random() * lowerCase.length)); 
    result += upperCase.charAt(Math.floor(Math.random() * upperCase.length)); 
    result += specialChars.charAt(Math.floor(Math.random() * specialChars.length)); 

    // 生成剩余的随机字符
    for (let i = result.length; i < count; i++) {
      result += allChars.charAt(Math.floor(Math.random() * allChars.length));
    }

  // 将结果字符串打乱，确保字符顺序是随机的
  return result.split('').sort(() => Math.random() - 0.5).join('');
}