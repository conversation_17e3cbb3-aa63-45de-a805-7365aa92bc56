<template>
    <FormLayout
      class="inner-page-height"
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :pagination="paginationRef"
      @paginationChange="paginationChange"
      :isNeedCollapse="false"
    >
      <!-- 表单 -->
      <template #searchForm>
        <n-form
          ref="formRef"
          label-placement="left"
          label-width="auto"
          :show-feedback="false"
          require-mark-placement="right-hanging"
          size="small"
          :style="{ width: '100%', marginLeft: '12px' }"
        >
          <!-- 商品名称 -->
          <n-form-item :span="12" label="商品名称">
            <JSearchInput
              v-model:value="model.name"
              placeholder="请输入商品名称"
              @search="handlerSearch"
            />
          </n-form-item>
          <!-- 商品编号 -->
<!--          <n-form-item :span="12" label="商品编号">-->
<!--            <JSearchInput-->
<!--              v-model:value="model.code"-->
<!--              placeholder="请输入商品编号"-->
<!--              @search="handlerSearch"-->
<!--            />-->
<!--          </n-form-item>-->
          <!-- 药物类型 -->
          <n-form-item v-if="props.storeType === SystemStoreType.PHARMACEUTICALMALL" :span="12" label="药物类型">
            <n-select
              style="width: 160px;"
              v-model:value="model.isPres" 
              :options="drugTypeOptions"
              clearable
             />
          </n-form-item>
          <!-- 上架状态 -->
          <n-form-item :span="12" label="上架状态">
            <n-select
              style="width: 160px;"
              v-model:value="model.isPublish" 
              :options="shelfStatusOptions"
              clearable
            />
          </n-form-item>
          <!-- 商品分类 -->
          <n-form-item :span="12" label="商品分类">
            <n-select 
              v-model:value="model.isCate"
              style="width: 160px;" 
              :options="goodsCategoryOptions" 
              clearable 
            />
          </n-form-item>
          <n-form-item :span="12" label="spu编码">
            <n-input
              v-model:value="model.spu"
              placeholder="请输入商品spu"
              :maxlength="40"
              clearable
              style="width: 160px;"
              :allow-input="(value)=>!value || /^[a-zA-Z0-9]+$/.test(value)"
              @keyup.enter.native="handlerSearch"
            />
          </n-form-item>
          <!-- 供应商名称 -->
          <n-form-item :span="12" label="供应商名称">
            <JSupplierSelector
              v-model:value="model.supplierId"
              style="width: 160px;"
              clearable
              placeholder="请选择供应商"
            />
          </n-form-item>
        </n-form>
      </template>
      <template #tableHeaderBtn>
        <n-button @click="refresh" class="store-button">刷 新</n-button>
        <!-- 新增商品 -->
        <template v-if="props.storeType === SystemStoreType.PHARMACEUTICALMALL && hasAddGoodsAuth">
          <n-popover trigger="hover" placement="bottom" style="padding: 0;">
            <template #trigger>
              <JAddButton type="primary">新增商品</JAddButton>
            </template>
            <n-button-group vertical style="width: 120px;">
              <n-button quaternary @click="openEditOrAddDrugGoods('add')">药品</n-button>
              <n-button quaternary @click="openEditOrAddTherapyGoods('add')">疗法</n-button>
              <n-button quaternary @click="openEditOrAddGeneralGoods('add')">普通商品</n-button>
            </n-button-group>
          </n-popover>
        </template>
        <template v-if="props.storeType === SystemStoreType.GENERALMALL && hasAddGoodsAuth">
          <JAddButton type="primary" @click="openEditOrAddGeneralGoods('add')">新增商品</JAddButton>
        </template>
      </template>
      <!-- 表格底部按钮 -->
		  <template #tableFooterBtn="scope">
			  <!-- 批量上架 -->
			  <n-popconfirm v-if="hasBulkGoodsAuth" @positive-click="handleBatchUnmountGoods(scope.selectedListIds, scope.selectedList, '1')" :positive-button-props="{
          loading: isBatchLoading
        }">
			  	<template #trigger>
			  		<n-button ghost type="primary" size="small">批量上架</n-button>
			  	</template>
			  	此操作将上架选中的商品，是否继续？
			  </n-popconfirm>
			  <!-- 批量下架 -->
			  <n-popconfirm v-if="hasBulkGoodsAuth" @positive-click="handleBatchUnmountGoods(scope.selectedListIds, scope.selectedList, '0')" :positive-button-props="{
          loading: isBatchLoading
        }">
			  	<template #trigger>
			  		<n-button ghost type="error" size="small">批量下架</n-button>
			  	</template>
			  	此操作将下架选中的商品，是否继续？
			  </n-popconfirm>
      </template>
    </FormLayout>
    <!-- 新建药品商品  to="#GoodsManagement" 抽屉可以全屏 -->
    <NewDrugGoods 
      ref="newDrugGoodsRef"
      :isPointEnable="isPointEnable"
      :isT9Sync="isT9Sync"
      :refreshTable="refresh"
    />
    <!-- 新建疗法商品 to="#GoodsManagement" 抽屉可以全屏 -->
    <NewTherapyGoods 
      ref="newTherapyGoodsRef"
      :isPointEnable="isPointEnable"
      :isT9Sync="isT9Sync"
      :refreshTable="refresh"
     />
    <!-- 新建普通商品 to="#GoodsManagement" 抽屉可以全屏 -->
    <NewGeneralGoods 
      ref="newGeneralGoodsRef"
      :isPointEnable="isPointEnable"
      :isT9Sync="isT9Sync"
      :refreshTable="refresh"
     />
  </template>
  
  <script lang="tsx" setup name="ProductList">
  import { ref, watch, toRefs, onMounted, computed } from "vue";
  import { useTableDefault } from "@/hooks/useTableDefault";
  import { 
    getGoodsPage,
    getSoldOutProductPage,
    batchUnmountGoods, 
    copyDrugGoodsById, 
    copyTherapyDrugGoodsById, 
    copyGeneralGoodsById,
   } from "@/services/api";
  import { useMessages } from '@/hooks';
  import { drugTypeOptions, shelfStatusOptions, shelfStatusLabels, goodsCategoryOptions } from "@/constants";
  import type { GoodsType } from "@/enums";
  import { GoodsCategoryType, StoreModelType, SystemStoreType, DrugTypeEnum } from "@/enums";
  import { useGetPointsEnabled } from "../hooks";
  import { isNullOrUnDef } from "@/utils";
  import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
  /** 权限 */
  import { hasAddGoodsAuth, hasBulkGoodsAuth, hasCopyGoodsAuth, hasEditGoodsAuth } from "../authList";
  /** 相关组件 */
  import FormLayout from "@/layout/FormLayout.vue";
  import TablePreview from "@/components/TablePreview/index.vue";
  import NewDrugGoods from "./NewDrugGoods.vue";
  import NewTherapyGoods from "./NewTherapyGoods.vue";
  import NewGeneralGoods from "./NewGeneralGoods.vue";
  import JSupplierSelector from "@/components/JSelect/JSupplierSelector.vue";
  import { CHINESE_HERBAL_MEDICINE_PARAMS } from "@/views/StoreModule/GoodsManagement/types";

  const systemStore = useSystemStoreWithoutSetup();
  /** 是否开启T9商城订单同步 */
  const isT9Sync = computed(() => systemStore._globalConfig['jtintegration']);
  
  const { createMessageSuccess, createMessageError } = useMessages();

  const { isPointEnable, getPointsEnabled } = useGetPointsEnabled();
  
  /** props */
  const props = defineProps<{
    cateId: string | null,
    name?: string, // 商品名
    type: GoodsType | null,
    isPublish?: '0' | '1', // 商品上架状态
    isSellout?: '0' | '1', // 是否售罄 0: 未售罄 1: 已售罄
    storeType?: StoreModelType; // 商城类型
  }>();

  const { isSellout: isSelloutRef } = toRefs(props);

  /** emits */
  const emits = defineEmits<{
    (e : "successful"): void;
  }>();
  
  /** 表格hook */
  const {
    isAddLoading,
    isEditLoading,
    isLoading,
    tableData,
    paginationRef,
    pageTableData,
    deleteTableData,
    editTableData,
    addTableData,
    refreshTableData,
    paginationChange,
  } = useTableDefault({
    pageDataRequest: isSelloutRef.value == '1' ? getSoldOutProductPage : getGoodsPage,
  });
  
  /* 表格列表项 */
  const tableColumns = [
    {
      title: "图片",
      key: "img",
      align: "left",
      fixed: "left",
      width: 120,
      render: (row) => {
        if (row?.productImgDTOList?.length > 0) {
          let paths = row?.productImgDTOList.map(item => item.path);
          return <TablePreview src={paths}></TablePreview>;
        } 
        return '-';
      },
    },
    {
      title: "商品",
      key: "name",
      width: 260,
      align: "left",
      render: (row) => {
        // 普通商品
        if (row.type == GoodsCategoryType.GENERAL) {
          let title = `${row.frontName ?? ''}`;
          return <table-tooltip row={row} nameKey="name" title={title} idKey="id" />;
        }
        // 疗法
        if (row.type == GoodsCategoryType.THERAPY) {
          let title = `${row.frontName ?? ''}`;
          return <table-tooltip row={row} nameKey="name" title={title} idKey="id" />;
        }
        // 药品
        if (row.type == GoodsCategoryType.DRUG) {
          let name = row.productSpecDTOList?.[0]?.name ?? '';
          let title = `[${row.frontName ?? ''}] ${row.name ?? ''} ${name}`;
          return <table-tooltip row={row} nameKey="name" title={title} idKey="id" />;
        }
      }
    },
    {
      title: "供应商名称",
      key: "supplierName",
      width: 150,
      align: "left",
      render: (row) => {
        return <span>{row.supplierName ?? '-'}</span>;
      }
    },
    {
      title: "价格",
      key: "price",
      width: 180,
      align: "left",
      render: row => {
        let price = row.productSpecDTOList?.[0]?.price ?? 0; // 确保 price 是数字
        let formattedPrice = formatPrice(price);
        if (row.type === GoodsCategoryType.GENERAL && row?.productSpecDTOList?.length > 1) {
            // 如果是普通商品并且有多个规格，找到最低价格
            const minPrice = row?.productSpecDTOList?.reduce((min, item) => Math.min(min, item.price), price) ?? price;
            formattedPrice = `${formatPrice(minPrice)}起`;
        }
        return <span>{formattedPrice}</span>;
      }
    },
    {
      title:"已售",
      key: "soldQty",
      width: 150,
      align: "left",
      render: row => {
        const allSoldQty = row?.productSpecDTOList?.reduce((total, item) => total + (item.soldQty ?? 0), 0) ?? 0;
        return <span>{allSoldQty}</span>;
      }
    },
  
    {
      title: "库存",
      key: "availStocks",
      width: 150,
      align: "left",
      render: row => {
        // 普通商品
        if (row.type == GoodsCategoryType.GENERAL) {
          // 计算 availStocks 的总值
          const totalAvailStocks = row?.productSpecDTOList?.reduce((total, item) => total + (item.availStocks ?? 0), 0) ?? 0;
          return <span>{totalAvailStocks}</span>;
        }
        let availStocks = row.productSpecDTOList?.[0]?.availStocks ?? 0; // 确保 price 是数字
        return <span>{availStocks}</span>;
      }
    },
    {
      title: "上架",
      key: "isPublish",
      width: 80,
      align: "left",
      render: (row) => {
        return (
          <n-tag 
            bordered={false} 
            size="small" 
            type={row.isPublish === 1 ? "success" : "error"}
          >
            {shelfStatusLabels[row.isPublish]}
          </n-tag>
        );
      },
    },
    // 1.0.8 删除
    // {
    //   title: "所属分类",
    //   key: "cateName",
    //   width: 150,
    //   align: "left",
    // },
    {
      title: "操作",
      key: "action",
      width: 120,
      fixed: "right",
      align: "left",
      render: row => {
        return (
          <n-space>
            {/* 编辑 */}
            {hasEditGoodsAuth ? <n-button text type="primary" onClick={() => openEdit(row)}>
              编辑
            </n-button> : null}
            {/* 复制创建 */}
            {hasCopyGoodsAuth ? <n-popconfirm
              onPositiveClick={() => handleCopyGoodsById(row)}
              positive-button-props={{
                  loading: isCopyLoading.value,
              }}
            >
              {{
                  default: () => {
                      return (
                          <span>{`确认复制创建《${row.name}》商品吗？`}</span>
                      );
                  },
                  trigger: () => {
                      return (
                          <n-button text type="primary">
                              复制创建
                          </n-button>
                      );
                  },
              }}
            </n-popconfirm> : null}
          </n-space>
        );
      },
    },
  ];

  /** 定义一个函数来格式化价格 */
  const formatPrice = (price) => `￥${(price / 100).toFixed(2)}`;

  /** 打开商品编辑 */
  const openEdit = (row: Partial<ApiStoreModule.Goods>) => {
    if (row.type === GoodsCategoryType.DRUG) openEditOrAddDrugGoods('edit', row);
    if (row.type === GoodsCategoryType.THERAPY) openEditOrAddTherapyGoods('edit', row);
    if (row.type === GoodsCategoryType.GENERAL) openEditOrAddGeneralGoods('edit', row);
  };
  
  /** 打开药品商品新增或编辑 */
  const newDrugGoodsRef = ref<InstanceType<typeof NewDrugGoods> | null>(null);
  const openEditOrAddDrugGoods = (type: 'add' | 'edit' | 'view', row: Partial<ApiStoreModule.Goods> = {}) => {
    newDrugGoodsRef.value?.acceptParams({
      cateId: props.cateId,
      type,
      row,
      productTypes: props.type
    });
  };

  /** 打开疗法商品新增或编辑 */
  const newTherapyGoodsRef = ref<InstanceType<typeof NewTherapyGoods> | null>(null);
  const openEditOrAddTherapyGoods = (type: 'add' | 'edit' | 'view', row: Partial<ApiStoreModule.Goods> = {}) => {
    newTherapyGoodsRef.value?.acceptParams({
      cateId: props.cateId,
      type,
      row,
      productTypes: props.type
    });
  };

  /** 打开普通商品新增或编辑 */
  const newGeneralGoodsRef = ref<InstanceType<typeof NewGeneralGoods> | null>(null);
  const openEditOrAddGeneralGoods = (type: 'add' | 'edit' | 'view', row: Partial<ApiStoreModule.Goods> = {}) => {
    newGeneralGoodsRef.value?.acceptParams({
      cateId: props.cateId,
      type,
      row,
      productTypes: props.type
    });
  };
  
  /** 参数 */
  const model = ref({
    name: props.name ? props.name : '',
    code: '',
    isPres: null,
    isPublish: props.isPublish ? Number(props.isPublish) : null,
    isSellout: props.isSellout ? Number(props.isSellout) : null,
    isCate: null,
    spu: '',
    supplierId: null,
  });
  
  /** 获取参数 */
  const getParams = () => {
    const { name, isPres, isPublish, code, isSellout, isCate, spu, supplierId } = model.value;
    console.log(model.value,'model.value')
    //中草药类型不需要isPres,新增medicineType搜索
    let isChineseHerbalMedicineObj={isPres}
    if(isPres===DrugTypeEnum.ChineseHerbalMedicine){
      isChineseHerbalMedicineObj={
        medicineType:CHINESE_HERBAL_MEDICINE_PARAMS
      }
    }
    return {
      ...isChineseHerbalMedicineObj,
      name,
      isPublish,
      code,
      type: props.type ?? null,
      cateId: props.cateId ?? null,
      isSellout,
      isCate: isNullOrUnDef(isCate) ? null : isCate ? true : false,
      spu,
      supplierId,
    };
  };


  /** 复制创建商品 */
  const isCopyLoading =  ref(false);
  const handleCopyGoodsById = async (row: Partial<ApiStoreModule.Goods>) => {
    try {
      isCopyLoading.value = true;
      switch (row.type) {
        case GoodsCategoryType.DRUG:
          await copyDrugGoodsById(row?.id);
          break;
        case GoodsCategoryType.THERAPY:
          await copyTherapyDrugGoodsById(row?.id);
          break;
        case GoodsCategoryType.GENERAL:
          await copyGeneralGoodsById(row?.id);
          break;
        default:
        break;
      }
      createMessageSuccess('复制创建成功');
      refresh();
    } catch (error) {
      createMessageError('复制创建失败：' + error)
    } finally {
      isCopyLoading.value = false;
    }
  };

  /** 批量上下架 */
  const isBatchLoading = ref(false);
  const handleBatchUnmountGoods = async (rowListIds: Array<string>, rowList: Array<any>,  isPublish: '0' | '1') => {
    try {
      isBatchLoading.value = true;
      let cateListIds = rowList.map(item => item?.cateId).reduce((acc, current) => {
        if (!acc.includes(current)) {
          acc.push(current);
        }
        return acc;
      }, []);
      let _params = {
        productIds: rowList.map(item => item?.id).join(',') || '',
        isPublish,
        cateIds: cateListIds.join(','),
      };
      await batchUnmountGoods(_params);
      createMessageSuccess('操作成功');
      refresh();
    } catch (error) {
      createMessageError('操作成功失败：' + error);
    } finally {
      isBatchLoading.value = false;
    }
  };
  
  /** 搜索 */
  const handlerSearch = () => {
    tableSearch();
  };
  
  /** 表格刷新 */
  function refresh(){
    tableSearch();
  };
  
  /* 表格搜索 */
  const tableSearch = async () => {
    await pageTableData(getParams(), paginationRef.value);
    emits("successful");
  };

  /** 组件挂载 */
  onMounted(() => {
    getPointsEnabled();
  })
  
  /** 监听 */
  watch(() => [props.cateId, props.type], (newVal, oldVal) => {
    // 关闭抽屉
    newDrugGoodsRef.value?.close();
    newTherapyGoodsRef.value?.close();
    tableSearch();
  }, { immediate: true });

  /** 监听 */
  watch(() => [model.value.isPres, model.value.isPublish, model.value.isCate, model.value.supplierId], (newVal) => {
    if (newVal) {
      tableSearch();
    }
  });
  </script>
  
  <style lang="less" scoped>
  </style>
  