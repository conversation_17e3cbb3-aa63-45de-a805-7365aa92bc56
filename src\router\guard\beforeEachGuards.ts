import NProgress from "nprogress";
import { useUserStoreWithoutSetup } from "@/stores/modules/user";
import { useMessages } from "@/hooks";
import { isDoctorEnv } from "@/utils/envUtils";
NProgress.configure({ showSpinner: false });
const startProgress = (to, from, next) => {
  if (!NProgress.isStarted()) {
    NProgress.start();
  }
  next();
};

const clearAllMessage = (to, from, next) => {
  const { destoryMessage } = useMessages();
  destoryMessage();
  next();
};

const unResiterHandler = (to, from, next) => {
  if (!to.matched.length) {
    const userStore = useUserStoreWithoutSetup();
    if (userStore.token && userStore.userInfo) {
      next("/404");
    } else {
        if(isDoctorEnv()){
          next("/doctor/login")
        }
        else{
          next("/login")
        }
    }
  } else next();
};

export const beforeEachGuardsList = [startProgress, unResiterHandler, clearAllMessage];
