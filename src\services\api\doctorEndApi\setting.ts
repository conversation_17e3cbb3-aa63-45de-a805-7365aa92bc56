import {defHttp} from '@/services';

export const enum DoctorBaseSettingApi {
    /** 基础设置 */
    getBaseSetting = '/doctorEntity/selectDoctorWelcomeMsg',
    /** 编辑基础设置 */
    editBaseSetting = '/doctorEntity/updateDoctorWelcomeMsg',
}

/** 基础设置 */
export function getDoctorBaseSetting(params) {
    return defHttp.get({
        url: DoctorBaseSettingApi.getBaseSetting,
        params,
        requestConfig:{
            isQueryParams:true
        }
    })
}

/** 编辑基础设置 */
export function editDoctorBaseSetting(params) {
    return defHttp.post({
        url: DoctorBaseSettingApi.editBaseSetting,
        params
    })
}
