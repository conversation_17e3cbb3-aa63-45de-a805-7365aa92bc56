<template>
  <div class="wrapper inner-page-height">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :pagination="paginationRef"
      @paginationChange="paginationChange"
      :isNeedCollapse="false"
      :isTableSelection="false"
      :isDisplayIndex="false"
    >
      <!-- 表单 -->
      <template #searchForm>
        <n-form
          ref="formRef"
          label-placement="left"
          label-width="auto"
          :show-feedback="false"
          require-mark-placement="right-hanging"
          size="small"
          :style="{ width: '100%' }"
        >
          <n-form-item :span="12" label="日期" path="">
            <n-date-picker v-model:value="model.statDate" type="date" />
          </n-form-item>
          <n-form-item :span="12" label="直播间名称">
            <JLivesSelect width="100%" v-model:value="model.liveId" />
          </n-form-item>
          <n-form-item :span="12" label="所属店铺">
            <JStoreSelect width="100%" v-model:value="model.storeId" />
          </n-form-item>
          <n-form-item :span="12" label="观看时长">
            <n-input
              v-model:value="model.duration"
              @input="value => (model.duration = value.replace(/[^0-9]/g, '').replace(/^0+/, '') || null)"
              placeholder="请输入分钟查询"
            />
          </n-form-item>
          <n-form-item :span="12" label="会员ID">
            <n-input
              v-model:value="model.memberShortId"
              @input="value => (model.memberShortId = value.replace(/[^0-9]/g, '').replace(/^0+/, '') || null)"
            />
          </n-form-item>
          <n-form-item :span="12" label="归属店员ID">
            <n-input
              v-model:value="model.staffShortId"
              @input="value => (model.staffShortId = value.replace(/[^0-9]/g, '').replace(/^0+/, '') || null)"
            />
          </n-form-item>
        </n-form>
      </template>

      <template #tableHeaderBtn>
        <n-button type="primary" class="store-button" @click="handlerSearch(false)">搜索</n-button>
        <n-button class="store-button" @click="handlerSearch(true)">重置</n-button>
        <n-button
          v-if="hasViewDataExportAuth"
          type="primary"
          class="store-button"
          :loading="exportLoading"
          @click="handlerExport"
        >
          导出
        </n-button>
      </template>
    </FormLayout>
    <Details ref="detailsRef" />
  </div>
</template>

<script lang="tsx" setup name="DoctorManagement">
import { onMounted, ref, watch } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { getViewDataPage, viewDataExport } from "@/services/api";
import { useMessages, useLoading } from "@/hooks";
import Details from "./components/Details.vue";
import { hasViewDataDetailsAuth, hasViewDataExportAuth } from "./authList";
import moment from "moment";
import { useRouter } from "vue-router";

const { createMessageSuccess, createMessageError } = useMessages();

/** 表格hook */
const { isLoading, tableData, paginationRef, pageTableData, paginationChange } = useTableDefault({
  pageDataRequest: getViewDataPage,
});

const router = useRouter();
/* 表格列表项 */
const tableColumns = ref([
  {
    title: "会员ID",
    key: "memberShortId",
    align: "left",
    fixed: "left",
    width: 100,
  },
  {
    title: "归属店员ID",
    key: "staffShortId",
    width: 150,
    align: "left",
  },
  {
    title: "会员昵称",
    key: "nickname",
    width: 180,
    align: "left",
  },
  {
    title: "日期",
    key: "statDate",
    width: 150,
    align: "left",
  },
  {
    title: "观看时长",
    key: "watchSeconds",
    width: 150,
    align: "left",
    render: row => {
      return <div>{Math.floor(row.watchSeconds / 60)}分钟</div>;
    },
  },
  {
    title: "直播间名称",
    key: "liveName",
    width: 150,
    align: "left",
  },
  {
    title: "所属店铺",
    key: "storeName",
    width: 150,
    align: "left",
  },
  {
    title: "操作",
    key: "action",
    width: 200,
    fixed: "right",
    align: "left",
    render: row => {
      return (
        <n-space align="center" justify="center">
          {hasViewDataDetailsAuth ? (
            <n-button text type="primary" onClick={() => handleViewDataDetails(row)}>
              详情
            </n-button>
          ) : null}
        </n-space>
      );
    },
  },
]);
/** 获取参数 */
const getParams = () => {
  const { statDate, liveId, storeId, duration, memberShortId, staffShortId } = model.value;
  const watchSeconds = duration ? duration * 60 : null;
  const date = statDate ? moment(statDate).format("YYYY-MM-DD") : null;
  return {
    statDate: date,
    watchSeconds,
    liveId,
    storeId,
    memberShortId,
    staffShortId,
  };
};

const detailsRef = ref();

const initParams = {
  statDate: null,
  liveId: null,
  storeId: null,
  duration: null,
  memberShortId: null,
  staffShortId: null,
};
const model = ref({ ...initParams });
/** 搜索 */
const handlerSearch = (isReset?: boolean) => {
  if (isReset) {
    model.value = { ...initParams };
  }
  tableSearch();
};

/** 表格刷新 */
function refresh() {
  tableSearch();
}
const handleViewDataDetails = (row: any) => {
  detailsRef.value.acceptParams(row.id);
};

/* 表格搜索 */
const tableSearch = () => {
  pageTableData(getParams(), paginationRef.value);
};
const { loading: exportLoading, startLoading, endLoading } = useLoading();
const handlerExport = async () => {
  try {
    startLoading();
    await viewDataExport({
      data: { ...getParams() },
      pageVO: {
        current: paginationRef.value.current,
        size: paginationRef.value.pageSize,
      },
    });
    createMessageSuccess("导出成功！");
  } catch (error) {
    createMessageError("导出失败：" + error);
  } finally {
    endLoading();
  }
};
/** 组件挂载 */
onMounted(() => {
  tableSearch();
});
</script>

<style lang="less" scoped>
@import "@/styles/defaultVar.less";
</style>
