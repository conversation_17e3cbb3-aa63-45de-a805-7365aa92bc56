<!-- 个人码 -->
<template>
  <n-modal
    v-model:show="modelProps.show"
    :auto-focus="false"
    style="width: 360px;"
    :bordered="false"
    size="small"
    :closable="false"
    preset="card"
    :mask-closable="false"
    :close-on-esc="false"
  >
    <template #header>
      <div style="display: flex;
        justify-content: space-between;
        line-height: 34px;"
      >
        <p>{{ modelProps.title }}</p>
        <n-button text @click="closeModal">关闭</n-button>
      </div>
    </template>
    <div style="display: flex;
           flex-wrap: wrap;
           justify-content: center;
           "
    >
      <n-spin :show="loading" description="加载中...">
        <n-image
          v-if="path && !modelProps.qrCode"
          width="260"
          height="260"
          :src="path"
        />
        <n-qr-code v-else-if="path && modelProps.qrCode" :value="path"
                   error-correction-level="Q" size="260"
        />
        <div v-else style="height: 260px;width: 260px">

        </div>
      </n-spin>
    </div>
  </n-modal>
</template>
<script setup lang="tsx" name="QRModel">
import { ref } from "vue";
export interface QRModalProps {
  show?: boolean; // 弹窗模式 --> 默认add
  title?: string; // 弹窗标题 --> 默认添加
  qrCode?:boolean; // 是否为二维码 --> 默认true
  api?: (params: any) => Promise<any>;// 新增保存Api
}
const modelProps = ref<QRModalProps>({} as QRModalProps);
const path = ref<string>('')
const loading = ref()
const showModal = async (params) => {
  modelProps.value.show = true;
  loading.value = true;
  modelProps.value.qrCode = params.qrCode || false
  path.value = ''
  modelProps.value.api = params.api
  try {
    path.value = await modelProps.value.api(params.data)
  }catch (e) {

  }finally {
    loading.value = false
  }
};

const closeModal = () => {
  modelProps.value.show = false;
}

defineExpose({ showModal });
</script>
<style scoped lang="less"></style>