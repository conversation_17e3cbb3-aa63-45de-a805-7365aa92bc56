<template>
  <MenuLayout v-model:activeKey="activeTypeRef" :menuOptions="menuList">
    <component :is="currentPage" />
  </MenuLayout>
</template>

<script setup lang="tsx">
import { ref, watch, computed } from "vue";
// import { useSvgIcon } from "@/hooks";
import { StoreConfigMenuType, type StoreConfigType } from "@/enums";
import { GroupNameValue } from "./type";
/** 相关组件 */
import MenuLayout from "@/components/MenuLayout/index.vue";
import AppletNavigation from "@/views/StoreModule/StoreConfig/components/AppletNavigation.vue";
import SlideShow from "@/views/StoreModule/StoreConfig/components/SlideShow.vue";
import MessageGroup from "@/views/StoreModule/StoreConfig/components/MessageGroup.vue";
import AddressConfiguration from "@/views/StoreModule/StoreConfig/components/AddressConfiguration.vue";
import HomeLogo from "@/views/StoreModule/StoreConfig/components/HomeLogo.vue";
import DeliverTool from "@/views/StoreModule/StoreConfig/components/DeliverTool.vue";
import OperationFlow from "@/views/StoreModule/StoreConfig/components/OperationFlow.vue";
import {
  hasOrderStoreconfigCarousel,
  hasOrderStoreconfigAddress,
  hasOrderStoreconfigDelivery,
  hasOrderStoreconfigTransaction,
  hasOrderStoreconfigDevelopment,
  hasOrderStoreconfigHomelogo,
  hasOrderStoreconfigMiniprogramnavigation,
} from "./hooks/authList";
import AuthRole from "./modules/AuthRole/index.vue";
import OperatorManagement from "./modules/OperatorManagement/index.vue";
import { hasAuthRolePageAuth, hasOperatorsPageAuth, hasOperationFlowPageAuth } from "./authList";
/** props */
const props = defineProps<{
  configurationAddressStatus?: number, // 配置地址跳转状态
}>();

// const { SvgIconVNode } = useSvgIcon();
/** 获取到的标签数据 */
const menuList = ref([
  {
    label: '轮播图',
    key: StoreConfigMenuType.SLIDESHOW,
    // icon: SvgIconVNode({ localIcon: 'Slideshow', fontSize: 18 }),
    show:hasOrderStoreconfigCarousel
  },
  {
    label: '地址配置',
    key: StoreConfigMenuType.ADDRESS,
    // icon: SvgIconVNode({ localIcon: 'Location', fontSize: 18 }),
    show:hasOrderStoreconfigAddress
  },
  {
    label: '发货工具',
    key: StoreConfigMenuType.DELIVERTOOL,
    // icon: SvgIconVNode({ localIcon: 'Location', fontSize: 18 }),
    show:hasOrderStoreconfigDelivery
  },
  {
    label: '交易相关',
    key: StoreConfigMenuType.DEAL,
    // icon: SvgIconVNode({ localIcon: 'Deal', fontSize: 18 }),
    show:hasOrderStoreconfigTransaction
  },
  {
    label: '开发配置',
    key: StoreConfigMenuType.DEVELOP,
    // icon: SvgIconVNode({ localIcon: 'DevConfig', fontSize: 18 }),
    show:hasOrderStoreconfigDevelopment
  },
  {
    label: '小程序页面',
    key: StoreConfigMenuType.HOMELOGO,
    // icon: SvgIconVNode({ localIcon: 'Home', fontSize: 18 }),
    show:hasOrderStoreconfigHomelogo
  },
  {
    label: '小程序导航',
    key: StoreConfigMenuType.APPLETNAV,
    // icon: SvgIconVNode({ localIcon: 'AppletNav', fontSize: 18 }),
    show:hasOrderStoreconfigMiniprogramnavigation
  },
  {
    label: '操作员管理',
    key: StoreConfigMenuType.OPERATOR,
    // icon: SvgIconVNode({ localIcon: 'UserLock', fontSize: 18 }),
    show: hasOperatorsPageAuth
  },
  {
    label: '操作员角色',
    key: StoreConfigMenuType.AUTHROLE,
    // icon: SvgIconVNode({ localIcon: 'UserShield', fontSize: 18 }),
    show: hasAuthRolePageAuth
  },
  {
    label: '业务流程',
    key: StoreConfigMenuType.FLOW,
    // icon: SvgIconVNode({ localIcon: 'UserShield', fontSize: 18 }),
    show: hasOperationFlowPageAuth
  },
]);
const activeTypeRef = ref<StoreConfigType>(menuList.value.filter(item => item.show).map(item => item.key)[0]);
/** 相关组件 */
const pageMap = {
  [StoreConfigMenuType.SLIDESHOW]: SlideShow,
  [StoreConfigMenuType.ADDRESS]: AddressConfiguration,
  [StoreConfigMenuType.DELIVERTOOL]: DeliverTool,
  [StoreConfigMenuType.HOMELOGO]: HomeLogo,
  [StoreConfigMenuType.APPLETNAV]: AppletNavigation,
  [StoreConfigMenuType.DEAL]: <MessageGroup key="Transaction" groupName={GroupNameValue.Transaction} />,
  [StoreConfigMenuType.DEVELOP]: <MessageGroup key="Development" groupName={GroupNameValue.Development} />,
  [StoreConfigMenuType.AUTHROLE]: AuthRole,
  [StoreConfigMenuType.OPERATOR]: OperatorManagement,
  [StoreConfigMenuType.FLOW]: OperationFlow,
}

/** 当前页 */
const currentPage = computed(() => pageMap[activeTypeRef.value])

/** 监听 */
watch(()=>{
  return props.configurationAddressStatus
},(newVal)=>{
  if(newVal == 1){
    activeTypeRef.value = StoreConfigMenuType.ADDRESS;
  }
},{immediate:true})
</script>

<style lang="less" scoped></style>
