<template>
  <n-modal
    v-model:show="showModal"
    :auto-focus="false"
    style="width: 600px"
    :bordered="false"
    size="small"
    @after-leave="closeModal"
    preset="card"
    :closable="false"
  >
    <n-input v-model:value="value" type="textarea" placeholder="请输入驳回原因" maxlength="50" show-count />
    <template #footer>
      <n-space justify="end" >
        <n-button @click="closeModal">取消</n-button>
        <n-button @click="rejectFn" type="primary" :loading="isLoading" >确定</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, toRefs, onMounted, computed} from 'vue';
import { paymentAuditReject } from '@/services/api';
import { useMessages } from "@/hooks";
const { createMessageSuccess, createMessageError } = useMessages();

const isLoading = ref(false);
const rejectFn = () => {
  if (!value.value) {
    createMessageError('请输入驳回原因');
    return;
  }
  isLoading.value = true;
  paymentAuditReject({data:{id:props.rejectId, rejectReason:value.value}}).then(() => {
    createMessageSuccess('操作成功');
    emits('success');
    closeModal();
  }).catch(err => {
    createMessageError(`操作失败:${err}`);
  }).finally(() => {
    isLoading.value = false;
  })
}

const props = withDefaults(defineProps<{
  show: boolean;
  rejectId: number;
}>(), {
  show: false,
  rejectId: null,
});
const emits = defineEmits<{
    (e: 'update:show', value: boolean): void;
    (e: 'success'): void;
}>();

/** 驳回原因 */
const value = ref('')

const showModal = computed({
    get() {
    return props.show;
    },
    set(value) {
    emits("update:show", value);
    },
})

const closeModal = () => {
    showModal.value = false;
    value.value = ''
}
</script>
<style scoped lang="less"></style>
