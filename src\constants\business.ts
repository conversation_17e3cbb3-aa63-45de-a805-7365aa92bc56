import { transformObjectToOption } from './_shared';

/** 基础设置 */
declare namespace BasicSetting {
  /**
   * 归属商品类型 
   * 1: 药品 
   * 2: 疗法
   */
  type merchandiseTypeKey = NonNullable<1 | 2 | 3>;

  /**
   * 药物类型
   * 0：OTC
   * 1：处方药
   * 2:中草药
   */
  type drugTypeKey = NonNullable<0 | 1 | 2>;


  /** 
   * 商品上架状态
   * 0：否
   * 1：是
   */
  type shelfStatusKey = NonNullable<'0' | '1'>;

  /** 
   * 商品审核状态
   * 0：全部、未提交、待审核、审核通过、审核不通过
   * 1：未提交
   * 2：待审核
   * 3：审核通过
   * 4：审核不通过
   */
  type auditStatusKey = NonNullable<0 | 1 | 2 | 3 | 4>;

  /** 
   * 是否外用
   * 0：否
   * 1：是
   */
  type externalStatusKey = NonNullable<0 | 1>;

  /** 
   * 产地类型
   * 1：国产
   * 2：进口
   */
  type drugOriginKey = NonNullable<1 | 2>;

  /**
   * 订单状态
   * 1： 待支付
   * 2： 待发货
   * 3： 待收货
   * 4： 已完成
   * 5： 已取消
   */
  type orderStatusKey = NonNullable<1 | 2 | 3 | 4 | 5>;

  /**
   * 处方状态
   * 1： 待开方
   * 2： 已开方
   * 3： 开方失败
   */
  type presStatusKey = NonNullable< 1 | 2 | 3>;

  /**
   * 支付方式
   * 0：暂无
   * 1：在线支付
   * 2：物流代收
   * 3：支付定金
   * 4: 纯积分支付
   * 5: 积分 + 现金
   * 6: 福利券兑换
   */
  type payTypeKey = NonNullable<0 | 1 | 2 | 3 | 4 | 5 | 6>;

  /**
   * 支付状态
   * 0: 未支付
   * 1：已支付定金
   * 2：已支付全款
   * 3：待物流收款
   */
  type payStatusKey = NonNullable<0 | 1 | 2 | 3>;

  /**
   * 签收类型
   * 0：未签收
   * 1：已签收
   * 2：拒收
   * 3：丢失
   * 4：退回
   * 5：物流显示已签收
   * 6：已超过自动确认收货天数
   */
  type completeTypeKey = NonNullable<0 | 1 | 2 | 3 | 4 | 5 | 6>;

  /**
   * 1: '社群',
   * 2: '商城自然流量',
   * 3: '群管代下单',
   * 4: 'T9系统',
   * 5: '个人分销码',
   * 6: '链接分享',
   * 7: '门店',
   */
  type orderSourceKey = NonNullable<1 | 2 | 3 | 4 | 5 | 6 | 7>;

  /**
   * 用药人当前状态
   * 1：无
   * 2：备孕
   * 3：妊娠
   * 4：哺乳
   */
  type currentPeriodStatusKey = NonNullable<1 | 2 | 3 | 4>;

  /**
   * 用药人用户关系
   * 1：本人
   * 2：子女
   * 3：父母
   * 4：配偶
   * 5：其他
   */
  type relationshipTypeKey = NonNullable<1 | 2 | 3 | 4 | 5>;

  /**
   * 售后状态筛选
   * 0：全部
   * 1：待受理
   * 5：待收货
   * 8：待打款
   */
  type afterSalesStatusScreeningTypeKey = NonNullable<0 | 1 | 5 | 8>

  /**
   * 1：仅退款
   * 2：退货退款
   * 3：仅取消订单
   */
  type afterSalesLabelsTypeKey = NonNullable<1 | 2 | 3>

  /**
   *0:其他,
   *1:拍错/多拍,
   *2:不想要了,
   *3:无快递信息,
   *4:包裹为空,
   *5:已拒签包裹,
   *6:快递长时间未送达,
   *7:与商品描述不符,
   *8:质量问题,
   *9:卖家发错货,
   *10:三无产品,
   *11:假冒产品,
   */
   type aReasonForRefundTypeKey = NonNullable<0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11>

   /**
    *0:非售后状态, 
    *1:待商家受理,
    *2:客户撤回申请,
    *3:商家拒绝退款,
    *4:待客户退货,
    *5:待商家收货,
    *6:退货退款关闭,
    *7:退款中,
    *8:待打款,
    *9:退款完成,
    *10:商家同意取消订单,
    *11:商家拒绝取消订单,
    */
   type afterSalesStatusTypeKey = NonNullable<0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11>

   /** 
     *1:'同意退款?',(退款金额小于等于线上支付金额)
     *2:'同意退款?',(退款金额大于线上支付金额)
     *3:'同意退货退款?',(退货地址已配置)
     *4:'同意退货退款?',(退货地址未配置)
     *5:'同意取消订单?',
     *6:'请通知财务付款',
     *7:'拒绝申请',
     *8:'收货并退款',(退款金额小于等于线上支付金额)
     *9:'收货并退款',(退款金额大于线上支付金额)
     *10:'再次发起退款'
     *11:'已线下退款'
     *21:'客户申请退款'
     *23:'录入物流信息'
    */
   type afterSaleManagementTypeKey = NonNullable<1 | 2 | 3 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 21 | 23>

   /**
     *0:未发货,
     *1:已发货,
    */
   type deliveryStatusTypeKey = NonNullable<0 | 1>

   /**
    * 1:买家发起售后，待商家处理
    * 2:买家已撤回申请，交易继续进行
    * 3:商家拒绝退款
    * 4:商家已同意申请，待买家退货
    * 5:买家已寄出退货商品，待商家收货
    * 6:买家在退货时限内未寄出退货商品，本次售后申请已自动取消。
    * 7:退款中
    * 8:待打款
    * 9:退款完成
    * 10:商家同意取消订单
    * 11:商家拒绝取消订单
    */
   type stateChangeRecordTypeKey = NonNullable<1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11>

   /**
    * 1:线上退款
    * 2:线下退款
    */
   type refundTypeTypeKey = NonNullable<1 | 2>

   /**
    *  0:'未退款',
    *  1:'已线上退款',
    *  2:'待打款',
    *  3:'已线下退款',
    *  4:'线上退款失败',
    *  5:'拒绝退款',
    */
   type refundStateTypeKey = NonNullable<0 | 1 | 2 | 3 | 4 | 5>

   /**
    * 0: 无商品分类
    * 1：有商品分类
    */
   type goodsCategoryTypeKey = NonNullable<0 | 1 >

   /**
    * 0:商城
    * 1:社群
    */
   type channelTypeKey = NonNullable<0 | 1>

   type videoTypeKey = NonNullable<0 | 1 | 2>
   /**
    * 0:过期未使用,
    * 1:积分购物支出,
    * 2:购物返还,
    * 3:签到,
    * 4:每日来访,
    * 5:查看商品,
    * 6:社群任务
    */
   type sourceCategoryTypeKey = NonNullable<0 | 1 | 2 | 3 | 4 | 5 | 6>

   /**
    * 0:增积分，
    * 1：减积分
    */
   type IntegralVariationTypeKey = NonNullable<11 | 12>

   /**
    * 1: 仅退款，
    * 2：退货退款
    */
   type ordersAfterSalesTypeKey = NonNullable<1 | 2>

   /**
    * 0:全部,
    * 1:待处理
    */
   type subBillManagementTypeKey = NonNullable<0 | 1>

   /**
    * 0: '未发起',
    * 1: '处理中',
    * 2: '分账成功',
    * 3: '分账失败',
    * 4: '已取消'
    */
   type splitStatusTypeKey = NonNullable<0 | 1 | 2 | 3 | 4>

   /**
    * 0: '未结算',
    * 1: '结算成功',
    * 2: '结算失败',
    * 3: '结算中',
    * 4: '已结算状态未知',
    * 5: '银行退票'
    */
   type settlementStatusTypeKey = NonNullable<0 | 1 | 2 | 3 | 4 | 5 >

   /**
    * 0: '待签署协议',
    * 1: '已生效:对应富友支付状态[生效]',
    * 2: '已失效:对应富友支付状态[失效]',
    * 3: '待绑卡(个人)',
    * 4: '待绑卡(企业)',
    * 5: '系统开户中'
    * 6: '待打款验证',
    * 7: '待短信验证'
    */
   type SubAccountPayeeManagementStateTypeKey = NonNullable<0 | 1 | 2 | 3 | 4 | 5 | 6 | 7>

   /**
    * 0: '个人',
    * 1: '企业',
    * 2: '个体工商户'
    */
   type accountOpeningTypeKey = NonNullable<1 | 2 | 3>

   /**
    * 0: '法人对私卡',
    * 1: '企业对公户',
    */
   type bankAccountTypeKey = NonNullable<1 | 2>
   
    /**
    * 0: '身份证',
    * 1: '营业执照',
    */
   type accountTypeKey = NonNullable<0 | 1 >
   /**
     * 1:微信支付
     * 2:富友支付
     * 3:中金支付
     * 4:汇聚支付
    */
   type paymentMethodsTypeKey = NonNullable<1 | 2 | 4>

   /**
    * 0: '解绑',
    * 1: '绑定',
    */
   type bankCardBindingTypeKey = NonNullable<0 | 1 | 2>

   /** 
    * 0: '身份证',
    * 1: '外国护照',
    * 2: '港澳通行证'
    */
   type legalPersonCertificateTypeKey = NonNullable<0 | 1 | 2>

   /**
    * 0: '待打款'
    * 1: '打款中'
    * 2: '已打款'
    * 3: '打款失败'
    * 4: '待线下打款'
    * 5: '已线下打款'
    */
   type payoutStatusTypeKey = NonNullable<0 | 1 | 2 | 3 | 4 | 5>

   /**
    * 0:'待结算',
    * 1:'已结算',
    * 2:'已取消',
    */
   type settlementStatusSecondTypeKey =  NonNullable<0 | 1 | 2>

   /**
    * 0:'正常',
    * 1:'已冻结',
    */
   type distributorStatusTypeKey = NonNullable<1 | 2>

   /**
    * 
    */
   type CommissionStatusTypeKey = NonNullable<0 | 1 | 2>

  /**
   *
   */
  type IsPublishStatusTypeKey = NonNullable<0 | 1 >

   /**
    * 1:'微信支付平台证书序列号',
    * 2:'微信支付公钥',
    */
   type configureValueTypeKey = NonNullable<1 | 2>
  /**
   * 0:'待打款',
   * 1:'打款中',
   * 2:'已打款',
   * 3:'打款失败',
   * 4:'待线下打款',
   * 5:'已线下打款'
   */
  type payoutsStatusSecondTypeKey = NonNullable<0 | 1 | 2 | 3 | 4 | 5>

   /**
   * 1:'主任医师',
   * 2:'副主任医师',
   * 3:'主治医师',
   * 4:'住院医师',
   * 5:'医士'
   */
   type JobTitleTypeKey = NonNullable< 1 | 2 | 3 | 4 | 5>

   /** 
    * 0:'是',
    * 1:'否'
    */
   type InterrogationOnlinePresenceTypeKey = NonNullable<0 | 1>

   /**
    * 1:'平台审核药师'
    */
   type OfficeTypeKey = NonNullable<1>

    /**
   * @description 地址配置
   * 1：'退货地址',
   * 3：'发货地址'
   */
  type AddressSceneTypeKey = NonNullable<1 | 3>;
  /**
   * 1:'自研系统'
   * 2:'第三方互联网医院'
   */
  type PresSystem = NonNullable<1 | 2>

  /**
     * @description 福利券领取状态
     * 0:'未使用'
     * 1:'已使用'
     * 2:'已失效'
     */
  type WelfareDrawStateTypeKey = NonNullable< 0 | 1 | 2 >

  /**
     * @description 福利品上架状态
     * 0:'上架'
     * 1:'下架'
     */
  type WelfareGoodsStateTypeKey = NonNullable< 0 | 1 >

  /**
   * @description 供应商状态
   * 1:'正常'
   * 0:'停用'
   */
  type SupplierStateTypeKey = NonNullable<0 | 1>

  /**
   * @description 锁单状态
   * 1:'是'
   * 0:'否'
   */
  type isLockedTypeKey = NonNullable<0 | 1>

  /**
   * @description 提货方式
   * 1:'快递'
   * 2:'自提'
   */
  type pickupType = NonNullable<2 | 1>
  /**
   * @description 可兑换日期
   * 1:'全时段可兑'
   * 2:'限定时间段可兑'
   */
  type redemptionDateType = NonNullable<0 | 1>
  /**
   * @description 小程序端积分商城用户可见
   * 1:'全部商品'
   * 2:'社群经销商商品'
   */
  type pointsMallVisibleType = NonNullable<0 | 1>
  /**
   * @description 商品成本扣除
   * 1:'不扣除'
   * 2:'扣除社群经销商账户余额'
   */
  type deductionOfCommodityCostType = NonNullable<0 | 1>

  /**
   * @description 店铺管理订单类型
   * 1:'普通订单'
   * 2:'代下单订单'
   * 3:'积分订单'
   * 4:'福利券订单'
   */
    type StoreOrderType = NonNullable<1 | 3 | 4>

  /**
   * @description 店铺管理商品类型
   */
  type StoreGoodsType = NonNullable<3 | 4 | 5>
}

/** 店铺管理商品类型 */
export const storeGoodsTypeLabels: Record<BasicSetting.StoreGoodsType, string> = {
  3: '普通商品',
  4: '积分商品',
  5: '福利券商品'
}
export const storeGoodsTypeOptions = transformObjectToOption(storeGoodsTypeLabels, true);

/** 店铺管理订单类型 */
export const storeOrderTypeLabels: Record<BasicSetting.StoreOrderType, string> = {
  1: '普通订单',
  3: '积分订单',
  4: '福利券订单'
}
export const storeOrderTypeOptions = transformObjectToOption(storeOrderTypeLabels, true);


/** 归属商品类型 */
export const merchandiseTypeLabels: Record<BasicSetting.merchandiseTypeKey, string> = {
  1: '药品',
  2: '疗法',
  3: '普通商品'
};
export const merchandiseTypeOptions = transformObjectToOption(merchandiseTypeLabels);

/** 药物类型 */
export const drugTypeLabels: Record<BasicSetting.drugTypeKey, string> = {
  0: 'OTC',
  1: '处方药',
  2: '中草药'
};
export const drugTypeOptions = transformObjectToOption(drugTypeLabels, true);

/** 商品上架状态  */
export const shelfStatusLabels: Record<BasicSetting.shelfStatusKey, string> = {
  0: '否',
  1: '是',
};
export const shelfStatusOptions = transformObjectToOption(shelfStatusLabels, true);

/** 商品审核状态 */
export const auditStatusLabels: Record<BasicSetting.auditStatusKey, string> = {
  0: '全部',
  1: '未提交',
  2: '待审核',
  3: '审核通过',
  4: '审核不通过',
};
export const auditStatusOptions = transformObjectToOption(auditStatusLabels, true);

/** 是否外用 */
export const externalStatusLabels: Record<BasicSetting.externalStatusKey, string> = {
  0: '否',
  1: '是',
};
export const externalStatusOptions = transformObjectToOption(externalStatusLabels, true);

/** 产地类型 */
export const drugOriginLabels: Record<BasicSetting.drugOriginKey, string> = {
  1: '国产',
  2: '进口',
};
export const drugOriginOptions = transformObjectToOption(drugOriginLabels, true);

/** 订单状态 */
export const orderStatusLabels: Record<BasicSetting.orderStatusKey, string> = {
  1: '待支付',
  2: '待发货',
  3: '待收货',
  4: '已完成',
  5: '已取消',
};
export const orderStatusOptions = transformObjectToOption(orderStatusLabels, true);

/** 订单状态 */
export const presStatusLabels: Record<BasicSetting.presStatusKey, string> = {
  1: '待开方',
  2: '已开方',
  3: '开方失败',
};
export const presStatusOptions = transformObjectToOption(presStatusLabels, true);


/** 支付方式 */
export const payTypeLabels: Record<BasicSetting.payTypeKey, string> = {
  0: '暂无',
  1: '在线支付',
  2: '物流代收',
  3: '支付定金',
  4: '纯积分支付',
  5: '积分 + 现金',
  6: '福利券兑换'
};
export const payTypeOptions = transformObjectToOption(payTypeLabels, true);

/** 支付状态 */
export const payStatusLabels: Record<BasicSetting.payStatusKey, string> = {
  0: '未支付',
  1: '已支付定金',
  2: '已支付全款',
  3: '待物流收款',
};
export const payStatusOptions = transformObjectToOption(payStatusLabels, true);

/** 签收类型 */
export const completeTypeLabels: Record<BasicSetting.completeTypeKey, string> = {
  0: '未签收',
  1: '已签收',
  2: '拒收',
  3: '丢失',
  4: '退回',
  5: '物流显示已签收',
  6: '已超过自动确认收货天数',
};
export const completeTypeOptions = transformObjectToOption(completeTypeLabels, true);

/** 订单来源 */
export const orderSourceLabels: Record<BasicSetting.orderSourceKey, string> = {
  1: '社群',
  2: '商城自然流量',
  3: '群管代下单',
  4: 'T9系统',
  5: '个人分销码',
  6: '链接分享',
  7: '门店'
};
export const orderSourceOptions = transformObjectToOption(orderSourceLabels, true);

/** 用药人当前状态 */
export const currentPeriodStatusLabels: Record<BasicSetting.currentPeriodStatusKey, string> = {
  1: '无',
  2: '备孕',
  3: '妊娠',
  4: '哺乳',
};
export const currentPeriodStatusOptions = transformObjectToOption(currentPeriodStatusLabels, true);

/** 用药人用户关系 */
export const relationshipTypeLabels: Record<BasicSetting.relationshipTypeKey, string> = {
  1: '本人',
  2: '子女',
  3: '父母',
  4: '配偶',
  5: '其他',
};
export const relationshipTypeOptions = transformObjectToOption(relationshipTypeLabels, true);

/** 售后状态筛选 */
export const afterSalesStatusScreeningLabels: Record<BasicSetting.afterSalesStatusScreeningTypeKey, string> = {
  0: '全部',
  1: '待受理',
  5: '待收货',
  8: '待打款',
};
export const afterSalesStatusScreeningOptions = transformObjectToOption(afterSalesStatusScreeningLabels, true);

/** 售后类型 */
export const afterSalesLabels: Record<BasicSetting.afterSalesLabelsTypeKey, string> = {
  1: '仅退款',
  2: '退货退款',
  3: '仅取消订单',
};
export const afterSalesLabelsOptions = transformObjectToOption(afterSalesLabels, true);

/** 退款原因 */
export const aReasonForRefundLabels: Record<BasicSetting.aReasonForRefundTypeKey, string> = {
  0:'其他',
  1:'拍错/多拍',
  2:'不想要了',
  3:'无快递信息',
  4:'包裹为空',
  5:'已拒签包裹',
  6:'快递长时间未送达',
  7:'与商品描述不符',
  8:'质量问题',
  9:'卖家发错货',
  10:'三无产品',
  11:'假冒产品'
};
export const aReasonForRefundOptions = transformObjectToOption(aReasonForRefundLabels, true);

/** 售后状态 */
export const afterSalesStatusLabels:  Record<BasicSetting.afterSalesStatusTypeKey, string> = {
  0:'非售后状态',
  1:'待商家受理',
  2:'客户撤回申请',
  3:'商家拒绝退款',
  4:'待客户退货',
  5:'待商家收货',
  6:'退货退款关闭',
  7:'退款中',
  8:'待打款',
  9:'退款完成',
  10:'商家同意取消订单',
  11:'商家拒绝取消订单',
};
export const afterSalesStatusOptions = transformObjectToOption(afterSalesStatusLabels, true);

/** 售后管理---点击操作菜单 */
export const afterSaleManagementLabels: Record<BasicSetting.afterSaleManagementTypeKey, string> = {
  1:'同意退款?',
  2:'同意退款?',
  3:'同意退货退款?',
  5:'同意取消订单?',
  6:'请通知财务付款',
  7:'拒绝申请',
  8:'收货并退款',
  9:'收货并退款',
  10:'再次发起退款',
  11:'已线下退款',
  21:'客户申请退款',
  23:'录入物流信息'
};
export const afterSaleManagementOptions = transformObjectToOption(afterSaleManagementLabels, true);

/** 售后管理---发货状态 */
export const deliveryStatusLabels: Record<BasicSetting.deliveryStatusTypeKey, string> = {
  0:'未发货',
  1:'已发货'
};
export const deliveryStatusOptions = transformObjectToOption(deliveryStatusLabels, true);

/** 售后管理 --- 详情 --- 状态变更记录内容 */
export const stateChangeRecordLabels: Record<BasicSetting.stateChangeRecordTypeKey, string> = {
  1:'买家发起售后，待商家处理',
  2:'买家已撤回申请，交易继续进行',
  3:'商家拒绝退款',
  4:'商家已同意申请，待买家退货',
  5:'买家已寄出退货商品，待商家收货',
  6:'买家在退货时限内未寄出退货商品，本次售后申请已自动取消。',
  7:'["商家发起在线退款请求，第三方支付平台处理中","商家已收到货，发起在线退款请求，第三方支付平台处理中"]',
  8:'["第三方支付平台退款失败，待商家处理","商家已同意退款，等待财务打款","商家已收到货，等待财务打款"]',
  9:'["线上退款成功","线下退款完成"]',
  10:'商家同意取消订单',
  11:'商家拒绝取消订单'
}
export const stateChangeRecordOptions = transformObjectToOption(stateChangeRecordLabels, true);

/** 售后管理 --- 退款类型 */
export const refundTypeLabels: Record<BasicSetting.refundTypeTypeKey, string> = {
  1:'线上退款',
  2:'线下退款',
}
export const refundTypeOptions = transformObjectToOption(refundTypeLabels, true);

/** 售后管理 --- 退款状态 */
export const refundStateLabels : Record<BasicSetting.refundStateTypeKey, string> = {
  0:'未退款',
  1:'已线上退款',
  2:'待打款',
  3:'已线下退款',
  4:'线上退款失败',
  5:'拒绝退款',
}
export const refundStateOptions = transformObjectToOption(refundStateLabels, true);


/** 有无商品分类 */
export const goodsCategoryLabels : Record<BasicSetting.goodsCategoryTypeKey, string> = {
  0:'无分类商品',
  1:'有分类商品',
}
export const goodsCategoryOptions = transformObjectToOption(goodsCategoryLabels, true);

/** 客户管理 -- 积分明细 -- 渠道 */
export const channelLabels : Record<BasicSetting.channelTypeKey, string> = {
  0:'商城',
  1:'社群',
}
export const channelOptions = transformObjectToOption(channelLabels, true);

export const videoTypeLabel  : Record<BasicSetting.videoTypeKey, string> = {
  0:'发布中',
  1:'已发布',
  2:'未发布',
}

export const videoAuditLabel  : Record<BasicSetting.videoTypeKey, string> = {
  0:'待审核',
  1:'审核通过',
  2:'审核未通过',
}
/** 客户管理 -- 积分明细 -- 来源类别 */
export const sourceCategoryLabels : Record<BasicSetting.sourceCategoryTypeKey, string> = {
  0:'过期未使用',
  1:'积分购物支出',
  2:'购物返还',
  3:'签到',
  4:'每日来访',
  5:'查看商品',
  6:'社群任务'
}
export const sourceCategoryOptions = transformObjectToOption(sourceCategoryLabels, true);


/** 客户管理 */
export const IntegralVariationLabels : Record<BasicSetting.IntegralVariationTypeKey, string> = {
  11:'增积分',
  12:'减积分',
}
export const IntegralVariationOptions = transformObjectToOption(IntegralVariationLabels, true);

/** 订单管理 -- 售后类型 */
export const ordersAfterSalesLabels : Record<BasicSetting.ordersAfterSalesTypeKey, string> = {
  1:'仅退款',
  2:'退货退款',
}
export const ordersAfterSalesOptions = transformObjectToOption(ordersAfterSalesLabels, true);
/** 商城配置 -- 商户号管理 -- 支付服务商 */
export const paymentMethodsLabels : Record<BasicSetting.paymentMethodsTypeKey, string> = {
  1:'微信支付',
  2:'富友支付',
  // 3:'中金支付'
  4:'汇聚支付'
}
export const paymentMethodsOptions = transformObjectToOption(paymentMethodsLabels, true);

/** 财务管理 -- 分账单管理 */
export const subBillManagementLabels: Record<BasicSetting.subBillManagementTypeKey, string> = {
  0: '全部',
  1: '待受理',
};
export const subBillManagementOptions = transformObjectToOption(subBillManagementLabels, true);

/** 财务管理 -- 分账单管理 -- 分账状态*/
export const splitStatusLabels: Record<BasicSetting.splitStatusTypeKey, string> = {
  0: '未发起',
  1: '处理中',
  2: '分账成功',
  3: '分账失败',
  4: '已取消'
};
export const splitStatusOptions = transformObjectToOption(splitStatusLabels, true);

/** 财务管理 -- 分账单管理 -- 结算状态*/
export const settlementStatusLabels: Record<BasicSetting.settlementStatusTypeKey, string> = {
  0: '未结算',
  1: '结算成功',
  2: '结算失败',
  3: '结算中',
  4: '已结算状态未知',
  5: '银行退票'
};
export const settlementStatusOptions = transformObjectToOption(settlementStatusLabels, true);

/** 财务管理 -- 分账入账方管理 -- 状态*/
export const SubAccountPayeeManagementStateLabels: Record<BasicSetting.SubAccountPayeeManagementStateTypeKey, string> = {
  0: '待签署协议',
  1: '已生效',
  2: '已失效',
  3: '待绑卡(个人)',
  4: '待绑卡(企业)',
  5: '系统开户中',
  6: '待打款验证',
  7: '待短信验证'
};
export const SubAccountPayeeManagementStateOptions = transformObjectToOption(SubAccountPayeeManagementStateLabels, true);

/** 财务管理 -- 分账入账方管理 -- 开户类型*/
export const accountOpeningLabels: Record<BasicSetting.accountOpeningTypeKey, string> = {
  1: '个人',
  2: '企业',
  3: '个体工商户'
};
export const accountOpeningOptions = transformObjectToOption(accountOpeningLabels, true);

/** 财务管理 -- 分账入账方管理 -- 银行账号类型*/
export const bankAccountLabels: Record<BasicSetting.bankAccountTypeKey, string> = {
  1: '法人对私卡',
  2: '企业对公户',
};
export const bankAccountOptions = transformObjectToOption(bankAccountLabels, true);


/** 财务管理 -- 分账入账方管理 -- 开户证件类型*/ 
export const accountLabels: Record<BasicSetting.accountTypeKey, string> = {
  0: '身份证',
  1: '营业执照',
};
export const accountOptions = transformObjectToOption(accountLabels, true);

/** 财务管理 -- 分账入账方管理 -- 法人证件类型可选*/ 
export const legalPersonCertificateLabels: Record<BasicSetting.legalPersonCertificateTypeKey, string> = {
  0: '身份证',
  1: '外国护照',
  2: '港澳通行证'
};
export const legalPersonCertificateOptions = transformObjectToOption(legalPersonCertificateLabels, true);

/** 财务管理 -- 分账入账方管理 -- 银行卡绑卡状态 */
export const bankCardBindingLabels: Record<BasicSetting.bankCardBindingTypeKey, string> = {
  0: '协议未签署',
  1: '失效',
  2: '生效'
};
export const bankCardBindingOptions = transformObjectToOption(bankCardBindingLabels, true);

/** 财务管理 -- 分账单结算 -- 打款状态 */
export const payoutStatusLabels: Record<BasicSetting.payoutStatusTypeKey, string> = {
  0:'待打款',
  1:'打款中',
  2:'已打款',
  3:'打款失败',
  4:'待线下打款',
  5:'已线下打款',
};
export const payoutStatusOptions = transformObjectToOption(payoutStatusLabels, true);

/** 财务管理 -- 分账单管理 -- 结算状态 */
export const settlementStatusSecondLabels: Record<BasicSetting.settlementStatusSecondTypeKey, string> = { 
  0:'待结算',
  1:'已结算',
  2:'已取消',
};
export const settlementStatusSecondOptions = transformObjectToOption(settlementStatusSecondLabels, true);

/** 财务管理 -- 分账单管理 -- 打款状态 */
export const payoutsStatusSecondLabels: Record<BasicSetting.payoutsStatusSecondTypeKey, string> = { 
  0:'待打款',
  1:'打款中',
  2:'已打款',
  3:'打款失败',
  4:'待线下打款',
  5:'已线下打款'
};
export const payoutsStatusSecondOptions = transformObjectToOption(payoutsStatusSecondLabels, true);

/** 分销员管理 -- 状态 */
export const DistributorStatusLabels: Record<BasicSetting.distributorStatusTypeKey, string> = { 
  1:'正常',
  2:'已冻结',
};
export const DistributorStatusOptions = transformObjectToOption(DistributorStatusLabels, true);

/** 分销员管理 -- 佣金状态 */
export const CommissionStatusLabels: Record<BasicSetting.CommissionStatusTypeKey, string> = { 
  0:'待结算',
  1:'已结算',
  2:'已取消',
};
export const CommissionStatusOptions = transformObjectToOption(CommissionStatusLabels, true);

/** 分销员管理 -- 上架状态 */
export const IsPublishStatusLabels: Record<BasicSetting.IsPublishStatusTypeKey, string> = {
  0:'否',
  1:'是',
};
export const IsPublishStatusOptions = transformObjectToOption(IsPublishStatusLabels, true);
/** 商城配置 -- 商户号管理 -- 选中模式 */
export const configureValueLabels : Record<BasicSetting.configureValueTypeKey, string> = {
  1:'平台证书模式',
  2:'公钥模式',
}
export const configureValueOptions = transformObjectToOption(configureValueLabels, true);


/** 问诊 -- 医生管理 -- 职称 */
export const JobTitleLabels: Record<BasicSetting.JobTitleTypeKey, string> = {
  1:'主任医师',
  2:'副主任医师',
  3:'主治医师',
  4:'住院医师',
  5:'医士'
};
export const JobTitleOptions = transformObjectToOption(JobTitleLabels, true);

/** 问诊 -- 医生管理 -- 在线状态 */ 
export const InterrogationOnlinePresenceLabels: Record<BasicSetting.InterrogationOnlinePresenceTypeKey, string> = {
  0:'否',
  1:'是'
};
export const InterrogationOnlinePresenceOptions = transformObjectToOption(InterrogationOnlinePresenceLabels, true);

/** 问诊 -- 药师管理 -- 职务 */ 
export const OfficeLabels: Record<BasicSetting.OfficeTypeKey, string> = {
  1:'平台审核药师'
};
export const OfficeOptions = transformObjectToOption(InterrogationOnlinePresenceLabels, true);

 /** 地址使用场景 */
export const AddressSceneLabels: Record<BasicSetting.AddressSceneTypeKey, string> = {
    1: '退货地址',
    3: '发货地址',
  };
export const AddressSceneOptions = transformObjectToOption(AddressSceneLabels, true);
/** 开方系统 */
export const PresSystemLabels: Record<BasicSetting.PresSystem, string> = {
  1: '自研系统',
  2: '第三方互联网医院',
};
export const PresSystemOptions = transformObjectToOption(PresSystemLabels, true);

/** 搜索类型 */
export const DoctorEndModuleSearchTypeRecord: Record<string, string> = {
    'id': '患者姓名',
    'userName': '医生姓名',
    'useAccount': '用户昵称',
    'listTitle': '经销商姓名',
    'listTitle1': '群管昵称',
    'listTitle2': '订单编号',
};

/** 医师端处方搜索类型 */
export const DoctorEndModulePrescriptionSearchTypeRecord: Record<string, string> = {
    'patientsName': '患者姓名',
    'doctorName': '医生姓名',
    'nickName': '用户昵称',
    'pharmacistName': '药师姓名',
    'code': '处方编号',
    'consultationCode': '问诊单号',
};

/** 医师端处方状态 */
export const DoctorEndModulePrescriptionStatusRecord: Record<number, string> = {
    0: '待审核',
    1: '可使用',
    2: '已使用',
    3: '审核不通过',
    4: '已失效',
};


/** 问诊单状态类型 */
export const MedicalConsultationTypeRecord:Record<string, string> = {
    'id1': '待支付',
    'id2': '待接诊',
    'id3': '咨询中',
    'id4': '已完成',
    'id5': '已取消',
    
}
/** 营销管理 - 福利券管理 - 状态 */
export const WelfareDrawStateLabels: Record<BasicSetting.WelfareDrawStateTypeKey, string> = {
  0: '未使用',
  1: '已使用',
  2: '已失效',
};
export const WelfareDrawStateOptions = transformObjectToOption(WelfareDrawStateLabels, true);

/** 供应商管理 - 状态 */
export const SupplierStateLabels: Record<BasicSetting.SupplierStateTypeKey, string> = {
  0: '停用',
  1: '正常',
};
export const SupplierStateOptions = transformObjectToOption(SupplierStateLabels, true);

/**  营销管理 - 福利品管理 - 上架状态 */
export const WelfareGoodsStateLabels: Record<BasicSetting.WelfareGoodsStateTypeKey, string> = {
  0: '下架',
  1: '上架',
};
export const WelfareGoodsStateOptions = transformObjectToOption(WelfareGoodsStateLabels, true);

/**  订单管理 锁单状态 */
export const isLockedLabels: Record<BasicSetting.isLockedTypeKey, string> = {
  0: '否',
  1: '是',
};
export const isLockedOptions = transformObjectToOption(isLockedLabels, true);

/**  订单管理 提货方式 */
export const pickupTypeLabels: Record<BasicSetting.pickupType, string> = {
  1: '快递',
  2: '自提',
};
export const pickupTypeOptions = transformObjectToOption(pickupTypeLabels, true);
/** 可兑换日期 */
export const redemptionDateTypeLabels: Record<BasicSetting.redemptionDateType, string> = {
  0: '全时段可兑',
  1: '限定时间段可兑',
};
export const redemptionDateOptions = transformObjectToOption(redemptionDateTypeLabels, true);
/** 小程序端积分商城用户可见 */
export const pointsMallVisibleTypeLabels: Record<BasicSetting.pointsMallVisibleType, string> = {
  0: '全部商品',
  1: '社群经销商商品',
};
export const pointsMallVisibleTypeOptions = transformObjectToOption(pointsMallVisibleTypeLabels, true);
/** 商品成本扣除 */
export const deductionOfCommodityCostTypeLabels: Record<BasicSetting.deductionOfCommodityCostType, string> = {
  0: '不扣除',
  1: '扣除社群端经销商账户余额',
};
export const deductionOfCommodityCostTypeOptions = transformObjectToOption(deductionOfCommodityCostTypeLabels, true);