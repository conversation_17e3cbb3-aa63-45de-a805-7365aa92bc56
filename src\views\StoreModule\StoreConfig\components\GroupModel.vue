<script setup lang="tsx">
import { ref, onMounted, watchEffect, watch, computed } from "vue";
import { NModal, NSpace, NButton } from "naive-ui";
import { useMessages } from "@/hooks";
import { mallConfiguration } from "../hooks"
const {selectList,radioList, messageList, validatorTest , validatorStrTest , systemConfigOptionGetOptionsApi,isLoading} = mallConfiguration()
import {
  batchUpdateSysConfig,
  batchUpdateDealConfig,
  batchUpdateDevConfig,
  startConfig,
  stopConfig,
} from "@/services/api";
import { isArray } from "@/utils";
import { SystemEnum } from "@/enums";
import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
const systemStore = useSystemStoreWithoutSetup();
const Api_list = [null,null,batchUpdateDealConfig,batchUpdateDevConfig]
/* 模态框 */
const show = ref(false);
const save_loading = ref(false)
const message = useMessages();
const emit = defineEmits(["tableSearch"])
const formRef = ref(null);
type ModelParamsType = {
  id:string,
  configName: string,
  value: any,
  desc:string,
  key:string,
  type: number,
  groupName:string,
  api:(params: any) => Promise<any>,
}
const initParams:ModelParamsType = {
  id:'',
  configName: '',
  value: '',
  desc:'',
  key:'',
  type:null,
  groupName:'',
  api:null
};
const model = ref<ModelParamsType>({ ...initParams });

const radio = [
  {
    value:'true',
    label:"是"
  },
  {
    value:'false',
    label:"否"
  }
]
const formRulesRef = ref({})
// 配置值下拉列表
const configList = ref([])

const isCustomerRole = ref(false);
/* 监听模态框，初始化可见项目rule */
watchEffect(() => {
  if (show.value) {
    isCustomerRole.value = false;
  }
});
/* 模态框显示 */
const showModal = (userData,value) => {
  show.value = true;
  model.value.id = userData?.id || null
  model.value.configName = userData?.configName || null
  model.value.value = userData?.value
  model.value.type = userData?.type
  model.value.key = userData?.key || null
  model.value.groupName = userData?.groupName || ''
  model.value.api = userData?.api || Api_list[value]
  if(userData?.key == 'sto_allocation_account_rule'){
    model.value.desc = userData?.desc.replace(/。/g, "。<br>") || null
  }else{
    model.value.desc = userData?.desc || null
  }

  if(model.value.type == 8 && model.value.key != 'sto_merchant_info'){
    if (selectList[model.value.key][0].value == 1){
      model.value.value = selectList[model.value.key][model.value.value - 1].value
    }else {
      model.value.value = selectList[model.value.key][model.value.value].value
    }
  }else if (model.value.type == 10){
    if (!model.value.value){
      model.value.value = []
    }else {
      model.value.value = JSON.parse(model.value.value)
    }
  }
  
  formRulesRef.value = {
    value:[
      {
        required: true,
        message: messageList[model.value.key]??'请输入正确值',
        trigger: ['input','blur'],
        validator: ()=>{
          switch (model.value.type) {
            case 1:
              return model.value.value != '' && validatorStrTest(model.value.key,model.value.value);
            case 2:
              return model.value.value != null && validatorTest(model.value.key,model.value.value)
            case 10:
              return model.value.value.length >= 1
            default:
              return true
          }
        }
      },
    ],
  }

  if(model.value.key == 'sto_merchant_info'){
    systemConfigOptionGetOptionsApi(model.value.id)
  }
};
/* 清空表单 */
const formDataReset = () => {
  model.value = { ...initParams };
};

const closeModal = () => {
  formDataReset()
};
/* 提交、保存 */
const _save = (e) => {
  e.preventDefault();
  let dataValue: any
  if (model.value.type == 10){
    if (isArray(model.value.value)){
      if (model.value.value.length>1){
        dataValue = model.value.value[1]
      }else {
        dataValue = model.value.value[0]
      }
    }else {
      dataValue = model.value.value
    }
    dataValue = JSON.stringify(dataValue)
  }else {
    dataValue = model.value.value
  }
  if (model.value.key == 'sto_t9_integration'){
    save_loading.value = true
    try {
      if (dataValue == 'true'){
        startConfig();
      }else {
        stopConfig();
      }
      message.createMessageSuccess(dataValue == 'true' ? "开启t9对接配置项成功！" : "关闭t9对接配置项成功！");
      show.value = false
      save_loading.value = false
      setTimeout(()=>{
        emit("tableSearch");
      },1000)
    } catch (error) {
      message.createMessageError('修改t9对接配置项失败：' + error);
    }
  }else {
    const params = {
      data:{
        id:model.value.id,
        configName: model.value.configName,
        value: dataValue,
        desc:model.value.desc,
        key:model.value.key,
        groupName:model.value.groupName,
      }
    }
    formRef.value?.validate(async(errors) => {
      
      try{
        if(!errors){
          save_loading.value = true
          const result = await model.value.api(params)
          message.createMessageSuccess(result?.message || "保存成功");
          // 判断是否修改商城类型
          if (model.value.key === SystemEnum.STO_MARKETPLACE_TYPE) {
            await systemStore._refreshStystemGlobalConfig();
          }
          show.value = false
          save_loading.value = false
          emit("tableSearch");
        }
      }catch(err){
        message.createMessageError(err || "保存失败")
        save_loading.value = false
      }
    });
  }
};

// 使用 computed 动态获取描述
const desc = computed(() => {
  const list = selectList[model.value.key]; // 获取当前 key 对应的列表
  if (Array.isArray(list)) {
    const matchedItem = list.find(item => item.value === model.value.value);
    return matchedItem ? matchedItem.desc : false;
  }
  return false; // 如果列表不存在或者不是数组，返回 false
});

const limitInput100 = computed(() => {
  if(model.value.key === 'sto_doctor_consultation_msg_template' || model.value.key === 'sto_pres_payment_notification_template') {
    return 100
  } else {
    return undefined
  }
   
})
onMounted(() => {});
defineExpose({ showModal });
</script>

<template>
  <n-modal
    v-model:show="show"
    :auto-focus="false"
    style="width: 600px"
    title="编辑配置值"
    :bordered="false"
    size="small"
    :closable="true"
    @after-leave="closeModal"
    preset="card"
  >
  <n-space vertical>
    <n-spin :show="isLoading">
      <n-scrollbar style="max-height: 500px">
      <n-form
        ref="formRef"
        :model="model"
        :rules="formRulesRef"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
        size="small"
        :style="{
          width: '100%',
        }"
      >
        <n-grid :cols="12" :x-gap="12">
          <n-form-item-gi :span="11" label="配置名" path="configName">
            {{model.configName}}
          </n-form-item-gi>
          <n-form-item-gi :span="11" label="配置值" path="value">
            <n-radio-group v-if="model.type===5 && model.key != 'sto_allocation_account_rule' " v-model:value="model.value" name="active">
              <n-space>
                <n-radio v-for="song in radioList[model.key]??radio" :key="song.value" :value="song.value">
                  {{ song.label }}
                </n-radio>
              </n-space>
            </n-radio-group>

            <n-radio-group 
              v-model:value="model.value" 
              v-else-if="model.type===5 && model.key == 'sto_allocation_account_rule'"
            >
              <n-space style="display: flex;flex-flow:inherit">
                <n-radio :class="index == 0 ? 'radioWidth' : ''" v-for="(song,index) in radioList[model.key]" :key="song.value" :value="song.value">
                  {{ song.label }}
                </n-radio>
              </n-space>
            </n-radio-group>

            <n-select v-else-if="model.type===8" v-model:value="model.value" :options="selectList[model.key]"/>
            <div v-else-if="model.type===10" style="width: 100%;">
              <TextUpload v-model:value="model.value" title="+上传PDF文档" listType="text" accept=".pdf" :fileListSize="2" :max="2"/>
            </div>

            <div v-else-if="model.type=== 2 && model.key == 'sto_allocation_account_action_time'">
              <span>{{ '订单状态变更为已完成后的第 ' }}</span>
              <n-input v-model:value="model.value" @input="(value)=>model.value=value.trim()" style="width: 70px;"/>
              <span >{{ ' 天,上午8点。' }}</span>
            </div>
            <n-input v-else v-model:value="model.value" @input="(value)=>model.value=value.trim()" :maxlength="limitInput100" :show-count="limitInput100" placeholder="请输入配置值"/>
            
          </n-form-item-gi>
          <n-form-item-gi :span="11" label="描述" path="desc">
            <div v-html="desc ? desc : model.desc"></div>
        </n-form-item-gi>
        </n-grid>
      </n-form>
    </n-scrollbar>
    </n-spin>
  </n-space>
   
    <template #footer>
      <n-space justify="end">
        <n-button size="small" @click="show = false" class="store-button">取 消</n-button>
        <n-button  size="small" type="primary" :disabled="isLoading" :loading="save_loading"  @click="_save" class="store-button">保 存</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<style scoped lang="less">
.radioWidth{
  width: 80px;
}
</style>
