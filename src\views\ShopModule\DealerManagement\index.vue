<template>
  <div class="dealer-management-page inner-page-height">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :pagination="paginationRef"
      @paginationChange="paginationChange"
      :isNeedCollapse="false"
      :isTableSelection="false"
      :isDisplayIndex="false"
    >
      <!-- 表单 -->
      <template #searchForm>
        <n-form
          ref="formRef"
          label-placement="left"
          label-width="auto"
          :show-feedback="false"
          require-mark-placement="right-hanging"
          size="small"
          :style="{ width: '100%' }"
        >
          <n-form-item :span="12" label="经销商姓名" path="">
            <j-search-input
              width="270px"
              v-model:value.trim="model.thirdDealerNameOrId"
              placeholder="请输入经销商姓名或ID"
              @search="handlerSearch"
            />
          </n-form-item>
        </n-form>
      </template>

      <template #tableHeaderBtn>
        <n-button v-if="hasDealerCodeAuth" type="primary" @click="handleOpenDealerCode">经销商注册码</n-button>
      </template>
    </FormLayout>
    <DealerCode ref="dealerCodeRef" />
    <ShopCode ref="shopCodeRef" />
  </div>
</template>

<script lang="tsx" setup>
import { computed, onMounted, ref, watch } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { getShopDealerPage } from "@/services/api";
import { useMessages } from "@/hooks";
import { hasShopCodeAuth, hasDealerCodeAuth } from "./authList";
import { useRouter } from "vue-router";
import { RoutesName } from "@/enums/routes";
import DealerCode from "./components/DealerCode.vue";
import ShopCode from "./components/ShopCode.vue";
const { createMessageSuccess, createMessageError } = useMessages();
/** 表格hook */
const { isLoading, tableData, paginationRef, pageTableData, paginationChange } = useTableDefault({
  pageDataRequest: getShopDealerPage,
});
const router = useRouter();
/* 表格列表项 */
const tableColumns = ref([
  {
    title: "归属经销商姓名/ID",
    key: "dealerName",
    align: "left",
    render: rowData => {
      return (
        <div>
          <div>{rowData?.dealerName ? rowData?.dealerName : "-"}</div>
          <div>用户ID：{rowData?.shortCsId ? rowData?.shortCsId : "-"} </div>
        </div>
      );
    },
  },
  {
    title: "联系人电话",
    key: "dealerPhone",
    align: "left",
  },
  {
    title: "归属区域名称/ID",
    key: "dealerName",
    align: "left",
    render: rowData => {
      return (
        <div>
          <div>{rowData?.regionName ? rowData.regionName : "-"} </div>
          <div>组织ID：{rowData?.regionShortId ? rowData.regionShortId : "-"}</div>
          <div>用户ID：{rowData?.regionShortCsId ? rowData.regionShortCsId : "-"}</div>
        </div>
      );
    },
  },
  {
    title: "门店数量",
    key: "storeCount",
    align: "left",
    render: rowData => {
      return (
        <n-button button text type="primary" onClick={() => handleToShopManagement(rowData)}>
          {rowData?.storeCount ? rowData.storeCount : 0}
        </n-button>
      );
    },
  },
  {
    title: "创建时间",
    key: "createTime",
    align: "left",
  },
  {
    title: "操作",
    key: "action",
    fixed: "right",
    align: "left",
    render: rowData => {
      return (
        <n-space align="center" justify="center">
          {hasShopCodeAuth ? (
            <n-button button text type="primary" onClick={() => handleOpenShopCode(rowData)}>
              门店注册码
            </n-button>
          ) : null}
        </n-space>
      );
    },
  },
]);
const dealerCodeRef = ref(null);
const shopCodeRef = ref(null);
/** 参数 */
const model = ref({
  thirdDealerNameOrId: "",
});

/** 获取参数 */
const getParams = () => {
  const { thirdDealerNameOrId } = model.value;
  return {
    thirdDealerNameOrId,
  };
};

const handleToShopManagement = rowData => {
  router.push({
    name: RoutesName.ShopManagement,
    query: {
      dealerShortId: rowData?.shortCsId,
    },
  });
};
const handleOpenDealerCode = () => {
  dealerCodeRef.value.acceptParams();
};
const handleOpenShopCode = row => {
  shopCodeRef.value.acceptParams(row);
};
/** 搜索 */
const handlerSearch = () => {
  tableSearch();
};

/** 表格刷新 */
function refresh() {
  tableSearch();
}

/* 表格搜索 */
const tableSearch = () => {
  pageTableData(getParams(), paginationRef.value);
};

/** 组件挂载 */
onMounted(() => {
  tableSearch();
});
</script>

<style lang="less" scoped>
@import "@/styles/defaultVar.less";
</style>
