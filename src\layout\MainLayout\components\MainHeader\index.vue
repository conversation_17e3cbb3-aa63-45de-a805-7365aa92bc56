<template>
  <div class="main-header">
    <!-- Logo -->
    <SystemLogo />
    <n-grid :cols="24" :x-gap="24">
      <!-- 一级菜单 -->
      <n-gi :span="18">
        <div class="main-menu">
          <MainMenu />
        </div>
      </n-gi>
      <!-- 右侧操作栏 -->
      <n-gi :span="6">
        <div class="header-left">
          <!-- 菜单搜索 -->
          <GlobalSearch />
          <OnLineStatusChange />
          <!-- 系统角色 -->
          <UserAvatar />
        </div>
      </n-gi>
    </n-grid>
  </div>
</template>

<script setup lang="ts">
/** 相关组件 */
import MainMenu from "../MainMenu/index.vue";
import GlobalSearch from "./components/GlobalSearch/index.vue";
import UserAvatar from "./components/UserAvatar/index.vue";
import SystemLogo from "./components/SystemLogo/index.vue";
import OnLineStatusChange from '@/views/DoctorEndModule/IM/components/common/OnLineStatusChange/index.vue'

defineOptions({ name: "MainHeader" });

// const uploadSuccessIconRef = ref(null);
// const showUploadSuccessStatusRef = ref(false);

// addEvent(EventsBusKeys.ExportSuccess,()=>{
//   showUploadSuccessStatusRef.value = true
//   nextTick(()=>{
//     uploadSuccessIconRef.value.addEventListener("webkitAnimationEnd", ()=>{
//     showUploadSuccessStatusRef.value = false
//   })
//   })
// })
</script>

<style scoped lang="less">
@import "@/styles/defaultVar.less";

.main-header {
  height: @main-header-height;
  background: #ffffff;
  display: flex;
  width: 100%;
  box-sizing: border-box;
  padding: 0px 16px;
  padding-left: 17px;

  .main-menu {
    height: 100%;
    display: flex;
    align-items: center;
  }

  .header-left {
    height: 100%;
    display: flex;
    justify-content: end;
  }
}

/** 导出相关css */
// .uploadSuccess {
//   display: block;
//   width: 50px;
//   height: 50px;
//   background: url("@/assets/image/system/export/export-success-frame.png") 0 0 no-repeat;
//   background-size: 2200%;
//   animation: uploadSuccessIconGif steps(22) 2s;
// }
// .exportIconText{
//   padding-left: 4px;
//   font-size: 14px;
//   transition: font-size 0.2s;
// }
// .uploadSuccessText {
//   font-size: 16px;
//   color: #333;
//   animation: uploadSuccessTextShinning 2s;
// }
// .export-wrapper{
//   zoom:1;
//   transition: zoom 0.2s ease-in-out;
// }
// .export-success-wrapper{
//   position: relative;
//   z-index: 1000000;
//   // zoom:1.5;
// }

// @keyframes uploadSuccessIconGif {
//   0% {
//     background-position: 0%;
//   }
//   100% {
//     background-position: 100%;
//   }
// }

// @keyframes uploadSuccessTextShinning {
//   0% {
//     color:#333;
//   }
//   5%{
//     color:@primary-color;
//   }
//   6%{
//     color:#333;
//   }
//   21%{
//     color:@primary-color;
//   }
//   26%{
//     color:#333;
//   }
//   41%{
//     color:@primary-color;
//   }
//   46%{
//     color:#333;
//   }
//   61%{
//     color:@primary-color;
//   }
//   66%{
//     color:#333;
//   }
//   100% {
//     color:#333;
//   }
// }
</style>
