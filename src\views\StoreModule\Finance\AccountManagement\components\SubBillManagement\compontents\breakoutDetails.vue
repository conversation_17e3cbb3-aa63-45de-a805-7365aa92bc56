<template>
    <JDrawer
      v-model:show="drawerVisible"
      title="分账单详情"
      :isGetLoading = isGetLoading
      :isShowFooter="false"
      @after-leave="closeDrawer"
      to="#subBillManagement"
      :contents-list="[
        {
          name: '',
          slotName: 'order_info'
        },
        {
          name: '商品分账明细',
          slotName: 'commodity_sub-account_details'
        },
      ]"
    >
    <template #order_info>
        <n-form
          :model="model"
          label-width="100"
          label-placement="left"
          require-mark-placement="right-hanging"
          size="small"
          :style="{
            width: '100%',
          }"
          id="orderInfo"
        >
          <n-grid cols="6 m:18 l:18 xl:24" :x-gap="24" responsive="screen">
            <!-- 第一列 -->
            <n-gi :span="12">
              <!-- 分账单号(商户流水号) -->
              <n-form-item-gi :span="6" label="分账单号" placeholder='无'>
                <n-input-group>
                  <n-input v-model:value="model.allocationNo" readonly placeholder='无'/>
                </n-input-group>
              </n-form-item-gi>

              <!-- 订单编号 -->
              <n-form-item-gi :span="12" label="订单编号" >
               <n-input-group>
                 <n-input v-model:value="model.orderCode" readonly placeholder='无'/>
               </n-input-group>
              </n-form-item-gi>

              <!-- 支付渠道 -->
              <n-form-item-gi :span="12" label="支付渠道">
                <n-input-group>
                  <n-select  v-model:value="model.payPlatform"
                  :options=paymentMethodsOptions 
                  placeholder='无'
                  style="width: 100%;"
                  clearable
                  disabled/>
                </n-input-group>
              </n-form-item-gi>

              <!-- 支付商户号 -->
              <n-form-item-gi :span="12" label="支付商户号">
                <n-input-group>
                  <n-input v-model:value="model.paymentMerchantId" readonly placeholder='无'/>
                </n-input-group>
              </n-form-item-gi>

              <!-- 归属经销商姓名 -->
              <n-form-item-gi :span="12" label="归属经销商姓名">
               <n-input-group>
                 <n-input v-model:value="model.dealerName" readonly placeholder='无'/>
               </n-input-group>
              </n-form-item-gi>

              <!-- 经销商id -->
              <n-form-item-gi :span="12" label="经销商id">
               <n-input-group>
                 <n-input v-model:value="model.accountUserId" readonly placeholder='无'/>
               </n-input-group>
              </n-form-item-gi>

              <!-- 结算状态 -->
              <n-form-item-gi :span="12" label="结算状态">
                <n-select 
                 v-model:value="model.status" 
                 :options="settlementStatusSecondOptions" 
                 placeholder='无'
                 style="width: 100%;" 
                 clearable
                 disabled
                />
              </n-form-item-gi>
              <!-- 取消结算原因 -->
              <n-form-item-gi :span="12" label="取消结算原因" v-if="model.cancelReason">
                <n-input-group>
                  <n-input 
                   type="textarea"  
                   :value="model.cancelReason" 
                   readonly  
                   placeholder='无' 
                   :autosize="{ minRows: 2}"/>
                </n-input-group>
              </n-form-item-gi>

              <!-- 打款状态 -->
              <n-form-item-gi :span="12" label="打款状态">
                <n-select 
                 v-model:value="model.settlementStatus" 
                 :options="payoutsStatusSecondOptions" 
                 placeholder='无'
                 style="width: 100%;" 
                 clearable
                 disabled
                />
              </n-form-item-gi>

              <!-- 订单支付金额 -->
              <n-form-item-gi :span="12" label="订单支付金额">
                {{ model.orderTotalAmount}}
              </n-form-item-gi>
              <!-- 商品总金额 -->
              <n-form-item-gi :span="12" label="商品总金额">
                {{ model.productTotalAmount}}
              </n-form-item-gi>
              <!-- 商品总成本 -->
              <n-form-item-gi :span="12" label="商品总成本" v-if="model.allocationRule == SubAccountPayeeRuleSettingEnum.Order" >
                {{ model.productCost}}
              </n-form-item-gi>
              <!-- 用户支付的运费 -->
              <n-form-item-gi :span="12" label="用户支付的运费" v-if="model.allocationRule == SubAccountPayeeRuleSettingEnum.Order">
                {{ model.shippingFee}}
              </n-form-item-gi>
              <!-- 订单线下核算成本 -->
              <n-form-item-gi :span="12" label="订单线下核算成本" v-if="model.allocationRule == SubAccountPayeeRuleSettingEnum.Order">
                {{ model.cost}}
              </n-form-item-gi>
              <!-- 分账比例 -->
              <n-form-item-gi :span="12" label="分账比例" v-if="model.allocationRule == SubAccountPayeeRuleSettingEnum.Order">
                {{ model.allocationRatio }}%
              </n-form-item-gi>
              <!-- 分账金额 -->
              <n-form-item-gi :span="12" label="分账金额">
                {{ model.allocationTotalAmount}}
              </n-form-item-gi>
              <!-- 分账规则 -->
              <n-form-item-gi :span="12" label="分账规则">
                <n-input-group>
                  <n-input 
                   type="textarea"  
                   :value="allocationRuleText" 
                   readonly  
                   placeholder='无' 
                   :autosize="{ minRows: 2}"/>
                </n-input-group>
              </n-form-item-gi>

              <!-- 订单创建时间 -->
              <n-form-item-gi :span="12" label="订单创建时间">
               {{model.orderCreateTime}}
              </n-form-item-gi>
              <!-- 订单支付时间 -->
              <n-form-item-gi :span="12" label="订单支付时间">
               {{model.orderPaidTime}}
              </n-form-item-gi>
              <!-- 订单完成时间 -->
              <n-form-item-gi :span="12" label="订单完成时间">
               {{model.orderCompleteTime}}
              </n-form-item-gi>
              <!-- 取消结算时间 -->
              <n-form-item-gi :span="12" label="取消结算时间" v-if="model.cancelTime">
               {{model.cancelTime}}
              </n-form-item-gi>
            </n-gi>
          </n-grid>
        </n-form>
    </template>
    <template #commodity_sub-account_details>
        <FormLayout
          style="width: 98%;height: 400px;"
          :tableData="tableData" 
          :tableColumns="tableColumns"
          :is-table-pagination="false"
          :is-table-selection="false"
          :is-display-header="false"
          :isDisplayIndex="false"
          :totalRendering="false"
        >
        </FormLayout>
    </template>
    </JDrawer>
</template>
  
<script setup lang="tsx" name="breakoutDetails">
import { ref, reactive, computed } from "vue";
import { useMessages } from "@/hooks";
import { deepClone } from "@/utils";
import FormLayout from "@/layout/FormLayout.vue";
import { settlementStatusSecondOptions ,paymentMethodsOptions, payoutsStatusSecondOptions } from "@/constants";
import { allocationGetDetail } from "@/services/api";
import { SubAccountPayeeRuleSettingEnum } from "@/enums";

/* 表单参数初始化 */
const initParams = {
    allocationNo:null,
    orderCode:null,
    dealerName:null,
    accountUserId:null,
    status:null,
    orderCreateTime:null,
    orderPaidTime:null,
    orderCompleteTime:null,

    orderTotalAmount:null,
    productTotalAmount:null,
    productCost:null,
    shippingFee:null,
    cost:null,
    allocationTotalAmount:null,
    payPlatform:null,
    paymentMerchantId:null,
    settlementStatus:null,
    cancelReason:null,
    cancelTime:null,
    allocationRule:null,
    allocationRatio:null
}

/** 分账规则 */
const allocationRuleText = computed(() => {
  switch(model.value.allocationRule){
    case SubAccountPayeeRuleSettingEnum.Commodity:
      return '按商品分账，分账金额 = 订单中每个商品分账的总额，每个商品分账的金额 =（该商品总额 - 该商品总成本价）* 该商品经销商分账比例'
    case SubAccountPayeeRuleSettingEnum.Order:
      return '按订单分账，分账金额 = 【订单总额 - 商品总成本 - 用户支付的运费 - 订单线下核算成本 】* 分账比例'
      
  }
})

const model = ref(deepClone(initParams));

const {createMessageError } = useMessages();

const tableData = ref([])

  /* 表格项 */
  const tableColumns = computed(() => [
    {
      title: "商品id",
      key: "productId",
      align: "left",
      fixed: "left",
      width: 100
    },
    {
      title: "商品名称",
      key: "productName",
      align: "left",
      width: 100,
    },
    {
      title: "商品规格",
      key: "productSpecName",
      align: "left",
      fixed: "left",
      width: 100,
     
    },
    {
      title: "销售单价(元)",
      key: "unitPrice",
      align: "left",
      fixed: "left",
      width: 100,
      render: (row) => {
        return  row.unitPrice ? formatPrice(row.unitPrice) : '0.00'
      }
    },
    {
      title: "数量",
      key: "number",
      align: "left",
      fixed: "left",
      width: 100,
    },
    {
      title: "总金额(元)",
      key: "totalAmount",
      align: "left",
      fixed: "left",
      width: 100,
      render: (row) => {
        return  row.totalAmount ? formatPrice(row.totalAmount) : '0.00'
      }
    },
    {
      title: "成本价(元)",
      key: "costPrice",
      align: "left",
      fixed: "left",
      width: 100,
      render: (row) => {
        return  row.costPrice ? formatPrice(row.costPrice) : '0.00'
      }
    },
    {
      title: "总成本(元)",
      key: "totalCost",
      align: "left",
      fixed: "left",
      width: 100,
      render: (row) => {
        return  row.totalCost ? formatPrice(row.totalCost) : '0.00'
      }
    },
    ...(
      model.value.allocationRule == SubAccountPayeeRuleSettingEnum.Order ? [] : [
        {
          title: "经销商分账比例",
          key: "dealerAllocationRatio",
          align: "left",
          fixed: "left",
          width: 100,
          render: (row) => {
            return  row.dealerAllocationRatio ? (row.dealerAllocationRatio + '%') : '0%'
          }
        },
        {
          title: "经销商分账金额(元)",
          key: "dealerAllocationAmount",
          align: "left",
          fixed: "left",
          width: 100,
          render: (row) => {
            return row.dealerAllocationAmount ? formatPrice(row.dealerAllocationAmount) : '0.00'
          }
        }
      ]
    )
    
]) 

/** 定义一个函数来格式化价格 */
const formatPrice = (price) => `${(price / 100).toFixed(2)}`;

/** 抽屉状态 */
const drawerVisible = ref(false);

/* 接收父组件传过来的参数 */
const drawerProps = ref()
const acceptParams = async(param) => {
  drawerVisible.value = true;
  drawerProps.value = param
  allocationGetDetailData(drawerProps.value.row?.id)
};

/** 关闭抽屉 */
const closeDrawer = () => {
    model.value = deepClone(initParams);
    drawerVisible.value = false;
};

/** 获取详情 */
const isGetLoading = ref(false)
const allocationGetDetailData = async(id) =>{
  isGetLoading.value = true
  try{
   const res =  await allocationGetDetail(id)
   const {
    allocationNo,
    orderCode,
    dealerName,
    accountUserId,
    status,
    orderCreateTime,
    orderPaidTime,
    orderCompleteTime,
    allocationItemList,
    orderTotalAmount,
    productTotalAmount,
    allocationTotalAmount,
    payPlatform,
    paymentMerchantId,
    settlementStatus,
    cancelReason,
    cancelTime,
    productCost,
    shippingFee,
    cost,
    allocationRule,
    allocationRatio
    } = res
    // 直接赋值的字段
    Object.assign(model.value, {
      allocationNo,
      orderCode,
      dealerName,
      accountUserId,
      status,
      payPlatform,
      paymentMerchantId,
      settlementStatus:settlementStatus ? Number(settlementStatus) : null,
      cancelReason,
      cancelTime,
      allocationRule,
      allocationRatio
    });
    
    // 条件处理的字段
    const fieldsWithFallback = {
      orderCreateTime: orderCreateTime || '-',
      orderPaidTime: orderPaidTime || '-',
      orderCompleteTime: orderCompleteTime || '-',
    };
    
    const priceFields = {
      orderTotalAmount: orderTotalAmount ? `￥${formatPrice(orderTotalAmount)}` : '￥0.00' ,
      productTotalAmount:productTotalAmount ? `￥${formatPrice(productTotalAmount)}` : '￥0.00',
      allocationTotalAmount: allocationTotalAmount ? `￥${formatPrice(allocationTotalAmount)}` : '￥0.00',
      productCost: productCost ? `￥${formatPrice(productCost)}` : '￥0.00',
      shippingFee: shippingFee ? `￥${formatPrice(shippingFee)}` : '￥0.00',
      cost: cost ? `￥${formatPrice(cost)}` : '￥0.00',
     };

    // 合并所有字段到 model.value
    Object.assign(model.value, fieldsWithFallback, priceFields);

    tableData.value = allocationItemList
  }catch(err){
    createMessageError('获取详情失败:' + err)
  }finally{
    isGetLoading.value = false
  }
}

defineExpose({
  acceptParams,
  closeDrawer,
  drawerVisible
});
</script>
  
<style scoped lang="less">
@import "@/styles/default.less";
@import "@/styles/defaultVar.less";
.wrapper {
  width: 100%;
  height: 100%;
  background-color: #f2f3f5;

  .publics-form {
    width: 100%;
    height: calc(100% - 52px);
    box-sizing: border-box;
    background-color: #fff;
    .order-info,
    .user-info,
    .order-info,
    .order-item {
      padding: 12px 24px;
    }
    .title-wrapper {
      height: 30px;
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: 700;
      margin-bottom: 12px;
      .title-line {
      	width: 4px;
      	height: 60%;
      	background-color: @primary-color;
      	margin-right: 5px;
      }
    }
  }
}

:deep(.n-form-item-label){
    width: 150px !important;
}
</style>
  