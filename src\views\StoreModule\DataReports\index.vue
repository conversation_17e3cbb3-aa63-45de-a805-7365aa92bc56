<template>
    <MenuLayout v-model:activeKey="activeTypeRef" :menuOptions="menuList">
      <component :is="currentPage" />
    </MenuLayout>
  </template>

  <script setup lang="tsx">
  import { ref, computed } from "vue";
  // import { useSvgIcon } from "@/hooks";
  import { DataReportsType, type ReportsType } from "@/enums";
  import { hasCoursePageAuth, hasDealerPageAuth, hasGroupPageAuth,hasReportsOrderAuth, hasProductPageAuth, hasStoreDataReportAuth, hasDistributorDataReportAuth, hasReportsCustomerAuth } from "./authList";
  /** 相关组件 */
  import MenuLayout from "@/components/MenuLayout/index.vue";
  import CourseTotalReport from "./CourseTotalReport/index.vue";
  // import CustomerTotalReport from "./CustomerTotalReport/index.vue"; ******* 去掉了客户统计
  import DealerTotalReport from "./DealerTotalReport/index.vue";
  // import GoodsTotalReport from "./GoodsTotalReport/index.vue"; ******* 去掉了商品统计
  import GroupMgrTotalReport from "./GroupMgrTotalReport/index.vue";
  import OrderTotalReport from "./OrderTotalReport/index.vue";
  import CommoditySalesReport from "./CommoditySalesReport/index.vue";
  import MemberConsumptionReport from "./MemberConsumptionReport/index.vue";
  import StoreDataReport from "./StoreDataReport/index.vue";
  import DistributorDataReport from "./DistributorDataReport/index.vue";

  const activeTypeRef = ref<ReportsType>(DataReportsType.ALLPLATFORMORDER);
  
  /** 数据报表 tab */
  const menuList = ref([
    {
      label: '全平台订单',
      key: DataReportsType.ALLPLATFORMORDER,
      show: hasReportsOrderAuth
    },
    {
      label: '门店数据统计',
      key: DataReportsType.STORESTATISTICS,
      show: hasStoreDataReportAuth
    },
    {
      label: '经销商数据统计',
      key: DataReportsType.DEALERDATASTATISTICS,
      show: hasDistributorDataReportAuth
    },
    {
      label: '商品销售榜',
      key: DataReportsType.COMMODITYSALES,
      show: hasProductPageAuth
    },
    {
      label: '会员消费榜',
      key: DataReportsType.MEMBERCONSUMPTION,
      show: hasReportsCustomerAuth
    },
    {
      label: '社群群管销量',
      key: DataReportsType.COMMUNITYSALES,
      show: hasGroupPageAuth
    },
    {
      label: '社群经销商销量',
      key: DataReportsType.COMMUNITYDEALERSALES,
      show: hasDealerPageAuth
    },
    {
      label: '社群课程销量',
      key: DataReportsType.COMMUNITYCOURSES,
      show: hasCoursePageAuth
    },
  ]);

  /** 相关组件 */
  const pageMap = {
    /** 全平台订单 */
    [DataReportsType.ALLPLATFORMORDER]: OrderTotalReport,
    /** 门店数据统计 */
    [DataReportsType.STORESTATISTICS]: StoreDataReport,
    /** 经销商数据统计 */
    [DataReportsType.DEALERDATASTATISTICS]: DistributorDataReport,
    /** 商品销售榜 */
    [DataReportsType.COMMODITYSALES]: CommoditySalesReport,
    /** 会员销售榜 */
    [DataReportsType.MEMBERCONSUMPTION]: MemberConsumptionReport,
    /** 社群群管销量 */
    [DataReportsType.COMMUNITYSALES]: GroupMgrTotalReport,
    /** 社群经销商销量 */
    [DataReportsType.COMMUNITYDEALERSALES]: DealerTotalReport,
    /** 社群课程销量 */
    [DataReportsType.COMMUNITYCOURSES]: CourseTotalReport,
  }

  /** 当前页 */
  const currentPage = computed(() => pageMap[activeTypeRef.value])
  </script>
  
  <style lang="less" scoped></style>