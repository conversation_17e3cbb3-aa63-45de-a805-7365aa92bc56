export const enum Cache_Key {
  Token = "jw-store-token",
  UserInfo = "jw-store-userinfo",
  RouteConfig = "jw-store-route-config",
  System = "jw-store-system",
  OptList = 'jw-store-opt-list',
  SearchMenus = 'jw-store-search-menus',
  AuthList = 'jw-store-auth-list',
  GlobalConfig = 'jw-store-global-config',
  ImConfig = 'jw-store-im-Config',
  CouponTypeList = 'jw-store-coupon-type-list',
}
export const enum StorageType {
  LOCAL = "local",
  SESSION = "session",
}
