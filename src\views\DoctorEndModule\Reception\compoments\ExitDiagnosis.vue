<template>
  <JModal
      v-model:show="show"
      width="680"
      title="退诊"
      @after-leave="closeModal"
      @positive-click="_save"
      :positiveButtonProps="{
			loading: isLoading
		}"
  >
    <n-form
        ref="formRef"
        :rules="rules"
        :model="model"
        label-width="auto"
        label-placement="left"
        require-mark-placement="right-hanging"
        :style="{
          width: '100%',
        }"
    >
      <n-grid :cols="8" :x-gap="24">
        <n-form-item-gi :span="8" label="退诊原因" path="cancelReason">
          <n-input v-model:value="model.cancelReason" placeholder="最多100个字" maxlength="100"/>
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </JModal>
</template>

<script setup lang="ts">
import {ref} from "vue";
import {useMessages} from "@/hooks";
import {isFunction} from "@/utils";

const initParams = {
  cancelReason: ''
};
const model = ref({...initParams});

export interface AddCompanyModalProps {
  refreshTable?: () => void; // 刷新表格数据
}

/* 提示信息 */
const message = useMessages();
/* 模态框显隐状态 */
const show = ref(false);

/* 表单规则 */
const rules = {
  cancelReason: {
    required: true,
    trigger: ["blur", "change"],
    message: "请输入退诊原因",
    validator: () => {
      return model.value.cancelReason != '';
    }
  },
};

// 退诊api
let cancelDiagnosisApi = null
// 行数据
let rowData = null;
let refreshFunction = null
const acceptParams = (params) => {
  parameter.value.refreshTable = params.refreshTable
  show.value = true
  cancelDiagnosisApi = params.api || null
  rowData = params.rowData || null
  refreshFunction = params.refresh
};
/* 表单实例 */
const formRef = ref(null);
/* 表单参数初始化 */
/* 清空表单 */
const formDataReset = () => {
  model.value = {...initParams};
  cancelDiagnosisApi = null
  rowData = null
};
const parameter = ref<AddCompanyModalProps>({});
/* 关闭弹窗 */
const closeModal = () => {
  formDataReset();

  isLoading.value = false;
  // 弹窗取消
  show.value = false;
};
/* 确认--保存 */
const isLoading = ref(false);
const _save = (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors && !isLoading.value) {
      try {
        isLoading.value = true;
        const params = {
          id: rowData.id,
          cancelReason: model.value.cancelReason
        }
        await cancelDiagnosisApi(params)
        message.createMessageSuccess('退诊成功')
        if (isFunction(refreshFunction)) {
          refreshFunction()
        }
      } catch (e) {
        message.createMessageError(`退诊失败： ${e}`);
        isLoading.value = false;
      } finally {
        isLoading.value = false
        show.value = false
      }
    }
  });
};
defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less">
::v-deep .n-form-item-label {
  width: 70px !important;
}
</style>

