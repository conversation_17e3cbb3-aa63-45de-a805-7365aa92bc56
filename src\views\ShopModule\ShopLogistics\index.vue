<template>
    <div class="shop-logistics-page inner-page-height">
      <FormLayout
        :isLoading="isLoading"
        :tableData="tableData"
        :tableColumns="tableColumns"
        :pagination="paginationRef"
        @paginationChange="paginationChange"
        :isNeedCollapse="false"
        :isTableSelection="true"
        :isDisplayIndex="false"
        @selectedKeysChange="onTableSelectedKeysChange"
      >
        <!-- 表单 -->
        <template #searchForm>
          <n-form
            ref="formRef"
            label-placement="left"
            label-width="auto"
            :show-feedback="false"
            require-mark-placement="right-hanging"
            size="small"
            :style="{ width: '100%' }"
          >
            <n-form-item label="店铺名称" label-placement="left">
                <JStoreSelect width="100%" v-model:value="model.storeId" placeholder="请选择店铺"></JStoreSelect>
            </n-form-item>
            <n-form-item :span="12" label="商品名称">
                <JSearchInput v-model:value="model.productName" style="width: 100%" maxlength="40" placeholder="请输入商品名称" @search="handlerSearch" />
            </n-form-item>
            <n-form-item :span="24" label="商品ID">
              <JSearchInput v-model:value="model.productId" style="width: 100%" maxlength="40" placeholder="请输入商品ID" @search="handlerSearch"/>
            </n-form-item>
            <n-form-item :span="24" label="SKU名称">
              <JSearchInput v-model:value="model.sku" style="width: 100%" maxlength="40" placeholder="请输入SKU名称" @search="handlerSearch"/>
            </n-form-item>
          </n-form>
        </template>
  
        <template #tableHeaderBtn>
          <n-button @click="refresh" class="store-button">刷 新</n-button>
          <n-button v-if="hasCreatAuth" type="primary" @click="handleAddLogisticsOrder">
            创建物流单
          </n-button>
        </template>
         <!-- 表格底部按钮 -->
        <template v-if="hasBatchDeleteAuth" #tableFooterBtn="scope">
          <n-popconfirm
            @positive-click="handleBatchDelete(selectedKeysReactive.selectedKeys,refresh)"
            :positive-button-props="{
              loading: isDeleteLoading
          }"
          >
          <template #trigger>
            <n-button ghost type="error" size="small">批量删除</n-button>
          </template>
          此操作将删除选中的商品，是否继续？
        </n-popconfirm>
        </template>
      </FormLayout>
      <AddLogisticsOrderModal 
        v-model:show="isAddShow"
        @refresh="refresh" 
        @onSubmit="handleAddFn" 
        :isAddLoading="isAddLoadingRef"
        :modalType="modalType"
        :row="selectRow"
      />
      <LogisticsTracesModal  v-model:show="isLogShow" :rowData="selectRow" v-if="isLogShow"></LogisticsTracesModal>
    </div>
  </template>
  
<script lang="tsx" setup>
import { reactive, onMounted, ref, watch } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { getLogisticsPage } from "@/services/api";
import { useMessages } from "@/hooks";
import { transformMinioSrc } from "@/utils";
import moment from "moment";
import AddLogisticsOrderModal from "./components/AddLogisticsOrderModal.vue";
import LogisticsTracesModal from "./components/LogisticsTracesModal.vue";
/** 权限 */
import {
  hasCreatAuth,
  hasUpdateAuth,
  hasLogisticsAuth,
  hasDeleteAuth,
  hasBatchDeleteAuth
} from "./authList";
import { useRouter } from "vue-router";
import { RoutesName } from "@/enums/routes";
import JStoreSelect from '@/components/JSelect/JStoreSelect.vue'
import { logisticsOrder } from './hooks/logisticsOrder'
/** 表格hook */
const { isLoading, tableData, paginationRef, pageTableData, paginationChange } = useTableDefault({
  pageDataRequest: getLogisticsPage,
});
const { 
  onCouponBatchFn,
  isAddLoadingRef,
  handleBatchDelete,
  handleDelete,
  isDeleteLoading
} = logisticsOrder(); 
const router = useRouter();
const isAddShow = ref(false)
const isLogShow = ref(false)
const selectRow = ref({})
const selectRowId = ref('')
/* 表格列表项 */
const tableColumns = ref([
  {
    title: "店铺ID",
    key: "storeId",
    align: "left",
  },
  {
    title: "店铺名称",
    key: "storeName",
    align: "left",
  },
  {
    title: "商品ID",
    key: "productId",
    align: "left",
  },
  {
    title: "商品名称",
    key: "productName",
    align: "left",
  },
  {
    title: "SKU名称",
    key: "sku",
    align: "left",
  },
  {
    title: "发货数量",
    key: "deliveryQuantity",
    align: "left",
  },
  {
    title: "物流公司",
    key: "logisticsCompany",
    align: "left",
  },
  {
    title: "物流单号",
    key: "logisticsNumber",
    align: "left",
  },
  {
    title: "操作",
    key: "action",
    fixed: "right",
    align: "left",
    render: (row, index) => {
      return (
        <n-space style="padding: 5px 0;">
          {
            hasLogisticsAuth() ?
            <n-button text type="primary" onClick={() => handlerlookLog(row)}>
                查看物流
          </n-button>:null
          }
         {
          hasUpdateAuth() ? 
          <n-button text type="primary" onClick={() => handlerCreateOrEdit(row)}>
                编辑
          </n-button> : null
         }
         {
          hasDeleteAuth() ?
          <n-popconfirm onPositiveClick={() => handleDelete(row?.id,refresh)}>
            {{
              trigger: () => (<n-button type='error' text>删除</n-button>),
              default: () => <span>是否确定删除该数据？</span>
            }}
          </n-popconfirm> : null
         }
          
      </n-space>
      )
    },

  },
]);
const modalType = ref('')
/** 参数 */
const model = ref({
  storeId:null,
  productName: "",
  productId: "",
  sku: "",
});

/** 获取参数 */
const getParams = () => {
  const { storeId,productName,productId,sku } = model.value;
  
  return {
    storeId,productName,productId,sku
  };
};
const selectedKeysReactive = reactive({
    selectedKeys: [],
    selectedOptions: [],
});
function onTableSelectedKeysChange(selectedKeys: Array<string>, options: Array<any>) {
    selectedKeysReactive.selectedKeys = selectedKeys;
    selectedKeysReactive.selectedOptions = options;
}
const distributeWelfareVoucherModalRef = ref();
const distributeWelfareVoucher = id => {
  distributeWelfareVoucherModalRef.value.acceptParams(id);
};
const handlerlookLog = (row) =>{
  isLogShow.value = true
  selectRow.value = row
}
const handleAddLogisticsOrder = () => {
  isAddShow.value = true
  modalType.value = 'add'
};
const handlerCreateOrEdit = rowData => {
  isAddShow.value = true
  modalType.value = 'edit'
  selectRow.value = rowData
};
const handleCheckOrder = rowData => {
  router.push({
    name: RoutesName.OrderManagement,
    query: {
      shopId: "666",
    },
  });
};
const handleAddFn = (params) =>{
    onCouponBatchFn(params.type,params.data,refresh)
}
const handleToCustomerManagement = row => {
  router.push({
    name: RoutesName.CustomerManagement,
    query: {},
  });
};
/** 搜索 */
const handlerSearch = () => {
  tableSearch();
};

/** 表格刷新 */
function refresh() {
  isAddShow.value = false
  tableSearch();
}

/* 表格搜索 */
const tableSearch = () => {
  pageTableData(getParams(), paginationRef.value);
};

/** 监听 */
watch(
  () => [model.value.storeId],
  () => {
    tableSearch();
  },
);

/** 组件挂载 */
onMounted(() => {
  tableSearch();
});
</script>

<style lang="less" scoped>
@import "@/styles/defaultVar.less";
</style>
