<template>
    <JModal
    v-model:show="isShow"
    width="680"
    :title="Add_active?'新增病种':'编辑病种'"
    @after-leave="closeModal"
		@positive-click="_save"
		:positiveButtonProps="{
			loading: isLoading
		}"
  >
  <div style="color: #999;margin-bottom: 10px;" >建议输入ICD10国临版病种名称和编码</div>
  <n-form
      ref="formRef"
      :rules="rules"
      :model="model"
      label-width="auto"
      label-placement="left"
      require-mark-placement="right-hanging"
      :style="{
          width: '100%',
        }"
    >
      <n-grid :cols="8" :x-gap="24">
        <n-form-item-gi :span="24" label="病种名称" path="name" required>
          <n-input v-model:value="model.name" placeholder="请输入病种名称" :maxlength="128" clearable/>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="病种编码" path="code" required>
          <n-input v-model:value="model.code" placeholder="请输入病种编码" :maxlength="128" clearable/>
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </JModal>
</template>

<script setup lang="ts">
import { ref,computed,watch } from 'vue';
import { addDisease, editDisease } from '@/services/api';
import { useMessages } from "@/hooks";
const message = useMessages();

const props = withDefaults(defineProps<{
    Add_active: boolean;
    row?: any;
    show: boolean;
}>(), {
    Add_active: true,
    row: null,
    show: false,
});
const emits = defineEmits<{
    (e: 'update:show', value: boolean): void;
    (e: 'refresh'): void;
}>();

const isShow = computed({
    get: () => props.show,
    set: (value: boolean) => {
        emits('update:show', value);
    }
});

const initParams = {
  id:null,
  name: '',
  code: ''
};
const model = ref({ ...initParams });

/* 表单规则 */
const rules = {
  name:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入病种名称",
    validator: ()=>{
      return model.value.name != '';
    }
  },
  code:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入病种编码",
    validator: ()=>{
      return model.value.code != '';
    }
  },
};

const isLoading = ref(false);

// 关闭按钮
const closeModal = () => {
    isShow.value = false;
    model.value = { ...initParams };
}

watch(()=>props.show, ()=>{
  if(props.show){
    if(props.Add_active){
      model.value = { ...initParams };
    }else{
      Object.assign(model.value, props.row);
    }
  }
})

// 确认按钮
const formRef = ref(null);
const _save = () => {
    formRef.value?.validate((errors: any) => {
        if (!errors) {
            console.log('errors', errors);
            // TODO:调用接口
            const params = {
              data:{
              ...(props.Add_active ? {} : {id: model.value.id}),
              name: model.value.name,
              code: model.value.code,
            }
            }
            isLoading.value = true;
            const api = props.Add_active ? addDisease : editDisease;
            api(params).then((res: any) => {
              message.createMessageSuccess('操作成功');
              emits('refresh');
              closeModal();
            }).catch(err=>{
              message.createMessageError(`${err}`);
            }).finally(()=>{
              isLoading.value = false;
            })
        }
    });
}

</script>