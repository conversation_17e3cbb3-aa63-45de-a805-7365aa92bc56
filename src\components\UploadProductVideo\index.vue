<template>
  <n-upload
    v-bind="$attrs"
    :custom-request="customRequest"
    accept="video/mp4"
    v-model:file-list="fileList"
    @remove="fileRemoveHandler"
    @before-upload="beforeUpload"
  >
    <n-button text type="primary" size="medium">{{ props.value ? `重新上传` : `上传` }}</n-button>
  </n-upload>
</template>

<script lang="ts" setup>
import { ref, toRef, watch } from "vue";
import type { UploadCustomRequestOptions, UploadFileInfo } from "naive-ui";
import { useMessages } from "@/hooks";
import { getMediaDuration, transformMinioSrc } from "@/utils/fileUtils";
import { fileUpload } from "@/services/api";

defineOptions({ name: 'UploadProductVideo' })

/** props */
const props = withDefaults(defineProps<{
    value: Array<{
        img: string;
        path: string;
        type: 0 | 1;
    }> | null;
    maxFileSize?: number; // 文件大小
    maxVideoDuration?: number; // 视频时长 s
}>(), {
    maxFileSize: 200,
    maxVideoDuration: 60
});

/** Emits */
const emits = defineEmits<{
	(e: "update:value", urlList: Array<{
        img: string;
        path: string;
        type: 0 | 1;
    }>): void;
}>();

const { createMessageWarning, createMessageError } = useMessages();

const valueRef = toRef(props, "value");
const maxFileSizeRef = toRef(props, "maxFileSize");
const maxVideoDurationRef = toRef(props, "maxVideoDuration");
const fileList = ref<Array<UploadFileInfo>>([]); // 文件列表

/** 自定义上传方法 */
async function customRequest(options: UploadCustomRequestOptions) {
    const { file, onFinish, onError, onProgress } = options;
    const _formData = new FormData();
    _formData.append("files", file.file);
    try {
        const resp = await fileUpload(false, _formData, ({ progress }) => {
          onProgress({ percent: Number((progress * 100).toFixed(2)) });
        });
        onFinish();
        emits("update:value", [{
            img: file?.name ?? `未知.mp4`,
            path: resp[0],
            type: 1
        }]);
    } catch (error) {
        createMessageError(`上传失败: ${error}`);
		onError();
    }
}

/**
 * @description 文件删除回调
 */
 function fileRemoveHandler(options: {
  file: UploadFileInfo;
  fileList: Array<UploadFileInfo>;
}) {
  const { url, status, id } = options.file;
  // 如果文件正在上传，提示用户不要删除
  if (status === "uploading") {
    createMessageWarning("正在上传中，请勿删除");
    return false;
  }
  emits("update:value", null);
  return true;
};

/**
 * @description 文件上传之前回调
 */
async function beforeUpload(options: {
    file: UploadFileInfo;
    fileList: UploadFileInfo[];
}) {
    // 校验文件类型
    const allowedTypes = ['video/mp4'];
    const { file } = options.file;
    if (!allowedTypes.includes(file!.type)) {
        createMessageError('只能上传MP4格式的视频');
        return false;
    }

    // 校验视频大小
    if (maxFileSizeRef.value) {
        const { file } = options.file;
        if (file!.size > maxFileSizeRef.value * 1024 * 1024) {
            createMessageError(`视频大小不能超过${maxFileSizeRef.value} MB`);
            return false;
        }
    }
    // 校验视频时长
    if (maxVideoDurationRef.value) {
        // 获取视频时长 s
        let duration = await getMediaDuration(options.file.file as File);
        // 检查视频时长是否满足要求
        if (duration > maxVideoDurationRef.value) {
            // 如果不符合要求，给出警告并阻止上传
            createMessageError(`视频时长不能超过${maxVideoDurationRef.value} 秒`);
            return false;
        }
    }
    return true;
}

/** 监听 */
watch(
    valueRef,
    (newVal) => {
        const fileListTemp: Array<UploadFileInfo> = [];
        newVal = newVal ? newVal : [];
        newVal.forEach((item, index) => {
          if (item) {
            const _id = `${Math.random() * 1000}-${index}`;
            fileListTemp.push({
              id: _id,
              name: item.img,
              status: "finished",
              url: transformMinioSrc(item.path),
            });
          }
        });
        fileList.value = [...fileListTemp];
    },
    { immediate: true }
);
</script>
