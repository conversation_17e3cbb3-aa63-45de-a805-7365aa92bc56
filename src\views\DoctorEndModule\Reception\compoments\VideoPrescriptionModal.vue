<template>
  <n-modal
      v-model:show="props.show"
      preset="card"
      style="width: 1400px"
      :title="titleComputed"
      :bordered="false"
      :auto-focus="false"
      size="small"
      :closable="true"
      @after-leave="closeModal"
      @close="emits('update:show',false)"
      @mask-click="emits('update:show',false)"
  >
    <n-spin :show="isPageLoading">
      <n-form
          ref="formRef"
          :model="model"
          :rules="rules"
          label-width="auto"
          label-placement="left"
          require-mark-placement="right-hanging"
          size="small"
          :style="{width: '100%'}">
        <n-grid :cols="24" :x-gap="24">
          <n-form-item-gi :span="24" label="处方类型">
            <n-radio-group v-model:value="model.type" name="active" :disabled="props.mode !== 'add'">
              <n-space>
                <n-radio v-for="song in pre_radio" :key="song.value" :value="song.value">
                  {{ song.label }}
                </n-radio>
              </n-space>
            </n-radio-group>
          </n-form-item-gi>
          <n-form-item-gi :span="24" label="临床诊断" path="clinicalDiagnosis">
            <JDiseaseFormularySelect
                v-model:value="model.clinicalDiagnosis"
                value-type="name"
                :disabled="props.mode == 'detail'"/>
          </n-form-item-gi>
          <n-form-item-gi :span="24" label="处方药物" label-placement="top" path="drugInformation">
            <div style="position: absolute;left: 75px;top: -28px" v-if="props.mode !== 'detail'">
              <n-button style="font-size: 13px" text ghost @click="openAddDrug">添加药品</n-button>
            </div>
            <DrugInformationTable v-if="model.type === 2" v-model:value="model.drugInformation" :mode="props.mode"/>
            <PreDrugInformationTable v-else v-model:value="model.drugInformation" :mode="props.mode"/>
          </n-form-item-gi>
        </n-grid>
      </n-form>
    </n-spin>
    <template #footer>
      <n-space justify="end">
        <n-button size="small" @click="()=>{
          emits('update:show',false);
          props.show = false}">
          {{ props.mode == 'detail' ? '关闭' : '取消' }}
        </n-button>
        <n-button
            size="small" type="primary" :loading="isSaveLoading"
            :disabled="props.mode == 'detail' || isPageLoading"
            @click="handleSave">
          保存
        </n-button>
      </n-space>
    </template>
  </n-modal>
  <AddDrugModal
      ref="addDrugModalRef"
      title="添加药品"
      :value="model.drugInformation"
      :type="GoodsCategoryType.DRUG"
      :formularyType="model.type"
      @update:value="handleSelectOptions"
      :store-model-type="3"
      :checked-set="drugInformationIdSet"
  />
</template>

<script setup lang="ts">
import {computed, ref, watch, reactive} from "vue";
import {useMessages} from "@/hooks/useMessage";
import DrugInformationTable from '@/views/DoctorEndModule/Prescription/compoments/drugInformationTable.vue'
import PreDrugInformationTable from '@/views/DoctorEndModule/Prescription/compoments/preDrugInformationTable.vue'
import {DoctorEndReceptionSelection, GoodsCategoryType} from "@/enums";
import AddDrugModal from "@/views/DoctorEndModule/Prescription/compoments/addDrugModal.vue";
import {deepClone, isNullOrUnDef, isNullStringOrNullOrUnDef, isNumber} from "@/utils";
import {
  doctorEndAddForVideo,
  doctorEndGetPrescriptionDetail,
} from "@/services/api/doctorEndApi";
import JDiseaseFormularySelect from "@/components/JSelect/JDiseaseFormularySelect.vue";

export type prescriptionEditorMode = 'add' | 'edit' | 'detail'

interface RoleEditorModalProps {
  mode: prescriptionEditorMode,
  show: boolean,
  params: {
    id?: string,
    name?: string,
    api?: Function
  }
}

const emits = defineEmits<{
  (e: 'update:show', value: boolean): void,
  (e: 'changeMessage', isChanged: boolean): void
  (e: 'refresh'): void,
}>()
const pre_radio = [
  {
    value:1,
    label:"中药处方"
  },
  {
    value:2,
    label:"西药处方"
  }
]
const {createMessageError, createMessageSuccess} = useMessages()
const isSaveLoading = ref(false)
const addDrugModalRef = ref();
const drugInformationIdSet = ref(new Set())
const isPageLoading = ref(false)
/* 表单实例 */
const formRef = ref(null);

const props = reactive<RoleEditorModalProps>({
  mode: 'add',
  show: false,
  params: {
    id: "",
    name: '',
    api: () => {
    }
  }
})
/* 表单参数初始化 */
const initParams = {
  id: null,
  clinicalDiagnosis: [],
  drugInformation: [],
  auditFailReason: '',
  type: 1,
};

const model = ref({...deepClone(initParams)});

const titleComputed = computed(() => {
  const titleMap: Record<prescriptionEditorMode, string> = {
    'add': '开处方',
    'edit': '修改处方',
    'detail': '处方详情'
  };
  return titleMap[props.mode]
})


function openAddDrug() {
  // addDrugRef.value?.acceptParams({
  //   type: 'add',
  // })
  addDrugModalRef.value?.acceptParams();
}


/** 添加、批量添加回调 */
function handleSelectOptions(goodsList: Array<any>) {
  let tempData = {
    routeOfAdministration: null, // 给药方式
    routeOfAdministrationOther: null, // 自定义给药方式
    frequencyOfAdministration: null, // 用药频次
    frequencyOfAdministrationOther: null, // 自定义用药频次
    chineseDosageCount:null, // 中药剂数
    dosage: null, // 剂量
    dosageUnits: null, // 剂量单位 1=粒；2=片
    count: null, // 购买数量
    medicalAdvice:null, // 医嘱
  }
  goodsList = goodsList.map(item => {
    if (!isNullOrUnDef(item?.routeOfAdministration)
        || !isNullOrUnDef(item?.routeOfAdministrationOther)
        || !isNullOrUnDef(item?.frequencyOfAdministration)
        || !isNullOrUnDef(item?.frequencyOfAdministrationOther)
        || !isNullOrUnDef(item?.chineseDosageCount)
        || !isNullOrUnDef(item.dosage)
        || !isNullOrUnDef(item.dosageUnits)
        || !isNullOrUnDef(item.count)
        || !isNullOrUnDef(item.medicalAdvice)){
      return item
    }
    return Object.assign(item, tempData)
  })
  model.value.drugInformation = goodsList
}

/* 表单规则 */
const rules = {
      clinicalDiagnosis: {
        required: true,
        trigger: ['blur', 'change'],
        validator(rule, value: Array<string>) {
          if (!value || value.length == 0) {
            return new Error('请输入临床诊断')
          }
          if (value.length > 5) {
            return new Error('临床诊断数已超出上限')
          }
          return true
        },
      },
      drugInformation: {
        required: true,
        trigger: ['submit'],
        validator(rule, value: Array<string>) {
          if (!model.value.drugInformation
              || model.value.drugInformation.length == 0) {
            return new Error('请选择药品')
          }
          if (model.value.drugInformation && model.value.type == 2
            && model.value.drugInformation.length > 5) {
            return new Error('处方药物不能添加超出5条')
          }
          if (model.value.drugInformation && model.value.type == 1
            && model.value.drugInformation.length > 48) {
            return new Error('中药处方药物不能添加超出48条')
          }
          for (let i = 0; i < model.value.drugInformation.length; i++) {
            let item = model.value.drugInformation[i]
            if (isNullOrUnDef(item.routeOfAdministration)) {
              return new Error('请填写完整给药方式')
            }
            if (item.routeOfAdministration == 0 && !item.routeOfAdministrationOther) {
              return new Error('请填写完整给药方式')
            }
            if (isNullOrUnDef(item.frequencyOfAdministration)) {
              return new Error('请填写完整用药频次')
            }
            if (item.frequencyOfAdministration == 0
                && isNullStringOrNullOrUnDef(item.frequencyOfAdministrationOther)) {
              return new Error('请填写完整用药频次')
            }
            if (isNullOrUnDef(item.dosage)) {
              return new Error('请填写完整剂量')
            }
            if (!item.dosage || isNaN(Number(item.dosage))) {
              return new Error('请填写正确的剂量')
            }
            if (isNullOrUnDef(item.dosageUnits)) {
              return new Error('请选择完整剂量单位')
            }
            if (!item.count || item.count <= 0) {
              return new Error('请填写完整购买数量')
            }
          }
          return true
        },
      },
    }
;

/* 清空表单 */
const formDataReset = () => {
  model.value = deepClone({...initParams});
  drugInformationIdSet.value = new Set()
  isSaveLoading.value = false
  isPageLoading.value = false

};

/* 关闭弹窗 */
const closeModal = () => {
  formDataReset();
};

function acceptParams(paramsProps: RoleEditorModalProps) {
  props.show = paramsProps.show
  props.mode = paramsProps.mode
  props.params = paramsProps.params
}


/* 确认--保存 */
function handleSave(e: MouseEvent) {
  if (model.value.type == 1){
    const data = {
      routeOfAdministration:model.value.drugInformation[0].routeOfAdministration,
      routeOfAdministrationOther:model.value.drugInformation[0].routeOfAdministrationOther,
      frequencyOfAdministration:model.value.drugInformation[0].frequencyOfAdministration,
      frequencyOfAdministrationOther:model.value.drugInformation[0].frequencyOfAdministrationOther,
      chineseDosageCount:model.value.drugInformation[0].chineseDosageCount,
      dosage:model.value.drugInformation[0].dosage,
      dosageUnits:model.value.drugInformation[0].dosageUnits,
      // count:model.value.drugInformation[0].count,
      medicalAdvice:model.value.drugInformation[0].medicalAdvice,
    }
    for (let i = 0; i < model.value.drugInformation.length; i++) {
      console.log(model.value.drugInformation[i]);
      model.value.drugInformation[i] = {
        ...model.value.drugInformation[i],
        ...data
      }
      console.log(model.value.drugInformation[i]);
    }

  }
  e.preventDefault();
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      saveData()
    }
  });
}

async function saveData() {
  isSaveLoading.value = true
  try {
    if (props.mode == 'add') {
      await addToSaveData()
    }
    if (props.mode == 'edit') {
      await editToSaveData()
    }
  } catch (e) {
    createMessageError(`保存失败:${e}`)
  } finally {
    isSaveLoading.value = false
  }
}

/* 公共参数 */
function commonApiParams() {
  return {
    presId: props.params.id,
    clinicalDiagnosisList: model.value.clinicalDiagnosis,
    type:model.value.type,
    presRpList: model.value.drugInformation.map(item => {
      if (!item.productSpecDTOList && !item.specId) {
        return
      }
      return {  
        productId: item.id,
        specId: item.specId || item.productSpecDTOList?.[0].id,
        routeOfAdministration: item.routeOfAdministration ?? '',
        routeOfAdministrationOther: item.routeOfAdministrationOther ?? '',
        frequencyOfAdministration: item.frequencyOfAdministration ?? '',
        frequencyOfAdministrationOther: item.frequencyOfAdministrationOther ?? '',
        dosage: String(item?.dosage),
        dosageUnits: item.dosageUnits,
        count: item.count,
        chineseDosageCount: item.chineseDosageCount?? '',
        medicalAdvice:item.medicalAdvice?? '',
      }
    }).filter(item => item !== undefined)
  }
}

async function addToSaveData() {
  let commonParams = commonApiParams()
  let params = {
    data: {
      ...commonParams
    }
  }
  let api: Function = doctorEndAddForVideo
  if (props.params.api) {
    api = props.params.api
  }
  await api(params)
  createMessageSuccess(props.mode == 'edit' ? '编辑成功' : '新建成功')
  props.show = false
  emits('update:show', false)
  emits('refresh')
}

async function editToSaveData() {
  let commonParams = commonApiParams()
  let params = {
    data: {
      ...commonParams
    }
  }
  let api: Function = doctorEndAddForVideo
  if (props.params.api) {
    api = props.params.api
  }
  await api(params)
  emits('changeMessage', true)
  createMessageSuccess(props.mode == 'edit' ? '编辑成功' : '新建成功')
  props.show = false
  emits('update:show', false)
  emits('refresh')
}

async function getPrescriptionDetail() {
  try {
    isPageLoading.value = true
    const result = await doctorEndGetPrescriptionDetail(props.params.id)
    if (result) {
      model.value.type = result.type ?? 1;
      model.value.id = result.id;
      model.value.clinicalDiagnosis = result.clinicalDiagnosis?.split(',')
      model.value.drugInformation = result.presRpList
      model.value.auditFailReason = result.auditFailReason ?? ''
    }
  } catch (e) {
    createMessageError(`获取处方单详情失败:${e}`)
  } finally {
    isPageLoading.value = false
  }
}


watch(() => model.value.drugInformation, (newValue) => {
  const idsSet = new Set(newValue.map(item => item.id));
  drugInformationIdSet.value = idsSet
})
watch(() => model.value.type, (newValue) => {
  if(!props.params.id){
    model.value.drugInformation = []
  }
})
watch(() => props.show, (newValue) => {
  if ((props.mode == 'detail' || props.mode == 'edit') && newValue) {
    getPrescriptionDetail()
  }
})

defineExpose({
  acceptParams,
});

</script>

<style scoped lang="less"></style>
