<template>
  <JModal
    v-model:show="show"
    title="选择规格"
    width="680"
    @after-leave="closeModal"
    :is-scale="false"
		@positive-click="handleSave"
		:positiveButtonProps="{
			disabled: !selectedOptionsKeys.length
		}"
    positiveText="确认"
  >
    <n-grid :cols="12" :x-gap="12" responsive="self">
      <!-- 操作栏 -->
      <n-gi :span="12">
        <n-flex style="margin-bottom: 12px;">
          <n-button type="primary" text size="small" :disabled="!checkedRowKeysRef.length" @click="batchAdd">
            批量添加
          </n-button>
          <n-button type="error" text size="small" :disabled="!checkedRowKeysRef.length" @click="batchCancel">
            取消添加
          </n-button>
        </n-flex>
      </n-gi>
      <!-- 规格 -->
      <n-gi :span="12">
        <n-data-table
          v-model:checked-row-keys="checkedRowKeysRef"
          :columns="columns"
          :data="sortedTableData"
          :row-key="row => row.id"
          default-expand-all
          :style="{ minHeight: `${300}px` }"
          flex-height
          :single-line="false"
          size="small"
        />
      </n-gi>
    </n-grid>
  </JModal>
</template>

<script setup lang="tsx" name="SelectSpecModal">
import { ref, computed } from "vue";
import type { DataTableRowKey } from "naive-ui";
import { useBoolean } from '@/hooks';
import useGenerateCombinations from "@/views/StoreModule/GoodsManagement/hooks/useGenerateCombinations";
const { generateCombinations } = useGenerateCombinations();

const props = defineProps<{
  attributeList: Array<any>; // 商品属性
}>();

/** emits */
const emits = defineEmits<{
  (e: 'update:selectedOptions', keys: Array<Partial<ApiStoreModule.ProductSpec>>): void;
}>();

/** 选择项 keys */
const selectedOptionsKeys = ref([]);

/** 表格数据 */
const tableDataRef = ref([]);

/** 当前选中行 */
const checkedRowKeysRef = ref<DataTableRowKey[]>([]);

/** 定义一个函数来格式化价格 */
const formatPrice = (price) => `￥${(price / 100).toFixed(2)}`;


/** 根据规格组合排序表格数据 */
const sortedTableData = computed(() => {
  if (!props.attributeList || !tableDataRef.value.length) {
    return tableDataRef.value;
  }
  
  // 生成期望的规格组合顺序
  const expectedCombinations = generateCombinations(props.attributeList);
  
  // 创建排序映射
  const sortMap = new Map();
  expectedCombinations.forEach((combination, index) => {
    // 根据组合生成排序键
    const sortKey = combination.map(item => item.id).join('-');
    sortMap.set(sortKey, index);
  });
  
  // 对表格数据排序
  return [...tableDataRef.value].sort((a, b) => {
    // 生成当前行的排序键
    const aKey = [a.firstAttrId, a.secondAttrId, a.thirdAttrId]
      .filter(id => id)
      .join('-');
    const bKey = [b.firstAttrId, b.secondAttrId, b.thirdAttrId]
      .filter(id => id)
      .join('-');
    
    const aIndex = sortMap.get(aKey) ?? 999;
    const bIndex = sortMap.get(bKey) ?? 999;
    
    return aIndex - bIndex;
  });
});

/** 动态生成表格列 */
const columns = computed(() => {
  const dynamicColumns = [
    {
      type: "selection",
      key: "selection",
      fixed: "left",
      width: 50,
      minWidth: 50,
      align: "center",
    }
  ];

  // 根据 attributeList 动态生成规格列
  props.attributeList?.forEach((attr, index) => {
    dynamicColumns.push({
      title: attr.attributeName,
      key: `spec_${index}`,
      width: 100,
      resizable: true,
      render: (row) => {
        // 根据索引获取对应的 attrId
        let attrId;
        if (index === 0) attrId = row.firstAttrId;
        else if (index === 1) attrId = row.secondAttrId;
        else if (index === 2) attrId = row.thirdAttrId;
        
        // 在当前属性的 specValue 中查找匹配的值
        const matchedSpec = attr.specValue.find(spec => spec.id === attrId);
        return <span>{matchedSpec?.attributeValue || '-'}</span>;
      }
    });
  });

  // 添加其他固定列
  dynamicColumns.push(
    {
      title: '售价（元）',
      key: 'price',
      resizable: true,
      render: (row) => {
        let formattedPrice = formatPrice(row.price);
        return <span>{formattedPrice}</span>
      }
    },
    {
      title: "库存",
      key: 'availStocks',
      resizable: true,
    },
    {
      title: '操作',
      key: 'operation',
      resizable: true,
      render: (row, index) => {
        return (
          <n-button size="small" type="primary" text onClick={() => handleAdd(row?.id)}>
            {selectedOptionsKeys.value.includes(row?.id) ? `取消添加` : `添加`}
          </n-button>
        );
      },
    }
  );

  return dynamicColumns;
});

/** 显隐 */
const { bool: show, setFalse, setTrue } = useBoolean(false);

/* 接收父组件传过来的参数 */
const acceptParams = (_params: {
  productSpecList: Array<Partial<ApiStoreModule.ProductSpec>>;
  productSpecListIds: string[];
}) => {
  if (_params.productSpecList?.length > 0) {
    tableDataRef.value = [..._params.productSpecList];
    selectedOptionsKeys.value = [..._params.productSpecListIds];
  }
  setTrue();
};

/** 添加 */
function handleAdd(id: string) {
  if (selectedOptionsKeys.value.includes(id)) {
    selectedOptionsKeys.value.splice(selectedOptionsKeys.value.indexOf(id), 1);
  } else {
    selectedOptionsKeys.value.push(id);
  }
}

/** 批量取消操作 */
function batchCancel() {
  if (selectedOptionsKeys.value.length === 0) {
    return;
  }
  selectedOptionsKeys.value = selectedOptionsKeys.value.filter(item => !checkedRowKeysRef.value.includes(item));
  // 刷新
  checkedRowKeysRef.value = [];
}

/** 批量添加 */
function batchAdd() {
  let tempList = [];
  checkedRowKeysRef.value.forEach(item => {
    if (!selectedOptionsKeys.value.includes(item)) {
      tempList.push(item);
    }
  });
  selectedOptionsKeys.value = [...selectedOptionsKeys.value, ...tempList];
  // 刷新
  checkedRowKeysRef.value = [];
}

/* 关闭弹窗之后 */
const closeModal = () => {
  selectedOptionsKeys.value = [];
  checkedRowKeysRef.value = [];
};

/* 确认--保存 */
const handleSave = (e: MouseEvent) => {
  e.preventDefault();
  const tempData = tableDataRef.value.filter(item => selectedOptionsKeys.value.includes(item.id));
  emits('update:selectedOptions', tempData);

  setFalse();
};

defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less">
@import "../../../styles/index.less";
</style>
