// TODO  医生端路由数据，暂时写死
export const DoctorEndResList: Array<any> = [
    {
        "id": "1818827791581745154",
        "createTime": "2024-07-30 16:00:00",
        "type": 10,
        "level": 1,
        "sort": 1,
        "name": "接诊",
        "code": "sg.store.doctor.reception.module",
        "status": 1,
        "children": [
            {
                "id": "1818827791581745155",
                "createTime": "2024-07-30 16:00:00",
                "type": 10,
                "level": 2,
                "sort": 1,
                "parentId": "1818827791581745154",
                "name": "接诊",
                "code": "sg.store.doctor.reception.page",
                "status": 1,
                "children": []
            },
        ]
    },
    {
        "id": "1876516879095554050",
        "createTime": "2025-01-07 10:30:00",
        "type": 10,
        "level": 1,
        "sort": 2,
        "name": "处方",
        "code": "sg.store.doctor.prescription.module",
        "status": 1,
        "children": [
            {
                "id": "1876516879095554051",
                "createTime": "2025-01-07 10:30:00",
                "type": 10,
                "level": 2,
                "sort": 1,
                "parentId": "1876516879095554050",
                "name": "处方",
                "code": "sg.store.doctor.prescription.page",
                "status": 1,
                "children": [
                    {
                        "id": "1876516879095554052",
                        "createTime": "2025-01-07 10:30:00",
                        "type": 10,
                        "level": 3,
                        "sort": 1,
                        "parentId": "1876516879095554051",
                        "name": "分销设置",
                        "code": "sg.store.structre.settings.index",
                        "status": 1,
                        "children": [
                            {
                                "id": "1876516879095554053",
                                "createTime": "2025-01-07 10:30:00",
                                "type": 10,
                                "level": 4,
                                "sort": 1,
                                "parentId": "1876516879095554052",
                                "name": "保存",
                                "code": "sg.store.structre.settings.index.save",
                                "status": 1,
                                "children": []
                            }
                        ]
                    }
                ]
            },
        ]
    },
    {
        "id": "1818837081323446284",
        "createTime": "2024-10-21 16:00:00",
        "type": 10,
        "level": 1,
        "sort": 3,
        "name": "问诊单",
        "code": "sg.store.doctor.consultation.module",
        "status": 1,
        "children": [
            {
                "id": "1818837081323446285",
                "createTime": "2024-10-21 16:00:00",
                "type": 10,
                "level": 2,
                "sort": 1,
                "parentId": "1818837081323446284",
                "name": "问诊单",
                "code": "sg.store.doctor.consultation.page",
                "status": 1,
                "children": []
            },
        ]
    },
    {
        "id": "1818837081323446280",
        "createTime": "2024-09-09 10:30:00",
        "type": 10,
        "level": 1,
        "sort": 4,
        "name": "设置",
        "code": "sg.store.doctor.setting.module",
        "status": 1,
        "children": [
            {
                "id": "1818827791581745172",
                "createTime": "2024-07-31 15:30:00",
                "updateTime": "2024-07-31 15:30:00",
                "type": 10,
                "level": 2,
                "sort": 1,
                "parentId": "1818837081323446280",
                "name": "设置",
                "code": "sg.store.doctor.setting.page",
                "status": 1,
                "children": []
            },
        ]
    }
]


export const PharmacistsResList: Array<any> = [
    {
        "id": "1876516879095554050",
        "createTime": "2025-01-07 10:30:00",
        "type": 10,
        "level": 1,
        "sort": 2,
        "name": "处方",
        "code": "sg.store.pharmacist.prescription.module",
        "status": 1,
        "children": [
            {
                "id": "1876516879095554051",
                "createTime": "2025-01-07 10:30:00",
                "type": 10,
                "level": 2,
                "sort": 1,
                "parentId": "1876516879095554050",
                "name": "处方",
                "code": "sg.store.pharmacist.prescription",
                "status": 1,
                "children": []
            },
        ]
    },
]

