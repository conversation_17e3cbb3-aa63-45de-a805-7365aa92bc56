<template>
  <div :class="['message-text-container', 'text-select']">
     <span class="text">
        {{ props.content }}
      </span>
  </div>
</template>

<script lang="ts" setup>
import {ref, watch} from 'vue';
import type {IMessageModel} from "@tencentcloud/chat-uikit-engine";
import {TUIChatEngine} from "@tencentcloud/chat-uikit-engine";
import {JSONToObject, safeParse} from "@/views/DoctorEndModule/IM/utils/IMUtils";
import type {JChatMessageModel} from "@/views/DoctorEndModule/IM/types";


interface IProps {
  content: string;
  messageItem: JChatMessageModel;
  enableURLHighlight?: boolean;
}

interface TextItem {
  name: string;
  text: string;
  src?: string;
  type?: string;
  emojiKey?: string;
  url?: string;
}

const props = withDefaults(defineProps<IProps>(), {
  content: '',
  messageItem: () => ({} as J<PERSON>hatMessageModel),
  enableURLHighlight: false,
});

</script>

<style lang="less" scoped>
.message-text-container {
  display: inline;
  font-size: 0;
  letter-spacing: -1px;
}

.text-select {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

.text, .emoji, .url-link {
  &::selection {
    background-color: #b4d5fe;
    color: inherit;
    cursor: text;
  }
}

.emoji {
  font-size: 0;
  vertical-align: bottom;
  width: 20px;
  height: 20px;
}

.text, .url-link {
  font-size: 14px;
  white-space: pre-wrap;
  word-break: break-all;
  letter-spacing: normal;
}

.url-link {
  color: #0366d6;
  text-decoration: none;
  word-break: break-all;
  cursor: text;

  &:hover:not(:active) {
    cursor: pointer;
  }

  &:visited {
    color: #0366d6;
  }
}
</style>
