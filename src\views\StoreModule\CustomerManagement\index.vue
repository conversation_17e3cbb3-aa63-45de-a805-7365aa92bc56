<template>
  <TreeContentLayout>
    <template #tree-content>
      <div class="label-container inner-page-height">
        <!-- Header -->
        <div class="header">
          <n-space justify="space-between" style="flex-wrap: nowrap; margin-bottom: 12px">
            <n-button size="small" :loading="isGetLoading" @click="refreshCustomerTag" class="store-button">
              刷 新
            </n-button>
            <!-- 新建客户管理 -->
            <n-button v-if="hasNewLabel" size="small" type="primary" @click="openAddCustomerTag('add')">新建标签</n-button>
          </n-space>
          <!-- 客户管理名称 -->
          <JSearchInput
            width="326"
            v-model:value="searchValue"
            placeholder="请输入标签名称"
            @search="search"
            size="small"
          />
        </div>
        <!-- 标签列表 -->
        <div class="customer-tags-container">
          <CustomerLabelList
            :options="tagOptions"
            :selected-keys="selectedKeys"
            :loading="isGetLoading"
            @scroll="handleScroll"
            @right-click="handleRightClick"
            @click-menu="handleMenuClick"
          />
        </div>
      </div>
    </template>
    <template #default>
      <div class="customer-container">
        <!-- 客户管理表格内容 -->
        <ManagementReport ref="acceptParamsReport" @afterSuccessfulRequest="handleAfterSuccessfulRequest" />
      </div>
      <!-- 新建客户管理 -->
      <AddLabel ref="addLabel" />
      <!-- 鼠标右键菜单 -->
      <JDropdown
        v-model:show="showDropdownRef"
        :options="rightOptions"
        :x="xRef"
        :y="yRef"
        @select="handleSelectClick"
        :targetTag="currentClickMenu"
      />
    </template>
  </TreeContentLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useDialog, type DropdownOption } from "naive-ui";
import { tagEntityDeleteTag } from '@/services/api';
import { useMessages, useBoolean } from "@/hooks";
import { useGetCustomerTag, useRightMenu } from "./hooks";
import { CUSTOMER_TAG_ALL_VALUE } from "./type";
import type { CustomerTag } from "./type";
/** 相关组件 */
import TreeContentLayout from "@/components/TreeContentLayout/index.vue";
import AddLabel from './components/AddLabel.vue';
import ManagementReport from './components/ManagementReport.vue';
import CustomerLabelList from './components/CustomerLabelList.vue';
import JDropdown from "@/components/JDropdown/index.vue";
import { hasEditCustomer, hasNewLabel, hasEditLabel, hasDeletelabel } from "./authList"; 

const { createMessageSuccess, createMessageError } = useMessages();

/** 获取客户标签 hooks */
const { isGetLoading, getCustomerTagData, selectedKeys, handleScroll, searchValue, tagOptions, _params } = useGetCustomerTag();

/** 右键菜单 */
const { rightOptions, xRef, yRef, showDropdownRef, currentClickMenu, handleRightClick } = useRightMenu(hasEditLabel,hasDeletelabel);

/** 确认框 */
const dialog = useDialog();

/** 打开新建标签名称 */
const addLabel = ref<InstanceType<typeof AddLabel> | null>(null);
const openAddCustomerTag = (type: 'add' | 'edit', tag?: any) => {
  const _params = {
    type,
    refresh: refreshCustomerTag,
    editId: tag?.key,
    editName: tag?.label,
  };
  addLabel.value?.acceptParams(_params);
};

/** 客户表格 */
const acceptParamsReport = ref<InstanceType<typeof ManagementReport> | null>(null);

/** 点击客户标签 */
const handleMenuClick = (key: Array<string>, option?: CustomerTag) => {
  // Check if an option is provided
  if (option) {
    isGetLoading.value = true;
    const { key: optionKey } = option;
    const isAllSelected = optionKey === CUSTOMER_TAG_ALL_VALUE;
    const isSelected = selectedKeys.value.includes(optionKey);

    // 当点击全部时，取消其他选中状态
    if (isAllSelected) {
      selectedKeys.value = [CUSTOMER_TAG_ALL_VALUE];
    } else {
      // 其他节点
      if (!isSelected) {
        // 过滤全部
        selectedKeys.value = selectedKeys.value.filter(item => item !== CUSTOMER_TAG_ALL_VALUE);
        selectedKeys.value.push(optionKey);
      } else {
        // 取消
        selectedKeys.value = selectedKeys.value.filter(item => item !== optionKey);
        if (selectedKeys.value.length === 0) {
          selectedKeys.value = [CUSTOMER_TAG_ALL_VALUE];
        }
      }
    }
  }

  // 触发客户表格请求参数
  const tagIdsList = selectedKeys.value.includes(CUSTOMER_TAG_ALL_VALUE) ? null : selectedKeys.value;
  acceptParamsReport.value?.acceptParamsReport({ tagIdsList },hasEditCustomer);
};

/** 客户表格请求成功回调 */
const handleAfterSuccessfulRequest = () => {
  isGetLoading.value = false;
};

/** 刷新 */
const refreshCustomerTag = () => {
  _params.pageVO.current = 1;
  getCustomerTagData(() => {
    handleMenuClick(selectedKeys.value);
  });
};

/** 组件挂载时 */
onMounted(() => {
  getCustomerTagData(() => {
    handleMenuClick(selectedKeys.value);
  });
});

/** 搜索 */
const search = () => {
  _params.pageVO.current = 1;
  selectedKeys.value = [CUSTOMER_TAG_ALL_VALUE];
  getCustomerTagData(() => {
    handleMenuClick(selectedKeys.value);
  });
};

/** 点击删除标签 */
const handleSelectClick = (key: string, option: DropdownOption, tag: CustomerTag) => {
  if (key === 'delete') {
    dialog.warning({
      title: '确定删除《' + tag?.label + '》标签',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => {
        tableDelete(tag?.key);
      },
    })
  } else {
    openAddCustomerTag('edit', tag);
  }
};

/** 删除标签 */
const tableDelete = async (id) => {
  try {
    await tagEntityDeleteTag({ id });
    // 过滤掉删除标签值
    selectedKeys.value = selectedKeys.value.filter(item => item !== id);
    refreshCustomerTag();
    createMessageSuccess("删除标签成功");
  } catch (err) {
    createMessageError('删除标签失败: ' + err);
  }
};
</script>

<style lang="less" scoped>
.label-container {
  width: 100%;
  .header {
    padding: 12px;
  }
  .customer-tags-container {
    height: calc(100% - 102px);
  }
}
.customer-container {
  width: 100%;
  height: 100%;
}
</style>
