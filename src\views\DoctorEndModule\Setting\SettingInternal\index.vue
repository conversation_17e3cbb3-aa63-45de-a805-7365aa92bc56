<template>
  <div class="container">
    <n-spin size="small" :show="loadShowRef">
      <n-scrollbar style="height: 100%;" :content-style="{ padding: '24px' }">
        <n-card :bordered="false" size="small" class="config">
          <div class="header">
            <n-space justify="space-between">
              <div>
                <p style="font-size: 16px;font-weight: 500;color: #333333">接诊欢迎语</p>
                <p class="subtext">接诊后系统自动发送以下内容</p>
              </div>
              <div>
                <n-switch v-model:value="activeConfig"/>
              </div>
            </n-space>
          </div>

          <div class="content">
            <n-input
                v-model:value="message"
                type="textarea"
                show-count
                :maxlength="100"
                placeholder="您好，非常高兴由我为您提供医疗咨询服务，可以把你的具体症状和检查结果发送过来，以便能够根据您的病情进行详细解答，谢谢您的理解和信任！"
            />
          </div>

          <div class="footer">
            <n-button type="primary" @click="save">保存</n-button>
          </div>
        </n-card>
      </n-scrollbar>
    </n-spin>
  </div>
</template>

<script setup lang="ts">
import {ref, onMounted} from 'vue'

import {useMessages} from "@/hooks";
import {editDoctorBaseSetting, getDoctorBaseSetting} from "@/services/api/doctorEndApi/setting";
import {useUserStore} from "@/stores/modules/user";
import {getConversationPage} from "@/services/api/doctorEndApi";


defineOptions({
  name: 'SettingInternal'
})

const {createMessageSuccess, createMessageError,} = useMessages();
const userStore = useUserStore();

/* 表单参数初始化 */
const initParams = {
  sto_doctor_welcome_msg: {},
  sto_setup_welcome_msg: {},
};
const model = ref([]);

const message = ref('')
const loadShowRef = ref<boolean>(true)
const activeConfig = ref(false)

const save = async () => {
  try {
    loadShowRef.value = true
    await editDoctorBaseSetting({
      data: {
        id: userStore.userInfo.systemUserId || '',
        doctorWelcomeMsg: message.value,
        isSetupWelcomeMsg: activeConfig.value ? 1 : 0
      }
    })
    createMessageSuccess(`保存成功!`);
  } catch (err) {
    createMessageError(`保存失败：${err}`);
  } finally {
    loadShowRef.value = false
  }
}

async function fromInit() {
  try {
    loadShowRef.value = true
    const res = await getDoctorBaseSetting({
      id: userStore.userInfo.systemUserId || ''
    })
    if (res) {
      message.value = res.doctorWelcomeMsg
      activeConfig.value = res?.isSetupWelcomeMsg == 1
    }
  } catch (err) {
    createMessageError(`获取开发配置失败：${err}`);
  } finally {
    loadShowRef.value = false
  }
}

/* 组件挂载 */
onMounted(async () => {
  await fromInit()
});

</script>

<style scoped>
.container {
  padding: 0 1px 0 1px;
  background-color: #fff;
  width: 100%;
  height: 100%;
}

.card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header {
  margin-bottom: 20px;
}

h3 {
  font-size: 16px;
  color: #333;
  margin-bottom: 4px;
}

.subtext {
  font-size: 14px;
  color: #666;
}

.content {
  margin-bottom: 20px;
}

.content :deep(.n-input) {
  border: 1px solid #ddd;
  border-radius: 4px;
}

.content :deep(.n-input__textarea-el) {
  min-height: 100px;
  font-size: 14px;
}

.counter {
  text-align: right;
  color: #999;
  font-size: 14px;
  margin-top: 8px;
}

.footer {
  display: flex;
  justify-content: flex-end;
}

.config {
  position: relative;
  margin-bottom: 12px;
  box-shadow: rgba(17, 17, 26, 0.05) 0px 1px 0px, rgba(17, 17, 26, 0.1) 0px 0px 8px;
}
</style>
