import { ref,reactive } from "vue";
import { useMessages } from "@/hooks";
import dayjs from "dayjs";
import { LiveStateTypeEnum } from "../type";
import { accountActivityData,
    accountActivityDataDuration,
    liveActivityOrderData,
    liveActivityGet,
    liveActivityProductRanking,
    pcViewUrl,
    actualLiveTime,
    getEnterUV,
    getLiveActivityOrderAmount
} from "@/services/api"
import { useAutoRefresh } from "./useAutoTime"


/** gmv数据类型 */
type LiveRoomInfoType = {
    value: number;
    label: string;
    prefix?: string;
    suffix?: string;
    hint?: string;
    precision:number
};
/** 商品数据类型 */
type goodsDataListType = {
    productId: string;
    totalMoney: number;
    totalProducts: number;
    frontName: string;
    productImgPath:string
};

const message = useMessages();
const isLoadingRef = ref<boolean>(false)
/** 拉流地址 */
const pcViewUrlRef = ref<string>('')
/** 商品列表 */
const goodsDataListRef = ref<goodsDataListType[]>([])
/**直播间信息 */
const liveRoomDataRef = ref({
        id:'',
        liveStartTime:"",
        liveEndTime:"",
        duration:0,
        status:LiveStateTypeEnum.START,
        name:"-",
        liveActualStartTime:''
    }
)
/**人均观看时长 */
const WatchDurationPerPeopleRef = ref<number>(0)
/**直播交易金额 */
const totalTransactionMoneyRef = ref<number>(0)
const liveRoomInfoReactive = reactive<Record<string, LiveRoomInfoType>>({
    "totalCustomers":{value:0,label:"成交人数",precision:0},
    "conversionRate":{value:0,label:"成交转化率",suffix:"%",hint:'成交人数/进直播间人数',precision:2},
    "totalMoney":{value:0,label:"成交总金额",prefix:'￥',precision:2},
    "clickRate":{value:0,label:"商品气泡点击率",suffix:'%',precision:2},
    "realPopularity":{value:0,label:"当前在线人数",precision:0},
    "totalOrders":{value:0,label:"成交订单数",precision:0},
    "totalProducts":{value:0,label:"成交商品数",precision:0},
    "unitPrice":{value:0,label:"客单价",prefix:'￥',hint:'成交总金额/成交人数',precision:2},
    "uv":{value:0,label:"进直播间人数",precision:0},
    "pcu":{value:0,label:"峰值在线人数",precision:0},
})
/**观看时长分布 */
const watchPlanDataRef = ref({
    list:[
        { name:'1-10分钟', num:0, color:'#f76357'},
        { name:'10-20分钟', num:0, color:'#08cd6c'},
        { name:'20-30分钟', num:0, color:'#16aefb'},
        { name:'30-60分钟', num:0, color:'#6183f6'},
        { name:'60分钟以上', num:0, color:'#ef9a5e'},
    ],
    sum:0
})
/**近5分数据*/
const fiveMinuteDataRef = ref({
    onlineNumber:0,
    totalMoney:'0'
})

/**获取直播间信息
 * @param id 直播间id
 */
async function getLiveActivity(id:string) {
    try {
        isLoadingRef.value = true
        const res = await liveActivityGet(id)
        liveRoomDataRef.value = { ...res}
        await getPcViewUrl()
        await getActualLiveTime()
        await oneMinuteAutoRefresh.triggerRefresh()
        await threeAutoRefresh.triggerRefresh()
        const isFinish = await sixtyAutoRefresh.triggerRefresh()
        isLoadingRef.value = isFinish
    } catch (error) {
        message.createMessageError('直播间信息异常'+error)
    }
}
/**拉流链接
 * @param id 直播间id
 */
async function getPcViewUrl() {
    try {
        const res = await pcViewUrl(liveRoomDataRef.value.id)
        pcViewUrlRef.value = `${res}&mode=player`
    } catch (error) {
        message.createMessageError('拉流异常'+error)
    }
}
/**获取直播间订单数据 */
async function getLiveActivityOrderData() {
    try {
        const res = await liveActivityOrderData(liveRoomDataRef.value.id)
        liveRoomInfoReactive['totalCustomers'].value = Number(res.totalCustomers) 
        liveRoomInfoReactive['totalMoney'].value = Number(res.totalMoney) / 100
        liveRoomInfoReactive['totalOrders'].value = Number(res.totalOrders) 
        liveRoomInfoReactive['totalProducts'].value = Number(res.totalProducts)
        liveRoomInfoReactive['unitPrice'].value = Number(res.totalCustomers) ? (Number(res.totalMoney)/100) / Number(res.totalCustomers) :0
        totalTransactionMoneyRef.value = Number(res.totalTransactionMoney) / 100
    } catch (error) {
        // message.createMessageError('订单异常'+error)
        console.log("订单异常"+error);
        
    }
}
/**获取 峰值 进直播间 人均观看时长  */
async function getAccountActivityData() {
    try {
        const params = {
            data:{
                id:liveRoomDataRef.value.id,
                name:liveRoomDataRef.value.name,
                liveStartTime:liveRoomDataRef.value.liveStartTime,
                liveEndTime:liveRoomDataRef.value.liveEndTime,
            }
        }
        const res = await accountActivityData(params)
        liveRoomInfoReactive['pcu'].value = Number(res.accountActivityData.pcu)|| 0
        liveRoomInfoReactive['uv'].value = Number(res.accountActivityData.uv)  || 0
        liveRoomInfoReactive['conversionRate'].value = Number(res.accountActivityData.uv) ? (liveRoomInfoReactive['totalCustomers'].value / Number(res.accountActivityData.uv))*100 : 0 
        liveRoomInfoReactive['realPopularity'].value = Number(res.realTimeOnlineNumber.realPopularity) || 0
        liveRoomInfoReactive['clickRate'].value = Number(res.clickRate) * 100 || 0
        WatchDurationPerPeopleRef.value = Number(res.accountActivityData.watchDurationPerPeople) * 60 || 0
        
    } catch (error) {
        // message.createMessageError('实时数据异常'+error)
        console.log("实时数据异常"+error);
        
    }
}
/**获取观看时长分布 */
async function getAccountActivityDataDuration() {
    try {
        const res = await accountActivityDataDuration(liveRoomDataRef.value.id)
        // 重置总数
        watchPlanDataRef.value.sum = 0
        watchPlanDataRef.value.list.forEach((item, index) => {
            const userNum = Number(res[index]) || 0;
            item.num = userNum;
            watchPlanDataRef.value.sum += item.num
        })
    } catch (error) {
        // message.createMessageError('时长分布异常'+error)
        console.log("时长分布异常"+error);

    }
}
/**获取直播间商品排行榜 */
async function getLiveActivityProductRanking() {
    try {
        const res = await liveActivityProductRanking(liveRoomDataRef.value.id)
        goodsDataListRef.value = [...res.records]
    } catch (error) {
        // message.createMessageError('商品排行榜异常'+error)
        console.log("商品排行榜异常"+error);
        
    }
}
/**获取实际开播时间 */
async function getActualLiveTime() {
    try {
        const { startTime,endTime } = await actualLiveTime(liveRoomDataRef.value.id)
        liveRoomDataRef.value.liveActualStartTime = dayjs.unix(startTime).format('YYYY-MM-DD HH:mm:ss')
        const seconds = Number(endTime) ? Number(endTime) - startTime:dayjs().unix() - startTime
        liveRoomDataRef.value.duration = Math.floor(Number(seconds) / 60)
    } catch (error) {
        // message.createMessageError('实际开播时间异常'+error)
        console.log("实际开播时间异常"+error);
    }
}
/** 获取近5分钟进入直播间人数 */
async function getEnterUVData() {
    try {
        const res = await getEnterUV(liveRoomDataRef.value.id)
        fiveMinuteDataRef.value.onlineNumber = res || 0
    } catch (error) {
        // message.createMessageError('近5分钟进入直播间人数异常'+error)
        console.log("近5分钟进入直播间人数异常"+error);
    }
}
/** 获取近5分钟成交金额 */
async function getLiveActivityOrderAmountData() {
    try {
        const res = await getLiveActivityOrderAmount(liveRoomDataRef.value.id)
        fiveMinuteDataRef.value.totalMoney = res ? Number(res / 100).toFixed(2) :'0.00'
    } catch (error) {
        // message.createMessageError('近5分钟进入直播间人数异常'+error)
        console.log("近5分钟进入直播间人数异常"+error);
    }
}
/**10-14秒区间定时器 */
const threeAutoRefresh = useAutoRefresh([10,14],async()=>{
    await getLiveActivityOrderData()
})

/**180-300秒区间定时器 */
const sixtyAutoRefresh = useAutoRefresh([180,300],async()=>{
    await getAccountActivityData()
    await getAccountActivityDataDuration()
    await getLiveActivityProductRanking()
})

/**1分钟区间定时器 */
const oneMinuteAutoRefresh = useAutoRefresh([60,70],async()=>{
    await getEnterUVData()
    await getLiveActivityOrderAmountData()
})

export default function useLiveDashboard() {
    return {
        isLoadingRef,
        pcViewUrlRef,
        goodsDataListRef,
        liveRoomDataRef,
        liveRoomInfoReactive,
        watchPlanDataRef,
        WatchDurationPerPeopleRef,
        totalTransactionMoneyRef,
        fiveMinuteDataRef,
        getLiveActivity,
        getAccountActivityData,
        getAccountActivityDataDuration,
        getLiveActivityOrderData,
        getLiveActivityProductRanking,
        sixtyAutoRefresh,
        threeAutoRefresh,
    }
}