import { SupplierManagementAuth } from "@/enums/authKeys";
import { hasAuth } from "@/utils/auth/authUtils";

/** 供应商管理 - 新增 */
export const hasSupplierManagementAddAuth = function(){
    return hasAuth(SupplierManagementAuth.supplierManagementAdd.key);
}()

/** 供应商管理 - 编辑 */
export const hasSupplierManagementEditAuth = function(){
    return hasAuth(SupplierManagementAuth.supplierManagementEdit.key);
}()

/** 供应商管理 - 删除 */
export const hasSupplierManagementDeleteAuth = function(){
    return hasAuth(SupplierManagementAuth.supplierManagementDelete.key);
}()
