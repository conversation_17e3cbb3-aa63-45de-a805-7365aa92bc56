import { computed, effectScope, nextTick, onScopeDispose, ref, watch } from "vue";
import * as echarts from "echarts/core";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "echarts/charts";
import type {
  BarSeriesOption,
  GaugeSeriesOption,
  LineSeriesOption,
  PictorialBarSeriesOption,
  PieSeriesOption,
  RadarSeriesOption,
  ScatterSeriesOption,
} from "echarts/charts";
import {
  DatasetComponent,
  GridComponent,
  LegendComponent,
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  TransformComponent,
} from "echarts/components";
import type {
  DatasetComponentOption,
  GridComponentOption,
  LegendComponentOption,
  TitleComponentOption,
  ToolboxComponentOption,
  TooltipComponentOption,
} from "echarts/components";
import { LabelLayout, UniversalTransition } from "echarts/features";
import { CanvasRenderer } from "echarts/renderers";
import { useElementSize } from "@/hooks";
import { SystemSetting } from "@/settings/systemSetting";

export type ECOption = echarts.ComposeOption<
  | BarSeriesOption
  | LineSeriesOption
  | PieSeriesOption
  | ScatterSeriesOption
  | PictorialBarSeriesOption
  | RadarSeriesOption
  | GaugeSeriesOption
  | TitleComponentOption
  | LegendComponentOption
  | TooltipComponentOption
  | GridComponentOption
  | ToolboxComponentOption
  | DatasetComponentOption
>;

echarts.use([
  TitleComponent,
  LegendComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  ToolboxComponent,
  BarChart,
  LineChart,
  PieChart,
  ScatterChart,
  PictorialBarChart,
  RadarChart,
  GaugeChart,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer,
]);

interface ChartHooks {
  onRender?: (chart: echarts.ECharts) => void | Promise<void>;
  onUpdated?: (chart: echarts.ECharts) => void | Promise<void>;
  onDestroy?: (chart: echarts.ECharts) => void | Promise<void>;
}

/**
 * use echarts
 *
 * @param optionsFactory echarts 配置
 * @param hooks echarts 回调
 */
export default function useEcharts<T extends ECOption>(optionsFactory: () => T, hooks: ChartHooks = {}) {
  // 创建effectScope
  const scope = effectScope();

  // 用于获取和存储 DOM 元素的引用
  const domRef = ref<HTMLElement | null>(null);
  const initialSize = { width: 0, height: 0 };
  const { width, height } = useElementSize(domRef, initialSize);

  // 用于存储 echarts 实例
  let chart: echarts.ECharts | null = null;
  const chartOptions: T = optionsFactory();
  // 解构钩子函数并提供默认实现
  const {
    onRender = instance => {
      const textColor = "rgb(31, 31, 31)";
      const maskColor = "rgba(255, 255, 255, 0.8)";

      instance.showLoading({
        color: SystemSetting.primaryColor,
        textColor,
        fontSize: 14,
        maskColor,
      });
    },
    onUpdated = instance => {
      instance.hideLoading();
    },
    onDestroy,
  } = hooks;

  /**
   * @description 判断是否可以渲染图表
   * 当 domRef 准备好并且 initialSize 是有效的
   */
  function canRender() {
    return domRef.value && initialSize.width > 0 && initialSize.height > 0;
  }

  /**
   * @description 判断图表是否已渲染
   */
  function isRendered() {
    return Boolean(domRef.value && chart);
  }

  /**
   * @description 更新图表选项
   * @param callback 回调函数，用于更新选项
   */
  async function updateOptions(callback: (opts: T, optsFactory: () => T) => ECOption = () => chartOptions) {
    if (!isRendered()) return;

    const updatedOpts = callback(chartOptions, optionsFactory);

    Object.assign(chartOptions, updatedOpts);

    if (isRendered()) {
      chart?.clear();
    }

    chart?.setOption({ ...updatedOpts, backgroundColor: "transparent" });

    await onUpdated?.(chart!);
  }

  /**
   * 设置图表选项
   * @param options 图表选项
   */
  function setOptions(options: T) {
    chart?.setOption(options);
  }

  /** 渲染图表 */
  async function render() {
    if (!isRendered()) {
      const chartTheme = "light";

      await nextTick();

      chart = echarts.init(domRef.value, chartTheme);

      chart.setOption({ ...chartOptions, backgroundColor: "transparent" });

      await onRender?.(chart);
    }
  }

  /** 调整图表大小 */
  function resize() {
    chart?.resize();
  }

  /** 销毁图表 */
  async function destroy() {
    if (!chart) return;

    await onDestroy?.(chart);
    chart?.dispose();
    chart = null;
  }

  /**
   * 根据尺寸渲染图表
   *
   * @param w 宽度
   * @param h 高度
   */
  async function renderChartBySize(w: number, h: number) {
    initialSize.width = w;
    initialSize.height = h;

    // 尺寸异常时，销毁图表
    if (!canRender()) {
      await destroy();

      return;
    }

    // 调整图表大小
    if (isRendered()) {
      resize();
    }

    // 渲染图表
    await render();
  }

  // 作用域内运行一组副作用
  scope.run(() => {
    // 监听宽度和高度变化，重新渲染图表
    watch([width, height], ([newWidth, newHeight]) => {
      renderChartBySize(newWidth, newHeight);
    });
  });

  // 作用域销毁时，销毁图表和停止作用域
  onScopeDispose(() => {
    destroy();
    scope.stop();
  });

  return {
    domRef,
    updateOptions,
    setOptions
  };
}
