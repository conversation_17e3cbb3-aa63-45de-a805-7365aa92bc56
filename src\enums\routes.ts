export const enum RoutesName {
  Root = "sg.store",
  Login = "sg.store.login",
  DoctorLogin = "sg.store.doctorend.login",
  Check = "sg.store.check",
  Exception403 = "sg.store.exception.403",
  Exception404 = "sg.store.exception.404",
  UnResiter = "sg.store.unresiter",
}

// 商城功能
export const enum RoutesName {
  StoreModule = "sg.store.store.module",
  Home = "sg.store.home",
  GoodsManagement = "sg.store.goods.management",
  CustomerManagement = "sg.store.customer.management",
  OrderManagement = "sg.store.order.management",
  SalesManagement = "sg.store.sales.management",
  AfterServiceManagement = "sg.store.after.service.management",
  DataReports = "sg.store.data.reports",
  StoreConfig = "sg.store.order.storeConfig",
  SupplierManagement = "sg.store.supplier.management",
  SupplierProductManagement = "sg.store.supplierProduct.management",
}

// 医生工作室
export const enum RoutesName {
  Doctor = "sg.store.doctorModule",
  PrescriptionManagement = "sg.store.prescription.management",
  DoctorManagement = "sg.store.doctor.management",
  PharmacistManagement = 'sg.store.pharmacists.management',
  OrganizationManagement = 'sg.store.organization.management',
  OfficesConfig = 'sg.store.department.config',
  DiseaseManagement = 'sg.store.disease.management',
  BasicsSettings = 'sg.store.basic.setting',
  MedicalInquiryForm = 'sg.store.medical.inquiry.form',
  MedicalInquiryPrescription = 'sg.store.medical.inquiry.prescription',
  AssistantDoctorManagement = 'sg.store.assistant.doctor.management'

}

// 内容
export const enum RoutesName{
  Content = "sg.store.content.module",
  VideoManagement = "sg.store.video.management",
  CommentManagement = "sg.store.comment.management",
  LexiconConfig = "sg.store.lexicon.config",
  ContentConfig = "sg.store.content.config",
}

// 分销
export const enum RoutesName {
  structreModule = "sg.store.structre.module",
  structreDepartment = "sg.store.structre.department",
  structreSettings = 'sg.store.structre.settings',
  structreMemberManagement = 'sg.store.structre.memberManagement',
  structreCommissionDeatail = 'sg.store.structre.commissionDeatail',
  structreApplication = 'sg.store.structre.application'
}


// 医师端
export const enum RoutesName{
  DoctorEndClinicalReceptionModule = 'sg.store.doctor.reception.module',
  DoctorEndClinicalReception = 'sg.store.doctor.reception.page',
  DoctorEndPrescriptionModule = 'sg.store.doctor.prescription.module',
  DoctorEndPrescription = 'sg.store.doctor.prescription.page',
  DoctorEndConsultationModule = 'sg.store.doctor.consultation.module',
  DoctorEndConsultation = 'sg.store.doctor.consultation.page',
  DoctorEndSettingModule = 'sg.store.doctor.setting.module',
  DoctorEndSetting = 'sg.store.doctor.setting.page',
}
// 药师处方
export const enum RoutesName {
  PharmacistPrescriptionModule = "sg.store.pharmacist.prescription.module",
  PharmacistPrescription = "sg.store.pharmacist.prescription",
}

// 财务
export const enum RoutesName {
  Finance = "sg.store.finance",
  FinanceMerchantNumberManagement = "sg.store.finance.merchant.management",
  FinanceUserRecharge = "sg.store.finance.recharge",
  FinanceAccountingForRevenueDistribution = "sg.store.finance.allocation.management",
  FinanceExpressDeliveryRecharge = "sg.store.finance.logistics.recharge",
  StoreCommissionRule = "sg.store.finance.commission.rule",
  StoreCommissionDetail = "sg.store.finance.commission.detail",
  FinancePaymentReview = "sg.store.finance.payment.review",
}
// 直播
export const enum RoutesName {
  Live = "sg.store.liveModule",
  LiveList = "sg.store.live.list",
  LiveSensitiveWords = "sg.store.live.sensitive.words",
  ViewDataStatistics = 'sg.store.view.data.statistics',
  ViewDuration = 'sg.store.watch.seconds.page',
  LiveDashboard = 'sg.store.live.dashboard',
}
// 门店
export const enum RoutesName {
  Shop = "sg.store.shop.module",
  ShopManagement = "sg.store.shop.management",
  PersonnelManagement = "sg.store.personnel.management",
  ShopLogistics = 'sg.store.shop.storeLogistics',
  ShopReturnLogistics = 'sg.store.shop.return.logistics',
  DealerManagement = 'sg.store.dealer.management',
  DealerGroup = 'sg.store.dealer.group',
}
