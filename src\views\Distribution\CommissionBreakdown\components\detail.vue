<template>
    <JDrawer
      v-model:show="drawerVisible"
      title="佣金明细详情"
      :isShowFooter="false"
      :isGetLoading="isGetLoading"
      @after-leave="closeDrawer"
      to="#distributorManagement"
      :contents-list="[
        {
          name: '',
          slotName: 'basic_information'
        },
        {
          name: '商品分佣明细',
          slotName: 'merchandise_commission_breakdown'
        },
      ]"
    >
        <template #basic_information>
            <n-form
             :model="model"
             label-width="100"
             label-placement="left"
             require-mark-placement="right-hanging"
             size="small"
             :style="{
               width: '100%',
             }"
             id="orderInfo"
            >
              <n-grid cols="6 m:18 l:18 xl:24" :x-gap="24" responsive="screen">
                   <!-- 第一列 -->
                <n-gi :span="12">
                  <!-- 佣金（元） -->
                  <n-form-item-gi :span="12" label="佣金(元)">
                    <n-input-group>
                      <n-input 
                       v-model:value="model.allocationTotalAmount"
                       readonly 
                       placeholder='无'/>
                    </n-input-group>
                  </n-form-item-gi>
                  <!-- 佣金状态 -->
                  <n-form-item-gi :span="6" label="佣金状态">
                    <n-select 
                      v-model:value="model.status"
                      :options="CommissionStatusOptions" 
                      placeholder='无'
                      style="width: 100%;"
                      clearable
                      disabled
                    />
                  </n-form-item-gi>
                  <!-- 订单编号 -->
                  <n-form-item-gi :span="12" label="订单编号">
                    <n-input-group>
                      <n-input 
                       v-model:value="model.orderCode"
                       readonly 
                       placeholder='无'/>
                    </n-input-group>
                  </n-form-item-gi>
                  <!-- 分销员昵称 -->
                  <n-form-item-gi :span="12" label="分销员昵称">
                    <n-input-group>
                      <n-input 
                       v-model:value="model.accountUserName"
                       readonly 
                       placeholder='无'/>
                    </n-input-group>
                  </n-form-item-gi>
                  <!-- 分销员ID -->
                  <n-form-item-gi :span="12" label="分销员ID">
                    <n-input-group>
                      <n-input 
                       v-model:value="model.accountUserId"
                       readonly 
                       placeholder='无'/>
                    </n-input-group>
                  </n-form-item-gi>
                  <!-- 组织名称 -->
                  <n-form-item-gi :span="12" label="组织名称">
                    <n-input-group>
                      <n-input 
                       v-model:value="model.structureName"
                       readonly 
                       placeholder='无'/>
                    </n-input-group>
                  </n-form-item-gi>
                  <!-- 组织ID -->
                  <n-form-item-gi :span="12" label="组织ID">
                    <n-input-group>
                      <n-input 
                       v-model:value="model.structureId"
                       readonly 
                       placeholder='无'/>
                    </n-input-group>
                  </n-form-item-gi>
                  <!-- 创建时间 -->
                  <n-form-item-gi :span="12" label="创建时间">
                    <n-input-group>
                      <n-input 
                       v-model:value="model.createTime"
                       readonly 
                       placeholder='无'/>
                    </n-input-group>
                  </n-form-item-gi>
                  <!-- 结算时间 -->
                  <n-form-item-gi :span="12" label="结算时间">
                    <n-input-group>
                      <n-input 
                       v-model:value="model.allocateTime"
                       readonly 
                       placeholder='无'/>
                    </n-input-group>
                  </n-form-item-gi>
                  <!-- 取消结算时间 -->
                  <n-form-item-gi :span="12" label="取消结算时间">
                    <n-input-group>
                      <n-input 
                       v-model:value="model.cancelTime"
                       readonly 
                       placeholder='无'/>
                    </n-input-group>
                  </n-form-item-gi>
                </n-gi>
              </n-grid>
            </n-form>
        </template>
        <template #merchandise_commission_breakdown>
          <n-data-table
            :columns="tableColumns"
            :data="tableData"
          >
            <template #empty>
              <div class="infoWrapper">
                <img style="height: 210px;" :src="EmptyDataSrc" alt="" />
                <div class="notice">暂无数据</div>
              </div>
            </template>
          </n-data-table>
        </template>
    </JDrawer>
</template>
<script lang="tsx" setup name="commissionBreakdown">
import { ref } from "vue";
import { deepClone } from "@/utils";
import { CommissionStatusOptions } from "@/constants";
import EmptyDataSrc from "@/assets/image/exception/emptyData.png";
import { distributionGet } from "@/services/api";
import { useMessages } from "@/hooks";
const message = useMessages();
/** 抽屉状态 */
const drawerVisible = ref(false);

/** 请求数据状态 */
const isGetLoading = ref(false)
const tableData = ref([])
const tableColumns = [
  {
    title: '商品id',
    key: 'productId',
    width: 200,
    tree:true
  },
  {
    title: "商品名称",
    key: "productName",
    width: 200,
  },
  {
    title: "商品规格",
    key: "productSpecName",
    width: 200,
  },
  {
    title: "销售单价",
    key: "unitPrice",
    width: 200,
    render: rowData => {
      return rowData.unitPrice?(Number(rowData.unitPrice) / 100).toFixed(2):'-';
    },
  },
  {
    title: "数量",
    key: "number",
    width: 200,
  },
  {
    title: "总金额(元)",
    key: "totalAmount",
    width: 200,
    render: rowData => {
      return rowData.totalAmount?(Number(rowData.totalAmount) / 100).toFixed(2):'-';
    },
  },
  {
    title: "分佣比例",
    key: "dealerAllocationRatio",
    width: 200,
    render: rowData => {
      return rowData.dealerAllocationRatio?rowData.dealerAllocationRatio+'%':'-';
    },
  },
  {
    title: "分佣金额(元)",
    key: "dealerAllocationAmount",
    width: 200,
    render: rowData => {
      return rowData.dealerAllocationAmount?(Number(rowData.dealerAllocationAmount) / 100).toFixed(2):'-';
    },
  },
]
/* 表单参数初始化 */
const initParams = {
  allocationNo:null,
  accountUserId:null,
  accountUserName:null,
  allocationTotalAmount:null,
  allocateTime:null,
  cancelTime:null,
  status:null,
  orderCode:null,
  structureName:null,
  structureId:null,
  createTime:null,
}

const model = ref(deepClone(initParams));

/** 关闭抽屉 */
const closeDrawer = () => {
    model.value = deepClone(initParams);
    drawerVisible.value = false;
};

/* 接收父组件传过来的参数 */
const acceptParams = async(id) => {
  drawerVisible.value = true;
  isGetLoading.value = true;
  try {
    let res = await distributionGet(id)
    model.value.allocationNo = res.allocationNo
    model.value.accountUserId = res.accountUserId
    model.value.accountUserName = res.accountUserName
    model.value.allocateTime = res.allocateTime
    model.value.cancelTime = res.cancelTime
    model.value.orderCode = res.orderCode
    model.value.structureName = res.structureName
    model.value.structureId = res.structureId
    model.value.createTime = res.createTime
    model.value.allocationTotalAmount = res.allocationTotalAmount?Number(res.allocationTotalAmount / 100).toFixed(2):'0.00'
    model.value.status = res.status
    tableData.value = res.allocationItemList
  }catch (e) {
    message.createMessageError(e)
  }finally {
    isGetLoading.value = false;
  }
};

defineExpose({
  acceptParams,
  closeDrawer,
  drawerVisible
});
</script>
<style scoped lang="less">
  @import "@/styles/default.less";
  @import "@/styles/defaultVar.less";
  .infoWrapper {
    width: 100%;
    text-align: center;
    padding: 70px;
    box-sizing: border-box;
  }
</style>