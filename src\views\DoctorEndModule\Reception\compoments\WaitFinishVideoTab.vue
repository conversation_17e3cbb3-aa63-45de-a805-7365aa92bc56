<template>
  <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumnsSource"
      :pagination="paginationRef"
      @paginationChange="paginationChange"
      :isTableSelection="true"
  >
    <template #searchForm>
      <n-form
          ref="formRef"
          :model="formValue"
          :show-feedback="false"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          size="small"
          :style="{ width: '100%' }">
      </n-form>
    </template>
    <!-- 操作栏 -->
    <template #tableHeaderBtn="scoped">
      <n-button @click="refresh" class="store-button">刷 新</n-button>
    </template>
    <template #tableFooterBtn="scope">
    </template>
  </FormLayout>
  <ExitDiagnosis ref="exitDiagnosisShow"/>
  <InquiryCardModal ref="InquiryCardModalRef"/>
</template>
<script setup lang="tsx">
import {onMounted, ref, watch} from 'vue';
import FormLayout from "@/layout/FormLayout.vue";
import {useTableDefault} from "@/hooks/useTableDefault";
import {
  getPrescriptionDetail,
  presBatchUpdateStatus,
  presBatchDelete,
  presUpdate,
  presPrescribing
} from "@/services/api";
import {useMessages} from "@/hooks";
import {NButton, NSpace} from "naive-ui";
import ExitDiagnosis from "@/views/DoctorEndModule/Reception/compoments/ExitDiagnosis.vue";
import {
  doctorPresCompletePres,
  receptionPresBatchConsult,
  receptionPresCancelConsult, receptionPresConsult,
  receptionPresCountToBeConsulted,
  receptionPresPage
} from "@/services/api/doctorEndApi";
import {doctorWaitFinishVideo} from "@/services/api/doctorEndApi/waitFinishVideo";
import InquiryCardModal from "@/views/DoctorEndModule/Reception/compoments/InquiryCardModal.vue";
import moment from "moment/moment";
import {formatTimeToDHMS} from "@/utils/dateUtils";

enum OperationTypeEnum {
  beginReception = '进入视频',
  cancelReception = '结束问诊',
}

interface prescriptionProps {
  tabNameRef: string; // tab标签值
}

defineOptions({
  name: 'WaitFinishVideoTab'
})

/** props */
const props = withDefaults(defineProps<prescriptionProps>(), {
  tabNameRef: 'wait_finish_video'
});
const emit = defineEmits(["tabQuantity"])

const {createMessageSuccess, createMessageError} = useMessages();
const isBatchLoading = ref<boolean>(false)
const totalCount = ref();
const receptionTypeMap = new Map([
  [1, '常规问诊'],
  [2,'预约问诊']
])

/* 初始化参数 */
const tabValue = ref(null);
const initParams = {
  searchValue: '', // 搜索关键字
  source: null, // 来源
  searchType: ''
};
const formValue = ref({...initParams});

/* 表格方法Hook */
const {
  isLoading,
  tableData,
  paginationRef,
  paginationChange,
  summaryRef,
  sortTableData,
  pageTableData,
} = useTableDefault({
  pageDataRequest: doctorWaitFinishVideo,
});

onMounted(() => {
  // getTableData()
  receptionPresCountToBeConsulted({})
})

//刷新
const refresh = () => {
  getTableData()
}

/** 搜索 */
const formSearch = () => {
  getTableData()
}

/** 获取参数 */
const getParams = () => {
  const {} = formValue.value;

  return {}
};

/** 请求表格数据 */
function getTableData() {
  pageTableData(getParams(), paginationRef.value, true);
}

//触发tab数量事件
const tabClick = () => {
  emit('tabQuantity', props.tabNameRef, totalCount.value);
}

function formatMinutesToDHM(totalMinutes) {
  const days = Math.floor(totalMinutes / (60 * 24));
  const hours = Math.floor((totalMinutes % (60 * 24)) / 60);
  const minutes = totalMinutes % 60;

  return [
    days > 0 && `${days}天`,
    hours > 0 && `${hours}小时`,
    minutes > 0 || (days === 0 && hours === 0) ? `${minutes}分钟` : null
  ].filter(Boolean).join(" ");
}

/* 表格项 */
const tableColumns = ref([]);
const tableColumnsSource = [
  {
    title: "用户昵称",
    key: "nickname",
    align: "left",
    fixed: "left",
    width: 100,
  },
  {
    title: "患者信息",
    key: "patientName",
    align: "left",
    width: 100,
    render: (row) => {
      return `${row.patientName || '-'}(${row.patientSex}  ${row.patientAge})`
    }
  },
  {
    title: "主诉",
    key: "chiefComplaint",
    align: "left",
    width: 100,
  },
  {
    title: '订单类型',
    key: "type",
    align: "left",
    width: 120,
    render: (row) => {
      return <span>{receptionTypeMap.get(row.type) ?? "-"}</span>;
    }
  },
  {
    title: "预约问诊时间",
    key: "remainingDuration",
    align: "left",
    width: 100,
    render: (row) => {
      if (!row.preBookTime) return '-'
      return <>
        <NSpace vertical>
          <span>{row.preBookTime}</span>
          <n-ellipsis>
            <span>{row.physicianAssistantName ? `医助:${row.physicianAssistantName}` : '-'}</span>
          </n-ellipsis>
        </NSpace>
      </>
    }
  },
  {
    title: "剩余接诊时长",
    key: "remainingDuration",
    align: "left",
    width: 100,
    render: (row) => {
      if (!row.remainingDuration) {
        if (row.type == 1) {
          return '-'
        }
        if (row.type == 2) {
          return '未到问诊时间'
        }
      }
      return formatTimeToDHMS(
          moment().valueOf(),
          moment().add({minute: row.remainingDuration}).valueOf()
      )
    }
  },
  {
    title: "操作",
    key: "action",
    width: 120,
    align: "left",
    fixed: "right",
    render: (row) => {
      return (
          <NSpace align="center" justify="center">
            {/* 接诊 */}
            <NButton text style={"margin-right: 8px;"} type="info" onClick={() => handleSubmitVideoReception(row)}>
              {OperationTypeEnum.beginReception}
            </NButton>
            {/* 结束问诊 */}
            <NButton style={"margin-right: 8px;"} text type="info" onClick={() => clickCancelReception(row)}>
              {OperationTypeEnum.cancelReception}
            </NButton>
          </NSpace>
      );
    },
  },
];

/** 开处方 与 编辑 */
const drawerPrescriptionShow = ref(null);
const clickPrescribe = (row, type) => {
  const _params = {
    row,
    getInfoApi: getPrescriptionDetail,
    updateInfoApi: type == '编辑' ? presUpdate : presPrescribing,
    refresh: refresh,
    type: type
  };
  drawerPrescriptionShow.value?.acceptParams(_params);
};

/**
 * 接诊、批量接诊
 */
const clickReception = async (idList: Array<string>, operationType?: '批量') => {
  try {
    isBatchLoading.value = true;
    if (operationType == '批量') {
      await receptionPresBatchConsult({
        data: {idList}
      })
    } else {
      await receptionPresConsult({
        data: {id: idList[0]}
      })
    }
    createMessageSuccess(`${operationType || ''}接诊成功`)
    refresh()
  } catch (error) {
    createMessageError(`${operationType || ''}接诊失败:${error}`)
  } finally {
    isBatchLoading.value = false;
  }
}

/** 批量取消、取消、恢复 */
const batchCancellationRecover = async (type: 'single-cancel' | 'batch-cancel' | 'single-recover' | 'batch-recover', ids: Array<string>) => {
  const params = {
    idList: ids,
  };
  const msg: Record<'single-cancel' | 'batch-cancel' | 'single-recover' | 'batch-recover', string> = {
    'single-cancel': '取消',
    'batch-cancel': '批量取消',
    'single-recover': '恢复',
    'batch-recover': '批量恢复',
  };
  try {
    await presBatchUpdateStatus(params);
    createMessageSuccess(`${msg[type]}成功`);
    refresh()
  } catch (err) {
    createMessageError(`${msg[type]}失败: ${err}`)
  }
};

/** 删除 */
const clickDelete = async (type: 'single' | 'batch', selectId: Array<string>, selectRowList: Array<any>) => {
  // customerIdList
  const createByList = selectRowList.map(item => item.createBy);

  const params = {
    idList: selectId,
    createByList: createByList,
  }
  try {
    await presBatchDelete({data: params})
    createMessageSuccess(type === 'batch' ? '批量删除成功' : '删除成功');
    refresh()
  } catch (err) {
    createMessageError(`${type === 'batch' ? '批量删除失败' : '删除失败'}: ${err}`);
  }
};

// 退诊原因
const exitDiagnosisShow = ref<InstanceType<typeof ExitDiagnosis> | null>(null);
const clickExitDiagnosisShow = (row) => {
  const _params = {
    show: true,
    api: receptionPresCancelConsult,
    rowData: row,
    refresh
  }
  exitDiagnosisShow.value?.acceptParams(_params)
}

// 预约问诊-结束问诊
const clickCancelReception = async (row: object & { id: string }) => {
  try {
    await doctorPresCompletePres({id: row.id})
    createMessageSuccess(`${OperationTypeEnum.cancelReception}成功`)
    refresh()
  } catch (error) {
    createMessageError(`${OperationTypeEnum.cancelReception}失败:${error}`)
  }
}

/* 视频接诊问诊卡句柄 */
const InquiryCardModalRef = ref<InstanceType<typeof InquiryCardModal> | null>(null);

/* 处理视频接诊 */
async function handleSubmitVideoReception(row) {
  const _params = {
    api: receptionPresCancelConsult,
    rowData: row,
    refresh
  }
  InquiryCardModalRef.value?.acceptParams(_params)
}



/** 监听 */
watch(() => props.tabNameRef, (newVal) => {
      tabValue.value = newVal;
      getTableData();
    },
    {
      immediate: true
    });

/* 监听 分页 */
watch(paginationRef, (newVal, oldVal) => {
  totalCount.value = newVal.total;
  tabClick()
},);
</script>

<style lang="less" scoped>
@import "@/styles/default.less";

:deep .n-tag__content {
  text-overflow: ellipsis;
  overflow: hidden;
}

</style>
