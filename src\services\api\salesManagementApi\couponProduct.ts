import { defHttp } from "@/services";

/** 福利商品 */
export const enum couponProductApi {
  add = "/couponProduct/add",
  update = "/couponProduct/update",
  page ="/couponProduct/page",
  get = "/couponProduct/getCouponProductDetail",
  publish = "/couponProduct/batch/publish"
}

/**
 * @description 新建福利商品
 */
export function addCouponProduct(_params) {
  return defHttp.post({
    url: couponProductApi.add,
    params: {
      data: _params,
    },
  });
}

/**
 * @description 更新福利商品
 */
export function updateCouponProduct(_params) {
  return defHttp.put({
    url: couponProductApi.update,
    params: {
      data: _params,
    },
  });
}

/**
 * @description 福利商品分页
 */
export function pageCouponProduct(_params) {
  return defHttp.post({
    url: couponProductApi.page,
    params: _params,
  });
}

/**
 * @description 福利商品详情
 */
export function getCouponProductDetail(id: string) {
  return defHttp.get({
    url: couponProductApi.get + `?id=${id}`,
  });
}

/** 批量上下架 */
export function batchUnmountCouponProduct(_params:{ productIds: string, isPublish: '0' | '1', cateIds: string }) {
  return defHttp.put({
    url: couponProductApi.publish + `?productIds=${_params.productIds}&isPublish=${_params.isPublish}&cateIds=${_params.cateIds}`,
  })
};