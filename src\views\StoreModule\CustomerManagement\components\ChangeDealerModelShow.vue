<template>
  <JModal
    v-model:show="show"
    width="680"
    :title="title"
    @after-leave="closeModal"
    @positive-click="_save"
    :positiveButtonProps="{
          loading: isLoading
      }"
  >
    <n-form
      ref="formRef"
      :rules="rules"
      :model="model"
      label-width="auto"
      label-placement="left"
      require-mark-placement="right-hanging"
      :style="{
            width: '100%',
          }"
    >
      <n-grid :cols="8" :x-gap="24">
        <n-form-item-gi :span="6" label="归属" path="changeType">
          <JShopUserSelect v-model:value="model.changeType" placeholder="请输入店员/店长或者ID查询"></JShopUserSelect>
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </JModal>
</template>

<script setup lang="ts" name="ChangeDealerModelShow">
import { ref, watch } from "vue";
import { useMessages } from "@/hooks";
import { customerEntityChangeStaffRelation} from "@/services/api";
import JShopUserSelect from "@/components/JSelect/JShopUserSelect.vue";
const initParams = {
  changeType:null
};
const model = ref({ ...initParams });
export interface CreditChangesProps {
  row?: any;
  refresh?: () => void; // 刷新表格
}
/* 提示信息 */
const message = useMessages();
/* 模态框显隐状态 */
const show = ref(false);
const title=ref('')

/* 表单规则 */
const rules = {
  changeType:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入店员/店长或者ID查询",
    validator: ()=>{
      return model.value.changeType != null;
    }
  },
};
const acceptParams = (params) => {
  parameter.value = params
  title.value=`修改归属店员【会员昵称：${params?.row?.nickname??''}】`
  show.value = true
};
/* 表单实例 */
const formRef = ref(null);

/* 表单参数初始化 */
/* 清空表单 */
const formDataReset = () => {
  model.value = { ...initParams };
};

const parameter = ref<CreditChangesProps>({
});

/* 关闭弹窗 */
const closeModal = () => {
  formDataReset();
  // 弹窗取消
  show.value = false;
};

/* 确认--保存 */
const isLoading = ref(false);
const _save = (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors && !isLoading.value) {
      isLoading.value = true;
      const params = {
        data:{
          sourceCustomerId:parameter.value.row.id,
          targetMemberId:model.value.changeType,
        }
      }
      try {
        await customerEntityChangeStaffRelation(params)
        // 刷新表格数据
        parameter.value.refresh();
        closeModal()
        message.createMessageSuccess('修改归属店员成功');
      } catch (e) {
        message.createMessageError(`修改归属店员失败： ${e}`);
      }finally{
        isLoading.value = false;
      }
    }
  });
};
defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less">
::v-deep .n-form-item-label{
  width: 70px !important;
}
</style>
