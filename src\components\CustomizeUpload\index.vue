<script setup lang="ts">
import { fileUpload, fileUploadCiccImg } from "@/services/api";
import type { UploadCustomRequestOptions, UploadFileInfo } from "naive-ui";
import { computed, h, onMounted, ref, toRef, toRefs, watch } from "vue";
import { useMessages } from "@/hooks";
import { getMediaDuration, revertToMinioSrc, transformMinioSrc, asyncFileToBase64 } from "@/utils/fileUtils";
import { isString } from "@/utils/isUtils";
import type { FileInfo } from "naive-ui/es/upload/src/interface";
import videoIconSrc from "@/assets/image/system/videoIcon.jpg";
const { createMessageWarning, createMessageError } = useMessages();
const props = withDefaults(
  defineProps<{
    value: Array<string> | string;
    listType?: "text" | "image" | "image-card";
    valueKey?: string;
    maxFileSize?: number; // 文件大小
    fileListSize?: number;
    accept?: string;
    sizeTitle?:string; // 自定义超出大小提示语
    disabled?:boolean;
    isThumb?: boolean; // 是否生成缩略图
    isSignatureImg?: boolean; // 是否为签名图片
    isCrop?:boolean; // 是否开启裁剪
    imageUploadType?:Array<string>; // 限制上传类型
    enableTypeValidation?:boolean; // 是否启用上传图片类型校验
    isZjImgUpload?:boolean; // 是否为中金图片上传
    merchantNumber?:string; //
  }>(),
  {
    listType: "image-card",
    valueKey: "data",
    sizeTitle:'',
    maxFileSize: 0,
    fileListSize: 1,
    isThumb: false,
    isSignatureImg:false,
    isCrop:false,
    enableTypeValidation:false,
    isZjImgUpload:false,
    merchantNumber:''
  }
);

const valueRef = toRef(props, "value");
const acceptRef = toRef(props, "accept");
const showVideoModalRef = ref(false);
const previewVideoSrcRef = ref("");
let tempValue = isString(valueRef.value)
  ? [valueRef.value]
  : [...valueRef.value];
const {
  listType: listTypeRef,
  valueKey: keyRef,
  maxFileSize: maxFileSizeRef,
  fileListSize: fileListSizeRef,
} = toRefs(props);

const fileList = ref<Array<UploadFileInfo>>([]);
const isVideoAccept = computed(() => acceptRef.value.indexOf("video") != -1);
const avatarEditModalShowRef = ref(false)
const cropperSrcRef= ref()
watch(
  valueRef,
  (newVal) => {
    const fileListTemp: Array<UploadFileInfo> = [];
    newVal = isString(newVal) ? [newVal] : newVal;
    newVal.forEach((url, index) => {
      if (url) {
        const _id = `${Math.random() * 1000}-${index}`;
        fileListTemp.push({
          id: _id,
          name: `file-${_id}`,
          status: "finished",
          url: transformMinioSrc(url),
          thumbnailUrl: isVideoAccept.value ? videoIconSrc : null,
        });
      }
    });
    fileList.value = [...fileListTemp];
  },
  { immediate: true }
);
const emits = defineEmits<{
  (e: "update:value", urlList: Array<string> | string): void;
  (e: 'update:duration',duration:Array<string>| string): void;
  (e:'update:imgId',imgId:string): void;
}>();

async function customRequest(options: UploadCustomRequestOptions) {
  const { file, onFinish, onError, onProgress } = options;
  const _formData = new FormData();
  _formData.append("files", file.file);
  let duration;
  if(isVideoAccept.value){
    try{
      const {size,type} = file.file
      duration = await getMediaDuration(file.file)
    }
    catch(e){
      createMessageError(`获取媒体文件时长失败: ${e}`);
      onError();
      return;
    }
  }

  if(!props.isCrop){
      try {
      const resp = !props.isZjImgUpload ?  await fileUpload(props.isThumb, _formData, ({ progress }) => {
        onProgress({ percent: Number((progress * 100).toFixed(2)) });
      }) :  await fileUploadCiccImg(props.merchantNumber, _formData);
      onFinish();
      if (isString(valueRef.value)){
        // 判断条件用于入账方信息录用 不为中金支付
        if(!props.isZjImgUpload){
          emits("update:value", resp);
        }

        // 判断条件用于入账方信息录用 为中金支付
        if(props.isZjImgUpload){
          emits("update:value", resp.path);
          emits('update:imgId', resp.imgId);
        }
        
        if(isVideoAccept.value){
          emits("update:duration", `${duration}`);
        }
      }
      else {
        if (tempValue.length >= fileListSizeRef.value) {
          tempValue.splice(0, 1);
        }
        tempValue.push(resp);
        emits("update:value", [...tempValue]);
      }
    } catch (e) {
      createMessageError(e);
      onError();
    }
  }

  if(props.isCrop){
    try {
    const blob = await asyncFileToBase64(file.file);
      cropperSrcRef.value = blob;
      avatarEditModalShowRef.value = true;
    } catch (error) {
      createMessageError('asyncFileToBase64 错误:');
    }
  }
  
}
function fileRemoveHandler(options: {
  file: UploadFileInfo;
  fileList: Array<UploadFileInfo>;
}) {
  const { url, status } = options.file;
  if (status === "uploading") {
    createMessageWarning("正在上传中，请勿删除");
    return false;
  }
  tempValue = tempValue.filter((item) => item !== revertToMinioSrc(url));
  if (isString(valueRef.value)) emits("update:value", tempValue.join(","));
  else emits("update:value", [...tempValue]);
  return false;
}

function beforeUpload(data: {
  file: UploadFileInfo;
  fileList: UploadFileInfo[];
}) {
  const {
    file: { size,type},
  } = data.file;
  if (maxFileSizeRef.value) {
    
    if (size > maxFileSizeRef.value * 1024 * 1024) {
      createMessageError(
        props.sizeTitle?props.sizeTitle:`该文件超出大小，大小限制为 ${maxFileSizeRef.value} MB`
      );
      return false;
    }
  }

  /** 图片类型上传限制 */
  if(props.enableTypeValidation){
    if(!props.imageUploadType.includes(type)){
      const resultArr = props.imageUploadType.map(item => item.replace(/^image\//, ''))
      createMessageError('请上传' + resultArr.toString() + '类型的图片');
      return false
    }
  }
  /** 中金禁止上传图片条件 （支付渠道: 中金支付 && 商户号: null） */
  if(props.isZjImgUpload && props.merchantNumber == null){
    createMessageError('中金上传图片商户号不可为空,请先选择商户号');
    return false
  }

  return true;
}
function videoPreview({ url }: FileInfo) {
  previewVideoSrcRef.value = url;
  showVideoModalRef.value = true;
}

function updateCropImage (value) {
  emits("update:value", value);
}

</script>

<template>
  <template v-if="isVideoAccept">
    <n-upload
      v-bind="$attrs"
      :custom-request="customRequest"
      :list-type="listTypeRef"
      :accept="acceptRef"
      :disabled="props.disabled"
      v-model:file-list="fileList"
      @remove="fileRemoveHandler"
      @before-upload="beforeUpload"
      @preview="videoPreview"
    />
    <slot :changeModalShow="(url)=>{
      videoPreview(url)}"></slot>
    <n-modal
      v-model:show="showVideoModalRef"
      preset="card"
      style="width: 400px; height: 600px"
      :auto-focus="false"
      title="视频预览"
      :content-style="{
        padding: '5px 10px',
      }"
      :header-style="{
        padding: '5px 10px',
        fontSize: '15px',
      }"
    >
      <video :src="(previewVideoSrcRef)" controls style="height: 550px; width: 100%"></video>
    </n-modal>
  </template>
  <template v-else>
    <n-upload
      v-bind="$attrs"
      :custom-request="customRequest"
      :list-type="listTypeRef"
      :accept="acceptRef"
      :disabled="props.disabled"
      v-model:file-list="fileList"
      @remove="fileRemoveHandler"
      @before-upload="beforeUpload"
    />
    <CropImageUpdates 
    v-bind="$attrs"
     v-model:show="avatarEditModalShowRef"
     v-model:value="cropperSrcRef"
     @updateCropImage="updateCropImage"
     ></CropImageUpdates>
  </template>
</template>
