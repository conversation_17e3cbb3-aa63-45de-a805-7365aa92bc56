import { computed, reactive, ref, watch } from "vue";
import { createCompactHeaders } from "@/components/CompactHeaders/createCompactHeaders";
import { NIcon, type DataTableColumn } from "naive-ui";
import { isNumber, isString, isNullOrUnDef, isNullStringOrNullOrUnDef } from "@/utils/isUtils";
import type { TableSelectionColumn } from "naive-ui/es/data-table/src/interface";
import HelpPopover from "@/components/HelpPopover/index.vue";
import { SettingsOutline as SettingsIcon, ChevronDown as DownIcon } from "@vicons/ionicons5";
import TableSorter from "@/components/TableSorter/index.vue";
interface compactHeadersProps {
  columns: Array<DataTableColumn>;
  isSelection?: boolean;
  isMultiple?: boolean;
  isDisplayIndex?: boolean;
  key?: "" | undefined;
  saveLocalAuth?: "" | undefined;
  saveGlobalAuth?: "" | undefined;
  customCols?: boolean;
  summary: object;
}

export const useCompactHeaders = ({
  columns = [],
  isSelection = true,
  isMultiple = true,
  isDisplayIndex = false,
  key = undefined,
  saveLocalAuth = undefined,
  saveGlobalAuth = undefined,
  customCols = true,
  summary = {},
}: compactHeadersProps) => {
  const refShow = ref<boolean>(false);
  const selectionIndex: number = columns.findIndex(item => item.type === "selection");

  const defaultSortIndex = columns.findIndex(item => item.isSortDefault);

  const totalSummaryRef = ref();
  const SummaryStyle = {
    fontWeight: 600,
  };
  const sorterInfo = ref<{
    key: string;
    type: "descend" | "ascend" | false;
    defaultKey: string;
  }>({
    key: defaultSortIndex != -1 ? columns[defaultSortIndex].key : "",
    type: defaultSortIndex != -1 ? "descend" : false,
    defaultKey: defaultSortIndex != -1 ? columns[defaultSortIndex].key : "",
  });
  const indexColTpl: DataTableColumn = {
    title: "序号",
    width: 100,
    minWidth: 100,
    fixed: "left",
    key: "index",
    align: "center",
    render: (renderData: object, index: number) => {
      return `${index + 1}`;
    },
  };

  const selectionColTpl = (isMultiple: boolean): TableSelectionColumn => {
    return {
      type: "selection",
      multiple: isMultiple,
      key: "selection",
      fixed: "left",
      width: 40,
      minWidth: 40,
      align: "center",
    };
  };

  if (selectionIndex === -1) {
    if (columns.findIndex(item => "index" === item?.key) == -1) {
      isDisplayIndex && columns.unshift(indexColTpl);
    }
    isSelection && columns.unshift(selectionColTpl(isMultiple));
  } else {
    if (isMultiple && (!columns[selectionIndex] as TableSelectionColumn["multiple"])) {
      throw new Error(`传入isMultiple值: ${isMultiple} 与传入columns值中的multiple值冲突`);
    }
  }

  let totalWidth = 0;
  columns.forEach(item => {
    if (!item.width) item.width = 160;
    if (item.title && isString(item.title)){
      item.colName = item.title;
    }
    if (isNullOrUnDef(item.summaryTitle)){
      item.summaryTitle = `总${item.colName}`;
    }
    if (isNullOrUnDef(item.isRequired)) {
      item.isRequired = item.fixed ? true : false;
    }
    if (item["key"] && item["key"].indexOf("Time") !== -1) {
      item.width = 160;
    }
    if (isNumber(item.width)) {
      totalWidth = totalWidth + item.width;
    }
    // if(item.type !=='selection' && item.key !=='index' ) item.align='left'
    if (!item.align) item.align = "left";
    
    if(item.type !=='selection' && item.key !=='index' ){
      item.minWidth = 50;
      item.maxWidth = 400;
      if(isNullOrUnDef(item.resizable))  item.resizable = true;
      if(item.key == 'operation') item.resizable = false;
    }

    if (!item.render) {
      item.render = function (rowData) {
        const value = rowData[item.key];
        return (
          <span>
            {(isString(value) && value !== "") || (!isString(value) && !isNullOrUnDef(value)) || isNumber(value)
              ? value
              : "-"}
          </span>
        );
      };
    }

    if (isNullOrUnDef(item.ellipsis) || item.ellipsis) {
      item.ellipsis = {
        tooltip: {
          style: {
            "max-width": "200px",
          },
        },
        // lineClamp:2
      };
    }
    if (item.helpKey && isString(item.title)) {
      const _renderTitle = () => (
        <div style="display: flex;align-items: center;">
          <HelpPopover help-entry={item.helpKey} text={item.title}/>
        </div>
      );
      item.title = _renderTitle();
    }
    if ((item.key === "action" || item.key === "operation") && customCols && isString(item.title)) {
      const _renderTitle = () => (
        <div style="width: 100%;display: flex;justify-content: space-between;align-items: center;">
          <span>{item.title}</span>
          <n-button
            type="info"
            text
            onClick={() => openModal()}
            style="width: 20px;height: 20px;cursor:pointer;color:#165dff;"
          >
            <n-icon size="16" depth={2}>
              <SettingsIcon />
            </n-icon>
          </n-button>
        </div>
      );
      item.title = _renderTitle();
      item.ellipsis = false;
    }
    if (item.filter && !item.renderFilterIcon) {
      const renderIcon = () => {
        return (
          <NIcon size="16" depth={2}>
            <DownIcon />
          </NIcon>
        );
      };
      item.renderFilterIcon = renderIcon;
    }
    
    if (item.sorter) {
      if(isNullOrUnDef(item.sorterType)){
        item.sorterType = 'async'
      }
      item.sortOrder = computed(() => {
        //目前只支持单列排序
        if (sorterInfo.value.key === item.key) {
          return sorterInfo.value.type;
        } else {
          return false;
        }
      });
      item.renderSorterIcon=({order})=>(<TableSorter order={order}></TableSorter>)
      item.align = 'right'
    }
  });

  watch(summary, newVal => {
    const summaryObj = {};
    columns.forEach((item, index) => {
      if (item.key === "index") {
        summaryObj[item.key] = {
          value: <span class='summaryText' style={{
            ...SummaryStyle,
            fontSize:'13px'
          }
           
          }>{"本页合计"}</span>,
        };
      } else {
        summaryObj[item.key] = {
          value: (
            <span class='summaryText' style={SummaryStyle}>{isNullStringOrNullOrUnDef(newVal[item.key]) ? "" : newVal[item.key]}</span>
          ),
        };
      }
    });
    totalSummaryRef.value = () => {
      return summaryObj;
    };
  });

  const refCols = ref<Array<DataTableColumn>>(columns);
  const configuration = {
    show: refShow,
    columns: refCols,
    key: key,
    saveLocalAuth: saveLocalAuth,
    saveGlobalAuth: saveGlobalAuth,
  };
  // const container = customCols?createCompactHeaders(configuration):null

  //
  function openModal(): void {
    console.log("here");
    refShow.value = true;
  }
  const closeModal = (): void => {
    refShow.value = false;
  };
  const removeModal = (): void => {
    // if (container) document.removeChild(container)
  };
  return {
    cols: refCols,
    openModal,
    closeModal,
    removeModal,
    totalWidth,
    refShow,
    tableSummaryFn: totalSummaryRef,
    sorterInfo,
  };
};
