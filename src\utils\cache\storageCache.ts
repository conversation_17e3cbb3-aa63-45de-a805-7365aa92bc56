import { decryption, encryption } from "../crypto";
import { isArray, isNullOrUnDef, isObject } from "../isUtils";
import type { Cache, CacheSaveObject, CacheValueType } from "./type";
import dayjs from "dayjs";
import { Cache_Key, StorageType } from "@/enums/cache";
import { isDevEnv } from "../envUtils";

type CacheMapType = Map<Cache_Key,WebStorage>

const lsMap:CacheMapType = new Map();
const ssMap:CacheMapType = new Map();

class WebStorage {
  private storage: Storage;
  private hasEncrypt: boolean;
  private KEY: Cache_Key;
  private expire: null | number;
  private type: StorageType;

  constructor({ storage = sessionStorage, hasEncrypt = true, key, expire,type }) {
    this.storage = storage;
    this.hasEncrypt = isDevEnv() ? false : hasEncrypt;
    this.KEY = key;
    this.expire = expire;
    this.type = type;
  }
  
  get(objectKey?: string | number): CacheValueType {
    const val = this.storage.getItem(this.KEY);
    if (!val) return null;
    const desVal = this.hasEncrypt ? decryption(val) : val;
    const data:CacheSaveObject = JSON.parse(desVal);
    const { value, expire } = data;
    if (isNullOrUnDef(expire) || expire >= dayjs().valueOf()) {
      if (isObject(value) && !isNullOrUnDef(objectKey)) return value[`${objectKey}`];
      else return value;
    }else{
      this.remove();
      return null;
    }
  }

  set(value: CacheValueType, objectKey?: string | number) {
    let cacheValue:CacheValueType;
    if (!isNullOrUnDef(objectKey)) {
      const tempCache:Record<string,CacheValueType> = isObject(this.get())?this.get() as Record<string,CacheValueType>:{};
      tempCache[`${objectKey}`] = value;
      cacheValue = tempCache;
    } else {
      cacheValue = value;
    }
    const cacheValueObject:CacheSaveObject = {
      value: cacheValue,
      expire: !isNullOrUnDef(this.expire) ? dayjs().valueOf() + this.expire * 1000 : null,
    }
    const data = JSON.stringify(cacheValueObject);
    const encData = this.hasEncrypt ? encryption(data) : data;
    this.storage.setItem(this.KEY, encData);
  }

  remove() {
    this.storage.removeItem(this.KEY);
    if(this.type===StorageType.LOCAL){
      lsMap.delete(this.KEY);
    }
    else if(this.type === StorageType.SESSION){
      ssMap.delete(this.KEY);
    }
  }
}

function getMapAndStorageByCacheConfig(type:StorageType):{storage:Storage,map:CacheMapType}{
  let storage:Storage;
  let map:CacheMapType;
  switch(type){
    case StorageType.LOCAL:
      storage = window.localStorage;
      map = lsMap;
      break;
    case StorageType.SESSION:
      storage = window.sessionStorage;
      map = ssMap;
      break;  
  }
  return {
    storage,
    map
  }
}

function clearStorageByType(exceptList: Array<Cache_Key>,type:StorageType){
  const {storage,map} = getMapAndStorageByCacheConfig(type)
  if(isArray(exceptList) && exceptList.length){
    map.forEach((WebStorage,key)=>{
      if(!exceptList.includes(key)){
        WebStorage.remove();
      }
    })
  }else{
    storage.clear();
    map.clear();
  }
}

export function createCacheStorage({ hasEncrypt = true, key, expire = null, type }: Cache): WebStorage {
  if (!key) throw new Error("please enter the Key");
  const {storage,map} = getMapAndStorageByCacheConfig(type)
  if (map.get(key)) return map.get(key);
  else {
    const newLs = new WebStorage({
      storage: storage,
      hasEncrypt,
      key,
      expire,
      type
    });
    map.set(key, newLs);
    return newLs;
  }
}

export function clearAllLocalStorage(exceptList: Array<Cache_Key>=[]) {
  clearStorageByType(exceptList,StorageType.LOCAL);
}

export function clearAllSessionStorage(exceptList: Array<Cache_Key>=[]) {
  clearStorageByType(exceptList,StorageType.SESSION);
}

export function clearStorage(exceptList: Array<Cache>=[]) {
  if(isArray(exceptList) && exceptList.length){
    const lsExceptList = exceptList.filter(cache=>cache.type===StorageType.LOCAL).map(cache=>cache.key);
    const ssExceptList = exceptList.filter(cache=>cache.type===StorageType.SESSION).map(cache=>cache.key);
    clearAllLocalStorage(lsExceptList);
    clearAllSessionStorage(ssExceptList);
  }
  else{
    clearAllLocalStorage();
    clearAllSessionStorage();
  }
}
