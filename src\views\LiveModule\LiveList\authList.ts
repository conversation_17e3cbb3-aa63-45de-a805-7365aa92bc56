import { LiveListAuth } from "@/enums/authKeys";
import { hasAuth } from "@/utils/auth/authUtils";


/** 推流地址 */
export const hasStreamingAddressAuth= function(){
    return hasAuth(LiveListAuth.Stream.key);
}()

/** 编辑 */
export const hasEditLiveAuth = function(){
 return hasAuth(LiveListAuth.Edit.key);
}()

/** 创建直播 */
export const hasAddLiveAuth = function(){
 return hasAuth(LiveListAuth.Add.key);
}()

/** 直播控制台 */
export const hasLiveControlAuth = function(){
 return hasAuth(LiveListAuth.Control.key);
}()

/** 分享 */
export const hasLiveShareAuth = function(){
 return hasAuth(LiveListAuth.Share.key);
}()

/** 商品管理 */
export const hasLiveCommodityAuth = function(){
 return hasAuth(LiveListAuth.Commodity.key);
}()

/** 福利券管理 */
export const hasLiveVoucherAuth = function(){
 return hasAuth(LiveListAuth.Voucher.key);
}()

/** 销售数据大屏 */
export const hasLiveSalesDataAuth = function(){
 return hasAuth(LiveListAuth.SalesData.key);
}()

/** 查询上播率 */
export const hasLiveBroadcastRateAuth = function(){
 return hasAuth(LiveListAuth.BroadcastRate.key);
}()