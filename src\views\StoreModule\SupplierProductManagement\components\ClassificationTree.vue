<template>
    <div class="tree-wrapper">
        <n-tree
          v-bind="$attrs"
          block-line
          :data="treeData"
          :cancelable="false"
          :default-expanded-keys="props.defaultExpandKeys"
          :node-props="nodeProps"
        />
    </div>
</template>

<script lang="ts" setup name='ClassificationTree'>
import { ref } from "vue";
import type { TreeOption, DropdownOption } from 'naive-ui';

/** props */
const props = withDefaults(defineProps<{
    value: string;
    treeData: Array<TreeOption & { isMenu: boolean }>;
    menuOptions: Array<DropdownOption>;
    defaultExpandKeys: Array<string | number>;
}>(), {
    treeData: () => [],
    menuOptions: () => [],
    defaultExpandKeys: () => [],
});

/** emits */
const emits = defineEmits<{
    (e: "update:value", value: string): void;
    (e: "menuSelect",  key: string | number, option: TreeOption ): void;
}>();

/** 右键菜单项 */
const optionsOriginRef = ref<DropdownOption[]>([...props.menuOptions]);
const optionsRef = ref<DropdownOption[]>([]);
const showDropdownRef = ref(false);
/** 坐标 */
const xRef = ref(0);
const yRef = ref(0);
/** 当前点击分类 */
const currentClassification = ref<TreeOption | null>(null);

/** 节点绑定事件 */
const nodeProps =  ({ option }: { option: TreeOption }) => {
    return {
      onContextmenu (e: MouseEvent): void {
        // 判断节点是否可右键菜单
        if (option.isMenu) {

            // 子分类过滤(新建子分类)
            if (option?.parentId) {
                optionsRef.value = optionsOriginRef.value.filter((item) => item.key !== 'addChild');
            } else {
                optionsRef.value = [...optionsOriginRef.value];
            }
            
            currentClassification.value = option;
            showDropdownRef.value = true;
            xRef.value = e.clientX;
            yRef.value = e.clientY;
            e.preventDefault();
        }
      }
    }
};

</script>


<style lang="less" scoped>
.tree-wrapper {
    padding: 0 12px;
}
</style>