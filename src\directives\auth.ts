
import type { App, Directive, DirectiveBinding } from "vue";
import {hasAuth} from '@/utils/auth/authUtils'
import { isNullOrUnDef } from "@/utils/isUtils";


const AuthDirective:Directive = {
    mounted(el:Element, binding:DirectiveBinding){
        const value= binding.value;
        if(isNullOrUnDef(value)) return;
        if(!hasAuth(value)){
            el.remove()
        }
    }
}

export function initAuthDirective(app:App){
    app.directive('auth',AuthDirective)
}

