<template>
  <transition appear name="fade" mode="out-in">
    <div class="label-warp" v-if="!show">
      <span>{{ state.name }}</span>
      <n-button class="edit-btn" text size="small" type="primary" @click="handleClick">
        <SvgIcon localIcon="edit" style="font-size: 18px"></SvgIcon>
      </n-button>
    </div>
    <div class="label-opt" v-else>
      <n-input
        v-model:value="modal.name"
        size="small"
        type="text"
        clearable
        :placeholder="`请输入大类名称`"
        style="width: 200px"
        @blur="modal.name = $event.target.value.trim()"
        class="mr-5"
      />
      <n-button
        size="tiny"
        type="success"
        class="mr-5"
        :disabled="loadingShow"
        :loading="loadingShow"
        @click="handleSave"
      >
        保存
      </n-button>
      <n-button size="tiny" type="info" ghost @click="cancelEdit">取消</n-button>
    </div>
  </transition>
</template>

<script setup lang="ts">
import { ref, computed, toRef } from "vue";
import { updateDealerGroup } from "@/services/api";
import { useMessages } from "@/hooks/useMessage";
import SvgIcon from "@/components/SvgIcon/index.vue";
type PropsType = "category";
const props = withDefaults(
  defineProps<{
    state: any;
  }>(),
  {
    state: () => {
      return {};
    },
  },
);

const message = useMessages();
const emits = defineEmits<{
  refresh: [value: object];
}>();

const initParams = {
  name: "",
};
const loadingShow = ref<boolean>(false);
const show = ref<boolean>(false);
const modal = ref({ ...initParams });
const handleSave = async () => {
  if (modal.value.name.trim() === "") {
    message.createMessageError(`大类名称不能为空！`);
    return;
  }
  try {
    const params = {
      data: {
        id: props.state.id,
        name: modal.value.name,
        type: 1,
      },
    };
    await updateDealerGroup(params);
    message.createMessageSuccess("保存成功！");
    const info = {
      id: props.state.id,
      name: modal.value.name,
    };
    emits("refresh", info);
    show.value = false;
  } catch (error) {
    message.createMessageError(`保存失败：${error}!`);
  } finally {
    loadingShow.value = false;
  }
};
const handleClick = () => {
  modal.value.name = props.state?.name;
  show.value = true;
};
const cancelEdit = () => {
  show.value = false;
};
</script>
<style scoped lang="less">
@import "@/styles/default.less";

.label-warp {
  display: flex;
  align-items: center;
  justify-content: center;

  .edit-btn {
    margin-left: 5px;
  }
}

.label-opt {
  display: flex;
  align-items: center;
}
</style>
