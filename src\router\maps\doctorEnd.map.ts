import {RoutesName} from "@/enums/routes";
import BlankLayout from "@/layout/BlankLayout.vue";

export const Default = {
    // [RoutesName.DoctorEndLogin]:{
    //     path:'/doctorEndLogin',
    //     component:() => import("@/views/DoctorEndModule/Login/index.vue"),
    //     meta:{
    //         title:'医师登录',
    //         isMenu: false,
    //         isShow: false,
    //     },
    // },
    [RoutesName.DoctorEndClinicalReceptionModule]: {
        path: "reception",
        component: BlankLayout,
        meta: {
            title: "接诊",
        },
    },
    [RoutesName.DoctorEndClinicalReception]: {
        path: "doctorClinicalReception",
        component: () => import("@/views/DoctorEndModule/Reception/index.vue"),
        meta: {
            title: "接诊",
        },
    },

    [RoutesName.DoctorEndPrescriptionModule]: {
        path: "prescription",
        component: BlankLayout,
        meta: {
            title: "处方",
        },
    },

    [RoutesName.DoctorEndPrescription]: {
        path: "prescription",
        component: () => import("@/views/DoctorEndModule/Prescription/index.vue"),
        meta: {
            title: "处方",
        },
    },

    [RoutesName.DoctorEndConsultationModule]: {
        path: "consultation",
        component: BlankLayout,
        meta: {
            title: "问诊单",
        },
    },
    [RoutesName.DoctorEndConsultation]: {
        path: "doctorConsultation",
        component: () => import("@/views/DoctorEndModule/MedicalInquiryForm/index.vue"),
        meta: {
            title: "问诊单",
        },
    },


    [RoutesName.DoctorEndSettingModule]: {
        path: "setting",
        component: BlankLayout,
        meta: {
            title: "设置",
        },
    },
    [RoutesName.DoctorEndSetting]: {
        path: "doctorSetting",
        component: () => import("@/views/DoctorEndModule/Setting/index.vue"),
        meta: {
            title: "设置",
        },
    },
}
