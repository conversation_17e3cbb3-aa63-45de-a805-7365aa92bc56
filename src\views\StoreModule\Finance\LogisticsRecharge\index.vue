<template>
  <MenuLayout v-model:activeKey="activeTypeRef" :menuOptions="menuList">
    <component :is="currentPage" />
  </MenuLayout>
</template>

<script setup lang="tsx">
import { ref, watch, computed } from "vue";
import {
  FinanceMenuType,
  type FinanceType,
  LogisticsRechargeTypeEnum,
  StoreConfigMenuType,
  type StoreConfigType,
} from "@/enums";
/** 相关组件 */
import MenuLayout from "@/components/MenuLayout/index.vue";
import TraceAccountBalance from "@/views/StoreModule/Finance/LogisticsRecharge/components/TraceAccountBalance/index.vue"
import TraceApiInvokeHistory from "@/views/StoreModule/Finance/LogisticsRecharge/components/TraceApiInvokeHistory/index.vue"
import { financeTraceAccountBalance, financeTraceInvokeHistory } from "../authList";

/** props */
const props = defineProps<{
  configurationAddressStatus?: number, // 配置地址跳转状态
}>();
/** 获取到的标签数据 */
const menuList = ref([
  {
    label: '轨迹查询余额',
    key: LogisticsRechargeTypeEnum.TraceAccountBalance,
    show:financeTraceAccountBalance
  },
  {
    label: '轨迹调用',
    key: LogisticsRechargeTypeEnum.TraceApiInvokeHistory,
    show:financeTraceInvokeHistory
  },
]);
const activeTypeRef = ref<FinanceType>(menuList.value.filter(item => item.show).map(item => item.key)[0]);
/** 相关组件 */
const pageMap = {
  [LogisticsRechargeTypeEnum.TraceAccountBalance]: TraceAccountBalance,
  [LogisticsRechargeTypeEnum.TraceApiInvokeHistory]: TraceApiInvokeHistory,
}

/** 当前页 */
const currentPage = computed(() => pageMap[activeTypeRef.value])

/** 监听 */
watch(()=>{
  return props.configurationAddressStatus
},(newVal)=>{
  if(newVal == 1){
    activeTypeRef.value = FinanceMenuType.RechargeRecord;
  }
},{immediate:true})
</script>

<style lang="less" scoped></style>
