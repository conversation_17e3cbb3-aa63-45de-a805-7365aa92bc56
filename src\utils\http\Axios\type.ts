import type { TableDataType } from "@/components/LTable/type";
import type { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios";
import type { Pinia } from "pinia";
import type { Router } from "vue-router";

export type ErrorMsgMode = "message" | "none" | undefined;

export type AxiosOptions = AxiosRequestConfig;

export interface RequestOptions {
  withToken?: boolean;
  isRetry?: boolean;
  isQueryParams?: boolean;
  isReturnRawResponse?: boolean;
  errorMsgMode?: ErrorMsgMode;
  responeseType?: "stream" | "json" | undefined;
  requestContentType?: "json" | "form-data" | "form-urlencoded";
}

export interface RequestProps<T = any> {
  url: string;
  params?: T;
  options?: AxiosOptions;
  requestConfig?: RequestOptions;
}

export interface ResponseResult<T = any> {
  code: string;
  data: T;
  message: string;
  timestamp?: string;
}

export type PageVOType = {
  current: number;
  size: number;
};
export interface PageDataProps<T extends object={}>{
  current:string, 
  records:Array<TableDataType<T>>, 
  size:string, 
  total:string
}
export interface PageDataRequestParams<T extends object = {}>{
  data: T,
  pageVO:PageVOType
}


export interface TAxiosConfig<D = any> extends AxiosRequestConfig<D> {
  requestOptions?: RequestOptions;
}

export type InterceptorResponse<T = any> = Pick<ResponseResult<T>,'data'>
export type RequestFunction<T=any,R=any> = (props: T) => Promise<R>;

export interface InterceptorsOptions {
  router: Router;
  store: Pinia;
}

export interface RequestInterceptor {
  onFulfilled?: (
    config: TAxiosConfig,
    options: InterceptorsOptions,
    axios: AxiosInstance,
  ) => TAxiosConfig;
  onRejected?: (error: AxiosError<ResponseResult>, options: InterceptorsOptions, axios: AxiosInstance) => Promise<any>;
}

type ResponseInterceptorFulfilledFn=(
  response: AxiosResponse<ResponseResult>&{config:TAxiosConfig},
  options: InterceptorsOptions,
  axios: AxiosInstance,
) => AxiosResponse<ResponseResult> | Promise<AxiosResponse<ResponseResult>> | InterceptorResponse;

type ResponseInterceptorRejectedFn=(
  error: AxiosError<ResponseResult> | AxiosResponse<ResponseResult>, 
  options: InterceptorsOptions, 
  axios: AxiosInstance
  ) => Promise<AxiosError<ResponseResult> | AxiosResponse<ResponseResult> | string>;

export interface ResponseInterceptor {
  onFulfilled?: ResponseInterceptorFulfilledFn;
  onRejected?: ResponseInterceptorRejectedFn;
}

export interface TAxiosInterceptors {
  request?: Array<RequestInterceptor | null>;
  response?: Array<ResponseInterceptor | null>;
}
