<template>
    <JModal
    v-model:show="isShow"
    width="680"
    :title="Add_active?'添加供应商':'编辑供应商'"
    @after-leave="closeModal"
		@positive-click="_submit"
		:positiveButtonProps="{
			loading: isLoading
		}"
  >
  <n-form
      ref="formRef"
      :rules="rules"
      :model="model"
      label-width="auto"
      label-placement="left"
      require-mark-placement="right-hanging"
      :style="{
          width: '100%',
        }"
    >
      <n-grid :cols="8" :x-gap="24">
        <n-form-item-gi :span="24" label="状态" path="status">
          <n-radio-group v-model:value="model.status">
                <n-space>
                  <n-radio :value="1">
                    正常
                  </n-radio>
                  <n-radio :value="0">
                    停用
                  </n-radio>
                </n-space>
            </n-radio-group>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="供应商昵称" path="supplierName" required>
          <n-input v-model:value="model.supplierName" placeholder="请输入供应商昵称" :maxlength="30" clearable/>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="联系人姓名" path="contactName">
          <n-input v-model:value="model.contactName" placeholder="请输入联系人姓名" :maxlength="30" clearable/>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="联系电话" path="contactPhone">
          <n-input v-model:value="model.contactPhone" placeholder="请输入联系电话" :maxlength="30" clearable/>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="公司全称" path="companyName">
          <n-input v-model:value="model.companyName" placeholder="请输入公司全称" :maxlength="30" clearable/>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="所在区域" path="addressOptions">
            <JAreaSelect v-model:value="model.addressOptions" />
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="详细地址" path="addressDetail">
          <n-input v-model:value="model.addressDetail" placeholder="请输入详细地址" :maxlength="30" clearable/>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="备注" path="remark">
          <n-input v-model:value="model.remark" type="textarea" rows="2" placeholder="请输入备注" :maxlength="150" clearable/>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="其它信息" path="extraInfo">
          <n-input v-model:value="model.extraInfo" type="textarea" rows="2" placeholder="请输入其它信息" :maxlength="150" clearable/>
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </JModal>
</template>

<script setup lang="ts">
import { ref,computed, watch } from 'vue';
import { useMessages } from '@/hooks';
import { addSupplier, updateSupplier } from '@/services/api';
const message = useMessages();

const props = withDefaults(defineProps<{
    Add_active: boolean;
    row?: any;
    show: boolean;
}>(), {
    Add_active: true,
    row: null,
    show: false,
});
const emits = defineEmits<{
    (e: 'update:show', value: boolean): void;
    (e: 'refresh'): void;
}>();

const isShow = computed({
    get: () => props.show,
    set: (value: boolean) => {
      emits('update:show', value);
    }
});

const initParams = {
  id:null,
  status:1,
  supplierName: '',
  contactName:'',
  contactPhone:'',
  companyName:'',
  addressOptions: {
    provinceId: null,
    province: null,
    cityId: null,
    cityName: null,
    areaId: null,
    area: null,
    remark:null,
  },
  addressDetail:'',
  remark:'',
  extraInfo:'',
};
const model = ref({ ...initParams });
const phoneReg = /^1[3-9]\d{9}$/;
/* 表单规则 */
const rules = {
  supplierName:{
    required: true,
    trigger: ["blur", "change"],
    message: "请录入供应商名称",
    validator: ()=>{
      return model.value.supplierName != '';
    }
  },
  contactPhone:{
    required: false,
    trigger: ["blur", "change"],
    message: "请录入正确格式的电话号码",
    validator: (rule, value) => {
      if (value !== undefined && value !== null && value !== "") {
        return phoneReg.test(value);
      }
      return true;
    }
  },
};

const isLoading = ref(false);

// 关闭按钮
const closeModal = () => {
    isShow.value = false;
    model.value = { ...initParams };
}

// 确认按钮
const formRef = ref(null); 
const _submit = () => {
    formRef.value?.validate(async (errors: any) => {
        if (!errors) {
            const {id,addressOptions,...other} = model.value
            const {areaId,cityName, area,...otherAddress} = model.value.addressOptions
            const params = {
              data:{
                ...(props.Add_active ? {} : {id: model.value.id}),
                ...other,
                ...otherAddress,
                city:cityName,
                districtId:areaId,
                district:area
              }
            }
            const api = props.Add_active ? addSupplier : updateSupplier;
            try {
              isLoading.value = true;
              await api(params)
              message.createMessageSuccess('操作成功');
              emits('refresh');
              closeModal();
            } catch (error) {
              message.createMessageError(`${error}`);
            }finally{
              isLoading.value = false;
            }
        }
    });
}

watch(()=>props.show,async (newVal, oldVal) => {
  if(newVal && !props.Add_active){
    const rowData = {
      id: props.row.id,
      status: props.row.status,
      supplierName: props.row.supplierName || '',
      contactName: props.row.contactName || '',
      contactPhone: props.row.contactPhone || '',
      companyName: props.row.companyName || '',
      addressDetail: props.row.addressDetail || '',
      remark: props.row.remark || '',
      extraInfo: props.row.extraInfo || '',
      addressOptions:{
        provinceId: props.row.provinceId || null,
        province: props.row.province || null,
        cityId: props.row.cityId || null,
        cityName: props.row.city || null,
        areaId: props.row.districtId || null,
        area: props.row.area || null,
      }
    }
    Object.assign(model.value, rowData);
  }
});

</script>