<template>
  <JModal
    v-model:show="isShow"
    width="780"
    title="参数获取指引"
    :positiveText="false"
    :negativeText="false"
    :positiveButtonProps="{
			loading: isLoading
		}"
  >
    <n-grid :cols="24" :x-gap="24" :y-gap="10" :style="{paddingBottom: '24px'}">
      <n-gi :span="24">
        步骤1：点击
        <a href="https://open.zto.com/#/login" target="_blank">https://open.zto.com/#/login</a>
        进入中通开放平台，注册登录。
      </n-gi>
      <n-gi :span="24">步骤2：进入【个人中心】完成企业资质认证</n-gi>
      <n-gi :span="24">
        步骤3：认证成功后，进入【控制台】应用管理内新建开发者应用，获取正式环境【AppKey】【AppSecret 】
      </n-gi>
      <n-gi :span="24">
        步骤4：发送邮件至 <EMAIL>，申请获取正式环境shopkey；邮件内容包括您的快递管家账号、店铺名称（可自定义）、接收单号信息回推的接口地址 {{ `${systemStore._imgPrefix}/ZTOEhk/notify` }}
      </n-gi>
      <n-gi :span="24">步骤5：收到快递管家配置好的 shopkey 后，即可在本系统启用中通快递管家功能。</n-gi>
    </n-grid>
  </JModal>
</template>
<script setup lang="ts">
import { ref,computed } from 'vue';
import { useSystemStoreWithoutSetup }  from '@/stores/modules/system';
const systemStore = useSystemStoreWithoutSetup();

const props = withDefaults(defineProps<{
    show: boolean;
}>(), {
    show: false,
});
const emits = defineEmits<{
    (e: 'update:show', value: boolean): void;
}>();
const isShow = computed({
  get(){
    return props.show;
  },
  set(value){
    emits('update:show',value);
  }
});

const isLoading = ref(false);
</script>
<style scoped lang="less">
@import "@/styles/default.less";
@import "@/styles/defaultVar.less";
a{
  color: @primary-color;
}
</style>
