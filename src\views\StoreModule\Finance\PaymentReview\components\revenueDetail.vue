<template>
  <n-modal
    v-model:show="showModal"
    :auto-focus="false"
    style="width: 900px"
    title="详情"
    :bordered="false"
    size="small"
    :closable="true"
    @after-leave="closeModal"
    preset="card"
  >
    <n-space :size="30" >
      <div>
        <JFieldItem label="申请单号">{{ detailData.applicationNo }}</JFieldItem>
        <JFieldItem label="申请来源">{{ detailData.applicationSource == 1 ? "用户提现" : "-" }}</JFieldItem>
        <JFieldItem label="申请时间">{{detailData.createTime}}</JFieldItem>
        <JFieldItem label="用户ID">{{detailData.userShortId}}</JFieldItem>
        <JFieldItem label="用户昵称">{{detailData.userNickname}}</JFieldItem>
        <JFieldItem label="打款金额">￥{{ detailData.amount ? (detailData.amount/100).toFixed(2) : '0.00'}}</JFieldItem>
        <JFieldItem label="收款人">{{detailData.receiverName}}</JFieldItem>
        <JFieldItem label="银行卡号">{{detailData.bankCardNo}}</JFieldItem>
        <JFieldItem label="联行号">{{detailData.unionBankNo}}</JFieldItem>
        <JFieldItem label="打款状态">{{orderStatus[Number(detailData.status) - 1]}}</JFieldItem>
        <JFieldItem label="账户类型">{{detailData.accountType == 201 ? '对私账户' : '对公账户'}}</JFieldItem>
        <JFieldItem label="手续费">￥{{detailData.fee ? (detailData.fee/100).toFixed(2) : '0.00'}}</JFieldItem>
        <JFieldItem label="发票图片">
          <n-image-group v-if="detailData.invoiceImageList.length > 0" >
            <n-space>
              <n-image width="80" v-for="item in detailData.invoiceImageList" :src="item" />
            </n-space>
          </n-image-group>
          <span v-else>无</span>
        </JFieldItem>
      </div>
      <n-space vertical :size="40">
        <div>
          <JFieldItem label="审核人">{{ detailData.auditorNickname || "-" }}</JFieldItem>
          <JFieldItem label="审核结果">{{ detailData.auditResult || "-" }}</JFieldItem>
          <JFieldItem label="审核时间">{{detailData.auditTime || "-"}}</JFieldItem>

          <JFieldItem label="审核人驳回原因" v-if="detailData.rejectReason" >
            <NInput
              v-model:value="detailData.rejectReason"
              type="textarea"
              placeholder="暂无驳回原因"
              readonly
              size="small"
              :autosize="{
                minRows: 3,
                maxRows: 5
              }"
            />
          </JFieldItem>
          <JFieldItem label="打款人">{{ detailData.payerNickname || "-" }}</JFieldItem>
          <JFieldItem label="审核结果">{{comStatus}}</JFieldItem>
          <JFieldItem label="审核时间">{{detailData.paymentTime || "-"}}</JFieldItem>
          <JFieldItem label="打款人驳回原因" v-if="detailData.rejectReason" >
            <NInput
              v-model:value="detailData.rejectReason" 
              type="textarea"
              placeholder="暂无驳回原因"
              readonly
              size="small"
              :autosize="{
                minRows: 3,
                maxRows: 5
              }"
            />
          </JFieldItem>
        </div>
        <div>
          <JFieldItem label="发起打款时间">{{detailData.paymentTime || "-"}}</JFieldItem>
          <JFieldItem label="商户号">{{detailData.merchantNumber || "-"}}</JFieldItem>
          <JFieldItem label="支付平台名称">{{detailData.payPlatformName || "-" }}</JFieldItem>
          <JFieldItem label="支付平台流水号">{{detailData.platformSerialNo || "-"}}</JFieldItem>
          <JFieldItem label="错误描述">{{detailData.errorDesc || "-"}}</JFieldItem>
        </div>
      </n-space>
    </n-space>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, toRefs, onMounted, computed } from "vue";
import JFieldItem from '@/components/JFieldItem/index.vue';
import { type PaymentAuditDTO } from "@/services/api";

const orderStatus = ['待审核','待打款','打款中','打款失败','已打款','已驳回']

const props = withDefaults(defineProps<{
  show: boolean;
  detailData: PaymentAuditDTO;
}>(), {
  show: false,
  detailData: () => ({}),
});

/** 打款人审核状态 */
const comStatus = computed(() => {
  if (props.detailData.status == 1 || props.detailData.status == 2) {
    return '待审核'
  } else if (props.detailData.status == 6) {
    return '已驳回'
  } else {
    return '审核通过'
  }
})

const emits = defineEmits<{
    (e: 'update:show', value: boolean): void;
}>();

const showModal = computed({
    get() {
        return props.show;
    },
    set(value) {
        emits("update:show", value);
    },
})

const closeModal = () => {
    showModal.value = false;
}
</script>
<style scoped lang="less"></style>
