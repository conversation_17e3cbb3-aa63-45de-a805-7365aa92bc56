import { ref, onMounted, onUnmounted } from 'vue';
import type { Ref } from 'vue';

export type ElementSizeHook = (target: Ref<HTMLElement | null>) => ElementSize

export interface ElementSize {
  width: number
  height: number
}

/**
 * 用于跟踪 DOM 元素的大小的 Hook
 * @param {Ref<HTMLElement | null>} target - 目标 DOM 元素的引用
 * @param {ElementSize} initialSize - 一个包含元素初始宽度和高度的可选对象。默认值为 { width: 0, height: 0 }
 * @returns {Object} 一个包含目标元素宽度和高度的对象
 */
export default function useElementSize(target: Ref<HTMLElement | null>, initialSize: ElementSize = { width: 0, height: 0 }) {
  // 用于存储目标元素的宽度和高度
  const width = ref(initialSize.width);
  const height = ref(initialSize.height);

  // ResizeObserver 实例，用于观察尺寸变化
  let observer: ResizeObserver | null = null;

  /**
   * 回调函数，根据 ResizeObserver 的条目更新宽度和高度。
   * @param {ResizeObserverEntry[]} entries - ResizeObserver 条目的数组。
   */
  const updateSize = (entries: ResizeObserverEntry[]) => {
    if (!entries.length) return;
    const entry = entries[0];
    // 更新宽度和高度为观察到的尺寸
    width.value = entry.contentRect.width;
    height.value = entry.contentRect.height;
  };

  // 生命周期钩子，在组件挂载时开始观察元素
  onMounted(() => {
    if (target.value) {
      // 创建新的 ResizeObserver 实例并观察目标元素
      observer = new ResizeObserver(updateSize);
      observer.observe(target.value);
    }
  });

  // 生命周期钩子，在组件卸载时清理观察
  onUnmounted(() => {
    if (observer && target.value) {
      // 停止观察目标元素并断开 ResizeObserver
      observer.unobserve(target.value);
      observer.disconnect();
    }
  });

  // 返回响应式的宽度和高度
  return { width, height };
}
