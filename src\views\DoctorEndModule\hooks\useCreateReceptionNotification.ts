import {useNotification, NButton, type NotificationReactive} from "naive-ui";
import {h, ref, type Ref} from "vue";
import toBeReception from "@/assets/audio/toBeReception.mp3";
import {useRouter} from "vue-router";
import {RoutesName} from "@/enums/routes";
import {isNullOrUnDef} from "@/utils";
import {getReceptionRemind} from "@/services/api/doctorEndApi";
import DoctorEndMitter from "@/views/DoctorEndModule/utils/DoctorEndMittEvent";

/** 处方待开方通知 */
let receptionNotificationRef: NotificationReactive | null = null;
/** 通知实例 */
let notificationInstance = null;
/** 定时器 */
let timer = null;

interface NotificationConfig {
    id: string | null;
    idRef: Ref<string | null>;
    notificationRef: NotificationReactive | null;
    showNotification: () => void;
}

export default function useCreateReceptionNotification() {
    const router = useRouter();
    /** 最新待接诊问诊单id */
    const receptionCode = ref(null);

    if (!notificationInstance) {
        notificationInstance = useNotification();
    }
    const audioRef = ref(null);

    /** 展示处方待开方通知 */
    function showPrescriptionNotification() {
        cancelButtonClick(receptionNotificationRef);
        /** 处方播报优先级低 所以判断当前播报是否结束 ，不可打断当前播报 */
        if (!audioRef.value || audioRef.value.ended) {
            audioRef.value = new Audio(toBeReception);
            audioRef.value.play();
        }
        receptionNotificationRef = notificationInstance.create({
            title: "待办事项提醒",
            content: () =>
                h("div", {class: "notification-content"}, [
                    h("div", "您有待接诊需求，请及时处理。"),
                    h("div", {class: "btnGroup"}, [
                        h(
                            NButton,
                            {
                                type: "default",
                                onClick: () => cancelButtonClick(receptionNotificationRef),
                            },
                            {
                                default: () => "取消",
                            },
                        ),
                        h(
                            NButton,
                            {
                                type: "primary",
                                onClick: handleButtonClick,
                            },
                            {
                                default: () => "去处理",
                            },
                        ),
                    ]),
                ]),
            onClose: () => {
                receptionNotificationRef = null;
                closeAudio();
            },
        });
    }

    async function handleButtonClick() {
        cancelButtonClick(receptionNotificationRef);
        if (router.currentRoute.value.name !== RoutesName.DoctorEndClinicalReception) {
            await router.push({name: RoutesName.DoctorEndClinicalReception});
        }else{
            DoctorEndMitter.emit("ChangeReceptionTab",'reception');
        }
    }

    /** 关闭处方通知 */
    function cancelButtonClick(notificationRef: NotificationReactive) {
        if (notificationRef) {
            notificationRef.destroy();
            notificationRef = null;
        }
    }

    function closeAudio() {
        if (audioRef.value) {
            audioRef.value.pause();
            audioRef.value.currentTime = 0;
            audioRef.value = null;
        }
    }

    /** 处理通知逻辑 */
    function handleNotification({id, idRef, notificationRef, showNotification}: NotificationConfig) {
        if (!isNullOrUnDef(id)) {
            const isNewNotification = isNullOrUnDef(idRef.value) || id > idRef.value;
            if (isNewNotification) {
                idRef.value = id;
                showNotification();
            }
        } else if (notificationRef) {
            notificationRef.destroy();
            notificationRef = null;
        }
    }

    /** 开始轮询 通过后端自增id判断 大于上次保存id 则给出弹窗，如果为空且弹窗存在则关闭弹窗 */
    function openReceptionNotification(isPharmacy: boolean = true) {
        getReceptionRemind()
            .then(({orderCode, consultId}) => {

                // 处理待接诊通知
                if (isPharmacy) {
                    handleNotification({
                        id: consultId,
                        idRef: receptionCode,
                        notificationRef: receptionNotificationRef,
                        showNotification: showPrescriptionNotification,
                    });
                }
            })
            .catch(err => {
                console.error("获取提醒失败:", err);
            })
            .finally(() => {
                if (timer) {
                    clearTimeout(timer);
                }
                timer = setTimeout(()=>openReceptionNotification(true), 10000);
            });
    }

    function destroyReceptionNotification() {
        notificationInstance.destroyAll();
        clearTimeout(timer);
    }

    return {
        openReceptionNotification,
        destroyReceptionNotification
    };
}
