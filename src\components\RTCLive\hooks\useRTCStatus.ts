
import {RTCLive} from "@/components/RTCLive/utils/RTCLiveInstance"



const RTCLiveInstance = new RTCLive()

export function useRTCLive(){
    /**断开RTC */
    async function disconnectRTC(){
        try{
            await RTCLiveInstance.setStreamingStatus(false)
        }
        catch(e){

        }
        await RTCLiveInstance.leaveRoom()
    }


    return {
        RTCLiveInstance,
        disconnectRTC
    }
}