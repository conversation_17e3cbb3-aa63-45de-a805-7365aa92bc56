<template>
  <div class="main-bg loginWrapper">
    <div class="title-wrapper">
      <img
          class="logo"
          src="data:image/png;base64,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"
          :alt="SystemSetting.doctorTitle"
      />
      <h1>{{ SystemSetting.doctorTitle }}</h1>
    </div>
    <div class="loginFormWrapper">
      <div class="loginForm">
        <div class="title">
          <div class="login-content">
            <p>登录到</p>
            <p>医疗健康服务后台</p>
            <div class="slide"></div>
          </div>
        </div>
        <div class="form">
          <n-space vertical>
            <n-alert v-if="errorMsg" :bordered="false" type="error">
              {{ errorMsg }}
            </n-alert>
            <div style="width: 100%">
              <n-form ref="formRef" :model="loginFormValue" :rules="rules" :show-label="false">
                <n-form-item path="username" label="账户">
                  <n-input
                      v-model:value="loginFormValue.username"
                      placeholder="请输入账户"
                      clearable
                      size="large"
                  >
                    <template #prefix>
                      <n-icon size="20"
                              :color="!formItemValidateErrorReacticve['username']?'#666666':'var(--n-caret-color-error)'">
                        <SvgIcon localIcon="username"></SvgIcon>
                      </n-icon>
                    </template>
                  </n-input>
                </n-form-item>
                <n-form-item path="password" label="密码">
                  <n-input
                      v-model:value="loginFormValue.password"
                      type="password"
                      show-password-on="click"
                      placeholder="请输入密码"
                      @keyup.enter.native="login"
                      size="large"
                      @focus="()=>handlerPwdIsFocus(true)"
                      @blur="()=>handlerPwdIsFocus(false)"
                      @mouseover="()=>handlerPwdIsFocus(true)"
                      @mouseleave="()=>handlerPwdIsFocus(false)"
                  >
                    <template #password-visible-icon>
                      <Transition>
                        <n-icon v-show='pwdDisplayStatusReacticve.isDisplayIcon' :size="20"
                                :color="SystemSetting.primaryColor">
                          <SvgIcon localIcon="eyeClose"></SvgIcon>
                        </n-icon>
                      </Transition>
                    </template>
                    <template #password-invisible-icon>
                      <Transition>
                        <n-icon v-show='pwdDisplayStatusReacticve.isDisplayIcon' :size="20" color="#c2c2c2">
                          <SvgIcon localIcon="eyeClose"></SvgIcon>
                        </n-icon>
                      </Transition>
                    </template>
                    <template #prefix>
                      <n-icon size="20"
                              :color="!formItemValidateErrorReacticve['password']?'#666666':'var(--n-caret-color-error)'">
                        <SvgIcon localIcon="password"></SvgIcon>
                      </n-icon>
                    </template>
                  </n-input>
                </n-form-item>
              </n-form>
            </div>
          </n-space>
        </div>
        <div class="loginButton">
          <n-button size="large" type="primary" block @click="login" :loading="isLoading">登录</n-button>
        </div>
      </div>
    </div>
    <div class="footer" v-if='SystemSetting.mainFooter.display'>
      <n-space justify="center">
        <p v-if='SystemSetting.mainFooter.companyName' class="footer-info">{{
            SystemSetting.mainFooter.companyName
          }}</p>
        <p v-if='SystemSetting.mainFooter.tel' class="footer-info">售后服务 : {{ SystemSetting.mainFooter.tel }}</p>
        <a v-if='SystemSetting.mainFooter.ICP' class="footer-info" href="https://beian.miit.gov.cn"
           target="_blank">{{ SystemSetting.mainFooter.ICP }}</a>
      </n-space>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {SystemSetting} from "@/settings/systemSetting";
import logoSrc from "@/assets/image/system/logoNew.png";
import {reactive, ref, watch, onMounted} from "vue";
import {afterLogin} from "@/utils/accountUtils";
import {useRouter} from "vue-router";
import SvgIcon from "@/components/SvgIcon/index.vue";
import {doctorLogin, userLogin} from "@/services/api";
import {md5Encryption} from "@/utils/crypto";
import {DoctorEndResList, PharmacistsResList} from '@/views/DoctorEndModule/Constant/index'
import {TencentIMLogin} from "@/views/DoctorEndModule/IM/utils/IMUtils";
import { isProdEnv } from "@/utils/envUtils";


const errorMsg = ref<string | null>(null);
const formRef = ref(null);
const isLoading = ref<boolean>(false);
const loginFormValue = ref({
  username: "",
  password: "",
});
const router = useRouter();
const formItemValidateErrorReacticve = reactive({
  username: false,
  password: false,
})
const pwdDisplayStatusReacticve = reactive({
  isDisplayIcon: false
})

function handlerPwdIsFocus(isFocus: boolean) {
  if (isFocus && loginFormValue.value.password.length > 0) {
    pwdDisplayStatusReacticve.isDisplayIcon = true
  } else {
    pwdDisplayStatusReacticve.isDisplayIcon = false
  }
}

watch(() => loginFormValue.value.password, (newVal) => {
  if (newVal.length > 0) {
    pwdDisplayStatusReacticve.isDisplayIcon = true
  } else {
    pwdDisplayStatusReacticve.isDisplayIcon = false
  }
})

const login = (e) => {
  // const isProduction = isProdEnv()
  const isProduction = true
  e.preventDefault();
  if (isLoading.value) {
    return;
  }
  formRef.value?.validate(async (errors) => {
    if (!errors) {
      isLoading.value = true;
      try {
        const data = await doctorLogin({
          username: loginFormValue.value.username,
          password: isProduction ? md5Encryption(loginFormValue.value.password) : loginFormValue.value.password
        })
        // const {token,userEntityDTO,treeResList} = data
        // if(!treeResList || !treeResList.length){
        //   errorMsg.value = '该账户角色异常，请联系管理员';
        //   isLoading.value = false;
        //   return
        // }
        const {token, account, id, name, marketplaceType, type, visitDataType, isInitialPassword, isSimplePassword, thirdIdList, systemUserId, doctorImg} = data
        // TODO 临时路由数据，暂时写死(医师端)
        let resList = DoctorEndResList
        if (visitDataType != '4') {
          // TODO 临时路由数据(药师端)
          resList = PharmacistsResList
        }
        await afterLogin({
          token, router, userinfo: {
            name,
            id,
            account,
            type,
            systemUserId,
            // 医生头像
            doctorImg
          },
          treeResList: resList,
          marketplaceType,
          visitDataType,
          isInitialPassword,
          isSimplePassword
        });
        if (visitDataType == '4') {
          await TencentIMLogin(thirdIdList.length > 0 ? thirdIdList[0] : null)
        }
      } catch (e) {
        errorMsg.value = e;
        console.log(e);
      }
      isLoading.value = false;
    }
  });
};
const rules = {
  username: {
    required: true,
    trigger: ["blur", "input"],
    message: "登录账户不能为空",
  },
  password: {
    required: true,
    trigger: ["blur", "input"],
    message: "请输入密码",
  },
};

</script>
<style scoped lang="less">
@import "@/styles/default.less";
@import "@/styles/defaultVar.less";

.loginWrapper {
  width: var(--content-client-width);
  height: var(--content-client-height);
  display: flex;
  background-color: #f9f9fa;
  position: relative;
  background: url("@/assets/image/system/login/doctorLoginBg.png") no-repeat;
  background-size: cover;
}

.title-wrapper {
  position: absolute;
  top: 32px;
  left: 48px;
  height: 50px;
  display: flex;
  align-items: center;

  h1 {
    color: #294848;
    font-size: 18px;
    font-weight: 600;
    font-size: 24px;
    margin-left: 4px;
  }

  img {
    height: 40px;
  }
}

.loginFormWrapper {
  position: absolute;
  overflow: hidden;
  width: 640px;
  box-sizing: border-box;
  z-index: 20;
  display: flex;
  justify-content: center;
  align-items: center;
  left: 0px;
  height: 100%;
  background: transparent;
  top: 0px;

  .loginForm {
    position: relative;
    padding: 5.6% 6%;
    min-width: 480px;
    display: flex;
    // width: 480px;
    width: 37.5%;
    box-sizing: border-box;
    right: 0;
    flex-wrap: wrap;
    flex-direction: column;
    border-radius: 21px;


    .loginButton {
      // margin-bottom: 28px;
    }

    & .title,
    & .form {
      width: 100%;
      z-index: 15;
    }

    & .form {
      padding: 20px 0;
    }

    & .title {
      // padding-bottom: 20px;
      .logo {
        width: 147px;
      }

      .welcome {
        font-size: 30px;
        margin-bottom: 16px;
        color: @primary-color;
      }

      .slogan {
        font-size: 16px;
        color: #666666;
        line-height: 22px;
      }
    }
  }

  .inputPrefix {
    width: 20px;
    height: 20px;
  }
}

.footer {
  position: absolute;
  bottom: 24px;
  left: 32px;

  .footer-info {
    font-size: @font-size-lg;
    color: #666666;
    line-height: 22px;
    text-decoration: none;
  }
}

:deep(.loginFormWrapper .form input:-webkit-autofill) {
  -webkit-box-shadow: 0 0 0 1000px white inset !important;
}

.v-enter-active,
.v-leave-active {
  transition: opacity 0.2s ease;
}

.v-enter-from,
.v-leave-to {
  opacity: 0;
}

.login-content {
  position: relative;
  display: inline-block;

  p {
    font-size: 28px;
    color: #333333;
    text-align: left;
    font-weight: bold;
    line-height: 48px;
  }

  .slide {
    position: absolute;
    right: -45px;
    bottom: 2px;
    width: 120px;
    height: 15px;
    background: linear-gradient(90deg, rgba(22, 119, 255, 0.53) 0%, rgba(185, 214, 255, 0) 100%);
    opacity: 0.6;
  }
}
</style>
