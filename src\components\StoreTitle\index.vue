<template>
  <div class="store-title">
    <div class="title-line"></div>
    <span class="title-text" :style="{ fontSize: titleSize }">
      <slot>{{ props.title }}</slot>
    </span>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
defineOptions({ name: 'StoreTitle' });

/** props */
const props = withDefaults(
  defineProps<{
    title?: string;
    size: 'small' | 'medium' | 'large';
  }>(),
  {
    title: '',
    size: 'small',
  },
);

const titleSize = computed(() => {
  switch (props.size) {
    case 'small':
      return '16px';
    case 'large':
      return '20px';
    case 'medium':
      return '18px';
    default:
      return '18px';
  }
})
</script>

<style lang="less" scoped>
  @import "@/styles/defaultVar.less";
  .store-title {
    height: 24px;
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: bold;
    line-height: 24px;
    box-sizing: border-box;

    .title-line {
      width: 4px;
      height: 18px;
      background-color: @primary-color;
      border-radius: 2px;
      margin-right: 8px;
    }
    .title-text {
        display: flex;
        align-items: center;
        gap: 4px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 600;
        color: #333333;
        line-height: 24px;
        text-align: left;
        font-style: normal;
        text-transform: none;
    }
  }
</style>
