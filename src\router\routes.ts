import { routesMap } from "@/router/maps/index";
import { baseRoutesConfig } from "@/router/config/base.config";
import { getRoutesMapByConfig } from "@/utils/routerUtils";
import { createCacheStorage } from "@/utils/cache/storageCache";
import { CacheConfig } from "@/utils/cache/config";
const routeConfigStorage = createCacheStorage(CacheConfig.RouteConfig);
const _routeConfigCache = routeConfigStorage.get();
const _config = _routeConfigCache ? _routeConfigCache : baseRoutesConfig;
routeConfigStorage.set(_config);
export const routes = getRoutesMapByConfig(_config, routesMap);
