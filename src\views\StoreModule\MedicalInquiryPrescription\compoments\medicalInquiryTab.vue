<template>
  <FormLayout
    :isLoading="isLoading"
    :tableData="tableData"
    :tableColumns="tableColumns"
    :pagination="paginationRef"
    @paginationChange="paginationChange"
    :isTableSelection="true"
  >
    <template #searchForm>
      <n-form
        ref="formRef"
        :model="formValue"
        :show-feedback="false"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
        size="small"
        :style="{ width: '100%' }"
      >
        <n-form-item label="">
          <n-input-group>
            <n-select
              v-model:value="formValue.searchType"
              placeholder="请选择"
              :options="searchTypeOptions"
              style="width: 120px"
            />
            <j-search-input
              v-model:value="formValue.searchValue"
              placeholder="请输入"
              @search="handlerSearch"
              :width="240"
            />
          </n-input-group>
        </n-form-item>
        <n-form-item label="创建时间">
          <j-date-range-picker
            v-model:value="formValue.creationTime"
            type="datetimerange"
            format="yyyy-MM-dd"
            :default-time="['00:00:00', '23:59:59']"
            clearable
          />
        </n-form-item>
      </n-form>
    </template>
    <!-- 操作栏 -->
    <template #tableHeaderBtn="scoped">
      <n-button @click="refresh" class="store-button">刷 新</n-button>
    </template>
  </FormLayout>
  <DetailsDrawer ref="detailDrawerRef" />
</template>
<script setup lang="tsx">
import { ref, watch } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { getMedicalInquiryPrescription } from "@/services/api";
import { useMessages } from "@/hooks";
import { MedicalInquiryPrescriptionSelection } from "@/enums";
import { deepClone } from "@/utils";
import DetailsDrawer from "./detailsDrawer.vue";
import { downloadLocalFile } from "@/utils/fileUtils";
import { transformMinioSrc } from "@/utils";
import moment from "moment";
/** 权限 */
import { hasManagementForm, hasManagementDetails } from "../authList";

interface prescriptionProps {
  tabNameRef: string; // tab标签值
}

/** props */
const props = withDefaults(defineProps<prescriptionProps>(), {
  tabNameRef: "pendingReview",
});

const { createMessageSuccess, createMessageError } = useMessages();

/* 初始化参数 */
const tabValue = ref(null);
const formValue = ref({
  searchValue: "", // 搜索关键字
  source: null, // 来源
  orderId: null, // 订单ID
  goodsValue: null, // 商品
  searchType: "patientsName",
  creationTime: null,
});
/** 搜索类型 */
const searchTypeOptions = [
  {
    label: "患者姓名",
    value: "patientsName",
  },
  {
    label: "用户昵称",
    value: "nickName",
  },
  {
    label: "医生姓名",
    value: "doctorName",
  },
  {
    label: "药师姓名",
    value: "pharmacistName",
  },
  {
    label: "处方编号",
    value: "code",
  },
  {
    label: "问诊单号",
    value: "consultationCode",
  },
];
watch([() => formValue.value.creationTime], () => {
  getTableData();
});
/* 表格方法Hook */
const { isLoading, tableData, paginationRef, paginationChange, summaryRef, sortTableData, pageTableData } =
  useTableDefault({
    pageDataRequest: getMedicalInquiryPrescription,
  });

//刷新
const refresh = () => {
  getTableData();
};

/** 搜索 */
const handlerSearch = () => {
  getTableData();
};

/** 获取参数 */
const getParams = () => {
  const { source, goodsValue, orderId, searchType, searchValue, creationTime } = formValue.value;
  const startTime = creationTime ? moment(creationTime[0]).format(`YYYY-MM-DD HH:mm:ss`) : null;
  const endTime = creationTime ? moment(creationTime[1]).format(`YYYY-MM-DD HH:mm:ss`) : null;
  return {
    status: tabValue.value,
    fromType: source,
    productId: goodsValue,
    orderId,
    startTime,
    endTime,
    [searchType]: searchValue,
  };
};

/** 请求表格数据 */
function getTableData() {
  pageTableData(getParams(), paginationRef.value, true);
}

/* 表格项 */
const tableColumns = ref([]);
const tableColumnsSource = [
  {
    title: "处方编号",
    key: "code",
    align: "left",
    fixed: "left",
    width: 100,
  },
  {
    title: "医生姓名",
    key: "doctorName",
    align: "left",
    width: 100,
  },
  {
    title: "科室",
    key: "departmentName",
    align: "left",
    width: 100,
  },
  {
    title: "机构",
    key: "institutionName",
    align: "left",
    width: 120,
  },
  {
    title: "患者姓名",
    key: "patientsName",
    align: "left",
    width: 100,
  },
  {
    title: "用户昵称",
    key: "nickName",
    align: "left",
    width: 120,
  },
  {
    title: "处方状态",
    key: "thirdCourseName",
    align: "left",
    width: 160,
    render: rowData => {
      const statusColors = ["#1677FF", "#4BE092", "#EEEEEE", "#FF3B2F", "#EEEEEE"];
      const statusTexts = ["待审核", "可使用", "已使用", "审核不通过", "已失效"];
      return (
        <div>
          <span
            style={{
              display: "inline-block",
              width: "8px",
              height: "8px",
              borderRadius: "50%",
              backgroundColor: statusColors[rowData.status],
              marginRight: "8px",
            }}
          />
          {statusTexts[rowData.status]}
        </div>
      );
    },
  },
  {
    title: "审核药师",
    key: "pharmacistName",
    align: "left",
    width: 160,
  },
  {
    title: "关联问诊单号",
    key: "consultationCode",
    align: "left",
    width: 160,
  },
  {
    title: "创建时间",
    key: "createTime",
    align: "left",
  },
  {
    title: "操作",
    key: "action",
    width: 80,
    align: "left",
    fixed: "right",
    render: row => {
      return (
        <n-space style="padding: 5px 0;">
          {hasManagementDetails ? (
            <n-button text type="primary" onClick={() => handlerDetailDrawerClick(row)}>
              详情
            </n-button>
          ) : null}
          {hasManagementForm &&
          (row.status == MedicalInquiryPrescriptionSelection.available ||
            row.status == MedicalInquiryPrescriptionSelection.haveBeenUsed) ? (
            <n-button
              text
              type="primary"
              disabled={isDownloading.value}
              onClick={() => handlerMedicalInquiryFormClick(row)}>
              处方笺
            </n-button>
          ) : null}
        </n-space>
      );
    },
  },
];

const detailDrawerRef = ref(null);
const handlerDetailDrawerClick = row => {
  const _params = {
    row,
  };
  detailDrawerRef.value?.acceptParams(_params);
};
const isDownloading = ref(false);
const handlerMedicalInquiryFormClick = async row => {
  let pdfMinioUrl = ""
  if (row.pdfMinioUrl && row.pdfMinioUrl !== "") {
    if (!row.pdfMinioUrl.startsWith("http")) {
      pdfMinioUrl = transformMinioSrc(row.pdfMinioUrl);
    } else {
      pdfMinioUrl = row.pdfMinioUrl;

    }
  }
  isDownloading.value = true;
  downloadLocalFile(pdfMinioUrl, "处方笺");
  isDownloading.value = false;
};

/** 监听 */
watch(
  () => [formValue.value.source, formValue.value.goodsValue],
  () => {
    getTableData();
  },
);

/** 监听 */
watch(
  () => props.tabNameRef,
  newVal => {
    tableColumns.value = deepClone(tableColumnsSource);
    tabValue.value = newVal;
    console.log(newVal);
    getTableData();

    // 处方管理 -- PrescriptionSelection.placeAnOrder 已下单
    // if (tabValue.value !== PrescriptionSelection.placeAnOrder) {
    //   tableColumns.value = tableColumns.value.filter(column => {
    //     return column.key !== "orderId";
    //   });
    // }
    // let targetColumn = tableColumns.value.find(column => column.key === "createTime" || column.key === "diagTime");
    // // 处方管理 -- 待开方 || 已取消
    // if (tabValue.value == PrescriptionSelection.unopened || tabValue.value == PrescriptionSelection.cancelled) {
    //   targetColumn.title = "创建时间";
    //   targetColumn.key = "createTime";
    // }
    // // 处方管理 -- 已开方 || 已下单
    // if (tabValue.value == PrescriptionSelection.alreadyOpen || tabValue.value == PrescriptionSelection.placeAnOrder) {
    //   targetColumn.title = "开处方时间";
    //   targetColumn.key = "diagTime";
    // }
  },
  {
    immediate: true,
  },
);
</script>

<style lang="less" scoped>
@import "@/styles/default.less";
:deep .n-tag__content {
  text-overflow: ellipsis;
  overflow: hidden;
}
.action-wrapper {
  display: flex;
  align-items: center;
}
</style>
