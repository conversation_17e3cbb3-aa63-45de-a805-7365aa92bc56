/**
 * @description 检查时间是否重叠
 * @param existingPeriods 
 * @param newPeriod 
 * @returns 
 */
export function hasOverlap(existingPeriods: Array<{ startTime: number, endTime: number, isDeleted: 0 | 1 }>, newPeriod) {
    let newExistingPeriods = existingPeriods.filter(item => item['isDeleted'] !== 1);
    let newStart = newPeriod[0];
    let newEnd = newPeriod[1];
    for (let i = 0; i < newExistingPeriods.length; i++) {
        let start = newExistingPeriods[i]['startTime'];
        let end = newExistingPeriods[i]['endTime'];
        // 检查是否有重叠
        if (!(newEnd < start || newStart > end)) {
            return false;
        }
    }
    return true;
}