<template>
    <JModal 
      v-model:show="showModalvalue" 
      @after-leave="closeModal"
      width="1400"
      height="580"
      title="社群关联"
      :positive-text="null" 
      :negative-text="null"
    >
      <FormLayout
        class="inner-page-height"
        :isLoading="loadShow"
        :tableData="tableData"
        :tableColumns="tableColumns"
        :isNeedCollapse="false"
		    :is-table-selection="false"
        :is-table-pagination="false"
        :is-display-header="false"
		    style="height: 100%;"
      ></FormLayout>
    </JModal>
</template>
<script setup lang="tsx">
import { ref ,watch} from "vue";
import {gmMemberRelateList} from '@/services/api';
import { useMessages } from "@/hooks";
import FormLayout from "@/layout/FormLayout.vue";

interface DrawerEditCustomer {
     csId?:string, //客户id
     showModal?:boolean
}

/** Props */
const props = withDefaults(defineProps<DrawerEditCustomer>(), {
    csId:null,
    showModal:null
});

const emits = defineEmits<{
  (e: "update:showModal", show: boolean): void;
}>();

const {createMessageError } = useMessages();

const tableData = ref([])
const showModalvalue = ref(false)
const loadShow = ref(false)
    /* 表格项 */
const tableColumns = [
    {
      title: "序号",
      width: 80,
      fixed: "left",
      key: "index",
      align: "center",
      render: (renderData: object, index: number) => {
        return `${index + 1}`;
      },
    },
    {
      title: "会员昵称",
      key: "thirdMemberNickname",
      align: "left",
      fixed: "left",
      width: 100,
      render: rowData => {
        return rowData.thirdMemberNickname ? rowData.thirdMemberNickname : '-'
      }
    },
    {
      title: "所属经销商",
      key: "gmDealerName",
      align: "left",
      fixed: "left",
      width: 100,
      render: rowData => {
        return rowData.gmDealerName ? rowData.gmDealerName : '-'
      }
    },
    {
      title: "所属群管",
      key: "gmMgrName",
      align: "left",
      width: 100,
      render: rowData => {
        return rowData.gmMgrName ? rowData.gmMgrName : '-'
      }
    },
    {
      title: "训练营",
      key: "campEntityName",
      align: "left",
      fixed: "left",
      width: 100,
      render: rowData => {
        return rowData.campEntityName ? rowData.campEntityName : '-'
      }
    },
    {
      title: "公众号",
      key: "wxappEntityName",
      align: "left",
      width: 100,
      render: rowData => {
        return rowData.wxappEntityName ? rowData.wxappEntityName : '-'
      }
    },
];

/** 获取社群关联相关弹框列表数据 */
const clickSocialCommunity = async()=>{
    loadShow.value = true
    try{
      const customerEntity = await gmMemberRelateList({ csId:props.csId });
      tableData.value = customerEntity
    }catch(err){
      createMessageError(err || '获取社群关联失败') 
    }finally{
      loadShow.value = false
    }
  }

const closeModal = () =>{
  emits("update:showModal", false);
  tableData.value = []
}

watch(() =>props.showModal,
 (newVla)=>{
  showModalvalue.value = newVla
  if(showModalvalue.value) clickSocialCommunity()
 }
)
</script>