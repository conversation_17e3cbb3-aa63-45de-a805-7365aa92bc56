@import "@/styles/defaultVar.less";
@import "@/styles/fonts.less";
@import "@/styles/echarts.less";
body {
  background-color: @blank-background-color;
}
.main-bg {
  height: 100vh;
  width: 100vw;
}

#nprogress {
  pointer-events: none;
  .bar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99999;
    width: 100%;
    height: 2px;
    background-color: @primary-color;
    opacity: 0.75;
  }
}

.inner-page-height {
  height: @inner-bg-height;
}
.n-data-table-tr.n-data-table-tr--summary {
  position: sticky;
  bottom: 0px;
  z-index: 10;
}

.n-data-table-base-table-body.n-scrollbar {
  .n-scrollbar-rail.n-scrollbar-rail--vertical,
  .n-scrollbar-rail.n-scrollbar-rail--horizontal {
    z-index: 11 !important;
  }
}
.n-data-table-th__title-wrapper {
  font-weight: 500;
  line-height: 22px;
}

.number-font {
  font-family: "BebasNeue";
}
.n-date-panel .n-date-panel-footer {
  border-top: none;
  padding: 0;
}

.n-date-panel-date.n-date-panel-date--current .n-date-panel-date__sup {
  border: 1px solid var(--n-item-color-active);
  width: 100%;
  height: 100%;
  background: transparent;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.header .n-grid {
  grid-template-columns: repeat(28, minmax(0px, 1fr));
}
.n-data-table .n-data-table-wrapper {
  // margin-bottom: 18px;
}
.n-data-table-td--fixed-left .n-ellipsis .n-space div div {
  height: 36px;
  line-height: 18px;
}
.rangeTime {
  margin-left: -20px;
}
.n-tabs-nav-scroll-content {
  height: 36px;
}

.n-menu .n-menu-item-content .n-menu-item-content__icon {
  margin-right: 5px !important;
}
// .n-tabs.n-tabs--top .n-tab-pane{
//   padding: 0;
// }
.n-menu .n-submenu .n-submenu-children .j-menu-item {
  padding-left: 10px;
}
.n-submenu-children .n-menu-item-content.n-menu-item-content--child-active {
  &::before {
    border-left-color: #1677FF;
    border-radius: var(--n-border-radius);
    background-color: var(--n-item-color-active);
  }
}
.n-modal-mask {
  background-color: rgba(0, 0, 0, 0.15);
}

.n-data-table .n-data-table-tr {
  &:hover .n-data-table-td .edit-wrapper .edit-icon {
    display: block;
  }
}

/*
  外边距、内边距全局样式
*/
.loopStyle(@counter) when (@counter > 0) {
  .mt-@{counter} {
    margin-top: @counter * 1px;
  }
  .mr-@{counter} {
    margin-right: @counter * 1px;
  }
  .mb-@{counter} {
    margin-bottom: @counter * 1px;
  }
  .ml-@{counter} {
    margin-left: @counter * 1px;
  }
  .pt-@{counter} {
    padding-top: @counter * 1px;
  }
  .pr-@{counter} {
    padding-right: @counter * 1px;
  }
  .pb-@{counter} {
    padding-bottom: @counter * 1px;
  }
  .pl-@{counter} {
    padding-left: @counter * 1px;
  }
  .px-@{counter} {
    padding-right: @counter * 1px;
    padding-left: @counter * 1px;
  }
  .p-@{counter} {
    padding: @counter * 1px;
  }
  .w-@{counter} {
    width: @counter * 1%;
  }
  .h-@{counter} {
    height: @counter * 1%;
  }
  .max-w-@{counter} {
    max-width: @counter * 1px;
  }
  .max-h-@{counter} {
    max-height: @counter * 1px;
  }
  .min-w-@{counter} {
    min-width: @counter * 1px;
  }
  .min-h-@{counter} {
    min-height: @counter * 1px;
  }

  .loopStyle((@counter - 1)); // 递归调用自身
}

.loopStyle(100);

.store-button {
  width: 72px;
}

.text-ellipsis{
  white-space: nowrap;
  display: inline-block;
  vertical-align: bottom;
  max-width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
}
// 全局通知样式
.notification-content {
  border-top: 1px solid #E5E6EB;
  padding: 10px;
  font-size: 14px;
  .btnGroup{
    margin-top: 36px;
    display: flex;
    justify-content: end;
    gap: 10px;
  }
}
.n-notification{
  padding: 0px !important;
  .n-notification-main__header{
    padding: 0px 10px;
  }
  .n-notification-main{
    padding: 16px 0px 0px 0px !important;
    // border: 1px solid black;
    width: 100% !important;
    margin-left: 0 !important;
  }
}

.n-tree .n-tree-node-content .n-tree-node-content__text {
  font-size: var(--n-font-size) !important;
}

