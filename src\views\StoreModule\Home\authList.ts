import { PrescriptionManagementAuth , GoodsManagementAuth , OrderManagementAuth , AfterServiceManagementAuth } from "@/enums/authKeys";
import { hasAuth } from "@/utils/auth/authUtils";

/** 处方管理 */
export const hasPrescriptionAuth= function(){
    return hasAuth(PrescriptionManagementAuth.prescriptionManagementIndex.key);
}()

/** 商品管理 */
export const hasGoodsManagementAuth = function(){
    return hasAuth(GoodsManagementAuth.goodsManagementIndex.key);
}()

/** 订单管理 */
export const hasOrderManagementAuth = function(){
    return hasAuth(OrderManagementAuth.orderManagementIndex.key);
}()

/** 售后管理 */
export const hasAfterServiceManagementAuth = function(){
    return hasAuth(AfterServiceManagementAuth.afterServiceManagementIndex.key);
}()
