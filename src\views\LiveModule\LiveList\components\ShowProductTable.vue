<template>
    <n-modal v-model:show="modalShow" :auto-focus="false" @after-leave="handleClose">
        <n-card
        class="product-card"
        style="width: 1100px"
        :bordered="false"
        size="small"
        title="商品管理"
        closable
        @close="handleClose"
        >
        <div class="addBtnStyle">
            <n-button size="small" type="primary" @click="addProduct">添加商品</n-button>
            <n-space>
                <span>启用直播间小黄车</span>
                <n-switch v-model:value="model.isOneself" :loading="switchLoading" @update:value="handleChange" />
            </n-space>
        </div>
        <FormLayout
            style="height:610px;"
            :isMultiple="true"
            :isLoading="isLoading"
            :is-need-collapse="false"
            :tableData="tableData"
            :tableColumns="tableColumns"
            :default-checked-keys="selectedKeysReactive.selectedKeys"
            :pagination="paginationRef"
            @paginationChange="paginationChange"
            @selectedKeysChange="onTableSelectedKeysChange">
        <template #searchForm>
            <n-form
            ref="formRef"
            :model="searchFormReactive"
            :show-feedback="false"
            label-placement="left"
            label-width="auto"
            require-mark-placement="right-hanging"
            size="small"
            :style="{ width: '100%' }"
            >
        
            <n-form-item :show-label="false" style="margin-left: 18px">
                <n-checkbox size="medium" v-model:checked="state.isCheckAll">
                全选({{ selectedKeysReactive.selectedKeys.length }})
                </n-checkbox>
            </n-form-item>
            </n-form>
        </template>
        <template #tableHeaderBtn>
        
            <n-popconfirm      
                 :positive-button-props="{
                    loading : deleteLoding
                 }"           
                 @positive-click="DeleteProductList">
            <template #trigger>
                <n-button text :disabled="batchBtnDisabled">
                <img style="width: 16px;height: 16px;margin-right: 6px" :src="trashCan"/>
                批量删除
                </n-button>
            </template>
            此操作将删除选中数据，是否继续？
            </n-popconfirm>
            <n-popconfirm 
                 :positive-button-props="{
                    loading : upOrdownLoding
                 }"
                 @positive-click="RecommendList(true)">
                <template #trigger>
                <n-button text :disabled="batchBtnDisabled">
                    <img style="width: 16px;height: 16px;margin-right: 6px" :src="putAway"/>
                    批量上架
                </n-button>
                </template>
                此操作将上架选中商品，是否继续？
            </n-popconfirm>

            <n-popconfirm
                :positive-button-props="{
                    loading : upOrdownLoding
                 }"
                 @positive-click="RecommendList(false)">
                <template #trigger>
                <n-button text :disabled="batchBtnDisabled">
                    <img style="width: 16px;height: 16px;margin-right: 6px" :src="soldOut"/>
                    批量下架
                </n-button>
                </template>
                此操作将下架选中商品，是否继续？
            </n-popconfirm>
        </template>
        </FormLayout>
        </n-card>
       
    </n-modal>
    <SelectProductModal
    v-model:show="productSelectShow"
    v-model:value="state.selectProductKeys"
    v-model:selectedOptions="tableData" 
    @update:data="pageProductData"
    :liveActivityId="props.liveActivityId"
    />
</template>
  
<script lang="tsx" setup>
import {reactive, watch, computed,ref,nextTick} from 'vue';
import FormLayout from '@/layout/FormLayout.vue';
import EmptyImg from '@/assets/image/system/emptyImg.jpg';
import {dataTableDark, NButton, NFlex, NImage, NPopconfirm} from 'naive-ui';
import {getLiveActivityProduct,updateCartEnable,deleteGoodsById,goodsBatchDelete,batchGoodsPublish,updateSort,updateFloatingStatus} from '@/services/api/liveStoreProductApi/liveGoods';
import trashCan from '@/assets/image/course/<EMAIL>';
import putAway from '@/assets/image/course/<EMAIL>';
import soldOut from '@/assets/image/course/<EMAIL>';
import {ArrowDown, ArrowUp} from '@vicons/ionicons5';
import SelectProductModal from "./SelectProductModal.vue";
import {useTableDefault} from '@/hooks/useTableDefault';
import { useMessages } from "@/hooks";
const { createMessageSuccess, createMessageError } = useMessages();
// 商品是否讲解
enum FloatingStatus {
  explain = 1,
  noExplain = 2
}
const switchLoading = ref(false)
const {
    isLoading,
    tableData,
    pageTableData,
    paginationRef,
    paginationChange,
    refreshTableData,
  } = useTableDefault({
    pageDataRequest: (params) => {
        return getLiveActivityProduct(params);
    },
  });
interface ProductTableProps {
    show:boolean;
    liveActivityId:string;
    shoppingCartEnable:number
    // value?: Array<string>;
    // selectedOptions?: Array<any>;
    // tableData: Array<any>;
    // showPutAway: boolean; // 是否显示上架下架按钮
    // showRecommend: boolean; // 是否显示推荐按钮
}
const productSelectShow = ref(false)
// 商品上架或下架状态
const enum ProductStorageStatus {
    UP = 1,
    DOWN = 0,
}
const props = withDefaults(defineProps<ProductTableProps>(), {
    show:false,
    liveActivityId:'',//直播课程列表id
    shoppingCartEnable:0
    // value: () => [], // 选中的id列表
    // selectedOptions: () => [], // 选中的数据列表item
    // tableData: () => [],
    // showPutAway: true, // 是否显示上架下架按钮
    // showRecommend: true, // 是否显示推荐按钮
});
const initSearchFormReactive = () => {
    return {
        liveActivityId: props.liveActivityId,
    };
  };
  
let searchFormReactive = reactive(initSearchFormReactive());
const emits = defineEmits<{
    (e: 'update:value', tableData: string[]): void
    (e: 'update:tableData', tableData: string[]): void
    (e: 'update:show', value: boolean): void
    (e: "refresh-table"): void
}>();

const state = reactive({
    isCheckAll: false, // 选中全部
    isLoading: false,
    productList:[],
    selectProductKeys:[] // 选中的商品key列表
});
const modalShow = computed({
    get() {
    return props.show;
    },
    set(value) {
    emits("update:show", value);
    },
});
/** 参数 */
const model = ref({
    isOneself: false,
});
function handleClose() {
    modalShow.value = false;
}
watch(() => state.isCheckAll, (newValue) => {
  if (newValue && selectedKeysReactive.selectedKeys.length !== tableData.value.length) {
    isLoading.value = true;
    selectedKeysReactive.selectedKeys = tableData.value.map(item => item.id || '');
    selectedKeysReactive.selectedOptions = tableData.value
    setTimeout(() => {
      isLoading.value = false;
    });
  }
  if (!newValue && selectedKeysReactive.selectedKeys.length === tableData.value.length) {
    isLoading.value = true;
    selectedKeysReactive.selectedKeys = [];
    setTimeout(() => {
      isLoading.value = false;
    });
  }
});
const selectedKeysReactive = reactive({
    selectedKeys: [],
    selectedOptions: [],
});

let tableColumns = [
{
    title: '商品名称',
    key: 'firstImg',
    align: 'left',
    fixed: 'left',
    width: 180,
    render: (rowData: any) => {
    // 如果是药品，标题展示商品名+通用名+规格
    let showName = rowData.productName;
    if (rowData.type === 1) {
        showName = `[${rowData.productName}] ${rowData.name} ${rowData.appletProductSpecDTOList?.[0].name || ''}`;
    }
    return (
        <>
        <NFlex wrap={false} align={'center'}>
            <NImage height="72" width="72" src={rowData.firstImg ? (rowData.firstImg) : EmptyImg}/>
            <div>
            <span>{showName || '-'}</span>
            </div>
        </NFlex>
        </>
    );
    },
},
{
    title: '商品价格(元)',
    key: 'appletProductSpecDTOList.price',
    align: 'left',
    width: 80,
    render: (rowData) => {
    // 积分商品处理
    // if (rowData?.appletPointSpecDTOS || rowData?.appletPointSpecList) {
    //     showPointPrice(rowData);
    //     return (
    //     <>
    //         <n-flex vertical>
    //         <span>现价:{rowData.minExchangePointAndPriceStr}</span>
    //         </n-flex>
    //     </>
    //     );
    // } else {
        // 非积分商品处理
        showPrice(rowData);
        return (
        <>
            <n-flex vertical>
            <span>现价:{rowData.nowPrice}</span>
            {rowData.activityPrice &&
                <span style={{'text-decoration': 'line-through'}}>原价:{rowData.oldPrice}</span>}
            </n-flex>
        </>
        );
    }
    // },
},
{
    title: '商品库存',
    key: 'availStocks',
    align: 'left',
    fixed: 'left',
    width: 60,
    // render: (rowData: any) => {
    // let allStocksNum = showPointProductStocks(rowData);
    // return (
    //     <span>{allStocksNum ?? '-'}</span>
    // );
    // },
},
{
    title: '排序',
    key: 'order',
    align: 'left',
    width: 60,
    render: (rowData, index) => {
    const upArrowDisable = index === 0 || disabledSort.value;
    const downArrowDisable = index === tableData.value.length - 1 || disabledSort.value;
    return <>
        <n-button disabled={upArrowDisable} text size={'small'}>
        {{
            default: () => {
            return '';
            },
            icon: () => {
            return (
                <n-flex onClick={() => {moveUpOrDown(rowData,index,'up');}}>
                <n-icon size="16">
                    <ArrowUp/>
                </n-icon>
                </n-flex>
            );
            },
        }}
        </n-button>
        <n-button text disabled={downArrowDisable} size={'small'}>
        {{
            default: () => {
            return '';
            },
            icon: () => {
            return (
                <n-icon size="16" onClick={() => {moveUpOrDown(rowData,index,'down');}}>
                <ArrowDown/>
                </n-icon>
            );
            },
        }}
        </n-button>
    </>;
    },
},
{
    title: '操作',
    key: 'operation',
    align: 'left',
    width: 100,
    render: (rowData, index) => {
    return (
        <>
        <n-flex>
            {
            <n-popconfirm
                onPositiveClick={() => {
                    publishGoodsStatus(rowData.publishStatus,rowData.id,index)
                    {/* rowData.publishStatus = rowData.publishStatus == ProductStorageStatus.UP
                        ? ProductStorageStatus.DOWN
                        : ProductStorageStatus.UP; */}
                    // 如果是下架，则取消推荐商品
                    }}
            >
                {{
                trigger: () => (
                    <n-button text disabled={disabledUp.value} style={{
                    color: `${rowData.publishStatus == 1 ? '#00B42A' : '#ff0d43'}`,
                    cursor: 'pointer',
                    }}>{rowData.publishStatus == 1 ? '下架' : '上架'}</n-button>
                ),
                default: () => <span>{'确认' + (rowData.publishStatus == 1 ? '下架' : '上架') + '?'}</span>,
                }}
            </n-popconfirm>
            }
          {rowData.publishStatus == 1 && (
            <n-popconfirm
              onPositiveClick={() => {
                publishFloatingStatus(rowData);
              }}
            >
              {{
                trigger: () => (
                  <n-button text disabled={disabledFloating.value} style={{
                    color: `${rowData?.floatingStatus == FloatingStatus.explain ? "#00B42A" : "#ff0d43"}`,
                    cursor: "pointer",
                  }}>{rowData?.floatingStatus == FloatingStatus.explain ? "取消讲解" : "讲解"}</n-button>
                ),
                default: () => <span>{"确认" + (rowData?.floatingStatus == FloatingStatus.explain ? "取消讲解" : "讲解") + "?"}</span>,
              }}
            </n-popconfirm>
          )}
            <n-popconfirm
            onPositiveClick={() => {
                DeleteProduct(index, rowData.id);
            }}>
            {{
                trigger: () => (
                    <n-button disabled={deleteLoding.value} text style={{color: 'red', cursor: 'pointer'}}>删除</n-button>
                ),
                default: () => <span>确定删除该商品吗?</span>,
            }}
            </n-popconfirm>
        </n-flex>
        </>
    );
    },
},
];

function addProduct (){
    productSelectShow.value = true
}
function onTableSelectedKeysChange(selectedKeys: Array<string>, options: Array<any>) {
    selectedKeysReactive.selectedKeys = options?.map(item => item?.id) || [];
    selectedKeysReactive.selectedOptions = options;
    if (selectedKeys.length === tableData.value.length) {
        state.isCheckAll = true;
    } else {
        state.isCheckAll = false;
    }
}

/**
 * 列中显示的价格设置
 */
function showPrice(rowData) {
    let mixPrice = rowData.price; // 所有规格最低价
    let activityPrice = rowData.activityPrice; // 所有规格最低活动价。
    // if (rowData?.appletProductSpecDTOList && rowData.appletProductSpecDTOList.length > 0) {
    //     let arr = rowData.appletProductSpecDTOList;
    //     arr.forEach((item, index) => {
    //     if (index === 0) {
    //         mixPrice = item.price;
    //         activityPrice = item?.activityPrice || undefined;
    //         return;
    //     }
    //     if (mixPrice > item.price) {mixPrice = item.price;}
    //     if (item?.activityPrice) {
    //         if (!activityPrice) {activityPrice = item.activityPrice;}
    //         if (activityPrice > item.activityPrice) {activityPrice = item.activityPrice;}
    //     }
    //     });
    // }
    // 有活动价，则显示活动价。mixPrice是所有规格最低价，activityPrice是活动价。
    let nowPrice = activityPrice ? (activityPrice / 100).toFixed(2) :
        (mixPrice ? (mixPrice / 100).toFixed(2) : '');
    let oldPrice = activityPrice ? (mixPrice / 100).toFixed(2) : '';

    rowData.nowPrice = nowPrice;
    rowData.oldPrice = oldPrice;
    rowData.activityPrice = activityPrice;
}
const disabledSort = ref(false)
async function updateGoodSort(data,rowIndex,tableIndex,flag) {
    disabledSort.value = true
    let params=[
            {
                id:data.id,
                liveActivityId:props.liveActivityId,
                sort:tableIndex.sort
            },{
                id:tableIndex.id,
                liveActivityId:props.liveActivityId,
                sort:data.sort
            }
        ]
    try {
        await updateSort(params)
        if (flag == 'up' ) { 
        // 交换目标元素与前一个元素的位置
        [tableData.value[rowIndex], tableData.value[rowIndex - 1]] = [
        tableData.value[rowIndex - 1],
        tableData.value[rowIndex]];
    }else{
        [tableData.value[rowIndex], tableData.value[rowIndex + 1]] = [
        tableData.value[rowIndex + 1],
        tableData.value[rowIndex]];
    }
        createMessageSuccess(`修改排序成功`);
    } catch (error) {
        createMessageSuccess(`修改排序失败:${error}`);
    }finally{
        disabledSort.value = false
    }
    
}
const moveUpOrDown = async(data,rowIndex: number,flag) => {
    let tableIndex = {}
    if(flag == 'up' ){
        tableIndex = tableData.value[rowIndex-1]
    }else{
        tableIndex = tableData.value[rowIndex+1]
    }
    await updateGoodSort(data,rowIndex,tableIndex,flag)
     
}
const moveUp = async(data,rowIndex: number) => {
   
    if (rowIndex != 0) { 
        // 交换目标元素与前一个元素的位置
        [tableData.value[rowIndex], tableData.value[rowIndex - 1]] = [
        tableData.value[rowIndex - 1],
        tableData.value[rowIndex]];
    }
};

const moveDown = (data,rowIndex: number) => {
    if (rowIndex != tableData.value.length - 1) {
        // 交换目标元素与后一个元素的位置
        [tableData.value[rowIndex], tableData.value[rowIndex + 1]] = [
        tableData.value[rowIndex + 1],
        tableData.value[rowIndex]];
    }
};
const disabledUp = ref(false)
const publishGoodsStatus = async(sataus,id,i)=>{
    disabledUp.value = true
    let showWord = sataus == ProductStorageStatus.DOWN ? '上架' : '下架'
    let params ={
        ids:[id],
        isPublish:sataus==0?1:0
    }
    try {
        await batchGoodsPublish(params)
        // tableData.value[i].publishStatus = 1;
        if (sataus == 0) {
            tableData.value[i].publishStatus = 1;
        } else {
            tableData.value[i].publishStatus = 0;
        }
        tableData.value[i].floatingStatus=FloatingStatus.noExplain
        createMessageSuccess(`商品${showWord}成功`);

    } catch (error) {
        createMessageError(`商品${showWord}失败:${error}`);
        return
    } finally{
        disabledUp.value = false
    }
}

const disabledFloating = ref(false);
const publishFloatingStatus = async (row) => {
  const floatingStatus = row?.floatingStatus == FloatingStatus.explain ? FloatingStatus.noExplain : FloatingStatus.explain;
  const message = floatingStatus == FloatingStatus.explain ? "取消讲解" : "讲解";
  try {
    disabledFloating.value = true;
    if (!model.value?.isOneself) {
      throw new Error("请先开启直播间小黄车");
    }
    if (!props?.liveActivityId || !row?.id) {
      throw new Error("缺少必要参数");
    }
    const params = {
      liveActivityId: props.liveActivityId,
      id: row?.id,
      floatingStatus: floatingStatus,
    };
    await updateFloatingStatus(params);
    //刷新表格数据
    refreshTableData();
  } catch (e) {
    createMessageError(`${e}`);
  } finally {
    disabledFloating.value = false;
  }
};
const upOrdownLoding = ref(false)
const RecommendList = async(sataus: boolean) => {
    upOrdownLoding.value = true
    let showWord = sataus  ? '上架' : '下架';
    let params ={
        ids:selectedKeysReactive.selectedKeys,
        isPublish:sataus?1:0
    }
    try {
        await batchGoodsPublish(params)
        if (sataus) {
        tableData.value.forEach((e) => {
        if (selectedKeysReactive.selectedKeys.includes(e.id)) {
            e.publishStatus = 1;
            e.floatingStatus = FloatingStatus.noExplain;
        }
        });
    } else {
        tableData.value.forEach((e) => {
        if (selectedKeysReactive.selectedKeys.includes(e.id)) {
            e.publishStatus = 0;
            e.floatingStatus = FloatingStatus.noExplain;
        }
        });
    }
    if (state.isCheckAll) {
        state.isCheckAll = false;
    }else{
        nextTick(()=>{
        state.isLoading = true;
        selectedKeysReactive.selectedKeys = []
        selectedKeysReactive.selectedOptions = []
        setTimeout(() => {
            state.isLoading = false;
        });
        })
    }
    createMessageSuccess(`商品${showWord}成功`);
    } catch (error) {
        createMessageError(`商品${showWord}失败:${error}`);
    }finally{
        upOrdownLoding.value = false
    }

};
const deleteLoding = ref(false)
const DeleteProduct = async(index, id) => {
    deleteLoding.value = true
    let _params = {
      id: id,
    };
    try {
       await deleteGoodsById(_params)
        createMessageSuccess('删除成功')
        pageProductData()
    } catch (error) {
        createMessageError(error)
    }finally{
        deleteLoding.value = false
    }
};

const DeleteProductList = async() => {
    deleteLoding.value = true
    console.log('批量删除===>');
    let _params = {
        ids: selectedKeysReactive.selectedKeys.toString(),
    };
    try {
       await goodsBatchDelete(_params)
        createMessageSuccess('删除成功')
        pageProductData()
    } catch (error) {
        createMessageError(error)
    }finally{
        deleteLoding.value = false
    }
    // emits('update:value', props.value.filter(item => !selectedKeysReactive.selectedKeys.includes(item)));
    // emits('update:tableData', tableData.value.filter(item => !selectedKeysReactive.selectedKeys.includes(item.id)));
    // selectedKeysReactive.selectedKeys = [];
    // selectedKeysReactive.selectedOptions = [];
    // state.isLoading = true;
};

const batchBtnDisabled = computed(() => {
    return tableData.value.length == 0 || selectedKeysReactive.selectedKeys.length == 0;
});
function pageProductData() {
    paginationRef.value.current = 1;
    pageTableData(searchFormReactive, paginationRef.value);
}
async function handleChange() {
    switchLoading.value = true;
    let param = {
        liveActivityId:props.liveActivityId,
        enable:model.value.isOneself?1:0
    }
    try {
        await updateCartEnable(param)
        createMessageSuccess(model.value.isOneself?'开启小黄车成功':'关闭小黄车成功')
        //刷新表格防止数据不同步
        emits("refresh-table")
       //刷新商品列表（讲解状态）
        refreshTableData();
    } catch (error) {
        createMessageError(error)
        model.value.isOneself = !model.value.isOneself
        
    }finally{
        switchLoading.value = false
    }
}
watch(() => props.show, (newVal) => {
    model.value.isOneself = props.shoppingCartEnable == 1?true:false
    if (newVal) {
        pageProductData();
    }
  }, {immediate: true});
</script>

<style scoped>
.addBtnStyle{
width: 95%;
display: flex;
justify-content: space-between;
/* margin-bottom:10px; */
}
</style>
