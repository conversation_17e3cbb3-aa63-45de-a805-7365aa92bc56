export interface SpecValue {
  attributeValue: string
  id?: string
  level?:number
}

export interface SpecList {
  attributeName: string
  specValue: SpecValue[]
}

export interface SpecPriceItem {
    /** 前端id */
    frontendId?:string
  id?: string
  name?: string
  specCombination?: string[]
  /** 规格组合ID，用于唯一标识 */
  specCombinationIds?: string
  /** 第一个规格的规格值ID */
  firstAttrId?: string | null
  /** 第二个规格的规格值ID */
  secondAttrId?: string | null
  /** 第三个规格的规格值ID */
  thirdAttrId?: string | null
  price: number | null
  /** 成本价格 */
  costPrice: number | null
  /** 划线价 */
  marketPrice?: number | null
  availStocks: number | null
  lockedStocks?: number
  upper?: number
  initSaled?: number
  sku?: string | null
  /** 活动价 */
  activityPriceVOList?:any
  /** 可获得积分 */
  points?:number,
  /** 是否支持定金支付。0=否；1=是 */
  isDownPayment?: 0 | 1,
  /** 是否支持物流代收。0=否；1=是 */
  isCashOnDelivery?: 0 | 1,
  /** 定金单价，单位分 */
  downPayment?: number | null,
  /** 积分配置id */
  pointConfigId?: string | null,
  /** 商品id */
  productId?: string | null,
  /** 积分来源 */
  sourceId?: string | null,
  /** 积分是否删除 */
  pointIsDeleted?: number | null,
  /** 经销商分佣比例 */
  dealerCommission?: number | null,
}
