<template>
  <div class="inner-page-height">
    <div class="data-panel-wrapper">
      <NSpace vertical :size="23">
        <DataStatistics />
        <PendingDispose />
        <GoodsAndCustomer />
        <OrderManagement />
      </NSpace>
    </div>
  </div>
</template>

<script lang="ts" setup>
/** 相关组件 */
import PendingDispose from "./modules/PendingDispose/index.vue";
import GoodsAndCustomer from "./modules/GoodsAndCustomer/index.vue";
import OrderManagement from "./modules/OrderManagement/index.vue";
import DataStatistics from "./modules/DataStatistics/index.vue";
</script>

<style lang="less" scoped>
@import "@/styles/scrollbar.less";
.data-panel-wrapper {
  width: 100%;
  height: 100%;
  padding: 20px 32px;
  box-sizing: border-box;
  overflow: auto;
  .scrollbar(); 
}
</style>