<script setup lang="ts">
import TabsLayout from "@/layout/TabsLayout.vue";
import {ref, watch , toRefs, computed } from "vue";
import { OrderManagementTab} from './index'
import {OrderManagementSelection} from '@/enums'

const tabNameRef = ref<string>(OrderManagementSelection.all);

const props = defineProps<{
  tabValue: string
}>();

watch(()=>props.tabValue,(val)=>{
  if (val) {
    tabNameRef.value = val
  }
},{immediate:true})

const tableDataUpdate = ref()
const tabsData = ref([
  {
    label: "全部",
    key: OrderManagementSelection.all,
  },
  {
    label: "待发货",
    key: OrderManagementSelection.waitFeliverGoods,
  },
  {
    label: "待收货",
    key: OrderManagementSelection.waitTakeOverGoods,
  },
  {
    label: "已完成",
    key: OrderManagementSelection.accomplish,
  },
  {
    label: "待支付",
    key: OrderManagementSelection.waitPay,
  },
  {
    label: "已取消",
    key: OrderManagementSelection.cancelled,
  },
]);


</script>

<template >
  <n-layout>
    <n-layout-content id="OrderManagement">
      <TabsLayout  v-model:value="tabNameRef" :tabsData="(tabsData as any)"  :onlyTabs="true" class="tabsLayout">
        <OrderManagementTab ref="tableDataUpdate" :tabNameRef="tabNameRef" />
      </TabsLayout>
    </n-layout-content>
  </n-layout>
</template>

<style lang="less" scoped>
// @import "@/styles/default.less";
</style>
