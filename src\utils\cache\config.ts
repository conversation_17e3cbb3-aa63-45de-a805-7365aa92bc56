import { Cache_Key, StorageType } from "@/enums/cache";
import type { Cache } from "./type";

export const CacheConfig: Record<string, Cache> = {
  Token: {
    key: Cache_Key.Token,
    type: StorageType.LOCAL,
  },
  UserInfo: {
    key: Cache_Key.UserInfo,
    type: StorageType.LOCAL,
  },
  RouteConfig: {
    key: Cache_Key.RouteConfig,
    type: StorageType.LOCAL,
  },
  System: {
    key: Cache_Key.System,
    type: StorageType.LOCAL,
  },
  OptList: {
    key: Cache_Key.OptList,
    type: StorageType.LOCAL,
  },
  SearchMenus: {
    key: Cache_Key.SearchMenus,
    type: StorageType.LOCAL,
  },
  AuthListTree:{
    key:Cache_Key.AuthList,
    type:StorageType.SESSION
  },
  GlobalConfig: {
    key: Cache_Key.GlobalConfig,
    type: StorageType.LOCAL,
  },
  ImConfig:{
    key: Cache_Key.ImConfig,
    type: StorageType.LOCAL,
  },
  CouponTypeList: {
    key: Cache_Key.CouponTypeList,
    type: StorageType.LOCAL,
  },
};
