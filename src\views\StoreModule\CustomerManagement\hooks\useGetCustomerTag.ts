import { ref } from "vue";
import { useMessages } from "@/hooks";
import { getCustomerTag } from "@/services/api";
import { deepClone } from "@/utils";
import { CUSTOMER_TAG_ALL_VALUE } from "../type";

const message = useMessages();

export default function useGetCustomerTag() {
    const isGetLoading = ref(false);
    const searchValue = ref('');
    /** 当前SelectedKeys */
    const selectedKeys = ref<Array<string>>([CUSTOMER_TAG_ALL_VALUE]);

    /** 分页总数 */
    let recordsTotal = 1;
    
    /** 商品分类搜索参数 */
    const _params = {
    	data: {
    		name: searchValue.value,
    	},
    	pageVO: {
    		current: 1,
    		size: 300,
    	},
    };

    /** 标签数据初始化 */
    const initTagData: Array<{ 
        key: string; // 标签标识
        label: string; // 标签名
        isMenu: boolean;
    }> = [
        {
            key: CUSTOMER_TAG_ALL_VALUE,
            label: '全部',
            isMenu: false,
        }
    ];

    /** 标签数据 */
    const tagOptions = ref(deepClone(initTagData));

    /** 处理数据 */
    const handleData = (dataList: Array<ApiCustomerManagement.CustomerTag>) => {
        let tempDataList = [];
        dataList.forEach(item => {
            tempDataList.push({
                key: item.id,
                label: item.name,
                isMenu: true,
            });
        });
        return tempDataList;
    };

    /** 获取客户标签数据 */
    const getCustomerTagData = async (callBack?: () => void) => {
        try {
    		isGetLoading.value = true;
            _params.data.name = searchValue.value;
            const { total, current, size, records } = await getCustomerTag(_params);
		    _params.pageVO.current = Number(current);
		    _params.pageVO.size = Number(size);
		    recordsTotal = Number(total);
            if (_params.pageVO.current == 1) {
                tagOptions.value = handleData(records);
                initTagData.forEach(item => {
                    tagOptions.value.unshift(item);
                });
                // 初始化回调
                callBack && callBack();
            } else {
                // 分页处理
                let tempList = handleData(records);
                tempList.forEach(item => {
                    tagOptions.value.push(item);
                });
            }
    	} catch (err) {
    		message.createMessageError("获取客户标签列表失败: " + err);
    	} finally {
    		isGetLoading.value = false;
    	}
    };

    /** 滚动加载 */
    const handleScroll = (e) => {
    	const currentTarget = e.currentTarget as HTMLElement;
    	if (currentTarget.scrollTop + currentTarget.offsetHeight >= currentTarget.scrollHeight) {
    		if (_params.pageVO.current * _params.pageVO.size < recordsTotal) {
    			_params.pageVO.current++;
    			getCustomerTagData();
    		}
    	}
    };

    return {
        isGetLoading,
        searchValue,
        getCustomerTagData,
        handleScroll,
        tagOptions,
        selectedKeys,
        _params
    }
};