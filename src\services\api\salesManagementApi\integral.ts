import { defHttp } from "@/services";

/** 积分配置 */
export const enum IntegralApi {
  get = "/pointConfig/getBySource",
  add = "/pointConfig/add",
  update = "/pointConfig/update",
  updatePointEnable = "/globalConfigs/updatePointEnable"
}

interface IntegralConfigRes {
  id: string;
  createTime: string;
  updateTime: string;
  points: number;
  viewDuration: number; // 秒
  channel: 0 | 1; // 渠道：0=商城、1=社群
  channelId: string;
  source: 0 | 1 | 2 | 3 | 4 | 5; // 来源类别：0=过期未使用、1=购物返回、2=积分购物签到、3=签到、4=每日来访、5=查看商品
  sourceId: string;
  isEnabled: 0 | 1; // 是否启用：0=否、1=是
  createBy: string;
  updateBy: string;
}
/**
 * @description 获取积分领取配置（根据来源类型获取）
 * @param source 来源类别：0: 过期未使用  1: 购物返回  2: 积分购物签到  3: 签到  4: 每日来访  5: 查看商品
 */
export function getIntegralConfig(source: 0 | 1 | 2 | 3 | 4 | 5) {
  return defHttp.get<IntegralConfigRes>({
    url: IntegralApi.get + "?source=" + source,
  });
}

/**
 * @description 新增积分领取配置
 */
export function addIntegralConfig(_params) {
  return defHttp.post({
    url: IntegralApi.add,
    params: {
      data: _params,
    },
  });
}

/**
 * @description 编辑积分领取配置
 */
export function updateIntegralConfig(_params) {
  return defHttp.put({
    url: IntegralApi.update,
    params: {
      data: _params,
    },
  });
}

// //处方-批量删除
// export function presBatchDelete(params) {
//     return defHttp.post({
//       url: IntegralApi.presBatchDelete,
//       params
//     });
// }

/**
 * @description 更新是否启用积分商城
 */
export function updateIntegralEnabled(_params) {
  return defHttp.post({
    url: IntegralApi.updatePointEnable,
    params: _params,
  });
}
