import { nextTick, onUnmounted, ref } from "vue"

export function useOverviweHeightResizeObserver(){
  const overviewDomRef = ref<null | HTMLDivElement>(null)
  const overviewDomHeightRef = ref(105);
  const overviewResizeObserver = createOverviweResizeObserver();
  function createOverviweResizeObserver(){
    return new ResizeObserver(() => {
      calcTableInnerHeight();
    });
  }
  function calcTableInnerHeight(){
    if(overviewDomRef.value){
      overviewDomHeightRef.value = overviewDomRef.value.offsetHeight || 105
    }
  }
  nextTick(() => {
    if(overviewDomRef.value){
      calcTableInnerHeight();
      overviewResizeObserver.observe(overviewDomRef.value);
    }
    
  });
  onUnmounted(() => {
    overviewResizeObserver.disconnect();
  });
   
  return {
    overviewDomRef,
    overviewDomHeightRef
  }
}