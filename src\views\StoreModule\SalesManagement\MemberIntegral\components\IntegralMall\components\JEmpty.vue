<template>
    <div class="infoWrapper">
      <img :src="EmptyDataSrc" alt="" />
      <div class="notice">暂无数据</div>
    </div>
</template>

<script lang="ts" setup>
import EmptyDataSrc from "@/assets/image/exception/emptyData.png";

defineOptions({ name: 'JE<PERSON><PERSON>' });

</script>


<style lang="less" scoped>
.infoWrapper {
  width: 100%;
  text-align: center;
  padding: 20px;
  box-sizing: border-box;
}

.notice {
  color: #333333;
  line-height: 29px;
  font-size: 16px;
}

img {
  height: 210px;
}
</style>