<template>
  <JModal
    v-model:show="isShow"
    width="680"
    :title="Add_active?'新增福利券分类':'编辑福利券分类'"
    @after-leave="closeModal"
    @positive-click="_submit"
    :positiveButtonProps="{
			loading: isLoading
		}"
  >
    <n-form
      ref="formRef"
      :rules="rules"
      :model="model"
      label-width="130px"
      label-placement="left"
      require-mark-placement="right-hanging"
      :style="{
          width: '100%',
        }"
    >
      <n-grid :cols="8" :x-gap="24">
        <n-form-item-gi :span="24" label="福利券名称" path="name" required>
          <n-input v-model:value="model.name" placeholder="请输入名称" :maxlength="30" clearable />
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="描述" path="description">
          <n-input
            v-model:value="model.description"
            type="textarea"
            rows="2"
            placeholder="请输入描述"
            :maxlength="150"
            clearable
          />
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="商品兑换说明" path="exchangeNote">
          <n-input
            v-model:value="model.exchangeNote"
            type="textarea"
            rows="2"
            placeholder="请输入兑换说明"
            :maxlength="150"
            clearable
          />
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="是否为现金抵扣券" path="cashCouponFlag">
          <n-radio-group v-model:value="model.cashCouponFlag" :disabled="!Add_active" name="radiogroup">
            <n-space>
              <n-radio :value="0">否</n-radio>
              <n-radio :value="1">是</n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item-gi>
        <n-form-item-gi v-if="model.cashCouponFlag === 1" :span="24" label="现金抵扣券金额" path="money" required>
          <n-input v-model:value="model.money" @input="handleMoneyInput" type="text" rows="2" clearable />
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="图片" path="imageUrl">
          <div style="width: 100%;">
            <UploadProductImg v-model:value="model.imageUrl" accept=".jpg,.png" :fileListSize="1" :maxFileSize="2" />
            <div style="width: 98%; margin-top: 12px;">建议图片尺寸为 160*160，图片需小于2M，只支持png、jpg格式。</div>
          </div>
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </JModal>
</template>

<script setup lang="ts">
import { ref,computed, watch } from 'vue';
import { useMessages } from '@/hooks';
import { updateCouponCate, addCouponCate } from '@/services/api';
import UploadProductImg from "@/components/UploadProductImg/index.vue";
import { yuanToCent } from '@/utils/math';
const message = useMessages();

const props = withDefaults(defineProps<{
    Add_active: boolean;
    row?: any;
    show: boolean;
}>(), {
    Add_active: true,
    row: ()=>{},
    show: false,
});
const emits = defineEmits<{
    (e: 'update:show', value: boolean): void;
    (e: 'refresh'): void;
}>();

const isShow = computed({
    get: () => props.show,
    set: (value: boolean) => {
      emits('update:show', value);
    }
});

const initParams = {
  id:null,
  name: '',
  description:'',
  exchangeNote:'',
  cashCouponFlag: 0,
  money: null,
  imageUrl:[]
};
const model = ref({ ...initParams });

/* 表单规则 */
const rules = {
   name:{
    required: true,
    trigger: ["blur", "change"],
    message: "请输入福利券分类名称",
  },
  money:{
    trigger: ["blur", "change"],
    message: "请输入大于0且最多两位小数的金额",
    validator: () => {
      if(model.value.cashCouponFlag === 1) {
        return model.value.money !== null && model.value.money > 0;
      } else {
        return true
      }
    }
  },
};

const isLoading = ref(false);
const handleMoneyInput = (value: string) => {
  // 允许数字、小数点，且最多保留两位小数
  const reg = /^(\d+)(\.\d{0,2})?$/
  if (value === '' || reg.test(value)) {
    model.value.money = value
  } else {
    // 自动截取前两位小数或拒绝非法输入
    const parsed = parseFloat(value)
    if (!isNaN(parsed)) {
      model.value.money = parsed.toFixed(2)
    } else {
      model.value.money = ''
    }
  }
}
// 关闭按钮
const closeModal = () => {
    isShow.value = false;
    model.value = { ...initParams };
    model.value.imageUrl = [];
}

// 确认按钮
const formRef = ref(null); 
const _submit = () => {
    formRef.value?.validate(async (errors: any) => {
        if (!errors) {
            const {id,imageUrl,...other} = model.value
            const params = {
              data:{
                ...(props.Add_active ? {} : {id: model.value.id}),
                ...other,
                imageUrl:imageUrl.length > 0 ? imageUrl[0].path:'',
                cashCouponAmt:model.value.cashCouponFlag === 1 ? yuanToCent(model.value.money) : null
              }
            }
            const api = props.Add_active ? addCouponCate : updateCouponCate;
            try {
              isLoading.value = true;
              await api(params)
              message.createMessageSuccess('操作成功');
              emits('refresh');
              closeModal();
            } catch (error) {
              message.createMessageError(`${error}`);
            }finally{
              isLoading.value = false;
            }
            
        }
    });
}

watch(()=>props.show, (newVal, oldVal) => {
  if(newVal && !props.Add_active){
    const rowData = {
      id: props.row.id || null,
      name: props.row.name || '',
      description: props.row.description || '',
      exchangeNote: props.row.exchangeNote || '',
      cashCouponFlag: props.row.cashCouponFlag ?? null,
      money: props.row.cashCouponAmt ? (props.row.cashCouponAmt / 100).toFixed(2) : 0,
      imageUrl: props.row.imageUrl ? [{ path: props.row.imageUrl }] : [],
    }
    Object.assign(model.value, rowData);
  }
});

</script>