<template>
  <JModal
    v-model:show="show"
    :title="title"
    width="1280"
    height="760"
    @after-leave="closeModal"
    @after-enter="handleAfterEnter"
	@positive-click="handleSave"
	:positiveButtonProps="{
		loading: isLoading
	}"
  >
    <div class="wrapper">
      <n-spin :show="isGetLoading" size="small" style="height: 100%;">
        <n-form
          ref="formRef"
          :model="model"
          :rules="rules"
          label-width="auto"
          label-placement="left"
          require-mark-placement="right-hanging"
          size="small"
          :style="{
	      	width: '100%',
	      	height: '290px',
	      }"
        >
          <n-grid :cols="12" :x-gap="12" responsive="self">
            <!-- 基本设置 -->
            <n-gi :span="12">
              <div class="title-wrapper">
                <div class="title-line"></div>
                <span>基本设置</span>
              </div>
            </n-gi>
            <!-- 首图 -->
            <n-form-item-gi :span="12" label="首图">
              <n-image width="80" height="80" :src="model.firstImg" />
            </n-form-item-gi>
            <!-- 名称 -->
            <n-form-item-gi :span="12" label="名称">
              <span>{{ model.name }}</span>
            </n-form-item-gi>
            <!-- 选择分类 -->
            <n-form-item-gi :span="12" label="选择分类" path="pointCateId">
              <JGoodsClassification
                size="small"
                :type="GoodsCategoryType.INTEGRAL"
                v-model:value="model.pointCateId"
                style="width: 320px;"
				isImmediately
              />
            </n-form-item-gi>
            <!-- 兑换积分与金额 -->
            <n-gi :span="12">
              <div class="title-wrapper">
                <div class="title-line"></div>
                <span>兑换积分与金额</span>
                <span style="color: #FF4D4F;">*</span>
                <p style="font-size: 14px; font-weight: normal; margin-left: 12px;">默认前台展示规格中最低的兑换积分</p>
              </div>
            </n-gi>
          </n-grid>
        </n-form>
        <!-- 商品配置 -->
        <div class="creditsexchange-h1">
          <CreditsExchangeTable 
		        ref="creditsExchangeTableRef" 
				:type="model.type"
		        :productSpecList="model.productSpecList"
				:attributeList="model.attributeList"
		  />
        </div>
      </n-spin>
    </div>
	<!-- 商品是否上架 -->
	<JCheckbox v-model:checked="model.isPointPublish" class="putaway">
      <span style="font-size: 16px;">上架</span>
    </JCheckbox>
  </JModal>
</template>

<script setup lang="ts" name="IntegralGoodsEdit">
import { ref, computed } from "vue";
import type { FormRules } from "naive-ui";
import { useMessages, useBoolean, useLoading } from '@/hooks';
import { addProductSpec, getProductSpec, updateProductSpec, getProductAttribute } from "@/services/api";
import { deepClone, isObject } from "@/utils";
import type { GoodsType } from "@/enums";
import { GoodsCategoryType } from "@/enums";
import {transformSpecData} from "@/views/StoreModule/GoodsManagement/components/PriceAndStock/utils/transformSpecData";
/** 相关组件 */
import CreditsExchangeTable from "./CreditsExchangeTable.vue";

const { createMessageSuccess, createMessageError } = useMessages();

/** props */
const props = defineProps<{
	refresh: () => void; // 刷新表格
}>();

/** emits */
const emits = defineEmits<{
	(e: "afterSuccess"): void;
}>();

/** 标题 */
const title = computed(() => {
	const titleMap: Record<'add' | 'edit', string> = {
		add: '设置',
		edit: '编辑',
	};
	return titleMap[model.value.type];
});

/** 显隐 */
const { bool: show, setFalse, setTrue } = useBoolean(false);

/* 表单参数初始化 */
const initParams = {
	type: 'add' as 'add' | 'edit', // 模式
	id: null,
	firstImg: null,
	name: "",
	pointCateId: null, // 积分商品分类

	specId: null,
	isPointPublish: false,

	productSpecList: [], // 商品规格

	productId: null, // 商品id

	tempProductSpecList: [], // 商品规格(编辑)

	attributeList: [], // 商品属性

};
const model = ref(deepClone(initParams));

/** 新增的商品id 用来获取商品规格属性 */
const addProductId = ref('');

/* 接收父组件传过来的参数 */
const acceptParams = (_params: {
	type: 'add' | 'edit';
	row?: Partial<ApiStoreModule.Goods>;
	id?: string;
}) => {
	let row = _params.row;
	model.value.type = _params.type;
	if (_params.id) model.value.id = _params.id;
	// 处理行数据
	if (isObject(row) && Object.keys(row).length !== 0) {
		const { frontName, productSpecDTOList } = row;
		addProductId.value = row.id;
		const isFirstImg = row.productImgDTOList.find(item => item.isFirst === 1)?.path;
		Object.assign(model.value, {
			firstImg: isFirstImg,
			name: frontName,
			productSpecList: productSpecDTOList,
		});
	}
	console.log(model.value,'model');
	
	// 显示
	setTrue();
};

/* 表单实例 */
const formRef = ref();

/** 兑换积分与金额表格实例 */
const creditsExchangeTableRef = ref<InstanceType<typeof CreditsExchangeTable> | null>(null);

/* 表单规则 */
const rules: FormRules = {
	pointCateId: {
		required: true,
		trigger: ['blur', 'change'],
		message: '请选择积分商品分类',
	},
};

/* 清空表单 */
const formDataReset = () => {
	model.value = deepClone(initParams);
};

/** 弹窗出现之后回调 */
const { loading: isGetLoading, startLoading: startGetLoading, endLoading: endGetLoading } = useLoading();
async function handleAfterEnter() {
	try {
		startGetLoading();
		const { id } = model.value;
		if (id) {
			const productData = await getProductSpec(id);
			const attributeData = await getProductAttribute(id);
			const attributeList = transformSpecData(attributeData);
			
			if (productData) {
				const { frontName, pointSpecs, isPointPublish, productId, pointCateId } = productData;
				const isFirstImg = productData.productImgDTOList.find(item => item.isFirst === 1)?.path;
				Object.assign(model.value, {
					firstImg: isFirstImg,
					name: frontName,
					productSpecList: [...pointSpecs],
					isPointPublish: isPointPublish ? true : false,
					productId: productId,
					pointCateId: pointCateId,
					tempProductSpecList: [...pointSpecs],
					attributeList
				});
			}
		}else{
			const attributeData = await getProductAttribute(addProductId.value);
			const attributeList = transformSpecData(attributeData);
			model.value.attributeList = attributeList;
		}
	} catch (error) {
		createMessageError("获取积分商品详情失败：" + error);
	} finally {
		endGetLoading();
	}
}


/* 关闭弹窗之后 */
const closeModal = () => {
	formDataReset();
};

/** 获取参数 */
const _getAddParams = () => {
	const { isPointPublish, pointCateId } = model.value;
	let pointSpecs = [];
	if (creditsExchangeTableRef.value) {
		pointSpecs = creditsExchangeTableRef.value?.tableData.map(item => {
			return {
				productId: item['productId'],
				specId: item['id'],
				exchangePoints: item['exchangePoints'] ?? 0,
				exchangePrice: (item['exchangePrice'] ?? 0 )* 100,
				upper: item['upper'],
				soldQty: 0,
				initSaled: item['initSaled'],
				isFreeShipping: 1, // 是否包邮，默认包邮。0=否；1=是
				isDeleted: 0,
			}
		});
	}
	return {
		pointSpecs,
		isPointPublish,
		pointCateId
	}
};

/** 筛选处理数据 */
function compareSpecList(originalArray, newArray) {
    let _paramsSpecList = [];

    // 创建一个字典来存储原数组的项，以便快速查找
    let originalDict = {};
    originalArray.forEach(item => {
        originalDict[item.id] = item;
    });

    // 创建一个字典来存储新数组的项，以便快速查找
    let newDict = {};
    newArray.forEach(item => {
        newDict[item.id] = item;
    });

    // 处理新增的项
    newArray.forEach(item => {
        if (!originalDict[item.id]) {
            _paramsSpecList.push({
                productId: item.productId,
                specId: item.id,
                exchangePoints: item.exchangePoints ?? 0,
                exchangePrice: (item.exchangePrice ?? 0) * 100,
                upper: item.upper,
                soldQty: 0,
                initSaled: item.initSaled,
                isFreeShipping: 1,
                isDeleted: 0
            });
        } else {
			_paramsSpecList.push({
				...item,
				exchangePrice: (item.exchangePrice ?? 0) * 100,
			});
		}
    });

    // 处理删除的项
    originalArray.forEach(item => {
        if (!newDict[item.id]) {
            _paramsSpecList.push({
                ...item,
                isDeleted: 1
            });
        }
    });

    return _paramsSpecList;
}

/** 获取编辑参数 */
function _getEditParams() {
	const { productId, isPointPublish, tempProductSpecList, pointCateId } = model.value;
	let _paramsSpecList = compareSpecList(tempProductSpecList, creditsExchangeTableRef.value?.tableData);
	return {
		pointSpecs: [..._paramsSpecList],
		isPointPublish,
		productId,
		pointCateId
	}
}

/** 校验函数 */
function validatePointSpecs(pointSpecs: Array<any>) {
    if (pointSpecs?.length === 0) {
        createMessageError('请选择规格！');
        return false;
    }

    for (const spec of pointSpecs) {
        if (!spec.exchangePoints) {
            createMessageError('兑换积分未录入，请输入兑换积分！');
            return false;
        }
        if (spec.upper === null) {
            createMessageError('请录入兑换总上限！');
            return false;
        }
    }

    return true;
}


/* 确认--保存 */
const { loading: isLoading, startLoading, endLoading } = useLoading(false);
const handleSave = (e: MouseEvent) => {
	e.preventDefault();
	formRef.value?.validate(async (errors: any) => {
		if (!errors) {
			try {
				startLoading();
				const { type } = model.value;
				// 新增
				if (type === 'add') {
					let _params = _getAddParams();
					// 校验
					let ckecked = validatePointSpecs(_params.pointSpecs);
					if (!ckecked) return;
					await addProductSpec(_params);
					createMessageSuccess('添加成功！');
					// emits
					emits('afterSuccess');
				}
				// 编辑
				if (type === 'edit') {
					let _params = _getEditParams();
					// 校验
					let ckecked = validatePointSpecs(_params.pointSpecs);
					if (!ckecked) return;
					await updateProductSpec(_params);
					createMessageSuccess('编辑成功！');
				}

				// 刷新
				props.refresh && props.refresh();
				setFalse();
			} catch (error) {
				createMessageError(`保存失败：` + error);
			} finally {
				endLoading();
			}
		}
	});
};

defineExpose({
	acceptParams,
});
</script>

<style scoped lang="less">
@import "../../../styles/index.less";

.wrapper {
	width: 100%;
	height: 100%;
	:deep(.n-spin-content) {
        height: 100%;
    }
	.creditsexchange-h1 {
		height: calc(100% - 290px);
	}
	.creditsexchange-h2 {
		height: calc(100% - 240px);
	}
}

.putaway{
	position: absolute;
	left: 18px;
	bottom: 16px;
}
</style>
