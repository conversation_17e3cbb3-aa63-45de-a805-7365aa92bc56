<template>
  <div class="wrapper inner-page-height">
      <FormLayout
        :isLoading="isLoading"
        :tableData="tableData"
        :tableColumns="tableColumns"
        :pagination="paginationRef"
        @paginationChange="paginationChange"
        :isNeedCollapse="false"
        :isDisplayIndex="false"
        :isTableSelection="false"
      >
        <!-- 表单 -->
        <template #searchForm>
          <n-form
            ref="formRef"
            label-placement="left"
            label-width="auto"
            :show-feedback="false"
            require-mark-placement="right-hanging"
            size="small"
            :style="{ width: '100%' }"
          >
            <n-form-item :span="12" label="机构名称" path="">
              <j-search-input
                v-model:value.trim="model.search"
                placeholder="请输入机构名称"
                @search="handlerSearch"
              />
            </n-form-item>
          </n-form>
        </template>
    
        <template #tableHeaderBtn>
          <n-button @click="refresh" :loading="isLoading" class="store-button">刷 新</n-button>
          <JAddButton  type="primary" @click="handleFn(true)" v-if="hasOrganizationManagementAddAuth">新增</JAddButton>
        </template>
      </FormLayout>
      <OrganizationSetting v-model:show="isShow" :Add_active="isAddActive" :row="row" @refresh="refresh" />
  </div>
</template>

<script lang="tsx" setup name="DoctorManagement">
import { onMounted, ref } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { organizationList, deleteOrganization } from "@/services/api";
import { useMessages } from '@/hooks';
import JImage from "@/components/JImage/index.vue";
import { hasOrganizationManagementAddAuth, hasOrganizationManagementEditAuth, hasOrganizationManagementDeleteAuth } from "./authList";
import OrganizationSetting from "./components/organizationSetting.vue";
import Popconfirm from "@/components/Popconfirm/index.vue";

const { createMessageSuccess, createMessageError } = useMessages();

/** 表格hook */
const {
  isAddLoading,
  isEditLoading,
  isLoading,
  tableData,
  paginationRef,
  pageTableData,
  deleteTableData,
  editTableData,
  addTableData,
  refreshTableData,
  paginationChange,
} = useTableDefault({
  pageDataRequest: organizationList,
});

/* 表格列表项 */
const tableColumns = ref([
  {
    title: "机构名称",
    key: "name",
    width: 300,
    align: "left",
  },
  {
    title: "所在省",
    key: "province",
    width: 180,
    align: "left",
  },

  {
    title: "所在市",
    key: "cityName",
    width: 150,
    align: "left",
  },
  {
    title: "所在区",
    key: "area",
    width: 150,
    align: "left",
  },
  {
    title: "创建时间",
    key: "createTime",
    width: 150,
    align: "left",
  },
  {
    title: "操作",
    key: "action",
    width: 120,
    fixed: "right",
    align: "left",
    render: row => {
      return (
        <n-space style="padding: 5px 0;">
          {
            hasOrganizationManagementEditAuth ? <n-button text type="primary" onClick={() => handleFn(false, row)}>编辑</n-button> : null
          }
          {
            hasOrganizationManagementDeleteAuth ? <Popconfirm 
              onHandleClick={() =>deleteFn(row.id)} 
              loading={deleteLoading.value} 
              buttonContent ={'删除'} 
              type={'error'} 
              promptContent={'删除后该机构的医生将变更为无机构状态。'}/> : null
          }
        </n-space>
      );
    },
  },
]);

const isShow = ref(false);
const isAddActive = ref(true);

const row = ref(null);
const handleFn = (isAdd:boolean, rowData?:any) => {
  isShow.value = true;
  isAddActive.value = isAdd;
  row.value = rowData;
};

const deleteLoading = ref(false);
const deleteFn = (id:string) => {
  deleteLoading.value = true;
  deleteOrganization({id}).then(res => {
    createMessageSuccess('删除成功');
    refresh();
  }).catch(err => {
    createMessageError(err);
  }).finally(() => {
    deleteLoading.value = false;
  });
};

/** 参数 */
const model = ref({
  search: ''
});

/** 获取参数 */
const getParams = () => {
  const { search } = model.value;
  return {
    search, 
  };
};

/** 搜索 */
const handlerSearch = () => {
  tableSearch();
};

/** 表格刷新 */
function refresh(){
  tableSearch();
}

/* 表格搜索 */
const tableSearch = () => {
  pageTableData(getParams(), paginationRef.value);
};

/** 组件挂载 */
onMounted(() => {
  tableSearch();
});
</script>

<style lang="less" scoped>
@import "@/styles/defaultVar.less";
</style>
