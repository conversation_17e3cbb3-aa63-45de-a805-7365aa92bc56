<template>
  <FormLayout
    :isLoading="isLoading"
    :is-need-collapse="false"
    :tableData="tableData"
    :tableColumns="tableColumns"
    :pagination="paginationRef"
    :isTableSelection="false"
    @paginationChange="paginationChange"
  >
    <template #searchForm>
      <n-form
        :model="model"
        :show-feedback="false"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
        size="small"
        :style="{ width: '100%' }"
      >
        <n-form-item :show-label="false">
          <JSearchInput 
            v-model:value="model.name" 
            placeholder="请输入操作员昵称" 
            @search="tableSearch"
            :width="200"
          />
        </n-form-item>
      </n-form>
    </template>
    <!-- 操作按钮 -->
    <template #tableHeaderBtn>
      <n-button @click="refreshTableData" class="store-button">刷 新</n-button>
      <n-button v-if="hasAddOperatorsAuth" type="primary" @click="openOperatorModel('add')">新增操作员</n-button>
    </template>
  </FormLayout>
  <OperatorModel ref="operatorModelRef" :refresh="refreshTableData" />
</template>

<script setup lang="tsx" name="OperatorManagement">
import { onMounted, ref } from "vue";
import { NButton, NSpace, NTag, useDialog } from "naive-ui";
import { useMessages, useLoading } from "@/hooks";
import { useTableDefault } from "@/hooks/useTableDefault";
import { useUserStore } from "@/stores/modules/user";
import { afterLogout } from "@/utils/accountUtils";
import { 
    getOperatorPage, 
    deleteOperatorById, 
    resetOperatorPassword, 
    updateOperatorStatus
 } from "@/services/api";
import { hasAddOperatorsAuth, hasEditOperatorsAuth, hasDeleteoperatorAuth, hasResetpasswordAuth } from "../../authList";
import { SystemRoleType, VisitDataMap } from "@/enums";
/** 相关组件 */
import FormLayout from "@/layout/FormLayout.vue";
import OperatorModel from "../OperatorModel.vue";
import { getRandomPwd } from "@/components/PwdInput/pwdUtils";
import { CopyOutline } from '@vicons/ionicons5'
import { copyText } from "@/utils/clipboardUtils";

const dialog = useDialog();

const { createMessageError, createMessageSuccess } = useMessages();
/** 用户 store */
const userStore = useUserStore();

/** 表格 hook */
const {
    isLoading,
    tableData,
    pageTableData,
    paginationRef,
    paginationChange,
    refreshTableData,
} = useTableDefault({
    pageDataRequest: getOperatorPage,
});

/** 表格 columns */
const tableColumns = [
    {
        title: "操作员昵称",
        key: "name",
        width: 150,
        align: 'left',
    },
    {
        title: "操作员账号",
        key: "account",
        width: 120,
        align: 'left',
    },
    {
        title: "状态",
        key: "status",
        width: 80,
        align: 'left',
        render: (row) => {
            return (
                <NSpace>
                    <NTag
                        bordered={false}
                        size="small"
                        type={row.status === 0 ? "error" : "success"}
                    >
                        {row.status === 0 ? "禁用" : "启用"}
                    </NTag>
                </NSpace>
            );
        }
    },
    {
        title: "角色",
        key: "roleNames",
        width: 120,
        align: 'left',
    },
    {
        title: "数据权限",
        key: "visitDataType",
        width: 120,
        align: 'left',
        render: (row) => {
            const visitDataTitle = Object.keys(VisitDataMap).find(key => VisitDataMap[key] === row.visitDataType);
            return <span>{visitDataTitle || "未知数据类型"}</span>;
        },
    },
    {
        title: "创建时间",
        key: "createTime",
        width: 150,
        align: 'left',
    },
    {
        title: "更新时间",
        key: "updateTime",
        width: 150,
        align: 'left',
    },
    {
        title: "操作",
        key: "operation",
        width: 150,
        fixed: "right",
        align: 'left',
        render: (row) => {
            if (row?.roleType === SystemRoleType.ADMINISTRATOR 
                && userStore._userInfo.type !== SystemRoleType.ADMINISTRATOR 
                && userStore._userInfo.type !== SystemRoleType.SMALLPROGRAMDEVROLE
            ) {
                return null;
            }
            return (
                <n-space>
                    {/* 编辑 */}
                    {hasEditOperatorsAuth ? <n-button
                        text
                        type="primary"
                        onClick={() => openOperatorModel('edit', row)}
                    >
                        编辑
                    </n-button> : null}
                    {/* 重置密码 */}
                    {hasResetpasswordAuth ? <n-popconfirm
                        onPositiveClick={() => handleOperatorResetPwd(row?.id,model.value.pwd)}
                        positive-button-props={{
                            loading: isResetPwdLoading.value,
                            type: "error",
                            size: 'small'
                        }}
                        negative-button-props={{
                            size: 'small'
                        }}
                    >
                        {{
                            default: () => {
                                return (
                                    <div style={{ padding: "5px 5px" }}>
                                        是否重置密码?
                                        <div style={{ color: "red", padding: "10px 0px" }}>
                                            重置后的密码为: {model.value.pwd}
                                            <n-button onClick={() => copyPwd()} title='复制' text style="font-size: 12px;margin-left: 4px;" type="info" size="tiny">
                                             <n-icon>
                                               <CopyOutline />
                                             </n-icon>
                                           </n-button>
                                        </div>
                                    </div>
                                );
                            },
                            trigger: () => {
                                return (
                                    <n-button
                                        text
                                        type="error"
                                        onClick={() => handleAnewSetUp()}
                                    >
                                        重置密码
                                    </n-button>
                                );
                            },
                        }}
                    </n-popconfirm> : null}
                    {/* 状态 */}
                    {
                        hasEditOperatorsAuth ? row.status == 1 ? (
                            <n-button
                                text
                                type="error"
                                disabled={isEnabledLoading.value}
                                onClick={() => {
                                    handleOperatorStatusChange(row?.id, 0);
                                }}
                            >
                                停用
                            </n-button>) :
                            (<n-button
                                text
                                type="primary"
                                disabled={isEnabledLoading.value}
                                onClick={() => {
                                    handleOperatorStatusChange(row?.id, 1);
                                }}
                            >
                                启用
                            </n-button>) : null
                    }
                    {/* 删除 */}
                    {hasDeleteoperatorAuth ? <n-popconfirm
                        onPositiveClick={() => handleDelete(row?.id, row?.account)}
                        positive-button-props={{
                            loading: isDelLoading.value,
                            type: "error",
                            size: 'small'
                        }}
                        negative-button-props={{
                            size: 'small'
                        }}
                    >
                        {{
                            default: () => {
                                return (
                                    <div style={{ padding: "5px 5px" }}>
                                        是否删除该操作员
                                    </div>
                                );
                            },
                            trigger: () => {
                                return (
                                    <n-button
                                        text
                                        type="error"
                                    >
                                        删除
                                    </n-button>
                                );
                            },
                        }}
                    </n-popconfirm> : null}
                </n-space>);
        },
    },
];

/** 表单参数 */
const defaultValueRef = () => ({
    name: '',
    pwd: null,
});
const model = ref(defaultValueRef());

/** 搜索 */
function tableSearch() {
    getTableData();
}

/** 获取表格数据 */
function getTableData() {
    const _params = {
        name: model.value.name,
    };
    pageTableData(_params, paginationRef.value, true);
}

/** 操作员弹窗 */
const operatorModelRef = ref<InstanceType<typeof OperatorModel> | null>(null);
const openOperatorModel = (type: 'add' | 'edit', row = {}) => {
    operatorModelRef.value?.acceptParams({
        type,
        row
    });
};

/** 停用与启用 0=禁用 1=启用 */
const { loading: isEnabledLoading, startLoading: startEnabledLoading, endLoading: endEnabledLoading }= useLoading();
async function handleOperatorStatusChange(id: string, status: 0 | 1) {
    try {
        startEnabledLoading();
        await updateOperatorStatus({
            id,
            status
        });
        createMessageSuccess(`${status ? '启用' : '停用'}成功！`);
        refreshTableData();
    } catch (e) {
        createMessageError(`${status ? '启用' : '停用'}失败: ${e}`);
    } finally {
        endEnabledLoading();
    }
}

/** 登出 */
const accountLogout = async () => {
  afterLogout();
};

/** 删除 */
const { loading: isDelLoading, startLoading: startDelLoading, endLoading: endDelLoading }= useLoading();
const handleDelete = async (id: string, account: string) => {
    try {
        startDelLoading();
        await deleteOperatorById(id);
        createMessageSuccess("删除成功");
        refreshTableData();

        // 判断是否是当前账户
        if (userStore.userInfo.account === account) {
            dialog.info({
              title: '提示',
              content: () => <span style="color: red;font-size: 16px;">当前账户被删除，你将被强制退出！</span>,
              positiveText: '确 认',
              positiveButtonProps: {
                type: 'primary',
                size: 'small',
                class: 'store-button'
              },
              showIcon: false,
              maskClosable: false,
              closable: false,
              transformOrigin: 'center',
              onPositiveClick: () => {
                accountLogout();
              },
            });
        }
    } catch (error) {
        createMessageError("删除失败：" + error);
    } finally {
        endDelLoading();
    }
}

/** 触发重新设置密码按钮 */
const handleAnewSetUp = () => {
    model.value.pwd = getRandomPwd()
}

/** 复制密码 */
const copyPwd = async() =>{
  try{
    await copyText(model.value.pwd)
    createMessageSuccess('复制密码成功')
  }
  catch(e){
    createMessageError(`复制密码失败${e}`)
  }
}

/** 重置密码 */
const { loading: isResetPwdLoading, startLoading, endLoading }= useLoading();
async function handleOperatorResetPwd(id: string, pwd:string) {
    try {
        startLoading();
        await resetOperatorPassword({
            id,
            pwd
        });
        createMessageSuccess("重置成功！");
        refreshTableData();
    } catch (error) {
        createMessageError("重置密码失败：" + error);
    } finally {
        endLoading();
    }
}

/** 组件挂载 */
onMounted(() => {
    getTableData();
});
</script>

<style lang="less" scoped>
@import "@/styles/default.less";
</style>
