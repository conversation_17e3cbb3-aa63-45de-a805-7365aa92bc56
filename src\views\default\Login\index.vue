<template>
  <div class="main-bg loginWrapper">
    <div class="title-wrapper">
      <img class="logo" :src="logoSrc" :alt="SystemSetting.title" />
      <h1>{{ SystemSetting.title }}</h1>
    </div>
    <div class="loginFormWrapper">
      <div class="loginForm">
        <div class="title">
          <!-- <img class="logo" :src="logoSrc" :alt="SystemSetting.title" /> -->
          <p class="welcome">欢迎登录</p>
          <p v-if='SystemSetting.slogan' class="slogan">{{ SystemSetting.slogan }}</p>
        </div>
        <div class="form">
          <n-space vertical>
            <n-alert v-if="errorMsg" :bordered="false" type="error">
              {{ errorMsg }}
            </n-alert>
            <div style="width: 100%">
              <n-form ref="formRef" :model="loginFormValue" :rules="rules" :show-label="false">
                <n-form-item path="username" label="账户">
                  <n-input 
                    v-model:value="loginFormValue.username" 
                    placeholder="请输入账户" 
                    clearable
                    size="large"
                  >
                    <template #prefix>
                      <n-icon size="20" :color="!formItemValidateErrorReacticve['username']?'#666666':'var(--n-caret-color-error)'">
                        <SvgIcon localIcon="username"></SvgIcon>
                      </n-icon>
                    </template>
                  </n-input>
                </n-form-item>
                <n-form-item path="password" label="密码">
                  <n-input
                    v-model:value="loginFormValue.password"
                    type="password"
                    show-password-on="click"
                    placeholder="请输入密码"
                    @keyup.enter.native="login"
                    size="large"
                    @focus="()=>handlerPwdIsFocus(true)"
                    @blur="()=>handlerPwdIsFocus(false)"
                    @mouseover="()=>handlerPwdIsFocus(true)"
                    @mouseleave="()=>handlerPwdIsFocus(false)"
                  >
                    <template #password-visible-icon>
                      <Transition>
                      <n-icon v-show='pwdDisplayStatusReacticve.isDisplayIcon' :size="20" :color="SystemSetting.primaryColor">
                        <SvgIcon localIcon="eyeClose"></SvgIcon>
                      </n-icon>
                    </Transition>
                    </template>
                    <template #password-invisible-icon>
                      <Transition>
                      <n-icon v-show='pwdDisplayStatusReacticve.isDisplayIcon' :size="20" color="#c2c2c2">
                        <SvgIcon localIcon="eyeClose"></SvgIcon>
                      </n-icon>
                    </Transition>
                    </template>
                    <template #prefix>
                      <n-icon size="20" :color="!formItemValidateErrorReacticve['password']?'#666666':'var(--n-caret-color-error)'">
                        <SvgIcon localIcon="password"></SvgIcon>
                      </n-icon>
                    </template>
                  </n-input>
                </n-form-item>
              </n-form>
            </div>
          </n-space>
        </div>
        <div class="loginButton">
          <n-button size="large" type="primary" block @click="login" :loading="isLoading">登录</n-button>
        </div>
      </div>
    </div>
    <div class="footer" v-if='SystemSetting.mainFooter.display'>
      <n-space justify="center">
        <p v-if='SystemSetting.mainFooter.companyName' class="footer-info">{{ SystemSetting.mainFooter.companyName }}</p>
        <p v-if='SystemSetting.mainFooter.tel' class="footer-info">售后服务 : {{ SystemSetting.mainFooter.tel }}</p>
        <a v-if='SystemSetting.mainFooter.ICP' class="footer-info" href="https://beian.miit.gov.cn" target="_blank">{{ SystemSetting.mainFooter.ICP }}</a>
      </n-space>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { SystemSetting } from "@/settings/systemSetting";
import logoSrc from "@/assets/image/system/logoNew.png";
import { reactive, ref, watch, onMounted } from "vue";
import { afterLogin } from "@/utils/accountUtils";
import { useRouter } from "vue-router";
import SvgIcon from "@/components/SvgIcon/index.vue";
import { userLogin } from "@/services/api";
import { md5Encryption } from "@/utils/crypto";
import { isProdEnv } from "@/utils/envUtils";


const errorMsg = ref<string | null>(null);
const formRef = ref(null);
const isLoading = ref<boolean>(false);
const loginFormValue = ref({
  username: "",
  password: "",
});
const router = useRouter();
const formItemValidateErrorReacticve = reactive({
  username:false,
  password:false,
})
const pwdDisplayStatusReacticve = reactive({
  isDisplayIcon:false
})

function handlerPwdIsFocus(isFocus:boolean){
  if(isFocus && loginFormValue.value.password.length>0){
    pwdDisplayStatusReacticve.isDisplayIcon = true
  }else{
    pwdDisplayStatusReacticve.isDisplayIcon = false
  }
}
watch(()=>loginFormValue.value.password,(newVal)=>{
  if(newVal.length>0){
    pwdDisplayStatusReacticve.isDisplayIcon = true
  }else{
    pwdDisplayStatusReacticve.isDisplayIcon = false
  }
})

const login = (e) => {
  const isProduction = isProdEnv()
  e.preventDefault();
  if (isLoading.value) {
    return;
  }
  formRef.value?.validate(async (errors) => {
    if (!errors) {
      isLoading.value = true;
      try {
        const data = await userLogin({
          username:loginFormValue.value.username,
          password: isProduction ? md5Encryption(loginFormValue.value.password) : loginFormValue.value.password
        })
        // const {token,userEntityDTO,treeResList} = data
        // if(!treeResList || !treeResList.length){
        //   errorMsg.value = '该账户角色异常，请联系管理员';
        //   isLoading.value = false;
        //   return
        // }
        const {token,account,resList,id, name,marketplaceType,type,visitDataType,isInitialPassword,isSimplePassword,supplierId} = data
        afterLogin({ token,router,userinfo:{
          name,
          id,
          account,
          type,
          supplierId
        },
        treeResList:resList,
        marketplaceType,
        visitDataType,
        isInitialPassword,
        isSimplePassword
      });
      } catch (e) {
        errorMsg.value = e;
        console.log(e);
      }
      isLoading.value = false;
    }
  });
};
const rules = {
  username: {
    required: true,
    trigger: ["blur", "input"],
    message: "登录账户不能为空",
  },
  password: {
    required: true,
    trigger: ["blur", "input"],
    message: "请输入密码",
  },
};

</script>
<style scoped lang="less">
@import "@/styles/default.less";
@import "@/styles/defaultVar.less";

.loginWrapper {
  width: var(--content-client-width);
  height: var(--content-client-height); 
  display: flex;
  background-color: #f9f9fa;
  position: relative;
  background: url("@/assets/image/system/login/loginBg2.jpg") no-repeat;
  background-size: cover;
}
.title-wrapper{
  position: absolute;
  top: 32px;
  left: 48px;
  // display: flex;
  height: 50px;
  // align-items: center;
  h1{
    color: #333333;
    font-size: 18px;
    font-weight: 600;
    margin-top: 8px;
  }
  img{
    height: 100%;
  }
}

.loginFormWrapper {
  position:absolute;
  overflow: hidden;
  width: 640px;
  box-sizing: border-box;
  z-index: 20;
  display: flex;
  justify-content: center;
  align-items: center;
  right: 0px;
  height: 100%;
  background: #fff;
  top:0px;

  .loginForm {
    position: relative;
    padding: 5.6% 6%;
    min-width: 480px;
    display: flex;
    // width: 480px;
    width: 37.5%;
    box-sizing: border-box;
    right: 0;
    flex-wrap: wrap;
    flex-direction: column;
    border-radius: 21px;
   

    .loginButton {
      // margin-bottom: 28px;
    }

    & .title,
    & .form {
      width: 100%;
      z-index: 15;
    }
    & .form {
      padding: 20px 0;
    }
    & .title {
      // padding-bottom: 20px;
      .logo {
        width: 147px;
      }
      .welcome{
        font-size: 30px;
        margin-bottom: 16px;
        color:@primary-color;
      }
      .slogan {
        font-size: 16px;
        color: #666666;
        line-height: 22px;
      }
    }
  }
  .inputPrefix{
    width: 20px;
    height: 20px;
  }
}
.footer {
  position: absolute;
  bottom: 24px;
  left: 32px;
  .footer-info {
    font-size: @font-size-lg;
    color: #666666;
    line-height: 22px;
    text-decoration: none;
  }
}

:deep(.loginFormWrapper .form input:-webkit-autofill){
  -webkit-box-shadow: 0 0 0 1000px white inset !important;
}
.v-enter-active,
.v-leave-active {
  transition: opacity 0.2s ease;
}

.v-enter-from,
.v-leave-to {
  opacity: 0;
}
</style>
