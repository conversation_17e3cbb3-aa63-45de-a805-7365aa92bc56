<template>
  <n-modal
    :show="props.show"
    preset="card"
    style="width: 800px"
    :title="titleComputed"
    :bordered="false"
    :auto-focus="false"
    size="small"
    :closable="true"
    @after-leave="closeModal"
    @close="emits('update:show',false)"
    @mask-click="emits('update:show',false)"
  >
    <n-form
      ref="formRef"
      :model="model"
      :rules="rules"
      label-width="auto"
      label-placement="left"
      require-mark-placement="right-hanging"
      size="small"
      :style="{
        width: '100%',
      }"
    >
      <n-grid :cols="24" :x-gap="24">
        <n-form-item-gi :span="16" label="角色名称" path="name">
          <n-input v-model:value="model.name" maxlength="20" placeholder="角色名称" clearable :disabled="props.mode=='detail'"/>
          <n-button v-if="props.params.type !== 13" type="primary" @click="openJRulePreview" style="margin-left: 10px">经销商或群管角色配置说明</n-button>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="关联权限内容" label-placement="top" path="selectedAuthIds">
          <MenuAuthTable
            v-if="props.params.type !== 13"
            v-model:value="model.selectedAuthIds"
            :disabled="props.mode=='detail'"
          ></MenuAuthTable>
          <n-data-table
            v-else
            :columns="DPColumns"
            :data="props.params.type === 13 && props.params.name === '医生角色' ? doctorData : props.params.name === '药师角色' ? pharmacistData : []"
            :style="{ height: `${300}px` }"
            :bordered="false"
            flex-height
          />
        </n-form-item-gi>
      </n-grid>
    </n-form>
    <template #footer>
      <n-space justify="end">
        <n-button size="small" @click="emits('update:show',false)">取消</n-button>
        <n-button v-if="props.mode !='detail'" size="small" type="primary" :loading="isSaveLoading" @click="handleSave">保存</n-button>
      </n-space>
    </template>
    <!-- 经销商或群管角色配置说明 -->
    <ConfigurationInstruction ref="ConfigurationInstructionRef"></ConfigurationInstruction>
  </n-modal>
</template>

<script setup lang="ts">
import { isArray } from "@/utils/isUtils";
import { computed, ref,watch } from "vue";
import type { RoleEditorMode } from "./type";
import MenuAuthTable from "../MenuAuthTable.vue"
import { authCodeListToAuthTree } from "@/utils/auth/authUtils";
import { createCacheStorage } from "@/utils/cache/storageCache";
import { CacheConfig } from "@/utils/cache/config";
import type { MenuAuthDataResponseItem } from "@/utils/auth/type";
import { roleAdd, roleUpdate } from "@/services/api/roleApi/index";
import {useMessages} from "@/hooks/useMessage";
import ConfigurationInstruction
  from "./ConfigurationInstruction.vue"
const {createMessageError,createMessageSuccess} = useMessages()
const isSaveLoading = ref(false)
const ConfigurationInstructionRef = ref()
const openJRulePreview = () => {
  ConfigurationInstructionRef.value?.acceptParams();
};

export type RoleEditorModalProps = {
  mode:RoleEditorMode,
  show:boolean,
  params:{
    id?:string,
    name?:string,
    authCodeList?:Array<string>,
    type?:number
  }
}
const props = withDefaults(defineProps<RoleEditorModalProps>(),{})
const emits = defineEmits<{
  (e:'update:show',value:boolean):void,
  (e:'refresh'):void,
}>()
const titleComputed = computed(()=>{
  const titleMap:Record<RoleEditorMode,string> = {
    'add': '新建角色',
    'edit': '编辑角色',
    'detail': '查看角色'
  };
  return titleMap[props.mode]
})

/* 表单实例 */
const formRef = ref(null);

/* 表单参数初始化 */
const initParams= {
  id:null,
  name: '',
  rolesJson:'',
  selectedAuthIds:[]
};

const model = ref({ ...initParams });
watch(()=>props.params,(newVal)=>{
  if(isArray(newVal.authCodeList)){
    const extarKeyList = []
    model.value.selectedAuthIds = [...newVal.authCodeList,...extarKeyList]
  }
  model.value.id = newVal.id
  model.value.name = newVal.name
})

function authTreeTransfer(selectedCodeList:Array<string>){
  const authTreeStorage = createCacheStorage(CacheConfig.AuthListTree);
  const authTreeCache = authTreeStorage.get('platform');
  const temp = {data:authCodeListToAuthTree(selectedCodeList,authTreeCache as Array<MenuAuthDataResponseItem>)}
  return JSON.stringify(temp)
}

watch(()=>model.value.selectedAuthIds,(newVal)=>{
  model.value.rolesJson = authTreeTransfer(newVal)
})

/* 表单规则 */
const rules = {
  name: {
    type: 'string',
    required: true,
    trigger: ['blur', 'change'],
    message: '请输入角色名称'
  },
  selectedAuthIds: {
    type: "array",
    required: true,
    trigger: ["blur", "change"],
    message: "请选择相关联权限",
  },
};
const DPColumns = [
   {
      title: '功能模块',
      key: 'module',
      width:150
    },
    {
      title: '功能说明',
      key: 'descr'
    },
]
const doctorData = [
  { module: "接诊", descr: '可进行接诊/退诊、开方等操作'},
  { module: "处方", descr: '可查询开方记录'},
  { module: "问诊单", descr: '可查询问诊单详情，包括问诊人信息，问诊时长和价格等。'},
  { module: "数据", descr: '可查看接诊人次和开方量等数据'},
  { module: "设置", descr: '可设置接诊自动回复等内容'}
]
const pharmacistData = [
  { module: "处方", descr: '审核处方，查看处方审核记录'}
]
/* 清空表单 */
const formDataReset = () => {
  model.value = { ...initParams };
};

/* 关闭弹窗 */
const closeModal = () => {
  formDataReset();
};



/* 确认--保存 */
function handleSave(e: MouseEvent){
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      console.log(model.value);
      try{
        isSaveLoading.value = true
        if(props.mode == 'edit'){
          await roleUpdate({
            id:model.value.id,
            name: model.value.name,
            authResGrantVOList:model.value.selectedAuthIds.map(id=>{
              const params = {
                resId:id,
                id:id,
                roleId:model.value.id,
              }
              if(!props.params.authCodeList.includes(id)){
                delete params['id']
              }
              return params
            })
          })
        }
        else{
          await roleAdd({
            name: model.value.name,
            authResGrantVOList:model.value.selectedAuthIds.map(id=>{
              return {
                resId:id
              }
            })
          })
        }
        createMessageSuccess(props.mode == 'edit'? '编辑成功':'新建成功')
        emits('update:show',false)
        emits('refresh')
      }
      catch(e){
        createMessageError(`保存失败:${e}`)
      }
      finally{
        isSaveLoading.value = false
      }
    }
  });
}

</script>

<style scoped lang="less"></style>
