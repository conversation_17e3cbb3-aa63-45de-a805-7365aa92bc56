import { hasAuth } from "@/utils/auth/authUtils";
import { AssistantDoctorAuth } from "@/enums/authKeys";

/** 新增 */
export const hasMedicalAssistantAdd = function(){
    return hasAuth(AssistantDoctorAuth.Add.key);
}()

/** 编辑 */
export const hasMedicalAssistantEdit = function(){
    return hasAuth(AssistantDoctorAuth.Edit.key);
}()

/** 删除 */
export const hasMedicalAssistantDelete = function(){
    return hasAuth(AssistantDoctorAuth.Delete.key);
}()
