<script setup lang="tsx">
import { isNullOrUnDef } from "@/utils/isUtils";
import { ref, toRefs, toRef, watch } from "vue";
type valType = string | number;
type labelType = string | number;
interface TagSelectDataItem {
  label: labelType;
  value: valType;
}

const selectedDateRangeVal = ref();

const props = withDefaults(
  defineProps<{
    data: TagSelectDataItem[];
    value?: valType;
  }>(),
  {
    data: () => [],
  }
);

const emits = defineEmits<{
  (e: "update:value", value: valType): void;
}>();

const { data } = toRefs(props);
const valueRef = toRef(props, "value");

watch(
  valueRef,
  (newVal) => {
    // if (!isNullOrUnDef(newVal)) {
    // }
    selectedDateRangeVal.value = newVal;
  },
  {
    immediate: true,
  }
);

const onDataRangeSelectChange = (value: valType) => {
  if (isNullOrUnDef(valueRef.value)) {
    selectedDateRangeVal.value = value;
  }
  emits("update:value", value);
};
</script>

<template>
  <div class="tagSelectGroupWrapper">
    <n-space size="small">
      <n-tag
        type="info"
        v-for="range in data"
        :key="range.value"
       
        :checked="selectedDateRangeVal === range.value"
        @update:checked="onDataRangeSelectChange(range.value)"
        :checkable="true"
      >
        {{ range.label }}
      </n-tag>
    </n-space>
  </div>
</template>

<style lang="less" scoped>
@import "@/styles/default.less";
.tagSelectGroupWrapper {
  background-color: #fff;
  padding: 3px 4px;
  height: 32px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}
</style>
