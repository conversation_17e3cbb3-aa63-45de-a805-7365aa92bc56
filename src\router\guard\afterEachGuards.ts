import NProgress from "nprogress";
import { SystemSetting } from "@/settings/systemSetting";
import { isDoctorEnv } from "@/utils/envUtils";

const doneProgress = () => {
  NProgress.done();
};

const changeBroswerTitileByRoute = to => {
  const { title } = to.meta;
  if(isDoctorEnv()){
    document.title = `${title} - ${SystemSetting.doctorTitle}`;
  }
  else{
     document.title = `${title} - ${SystemSetting.title}`;
  }
};

export const afterEachGuardsList = [doneProgress, changeBroswerTitileByRoute];
