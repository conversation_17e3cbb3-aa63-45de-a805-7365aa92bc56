<template>
  <FormLayout
    :isLoading="isLoading"
    :tableData="tableData"
    :tableColumns="tableColumns"
    :isNeedCollapse="false"
    :pagination="paginationRef"
    :isBatchDelete="false"
    :isTableSelection="false"
    :isDisplayHeader="false"
    :table-summary="summaryRefs"
    @paginationChange="paginationChange"
    @table-sorter-change="tableSorterChange"
  />
</template>

<script lang="ts" setup name="OrderReportTable">
import { watch } from "vue";
import dayjs from "dayjs";
import { useTableDefault } from "@/hooks/useTableDefault";
import { getOrderReportsPage } from "@/services/api";
import { useOrderTable } from "../../hooks";
/** 相关组件 */
import FormLayout from "@/layout/FormLayout.vue";

/** props */
const props = defineProps<{
    // 搜索参数
    model: {
        rangeTime: Array<number>;
        type: number;
        orderBy: string;
        isAsc: boolean;
    };
}>();

/** emits */
const emits = defineEmits<{
    (e: 'update:model', value: Object): void;
}>();

/* 表格方法Hook */
const {
    isLoading,
    tableData,
    paginationRef,
    pageTableData,
    refreshTableData,
    paginationChange,
    sortTableData
} = useTableDefault({
    pageDataRequest: getOrderReportsPage,
});

/** 表格数据 */
const { tableColumns, summaryRefs, summaryColumn } = useOrderTable();

/** 表格排序 */
function tableSorterChange(info) {
    let params = {
        ...props.model,
        orderBy: info.sort,
        isAsc: info.sortAsc === 'ascend' ? true : false,
    }
    emits('update:model', params);
    tableSearch();
}

/** 获取参数 */
function getParams() {
    const { rangeTime, isAsc, type, orderBy } = props.model;
    const [startTime, endTime] = rangeTime;
    return {
        startTime: dayjs(startTime).format('YYYY-MM-DD 00:00:00'),
        endTime: dayjs(endTime).format('YYYY-MM-DD 23:59:59'),
        isAsc,
        type,
        orderBy
    }
}

/** 表格搜索 */
function tableSearch() {
    pageTableData(getParams(), paginationRef.value);
}

/** 表格刷新 */
function _refresh() {
    tableSearch();
}

/** 监听 */
watch(() => props.model.rangeTime, (newVal) => {
    tableSearch()
}, { immediate: true });

watch(tableData, (newVal) => {
 summaryRefs.value = summaryColumn(newVal);
});

/** 暴露给父组件 */
defineExpose({
    _refresh,
    paginationRef
});
</script>

<style lang="less" scoped>
:deep(.n-data-table) {
    padding: 0 !important;
}
</style>
