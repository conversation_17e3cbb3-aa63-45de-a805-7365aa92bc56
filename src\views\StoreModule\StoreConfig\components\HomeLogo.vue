<template>
  <div class="wrapper">
    <n-scrollbar style="height: calc(100% - 50px);">
      <n-spin :show="loadShow">
        <n-form
          ref="formRef"
          :model="model"
          :rules="rules"
          label-width="auto"
          label-placement="left"
          require-mark-placement="right-hanging"
          size="small"
          :style="{
               width: '100%',
             }"
        >
          <n-scrollbar style="height: 100%;">
            <n-card :bordered="false" :content-style="{ padding: '25px' }">
              <n-grid :cols="24" :x-gap="24" responsive="self">
                <n-gi :span="24">
                  <div class="title-wrapper">
                    <div class="title-line"></div>
                    <span style="margin-right: 10px">图片Logo</span>
                    <n-checkbox v-model:checked="model.isEnableImg">
                      启用
                    </n-checkbox>
                  </div>
                  <div v-show="model.imgStatic" style="padding:0 0 10px 0;color: red">启用失败，请检查配置</div>
                  <div>
                    <CustomizeUpload v-model:value="model.imgPath" accept="image/*" :fileListSize="1" :max="1"/>
                  </div>
                  <div style="padding:10px 0 10px 0">建议尺寸：24x24px</div>
                </n-gi>
                <n-gi :span="24">
                  <div class="title-wrapper">
                    <div class="title-line"></div>
                    <span style="margin-right: 10px">文字Logo</span>
                    <n-checkbox v-model:checked="model.isEnableName">
                      启用
                    </n-checkbox>
                  </div>
                  <div v-show="model.nameStatic" style="padding:0 0 10px 90px;color: red">启用失败，请检查配置</div>
                  <div style="padding: 10px;font-size: 20px">名称</div>
                  <n-form-item-gi :span="24" path="name" required>
                    <n-input v-model:value="model.name" style="width: 100%" :show-button="false" maxlength="15"/>
                  </n-form-item-gi>
                  <div>(例如公众号名称，商家名)</div>
                  <div style="padding: 10px;font-size: 20px">标语</div>
                  <n-form-item-gi :span="24" path="slogan">
                    <n-input v-model:value="model.slogan" style="width: 100%" :show-button="false" maxlength="30"/>
                  </n-form-item-gi>
                  <div>(例如介绍认证，是世界各地千万用户健康选择)</div>
                </n-gi>
                <n-gi :span="24">
                  <div class="title-wrapper" style="margin-top: 6px">
                    <div class="title-line"></div>
                    <span style="margin-right: 10px">商城首页首屏背景图</span>
                  </div>
                  <div style="padding-bottom: 10px;font-size: 14px">支持JPEG、JPG、PNG、GIF格式，不能大于1M，建议尺寸：375x320px</div>
                  <n-form-item-gi :span="24" path="imgPath" required>
                    <div>
                      <CustomizeUpload v-model:value="model.homeImgPath" accept=".jpeg,.jpg,.png,.gif" :fileListSize="1" :max="1" :maxFileSize="1" sizeTitle="图片大小不能超过1M，请重新上传"/>
                    </div>
                  </n-form-item-gi>
                </n-gi>
              </n-grid>
            </n-card>
          </n-scrollbar>
        </n-form>
      </n-spin>
    </n-scrollbar>
    <div class="footer-wrapper">
      <n-space>
        <n-button type="info" @click="_save" class="store-button">
          保 存
        </n-button>
      </n-space>
    </div>
  </div>
</template>

<script setup lang="tsx" name="campPeriodDetails">
import { ref, onMounted, watch } from "vue";
import { useMessages } from "@/hooks/useMessage";
import { deepClone } from "@/utils";
const { createMessageSuccess, createMessageError,  } = useMessages();
import { homeLogoUpdateParams, homeLogoGetLogoParams } from "@/services/api";
/* 表单参数初始化 */
const initParams = {
  id:null,
  imgPath:'',
  isEnableImg:null,
  name:'',
  isEnableName:null,
  slogan:'',
  imgStatic:false,
  homeImgPath:'',
  nameStatic:false
};
const loadShow = ref(true)
const model = ref(deepClone(initParams));
/* 表单规则 */
const rules = {
  // name: {
  //   required: true,
  //   trigger: ["blur", "change"],
  //   message: "保存失败，请检查配置",
  //   validator: ()=>{
  //     return model.value.name != '' || !model.value.nameStatic;
  //   }
  // },
  // imgPath:{
  //   required: true,
  //   trigger: ["blur", "change"],
  //   message: "请先上传图片",
  //   validator: ()=>{
  //     return model.value.imgPath != '' || !model.value.imgStatic;
  //   }
  // },
};
/* 表单实例 */
const formRef = ref(null);
function fromInit() {
  loadShow.value = true
  try{
    homeLogoGetLogoParams({}).then(e=>{
      model.value = {...model.value,...e}
      model.value.isEnableImg = model.value.isEnableImg == '0'
      model.value.isEnableName = model.value.isEnableName == '0'
    })
  }catch(err){
    createMessageError(`获取开发配置失败：${err}`);
  }finally {
    loadShow.value = false
  }
}
function _save(e: MouseEvent) {
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      try{
        loadShow.value = true;
        model.value.imgStatic = model.value.isEnableImg && (model.value.imgPath == '' || model.value.imgPath.length<1)
        model.value.nameStatic = model.value.isEnableName && model.value.name == ''
        if (model.value.nameStatic || model.value.imgStatic){
          return
        }
        model.value.imgPath = Array.isArray(model.value.imgPath)?model.value.imgPath[0]:model.value.imgPath
        model.value.homeImgPath = Array.isArray(model.value.homeImgPath)?model.value.homeImgPath[0]:model.value.homeImgPath
        model.value.isEnableName = model.value.isEnableName?'0':'1'
        model.value.isEnableImg = model.value.isEnableImg?'0':'1'
        homeLogoUpdateParams({ data:model.value }).then(e=>{
          createMessageSuccess(`操作成功!`);
          setTimeout(fromInit,300)
        })
      }catch(err){
        createMessageError(`操作失败：${err}`);
      } finally {
        loadShow.value = false
      }
    }
  });
}
watch(()=>model.value.imgPath,(newVal)=>{
  if(newVal[0] == '') model.value.imgPath = ''
})
watch(()=>model.value.homeImgPath,(newVal)=>{
  if(newVal[0] == '') model.value.homeImgPath = ''
})
/* 组件挂载 */
onMounted(() => {
  fromInit()
});
</script>

<style scoped lang="less">
@import "@/styles/default.less";
@import "@/styles/defaultVar.less";

@details-inner-bg-height: calc(100vh - @main-header-height - @main-footer-height - @blank-page-padding * 2);

.wrapper {
  width: 100%;
  height: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.details-inner-bg-height {
  height: @details-inner-bg-height;
}

:deep(.n-drawer-body-content-wrapper) {
  padding: 0 !important;
  overflow: hidden;
}

.footer-wrapper {
  width: 100%;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  box-sizing: border-box;
  padding-right: 12px;
  background-color: #fff;
  box-shadow: rgba(17, 17, 26, 0.1) 0 4px 16px, rgba(17, 17, 26, 0.05) 0 8px 32px;
}

:deep(.n-drawer-body-content-wrapper) {
  overflow: hidden;
}

:deep(.n-card.n-card--bordered) {
  border: 0;
}

:deep(.n-input__input-el) {
  text-overflow: ellipsis;
}

.title-wrapper {
  height: 30px;
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 6px;

  .title-line {
    width: 4px;
    height: 60%;
    background-color: @primary-color;
    margin-right: 5px;
  }
}
</style>
