<template>
  <div class="info-wrapper">
    <n-avatar
      lazy
      round
      size="small"
      :src="infoItem.img ? (infoItem.img.endsWith('/132') ? `${infoItem.img.slice(0, -4)}/64` : infoItem.img) : defaultAvatar"
    />
    <div class="info" >
      <template v-if=" infoItem.name || infoItem.nickname">
        <n-ellipsis style="max-width: 200px;">
         {{ infoItem.name || infoItem.nickname }}
        </n-ellipsis>
        <span class="info-id">
          ID:{{ infoItem.id || infoItem.accountUserId }}
        </span>
      </template>
      <slot v-else name="Info"></slot>
    </div>
  </div>
</template>

<script lang="ts" setup name="CustomOptions">
import { toRefs } from "vue";
import defaultAvatar from "@/assets/image/system/avatar.png";

/** props */
const props = defineProps<{
  infoItem: Partial<{
    id: number;
    name: string;
    nickname: string;
    img: string;
    accountUserId:string
  }>;
}>();

const { infoItem } = toRefs(props);
</script>

<style lang="less" scoped>
.info-wrapper {
  width: 100%;
  height: 60px;
  display: flex;
  align-items: center;
  .info {
    display: flex;
    flex-direction: column;
    padding-left: 8px;
  }
  :deep(.info-id) {
      font-weight: 400;
      font-size: 12px;
      color: #999999;
    }
}
</style>
