<template>
  <div class="table_wrapper">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :isNeedCollapse="false"
      :pagination="paginationRef"
      @paginationChange="paginationChange"
      @tableSorterChange="tableSorterChange"
      :isTableSelection="false"
      table-row-key="_dummyId"
    >
      <template #searchForm>
        <!-- 表单 -->
        <n-form
          ref="formRef"
          :model="model"
          :show-feedback="false"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          size="small"
          :style="{ width: '100%' }"
        >
          <!-- 商品类型 -->
          <n-form-item label="商品类型">
            <n-select
              v-model:value="model.type"
              placeholder="请选择商品类型"
              :options="storeGoodsTypeOptions"
              style="width: 160px;"
              clearable
            />
          </n-form-item>
          <!-- 商品搜索 -->
          <n-form-item label="商品搜索">
            <n-input-group>
              <n-select
                v-model:value="model.searchType"
                placeholder="请选择"
                :options="searchTypeOptions"
                style="width: 100px;"
              />
              <JSearchInput
                v-model:value="model.searchValue"
                placeholder="请输入商品名称或ID"
                @search="tableSearch"
                :is-show-search-icon="false"
                :width="210"
              />
            </n-input-group>
          </n-form-item>
          <!-- SPU编码 -->
          <n-form-item label="SPU编码">
            <JSearchInput
              v-model:value="model.spu"
              placeholder="请输入SPU编码"
              @search="tableSearch"
              :is-show-search-icon="false"
              :width="210"
            />
          </n-form-item>
          <!-- 上架状态 -->
          <n-form-item :span="12" label="上架状态">
            <n-select style="width: 160px;" v-model:value="model.isPublish" :options="shelfStatusOptions" clearable />
          </n-form-item>
        </n-form>
      </template>
      <!-- 操作项 -->
      <template #tableHeaderBtn>
        <n-button @click="refresh" class="store-button">刷 新</n-button>
      </template>
    </FormLayout>
  </div>
</template>

<script lang="tsx" setup>
import { ref, watch, onMounted } from "vue";
import { storeGoodsTypeOptions, shelfStatusOptions, shelfStatusLabels } from "@/constants";
import { useTableDefault } from "@/hooks/useTableDefault";
import { useMessages } from "@/hooks";
import { StoreGoodsTypeEnum } from "@/enums";
import { ProductSaleSortTypeEnum, GoodsSearchType, ProductSaleSortValue, ReportType } from "../types";
import { getProduceSaleStatData } from "@/services/api";
import { hasCourseExportAuth } from "../authList";
/** 相关组件 */
import FormLayout from "@/layout/FormLayout.vue";

defineOptions({
  name: "CommoditysalesReport",
});

const { createMessageError, createMessageSuccess } = useMessages();

/* 表格方法Hook */
const {
isLoading,
tableData,
paginationRef,
pageTableData,
refreshTableData,
paginationChange,
} = useTableDefault({
  useDummyId: true,
  pageDataRequest: getProduceSaleStatData,
});

/* 初始化参数 */
const initParams = {
  type: StoreGoodsTypeEnum.REGULAR_PRODUCTS,
  searchType: GoodsSearchType.NAME,
  searchValue: "",
  productId: null,
  productName: null,
  sortType: ProductSaleSortTypeEnum.SalesVolume,
  isAsc: false,
  spu: "",
  isPublish: null,
  reportType: ReportType.GOODSREPORT
};
const model = ref({ ...initParams });

/** 搜索类型 */
const searchTypeOptions = [
  {
    label: "商品名称",
    value: GoodsSearchType.NAME,
  },
  {
    label: "商品ID",
    value: GoodsSearchType.ID,
  },
];

/* 表格项 */
const tableColumns = [
  {
    title: "商品主图",
    key: "nickName",
    align: "left",
    fixed: "left",
    width: 80,
    render: (row) => {
      if (!row.productPic) return <span>-</span>;
      return <n-image width="60" src={row.productPic} lazy />;
    },
  },
  {
    title: "商品名称",
    key: "productFrontName",
    align: "left",
    fixed: "left",
    width: 160,
    render: (row) => {
      return <table-tooltip row={row} nameKey="productFrontName" idKey="productId" />;
    }
  },
  {
    title: "SPU编码",
    key: "spu",
    align: "left",
    width: 80,
  },
  {
    title: "销量",
    sorter: true,
    isSortDefault: true,
    key: "saleNum",
    align: "left",
    width: 120,
  },
  {
    title: "销量额(元)",
    sorter: true,
    key: "saleAmount",
    align: "left",
    width: 120,
    render: row => row.saleAmount ? (row.saleAmount / 100).toFixed(2) : '0.00'
  },
  {
    title: "上架状态",
    key: "isPublish",
    width: 60,
    align: "left",
    render: row => {
      return (
        <n-tag bordered={false} size="small" type={row.isPublish === 1 ? "success" : "error"}>
          {shelfStatusLabels[row.isPublish]}
        </n-tag>
      );
    },
  },
];

/** 排序 */
const tableSorterChange = (info: { sort: string, sortAsc: "ascend" | "descend" }) => {
  model.value.sortType = ProductSaleSortValue[info.sort];
  if (info.sortAsc === 'ascend') {
    model.value.isAsc = true;
  } else {
    model.value.isAsc = false;
  }
  tableSearch();
};

/** 获取参数 */
function getParams() {
  const { spu, type, sortType, isAsc, searchType, searchValue, reportType, isPublish } = model.value;

  let _params = {
    type,
    isPublish,
    sortType,
    isAsc,
    reportType,
    spu
  };

  if (searchType === GoodsSearchType.NAME) {
    _params['productName'] = searchValue;
  } else {
    _params['productId'] = searchValue;
  }

  return _params;
}

/** 表格搜索 */
function tableSearch() {
  pageTableData(getParams(), paginationRef.value);
}

/** 表格刷新 */
function refresh(){
  tableSearch();
};

/* 组件挂载 */
onMounted(() => {
  tableSearch();
});

/** 监听 */
watch(() => [model.value.type, model.value.isPublish], (newVal) => {
  if (newVal) {
    tableSearch();
  }
});
</script>

<style lang="less" scoped>
@import "@/styles/default.less";
.table_wrapper {
  width: 100%;
  height: 100%;
}
</style>
