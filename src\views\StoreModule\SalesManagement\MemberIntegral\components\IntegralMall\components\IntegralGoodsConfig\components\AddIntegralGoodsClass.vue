<template>
	<JModal
		v-model:show="show"
		:title="title"
		width="680"
		@after-leave="closeModal"
		@positive-click="handleSave"
		:positiveButtonProps="{
			loading: isLoading
		}"
	>
		<n-form
			ref="formRef"
			:model="model"
			:rules="rules"
			label-width="auto"
			label-placement="left"
			require-mark-placement="right-hanging"
			size="small"
			:style="{
				width: '100%',
			}"
		>
			<n-grid :cols="12" :x-gap="12" responsive="self">
				<!-- 提示 -->
				<n-gi :span="12" style="margin-bottom: 12px;">
				    <span>保存后展示在积分商城首页，最多创建10个分类。</span>
				</n-gi>
				<!-- 分类名称 -->
				<n-form-item-gi :span="12" label="分类名称" path="name">
					<n-input
						v-model:value="model.name"
						@blur="model.name=($event.target as HTMLInputElement)?.value.trim()"
						placeholder="请输入分类名称（4字符）"
						:maxlength="4"
					/>
				</n-form-item-gi>
                <!-- 排序号 -->
                <n-form-item-gi :span="12" label="排序号">
					<n-input-number
						v-model:value="model.sort"
						placeholder="序号越大排位越前,最多3位数"
						:max="999"
                        :min="0"
                        clearable
                        :show-button="false"
                        style="width: 100%;"
						:precision="0"
					/>
				</n-form-item-gi>
                <!-- 图标 -->
                <n-form-item-gi :span="12" label="图标" path="iconPath">
                    <div style="display: flex;flex-direction: column;margin-bottom: 8px;">
                        <UploadProductImg 
                           v-model:value="model.iconPath" 
                           :maxFileSize="0.5"
                           accept="image/*" 
                           :fileListSize="1" 
                           :max="1"
                        />
                        <span>注：图片需小于500K，支持png、jpg、JPEG、GIF格式。<br /> 建议尺寸：20 x 20</span>
                    </div>
                </n-form-item-gi>
			</n-grid>
		</n-form>
	</JModal>
</template>

<script setup lang="ts" name="AddIntegralGoodsClass">
import { ref, computed } from "vue";
import type { FormRules } from "naive-ui";
import { useMessages, useBoolean, useLoading } from '@/hooks';
import type { TreeOption } from 'naive-ui';
import { addPointGoodsClassification, updatePointGoodsClass } from "@/services/api";
import { isArray, isObject, deepClone } from "@/utils";
import { GoodsCategoryType } from "@/enums";
import type { GoodsType } from "@/enums";

const { createMessageSuccess, createMessageError } = useMessages();

interface ModalProps {
	type: 'add' | 'edit';
	row?: TreeOption & { isMenu?: boolean } & Partial<ApiStoreModule.GoodsClassification>; 
};

/** emits */
const emits = defineEmits<{
  (e: "afterSuccess", value: string, type: GoodsType ): void;
}>();

/** 标题 */
const title = computed(() => {
  const titleMap: Record<'add' | 'edit', string> = {
    add: '新建分类',
    edit: '编辑分类',
  };
  return titleMap[modalProps.value.type];
});

/** 显隐 */
const { bool: show, setTrue, setFalse } = useBoolean();
const modalProps = ref<ModalProps>({
	type: 'add',
});

/* 接收父组件传过来的参数 */
const acceptParams = (params: ModalProps) => {
	modalProps.value = params;
	let row = params.row;
	// 处理行数据
	if (isObject(row) && Object.keys(row).length !== 0) {
		model.value.id = row.id ?? null;
		model.value.name = row.name ?? '';
		model.value.sort = row.sort ?? null;
		model.value.iconPath = [{
			path: row.iconPath
		}] ?? [];
	}
	setTrue();
};

/* 表单参数初始化 */
const initParams = {
	id: null,
	name: "",
    sort: null,
    iconPath: [],
};
const model = ref(deepClone(initParams));

/* 表单实例 */
const formRef = ref();

/* 表单规则 */
const rules: FormRules = {
	name: {
      required: true,
      trigger: ['blur', 'change'],
      message: '请输入分类名称',
    },
	iconPath: {
      type: 'array', 
      required: true,
      trigger: ['blur', 'change'],
      message: '请选择图标',
    },
};

/* 清空表单 */
const formDataReset = () => {
	model.value = deepClone(initParams);
};

/* 关闭弹窗之后 */
const closeModal = () => {
	formDataReset();
};

/** 获取参数 */
const _getParams = () : {
    name: string;
    type: GoodsType;
    iconPath: string;
    sort: number;
    isShow: 0 | 1;
} => {
	const { name, iconPath, sort } = model.value;

	return {
		name, 
		iconPath: isArray(iconPath) ? iconPath[0]?.path : '',
		sort: sort ? sort : 0,
		type: GoodsCategoryType.INTEGRAL,
		isShow: 0
	}
};

/* 确认--保存 */
const { loading: isLoading, startLoading, endLoading } = useLoading();
const handleSave = (e: MouseEvent) => {
	e.preventDefault();
	formRef.value?.validate(async (errors: any) => {
		if (!errors) {
			try {
				startLoading();
				// 新增
				if (modalProps.value.type === 'add') {
					let _params = _getParams();
					const data = await addPointGoodsClassification(_params);
					createMessageSuccess('新建分类成功');
					// 更新的商品分类Id
				    emits('afterSuccess', data?.id, GoodsCategoryType.INTEGRAL);
				} 
				else {
					await updatePointGoodsClass({
						id: model.value.id,
						..._getParams()
					});
					createMessageSuccess('编辑分类成功');
					// 更新的商品分类Id
				    emits('afterSuccess', model.value.id, GoodsCategoryType.INTEGRAL);
				}
				// 刷新
				setFalse();
			} catch (error) {
				createMessageError(modalProps.value.type === 'add' ? `新建分类失败: ${error}` : `修改分类失败: ${error}`);
			} finally {
				endLoading();
			}
		}
	});
};

defineExpose({
	acceptParams,
});
</script>

<style scoped lang="less"></style>
