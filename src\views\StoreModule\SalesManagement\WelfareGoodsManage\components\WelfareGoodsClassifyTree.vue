<template>
  <div class="tree-wrapper">
    <NTree
      v-bind="$attrs"
      block-line
      :data="treeData"
      :cancelable="false"
      :default-expanded-keys="props.defaultExpandKeys"
      :node-props="nodeProps"
    />
    <!-- 右键菜单 -->
    <NDropdown
      trigger="manual"
      placement="bottom-start"
      :show="showDropdown"
      :options="dropdownOptions"
      :x="dropdownX"
      :y="dropdownY"
      @select="handleSelect"
      @clickoutside="handleClickoutside"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import type { TreeOption, DropdownOption } from 'naive-ui';

defineOptions({
    name: 'WelfareGoodsClassifyTree',
});

/** props */
const props = withDefaults(defineProps<{
    value: string;
    treeData: Array<TreeOption & { isMenu: boolean }>;
    menuOptions: Array<DropdownOption>;
    defaultExpandKeys: Array<string | number>;
}>(), {
    treeData: () => [],
    menuOptions: () => [],
    defaultExpandKeys: () => [],
});

/** emits */
const emits = defineEmits<{
    (e: "update:value", value: string): void;
    (e: "menuSelect", key: string | number, option: TreeOption): void;
}>();

/** 右键菜单项 */
const dropdownOptionsOrigin = ref<DropdownOption[]>([...props.menuOptions]);
const dropdownOptions = ref<DropdownOption[]>([]);
const showDropdown = ref(false);
/** 坐标 */
const dropdownX = ref(0);
const dropdownY = ref(0);
/** 当前点击分类 */
const currentClassification = ref<TreeOption | null>(null);

/** select 选中时触发的回调函数 */
const handleSelect = (key: string | number, option: DropdownOption) => {
    emits('menuSelect', key, currentClassification.value);
    showDropdown.value = false;
    currentClassification.value = null;
};

/** clickoutside 的时候触发的回调函数 */
const handleClickoutside = () => {
    showDropdown.value = false;
    currentClassification.value = null;
};

/** 节点绑定事件 */
const nodeProps = ({ option }: { option: TreeOption }) => {
    return {
        onContextmenu(e: MouseEvent): void {
            // 判断节点是否可右键菜单
            if (option.isMenu) {
                // 子分类过滤(新建子分类)
                if (option?.parentId) {
                    dropdownOptions.value = dropdownOptionsOrigin.value.filter(
                        (item) => item.key !== 'addChild'
                    );
                } else {
                    dropdownOptions.value = [...dropdownOptionsOrigin.value];
                }

                currentClassification.value = option;
                showDropdown.value = true;
                dropdownX.value = e.clientX;
                dropdownY.value = e.clientY;
                e.preventDefault();
            }
        }
    };
};
</script>

<style lang="less" scoped>
.tree-wrapper {
    padding: 0 12px;
}
</style>
