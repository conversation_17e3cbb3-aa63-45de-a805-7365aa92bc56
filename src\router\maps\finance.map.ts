import BlankLayout from '@/layout/BlankLayout.vue'
import { RoutesName } from "@/enums/routes";

export const Default = {
  [RoutesName.Finance]: {
    path: "/finance",
    component: BlankLayout,
    meta: {
      title: "财务",
    },
  },
  [RoutesName.FinanceMerchantNumberManagement]: {
    path: "merchant-number-management",
    component:() => import("@/views/StoreModule/Finance/MerchantManagement/MerchantManagement.vue"),
    meta: {
      title: "商户号管理",
      icon: 'merchant-number-management'
    }
  },
  // [RoutesName.FinanceUserRecharge]: {
  //   path: "FinanceUserRecharge",
  //   component:() => import("@/views/StoreModule/Finance/UserRecharge/index.vue"),
  //   meta: {
  //     title: "用户充值",
  //     icon: 'content-config'
  //   }
  // },
  [RoutesName.FinanceAccountingForRevenueDistribution]: {
    path: "accounting-revenue-distribution",
    component:() => import("@/views/StoreModule/Finance/AccountManagement/index.vue"),
    meta: {
      title: "分账管理",
      icon: 'accounting-revenue-distribution'
    }
  },
  [RoutesName.FinanceExpressDeliveryRecharge]: {
    path: "express-delivery-recharge",
    component:() => import("@/views/StoreModule/Finance/LogisticsRecharge/index.vue"),
    meta: {
      title: "物流充值",
      icon: 'express-delivery-recharge'
    }
  },
  [RoutesName.StoreCommissionRule]: {
    path: "store-commission-rule",
    component:() => import("@/views/StoreModule/Finance/StoreCommissionRule/index.vue"),
    meta: {
      title: "门店分佣规则",
      icon: 'store-commission-rule'
    }
  },
  [RoutesName.StoreCommissionDetail]: {
    path: "store-commission-detail",
    component:() => import("@/views/StoreModule/Finance/StoreCommissionDetail/index.vue"),
    meta: {
      title: "门店分佣明细",
      icon: 'store-commission-detail'
    }
  },
  [RoutesName.FinancePaymentReview]: {
    path: "payment-review",
    component:() => import("@/views/StoreModule/Finance/PaymentReview/index.vue"),
    meta: {
      title: "打款审核",
      icon: 'payment-review'
    }
  },
};
