import { loadRoutes, mergeRoutesConfig, getRoutesMapByConfig, getFirstChildrenRoute, transformAuthRouteToSearchMenus } from "./routerUtils";
import { routesMap } from "@/router/maps/index";
import { baseRoutesConfig } from "@/router/config/base.config";
import { clearStorage } from "@/utils/cache/storageCache";
import type { Router } from "vue-router";
import { useUserStoreWithoutSetup } from "@/stores/modules/user";
import { RoutesName } from "@/enums/routes";
import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
import { useMessages } from "@/hooks";
import { getImgUrlPrefix } from "@/utils/http/urlUtils";
import { isDoctorEnv, isProdEnv } from "./envUtils";
import { authTreeToRouteConfigAndOptAuthList } from "./auth/authUtils";
import type { MenuAuthDataResponseItem } from "./auth/type";
import { _syncRouteConfig } from "@/router/config/sync/sync";
import type { SystemRoleType, SystemStoreType, ManagementType,FirstLogin,EasyPasswords } from "@/enums/systemEnum";
import {doctorOnlineStatusChange, handleDoctorLogout, TencentIMLogout} from "@/views/DoctorEndModule/IM/utils/IMUtils";

export async function afterLogout() {
  const userStore = useUserStoreWithoutSetup();
  await handleDoctorLogout()
  const { destoryMessage } = useMessages();
  destoryMessage();
  userStore.$reset();
  clearStorage();
  if(isDoctorEnv()){
    location.href = `${location.origin}/#/doctor/login`;
  }
  else{
    location.href = `${location.origin}/#/login`;
  }
 
}

type AfterLoginProps = {
  token: string;
  userinfo: object;
  router: Router;
  treeResList: Array<MenuAuthDataResponseItem>,
  marketplaceType:SystemStoreType,
  visitDataType:ManagementType,
  isInitialPassword:FirstLogin,
  isSimplePassword:EasyPasswords
};

export async function afterLogin({  userinfo,token, router,treeResList,marketplaceType,visitDataType,isInitialPassword,isSimplePassword }: AfterLoginProps) {
  const userStore = useUserStoreWithoutSetup();
  const systemStore = useSystemStoreWithoutSetup()
  userStore.setToken(token);
  userStore.setUserInfo(userinfo);
  systemStore.setStystemGlobalConfig({marketplaceType,visitDataType,isInitialPassword,isSimplePassword});
  userStore.setUserInfo(userinfo);
  const {
    routeConfig: _routeConfig,
    optAuthList: _optAuthList,
  } = authTreeToRouteConfigAndOptAuthList(treeResList)
  userStore.setOptAuthList(_optAuthList);
  const _config = mergeRoutesConfig(_routeConfig, baseRoutesConfig);
  // const _config = _syncRouteConfig
  userStore.setRouteConfig(_config);
  const routes = getRoutesMapByConfig(_config, routesMap);
  loadRoutes(routes, router);
  systemStore.setImgPrefix(getImgUrlPrefix());
  // 将权限路由转换为搜索菜单
  systemStore.setSearchMenus(transformAuthRouteToSearchMenus(routes));
  const tenantRootRoute = routes.find(route=>route.name == RoutesName.Root && route.children && route.children.length)
  router.replace(getFirstChildrenRoute(tenantRootRoute.children));
}
