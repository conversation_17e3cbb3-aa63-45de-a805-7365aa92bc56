import { CustomerManagementAuth } from "@/enums/authKeys";
import { hasAuth } from "@/utils/auth/authUtils";


/** 编辑客户 */
export const hasEditCustomer = function(){
    return hasAuth(CustomerManagementAuth.customerManagementIndexEditcustomer.key);
}()
/** 新建标签 */
export const hasNewLabel = function(){
    return hasAuth(CustomerManagementAuth.customerManagementIndexNewlabel.key);
}()
/** 编辑标签 */
export const hasEditLabel = function(){
    return hasAuth(CustomerManagementAuth.customerManagementIndexEditlabel.key);
}()
/** 删除标签 */
export const hasDeletelabel = function(){
    return hasAuth(CustomerManagementAuth.customerManagementIndexDeletelabel.key);
}()
/** 新建虚拟号 */
export const hasVirtualAccount = function(){
    return hasAuth(CustomerManagementAuth.customerManagementAddVirtualAccount.key);
}()
/** 禁止评论/解除禁止评论 */
export const hasDisableComment = function(){
    return hasAuth(CustomerManagementAuth.customerManagementIndexEnableOrDisableComment.key);
}()
/** 增减积分 */
export const hasIncreaseAndDecrease = function(){
    return hasAuth(CustomerManagementAuth.customerManagementPointsIncreaseAndDecrease.key);
}()
/** 导出会员列表 */
export const hasExportAccountList = function(){
    return hasAuth(CustomerManagementAuth.customerManagementExportAccountList.key);
}()
/** 修改归属店员 */
export const hasChangeAccountOwner = function(){
    return hasAuth(CustomerManagementAuth.customerManagementChangeAccountOwner.key);
}()
/** 拉黑/取消拉黑 */
export const hasDisableOrEnableAccount = function(){
    return hasAuth(CustomerManagementAuth.customerManagementDisableOrEnableAccount.key);
}()

