<template>
  <JDrawer
    v-model:show="show"
    :title="title"
    :isGetLoading="isGetLoading"
    @after-leave="handleAfterLeave"
    @after-enter="handleAfterEnter"
  >
    <!-- 基本信息 -->
    <template #content>
      <NForm
        ref="formRef"
        :model="model"
        :rules="rules"
        label-placement="left"
        label-width="120"
        require-mark-placement="right-hanging"
        size="small"
        :style="{ width: '100%' }"
      >
        <NGrid :cols="12" :x-gap="12">
          <!-- 基本信息 -->
          <NGi :span="12">
            <StoreTitle title="基本信息" size="medium" style="margin-bottom: 12px;" />
          </NGi>
          <!-- 管理名称 -->
          <NFormItemGi :span="10" label="管理名称" path="manageName">
            <NInput
              v-model:value="model.manageName"
              placeholder="请输入管理名称"
              :maxlength="60"
              clearable
              style="width: 600px;"
            />
          </NFormItemGi>
          <!-- 前端名称 -->
          <NFormItemGi :span="10" label="前端名称" path="frontName">
            <div style="width: 100%; display: flex; flex-direction: column;">
              <NInput
                v-model:value="model.frontName"
                placeholder="请输入前端名称"
                :maxlength="60"
                clearable
                style="width: 600px;"
              />
              <span style="margin-top: 4px;font-size: 14px;">注：前端商城小程序显示使用</span>
            </div>
          </NFormItemGi>
          <!-- 所属分类 -->
          <NFormItemGi :span="10" label="所属分类" path="categoryId">
            <JCouponProduct
              :isImmediately="isEditMode"
              style="width: 600px;"
              placeholder="请选择分类"
              v-model:value="model.categoryId"
            />
          </NFormItemGi>
          <!-- 供应商 -->
          <NFormItemGi :span="10" label="供应商">
            <JSupplierSelector
              style="width: 600px;"
              v-model:value="model.supplierId"
              :extraId="isEditMode ? model.supplierId : ''"
            />
          </NFormItemGi>
          <!-- spu编码 -->
          <NFormItemGi :span="10" label="spu编码" path="spu编码">
            <NInput
              v-model:value="model.spu"
              placeholder="请录入编码"
              :maxlength="40"
              clearable
              style="width: 600px;"
               @update:value="newValue => model.spu = newValue.replace(/[^a-zA-Z0-9]/g, '')"
            />
          </NFormItemGi>
          <!-- 商品图片 -->
          <NGi :span="12">
            <NGrid :cols="24" :x-gap="12">
              <NFormItemGi :span="2" label="商品图片"></NFormItemGi>
              <NFormItemGi
                :span="6"
                label="首图"
                :path="model.productFirstImg.length || model.productMoreImg.length ? '' : 'productFirstImg'"
              >
                <UploadProductImg v-model:value="model.productFirstImg" accept="image/*" :fileListSize="1" />
              </NFormItemGi>
              <NFormItemGi
                :span="16"
                label="更多图片"
                :path="model.productFirstImg.length || model.productMoreImg.length ? '' : 'productMoreImg'"
              >
                <UploadProductImg
                  ref="uploadProductImgRef"
                  v-model:value="model.productMoreImg"
                  accept="image/*"
                  :fileListSize="8"
                  is-multiple
                />
              </NFormItemGi>
            </NGrid>
          </NGi>
          <!-- 每人兑换上限 -->
          <NFormItemGi :span="10" label="每人兑换上限" path="limitNum">
            <NInputNumber
              v-model:value="model.limitNum"
              placeholder="请输入购买上限"
              :min="0"
              :max="99999"
              :precision="0"
              :show-button="false"
            />
            <span style="margin-left: 12px;font-size: 14px;">注：0表示不设限</span>
          </NFormItemGi>
          <!-- 兑换场景 -->
          <NFormItemGi :span="10" label="兑换场景" path="saleScene">
            <NCheckboxGroup v-model:value="model.saleScene">
              <!-- 门店 -->
              <NFlex :size="2">
                <NCheckbox :value="WelfareExchangeSceneEnum.Storefront" style="margin-top: 2px;">
                  <NFlex align="center" :size="2">
                    <span>门店</span>
                    <HelpPopover helpEntry="门店" size="18" />
                  </NFlex>
                </NCheckbox>
                <!-- 门店下拉选择 -->
                <div class="store-select" v-if="model.saleScene.includes(WelfareExchangeSceneEnum.Storefront)">
                  <JStoreSelect
                    style="width: 460px;"
                    v-model:value="model.productStoreRelation.storeId"
                    multiple
                    :extraId="model.productStoreRelation.storeId || ''"
                    size="small"
                  />
                  <NFlex align="center" :size="2">
                    <span>商品在门店中可见范围</span>
                    <HelpPopover helpEntry="门店可见范围" size="18" />
                  </NFlex>
                  <!-- 数据可见范围 -->
                  <NCheckboxGroup v-model:value="model.productStoreRelation.visibleScope">
                    <NCheckbox :value="StoreVisibleRangeEnum.Normal" label="普通用户" size="small" />
                    <NCheckbox :value="StoreVisibleRangeEnum.StoreKeeper" label="店员&店长" size="small" />
                  </NCheckboxGroup>
                </div>
              </NFlex>
            </NCheckboxGroup>
          </NFormItemGi>
           <NFormItemGi :span="10" label="提货方式" required>
              <NFlex vertical>
              <NFlex>
                <NRadioGroup v-model:value="model.deliveryType" name="radiogroup">
                  <NRadio
                    v-for="option in [{ label: '到店自提', value: PickUpGoodsType.PICKUP }, { label: '快递到家', value: PickUpGoodsType.EXPRESSAGE }]"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </NRadio>
                </NRadioGroup>
                <div style="font-size: 14px;">（仅对百货门店模式生效，对小程序商城不生效）</div>
              </NFlex>
              <NRadioGroup v-model:value="model.verificationType" name="radiogroup" v-if="model.deliveryType === PickUpGoodsType.PICKUP" >
                  <NRadio
                    v-for="option in pickUpGoodsSubTypeOptions"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </NRadio>
                <HelpPopover helpEntry="自提子项" size="18" />

              </NRadioGroup>
            </NFlex>
          </NFormItemGi>
          <!-- 售价与库存 -->
          <NGi :span="12">
            <StoreTitle title="售价与库存" size="medium" style="margin-bottom: 12px;" />
          </NGi>
          <!-- 规格 -->
          <NFormItemGi :span="12" :show-label="false" path="couponProductSpecList">
            <WelfareCouponSpecifications
              v-model:value="model.couponProductSpecList"
              style="width: 98%;"
              :type="pattern"
            />
          </NFormItemGi>
          <!-- 图文描述 -->
          <NGi :span="12">
            <StoreTitle title="图文描述" size="medium" style="margin-bottom: 12px;" />
          </NGi>
          <!-- 描述 -->
          <NGi :span="12">
            <div style="height:520px; width: 98%; margin-bottom: 52px;">
              <div id="desc-richtext-container" ref="descRichTextDomRef" style="height:100%;width:100%;"></div>
            </div>
          </NGi>
        </NGrid>
      </NForm>
    </template>
    <!-- Footer -->
    <template #footer>
      <div class="footer-wrapper">
        <!-- 商品是否上 -->
        <JCheckbox v-model:checked="model.status" style="margin-left: 24px;">
          <span style="font-size: 16px;">上架（上架商品才会在门店显示，用户才能兑换）</span>
        </JCheckbox>
        <n-space>
          <n-button @click="show = false" class="store-button">取 消</n-button>
          <n-button type="primary" :loading="isLoading" @click="_save" class="store-button">保 存</n-button>
        </n-space>
      </div>
    </template>
  </JDrawer>
</template>

<script lang="ts" setup>
import { ref, computed, nextTick, watch, effectScope, onScopeDispose } from "vue";
import { createDummyId, deepClone } from "@/utils";
import type { FormRules, FormItemRule } from "naive-ui";
import { isArray, _debounce } from "@/utils";
import { addCouponProduct, updateCouponProduct, getCouponProductDetail, uploadRichTextResouce } from "@/services/api";
import { useMessages } from '@/hooks';
import Quill from 'quill';
import "@/assets/quill/quill.snow.css";
// import quillDropdownIcon from 'quill/assets/icons/dropdown.svg';
import JDrawer from "@/components/JDrawer/index.vue";
import UploadProductImg from "@/components/UploadProductImg/index.vue";
import { getOssUrlPrefix } from "@/utils/http/urlUtils";
import { createVideoElement } from "@/utils/quill/createVideoElement";
import type { GoodsType } from "@/enums";
import {
  GoodsCategoryType,
  PickUpGoodsType,
  WelfareExchangeSceneEnum,
  SalesSceneStoreGoodsTypeEnum,
  StoreVisibleRangeEnum,
  PickUpGoodsSubType
 } from "@/enums";
/** 相关组件 */
import WelfareCouponSpecifications from "./WelfareCouponSpecifications.vue";

interface AcceptParams {
  categoryId?: string | null;
  row: Partial<ApiStoreModule.Goods>;
  type: 'add' | 'edit';
  productTypes: GoodsType;
}
/** 自提子项 */
const pickUpGoodsSubTypeOptions = [
  {
    label: '下单后待核销',
    value: PickUpGoodsSubType.ORDER
  },
  {
    label: '下单自动核销',
    value: PickUpGoodsSubType.AUTOMATIC
  },
  {
    label: '下单门店到货后核销',
    value: PickUpGoodsSubType.STOREARRIVAL
  }
];
/**
 * 福利商品规格接口定义
 */
 interface WelfareCouponSpecification {
  id: string;                   // 唯一标识
  couponProductId?: string;      // 关联商品ID
  specName?: string;            // 规格名称
  availStock?: number;         // 可用库存
  lockedStocks?: number;        // 锁定库存
  limitPerPurchase?: number;    // 每单限购数量
  soldCount?: number;           // 已售数量
  skuCode?: string;             // SKU编码
  couponCateId?: string;        // 福利券分类ID
  exchangeCount?: number;       // 兑换所需数量
  purchasePrice?: number;       // 采购价格
}

defineOptions({
  name: "CreateWelfareGoods"
});

/** props */
const props = withDefaults(defineProps<{
  to?: string; // 弹窗位置
}>(), {
  to: '.table-wrapper',
});

/** emit */
const emit = defineEmits<{
  (e: 'refresh'): void;
}>();

const scope = effectScope();
const { createMessageSuccess, createMessageError } = useMessages();
const descRichTextDomRef = ref(null);
/** 是否编辑模式 */
const isEditMode = computed(() => pattern.value === 'edit');

/** 显隐 */
const show = ref(false);

/* 表单实例 */
const formRef = ref();

/** 多文件上传实例 */
const uploadProductImgRef = ref<InstanceType<typeof UploadProductImg> | null>(null);

/* 表单参数初始化 */
const initParams = {
  id: null, // 编辑时，商品id
  type: GoodsCategoryType.GENERAL, // 商品类别

  manageName: null, // 管理名称
  frontName: null, // 前端名称
  categoryId: null, // 所属分类
  supplierId: null, // 供应商
  productFirstImg: [], // 首图
  productMoreImg: [], // 更多图片

  limitNum: null, // 每订单上限
  saleScene: [], // 兑换场景
  description: null, // 商品描述
  status: null, // 上下架状态
  deliveryType: PickUpGoodsType.PICKUP,
  verificationType: PickUpGoodsSubType.ORDER,
  spu: null, // spu编码
  couponProductSpecList: [{
    id: createDummyId(), // 标识
    couponProductId: undefined, // 商品id
    specName: null, // 规格名称
    availStock: null, // 可用库存
    lockedStocks: null, // 冻结库存
    limitPerPurchase: null, // 每订单上限
    soldCount: null, // 初始已兑（前端展示）
    skuCode: null, // 商品编码
    couponCateId: null, // 福利券兑换分类ID
    exchangeCount: null, // 福利券兑换数量
    purchasePrice: null, //采购价
  }], // 售价与库存
  productStoreRelation: {
    storeId: null, // 门店id
    productId: null, // 商品id
    visibleScope: [], // 可见范围
    productType: SalesSceneStoreGoodsTypeEnum.Welfare, // 商品类型
  }, // 可见门店
};
const model = ref(deepClone(initParams));
let quill: Quill = null;

/* 接收父组件传过来的参数 */
const pattern = ref<'add' | 'edit'>('add');
const acceptParams = (params: AcceptParams) => {
  pattern.value = params.type;
  model.value.categoryId = params.categoryId ?? null;

  if (params.type === 'edit' && params.row?.id) {
    model.value.id = params.row.id;
  }

  show.value = true;
};

/** 标题 */
const title = computed(() => {
  const titleMap: Record<'add' | 'edit', string> = {
    add: '新建福利商品',
    edit: '编辑福利商品',
  };
  return titleMap[pattern.value];
});

/* 表单规则 */
const rules: FormRules =
  {
  manageName: {
    type: "string",
    required: true,
    trigger: ["blur", "input"],
    message: "请输入管理名称",
  },
  frontName: {
    type: "string",
    required: true,
    trigger: ["blur", "input"],
    message: "请输入前端名称",
  },
  limitNum: {
    type: "number",
    required: true,
    trigger: ["blur", "input"],
    message: "请输入每人兑换上限",
  },
  categoryId: {
    type: "string",
    required: true,
    trigger: ["blur", "input"],
    message: "请选择所属分类",
  },
  productFirstImg: {
    type: "array",
    required: true,
    trigger: ["blur", "input"],
    message: "请选择商品图片",
  },
  productMoreImg: {
    type: "array",
    required: true,
    trigger: ["blur", "input"],
    message: "请选择商品图片",
  },
  couponProductSpecList: {
    type: "array",
    required: true,
    validator: (rule: FormItemRule, value: Array<WelfareCouponSpecification>) => {
      if (!value || value.length === 0) {
        return false;
      }

      return value.every(spec => {
        return (
          spec.specName &&
          spec.availStock !== undefined && spec.availStock !== null && spec.availStock >= 0 &&
          spec.purchasePrice !== undefined && spec.purchasePrice !== null && spec.purchasePrice >= 0 &&
          spec.limitPerPurchase !== undefined && spec.limitPerPurchase !== null && spec.limitPerPurchase > 0 &&
          spec.soldCount !== undefined && spec.soldCount !== null && spec.soldCount >= 0 &&
          spec.couponCateId &&
          spec.exchangeCount !== undefined && spec.exchangeCount !== null && spec.exchangeCount > 0
        );
      });
    },
    trigger: ['blur', 'change'],
    renderMessage: () => {
      return `请完善所有商品规格信息，包括：规格名称、可用库存、采购价格、每单限购、初始已兑、福利券种类、兑换所需数量`;
    }
  },
  saleScene: {
    type: "array",
    required: true,
    validator: (rule: FormItemRule, value) => {
      // 校验至少选择一个
      if (!value || value.length === 0) {
        return false;
      }
      return true;
    },
    trigger: ['change'],
    message: "请选择兑换场景，至少选择一个",
  },
 };

/** 关闭抽屉回调 */
const handleAfterLeave = () => {
  // 初始化参数
  model.value = deepClone(initParams);
};

/** 抽屉出现后的回调 */
const isGetLoading = ref(false);
const handleAfterEnter = async () => {
  try {
    if (show.value && (pattern.value === 'edit')) {
      isGetLoading.value = true;
      const resp = await getCouponProductDetail(model.value.id);
      // console.log("查询到商品信息", resp);
      // 解构
      const {
        id,
        categoryId,
        frontName,
        limitNum,
        manageName,
        saleScene,
        status,
        supplierId,
        couponProductImgDTOList,
        couponProductSpecList,
        productStoreRelationDTOList,
        supplierDTO,
        description,
        deliveryType,
        verificationType,
        spu
      } = resp;

      // 过滤图片与视频
      const productFirstImg = couponProductImgDTOList?.filter(img => img.isFirst === 1 && img.type === 0);
      const productMoreImg = couponProductImgDTOList?.filter(img => img.isFirst !== 1 && img.type === 0);

      Object.assign(model.value, {
        id,
        categoryId,
        frontName,
        limitNum,
        manageName,
        saleScene: [saleScene],
        status: status ? true : false,
        supplierId,
        productFirstImg,
        productMoreImg,
        deliveryType,
        verificationType,
        spu,
        /** 门店信息 */
        productStoreRelation: {
          storeId: isArray(productStoreRelationDTOList) ? productStoreRelationDTOList.map(({ storeId }) => storeId)  : null,
          visibleScope: isArray(productStoreRelationDTOList) ? getVisibleScope(productStoreRelationDTOList[0]?.visibleScope) : []
        },
        couponProductSpecList: isArray(couponProductSpecList) ? couponProductSpecList.map(({ purchasePrice, ...rest }) => ({
          ...rest,
          purchasePrice: purchasePrice / 100
        })) : [
          {
            id: createDummyId(),
            couponProductId: undefined,
            specName: null,
            availStock: null,
            lockedStocks: null,
            limitPerPurchase: null,
            soldCount: null,
            skuCode: null,
            couponCateId: null,
            exchangeCount: null,
            purchasePrice: null,
          }
        ],
      });
      quill.root.innerHTML = description ?? '';
    }
  } catch (error) {
    createMessageError('查询福利商品失败: ' + error);
  } finally {
    isGetLoading.value = false;
  }
};

/**
 * @description 编辑时，门店可见范围处理
 */
 function getVisibleScope(scope: StoreVisibleRangeEnum): StoreVisibleRangeEnum[] {
  const visibleMap: Record<StoreVisibleRangeEnum, StoreVisibleRangeEnum[]> = {
    [StoreVisibleRangeEnum.All]: [StoreVisibleRangeEnum.Normal, StoreVisibleRangeEnum.StoreKeeper],
    [StoreVisibleRangeEnum.Normal]: [StoreVisibleRangeEnum.Normal],
    [StoreVisibleRangeEnum.StoreKeeper]: [StoreVisibleRangeEnum.StoreKeeper],
    [StoreVisibleRangeEnum.NotVisible]: [],
  };

  return visibleMap[scope] ?? [];
}

/** 获取新增普通商品参数 */
const getAddParams = () => {
  const {
    manageName,
    frontName,
    categoryId,
    supplierId,
    limitNum,
    saleScene,
    status,
    productFirstImg,
    productMoreImg,
    couponProductSpecList,
    deliveryType,
    verificationType,
    spu,
    productStoreRelation: { storeId, visibleScope, productType },
  } = model.value;

  // 商品图片
  const imgList = deepClone(productMoreImg).map(item => ({ ...item, type: 0 }));
  if (productFirstImg.length > 0) {
    imgList.unshift({ ...productFirstImg[0], type: 0 });
  }

  return {
    manageName,
    frontName,
    categoryId,
    supplierId,
    limitNum,
    saleScene: saleScene.includes(WelfareExchangeSceneEnum.Storefront) ? WelfareExchangeSceneEnum.Storefront : undefined,
    status: status ? 1 : 0,
    couponProductImgVOList: imgList,
    deliveryType,
    verificationType,
    spu,
    productStoreRelationVOList: saleScene.includes(WelfareExchangeSceneEnum.Storefront) ? storeId.map(item => ({
      storeId: item,
      visibleScope: determineVisibleScope(visibleScope),
      productType,
    })) : undefined,
    /** 福利商品规格 */
    couponProductSpecList: couponProductSpecList.map(item => {
      const { id, couponProductId, purchasePrice, ...rest } = item;
      // 新增规格
      return {
        ...rest,
        purchasePrice: parseFloat((purchasePrice * 100).toFixed(2)), // 分
      }
    }),
    description: quill.root.innerHTML,
  }
};

/**
 * @description 判断门店可见范围
 */
function determineVisibleScope(visibleScope: StoreVisibleRangeEnum[]): StoreVisibleRangeEnum {
  if (visibleScope.length === 0) {
    return StoreVisibleRangeEnum.NotVisible;
  }

  const hasBoth = [StoreVisibleRangeEnum.Normal, StoreVisibleRangeEnum.StoreKeeper]
    .every(scope => visibleScope.includes(scope));

  return hasBoth
    ? StoreVisibleRangeEnum.All
    : visibleScope[0];
};

/** 获取编辑普通商品参数 */
const getEditParams = () => {
  const {
    id, // 商品Id
    manageName,
    frontName,
    categoryId,
    supplierId,
    limitNum,
    saleScene,
    status,
    productFirstImg,
    productMoreImg,
    deliveryType,
    verificationType,
    spu,
    couponProductSpecList,
    productStoreRelation: { storeId, visibleScope, productType },
  } = model.value;

  // 商品图片
  const imgList = productMoreImg.map(item => ({ ...item, productId: id, isFirst: 0, type: 0 }));
  if (productFirstImg.length > 0) {
    imgList.unshift({ ...productFirstImg[0], productId: id, isFirst: 1, type: 0 });
  } else if (imgList.length > 0) {
    // 首图为空，设置更多图片第一张为首图
    imgList[0].isFirst = 1;
  }

  return {
    id, // 商品Id
    manageName,
    frontName,
    categoryId,
    supplierId,
    limitNum,
    saleScene: saleScene.includes(WelfareExchangeSceneEnum.Storefront) ? WelfareExchangeSceneEnum.Storefront : undefined,
    status: status ? 1 : 0,
    couponProductImgVOList: imgList,
    deliveryType,
    verificationType,
    spu,
    productStoreRelationVOList: saleScene.includes(WelfareExchangeSceneEnum.Storefront) ? storeId.map(item => ({
      productId: id,
      storeId: item,
      visibleScope: determineVisibleScope(visibleScope),
      productType,
    })) : undefined, // 可见门店
    /** 福利商品规格 */
    couponProductSpecList: couponProductSpecList.map(item => {
      const { id, couponProductId, purchasePrice, ...rest } = item;
      if (couponProductId) {
        return {
          ...rest,
          id,
          couponProductId,
          purchasePrice: parseFloat((purchasePrice * 100).toFixed(2)), // 分
        }
      }
      // 编辑新增规格
      return {
        ...rest,
        couponProductId,
        purchasePrice: parseFloat((purchasePrice * 100).toFixed(2)), // 分
      }
    }),
    description: quill.root.innerHTML,
  }
};

/* 确认--保存 */
const isLoading = ref(false);
const _save = async (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      try {
        isLoading.value = true;
        const { saleScene, productStoreRelation: { storeId } } = model.value;
        // 是否是门店商品
        const isStoreProduct = saleScene.includes(WelfareExchangeSceneEnum.Storefront);

        /** 是否正在进行文件上传 */
        if (uploadProductImgRef.value?.isUploadLoading) {
          createMessageError('文件正在上传中，请稍等！');
          return;
        }
        if (isStoreProduct && (storeId === null || !isArray(storeId) || !storeId.length)) {
          createMessageError('请选择门店！');
          return;
        }

        /** 新增 */
        if (!isEditMode.value) {
          console.log("普通福利商品新增参数", getAddParams());
          await addCouponProduct(getAddParams());
        }
        /** 编辑 */
        if (isEditMode.value) {
          console.log("普通福利商品编辑参数", getEditParams());
          await updateCouponProduct(getEditParams());
        }
        createMessageSuccess(!isEditMode.value ? '新增福利商品成功' : '编辑福利商品成功');
        // 关闭并刷新
        show.value = false;
        emit('refresh');
      } catch (error) {
        createMessageError(!isEditMode.value ? `新增福利商品失败: ${error}` : `编辑福利商品失败: ${error}`);
      } finally {
        isLoading.value = false;
      }
    }
  });
};

/** 关闭 */
const close = () => {
  show.value = false;
}

/** 在作用域内运行监听器 */
scope.run(() => {

  /** 监听 */
  watch(show, (newVal) => {
    if (newVal) {
      nextTick(() => {
        createVideoElement()
        quill = new Quill(descRichTextDomRef.value, {
          theme: 'snow', // 使用 snow 主题
          modules: {
            toolbar: {
              container: [
                // Include image button in the toolbar
                [{ 'size': ['small', false, 'large', 'huge'] }],  // 自定义字体大小
                ['bold', 'italic', 'underline', 'strike'],        // 加粗、斜体、下划线和删除线
                [{ 'color': [] }, { 'background': [] }],          // 字体颜色和背景颜色
                [{ 'header': '1' }, { 'header': '2' }, 'blockquote',],  // 标题、引用和代码块
                [{ 'list': 'ordered' }, { 'list': 'bullet' }, { 'indent': '-1' }, { 'indent': '+1' }], // 列表和缩进
                [{ 'direction': 'rtl' }, { 'align': [] }],        // 文本方向和对齐方式
                ['image', 'video'],            // 链接、图片、视频和公式
              ],
              // Handle image uploads
              handlers: {
                image: function () {
                  const input = document.createElement('input');
                  input.setAttribute('type', 'file');
                  input.setAttribute('accept', 'image/*');
                  input.setAttribute('multiple', 'true');
                  input.click();

                  input.onchange = () => {
                    const file = input.files[0];
                    if (file) {
                      const _formData = new FormData()
                      for (let key in input.files) {
                        if (input.files.hasOwnProperty(key)) {
                          console.log(key, input.files[key]);
                          _formData.append('files', input.files[key])
                        }
                      }
                      uploadRichTextResouce(_formData).then(res => {
                        res.forEach(e=>{
                          const range = quill.getSelection();
                          quill.insertEmbed(range.index, 'image', `${getOssUrlPrefix()}/${e}`);
                        })
                      }).catch((error) => {
                        console.error('Image upload failed:', error);
                      });
                    }
                  };
                },
                video: function () {
                  const input = document.createElement('input');
                  input.setAttribute('type', 'file');
                  input.setAttribute('accept', 'video/mp4');
                  input.click();

                  input.onchange = () => {
                    const file = input.files[0];
                    if (file) {
                      console.log(file);
                      const _formData = new FormData()
                      _formData.append('files', file)
                      uploadRichTextResouce(_formData).then(res => {
                        const videoUrl = res[0]
                        const range = quill.getSelection();
                        quill.insertEmbed(range.index, 'video', `${getOssUrlPrefix()}/${videoUrl}`);
                      }).catch((error) => {
                        console.error('Video upload failed:', error);
                      });
                    }
                  };
                }
              }
            }
          }
        });
      })
    }
  });
});

/** 作用域销毁时清理 */
onScopeDispose(() => {
  scope.stop();
});

defineExpose({
  acceptParams,
  close
});
</script>

<style lang="less" scoped>
@import "@/styles/defaultVar.less";

:deep(.n-scrollbar-rail) {
  bottom: 8px !important;
}

.footer-wrapper {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.distributors :deep(.n-form-item-label){
  width: 164px !important;
}
.store-select {
  display: flex;
  flex-direction: column;
  gap: 8px;
  span {
    font-size: 14px;
  }
}
</style>
