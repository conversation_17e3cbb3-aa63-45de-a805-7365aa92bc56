<template>
 <div class="inner-page-height">
    <FormLayout
        :isLoading="isLoading"
        :tableData="tableData"
        :tableColumns="tableColumns"
        :pagination="paginationRef"
        :isTableSelection="false"
        @paginationChange="paginationChange"
        :isDisplayIndex="false"
        id="distribution"
      >
        <!-- 表单 -->
        <template #searchForm>
            <n-form
              ref="formRef"
              label-placement="left"
              label-width="auto"
              :show-feedback="false"
              require-mark-placement="right-hanging"
              size="small"
              :style="{ width: '100%' }"
            >
             <n-grid cols="4 m:12 l:18 xl:24" :x-gap="32" responsive="screen">
                <!-- 分销员信息 -->
                <n-gi :span="6">
                    <n-form-item label="客户关键字" path="range">
                      <n-input-group>
                        <n-select
                          v-model:value="formValue.searchType"
                          placeholder="请选择"
                          :options="distributorInformationOptions"
                          style="width: 135px;"
                        />
                        <JSearchInput
                          v-model:value="formValue.searchValue"
                          placeholder="请输入关键字"
                          @search="formSearch"
                          :width="240"
                        />
                      </n-input-group>
                    </n-form-item>
                </n-gi>
                <!-- 状态 -->
                <n-gi :span="4">
                    <n-form-item label="状态">
                        <n-select 
                         v-model:value="formValue.status" 
                         :options="DistributorStatusOptions" 
                         placeholder='请选择状态'
                         style="width: 170px;" 
                         clearable
                        />
                    </n-form-item>
                </n-gi>

                <!-- 成为分销员时间 -->
                <n-gi :span="6">
                    <n-form-item label="成为分销员时间">
                        <j-date-range-picker 
                        style="flex: 1;" 
                        v-model:value="formValue.creationTime"  
                        type="datetimerange" format="yyyy-MM-dd" 
                        :default-time="['00:00:00', '23:59:59']" 
                        clearable />
                    </n-form-item>
                </n-gi>
             </n-grid>
            </n-form>
        </template>
        <!-- 操作项 -->
        <template #tableHeaderBtn>
          <n-button @click="refresh" class="store-button" :loading="isLoading">刷 新</n-button>
          <n-button v-if="hasStructreMemberManagementIndexAddAuth" type="info" @click="handleDistributor(DistributorType.AddDistributor)">添加分销员</n-button>
        </template>
      </FormLayout>
      <DistributorModal ref="distributorModalShow" />
      <personalSizeModal ref="personalSizeModalShow" />
      <distributeProductDetails ref="distributeProductDetailsShow"/>
 </div>
</template>
<script setup lang="tsx" name="DistributorManagement">
import { ref, watch, onMounted } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { DistributorStatusOptions,DistributorStatusLabels } from "@/constants";
import DistributorModal from './components/distributorModal.vue'
import { DistributorType } from "@/enums";
import JImage from "@/components/JImage/index.vue";
import { distributorPage } from '@/services/api';
import moment from "moment";
import {hasStructreMemberManagementIndexAddAuth,hasStructreMemberManagementIndexEnableAuth,hasStructreMemberManagementIndexDeleteAuth,
  hasStructreMemberManagementIndexProductAuth,hasStructreMemberManagementIndexPersonalCodeAuth,hasStructreMemberManagementIndexBelongAuth} from '../authList'
import personalSizeModal from './components/personalSizeModal.vue'
import distributeProductDetails from './components/distributeProductDetails.vue'

/** 组件挂载时 */
onMounted(() => {
  tableSearch()
});

/** 搜索 */
const formSearch = () =>{
  tableSearch()
}

/** 表格刷新 */
function refresh(){
  tableSearch();
}

/** 获取参数 */
const getParams = () => { 
  const { alias, status, creationTime, searchType, searchValue} = formValue.value;
  const startTime =  creationTime ? moment(creationTime[0]).format(`YYYY-MM-DD HH:mm:ss`) : null
  const endTime = creationTime ? moment(creationTime[1]).format(`YYYY-MM-DD HH:mm:ss`) : null
  return {
    alias,
    status,
    startTime,
    endTime,
    searchType,
    searchValue
  };
};

/* 表格搜索 */
const tableSearch = () => {
  pageTableData(getParams(), paginationRef.value);
};

const formValue = ref({
  alias:undefined,
  status:undefined,
  creationTime:null,
  searchType:'nickname',
  searchValue:'',
  distributorVlue:null
})

/** 表格方法Hook */
const {
  isLoading,
  tableData,
  pageTableData,
  paginationRef,
  paginationChange,
} = useTableDefault({
  pageDataRequest: distributorPage,
});

/* 表格列表项 */
const tableColumns = ref([
    {
        title: "头像",
        key: "img",
        width: 100,
        align: "left",
        render: rowData => {
          return <JImage imgPath={rowData.img} />;
      },
    },
    {
        title: "分销员昵称/ID",
        key: "id",
        width: 180,
        align: "left",
        render: (row) => {
          let title = `${row.nickname ?? ''}`;
          return <table-tooltip row={row} nameKey="name" title={title} idKey="id" />;
      }
    },
    {
        title: "手机号",
        key: "mobile",
        width: 150,
        align: "left"
    },
    {
        title: "归属组织",
        key: "structureName",
        width: 150,
        align: "left"
    },
    {
        title: "绑定客户数",
        key: "csNum",
        width: 150,
        align: "left"
    },
    {
        title: "状态",
        key: "status",
        width: 150,
        align: "left",
        render: (row) => {
          return DistributorStatusLabels[row.status] ? DistributorStatusLabels[row.status] : '-'
        }
    },
    {
        title: "成为分销员时间",
        key: "createTime",
        width: 150,
        align: "left"
    },
    {
        title: "操作",
        key: "action",
        width: 100,
        fixed: "right",
        align: "left",
        render: (row) => {
            return (
                <n-space>
                    {
                      (DistributorStatusLabels[row.status] == '正常' && hasStructreMemberManagementIndexEnableAuth )? 
                      <n-button
                        text
                        type="primary"
                        onClick={() => {handleDistributor(DistributorType.FreezeDistributor,row)}}
                      >
                        冻结
                      </n-button> : null
                    }
                    {(DistributorStatusLabels[row.status] == '已冻结'  && hasStructreMemberManagementIndexEnableAuth)? 
                    <n-button
                        text
                        type="primary"
                        onClick={() => {handleDistributor(DistributorType.ThawDistributor,row)}}
                    >
                        解冻
                    </n-button> : null
                    }
                    {
                      hasStructreMemberManagementIndexDeleteAuth ? 
                      <n-button
                        text
                        type="error"
                        onClick={() => {handleDistributor(DistributorType.DeleteDistributor,row)}}
                    >
                        删除
                      </n-button> : null
                    }
                    {
                      hasStructreMemberManagementIndexBelongAuth ? <n-button
                        text
                        type="primary"
                        onClick={() => {handleDistributor(DistributorType.AttributionOrganizationSettings,row)}}
                    >
                        归属组织
                    </n-button> : null
                    }
                    {
                      hasStructreMemberManagementIndexProductAuth ? <n-button
                        text
                        type="primary"
                        onClick={() => {handleDistributionGoods(row)}}
                    >
                        分销商品
                      </n-button> : null
                    }
                    {
                      hasStructreMemberManagementIndexPersonalCodeAuth ? <n-button
                        text
                        type="primary"
                        onClick={() => {hanlePersonalSize(row)}}
                    >
                        个人码
                      </n-button> : null
                    }
                </n-space>
            )
        }
    },
]);

/** 分销员信息类型 */
const distributorInformationOptions = [
  {
    label: "分销员昵称",
    value: "nickname",
  },
  {
    label: "分销员id",
    value: "id",
  },
  {
    label: "手机号",
    value: "mobile",
  },
  {
    label: "组织名称",
    value: "structureName",
  }
];

/** 添加、冻结、解冻、删除 分销员 */
const distributorModalShow = ref()
const handleDistributor = (type,row) =>{
  let params = {
     type,
     row,
     refreshTable: refresh,
    }
    distributorModalShow.value.acceptParams(params)
}

/** 分销商品 */
const distributeProductDetailsShow = ref()
const handleDistributionGoods = (row) =>{
  distributeProductDetailsShow.value.acceptParams({ distributorId:row.id,distributeProductRange:row.distributeProductRange,refreshTable: refresh, })
}

/** 个人码 */
const personalSizeModalShow = ref()
const hanlePersonalSize = (row) =>{
  personalSizeModalShow.value.showModal(row.id)
}

/** 监听 */
watch(() => 
[formValue.value.status,
 formValue.value.creationTime
]
, (newVal) => {
  if (newVal) {
    tableSearch();
  }
});

</script>

<style scoped lang="less"></style>