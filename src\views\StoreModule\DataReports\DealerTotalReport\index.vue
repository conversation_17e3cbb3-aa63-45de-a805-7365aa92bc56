<template>
  <div class="dealer-wrapper">
    <!--介绍 -->
    <n-card 
      title="社群经销商销量统计" 
      size="small" 
      class="header" 
      :bordered="false"
    >
      按时间段统计每个经销商的订单情况。
    </n-card>
    <!-- 表格 -->
    <div class="dealer-table">
      <FormLayout
        :isLoading="isLoading"
        :tableData="tableData"
        :tableColumns="tableColumns"
        :isNeedCollapse="false"
        :pagination="paginationRef"
        @paginationChange="paginationChange"
        :table-summary="summaryRefs"
        @tableSorterChange="tableSorterChange"
        :isTableSelection="false"
        table-row-key="_dummyId"
      >
        <template #searchForm>
          <!-- 表单 -->
          <n-form
            ref="formRef"
            :model="model"
            :show-feedback="false"
            label-placement="left"
            label-width="auto"
            require-mark-placement="right-hanging"
            size="small"
            :style="{ width: '100%' }"
          >
            <n-form-item label="统计区间">
              <JDateRangePicker
                v-model:value="model.rangeTime"
                :limitTimeRang="model.limitTimeRang"
                style="flex: 1;"
                type="daterange"
                format="yyyy-MM-dd"
                :clearable="false"
                :maxDays="366"
              />
            </n-form-item>
            <!-- 昵称 -->
            <n-form-item label="昵称">
              <JSearchInput
                v-model:value="model.dealerName"
                placeholder="请输入昵称"
                @search="tableSearch"
                :isPopover="false"
                :is-show-search-icon="false"
              />
            </n-form-item>
          </n-form>
        </template>
        <!-- 操作项 -->
        <template #tableHeaderBtn>
          <n-button @click="refresh" class="store-button">刷 新</n-button>
          <n-button v-if="hasDealerExportAuth" type="primary" :loading="exportLoading" @click="handeReport" :disabled="!tableData.length">
            导出查询到的数据
          </n-button>
        </template>
      </FormLayout>
    </div>
  </div>
</template>

<script lang="tsx" setup name="DealerTotalReport">
import { ref, watch, onMounted } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import dayjs from "dayjs";
import { useMessages, useLoading } from "@/hooks";
import { SortType, SortTypeValue } from "../types";
import { dealerStatDataExport, getDealerStatData } from "@/services/api";
import { hasDealerExportAuth } from "../authList";

const { createMessageError, createMessageSuccess } = useMessages();

/* 表格方法Hook */
const {
isLoading,
tableData,
paginationRef,
pageTableData,
refreshTableData,
paginationChange,
} = useTableDefault({
  useDummyId: true,
  pageDataRequest: getDealerStatData,
});

/* 初始化参数 */
const initParams = {
  rangeTime: [
    dayjs().subtract(29, 'day').valueOf(),
    dayjs().valueOf()
  ],
  limitTimeRang: [dayjs().valueOf(), 0],
  dealerName: '',
  sortType: SortType.TOTALORDER,
  isAsc: false,
};
const model = ref({ ...initParams });

/* 表格项 */
const tableColumns = [
{
  title: "昵称",
  key: "dealerName",
  summaryTitle: "",
  align: "left",
  fixed: "left",
  width: 80,
},
{
  title: "总订单数",
  key: "totalOrder",
  sorter: true,
  isSortDefault: true,
  summaryTitle: "总订单数",
  align: "left",
  width: 120,
},
{
  title: "已支付单数",
  key: "paidOrder",
  sorter: true,
  isSortDefault: true,
  summaryTitle: "总已支付单数",
  align: "left",
  width: 120,
},
{
  title: "待支付单数",
  key: "unpaidOrder",
  sorter: true,
  isSortDefault: true,
  summaryTitle: "总待支付单数",
  align: "left",
  width: 120,
},
{
  title: "在线支付单数",
  key: "onlinePaymentSingular",
  align: "left",
  width: 120,
},
{
  title: "定金支付单数",
  key: "downPaymentSingular",
  align: "left",
  width: 120,
},
{
  title: "物流代收单数",
  key: "logisticsCollectionSingular",
  align: "left",
  width: 120,
},
{
  title: "已支付订单总额（元）",
  key: "totalAmount",
  sorter: true,
  isSortDefault: true,
  summaryTitle: "总已支付订单总额（元）",
  align: "left",
  width: 160,
  render: row => {
    return (
      <span>{row.totalAmount ? (row.totalAmount / 100).toFixed(2) : '0.00'}</span>
    );
  }
},
{
  title: "线上实收款（元）",
  key: "onlinePaymentAmount",
  sorter: true,
  isSortDefault: true,
  summaryTitle: "总线上实收款（元）",
  align: "left",
  width: 140,
  render: row => {
    return (
      <span>{row.onlinePaymentAmount ? (row.onlinePaymentAmount / 100).toFixed(2) : '0.00'}</span>
    );
  }
},
];

/** 排序 */
const tableSorterChange = (info: { sort: string, sortAsc: "ascend" | "descend" }) => {
model.value.sortType = SortTypeValue[info.sort];
if (info.sortAsc === 'ascend') {
  model.value.isAsc = true;
} else {
  model.value.isAsc = false;
}
tableSearch();
};

/** 导出 */
const { loading: exportLoading, startLoading, endLoading } = useLoading();
async function handeReport() {
try {
  startLoading();
  await dealerStatDataExport({
    data: { ...getParams() },
    pageVO:{
      current: paginationRef.value.current,
      size: paginationRef.value.pageSize
    }
  });
  createMessageSuccess('导出成功！');
} catch (error) {
  createMessageError('导出失败：' + error);
} finally {
  endLoading();
}
}

/** 当前页数据总计 */
const summaryRefs = ref(null);
watch(tableData, (newVal) => {
summaryRefs.value = summaryColumn(newVal);
});
const summaryColumn = (rowData) => {
const _sum = {
  "totalOrder":0,
  "paidOrder": 0,
  "unpaidOrder": 0,
  "onlinePaymentSingular": 0,
  "downPaymentSingular": 0,
  "logisticsCollectionSingular": 0,
  "totalAmount": 0,
  "onlinePaymentAmount": 0,
};
rowData.forEach(row=>{
  for(let key in row){
    if(Object.keys(_sum).includes(key)){
      _sum[key] = _sum[key] + row[key]
    }
  }
});
_sum['totalAmount'] = Number((_sum['totalAmount'] / 100).toFixed(2));
_sum['onlinePaymentAmount'] = Number((_sum['onlinePaymentAmount'] / 100).toFixed(2));
return {
  ..._sum,
}
};

/** 获取参数 */
function getParams() {
const { rangeTime, sortType, isAsc, dealerName } = model.value;
const [startTime, endTime] = rangeTime;
return {
  sortType,
  isAsc,
  dealerName,
  startTime: dayjs(startTime).format('YYYY-MM-DD 00:00:00'),
  endTime: dayjs(endTime).format('YYYY-MM-DD 23:59:59'),
}
}

/** 表格搜索 */
function tableSearch() {
pageTableData(getParams(), paginationRef.value);
}

/** 表格刷新 */
function refresh(){
tableSearch();
};

/* 组件挂载 */
onMounted(() => {
tableSearch();
});

/** 监听 */
watch(() => [model.value.rangeTime], (newVal) => {
if (newVal) {
  tableSearch();
}
});
</script>

<style lang="less" scoped>
@import "@/styles/default.less";
.dealer-wrapper {
  width: 100%;
  height: 100%;
  .header {
    height: 86px;
    border-bottom: 1px solid @default-border-color;
  }
  .dealer-table {
    height: calc(100% - 86px);
  }
}
</style>
