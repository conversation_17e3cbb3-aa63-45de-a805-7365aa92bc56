import { defHttp } from "@/services";

/** 下拉选项Api */
export const enum DropDownListApi {
  productPresOption = "/productPresOption/page",
  // 剂型
  dosageForm = "/product/manage/get/dosageForm",
  // 平台角色
  list = "/authRole/list",
  // 供应商
  supplier = "/supplier/pageSupplier",
  /** 供应商无权限校验 */
  supplierNoAuth = "supplier/pageSupplier/noAuth",
  // 门店
  store = "/storeEntity/page",
  // 福利券分类
  couponType = "/couponCategory/list",
}

interface GetDoctorEntityPageRes {
  records: ApiStoreModule.DoctorEntity[];
  total: string;
  size: string;
  current: string;
}
/** 处方病症选项分页 */
export function getProductPresOptionPage(params: {
  data: {
    productId: string;
    option: string;
  };
  pageVO: {
    current: number;
    size: number;
  };
}) {
  return defHttp.post<GetDoctorEntityPageRes>({
    url: DropDownListApi.productPresOption,
    params,
  });
}

/** 获取药品剂型 */
export function getDosageForm(params) {
  return defHttp.post({
    url: DropDownListApi.dosageForm,
    params,
  });
}

/** 平台角色 */
export function getPlatformUser(_params) {
  return defHttp.post({
    url: DropDownListApi.list,
    params: {
      data: _params,
    },
  });
}

/**
 * @description 供应商
 */
export function getSupplier(_params) {
  return defHttp.post({
    url: DropDownListApi.supplier,
    params: _params,
  });
}

/**
 * @description 供应商无权限校验
 */
export function getSupplierNoAuth(_params) {
  return defHttp.post({
    url: DropDownListApi.supplierNoAuth,
    params: _params,
  });
}

/**
 * @description 福利券分类 list
 */
export function getCouponTypeList(_params) {
  return defHttp.post({
    url: DropDownListApi.couponType,
    params: {
      data: _params,
    },
  });
}

/**
 * @description 门店列表
 */
export function getStoreList(_params: {
  data: {
    storeName: string;
  };
  pageVO: {
    current: number;
    size: number;
  };
}) {
  return defHttp.post({
    url: DropDownListApi.store,
    params: _params,
  });
}
