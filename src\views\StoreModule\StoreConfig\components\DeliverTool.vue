<template>
  <div class="wrapper">
    <n-grid :cols="24" :x-gap="24" :y-gap="10" responsive="self">
      <n-gi :span="24" :style="{display:'flex',alignItems:'center',justifyContent:'space-between'}">
        <div class="title-wrapper">
          <div class="title-line"></div>
          <span style="margin-right: 10px">中通快递管家</span>
        </div>
        <n-form-item label-placement="left">
          <template #label>
            <span class="pointer introduce" @click="showEditParamsModal = true" v-if="hasOrderStoreconfigDeliveryEdit">编辑参数</span>
          </template>
          <n-switch v-model:value="ExpressButlerConfig.value" :loading="switchLoading" @update:value="handleChange" v-if="hasOrderStoreconfigDeliveryEnable" />
        </n-form-item>
      </n-gi>
      <n-gi :span="20">
        <span class="introduce" >启用本功能后，商城实物类订单在用户支付成功后将自动推送至您指定的快递管家账号内，您可以在快递管家里打印快递单，免除订单导出导入操作。商城端会自动获取快递公司名称和运单号发货。</span>
      </n-gi>
      <n-gi :span="20">
          <span style="color: red;" class="introduce" >启用本功能前，您需要在【地址配置】页面设置默认发货地址，并在中通开放平台获取配置参数和设置IP白名单，获取到的配置参数在【编辑参数】中填写，设置IP白名单请联系客服。</span>
        <span class="pointer introduce" @click="showGetParamsModal = true">点击查看配置参数获取指引</span>
      </n-gi>
      <n-gi :span="20">
        中通快递管家登录界面：
        <a href="https://vip.zto.com/login" target="_blank">https://vip.zto.com/login</a>
      </n-gi>
      <n-gi :span="20">
        <span class="pointer" @click="isShowAbnormalShipmentRef = true" v-if="hasManagementShip">发货异常记录</span>
      </n-gi>
    </n-grid>
    <n-divider />
    <getParamsModal v-model:show="showGetParamsModal" />
  <editParamsModal v-model:show="showEditParamsModal" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, computed } from 'vue'
import getParamsModal from './deliverToolComponents/getParamsModal.vue';
import editParamsModal from './deliverToolComponents/editParamsModal.vue';
import {getZtoStatus, updateZtoStatus, type GlobalConfigData } from '@/services/api';
import { hasOrderStoreconfigDeliveryEdit , hasOrderStoreconfigDeliveryEnable } from '../hooks/authList';
import { hasManagementShip } from "@/views/StoreModule/OrderManagement/authList"
import { useAbnormalShipment } from '@/hooks';
const { isShowAbnormalShipmentRef } = useAbnormalShipment();
import { useMessages } from "@/hooks";
const message = useMessages();

// 快递管家配置参数
const ExpressButlerConfig = reactive<GlobalConfigData>({} as GlobalConfigData);
const showGetParamsModal = ref(false);
const showEditParamsModal = ref(false);
const handleChange = (value:boolean)=>{
  updateZtoStatusFn()
};

const switchLoading = ref(false);
/** 获取中通快递管家功能启用状态 */
const getZtoStatusFn = async ()=>{
  switchLoading.value = true;
  getZtoStatus().then(res=>{
    console.log(res);
    Object.assign(ExpressButlerConfig, res);
    ExpressButlerConfig.value = res.value == 'true' || res.value == true;
  }).catch(err=>{
    message.createMessageError(`获取中通快递管家功能启用状态失败，${err}`);
  }).finally(()=>{
    switchLoading.value = false;
  });
};

/** 修改快递管家功能启用状态 */
const updateZtoStatusFn = async ()=>{
  switchLoading.value = true;
  updateZtoStatus(ExpressButlerConfig).then(res=>{
    console.log(res);
  }).catch(err=>{
    ExpressButlerConfig.value = !ExpressButlerConfig.value;
    message.createMessageError(`修改中通快递管家功能启用状态失败，${err}`);
  }).finally(()=>{
    switchLoading.value = false;
  });
};
onMounted(()=>{
  getZtoStatusFn();
});

</script>

<style scoped lang="less">
@import "@/styles/default.less";
@import "@/styles/defaultVar.less";
.wrapper{
  width: 100%;
  height: 100%;
  background-color: #fff;
padding: 24px;
box-sizing: border-box;
.introduce{
  line-height: 24px;
}
a{
  color: #666666;
  &:hover{
    color: @primary-color;
  }
}
.pointer{
  &:hover{
    text-decoration: underline;
  }
  cursor: pointer;
  color: @primary-color;
}
  .title-wrapper {
  height: 30px;
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 6px;

  .title-line {
    width: 4px;
    height: 60%;
    background-color: @primary-color;
    margin-right: 5px;
  }
}
}
</style>
