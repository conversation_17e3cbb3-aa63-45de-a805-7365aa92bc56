<template>
  <div :class="['image-upload']">
    <input
        style="display: none"
        ref="inputRef"
        title="图片"
        type="file"
        data-type="image"
        accept="image/gif,image/jpeg,image/jpg,image/png,image/bmp,image/webp"
        @change="sendImageInWeb"
    >
    <n-button size="tiny" type="info" @click="inputRef.click()">
      发图片
    </n-button>
  </div>
</template>
<script lang="ts" setup>
import TUIChatEngine, {
  TUIChatService,
  TUIStore,
  StoreName,
  type IConversationModel,
  type SendMessageParams,
} from '@tencentcloud/chat-uikit-engine';
import {ref, computed, inject} from 'vue';
import imageIconLight from '@/assets/icons/svg/imageLight.svg';
import {isEnabledMessageReadReceiptGlobal, safeParse} from "@/views/DoctorEndModule/IM/utils/IMUtils";
import {fileUploadCdn} from "@/services/api";
import {useMessages} from "@/hooks";
import {useUserStore} from "@/stores/modules/user";
import ImEMitter from "@/views/DoctorEndModule/IM/utils/ImMitter";

const props = defineProps({
  // Image source: only valid for uni-app version, web version only supports selecting images from the album.
  // album: Select from album
  // camera: Take a photo using the camera
  imageSourceType: {
    type: String,
    default: 'album',
  },
});

const userStore = useUserStore();
const inputRef = ref();
const currentConversation = ref<IConversationModel>();
const IMAGE_TOOLBAR_SHOW_MAP = {
  web_album: {
    icon: imageIconLight,
    title: '图片',
  },
};

TUIStore.watch(StoreName.CONV, {
  currentConversation: (conversation: IConversationModel) => {
    currentConversation.value = conversation;
  },
});

const sendImageInWeb = (e: any) => {
  const files = e.target.files;
  if (files?.length <= 0) {
    return;
  }
  sendImageMessage(files[0]);
  e.target.value = '';
};

const {createMessageError} = useMessages();
const {currentConversationData, changeCurrentConversation} = inject('currentConversationData');
const IMUserInfo = TUIStore.getData(StoreName.USER, 'userProfile');
const sendImageMessage = (files: any) => {
  if (!files && files.length == 0) {
    return;
  }
  const formData = new FormData();
  formData.append('files', files); // 关键步骤[1,5](@ref)
  // 上传文件并处理结果
  fileUploadCdn(true, formData).then(async res => {
    let payloadData = {
      "fromUserType": 1,
      "fromUserId": userStore.imConfig?.userID,
      "toUserType": 0,
      "toUserId": currentConversationData.value?.contactUserId,
      "conversationId": currentConversationData.value.conversationId,
      'originCdn': res[0].cdnUrl,
      'origin': res[0].path
    }
    const options = {
      to: currentConversationData?.value?.contactImUserId,
      conversationType: TUIChatEngine.TYPES.CONV_C2C,
      payload: {
        file: files,
      },
      needReadReceipt: isEnabledMessageReadReceiptGlobal(),
    } as SendMessageParams;
    options.payload = {
      data: JSON.stringify(payloadData),
      description: 'IMG',
      extension: '',
    }
    let sendMessageResult = await TUIChatService.sendCustomMessage(options);
    if (sendMessageResult?.data?.message) {
      sendMessageResult = sendMessageResult.data.message
      ImEMitter.emit('GetSendMessageResult', sendMessageResult)
    }
  }).catch(err => {
    createMessageError("上传图片失败: " + err);
  });
};
</script>

<style lang="less" scoped>

.image-upload {
  margin-right: 8px;

  :deep(.n-upload .n-upload-file-list) {
    margin: 0;
  }
}

</style>
