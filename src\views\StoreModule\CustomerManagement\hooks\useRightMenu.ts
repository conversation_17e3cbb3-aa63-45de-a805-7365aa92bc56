import { ref } from "vue";
import { CUSTOMER_TAG_ALL_VALUE } from "../type";

export default function useRightMenu(hasEditLabel,hasDeletelabel) {
    /** 右键选项 */
    const rightOptions = [
        {
            label: '删除标签',
            key: 'delete',
            show:hasDeletelabel
        },
        {
            label: '修改标签',
            key: 'hasEditLabel',
            show:hasEditLabel
        }
    ];
    /** 右键菜单 */
    const xRef = ref(0);
    const yRef = ref(0);
    const showDropdownRef = ref(false);

    /** 当前点击Menu */
    const currentClickMenu = ref(null);
    const handleRightClick = (e, option) => {
      showDropdownRef.value = true;
      xRef.value = e.clientX;
      yRef.value = e.clientY;
      currentClickMenu.value = option;
      e.preventDefault();
    };

    return {
        rightOptions,
        xRef,
        yRef,
        showDropdownRef,
        currentClickMenu,
        handleRightClick
    }
}