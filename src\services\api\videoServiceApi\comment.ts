import { defHttp } from "@/services";

const enum CommentApi {
    commentPage = "/videoComment/pageComment",
    deleteComment = "/videoComment/batchDelete",
    updateCommentType = "/videoComment/updateAuditState",
    commentByVideo =  "videoComment/pageByVideoId"
}

export function commentPage(params){
    return defHttp.post({
        url:CommentApi.commentPage,
        params
    })
}

export function deleteComment(params){
    return defHttp.delete({
        url:CommentApi.deleteComment,
        params:{data:{idList:params}},
        requestConfig: {
            isQueryParams: false,
          },
    })
}

export function updateCommentType(params){
    return defHttp.post({
        url:CommentApi.updateCommentType,
        params:{data:params},
        
    })
}

export function commentByVideo(params){
    return defHttp.post({
        url:CommentApi.commentByVideo,
        params,
        
    })
}