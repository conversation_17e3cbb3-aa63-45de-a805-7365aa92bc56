<!-- 分销员 -->
<template>
    <n-select
      :value="null"
      :options="processedData"
      :loading="loading"
      :style="{ width: `${props.width}px` }"
      @update:value="handleUpdateValue"
      :render-label="renderLabel"
      :render-tag="renderSingleSelectTag"
      :max-tag-count="props.maxTagCount"
      @keydown.enter="handleSearch"
      filterable
      @scroll="handleScroll"
      :reset-menu-on-options-change="isResetSelectStatusRef"
      @blur="handleBlur"
      placeholder="请输入昵称"
    >
      <template #empty>
        <div class="empty-wrapper">
          <img :src="EmptySrc" alt="" />
          <p>暂无数据</p>
        </div>
      </template>
    </n-select>
</template>
<script lang="tsx" setup>
import EmptySrc from "@/assets/image/exception/empty.png";
import type { SelectRenderLabel, SelectRenderTag } from 'naive-ui'
import { onMounted, ref, h, watch } from 'vue'
import { NAvatar, NTag, NText } from 'naive-ui'
import { useMessages } from "@/hooks";
import { distributorPageCsInfo } from '@/services/api';

// 定义响应式数据
const processedData = ref([]); // 处理后的最终数据

/* 提示信息 */
const message = useMessages();

const loading = ref(false)

const isResetSelectStatusRef = ref(false); // 重置选择状态

/* Props */
const props = withDefaults(
  defineProps<{
    // value: Array<string | number> | string | number | null; // 选择的值
    width: string | number,
    multiple : boolean,
    maxTagCount: number// 多选的最大数量
    tableData: any[];
  }>(),
  {
    width:400,
    multiple:false,
    maxTagCount:1,
    tableData:[]
  },
);

const emits = defineEmits<{
    (e: "distributorsValue", selectValue: any): void; // 更新选择值事件
  }>();

//回调事件
const handleUpdateValue = (value,text) =>{
    let shouldEmit = true;
    props.tableData.map((item)=>{
        if(item.value == value){
            message.createMessageWarning('已添加，不能重复操作')
            shouldEmit = false;
            return 
        }
    })
    
    if (shouldEmit) {
        emits("distributorsValue", text);
    }
    
    if(params.data.searchValue != ''){
        params.data.searchValue = ''; 
        getStructureListStructures();
    }
}

const params: { data:{searchType:string|number,searchValue:string|number},pageVO: { current: number; size: number } } = {
    data:{
        searchType:'nickname',
        searchValue:'',//名称
    },
    pageVO: {
      current: 1, // 当前页
      size: 100, //每页大小
    },
};

//请求数据
const getStructureListStructures = async() =>{
    loading.value = true
    try{
      const { total, current, size, records } = await distributorPageCsInfo(params)
      params.pageVO.current = Number(current);
      params.pageVO.size = Number(size);
      recordsTotal = Number(total);
      // 如果是第一页
      if (params.pageVO.current == 1) {
        isResetSelectStatusRef.value = true; // 重置选择状态为true
        processedData.value  = processData(records);
      } else {
        isResetSelectStatusRef.value = false; // 重置选择状态为false
        processData(records).forEach(item => {
          // 如果列表中不存在该项
          if (!processedData.value.find(temp => temp.value == item.value)) {
            // 添加到列表中
            processedData.value.push(item);
          }
        });
      }
    }catch(err){
        message.createMessageError('获取昵称与手机号失败:' + err)
    }finally{
        loading.value = false
    }
}

// 数据处理函数
const processData = (data) => {
  return data.map(item => {
    const result = {
      label: item.csNickname, 
      value: item.customerShortId, 
      csImg: item.csImg,
      csMobile:item.csMobile,
      disabled: item.isDistribute ? true : false,
    };
    return result;
  });
};

//下拉框内容渲染
const renderLabel: SelectRenderLabel = (option) => {
  return h(
    'div',
    {
      style: {
        display: 'flex',
        alignItems: 'center'
      }
    },
    [
      h(NAvatar, {
        src:  option.csImg ? option.csImg : 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg',
        round: true,
        size: 'small'
      }),
      h(
        'div',
        {
          style: {
            marginLeft: '12px',
            padding: '4px 0'
          }
        },
        [
          h('div', null, [option.label as string]),
          h(
            NText,
            { depth: 3, tag: 'div' },
            {
              default: () => option.csMobile
            }
          ),
          h(
            NText,
            { depth: 3, tag: 'div' },
            {
              default: () => '用户ID:' + option.value
            }
          )
        ]
      )
    ]
  )
}

//选中表格渲染
const renderSingleSelectTag: SelectRenderTag = ({ option }) => {
    return h(
        NTag,
        {
          style: {
            padding: '0 6px 0 4px'
          },
          round: true,
        }, 
        h('div',option.label as string)
    )
}

/** 搜索事件处理函数 */
const handleSearch = (event) => {
  params.pageVO.current = 1;
  params.data.searchValue = event.target.value;
  getStructureListStructures();
}

/* 执行搜索返回的内容*/
let recordsTotal = 1; // 总记录数

/** 滚动事件处理函数 */
function handleScroll(e) {
  const currentTarget = e.currentTarget as HTMLElement;
  if (currentTarget.scrollTop + currentTarget.offsetHeight >= currentTarget.scrollHeight) {
    // 如果当前页乘以每页大小小于总记录数
    if (params.pageVO.current * params.pageVO.size < recordsTotal) {
      params.pageVO.current++; // 当前页加1
      getStructureListStructures();
    }
  }
}

/** 失焦 */
const handleBlur = () =>{
  if( params.data.searchValue != ''){
    params.data.searchValue = ''
    getStructureListStructures()
  }
}

onMounted(() => {
    getStructureListStructures()
})


</script>
<style scoped lang="less">
.empty-wrapper {
  img {
    height: 50px;
  }
  p {
    color: #666;
    font-size: 12px;
    text-align: center;
  }
}
</style>
