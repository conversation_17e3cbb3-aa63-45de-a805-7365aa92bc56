<template>
	<!-- 这个组件是一个树形选择输入框 -->
	<n-tree-select
		multiple
		filterable
		cascade
		checkable
		check-strategy="child"
		placeholder="请选择分组"
		:options="dealerGroupsSelectList"
		style="width: 160px"
		:onFocus="handlerFocus"
		:onBlur="handlerBlur"
		:loading="isGetLoading"
		@update:value="handleUpdateValue"
		:clearable="clearable"
		:max-tag-count="maxTagCount"
		:clear-filter-after-select="false"
		:render-label="renderLabel"
		:default-value="props.defaultValue"
		:default-expand-all="props.isDefaultExpandAll"
		:disabled="isDisabled"
		:default-expanded-keys="['all']"
	/>
</template>

<script setup lang="tsx">
import { ref, watch, computed } from "vue";
import { getSgDealerGroup } from "@/services/api";
import { isArray } from "@/utils";
import type { TreeSelectOption, TreeOption } from 'naive-ui'

interface JDealerGroupsSelect {
	value: Array<string | number> | string | number | null; // 受控模式值
	multiple?: boolean; // 是否多选
	clearable?: boolean; // 是否可清除
	disabled?: boolean; // 是否禁用
	displayNoGroupOption?: boolean; // 显示无分组选项
	defaultValue?: string | number | Array<string | number> | null; // 默认选中的key
	isDefaultExpandAll?: boolean; // 是否展开全部
	isDeleteOption?: boolean; // 是否可删除选项
}

const props = withDefaults(defineProps<JDealerGroupsSelect>(), {
	multiple: true,
	clearable: true,
	disabled: false,
	displayNoGroupOption: false,
	defaultValue: null,
	isDefaultExpandAll: false,
	isDeleteOption: true,
});

const emits = defineEmits<{
	(e: "update:value", selectValue: any): void;
	(e: "focusState", focusState: boolean): void;
}>();

/** 是否禁用 */
const isDisabled = computed(() => {
	if (isArray(props.defaultValue)) {
		if(props.disabled || props.defaultValue.length == 0) {
			return true;
		}
		return false;
	} else {
		return props.disabled;
	}
});

const dealerGroupsSelectList = ref([]);
const maxTagCount = ref(1);

/** Focus 时的回调 */
const handlerFocus = () => {
	// 如果dealerGroupsSelectList为空，则获取选项
	if (!dealerGroupsSelectList.value.length) {
		getDealerGroupsSelectList();
	}
	// 发出焦点状态
	emits("focusState", true);
};

/** Blur 时的回调 */
const handlerBlur = () => {
	// 发出失焦状态
	emits("focusState", false);
};

/** 节点内容的渲染函数 */
const renderLabel = (info) => {
	const { option, checked, selected } = info;
	return (
		<n-tooltip trigger="hover">
			{{
				default: () => `${option.label}`,
				trigger: () => (
					<div
						style={{
							fontSize: "14px",
							width: "120px",
							whiteSpace: "nowrap",
							overflow: "hidden",
							textOverflow: "ellipsis",
						}}
					>
						{option.label}
						{checked && <i class="icon-check"></i>}
					</div>
				),
			}}
		</n-tooltip>
	);
};

const isGetLoading = ref(false);
/** 获取经销商分组数据 */
const getDealerGroupsSelectList = async () => {
	try {
      dealerGroupsSelectList.value = [];
	  isGetLoading.value = true;
	  // 获取经销商分组列表
	  const res = await getSgDealerGroup({});
	  // 如果响应为空，则返回
	  if (!res.length) {
	  	return;
	  }
	  // 创建选项
	  let childrenList = [];
	  // 判断是否显示 无分组 选项
	  if (props.displayNoGroupOption) {
	  	childrenList.push({
        label: '无分组',
        key: '0',
        depth: 2,
        isLeaf: true,
        disabled: false
      });
	  }
	  childrenList.push(...res.map((item, index) => {
	  	const children = item.dealerGroupPOS?.map((subItem) => {
	  		return {
	  			label: subItem.name,
	  			key: subItem.id,
	  			depth: 3,
	  			isLeaf: true,
	  			disabled: false
	  		};
	  	});
	  	return {
	  		label: item.name,
	  		key: item.id,
	  		depth: 2,
	  		isLeaf: children == undefined ? true : false,
	  		children: children,
	  		disabled: children == undefined ? true : false,
	  	};
	  }));
	  // 将全部选项推送到dealerGroupsSelectList
	  dealerGroupsSelectList.value.push({
	  	label: "全部",
	  	key: "all",
	  	depth: 1,
	  	children: childrenList,
	  	disabled: childrenList.length == 0 ? true : false,
	  });
  
	  // 禁用默认值
	  if (props.isDefaultExpandAll && !props.isDeleteOption) {
	  	disableKeys(dealerGroupsSelectList.value, props.defaultValue);
	  }
	} catch (error) {
		console.log("获取经销商分组失败: " + error);
	} finally {
		isGetLoading.value = false;
	}
};

// 更新节点的disabled属性
const disableKeys = (data, keys) => {
  data.forEach(node => {
    if (keys.includes(node.key)) {
      node.disabled = true;
    }
    if (node.children) {
      disableKeys(node.children, keys);
    }
  });
};

/** 返回更新值 */
const handleUpdateValue = (value: string | number | Array<string | number> | null, option: TreeSelectOption | null | Array<TreeSelectOption | null>, meta: { node: TreeOption | null, action: 'select' | 'unselect' | 'delete' | 'clear' }) => {
	if (isArray(value)) {
		let _tempValue = value;
		if (meta.action === 'unselect' || meta.action === 'delete') {
		  if (isArray(props.defaultValue)) {
		  	if (props.defaultValue.includes(meta.node.key)) {
		  		_tempValue.push(meta.node.key);
	        emits("update:value", _tempValue);
		  	} else {
					emits("update:value", value);
				}
		  }
	  } else {
	    emits("update:value", value);
		}
	} else {
		emits("update:value", value);
	}
	// 根据所选值的数量设置maxTagCount
	if (props.multiple && isArray(value)) {
		if (value.length > 1) maxTagCount.value = 0;
		if (value.length == 1) maxTagCount.value = 1;
	}
};

/** 监听 */
watch(() => props.defaultValue, (newVal) => {
	if (isArray(newVal) && newVal.length > 0) {
		maxTagCount.value = 0;
		getDealerGroupsSelectList();
	}
}, { immediate: true });
</script>

<style scoped lang="less">
:deep(.n-base-selection-tag-wrapper) {
	width: 80% !important;
}
</style>
