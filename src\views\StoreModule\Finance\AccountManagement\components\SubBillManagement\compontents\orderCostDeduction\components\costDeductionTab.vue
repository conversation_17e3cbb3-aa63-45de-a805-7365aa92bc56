<template>
    <div class="wrapper">
      <FormLayout
        :tableData="tabularData"
        :tableColumns="tableColumns"
        :isTableSelection="false"
        :isDisplayIndex="false"
        :is-table-pagination="false"
      >
        <template #tableHeaderBtn>
          <n-button
            @click="exportData"
            v-if="props.tabNameRef === OrderCostDeductionStatus.NOT_PROCESSED"
            class="store-button"
            type="primary"
            :loading="exportLoading"
            text
          >
            导 出
          </n-button>
        </template>
      </FormLayout>
    </div>
  </template>
  <script setup lang="tsx">
  import { ref, computed, watch,reactive } from 'vue';
  import { OrderCostDeductionStatus } from '../type';
  import FormLayout from "@/layout/FormLayout.vue";
  import { useMessages } from "@/hooks";
  const { createMessageSuccess, createMessageError } = useMessages();
  import { costPriceExport } from '@/services/api';
  
  const props = defineProps<{
    tabNameRef: OrderCostDeductionStatus,
    tabularData: any,
    fileKey: string
  }>();
  
  /* 表格项 */
  const tableColumns = computed(() => [
    ...(props.tabNameRef === OrderCostDeductionStatus.NOT_PROCESSED ? [
        {
            title: "分账单号",
            key: "allocationNo",
            align: "left",
            width: 100,
        }
    ] : []),
      {
          title: "订单编号",
          key: "orderCode",
          align: "left",
          width: 170,
          fixed: "left",
      },
      {
          title: "扣除成本金额",
          key: "cost",
          align: "left",
          width: 100,
          render: (row) => {
            return row.cost ? `${(row.cost / 100).toFixed(2)}` : '0.00'
          }
      },
      {
          title: "失败原因",
          key: "failureReason",
          align: "left",
          width: 250,
      },
  ])
  
  const exportLoading = ref<boolean>(false);
  /** 导出订单 */
  const exportData = () => {
    exportLoading.value = true;
    costPriceExport(props.fileKey).then((res) => {
        console.log(res,'导出成功')
    }).catch((err) => {
        createMessageError(`导出失败:${err}`);
    }).finally(() => {
        exportLoading.value = false;
    })
  }
  </script>
  <style lang="less" scoped>
  .wrapper{
      height: 100%;
  }
  </style>
  