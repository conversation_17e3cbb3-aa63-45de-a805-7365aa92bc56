<template>
    <div class="tree-wrapper">
        <n-tree
          v-bind="$attrs"
          block-line
          :data="treeData"
          :default-expanded-keys="[String(GoodsCategoryType.INTEGRAL), String(GoodsCategoryType.INTEGRALLABEL)]"
          :node-props="nodeProps"
          :cancelable="false"
        />
        <!-- 右键菜单 -->
        <n-dropdown
          trigger="manual"
          placement="bottom-start"
          :show="showDropdownRef"
          :options="menuOptions"
          :x="xRef"
          :y="yRef"
          @select="handleSelect"
          @clickoutside="handleClickoutside"
        />
    </div>
</template>

<script lang="ts" setup name='IntegralGoodsClassTree'>
import { ref, nextTick } from "vue";
import type { TreeOption, DropdownOption } from 'naive-ui';
import { GoodsCategoryType } from "@/enums";

/** props */
const props = withDefaults(defineProps<{
    value: string;
    treeData: Array<TreeOption & { isMenu: boolean }>;
    classMenuOptions: Array<DropdownOption>;
    labelMenuOptions: Array<DropdownOption>;
}>(), {
    treeData: () => [],
    classMenuOptions: () => [],
    labelMenuOptions: () => [],
});

/** emits */
const emits = defineEmits<{
    (e: "update:value", value: string): void;
    (e: "menuSelect",  key: string | number, option: TreeOption ): void;
}>();

const showDropdownRef = ref(false);
const menuOptions = ref([]);

/** 坐标 */
const xRef = ref(0);
const yRef = ref(0);
/** 当前点击分类 */
const currentClassification = ref<TreeOption | null>(null);

/** select 选中时触发的回调函数 */
const handleSelect = (key: string | number, option: DropdownOption) => {
    emits('menuSelect', key, currentClassification.value);
    showDropdownRef.value = false;
    currentClassification.value = null;
};

/** clickoutside 的时候触发的回调函数 */
const handleClickoutside = () => {
    showDropdownRef.value = false;
    currentClassification.value = null;
};

/** 节点绑定事件 */
const nodeProps =  ({ option }: { option: TreeOption }) => {
    return {
      onContextmenu (e: MouseEvent): void {
        // 判断右键分类还是标签
        if (option.type === GoodsCategoryType.INTEGRAL) {
            menuOptions.value = props.classMenuOptions;
        } else if (option.type === GoodsCategoryType.INTEGRALLABEL) {
            menuOptions.value = props.labelMenuOptions;
        }
        // 判断节点是否可右键菜单
        if (option.isMenu) {
            nextTick(() => {
                currentClassification.value = option;
                showDropdownRef.value = true;
                xRef.value = e.clientX;
                yRef.value = e.clientY;
                e.preventDefault();
            });
        }
      }
    }
};

</script>


<style lang="less" scoped>
.tree-wrapper {
    padding: 12px;
}
</style>