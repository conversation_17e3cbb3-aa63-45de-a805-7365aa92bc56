import { VideoManagementAuth } from "@/enums/authKeys";
import { hasAuth } from "@/utils/auth/authUtils";


/** 删除 */
export const hasManagementDelete = function(){
    return hasAuth(VideoManagementAuth.VideoManagementDelete.key);
}()
/** 详情 */
export const hasManagementDetails = function(){
    return hasAuth(VideoManagementAuth.VideoManagementDetail.key) ;
}()
/** 发布 */
export const hasManagementPublish = function(){
    return hasAuth(VideoManagementAuth.VideoManagementAdd.key) ;
}()
/** 修改状态 */
export const hasManagementStatus = function(){
    return hasAuth(VideoManagementAuth.VideoManagementHandleStatus.key) ;
}()


