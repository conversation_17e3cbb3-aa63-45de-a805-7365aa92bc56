<template>
  <NModal
    v-bind="$attrs"
    :title="props.title"
    :show="props.show"
    :preset="props.preset"
    size="small"
    closable
    :style="modalStyle"
    @after-enter="handleAfterEnter"
    @mask-click="handleMaskClick"
    @close="handleCloseModal"
    :auto-focus="false"
    :bordered="false"
  >
    <template v-if="isScale" #header-extra>
      <transition appear name="fade" mode="out-in">
        <NButton tertiary size="small" :style="resizeButtonStyle" @click="toggle">
          <template #icon>
            <NIcon>
              <svg v-if="isResize" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16">
                <g fill="none">
                  <path
                    d="M13.854 2.854a.5.5 0 0 0-.708-.708L10 5.293V3.5a.5.5 0 0 0-1 0v2.9a.6.6 0 0 0 .6.6h2.9a.5.5 0 0 0 0-1h-1.793l3.147-3.146zM6.5 13a.5.5 0 0 1-.5-.5v-1.793l-3.146 3.147a.5.5 0 0 1-.708-.708L5.293 10H3.5a.5.5 0 0 1 0-1h2.9a.6.6 0 0 1 .6.6v2.9a.5.5 0 0 1-.5.5z"
                    fill="currentColor"
                  />
                </g>
              </svg>
              <ResizeIcon v-else />
            </NIcon>
          </template>
        </NButton>
      </transition>
    </template>

    <template #header>
      <slot name="header"></slot>
    </template>

    <slot></slot>

    <template v-if="showFooter" #footer>
      <slot name="footer">
        <NFlex justify="end">
          <NButton v-if="props.negativeText" size="small" :style="buttonStyle" @click="negativeClick">
            {{ props.negativeText }}
          </NButton>
          <NButton
            v-if="props.positiveText"
            size="small"
            type="primary"
            :style="buttonStyle"
            :loading="props.positiveButtonProps?.loading"
            :disabled="props.positiveButtonProps?.disabled"
            @click="emit('positive-click', $event)"
          >
            {{ props.positiveText }}
          </NButton>
        </NFlex>
      </slot>
    </template>
  </NModal>
</template>

<script lang="tsx" setup>
import { ref, watch, computed, effectScope, onScopeDispose  } from "vue";
import { useDialog, NModal, NButton, NFlex, NIcon } from "naive-ui";
import { ResizeOutline as ResizeIcon } from '@vicons/ionicons5';
import { useBoolean, useDragModal } from '@/hooks';
import { isBoolean } from "@/utils";

interface Props {
  /** 是否展示 Modal */
  show: boolean;
  /** 宽 */
  width?: string;
  /** 高 */
  height?: string | boolean;
  /** 是否支持缩放，默认true */
  isScale?: boolean;
  /** 积极显示的文字 */
  positiveText?: string;
  /** 消极显示的文字 */
  negativeText?: string;
  /** modal显示的标题 */
  title: string;
  /** 预设类型 */
  preset?: 'dialog' | 'card';
  /** 确定按钮的属性 */
  positiveButtonProps?: {
      loading?: boolean;
      disabled?: boolean;
  };
  /** 是否需二次确认关闭弹窗 */
  isConfirm?: boolean;
}

/** props */
const props = withDefaults(defineProps<Props>(), {
  show: false,
  width: '600',
  height: false,
  isScale: false,
  preset: 'card',
  positiveText: "保 存",
  negativeText: "取 消",
  positiveButtonProps: () => ({
    loading: false,
    disabled: false
  }),
  isConfirm: false,
});

/** emit */
const emit = defineEmits<{
  (e: 'positive-click', value: MouseEvent): void; // 点击确定的回调函数
  (e: 'negative-click'): void; // 点击取消的回调函数
  (e: 'close'): void;
  (e: "update:show", value: boolean): void;
  (e: 'AfterEnter'): void;
}>();

const scope = effectScope();
/** 对话框 */
const dialog = useDialog();
const isShrink = ref(false);
const { bool: isResize, toggle, setFalse } = useBoolean();

const showFooter = computed(() => props.negativeText || props.positiveText);
const buttonStyle = computed(() => ({
  minWidth: '72px',
  minHeight: '32px',
  '--n-padding': '4px 11px',
  '--n-border-radius': '5px'
}));

const resizeButtonStyle = computed(() => ({
  width: '22px',
  height: '22px',
  '--n-color': '#fff'
}));

const modalStyle = computed(() => ({
  width: isResize.value ? 'calc(-120px + 100vw)' : `${props.width}px`,
  height: isResize.value
    ? 'calc(-160px + 100vh)'
    : !isBoolean(props.height) ? `${props.height}px` : 'auto',
  transition: isResize.value
    ? 'width .3s, height .3s'
    : isShrink.value && props.show ? 'width .3s, height .3s' : null
}));

/** 出现动画完成执行的回调 */
const handleAfterEnter = (e) => {
  useDragModal(e);
  emit('AfterEnter');
};

/** 二次确认 */
const handleMaskClick = () => {
  if (props.isConfirm) {
    dialog.warning({
      title: () => <span style="font-weight: 500;font-size: 16px;">确定退出当前编辑？</span>,
      content: '退出后，当前修改的内容不会被保存',
      positiveText: '确 认',
      positiveButtonProps: {
        type: 'primary',
        size: 'small',
        class: 'store-button'
      },
      negativeText: '取 消',
      negativeButtonProps: {
        size: 'small',
        class: 'store-button'
      },
      closable: false,
      autoFocus: false,
      transformOrigin: 'center',
      onPositiveClick: handleCloseModal,
      onNegativeClick: () => {}
    });
  } else {
    handleCloseModal();
  }
};

const handleCloseModal = () => {
  emit("update:show", false);
  emit("close");
};

const negativeClick = () => {
  emit("negative-click");
  handleCloseModal();
};

/** 在作用域内运行监听器 */
scope.run(() => {
  watch(() => props.show, (newVal) => {
    if (newVal) {
      setFalse();
    }
    isShrink.value = false;
  });
  
  watch(isResize, (newVal) => {
    isShrink.value = !newVal;
  });
});

/** 作用域销毁时清理 */
onScopeDispose(() => {
  scope.stop();
});
</script>
