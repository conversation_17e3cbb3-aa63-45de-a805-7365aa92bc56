<template>
  <NTreeSelect
    filterable
    :value="props.value"
    :loading="loading"
    :options="options"
    :defaultExpandedKeys="defaultExpandedKeys"
    :onFocus="handlerFocus"
    :render-tag="renderReportsTag"
    @update:value="handleUpdateValue"
  />
</template>

<script lang="ts" setup>
import { ref, watch, effectScope, onScopeDispose } from 'vue';
import { NTreeSelect } from 'naive-ui';
import type { TreeSelectOption } from 'naive-ui';
import { useMessages, useLoading } from "@/hooks";
import { transformOptions, getExpandKeys, findParentAndChild } from "./utils";
import { fetchGoodsCategories } from "@/services/api";
import type { GoodsType } from "@/enums";

/** props */
const props = withDefaults(
  defineProps<{
    isImmediately?: boolean; // 是否立即加载
    value: string | number | Array<string | number> | null; // 选择的值
    disableId?: string | null; // 禁用的id
    type?: GoodsType; // 分类类型
  }>(),
  {
    value: undefined,
    isImmediately: false,
    disableId: null,
    type: null
  },
);

/** emits */
const emits = defineEmits<{
  (e: "update:value", value: string | number | Array<string | number> | null): void;
}>();

/* 提示 */
const message = useMessages();
const { loading, startLoading, endLoading } = useLoading();
const options = ref([]);
const defaultExpandedKeys = ref([]);

/** 搜索参数 */
const params: { data: { type: GoodsType }} = {
  data: {
    type: props.type ?? null,
  }
};

/** 自定义Tag */
function renderReportsTag(props: { option: TreeSelectOption, handleClose: () => void }) {
  const { option } = props;
  if (option?.parentId) {
    let label = findParentAndChild(options.value, option.parentId, option.key);
    return label;
  } else {
    return props.option.label;
  }
}

/**
 * @description 处理数据
 */
function handlerSecondaryData(filterData: Array<ApiStoreModule.GoodsClassification>) {
  const treeData = transformOptions(filterData, props.disableId);
  return treeData;
}

/**
 * @description 获取商品分类列表
 */
async function getGoodsClassificationList() {
  try {
    startLoading();
    const data = await fetchGoodsCategories(params);
    if (data?.length > 0) {
      options.value = handlerSecondaryData(data);
      let newDefaultExpandKeys = getExpandKeys(options.value).filter((value, index, self) => self.indexOf(value) === index);
      defaultExpandedKeys.value.push(...newDefaultExpandKeys);
    }
  } catch (error) {
    message.createMessageError("获取商品分类列表失败" + error);
  } finally {
    endLoading();
  }
}

/**
 * @description Focus 时的回调
 */
function handlerFocus() {
  // 如果会员标签列表为空
  if (!options.value.length) {
    getGoodsClassificationList();
  }
};

/**
 * @description 更新值的回调
 */
function handleUpdateValue(value: string) {
  emits("update:value", value);
};

/** 创建effectScope */
const scope = effectScope();

/** 作用域内运行一组副作用 */
scope.run(() => {
  /** 监听 */
  watch(
    () => props.isImmediately,
    newVal => {
      if (newVal) {
        getGoodsClassificationList();
      }
    },
    { immediate: true },
  );
});

/** 作用域销毁时，销毁图表和停止作用域 */
onScopeDispose(() => {
  console.log("销毁监听");
  scope.stop();
});
</script>
