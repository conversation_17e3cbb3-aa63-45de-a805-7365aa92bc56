<template>
    <div class="order_item_container">
        <!-- label -->
        <div class="order_item_label">
            <slot name="label">
                {{ labelValue }}
            </slot>
        </div>
        <!-- content -->
        <div class="order_item_content">
            <slot></slot>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, toRefs } from "vue";

defineOptions({ name: 'JFieldItem' });

/** props */
const props = defineProps<{
    label?: string;
}>();

const { label: labelValue } = toRefs(props);
</script>

<style lang="less" scoped>
.order_item_container {
    width: 400px;
    display: flex;
    gap: 16px;
    .order_item_label {
        min-width: 100px;
        font-family: Source <PERSON>, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        line-height: 28px;
        text-align: right;
        font-style: normal;
        text-transform: none;
    }
    .order_item_content {
        flex: 1;
        font-family: Source <PERSON>, Source <PERSON>;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        line-height: 28px;
        text-align: left;
        font-style: normal;
        text-transform: none;
    }
}
</style>