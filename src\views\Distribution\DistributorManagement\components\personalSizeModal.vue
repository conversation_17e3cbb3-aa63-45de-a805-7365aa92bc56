<!-- 个人码 -->
<template>
     <n-modal
      v-model:show="show"
      :auto-focus="false"
      style="width: 360px;"
      :bordered="false"
      size="small"
      :closable="false"
      preset="card"
      :mask-closable="false"
      :close-on-esc="false"
    >
      <template #header>
        <div style="display: flex;
        justify-content: space-between; 
        line-height: 34px;"
        >
          <p>个人码</p>
          <n-button text @click="closeModal">关闭</n-button>
        </div>
      </template>
      <div style="display: flex;
           flex-wrap: wrap;
           justify-content: center;
           "
      >
        <n-spin :show="loading" description="加载中...">
          <n-image
            v-if="path"
            width="260"
            height="260"
            :src="path"
          />
          <div v-else style="height: 260px;width: 260px">

          </div>
        </n-spin>
      </div>
    </n-modal>
</template>
<script setup lang="tsx" name="personalSizeModal">
import { ref } from "vue";
import { getPersonalCode } from "@/services/api";
const show = ref(false);
const path = ref('')
const loading = ref()
const showModal = async (params) => {
  show.value = true;
  loading.value = true;
  path.value = ''
  try {
    path.value = await getPersonalCode(params)
  }catch (e) {

  }finally {
    loading.value = false
  }
};

const closeModal = () => {
  show.value = false;
}

defineExpose({ showModal });
</script>
<style scoped lang="less"></style>