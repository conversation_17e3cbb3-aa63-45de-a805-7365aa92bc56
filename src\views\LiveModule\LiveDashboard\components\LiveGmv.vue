<template>
    <div class="liveGmv-wrapper">
        <div class="refresh">
            <span>每3秒更新一次数据</span>
            <n-button size="small" color="#fff" ghost @click="handleRefresh">
                <template #icon>
                    <n-icon :class="{ 'rotate-animation': isRefreshing }">
                        <Refresh />
                    </n-icon>
                </template>
                手动刷新
            </n-button>
        </div>
        <div class="gmv">
            <h3 class="tit">直播间交易总额(GMV)</h3>
            <n-statistic tabular-nums>
                ￥<n-number-animation class="custom-font" show-separator :from="0" :duration="500" :to="totalTransactionMoneyRef" :precision="2" />
            </n-statistic>
        </div>
        <div class="info">
            <div v-for="(item, key) in liveRoomInfoReactive" :key="key" class="info-item">
                <div class="label">
                    {{item.label}}
                    <n-popover trigger="click" v-if="item.hint">
                        <template #trigger>
                            <n-icon size="20" class="icon">
                                <HelpCircleOutline />
                            </n-icon>
                        </template>
                        <span>{{ item.hint}}</span>
                    </n-popover>
                </div>
                <div class="sum">
                    <n-statistic tabular-nums>
                        {{ item.prefix }}
                        <n-number-animation class="custom-font" show-separator :from="0" :duration="500" :to="item.value" :precision="item.precision" />
                        {{ item.suffix }}
                    </n-statistic>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Refresh,HelpCircleOutline } from '@vicons/ionicons5'
import useLiveDashboard from "../hook/useLiveDashboard";

const { liveRoomInfoReactive,totalTransactionMoneyRef,threeAutoRefresh } = useLiveDashboard()

const isRefreshing = ref(false);

const handleRefresh = () => {
    isRefreshing.value = true;
    threeAutoRefresh.triggerRefresh()
    setTimeout(() => {
        isRefreshing.value = false;
    }, 1000);
}
</script>

<style lang="less" scoped>
.liveGmv-wrapper {
    .refresh{
        display: flex;
        align-items: center;
        font-size: 14px;
        justify-content: end;
        span{
            margin-right: 10px;
        }
        .rotate-animation {
            animation: rotate360 1s linear;
        }
        @keyframes rotate360 {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }
    }
    .gmv{
        display: flex;
        align-items: center;
        flex-direction: column;
        .tit{
            font-size: 18px;
        }
        :deep(.n-statistic .n-statistic-value .n-statistic-value__content) {
            color: #fff;
            font-size: 40px;
            text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.3);
            font-weight: bold;
        }
    }
    .info{
        margin-top: 20px;
        display: flex;
        flex-wrap: wrap;
        .info-item {
            display: flex;
            margin-bottom: 20px;
            flex-direction: column;
            align-items: center;
            width: 20%;
            white-space: nowrap;
            .label {
                font-size: 17px;
                color: #464655;
                margin-bottom: 2px;
                display: flex;
                line-height: 18px;
                .icon{
                    cursor: pointer;
                }
            }
            .sum {
                :deep(.n-statistic .n-statistic-value .n-statistic-value__content) {
                    color: #fff;
                    font-size: 21px;
                }
            }
        }
    }
}
</style>
