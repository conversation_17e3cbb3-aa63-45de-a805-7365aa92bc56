<template>
  <JDrawer
    v-model:show="drawerVisible"
    title="导入发货"
    @after-leave="closeDrawer"
	  :isShowFooter="false"
    to="#OrderManagement"
	  :contents-list="[
        {
          name: '发货流程',
          slotName: 'delivery_Process'
        },
		    {
          name: model.showForm ? '发货失败订单列表' : '',
          slotName: 'delivery_Failure'
        },
    ]"
  > 
  <template #delivery_Process>
	<n-space justify="space-between">
      <div style="overflow: auto">
           <n-timeline horizontal >
             <n-timeline-item v-for="(item,index) in deliveryProcess" :key="index" type="info" :content="item" />
           </n-timeline> 
      </div>
      <n-button style="width: 100px;" type="info" @click="downloadFile">下载模版</n-button>
  </n-space>
  <n-space justify="center" style="margin-top:100px;">
	  <div style="text-align: center;">
		 <p>{{importPromptContent[promptContentSwitching]}}</p>

		 <div style="display: flex;justify-content: center;">
			 <n-button  v-if='model.cancel' style="width: 100px;  margin:10px;"  @click="handleEnd()"> 取 消 </n-button>
			 <n-button  v-if='model.sure' style="width: 100px;  margin:10px;" secondary type="primary" :loading="buttonIsLoading.sure"  @click="handleConfirmDelivery()" :disabled="model.totalCount == 0"> 确定</n-button>
       <n-button  v-if='model.understand' style="width: 100px;  margin:10px;" secondary type="primary" @click="handleKnow()"> 知道了</n-button> 
       <n-button  v-if='model.continueDelivery' style="width: 100px;  margin:10px;" secondary type="primary" @click="handleAdvance()"> 继续发货</n-button>
       <excelSubassembly v-if='model.upload' style="width: 100px;  margin:10px;" @importedData="importedData" :isLoadingShow="buttonIsLoading.update"  :buttonName = "'+ 上传发货单'"/>
		 </div>

	  </div>
	</n-space>
  </template>
  <template #delivery_Failure v-if="model.showForm">
	  <div class="dashboard-sm-content">
	   <FormLayout 
          class="inner-page-height"
          :isLoading="isLoading" 
          :tableData="tableData" 
          :tableColumns="tableColumns" 
          :isNeedCollapse="false"
	        :is-table-selection="false"
          :is-table-pagination="false"
          :is-display-header="false"
	        style="height: 520px;"
         >
	   </FormLayout>
	  </div>
  </template>
  </JDrawer>
</template>
  
<script setup lang="tsx" name="drawerOrderStatus">
import { computed, ref } from "vue";
 import FormLayout from "@/layout/FormLayout.vue";
import { useMessages } from "@/hooks";
import { deepClone } from "@/utils";
 import excelSubassembly from './excelSubassembly.vue';
 import { ImportShipmentType } from '@/enums';
const { createMessageSuccess, createMessageError } = useMessages();

/* 表单参数初始化 */
const initParams = {
  fileUrl:null,
  totalCount:null,
  successCount:null, //成功订单数
  failedCount:null, //失败订单数
  showForm:false,//是否显示发货失败订单表单
  cancel:false, //取消按钮
  sure:false,  //确定按钮
  understand:false, //了解按钮
  continueDelivery:false, //继续发货按钮
  upload:true //上传发货单按钮
};

/** 发货流程 */
const deliveryProcess = [
  "1.下载发货单模板",
  "2.按模板填写发货单",
  "3.上传发货单",
  "4.确认发货"
]

/** 表格isLoading  */
const isLoading = ref(false);

/* 表格方法数据 */
const tableData = ref([]) 

const model = ref(deepClone(initParams));

/** 抽屉状态 */
const drawerVisible = ref(false);
const drawerProps = ref(null)
/* 接收父组件传过来的参数 */
const acceptParams = (params) => {
  drawerVisible.value = true;
  drawerProps.value = params
};

/** 关闭抽屉 */
const closeDrawer = () => {
  model.value = { ...initParams };
  drawerVisible.value = false;
  promptContentSwitching.value = ImportShipmentType.startImporting
};

/* 表格项 */
const tableColumns = [
    {
      title: "订单编号",
      key: "code",
      align: "left",
      fixed: "left",
      width: 100,
      summaryTitle:"",
    },
    {
      title: "快递公司编号",
      key: "shipCompanyCode",
      align: "left",
      summaryTitle:"",
      width: 100,
    },
    {
      title: "快递单号",
      key: "trackingNo",
      align: "left",
      summaryTitle:"",
      width: 100,
    },
    {
      title: "发货失败原因",
      key: "reason",
      align: "left",
      summaryTitle:"",
      width: 200,
    },
];

/** 知道了的点击按钮 */
const handleKnow = () =>{
  drawerProps.value.refresh()
  closeDrawer()
}

/** 取消上传 */
const handleEnd = () =>{
  promptContentSwitching.value = ImportShipmentType.startImporting
  model.value = { ...initParams };
}

/** 继续上传 */
const handleAdvance = () =>{
  model.value = { ...initParams };
  promptContentSwitching.value = ImportShipmentType.startImporting
}

/** 提示内容切换 */
const promptContentSwitching = ref(ImportShipmentType.startImporting)

/** 选中导入的数据 */
const importedData = async(file) =>{
  buttonIsLoading.value.update = true
  model.value.upload = false
  promptContentSwitching.value = ImportShipmentType.importInProgress
  try{
    
    const data = await drawerProps.value.getImportOrderShipApi(file)
    if(data){
      promptContentSwitching.value = ImportShipmentType.importSucceeded
      model.value.totalCount = data.totalCount
      model.value.fileUrl = data.fileUrl

      model.value.cancel = true
      model.value.sure = true
    }

  }catch(err){
    createMessageError('订单发货导入失败:' + err)
    model.value.upload = true
    promptContentSwitching.value = ImportShipmentType.importFailed
  }finally{
    buttonIsLoading.value.update = false
  }
}

/** 确定发货按钮 */
const handleConfirmDelivery = async() =>{
  buttonIsLoading.value.sure = true
  const _params = {
      fileUrl:model.value.fileUrl
  }

  try{
    const data = await drawerProps.value.getConfirmOrderApi(_params)
    if(data){
      model.value.successCount = data.successCount ? data.successCount : 0
      model.value.failedCount = data.failedCount ? data.failedCount : 0
      tableData.value =  data.failedList
      promptContentSwitching.value = ImportShipmentType.deliveryResult

      model.value.showForm = true
      model.value.cancel = false
      model.value.sure = false
      model.value.understand = true
      model.value.continueDelivery = true
    }
  }catch(err){
    createMessageError('导入成功确认信息失败:' + err)
  }finally{
    buttonIsLoading.value.sure = false
  }
}

/** 提示内容 */
const importPromptContent = computed(() =>{
  return [
    '单次最多可发5000张订单',
    '数据导入中，请不要刷新当前页面 ....',
    '单次发货量不能超过5000张订单，请重新上传。',
    '已导入' + (model.value.totalCount ? model.value.totalCount : 0) + ' 张订单，确定发货后操作不可撤销，发货成功的物流信息将对买家展示。',
    '发货完成，共发货成功' + model.value.successCount + '张订单，' + (model.value.failedCount ? model.value.failedCount : 0) + '张订单存在异常情况'
  ]
}) 

/** 按钮 isLoading 状态*/
const buttonIsLoading = ref(
  {
    'sure':false,
    'update':false
  }
)

/** 下载模板 */
const downloadFile = () =>{
  const a = document.createElement("a"); 
  a.href = "/static/下载导入发货模板.xlsx"; // 给a标签的href属性值加上地址，注意，这里是绝对路径，不用加 点.
  a.download = "下载导入发货模板.xlsx"; 
  a.style.display = "none";
  document.body.appendChild(a);
  a.click(); // 模拟点击了a标签，会触发a标签的href的读取，浏览器就会自动下载了
  a.remove(); // 一次性的，用完就删除a标签
}

defineExpose({
  acceptParams,
  closeDrawer,
  drawerVisible
});
</script>
  
<style scoped lang="less">
.footer-wrapper {
  display: flex;
  align-items: center;
  justify-content: end;
}
.dashboard-sm-content{
  height: 500px;
  .inner-page-height {
    height: 100%;
  }
}
</style>
  