<template>
    <JModal
    v-model:show="isShow"
    width="680"
    title="设置默认地址"
    @after-leave="closeModal"
		@positive-click="_save"
		:positiveButtonProps="{
			loading: isLoading
		}"
  >
  <div>
    <n-spin :show="isLoading">
    <n-form
      ref="formRef"
      :rules="rules"
      :model="model"
      label-width="auto"
      label-placement="left"
      require-mark-placement="right-hanging"
    >
        <!-- 选择默认发货地址 -->
        <n-form-item :span="24" label="默认发货地址" path="deliveryAddressId">
            <JSelect v-model:value="model.deliveryAddressId" :options="deliveryAddressOptions" placeholder="请选择默认发货地址" :isShowTooltip="false" :render-label="renderLabel" value-field="id" />
        </n-form-item>
        <!-- 选择默认退货地址 -->
        <n-form-item :span="24" label="默认退货地址" path="returnAddressId">
            <JSelect v-model:value="model.returnAddressId" :options="returnAddressOptions" placeholder="请选择默认退货地址" :isShowTooltip="false" :render-label="renderLabel" value-field="id" />
        </n-form-item>
    </n-form>
    </n-spin>
  </div>
</JModal>
</template>

<script setup lang="ts">
import { ref,computed, reactive, watch } from 'vue'
import { addressList,setDefaultAddress } from '@/services/api';
import { useMessages } from "@/hooks";
const { createMessageSuccess, createMessageError } = useMessages();
import { AddressType, AddressIsActivated } from '@/views/StoreModule/StoreConfig/type';

const getAddressData = () => {
    isLoading.value = true;
    addressList({data:{type:0}}).then(res=>{
        deliveryAddressOptions.value = res.filter(item=>item.type == AddressType.Delivery && item.isActivated == AddressIsActivated.Activated);
        model.deliveryAddressId = deliveryAddressOptions.value.find(item=>item.isDefault == 1)?.id
        returnAddressOptions.value = res.filter(item=>item.type == AddressType.Return && item.isActivated == AddressIsActivated.Activated);
        model.returnAddressId = returnAddressOptions.value.find(item=>item.isDefault == 1)?.id
    }).catch(err=>{
        createMessageError(`获取地址数据失败：${err}`)
    }).finally(()=>{
        isLoading.value = false;
    })
}

const props = defineProps({
    show:Boolean
});

watch(()=>props.show,(newValue)=>{
    if(newValue){
        getAddressData();
    }
});
const emit = defineEmits(['update:show']);

const deliveryAddressOptions = ref([])
const returnAddressOptions = ref([])

const renderLabel = (option:any) => {
    return `${option.province}-${option.cityName}-${option.area}-${option.address}`
}

const initParams = {
    /** 默认发货地址 */
    deliveryAddressId: null,
    /** 默认退货地址 */
    returnAddressId: null
}

const model = reactive({...initParams});

const rules = {
    addressId: {
        type: 'string',
        required: true,
        trigger: ['blur', 'change'],
        message: '请选择地址',
    },
}

const isShow = computed({
    get(){
        return props.show
    },
    set(value){
        emit('update:show',value)
    }
});

const isLoading = ref(false)

const _save = () => {
    isLoading.value = true;
    const params = [
        model.deliveryAddressId,
        model.returnAddressId
    ].filter(item=>item != null)
    setDefaultAddress({data:params}).then(res=>{
        createMessageSuccess('设置成功');
        closeModal();
    }).catch(err=>{
        createMessageError(`设置失败：${err}`)
    }).finally(()=>{
        isLoading.value = false;
    })
}

/** 关闭弹窗 */
const closeModal = () => {
    isShow.value = false;
    Object.assign(model,initParams)
}


</script>

<style scoped lang="less" >  </style>
