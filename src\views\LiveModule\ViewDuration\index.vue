<template>
  <div class="wrapper inner-page-height">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :pagination="paginationRef"
      @paginationChange="paginationChange"
      :isNeedCollapse="false"
      :isTableSelection="false"
      :isDisplayIndex="false"
    >
      <!-- 表单 -->
      <template #searchForm>
        <n-form
          ref="formRef"
          label-placement="left"
          label-width="auto"
          :show-feedback="false"
          require-mark-placement="right-hanging"
          size="small"
          :style="{ width: '100%' }"
        >
          <n-form-item :span="12" label="会员ID">
            <n-input
              v-model:value="model.memberShortId"
              @input="value => (model.memberShortId = value.replace(/[^0-9]/g, '').replace(/^0+/, '') || null)"
            />
          </n-form-item>
          <n-form-item :span="12" label="归属店员ID">
            <n-input
              v-model:value="model.staffShortId"
              @input="value => (model.staffShortId = value.replace(/[^0-9]/g, '').replace(/^0+/, '') || null)"
            />
          </n-form-item>
        </n-form>
      </template>

      <template #tableHeaderBtn>
        <n-button type="primary" class="store-button" @click="handlerSearch(false)">搜索</n-button>
        <n-button class="store-button" @click="handlerSearch(true)">重置</n-button>
      </template>
    </FormLayout>
    <Details ref="detailsRef" />
  </div>
</template>

<script lang="tsx" setup name="DoctorManagement">
import { onMounted, ref, watch } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { getViewDurationPage } from "@/services/api";
import { useMessages } from "@/hooks";
import Details from "./components/Details.vue";
import { hasViewDataDetailsAuth, hasViewDataExportAuth } from "./authList";
import moment from "moment";
import { useRouter } from "vue-router";

const { createMessageSuccess, createMessageError } = useMessages();

/** 表格hook */
const { isLoading, tableData, paginationRef, pageTableData, paginationChange } = useTableDefault({
  pageDataRequest: getViewDurationPage,
});

/* 表格列表项 */
const tableColumns = ref([
  {
    title: "会员ID",
    key: "memberShortId",
    align: "left",
    fixed: "left",
  },
  {
    title: "归属店员ID",
    key: "staffShortId",
    align: "left",
  },
  {
    title: "会员昵称",
    key: "memberName",
    align: "left",
  },
  {
    title: "观看时长（分钟）",
    key: "watchSeconds",
    width: 350,
    align: "center",
    children: [
      {
        title: "昨日",
        key: "yesterdayWatchSeconds",
        align: "center",
        width: 116,
        render: row => {
          return <div>{Math.floor(row.yesterdayWatchSeconds / 60)}</div>;
        },
      },
      {
        title: "本周",
        key: "weekWatchSeconds",
        align: "center",
        width: 116,
        render: row => {
          return <div>{Math.floor(row.weekWatchSeconds / 60)}</div>;
        },
      },
      {
        title: "本月",
        key: "monthWatchSeconds",
        align: "center",
        width: 116,
        render: row => {
          return <div>{Math.floor(row.monthWatchSeconds / 60)}</div>;
        },
      },
    ],
  },
]);
/** 获取参数 */
const getParams = () => {
  const { memberShortId, staffShortId } = model.value;
  return {
    memberShortId,
    staffShortId,
  };
};

const detailsRef = ref();

const initParams = {
  memberShortId: null,
  staffShortId: null,
};
const model = ref({ ...initParams });
/** 搜索 */
const handlerSearch = (isReset?: boolean) => {
  if (isReset) {
    model.value = { ...initParams };
  }
  tableSearch();
};

/** 表格刷新 */
function refresh() {
  tableSearch();
}
const handleViewDataDetails = (row: any) => {
  detailsRef.value.acceptParams(row);
};

/* 表格搜索 */
const tableSearch = () => {
  pageTableData(getParams(), paginationRef.value);
};
/** 组件挂载 */
onMounted(() => {
  tableSearch();
});
</script>

<style lang="less" scoped>
@import "@/styles/defaultVar.less";
</style>
