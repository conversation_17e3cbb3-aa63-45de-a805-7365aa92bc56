<template>
  <div class="chat-header">
    <div class="chat-header-container">
      <div class="chat-header-content">
        {{ currentConversationTitleName }}
      </div>
      <div>
      </div>
    </div>
    <div class="chat-header-setting" v-if="tabName !== 'completed'">
      <IMCountdown/>
    </div>
  </div>
</template>

<script setup lang="ts">
import {computed, inject} from 'vue'
import type {ExtensionInfo} from "@tencentcloud/tui-core";
import IMCountdown from '@/views/DoctorEndModule/IM/components/common/IMCountdown/index.vue'

const props = withDefaults(
    defineProps<{
      headerExtensionList: ExtensionInfo[];
    }>(),
    {
      headerExtensionList: () => ([]),
    },
);

const {tabName} = inject('tabName'); // 当前的TabName,值为 processing | completed
const {currentConversationData, changeCurrentConversation} = inject('currentConversationData');

const currentConversationTitleName = computed(() => {
  return currentConversationData.value.nickname || '-'
})


</script>

<style scoped lang="less">
.chat-header {
  display: flex;
  min-width: 0;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;

  &-container {
    display: flex;
    min-width: 0;
    flex-direction: column;
    justify-content: flex-start;
  }

  &-content {
    margin-right: 20px;
    flex: 1;
    font-size: 16px;
    line-height: 30px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    color: #000;
    letter-spacing: 0;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  &-setting {
    display: flex;
    align-items: center;
    height: 27px;

    .icon {
      width: 100%;
      height: 100%;
    }
  }

  &-back {
    width: 27px;
    height: 27px;

    .icon {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
