<template>
  <div class="wrapper inner-page-height">
    <FormLayout
      :isLoading="isLoading"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :pagination="paginationRef"
      @paginationChange="paginationChange"
      :isNeedCollapse="false"
      :isDisplayIndex="false"
      :isTableSelection="false"
    >
      <!-- 表单 -->
      <template #searchForm></template>

      <template #tableHeaderBtn>
        <JAddButton type="primary" v-if="hasMedicalAssistantAdd" @click="handlerCreateOrEdit">新增</JAddButton>
      </template>
    </FormLayout>
    <AddMedicalAssistant v-model:show="showMedicalAssistant" :data="data" @refresh="refresh" />
  </div>
</template>

<script lang="tsx" setup name="DoctorManagement">
import { createRenderer, onMounted, ref, watch } from "vue";
import FormLayout from "@/layout/FormLayout.vue";
import { useTableDefault } from "@/hooks/useTableDefault";
import { assistantDoctorPage, deleteAssistantDoctor } from "@/services/api";
import { useMessages } from "@/hooks";
import { hasMedicalAssistantAdd, hasMedicalAssistantEdit, hasMedicalAssistantDelete } from "./authList";
import AddMedicalAssistant from "./components/AddMedicalAssistant.vue";
import moment from "moment";
import Popconfirm from "@/components/Popconfirm/index.vue";
const { createMessageSuccess, createMessageError } = useMessages();

/** 表格hook */
const { isLoading, tableData, paginationRef, pageTableData, paginationChange } = useTableDefault({
  pageDataRequest: assistantDoctorPage,
});

const showMedicalAssistant = ref(false);

const data = ref({});

/** 参数 */
const formValue = ref({
  searchValue: "",
  searchType: "patientName",
  consultationStatus: null,
  creationTime: null,
});
/* 表格列表项 */
const tableColumns = ref([
  {
    title: "姓名",
    key: "name",
    width: 300,
    align: "left",
  },
  {
    title: "状态",
    key: "status",
    align: "left",
    render: row => {
      return <div>{row.status === 1 ? "启用" : "停用"}</div>;
    },
  },
  {
    title: "创建时间",
    key: "createTime",
    align: "left",
  },
  {
    title: "操作",
    key: "action",
    width: 120,
    fixed: "right",
    align: "left",
    render: row => {
      return (
        <n-space style="padding: 5px 0;">
          {hasMedicalAssistantDelete ? (
            <Popconfirm
              onHandleClick={() => clickDelete(row.id)}
              loading={isDeleteLoading.value}
              promptContent={"是否确定删除该数据?"}
            />
          ) : null}
          {hasMedicalAssistantEdit ? (
            <n-button text type="primary" onClick={() => handlerCreateOrEdit(row)}>
              编辑
            </n-button>
          ) : null}
        </n-space>
      );
    },
  },
]);
watch([() => formValue.value.creationTime], () => {
  tableSearch();
});
/** 获取参数 */
const getParams = () => {
  const { searchType, searchValue, creationTime, consultationStatus } = formValue.value;
  const createStartTime = creationTime ? moment(creationTime[0]).format(`YYYY-MM-DD HH:mm:ss`) : null;
  const createEndTime = creationTime ? moment(creationTime[1]).format(`YYYY-MM-DD HH:mm:ss`) : null;
  return {
    searchType,
    searchValue,
    createStartTime,
    createEndTime,
    consultationStatus,
  };
};

/** 搜索 */
const handlerSearch = () => {
  tableSearch();
};

/** 表格刷新 */
function refresh() {
  tableSearch();
}

/* 表格搜索 */
const tableSearch = () => {
  pageTableData({}, paginationRef.value);
};
const isDeleteLoading = ref(false);
const clickDelete = async id => {
  isDeleteLoading.value = true;
  try {
    await deleteAssistantDoctor({ id });
    createMessageSuccess("删除成功");
    tableSearch();
  } catch (err) {
    createMessageError("删除失败:" + err);
  } finally {
    isDeleteLoading.value = false;
  }
};
/** 打开详情抽屉 */
const detailDrawerRef = ref();
const handlerMedicalAssistantEdit = row => {
  const _params = {
    row,
  };
  detailDrawerRef.value?.acceptParams(_params);
};

const handlerCreateOrEdit = row => {
  data.value = row;
  showMedicalAssistant.value = true;
};
/** 组件挂载 */
onMounted(() => {
  tableSearch();
});
</script>

<style lang="less" scoped>
@import "@/styles/defaultVar.less";
</style>
